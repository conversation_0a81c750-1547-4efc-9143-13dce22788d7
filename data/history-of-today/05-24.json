{"date": "May 24", "url": "https://wikipedia.org/wiki/May_24", "data": {"Events": [{"year": "919", "text": "The nobles of Franconia and Saxony elect <PERSON> at the Imperial Diet in Fritzlar as king of the East Frankish Kingdom.", "html": "919 - The nobles of <a href=\"https://wikipedia.org/wiki/Franconia\" title=\"Franconia\">Franconia</a> and <a href=\"https://wikipedia.org/wiki/Duchy_of_Saxony\" title=\"Duchy of Saxony\">Saxony</a> elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fowler\" title=\"<PERSON> the Fowler\"><PERSON> the Fowler</a> at the <a href=\"https://wikipedia.org/wiki/Imperial_Diet_(Holy_Roman_Empire)\" title=\"Imperial Diet (Holy Roman Empire)\">Imperial Diet</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as king of the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish Kingdom</a>.", "no_year_html": "The nobles of <a href=\"https://wikipedia.org/wiki/Franconia\" title=\"Franconia\">Franconia</a> and <a href=\"https://wikipedia.org/wiki/Duchy_of_Saxony\" title=\"Duchy of Saxony\">Saxony</a> elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fowler\" title=\"<PERSON> Fowler\"><PERSON> the Fowler</a> at the <a href=\"https://wikipedia.org/wiki/Imperial_Diet_(Holy_Roman_Empire)\" title=\"Imperial Diet (Holy Roman Empire)\">Imperial Diet</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>lar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as king of the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish Kingdom</a>.", "links": [{"title": "Franconia", "link": "https://wikipedia.org/wiki/Franconia"}, {"title": "Duchy of Saxony", "link": "https://wikipedia.org/wiki/Duchy_of_Saxony"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Imperial Diet (Holy Roman Empire)", "link": "https://wikipedia.org/wiki/Imperial_Diet_(Holy_Roman_Empire)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "East Francia", "link": "https://wikipedia.org/wiki/East_Francia"}]}, {"year": "1218", "text": "The Fifth Crusade leaves Acre for Egypt.", "html": "1218 - The <a href=\"https://wikipedia.org/wiki/Fifth_Crusade\" title=\"Fifth Crusade\">Fifth Crusade</a> leaves <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre</a> for <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fifth_Crusade\" title=\"Fifth Crusade\">Fifth Crusade</a> leaves <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre</a> for <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Fifth Crusade", "link": "https://wikipedia.org/wiki/Fifth_Crusade"}, {"title": "Acre, Israel", "link": "https://wikipedia.org/wiki/Acre,_Israel"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1276", "text": "<PERSON> is crowned King of Sweden in Uppsala Cathedral.", "html": "1276 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5s\" title=\"<PERSON>\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/King_of_Sweden\" class=\"mw-redirect\" title=\"King of Sweden\">King of Sweden</a> in <a href=\"https://wikipedia.org/wiki/Uppsala_Cathedral\" title=\"Uppsala Cathedral\">Uppsala Cathedral</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5s\" title=\"<PERSON>\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/King_of_Sweden\" class=\"mw-redirect\" title=\"King of Sweden\">King of Sweden</a> in <a href=\"https://wikipedia.org/wiki/Uppsala_Cathedral\" title=\"Uppsala Cathedral\">Uppsala Cathedral</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ladul%C3%A5s"}, {"title": "King of Sweden", "link": "https://wikipedia.org/wiki/King_of_Sweden"}, {"title": "Uppsala Cathedral", "link": "https://wikipedia.org/wiki/Uppsala_Cathedral"}]}, {"year": "1487", "text": "The ten-year-old <PERSON> is crowned in Christ Church Cathedral, Dublin, Ireland, with the name of <PERSON> in a bid to threaten King <PERSON>'s reign.", "html": "1487 - The ten-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_Si<PERSON>\" title=\"<PERSON> Si<PERSON>\"><PERSON></a> is crowned in <a href=\"https://wikipedia.org/wiki/Christ_Church_Cathedral,_Dublin\" title=\"Christ Church Cathedral, Dublin\">Christ Church Cathedral, Dublin</a>, Ireland, with the name of <PERSON> in a bid to threaten <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"<PERSON> VII of England\">King <PERSON> VII</a>'s reign.", "no_year_html": "The ten-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_Si<PERSON>\" title=\"<PERSON> Si<PERSON>\"><PERSON></a> is crowned in <a href=\"https://wikipedia.org/wiki/Christ_Church_Cathedral,_Dublin\" title=\"Christ Church Cathedral, Dublin\">Christ Church Cathedral, Dublin</a>, Ireland, with the name of <PERSON> in a bid to threaten <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"<PERSON> VII of England\">King <PERSON> VII</a>'s reign.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lambert_Simnel"}, {"title": "Christ Church Cathedral, Dublin", "link": "https://wikipedia.org/wiki/Christ_Church_Cathedral,_Dublin"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1567", "text": "<PERSON> of Sweden and his guards murder five incarcerated Swedish nobles.", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Erik XIV of Sweden\"><PERSON> of Sweden</a> and his guards <a href=\"https://wikipedia.org/wiki/Sture_Murders\" class=\"mw-redirect\" title=\"Sture Murders\">murder</a> five incarcerated Swedish nobles.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Erik XIV of Sweden\"><PERSON> of Sweden</a> and his guards <a href=\"https://wikipedia.org/wiki/Sture_Murders\" class=\"mw-redirect\" title=\"Sture Murders\">murder</a> five incarcerated Swedish nobles.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}, {"title": "Sture Murders", "link": "https://wikipedia.org/wiki/Sture_Murders"}]}, {"year": "1595", "text": "Nomenclator of Leiden University Library appears, the first printed catalog of an institutional library.", "html": "1595 - <i><a href=\"https://wikipedia.org/wiki/Nomenclator_(nomenclature)\" title=\"Nomenclator (nomenclature)\">Nomenclator</a></i> of <a href=\"https://wikipedia.org/wiki/Leiden_University_Library\" title=\"Leiden University Library\">Leiden University Library</a> appears, the first printed catalog of an institutional library.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Nomenclator_(nomenclature)\" title=\"Nomenclator (nomenclature)\">Nomenclator</a></i> of <a href=\"https://wikipedia.org/wiki/Leiden_University_Library\" title=\"Leiden University Library\">Leiden University Library</a> appears, the first printed catalog of an institutional library.", "links": [{"title": "Nomenclator (nomenclature)", "link": "https://wikipedia.org/wiki/Nomenclator_(nomenclature)"}, {"title": "Leiden University Library", "link": "https://wikipedia.org/wiki/Leiden_University_Library"}]}, {"year": "1607", "text": "Jamestown, the first permanent English colony in North America, is founded.", "html": "1607 - <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown</a>, the first permanent English colony in <a href=\"https://wikipedia.org/wiki/North_America\" title=\"North America\">North America</a>, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown</a>, the first permanent English colony in <a href=\"https://wikipedia.org/wiki/North_America\" title=\"North America\">North America</a>, is founded.", "links": [{"title": "Jamestown, Virginia", "link": "https://wikipedia.org/wiki/Jamestown,_Virginia"}, {"title": "North America", "link": "https://wikipedia.org/wiki/North_America"}]}, {"year": "1621", "text": "The Protestant Union is formally dissolved.", "html": "1621 - The <a href=\"https://wikipedia.org/wiki/Protestant_Union\" title=\"Protestant Union\">Protestant Union</a> is formally dissolved.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Protestant_Union\" title=\"Protestant Union\">Protestant Union</a> is formally dissolved.", "links": [{"title": "Protestant Union", "link": "https://wikipedia.org/wiki/Protestant_Union"}]}, {"year": "1626", "text": "<PERSON> buys Manhattan.", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> buys <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> buys <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Manhattan", "link": "https://wikipedia.org/wiki/Manhattan"}]}, {"year": "1667", "text": "The French Royal Army crosses the border into the Spanish Netherlands, starting the War of Devolution opposing France to the Spanish Empire and the Triple Alliance.", "html": "1667 - The <a href=\"https://wikipedia.org/wiki/French_Royal_Army_(1652%E2%80%931830)\" class=\"mw-redirect\" title=\"French Royal Army (1652-1830)\">French Royal Army</a> crosses the border into the <a href=\"https://wikipedia.org/wiki/Spanish_Netherlands\" title=\"Spanish Netherlands\">Spanish Netherlands</a>, starting the <a href=\"https://wikipedia.org/wiki/War_of_Devolution\" title=\"War of Devolution\">War of Devolution</a> opposing <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> to the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a> and the <a href=\"https://wikipedia.org/wiki/Triple_Alliance_(1668)\" title=\"Triple Alliance (1668)\">Triple Alliance</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Royal_Army_(1652%E2%80%931830)\" class=\"mw-redirect\" title=\"French Royal Army (1652-1830)\">French Royal Army</a> crosses the border into the <a href=\"https://wikipedia.org/wiki/Spanish_Netherlands\" title=\"Spanish Netherlands\">Spanish Netherlands</a>, starting the <a href=\"https://wikipedia.org/wiki/War_of_Devolution\" title=\"War of Devolution\">War of Devolution</a> opposing <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> to the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a> and the <a href=\"https://wikipedia.org/wiki/Triple_Alliance_(1668)\" title=\"Triple Alliance (1668)\">Triple Alliance</a>.", "links": [{"title": "French Royal Army (1652-1830)", "link": "https://wikipedia.org/wiki/French_Royal_Army_(1652%E2%80%931830)"}, {"title": "Spanish Netherlands", "link": "https://wikipedia.org/wiki/Spanish_Netherlands"}, {"title": "War of Devolution", "link": "https://wikipedia.org/wiki/War_of_Devolution"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Triple Alliance (1668)", "link": "https://wikipedia.org/wiki/Triple_Alliance_(1668)"}]}, {"year": "1683", "text": "The Ashmolean Museum in Oxford, England, opens as the world's first university museum.", "html": "1683 - The <a href=\"https://wikipedia.org/wiki/Ashmolean_Museum\" title=\"Ashmolean Museum\">Ashmolean Museum</a> in <a href=\"https://wikipedia.org/wiki/Oxford\" title=\"Oxford\">Oxford</a>, England, opens as the world's first university museum.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ashmolean_Museum\" title=\"Ashmolean Museum\">Ashmolean Museum</a> in <a href=\"https://wikipedia.org/wiki/Oxford\" title=\"Oxford\">Oxford</a>, England, opens as the world's first university museum.", "links": [{"title": "Ashmolean Museum", "link": "https://wikipedia.org/wiki/Ashmolean_Museum"}, {"title": "Oxford", "link": "https://wikipedia.org/wiki/Oxford"}]}, {"year": "1689", "text": "The English Parliament passes the Act of Toleration protecting dissenting Protestants but excluding Roman Catholics.", "html": "1689 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">English Parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Act_of_Toleration_1689\" class=\"mw-redirect\" title=\"Act of Toleration 1689\">Act of Toleration</a> protecting <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">dissenting Protestants</a> but excluding <a href=\"https://wikipedia.org/wiki/Roman_Catholics\" class=\"mw-redirect\" title=\"Roman Catholics\">Roman Catholics</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">English Parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Act_of_Toleration_1689\" class=\"mw-redirect\" title=\"Act of Toleration 1689\">Act of Toleration</a> protecting <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">dissenting Protestants</a> but excluding <a href=\"https://wikipedia.org/wiki/Roman_Catholics\" class=\"mw-redirect\" title=\"Roman Catholics\">Roman Catholics</a>.", "links": [{"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}, {"title": "Act of Toleration 1689", "link": "https://wikipedia.org/wiki/Act_of_Toleration_1689"}, {"title": "Protestantism", "link": "https://wikipedia.org/wiki/Protestantism"}, {"title": "Roman Catholics", "link": "https://wikipedia.org/wiki/Roman_Catholics"}]}, {"year": "1738", "text": "<PERSON> is converted, essentially launching the Methodist movement; the day is celebrated annually by Methodists as Aldersgate Day and a church service is generally held on the preceding Sunday.", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Born_again_(Christianity)\" class=\"mw-redirect\" title=\"Born again (Christianity)\">converted</a>, essentially launching the <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist movement</a>; the day is celebrated annually by <a href=\"https://wikipedia.org/wiki/Methodists\" class=\"mw-redirect\" title=\"Methodists\">Methodists</a> as <a href=\"https://wikipedia.org/wiki/Aldersgate_Day\" title=\"Aldersgate Day\">Aldersgate Day</a> and a church service is generally held on the preceding Sunday.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Born_again_(Christianity)\" class=\"mw-redirect\" title=\"Born again (Christianity)\">converted</a>, essentially launching the <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist movement</a>; the day is celebrated annually by <a href=\"https://wikipedia.org/wiki/Methodists\" class=\"mw-redirect\" title=\"Methodists\">Methodists</a> as <a href=\"https://wikipedia.org/wiki/Aldersgate_Day\" title=\"Aldersgate Day\">Aldersgate Day</a> and a church service is generally held on the preceding Sunday.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Born again (Christianity)", "link": "https://wikipedia.org/wiki/Born_again_(Christianity)"}, {"title": "Methodism", "link": "https://wikipedia.org/wiki/Methodism"}, {"title": "Methodists", "link": "https://wikipedia.org/wiki/Methodists"}, {"title": "Aldersgate Day", "link": "https://wikipedia.org/wiki/Aldersgate_Day"}]}, {"year": "1798", "text": "The Irish Rebellion of 1798 led by the United Irishmen against British rule begins.", "html": "1798 - The <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a> led by the <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irishmen</a> against British rule begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a> led by the <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irishmen</a> against British rule begins.", "links": [{"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}, {"title": "Society of United Irishmen", "link": "https://wikipedia.org/wiki/Society_of_United_Irishmen"}]}, {"year": "1813", "text": "South American independence leader <PERSON><PERSON><PERSON> enters Mérida, leading the invasion of Venezuela, and is proclaimed El Libertador (\"The Liberator\").", "html": "1813 - South American independence leader <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON> Bolí<PERSON>\"><PERSON><PERSON><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/M%C3%A9rida,_M%C3%A9rida\" title=\"Mérida, Mérida\"><PERSON><PERSON><PERSON></a>, leading the invasion of <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>, and is proclaimed <i>El Libertador</i> (\"The Liberator\").", "no_year_html": "South American independence leader <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON> Bol<PERSON>\"><PERSON><PERSON><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/M%C3%A9rida,_M%C3%A9rida\" title=\"Mérida, Mérida\"><PERSON><PERSON><PERSON></a>, leading the invasion of <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>, and is proclaimed <i>El Libertador</i> (\"The Liberator\").", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar"}, {"title": "Mérida, Mérida", "link": "https://wikipedia.org/wiki/M%C3%A9rida,_M%C3%A9rida"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "1822", "text": "Battle of Pichincha: <PERSON> secures the independence of the Presidency of Quito.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Battle_of_Pichincha\" title=\"Battle of Pichincha\">Battle of Pichincha</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> secures the independence of the <a href=\"https://wikipedia.org/wiki/Presidency_of_Quito\" class=\"mw-redirect\" title=\"Presidency of Quito\">Presidency of Quito</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Pichincha\" title=\"Battle of Pichincha\">Battle of Pichincha</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a> secures the independence of the <a href=\"https://wikipedia.org/wiki/Presidency_of_Quito\" class=\"mw-redirect\" title=\"Presidency of Quito\">Presidency of Quito</a>.", "links": [{"title": "Battle of Pichincha", "link": "https://wikipedia.org/wiki/Battle_of_Pichincha"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>cre"}, {"title": "Presidency of Quito", "link": "https://wikipedia.org/wiki/Presidency_of_Quito"}]}, {"year": "1832", "text": "The First Kingdom of Greece is declared in the London Conference.", "html": "1832 - The <a href=\"https://wikipedia.org/wiki/First_Kingdom_of_Greece\" class=\"mw-redirect\" title=\"First Kingdom of Greece\">First Kingdom of Greece</a> is declared in the <a href=\"https://wikipedia.org/wiki/London_Conference_of_1832\" title=\"London Conference of 1832\">London Conference</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Kingdom_of_Greece\" class=\"mw-redirect\" title=\"First Kingdom of Greece\">First Kingdom of Greece</a> is declared in the <a href=\"https://wikipedia.org/wiki/London_Conference_of_1832\" title=\"London Conference of 1832\">London Conference</a>.", "links": [{"title": "First Kingdom of Greece", "link": "https://wikipedia.org/wiki/First_Kingdom_of_Greece"}, {"title": "London Conference of 1832", "link": "https://wikipedia.org/wiki/London_Conference_of_1832"}]}, {"year": "1844", "text": "<PERSON> sends the message \"What hath God wrought\" (a biblical quotation, Numbers 23:23) from a committee room in the United States Capitol to his assistant, <PERSON>, in Baltimore, Maryland, to inaugurate a commercial telegraph line between Baltimore and Washington D.C.", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends the message \"What hath God wrought\" (a <a href=\"https://wikipedia.org/wiki/Bible\" title=\"Bible\">biblical</a> quotation, <a href=\"https://wikipedia.org/wiki/Book_of_Numbers\" title=\"Book of Numbers\">Numbers</a> 23:23) from a committee room in the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> to his assistant, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> V<PERSON>\"><PERSON></a>, in <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a>, <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a>, to inaugurate a commercial <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a> line between Baltimore and Washington D.C.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends the message \"What hath God wrought\" (a <a href=\"https://wikipedia.org/wiki/Bible\" title=\"Bible\">biblical</a> quotation, <a href=\"https://wikipedia.org/wiki/Book_of_Numbers\" title=\"Book of Numbers\">Numbers</a> 23:23) from a committee room in the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> to his assistant, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a>, <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a>, to inaugurate a commercial <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a> line between Baltimore and Washington D.C.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bible", "link": "https://wikipedia.org/wiki/Bible"}, {"title": "Book of Numbers", "link": "https://wikipedia.org/wiki/Book_of_Numbers"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ail"}, {"title": "Baltimore", "link": "https://wikipedia.org/wiki/Baltimore"}, {"title": "Maryland", "link": "https://wikipedia.org/wiki/Maryland"}, {"title": "Telegraphy", "link": "https://wikipedia.org/wiki/Telegraphy"}]}, {"year": "1856", "text": "<PERSON> and his men kill five slavery supporters at Pottawatomie Creek, Kansas.", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(abolitionist)\" title=\"<PERSON> (abolitionist)\"><PERSON></a> and his men <a href=\"https://wikipedia.org/wiki/Pottawatomie_massacre\" title=\"Pottawatomie massacre\">kill five slavery supporters at Pottawatomie Creek, Kansas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(abolitionist)\" title=\"<PERSON> (abolitionist)\"><PERSON></a> and his men <a href=\"https://wikipedia.org/wiki/Pottawatomie_massacre\" title=\"Pottawatomie massacre\">kill five slavery supporters at Pottawatomie Creek, Kansas</a>.", "links": [{"title": "<PERSON> (abolitionist)", "link": "https://wikipedia.org/wiki/<PERSON>_(abolitionist)"}, {"title": "Pottawatomie massacre", "link": "https://wikipedia.org/wiki/Pottawatomie_massacre"}]}, {"year": "1861", "text": "American Civil War: Union troops occupy Alexandria, Virginia, with Colonel <PERSON> becoming the first Union officer to be killed during the war.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops occupy <a href=\"https://wikipedia.org/wiki/Alexandria,_Virginia\" title=\"Alexandria, Virginia\">Alexandria, Virginia</a>, with Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becoming the first Union officer to be killed during the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops occupy <a href=\"https://wikipedia.org/wiki/Alexandria,_Virginia\" title=\"Alexandria, Virginia\">Alexandria, Virginia</a>, with Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becoming the first Union officer to be killed during the war.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Alexandria, Virginia", "link": "https://wikipedia.org/wiki/Alexandria,_Virginia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON> becomes the first black president of a predominantly white university in the United States.", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first black president of a predominantly white university in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first black president of a predominantly white university in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "The Brooklyn Bridge in New York City is opened to traffic after 14 years of construction.", "html": "1883 - The <a href=\"https://wikipedia.org/wiki/Brooklyn_Bridge\" title=\"Brooklyn Bridge\">Brooklyn Bridge</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> is opened to traffic after 14 years of construction.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Brooklyn_Bridge\" title=\"Brooklyn Bridge\">Brooklyn Bridge</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> is opened to traffic after 14 years of construction.", "links": [{"title": "Brooklyn Bridge", "link": "https://wikipedia.org/wiki/Brooklyn_Bridge"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1900", "text": "Second Boer War: The United Kingdom annexes the Orange Free State.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: The United Kingdom annexes the <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: The United Kingdom annexes the <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a>.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Orange Free State", "link": "https://wikipedia.org/wiki/Orange_Free_State"}]}, {"year": "1915", "text": "World War I: Italy declares war on Austria-Hungary, joining the conflict on the side of the Allies.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> declares war on <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>, joining the conflict on the side of <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">the Allies</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> declares war on <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>, joining the conflict on the side of <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">the Allies</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}]}, {"year": "1930", "text": "<PERSON> lands in Darwin, Northern Territory, becoming the first woman to fly solo from England to Australia (she left on May 5 for the 11,000 mile flight).", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Darwin,_Northern_Territory\" title=\"Darwin, Northern Territory\">Darwin, Northern Territory</a>, becoming the first woman to fly solo from England to Australia (she left on <a href=\"https://wikipedia.org/wiki/May_5\" title=\"May 5\">May 5</a> for the 11,000 mile flight).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Darwin,_Northern_Territory\" title=\"Darwin, Northern Territory\">Darwin, Northern Territory</a>, becoming the first woman to fly solo from England to Australia (she left on <a href=\"https://wikipedia.org/wiki/May_5\" title=\"May 5\">May 5</a> for the 11,000 mile flight).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Darwin, Northern Territory", "link": "https://wikipedia.org/wiki/Darwin,_Northern_Territory"}, {"title": "May 5", "link": "https://wikipedia.org/wiki/May_5"}]}, {"year": "1935", "text": "The first night game in Major League Baseball history is played in Cincinnati, Ohio, with the Cincinnati Reds beating the Philadelphia Phillies 2-1 at Crosley Field.", "html": "1935 - The first <a href=\"https://wikipedia.org/wiki/Night_game\" title=\"Night game\">night game</a> in <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> history is played in <a href=\"https://wikipedia.org/wiki/Cincinnati,_Ohio\" class=\"mw-redirect\" title=\"Cincinnati, Ohio\">Cincinnati, Ohio</a>, with the <a href=\"https://wikipedia.org/wiki/Cincinnati_Reds\" title=\"Cincinnati Reds\">Cincinnati Reds</a> beating the <a href=\"https://wikipedia.org/wiki/Philadelphia_Phillies\" title=\"Philadelphia Phillies\">Philadelphia Phillies</a> 2-1 at <a href=\"https://wikipedia.org/wiki/Crosley_Field\" title=\"Crosley Field\">Crosley Field</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Night_game\" title=\"Night game\">night game</a> in <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> history is played in <a href=\"https://wikipedia.org/wiki/Cincinnati,_Ohio\" class=\"mw-redirect\" title=\"Cincinnati, Ohio\">Cincinnati, Ohio</a>, with the <a href=\"https://wikipedia.org/wiki/Cincinnati_Reds\" title=\"Cincinnati Reds\">Cincinnati Reds</a> beating the <a href=\"https://wikipedia.org/wiki/Philadelphia_Phillies\" title=\"Philadelphia Phillies\">Philadelphia Phillies</a> 2-1 at <a href=\"https://wikipedia.org/wiki/Crosley_Field\" title=\"Crosley Field\">Crosley Field</a>.", "links": [{"title": "Night game", "link": "https://wikipedia.org/wiki/Night_game"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}, {"title": "Cincinnati, Ohio", "link": "https://wikipedia.org/wiki/Cincinnati,_Ohio"}, {"title": "Cincinnati Reds", "link": "https://wikipedia.org/wiki/Cincinnati_Reds"}, {"title": "Philadelphia Phillies", "link": "https://wikipedia.org/wiki/Philadelphia_Phillies"}, {"title": "Crosley Field", "link": "https://wikipedia.org/wiki/Crosley_Field"}]}, {"year": "1940", "text": "<PERSON> performs the first successful single-rotor helicopter flight.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs the first successful single-rotor <a href=\"https://wikipedia.org/wiki/Helicopter\" title=\"Helicopter\">helicopter</a> flight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs the first successful single-rotor <a href=\"https://wikipedia.org/wiki/Helicopter\" title=\"Helicopter\">helicopter</a> flight.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Helicopter", "link": "https://wikipedia.org/wiki/Helicopter"}]}, {"year": "1940", "text": "Acting on the orders of Soviet leader <PERSON>, NKVD agent <PERSON><PERSON><PERSON> orchestrates an unsuccessful assassination attempt on exiled Russian revolutionary <PERSON> in Coyoacán, Mexico.", "html": "1940 - Acting on the orders of <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">NKVD</a> agent <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> orchestrates an unsuccessful assassination attempt on exiled Russian revolutionary <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Coyoac%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Coyoacán</a>, Mexico.", "no_year_html": "Acting on the orders of <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">NKVD</a> agent <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> orchestrates an unsuccessful assassination attempt on exiled Russian revolutionary <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Coyoac%C3%A1n\" title=\"Coyoac<PERSON>\">Coyoacán</a>, Mexico.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "NKVD", "link": "https://wikipedia.org/wiki/NKVD"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coyoacán", "link": "https://wikipedia.org/wiki/Coyoac%C3%A1n"}]}, {"year": "1941", "text": "World War II: Battle of the Atlantic: In the Battle of the Denmark Strait, the German battleship Bismarck sinks the pride of the Royal Navy, HMS Hood, killing all but three crewmen.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Atlantic\" title=\"Battle of the Atlantic\">Battle of the Atlantic</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Denmark_Strait\" title=\"Battle of the Denmark Strait\">Battle of the Denmark Strait</a>, the <a href=\"https://wikipedia.org/wiki/German_battleship_<PERSON>\" title=\"German battleship Bismarck\">German battleship <i>Bismarck</i></a> sinks the pride of the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>, <a href=\"https://wikipedia.org/wiki/HMS_Hood\" title=\"HMS Hood\">HMS <i>Hood</i></a>, killing all but three crewmen.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Atlantic\" title=\"Battle of the Atlantic\">Battle of the Atlantic</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Denmark_Strait\" title=\"Battle of the Denmark Strait\">Battle of the Denmark Strait</a>, the <a href=\"https://wikipedia.org/wiki/German_battleship_Bismarck\" title=\"German battleship <PERSON>\">German battleship <i>Bismarck</i></a> sinks the pride of the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>, <a href=\"https://wikipedia.org/wiki/HMS_Hood\" title=\"HMS Hood\">HMS <i>Hood</i></a>, killing all but three crewmen.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of the Atlantic", "link": "https://wikipedia.org/wiki/Battle_of_the_Atlantic"}, {"title": "Battle of the Denmark Strait", "link": "https://wikipedia.org/wiki/Battle_of_the_Denmark_Strait"}, {"title": "German battleship Bismarck", "link": "https://wikipedia.org/wiki/German_battleship_Bismarck"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "HMS Hood", "link": "https://wikipedia.org/wiki/HMS_Hood"}]}, {"year": "1944", "text": "Börse Berlin building burns down after being hit in an air raid during World War II.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/B%C3%B6rse_Berlin\" title=\"Börse Berlin\">Börse Berlin</a> building burns down after being hit in an air raid during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%B6rse_Berlin\" title=\"Börse Berlin\">Börse Berlin</a> building burns down after being hit in an air raid during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "Börse Berlin", "link": "https://wikipedia.org/wiki/B%C3%B6rse_Berlin"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1944", "text": "Congress of Përmet occurs which establishes a provisional government in Albania in areas under partisan control, the first independent Albanian government since 1939. In honor of this the national emblem of Albania inscribed this date from 1946 until 1992.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Congress_of_P%C3%ABrmet\" title=\"Congress of Përmet\">Congress of Përmet</a> occurs which establishes a provisional government in <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a> in areas under <a href=\"https://wikipedia.org/wiki/National_Liberation_Movement_(Albania)\" title=\"National Liberation Movement (Albania)\">partisan</a> control, the first independent Albanian government since <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_Albania\" title=\"Italian invasion of Albania\">1939</a>. In honor of this the <a href=\"https://wikipedia.org/wiki/Coat_of_arms_of_Albania\" title=\"Coat of arms of Albania\">national emblem of Albania</a> inscribed this date from 1946 until 1992.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Congress_of_P%C3%ABrmet\" title=\"Congress of Përmet\">Congress of Përmet</a> occurs which establishes a provisional government in <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a> in areas under <a href=\"https://wikipedia.org/wiki/National_Liberation_Movement_(Albania)\" title=\"National Liberation Movement (Albania)\">partisan</a> control, the first independent Albanian government since <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_Albania\" title=\"Italian invasion of Albania\">1939</a>. In honor of this the <a href=\"https://wikipedia.org/wiki/Coat_of_arms_of_Albania\" title=\"Coat of arms of Albania\">national emblem of Albania</a> inscribed this date from 1946 until 1992.", "links": [{"title": "Congress of Përmet", "link": "https://wikipedia.org/wiki/Congress_of_P%C3%ABrmet"}, {"title": "Albania", "link": "https://wikipedia.org/wiki/Albania"}, {"title": "National Liberation Movement (Albania)", "link": "https://wikipedia.org/wiki/National_Liberation_Movement_(Albania)"}, {"title": "Italian invasion of Albania", "link": "https://wikipedia.org/wiki/Italian_invasion_of_Albania"}, {"title": "Coat of arms of Albania", "link": "https://wikipedia.org/wiki/Coat_of_arms_of_Albania"}]}, {"year": "1948", "text": "Arab-Israeli War: Egypt captures the Israeli kibbutz of Yad Mordechai, but the five-day effort gives Israeli forces time to prepare enough to stop the Egyptian advance a week later.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">Arab-Israeli War</a>: Egypt <a href=\"https://wikipedia.org/wiki/Battle_of_Yad_Mordechai\" title=\"Battle of Yad Mordechai\">captures</a> the Israeli <a href=\"https://wikipedia.org/wiki/Kibbutz\" title=\"Kibbutz\">kibbutz</a> of <a href=\"https://wikipedia.org/wiki/Yad_Mordechai\" title=\"Yad Mordechai\">Yad <PERSON></a>, but the five-day effort gives Israeli forces time to prepare enough to stop the Egyptian advance a week later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">Arab-Israeli War</a>: Egypt <a href=\"https://wikipedia.org/wiki/Battle_of_Yad_Mordechai\" title=\"Battle of Yad Mordechai\">captures</a> the Israeli <a href=\"https://wikipedia.org/wiki/Kibbutz\" title=\"Kibbutz\">kibbutz</a> of <a href=\"https://wikipedia.org/wiki/Yad_Mordechai\" title=\"Yad Mordechai\">Yad Mo<PERSON>chai</a>, but the five-day effort gives Israeli forces time to prepare enough to stop the Egyptian advance a week later.", "links": [{"title": "1948 Arab-Israeli War", "link": "https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War"}, {"title": "Battle of Yad Mordechai", "link": "https://wikipedia.org/wiki/Battle_of_Yad_Mordechai"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yad_<PERSON><PERSON><PERSON>i"}]}, {"year": "1956", "text": "The first Eurovision Song Contest is held in Lugano, Switzerland.", "html": "1956 - The first <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest\" title=\"Eurovision Song Contest\">Eurovision Song Contest</a> is held in <a href=\"https://wikipedia.org/wiki/Lugano\" title=\"Lugano\">Lugano</a>, Switzerland.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest\" title=\"Eurovision Song Contest\">Eurovision Song Contest</a> is held in <a href=\"https://wikipedia.org/wiki/Lugano\" title=\"Lugano\">Lugano</a>, Switzerland.", "links": [{"title": "Eurovision Song Contest", "link": "https://wikipedia.org/wiki/Eurovision_Song_Contest"}, {"title": "Lugano", "link": "https://wikipedia.org/wiki/Lugano"}]}, {"year": "1958", "text": "United Press International is formed through a merger of the United Press and the International News Service.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/United_Press_International\" title=\"United Press International\">United Press International</a> is formed through a merger of the <a href=\"https://wikipedia.org/wiki/United_Press\" class=\"mw-redirect\" title=\"United Press\">United Press</a> and the <a href=\"https://wikipedia.org/wiki/International_News_Service\" title=\"International News Service\">International News Service</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Press_International\" title=\"United Press International\">United Press International</a> is formed through a merger of the <a href=\"https://wikipedia.org/wiki/United_Press\" class=\"mw-redirect\" title=\"United Press\">United Press</a> and the <a href=\"https://wikipedia.org/wiki/International_News_Service\" title=\"International News Service\">International News Service</a>.", "links": [{"title": "United Press International", "link": "https://wikipedia.org/wiki/United_Press_International"}, {"title": "United Press", "link": "https://wikipedia.org/wiki/United_Press"}, {"title": "International News Service", "link": "https://wikipedia.org/wiki/International_News_Service"}]}, {"year": "1960", "text": "Following the 1960 Valdivia earthquake, the largest ever recorded earthquake, Cordón Caulle begins to erupt.", "html": "1960 - Following the <a href=\"https://wikipedia.org/wiki/1960_Valdivia_earthquake\" title=\"1960 Valdivia earthquake\">1960 Valdivia earthquake</a>, the largest ever recorded earthquake, <a href=\"https://wikipedia.org/wiki/Puyehue-Cord%C3%B3n_Caulle#1960_eruption\" title=\"Puyehue-Cordón Caulle\">Cordón Caulle begins to erupt</a>.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/1960_Valdivia_earthquake\" title=\"1960 Valdivia earthquake\">1960 Valdivia earthquake</a>, the largest ever recorded earthquake, <a href=\"https://wikipedia.org/wiki/Puyehue-Cord%C3%B3n_Caulle#1960_eruption\" title=\"Puyehue-Cordón Caulle\">Cordón Caulle begins to erupt</a>.", "links": [{"title": "1960 Valdivia earthquake", "link": "https://wikipedia.org/wiki/1960_Valdivia_earthquake"}, {"title": "Puyehue-Cordón Caulle", "link": "https://wikipedia.org/wiki/Puyehue-Cord%C3%B3n_Caulle#1960_eruption"}]}, {"year": "1961", "text": "American civil rights movement: Freedom Riders are arrested in Jackson, Mississippi, for \"disturbing the peace\" after disembarking from their bus.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/American_civil_rights_movement\" class=\"mw-redirect\" title=\"American civil rights movement\">American civil rights movement</a>: <a href=\"https://wikipedia.org/wiki/Freedom_Riders\" title=\"Freedom Riders\">Freedom Riders</a> are arrested in <a href=\"https://wikipedia.org/wiki/Jackson,_Mississippi\" title=\"Jackson, Mississippi\">Jackson, Mississippi</a>, for \"disturbing the peace\" after disembarking from their bus.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_civil_rights_movement\" class=\"mw-redirect\" title=\"American civil rights movement\">American civil rights movement</a>: <a href=\"https://wikipedia.org/wiki/Freedom_Riders\" title=\"Freedom Riders\">Freedom Riders</a> are arrested in <a href=\"https://wikipedia.org/wiki/Jackson,_Mississippi\" title=\"Jackson, Mississippi\">Jackson, Mississippi</a>, for \"disturbing the peace\" after disembarking from their bus.", "links": [{"title": "American civil rights movement", "link": "https://wikipedia.org/wiki/American_civil_rights_movement"}, {"title": "Freedom Riders", "link": "https://wikipedia.org/wiki/Freedom_Riders"}, {"title": "Jackson, Mississippi", "link": "https://wikipedia.org/wiki/Jackson,_Mississippi"}]}, {"year": "1962", "text": "Project Mercury: American astronaut <PERSON> orbits the Earth three times in the Aurora 7 space capsule.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: American <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orbits the Earth three times in the <i><a href=\"https://wikipedia.org/wiki/Aurora_7\" class=\"mw-redirect\" title=\"Aurora 7\">Aurora 7</a></i> <a href=\"https://wikipedia.org/wiki/Space_capsule\" title=\"Space capsule\">space capsule</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: American <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orbits the Earth three times in the <i><a href=\"https://wikipedia.org/wiki/Aurora_7\" class=\"mw-redirect\" title=\"Aurora 7\">Aurora 7</a></i> <a href=\"https://wikipedia.org/wiki/Space_capsule\" title=\"Space capsule\">space capsule</a>.", "links": [{"title": "Project Mercury", "link": "https://wikipedia.org/wiki/Project_Mercury"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Aurora 7", "link": "https://wikipedia.org/wiki/Aurora_7"}, {"title": "Space capsule", "link": "https://wikipedia.org/wiki/Space_capsule"}]}, {"year": "1967", "text": "Egypt imposes a blockade and siege of the Red Sea coast of Israel.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> imposes a blockade and siege of the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a> coast of <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> imposes a blockade and siege of the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a> coast of <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "links": [{"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Red Sea", "link": "https://wikipedia.org/wiki/Red_Sea"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "1967", "text": "Belle de Jour, directed by <PERSON>, is released.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(film)\" title=\"Belle de Jour (film)\"><PERSON> de Jour</a>, directed by <a href=\"https://wikipedia.org/wiki/<PERSON>_Bu%C3%B1uel\" title=\"<PERSON>\"><PERSON></a>, is released.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(film)\" title=\"Belle de Jour (film)\"><PERSON> de Jour</a>, directed by <a href=\"https://wikipedia.org/wiki/<PERSON>_Bu%C3%B1uel\" title=\"<PERSON>\"><PERSON></a>, is released.", "links": [{"title": "Belle de Jour (film)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(film)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Bu%C3%B1uel"}]}, {"year": "1976", "text": "The Judgment of Paris takes place in France, launching California as a worldwide force in the production of quality wine.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Judgment_of_Paris_(wine)\" title=\"Judgment of Paris (wine)\">Judgment of Paris</a> takes place in France, launching <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> as a worldwide force in the production of quality <a href=\"https://wikipedia.org/wiki/Wine\" title=\"Wine\">wine</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Judgment_of_Paris_(wine)\" title=\"Judgment of Paris (wine)\">Judgment of Paris</a> takes place in France, launching <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> as a worldwide force in the production of quality <a href=\"https://wikipedia.org/wiki/Wine\" title=\"Wine\">wine</a>.", "links": [{"title": "Judgment of Paris (wine)", "link": "https://wikipedia.org/wiki/Judgment_of_Paris_(wine)"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Wine", "link": "https://wikipedia.org/wiki/Wine"}]}, {"year": "1981", "text": "Ecuadorian president <PERSON>, his wife, and his presidential committee die in an aircraft accident while travelling from Quito to Zapotillo minutes after the president gave a famous speech regarding the 24 de mayo anniversary of the Battle of Pichincha.", "html": "1981 - Ecuadorian president <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%B3s_Aguilera\" title=\"<PERSON>\"><PERSON></a>, his wife, and his presidential committee die in an aircraft accident while travelling from Quito to Zapotillo minutes after the president gave a famous speech regarding the 24 de mayo anniversary of the <a href=\"https://wikipedia.org/wiki/Battle_of_Pichincha\" title=\"Battle of Pichincha\">Battle of Pichincha</a>.", "no_year_html": "Ecuadorian president <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%B3s_Aguilera\" title=\"<PERSON>\"><PERSON></a>, his wife, and his presidential committee die in an aircraft accident while travelling from Quito to Zapotillo minutes after the president gave a famous speech regarding the 24 de mayo anniversary of the <a href=\"https://wikipedia.org/wiki/Battle_of_Pichincha\" title=\"Battle of Pichincha\">Battle of Pichincha</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jaime_Rold%C3%B3s_A<PERSON>ilera"}, {"title": "Battle of Pichincha", "link": "https://wikipedia.org/wiki/Battle_of_Pichincha"}]}, {"year": "1982", "text": "Liberation of Khorramshahr: Iranians recapture of the port city of Khorramshahr from the Iraqis during the Iran-Iraq War.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Liberation_of_Khorramshahr\" class=\"mw-redirect\" title=\"Liberation of Khorramshahr\">Liberation of Khorramshahr</a>: <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranians</a> recapture of the port city of <a href=\"https://wikipedia.org/wiki/Khorramshahr\" title=\"Khorramshahr\">Khorramshahr</a> from the <a href=\"https://wikipedia.org/wiki/Iraqis\" title=\"Iraqis\">Iraqis</a> during the <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Iraq_War\" title=\"Iran-Iraq War\">Iran-Iraq War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liberation_of_Khorramshahr\" class=\"mw-redirect\" title=\"Liberation of Khorramshahr\">Liberation of Khorramshahr</a>: <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranians</a> recapture of the port city of <a href=\"https://wikipedia.org/wiki/Khorramshahr\" title=\"Khorramshahr\">Khorramshahr</a> from the <a href=\"https://wikipedia.org/wiki/Iraqis\" title=\"Iraqis\">Iraqis</a> during the <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Iraq_War\" title=\"Iran-Iraq War\">Iran-Iraq War</a>.", "links": [{"title": "Liberation of Khorramshahr", "link": "https://wikipedia.org/wiki/Liberation_of_K<PERSON>ramshahr"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "Iraqis", "link": "https://wikipedia.org/wiki/Iraqis"}, {"title": "Iran-Iraq War", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Iraq_War"}]}, {"year": "1988", "text": "Section 28 of the United Kingdom's Local Government Act 1988, a controversial amendment stating that a local authority cannot intentionally promote homosexuality, is enacted.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Section_28\" title=\"Section 28\">Section 28</a> of the United Kingdom's <a href=\"https://wikipedia.org/wiki/Local_Government_Act_1988\" title=\"Local Government Act 1988\">Local Government Act 1988</a>, a controversial amendment stating that a <a href=\"https://wikipedia.org/wiki/Local_government_in_England\" title=\"Local government in England\">local authority</a> cannot intentionally promote <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexuality</a>, is enacted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Section_28\" title=\"Section 28\">Section 28</a> of the United Kingdom's <a href=\"https://wikipedia.org/wiki/Local_Government_Act_1988\" title=\"Local Government Act 1988\">Local Government Act 1988</a>, a controversial amendment stating that a <a href=\"https://wikipedia.org/wiki/Local_government_in_England\" title=\"Local government in England\">local authority</a> cannot intentionally promote <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexuality</a>, is enacted.", "links": [{"title": "Section 28", "link": "https://wikipedia.org/wiki/Section_28"}, {"title": "Local Government Act 1988", "link": "https://wikipedia.org/wiki/Local_Government_Act_1988"}, {"title": "Local government in England", "link": "https://wikipedia.org/wiki/Local_government_in_England"}, {"title": "Homosexuality", "link": "https://wikipedia.org/wiki/Homosexuality"}]}, {"year": "1991", "text": "Israel conducts Operation Solomon, evacuating Ethiopian Jews to Israel.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> conducts <a href=\"https://wikipedia.org/wiki/Operation_Solomon\" title=\"Operation Solomon\">Operation Solomon</a>, evacuating <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> Jews to Israel.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> conducts <a href=\"https://wikipedia.org/wiki/Operation_Solomon\" title=\"Operation Solomon\">Operation Solomon</a>, evacuating <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> Jews to Israel.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Operation Solomon", "link": "https://wikipedia.org/wiki/Operation_Solomon"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1992", "text": "The last Thai dictator, General <PERSON><PERSON>, resigns following pro-democracy protests.", "html": "1992 - The last <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thai</a> dictator, General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ray<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, resigns following pro-democracy protests.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thai</a> dictator, General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ray<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, resigns following pro-democracy protests.", "links": [{"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "The ethnic cleansing in Kozarac, Bosnia and Herzegovina begins when Serbian militia and police forces enter the town.", "html": "1992 - The ethnic cleansing in <a href=\"https://wikipedia.org/wiki/Kozarac\" title=\"Kozarac\">Kozarac</a>, <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a> begins when Serbian militia and police forces enter the town.", "no_year_html": "The ethnic cleansing in <a href=\"https://wikipedia.org/wiki/Kozarac\" title=\"Kozarac\">Kozarac</a>, <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a> begins when Serbian militia and police forces enter the town.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}]}, {"year": "1993", "text": "Eritrea gains its independence from Ethiopia.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrea</a> gains its independence from <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrea</a> gains its independence from <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "links": [{"title": "Eritrea", "link": "https://wikipedia.org/wiki/Eritrea"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1993", "text": "Roman Catholic Cardinal <PERSON> and five other people are assassinated in a shootout at Miguel Hidalgo y Costilla Guadalajara International Airport in Mexico.", "html": "1993 - Roman Catholic Cardinal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAs_Posadas_Ocampo\" title=\"<PERSON>\"><PERSON></a> and five other people are assassinated in a shootout at <a href=\"https://wikipedia.org/wiki/Miguel_Hidalgo_y_Costilla_Guadalajara_International_Airport\" class=\"mw-redirect\" title=\"Miguel Hidalgo y Costilla Guadalajara International Airport\">Miguel Hidalgo y Costilla Guadalajara International Airport</a> in Mexico.", "no_year_html": "Roman Catholic Cardinal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAs_Posadas_Ocampo\" title=\"<PERSON>\"><PERSON></a> and five other people are assassinated in a shootout at <a href=\"https://wikipedia.org/wiki/Miguel_Hidalgo_y_Costilla_Guadalajara_International_Airport\" class=\"mw-redirect\" title=\"Miguel Hidalgo y Costilla Guadalajara International Airport\">Miguel <PERSON> y Costilla Guadalajara International Airport</a> in Mexico.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAs_Posadas_Ocampo"}, {"title": "Miguel <PERSON> y Costilla Guadalajara International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_y_Costilla_Guadalajara_International_Airport"}]}, {"year": "1994", "text": "Four men are convicted of bombing the World Trade Center in New York in 1993; each one is sentenced to 240 years in prison.", "html": "1994 - Four men are convicted of <a href=\"https://wikipedia.org/wiki/1993_World_Trade_Center_bombing\" title=\"1993 World Trade Center bombing\">bombing the World Trade Center in New York</a> in 1993; each one is sentenced to 240 years in prison.", "no_year_html": "Four men are convicted of <a href=\"https://wikipedia.org/wiki/1993_World_Trade_Center_bombing\" title=\"1993 World Trade Center bombing\">bombing the World Trade Center in New York</a> in 1993; each one is sentenced to 240 years in prison.", "links": [{"title": "1993 World Trade Center bombing", "link": "https://wikipedia.org/wiki/1993_World_Trade_Center_bombing"}]}, {"year": "1995", "text": "While attempting to return to Leeds Bradford Airport in the United Kingdom, Knight Air Flight 816 crashes in Dunkeswick, North Yorkshire, killing all 12 people on board.", "html": "1995 - While attempting to return to <a href=\"https://wikipedia.org/wiki/Leeds_Bradford_Airport\" title=\"Leeds Bradford Airport\">Leeds Bradford Airport</a> in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>, <a href=\"https://wikipedia.org/wiki/Knight_Air_Flight_816\" title=\"Knight Air Flight 816\">Knight Air Flight 816</a> crashes in <a href=\"https://wikipedia.org/wiki/Dunkeswick\" title=\"Dunkeswick\">Dunkeswick</a>, <a href=\"https://wikipedia.org/wiki/North_Yorkshire\" title=\"North Yorkshire\">North Yorkshire</a>, killing all 12 people on board.", "no_year_html": "While attempting to return to <a href=\"https://wikipedia.org/wiki/Leeds_Bradford_Airport\" title=\"Leeds Bradford Airport\">Leeds Bradford Airport</a> in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>, <a href=\"https://wikipedia.org/wiki/Knight_Air_Flight_816\" title=\"Knight Air Flight 816\">Knight Air Flight 816</a> crashes in <a href=\"https://wikipedia.org/wiki/Dunkeswick\" title=\"Dunkeswick\">Dunkeswick</a>, <a href=\"https://wikipedia.org/wiki/North_Yorkshire\" title=\"North Yorkshire\">North Yorkshire</a>, killing all 12 people on board.", "links": [{"title": "Leeds Bradford Airport", "link": "https://wikipedia.org/wiki/Leeds_Bradford_Airport"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Knight Air Flight 816", "link": "https://wikipedia.org/wiki/Knight_Air_Flight_816"}, {"title": "Dunkeswick", "link": "https://wikipedia.org/wiki/Dunkeswick"}, {"title": "North Yorkshire", "link": "https://wikipedia.org/wiki/North_Yorkshire"}]}, {"year": "1999", "text": "The International Criminal Tribunal for the former Yugoslavia in The Hague, Netherlands indicts <PERSON><PERSON><PERSON><PERSON> and four others for war crimes and crimes against humanity committed in Kosovo.", "html": "1999 - The <a href=\"https://wikipedia.org/wiki/International_Criminal_Tribunal_for_the_former_Yugoslavia\" title=\"International Criminal Tribunal for the former Yugoslavia\">International Criminal Tribunal for the former Yugoslavia</a> in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a>, Netherlands indicts <a href=\"https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and four others for <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a> and <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a> committed in <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Criminal_Tribunal_for_the_former_Yugoslavia\" title=\"International Criminal Tribunal for the former Yugoslavia\">International Criminal Tribunal for the former Yugoslavia</a> in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a>, Netherlands indicts <a href=\"https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and four others for <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a> and <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a> committed in <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a>.", "links": [{"title": "International Criminal Tribunal for the former Yugoslavia", "link": "https://wikipedia.org/wiki/International_Criminal_Tribunal_for_the_former_Yugoslavia"}, {"title": "The Hague", "link": "https://wikipedia.org/wiki/The_Hague"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87"}, {"title": "War crime", "link": "https://wikipedia.org/wiki/War_crime"}, {"title": "Crimes against humanity", "link": "https://wikipedia.org/wiki/Crimes_against_humanity"}, {"title": "Kosovo", "link": "https://wikipedia.org/wiki/Kosovo"}]}, {"year": "2000", "text": "Israeli troops withdraw from southern Lebanon after 22 years of occupation.", "html": "2000 - Israeli troops withdraw from <a href=\"https://wikipedia.org/wiki/Southern_Lebanon\" title=\"Southern Lebanon\">southern Lebanon</a> after 22 years of occupation.", "no_year_html": "Israeli troops withdraw from <a href=\"https://wikipedia.org/wiki/Southern_Lebanon\" title=\"Southern Lebanon\">southern Lebanon</a> after 22 years of occupation.", "links": [{"title": "Southern Lebanon", "link": "https://wikipedia.org/wiki/Southern_Lebanon"}]}, {"year": "2002", "text": "Russia and the United States sign the Moscow Treaty.", "html": "2002 - Russia and the United States sign the <a href=\"https://wikipedia.org/wiki/Strategic_Offensive_Reductions_Treaty\" title=\"Strategic Offensive Reductions Treaty\">Moscow Treaty</a>.", "no_year_html": "Russia and the United States sign the <a href=\"https://wikipedia.org/wiki/Strategic_Offensive_Reductions_Treaty\" title=\"Strategic Offensive Reductions Treaty\">Moscow Treaty</a>.", "links": [{"title": "Strategic Offensive Reductions Treaty", "link": "https://wikipedia.org/wiki/Strategic_Offensive_Reductions_Treaty"}]}, {"year": "2014", "text": "A 6.4 magnitude earthquake occurs in the Aegean Sea between Greece and Turkey, injuring 324 people.", "html": "2014 - A 6.4 magnitude <a href=\"https://wikipedia.org/wiki/2014_Aegean_Sea_earthquake\" title=\"2014 Aegean Sea earthquake\">earthquake</a> occurs in the <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean Sea</a> between <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> and <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, injuring 324 people.", "no_year_html": "A 6.4 magnitude <a href=\"https://wikipedia.org/wiki/2014_Aegean_Sea_earthquake\" title=\"2014 Aegean Sea earthquake\">earthquake</a> occurs in the <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean Sea</a> between <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> and <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, injuring 324 people.", "links": [{"title": "2014 Aegean Sea earthquake", "link": "https://wikipedia.org/wiki/2014_Aegean_Sea_earthquake"}, {"title": "Aegean Sea", "link": "https://wikipedia.org/wiki/Aegean_Sea"}, {"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}]}, {"year": "2014", "text": "At least three people are killed in a shooting at Brussels' Jewish Museum of Belgium.", "html": "2014 - At least three people are killed in a <a href=\"https://wikipedia.org/wiki/Jewish_Museum_of_Belgium_shooting\" title=\"Jewish Museum of Belgium shooting\">shooting</a> at Brussels' <a href=\"https://wikipedia.org/wiki/Jewish_Museum_of_Belgium\" title=\"Jewish Museum of Belgium\">Jewish Museum of Belgium</a>.", "no_year_html": "At least three people are killed in a <a href=\"https://wikipedia.org/wiki/Jewish_Museum_of_Belgium_shooting\" title=\"Jewish Museum of Belgium shooting\">shooting</a> at Brussels' <a href=\"https://wikipedia.org/wiki/Jewish_Museum_of_Belgium\" title=\"Jewish Museum of Belgium\">Jewish Museum of Belgium</a>.", "links": [{"title": "Jewish Museum of Belgium shooting", "link": "https://wikipedia.org/wiki/Jewish_Museum_of_Belgium_shooting"}, {"title": "Jewish Museum of Belgium", "link": "https://wikipedia.org/wiki/Jewish_Museum_of_Belgium"}]}, {"year": "2019", "text": "Twenty-two students die in a fire in Surat (India).", "html": "2019 - Twenty-two students die in a <a href=\"https://wikipedia.org/wiki/2019_Surat_fire\" title=\"2019 Surat fire\">fire</a> in <a href=\"https://wikipedia.org/wiki/Surat\" title=\"Surat\">Surat</a> (<a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>).", "no_year_html": "Twenty-two students die in a <a href=\"https://wikipedia.org/wiki/2019_Surat_fire\" title=\"2019 Surat fire\">fire</a> in <a href=\"https://wikipedia.org/wiki/Surat\" title=\"Surat\">Surat</a> (<a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>).", "links": [{"title": "2019 Surat fire", "link": "https://wikipedia.org/wiki/2019_Surat_fire"}, {"title": "Surat", "link": "https://wikipedia.org/wiki/Surat"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}]}, {"year": "2019", "text": "Under pressure over her handling of <PERSON><PERSON><PERSON><PERSON>, British Prime Minister <PERSON> announces her resignation as Leader of the Conservative Party, effective as of June 7.", "html": "2019 - Under pressure over her handling of <a href=\"https://wikipedia.org/wiki/Brexit\" title=\"Brexit\"><PERSON><PERSON><PERSON><PERSON></a>, British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON>\"><PERSON> May</a> announces her resignation as Leader of the <a href=\"https://wikipedia.org/wiki/Conservative_Party_(UK)\" title=\"Conservative Party (UK)\">Conservative Party</a>, effective as of June 7.", "no_year_html": "Under pressure over her handling of <a href=\"https://wikipedia.org/wiki/Brexit\" title=\"Brexit\"><PERSON><PERSON><PERSON><PERSON></a>, British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON>\"><PERSON> May</a> announces her resignation as Leader of the <a href=\"https://wikipedia.org/wiki/Conservative_Party_(UK)\" title=\"Conservative Party (UK)\">Conservative Party</a>, effective as of June 7.", "links": [{"title": "Brexit", "link": "https://wikipedia.org/wiki/Brexit"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}, {"title": "Conservative Party (UK)", "link": "https://wikipedia.org/wiki/Conservative_Party_(UK)"}]}, {"year": "2022", "text": "A mass shooting occurs at Robb Elementary School in Uvalde, Texas, United States, resulting in the deaths of 21 people, including 19 children.", "html": "2022 - A <a href=\"https://wikipedia.org/wiki/Uvalde_school_shooting\" title=\"Uvalde school shooting\">mass shooting</a> occurs at Robb Elementary School in <a href=\"https://wikipedia.org/wiki/Uvalde,_Texas\" title=\"Uvalde, Texas\">Uvalde, Texas</a>, United States, resulting in the deaths of 21 people, including 19 children.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Uvalde_school_shooting\" title=\"Uvalde school shooting\">mass shooting</a> occurs at Robb Elementary School in <a href=\"https://wikipedia.org/wiki/Uvalde,_Texas\" title=\"Uvalde, Texas\">Uvalde, Texas</a>, United States, resulting in the deaths of 21 people, including 19 children.", "links": [{"title": "Uvalde school shooting", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_school_shooting"}, {"title": "Uvalde, Texas", "link": "https://wikipedia.org/wiki/Uvalde,_Texas"}]}], "Births": [{"year": "15 BC", "text": "<PERSON><PERSON>, Roman general (d. 19)", "html": "15 BC - 15 BC - <a href=\"https://wikipedia.org/wiki/Germanicus\" title=\"Germanicus\">Germanicus</a>, Roman general (d. 19)", "no_year_html": "15 BC - <a href=\"https://wikipedia.org/wiki/Germanicus\" title=\"Germanicus\">Germanicus</a>, Roman general (d. 19)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Germanicus"}]}, {"year": "1335", "text": "<PERSON> of Bohemia, Queen of Hungary (d. 1349)", "html": "1335 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia,_Queen_of_Hungary\" title=\"<PERSON> of Bohemia, Queen of Hungary\"><PERSON> of Bohemia, Queen of Hungary</a> (d. 1349)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia,_Queen_of_Hungary\" title=\"<PERSON> of Bohemia, Queen of Hungary\"><PERSON> of Bohemia, Queen of Hungary</a> (d. 1349)", "links": [{"title": "<PERSON> of Bohemia, Queen of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bohemia,_Queen_of_Hungary"}]}, {"year": "1494", "text": "<PERSON><PERSON><PERSON>, Italian painter (d. 1557)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/Pontormo\" title=\"Pontormo\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pontormo\" title=\"Pontormo\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1557)", "links": [{"title": "Pontormo", "link": "https://wikipedia.org/wiki/Pontormo"}]}, {"year": "1522", "text": "<PERSON>, English bishop (d. 1571)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (d. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (d. 1571)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1544", "text": "<PERSON>, English physician, physicist, and astronomer (d. 1603)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" class=\"mw-redirect\" title=\"<PERSON> (astronomer)\"><PERSON></a>, English physician, physicist, and astronomer (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" class=\"mw-redirect\" title=\"<PERSON> (astronomer)\"><PERSON></a>, English physician, physicist, and astronomer (d. 1603)", "links": [{"title": "<PERSON> (astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronomer)"}]}, {"year": "1576", "text": "<PERSON>, Lady <PERSON>, English courtier (d. 1635)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Lady <PERSON>\"><PERSON>, Lady <PERSON></a>, English courtier (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Lady <PERSON>\"><PERSON>, Lady <PERSON></a>, English courtier (d. 1635)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1616", "text": "<PERSON>, 1st Duke of Lauderdale, Scottish politician, Secretary of State, Scotland (d. 1682)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Lauderdale\" title=\"<PERSON>, 1st Duke of Lauderdale\"><PERSON>, 1st Duke of Lauderdale</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State,_Scotland\" class=\"mw-redirect\" title=\"Secretary of State, Scotland\">Secretary of State, Scotland</a> (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Lauderdale\" title=\"<PERSON>, 1st Duke of Lauderdale\"><PERSON>, 1st Duke of Lauderdale</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State,_Scotland\" class=\"mw-redirect\" title=\"Secretary of State, Scotland\">Secretary of State, Scotland</a> (d. 1682)", "links": [{"title": "<PERSON>, 1st Duke of Lauderdale", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Lauderdale"}, {"title": "Secretary of State, Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State,_Scotland"}]}, {"year": "1628", "text": "<PERSON><PERSON>, Polish noble (d. 1652)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(1628%E2%80%931652)\" title=\"<PERSON><PERSON> (1628-1652)\"><PERSON><PERSON></a>, Polish noble (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(1628%E2%80%931652)\" title=\"<PERSON><PERSON> (1628-1652)\"><PERSON><PERSON></a>, Polish noble (d. 1652)", "links": [{"title": "<PERSON><PERSON> (1628-1652)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(1628%E2%80%931652)"}]}, {"year": "1669", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish royal favorite (d. 1743)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/Em<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%<PERSON>ben\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish royal favorite (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%<PERSON>ben\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish royal favorite (d. 1743)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emerenti<PERSON>_<PERSON>_<PERSON>%C3%BCben"}]}, {"year": "1671", "text": "<PERSON><PERSON>, Grand Duke of Tuscany (d. 1737)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON> <PERSON><PERSON>, Grand Duke of Tuscany</a> (d. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON> <PERSON><PERSON>, Grand Duke of Tuscany</a> (d. 1737)", "links": [{"title": "<PERSON><PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1686", "text": "<PERSON>, Polish-German physicist and engineer, developed the Fahrenheit scale (d. 1736)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>heit\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and engineer, developed the <a href=\"https://wikipedia.org/wiki/Fahrenheit\" title=\"Fahrenheit\">Fahrenheit scale</a> (d. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>heit\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and engineer, developed the <a href=\"https://wikipedia.org/wiki/Fahrenheit\" title=\"Fahrenheit\">Fahrenheit scale</a> (d. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>heit"}, {"title": "Fahrenheit", "link": "https://wikipedia.org/wiki/Fahrenheit"}]}, {"year": "1689", "text": "<PERSON>, 8th Earl of Winchilsea, English politician, Lord President of the Council (d. 1769)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Winchilsea\" title=\"<PERSON>, 8th Earl of Winchilsea\"><PERSON>, 8th Earl of Winchilsea</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Winchilsea\" title=\"<PERSON>, 8th Earl of Winchilsea\"><PERSON>, 8th Earl of Winchilsea</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1769)", "links": [{"title": "<PERSON>, 8th Earl of Winchilsea", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Winchilsea"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1743", "text": "<PERSON><PERSON><PERSON>, Swiss-French physician, journalist, and politician (d. 1793)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-French physician, journalist, and politician (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-French physician, journalist, and politician (d. 1793)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1789", "text": "<PERSON><PERSON><PERSON>, German operatic singer and actress (d. 1828)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German operatic singer and actress (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German operatic singer and actress (d. 1828)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>r"}]}, {"year": "1794", "text": "<PERSON>, English priest and philosopher (d. 1866)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and philosopher (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and philosopher (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, Finnish biologist and paleontologist (d. 1866)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish biologist and paleontologist (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish biologist and paleontologist (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, German rabbi and scholar (d. 1874)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi and scholar (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi and scholar (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, German-American painter (d. 1868)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON> of the United Kingdom (d. 1901)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> of the United Kingdom (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> of the United Kingdom (d. 1901)", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Russian painter and academic (d. 1897)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and academic (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and academic (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, English actor, director, and playwright (d. 1934)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and playwright (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and playwright (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, 1st Baron <PERSON>, Maltese lawyer and politician, 4th Prime Minister of Malta (d. 1940)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Maltese lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Maltese lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 1940)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1863", "text": "<PERSON>, American sculptor (d. 1938)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American engineer and mechanic (d. 1956)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mechanic)\" title=\"<PERSON> (mechanic)\"><PERSON></a>, American engineer and mechanic (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mechanic)\" title=\"<PERSON> (mechanic)\"><PERSON></a>, American engineer and mechanic (d. 1956)", "links": [{"title": "<PERSON> (mechanic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mechanic)"}]}, {"year": "1870", "text": "<PERSON>, American lawyer and judge (d. 1938)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, South African lawyer and politician, 2nd Prime Minister of South Africa (d. 1950)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}]}, {"year": "1874", "text": "Princess <PERSON> of Hesse and by Rhine (d. 1878)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine_(1874%E2%80%931878)\" title=\"Princess <PERSON> of Hesse and by Rhine (1874-1878)\">Princess <PERSON> of Hesse and by Rhine</a> (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine_(1874%E2%80%931878)\" title=\"Princess <PERSON> of Hesse and by Rhine (1874-1878)\">Princess <PERSON> of Hesse and by Rhine</a> (d. 1878)", "links": [{"title": "Princess <PERSON> of Hesse and by Rhine (1874-1878)", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine_(1874%E2%80%931878)"}]}, {"year": "1875", "text": "<PERSON>, American discus thrower and shot putter (d. 1961)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and shot putter (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and shot putter (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American psychologist and engineer (d. 1972)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and engineer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and engineer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON> <PERSON><PERSON>, American candy maker, created <PERSON>'s Peanut Butter Cups (d. 1956)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Reese\"><PERSON><PERSON> <PERSON><PERSON></a>, American candy maker, created <a href=\"https://wikipedia.org/wiki/Reese%27s_Peanut_Butter_Cups\" title=\"Reese's Peanut Butter Cups\">Reese's Peanut Butter Cups</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Reese\"><PERSON><PERSON> <PERSON><PERSON></a>, American candy maker, created <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Peanut_Butter_Cups\" title=\"Reese's Peanut Butter Cups\">Reese's Peanut Butter Cups</a> (d. 1956)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Reese's Peanut Butter Cups", "link": "https://wikipedia.org/wiki/Reese%27s_Peanut_Butter_Cups"}]}, {"year": "1886", "text": "<PERSON>, French organist, composer, and conductor (d. 1979)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist, composer, and conductor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist, composer, and conductor (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Irish soldier and pilot, Victoria Cross recipient (d. 1918)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish soldier and pilot, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish soldier and pilot, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1891", "text": "<PERSON>, American archaeologist, philologist, and scholar (d. 1971)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist, philologist, and scholar (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist, philologist, and scholar (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American author and educator (d. 1958)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>house Sr., American publisher, founded Advance Publications (d. 1979)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON> Sr.</a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/Advance_Publications\" title=\"Advance Publications\">Advance Publications</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON> Sr.</a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/Advance_Publications\" title=\"Advance Publications\">Advance Publications</a> (d. 1979)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Advance Publications", "link": "https://wikipedia.org/wiki/Advance_Publications"}]}, {"year": "1899", "text": "<PERSON>, French tennis player (d. 1938)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Belgian-French poet and painter (d. 1984)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French poet and painter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French poet and painter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Italian actor and screenwriter (d. 1984)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and screenwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and screenwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Uruguayan footballer and manager (d. 1968)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Canadian football player and politician (d. 1954)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player and politician (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player and politician (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Canadian sculptor (d. 2004)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese jumper and journalist (d. 1997)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Ch%C5%ABhe<PERSON>_Nambu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese jumper and journalist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ch%C5%ABhei_Nambu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese jumper and journalist (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ch%C5%ABhei_Nambu"}]}, {"year": "1905", "text": "<PERSON>, American woodworker and architect (d. 1990)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American woodworker and architect (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American woodworker and architect (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Russian novelist and short story writer, Nobel Prize laureate (d. 1984)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, American banker and politician (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American banker and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American banker and politician (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American golfer (d. 1983)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American baseball player and soldier (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and soldier (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and soldier (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, German-American actress (d. 1986)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actress (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actress (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Australian lieutenant and politician, 32nd Governor of New South Wales (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian lieutenant and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian lieutenant and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ler"}, {"title": "Governor of New South Wales", "link": "https://wikipedia.org/wiki/Governor_of_New_South_Wales"}]}, {"year": "1917", "text": "<PERSON>, <PERSON> of Alloway, English lawyer and judge (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Alloway\" title=\"<PERSON>, Baron <PERSON> of Alloway\"><PERSON>, Baron <PERSON> of Alloway</a>, English lawyer and judge (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Alloway\" title=\"<PERSON>, Baron <PERSON> of Alloway\"><PERSON>, Baron <PERSON> of Alloway</a>, English lawyer and judge (d. 2013)", "links": [{"title": "<PERSON>, Baron <PERSON> of Alloway", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Alloway"}]}, {"year": "1918", "text": "<PERSON>, American politician, 66th Mayor of Detroit (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 66th <a href=\"https://wikipedia.org/wiki/Mayor_of_Detroit\" class=\"mw-redirect\" title=\"Mayor of Detroit\">Mayor of Detroit</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 66th <a href=\"https://wikipedia.org/wiki/Mayor_of_Detroit\" class=\"mw-redirect\" title=\"Mayor of Detroit\">Mayor of Detroit</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Detroit", "link": "https://wikipedia.org/wiki/Mayor_of_Detroit"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish actress (d. 1986)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Siobh%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish actress (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siobh%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish actress (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Siobh%C3%A1n_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American soldier and painter (d. 2022)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American illustrator and educator (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Infantino"}]}, {"year": "1925", "text": "<PERSON>, Swedish actress and director (d. 1994)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress and director (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress and director (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Scottish actor and screenwriter", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Irish novelist, playwright and short story writer (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, playwright and short story writer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, playwright and short story writer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English playwright and producer (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American lawyer and politician, 50th Mayor of Chicago (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Canadian television host and actor (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/R%C3%A9al_Gigu%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian television host and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9al_Gigu%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian television host and actor (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9al_Gigu%C3%A8re"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, French-Israeli rabbi and author (d. 2015)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Aharon_Lichtenstein\" title=\"Aharon Lichtenstein\"><PERSON><PERSON><PERSON></a>, French-Israeli rabbi and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aharon_Lichtenstein\" title=\"Aharon Li<PERSON>\"><PERSON><PERSON><PERSON></a>, French-Israeli rabbi and author (d. 2015)", "links": [{"title": "<PERSON>aron <PERSON>", "link": "https://wikipedia.org/wiki/Aharon_Lichtenstein"}]}, {"year": "1935", "text": "<PERSON>, American director and screenwriter (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American composer and poet (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and poet (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and poet (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, French runner and educator (d. 2008)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French runner and educator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French runner and educator (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American saxophonist and composer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Jamaican singer-songwriter and producer (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince Buster\"><PERSON></a>, Jamaican singer-songwriter and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Buster\" title=\"Prince Buster\"><PERSON></a>, Jamaican singer-songwriter and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian-American actor, director, producer, and screenwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Russian-American poet and essayist, Nobel Prize laureate (d. 1996)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American poet and essayist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American poet and essayist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter, guitarist, artist, writer, and producer; Nobel Prize laureate", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, artist, writer, and producer; <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dylan\"><PERSON></a>, American singer-songwriter, guitarist, artist, writer, and producer; <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1941", "text": "<PERSON>, <PERSON> of Heigham, English academic and politician (d. 2018)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON><PERSON>_of_Heigham\" title=\"<PERSON>, Baroness <PERSON> of Heigham\"><PERSON>, Baroness <PERSON> of Heigham</a>, English academic and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON><PERSON>_of_Heigham\" title=\"<PERSON>, Baroness <PERSON> of Heigham\"><PERSON>, Baroness <PERSON> of Heigham</a>, English academic and politician (d. 2018)", "links": [{"title": "<PERSON>, Baroness <PERSON> of Heigham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, South African cricketer and manager", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Finnish race car driver (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician, Japanese Minister of Home Affairs", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Ichir%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Home_Affairs_(Japan)\" title=\"Ministry of Home Affairs (Japan)\">Japanese Minister of Home Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ichir%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Home_Affairs_(Japan)\" title=\"Ministry of Home Affairs (Japan)\">Japanese Minister of Home Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ichir%C5%8D_Ozawa"}, {"title": "Ministry of Home Affairs (Japan)", "link": "https://wikipedia.org/wiki/Ministry_of_Home_Affairs_(Japan)"}]}, {"year": "1943", "text": "<PERSON>, American actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2012)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English engineer and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English lieutenant and politician, Shadow Secretary of State for Environment, Food and Rural Affairs", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Environment,_Food_and_Rural_Affairs\" title=\"Shadow Secretary of State for Environment, Food and Rural Affairs\">Shadow Secretary of State for Environment, Food and Rural Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Environment,_Food_and_Rural_Affairs\" title=\"Shadow Secretary of State for Environment, Food and Rural Affairs\">Shadow Secretary of State for Environment, Food and Rural Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Secretary of State for Environment, Food and Rural Affairs", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Environment,_Food_and_Rural_Affairs"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, American actress and businesswoman", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and businesswoman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Turkish politician, Prime Minister of Turkey", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Tansu_%C3%87iller\" title=\"<PERSON><PERSON>ille<PERSON>\"><PERSON><PERSON></a>, Turkish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tansu_%C3%87iller\" title=\"<PERSON><PERSON> Çiller\"><PERSON><PERSON></a>, Turkish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tansu_%C3%87iller"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Russian-Polish sprinter (d. 2018)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wi%C5%84ska\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Polish sprinter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wi%C5%84ska\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Polish sprinter (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irena_Szewi%C5%84ska"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter, guitarist, and drummer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Filipino director, producer, screenwriter and cinematographer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Filipino director, producer, screenwriter and cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Filipino director, producer, screenwriter and cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter, pianist, and American football player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, pianist, and American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, pianist, and American football player", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American guitarist, singer-songwriter, and record producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Waddy_Wachtel\" title=\"Waddy Wachtel\"><PERSON><PERSON><PERSON></a>, American guitarist, singer-songwriter, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waddy_Wachtel\" title=\"Waddy Wachtel\"><PERSON><PERSON><PERSON></a>, American guitarist, singer-songwriter, and record producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waddy_<PERSON>achtel"}]}, {"year": "1947", "text": "<PERSON>, German businessman", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, French director and screenwriter (d. 2004)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English cinematographer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Belgian singer and songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian composer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON><PERSON>, American constitutional historian (d. 2023)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American constitutional historian (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American constitutional historian (d. 2023)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English lawyer and politician, Attorney General for England and Wales", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1956", "text": "<PERSON>, Irish archbishop", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Irish archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Irish archbishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1958", "text": "<PERSON>, American race car driver, team owner and businessman", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver, team owner and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver, team owner and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Swedish-American ice hockey player (d. 1985)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American ice hockey player (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American ice hockey player (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian politician, 43rd Premier of New South Wales", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27F<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 43rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27F<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 43rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barry_O%27Farrell"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1960", "text": "<PERSON>, English keyboard player, guitarist, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian rugby league referee and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league referee and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league referee and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, English actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Italian philosopher and theorist (d. 2013)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian philosopher and theorist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian philosopher and theorist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1961", "text": "<PERSON>, Canadian-American ice hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American boxer (d. 2012)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American boxer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American boxer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Camacho"}]}, {"year": "1962", "text": "<PERSON>, American actor, dancer, and choreographer (d. 2003)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and choreographer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and choreographer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Italian race car driver and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American novelist, short story writer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American computer scientist and educator", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and educator", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}]}, {"year": "1964", "text": "<PERSON>, Scottish educator and runner", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English swimmer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adrian_<PERSON>house"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Mexican boxer (d. 2013)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Isidro_P%C3%A9rez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican boxer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Is<PERSON>ro_P%C3%A9rez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican boxer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isidro_P%C3%A9rez"}]}, {"year": "1964", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Shinichir%C5%8D_Watanabe\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shinichir%C5%8D_Watanabe\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shinichir%C5%8D_Watanabe"}]}, {"year": "1966", "text": "<PERSON>, French footballer and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eric_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American race car driver and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Craven\"><PERSON></a>, American race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Craven\"><PERSON></a>, American race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Turkish actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>da%C4%9Fl%C4%B1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%9Fl%C4%B1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>er_Karada%C4%9Fl%C4%B1"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Russian-English economist and businessman", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-English economist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-English economist and businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON> <PERSON>, Jamaican-American rapper, producer, and actor (d. 2011)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Heavy_D\" title=\"Heavy D\">Heavy D</a>, Jamaican-American rapper, producer, and actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heavy_D\" title=\"Heavy D\">Heavy D</a>, Jamaican-American rapper, producer, and actor (d. 2011)", "links": [{"title": "Heavy D", "link": "https://wikipedia.org/wiki/Heavy_D"}]}, {"year": "1967", "text": "<PERSON>, Venezuelan-American baseball player and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, Venezuelan-American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, Venezuelan-American baseball player and manager", "links": [{"title": "<PERSON> (catcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1<PERSON><PERSON>_(catcher)"}]}, {"year": "1969", "text": "<PERSON>, Northern Irish-English cricketer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American guitarist and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Indian music director and businessman", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian music director and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian music director and businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Man<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Argentinian singer-songwriter (d. 2000)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Argentinian singer-songwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Argentinian singer-songwriter (d. 2000)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Dominican-American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Col%C3%B3n\" title=\"Bart<PERSON> Colón\"><PERSON><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartolo_Col%C3%B3n\" title=\"Bartolo Colón\"><PERSON><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartolo_Col%C3%B3n"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Indian director, producer, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_%C5%A0micer\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_%C5%A0micer\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_%C5%A0micer"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, French runner and actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French runner and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French runner and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, German biochemist and computer programmer, developed MediaWiki", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biochemist and computer programmer, developed <a href=\"https://wikipedia.org/wiki/MediaWiki\" title=\"MediaWiki\">MediaWiki</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biochemist and computer programmer, developed <a href=\"https://wikipedia.org/wiki/MediaWiki\" title=\"MediaWiki\">MediaWiki</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "MediaWiki", "link": "https://wikipedia.org/wiki/MediaWiki"}]}, {"year": "1975", "text": "<PERSON>, Canadian actor and comedian", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian speed skater", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Greek footballer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian-American singer and keyboard player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American singer and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American singer and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, New Zealand-Australian netball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(netball)\" title=\"<PERSON> (netball)\"><PERSON></a>, New Zealand-Australian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(netball)\" title=\"<PERSON> (netball)\"><PERSON></a>, New Zealand-Australian netball player", "links": [{"title": "<PERSON> (netball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(netball)"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Norwegian singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Indian score composer, music director and singer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>annguli\"><PERSON><PERSON></a>, Indian score composer, music director and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>annguli\"><PERSON><PERSON></a>, Indian score composer, music director and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Swedish ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, French singer, songwriter and composer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(French_singer)\" title=\"<PERSON> (French singer)\"><PERSON></a>, French singer, songwriter and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(French_singer)\" title=\"<PERSON> (French singer)\"><PERSON></a>, French singer, songwriter and composer", "links": [{"title": "<PERSON> (French singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(French_singer)"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian comedian, actor, and screenwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian, actor, and screenwriter", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Ghanaian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Cust%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cust%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON>ust<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cust%C3%B3dio_Castro"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Iranian-American meteorologist and journalist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Pedram_<PERSON>her<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-American meteorologist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-American meteorologist and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>edram_<PERSON>i"}]}, {"year": "1983", "text": "<PERSON><PERSON>, South Korean model and actress (d. 2009)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Woo_<PERSON><PERSON>-yeon\" title=\"Woo <PERSON>-yeon\"><PERSON><PERSON>-yeon</a>, South Korean model and actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woo_<PERSON><PERSON>-yeon\" title=\"Woo <PERSON>-yeon\"><PERSON><PERSON>-yeon</a>, South Korean model and actress (d. 2009)", "links": [{"title": "<PERSON><PERSON>yeon", "link": "https://wikipedia.org/wiki/Woo_<PERSON><PERSON>-yeon"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter, guitarist, dancer, and actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, dancer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Artem_Anisimov\" title=\"Artem Anisimov\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artem_Anisimov\" title=\"Artem Anisimov\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON> Anisimov", "link": "https://wikipedia.org/wiki/Artem_Anisimov"}]}, {"year": "1988", "text": "<PERSON>, American sergeant", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American musician", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American political artist and White House correspondent", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political artist and White House correspondent", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political artist and White House correspondent", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Slovenian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Slovenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Slovenian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Petri%C4%87"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper", "html": "1989 - <a href=\"https://wikipedia.org/wiki/G-E<PERSON><PERSON>\" title=\"G-<PERSON>azy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G-E<PERSON><PERSON>\" title=\"G-<PERSON>az<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "G-Eazy", "link": "https://wikipedia.org/wiki/G-Eazy"}]}, {"year": "1989", "text": "<PERSON>, English race car driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American race car driver", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Welsh discus thrower", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(field_athlete)\" title=\"<PERSON><PERSON> (field athlete)\"><PERSON><PERSON></a>, Welsh discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(field_athlete)\" title=\"<PERSON><PERSON> (field athlete)\"><PERSON><PERSON></a>, Welsh discus thrower", "links": [{"title": "<PERSON><PERSON> (field athlete)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(field_athlete)"}]}, {"year": "1991", "text": "<PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Argentine footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Scottish netball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish netball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Japanese swimmer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1994", "text": "<PERSON>, American  2016 Wikipedian of the Year award", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>Wood\"><PERSON></a>, American 2016 <a href=\"https://wikipedia.org/wiki/Wikipedian_of_the_Year\" class=\"mw-redirect\" title=\"Wikipedian of the Year\">Wikipedian of the Year</a> award", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American 2016 <a href=\"https://wikipedia.org/wiki/Wikipedian_of_the_Year\" class=\"mw-redirect\" title=\"Wikipedian of the Year\">Wikipedian of the Year</a> award", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Wikipedian of the Year", "link": "https://wikipedia.org/wiki/Wikipedian_of_the_Year"}]}, {"year": "1996", "text": "<PERSON>, Japanese voice actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Uchida\"><PERSON></a>, Japanese voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Uchida\"><PERSON></a>, Japanese voice actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian actor", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Tar<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tar<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tar<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": " <PERSON>,  journalist and social media influencer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"> <PERSON></a>, journalist and social media influencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"> <PERSON></a>, journalist and social media influencer", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}], "Deaths": [{"year": "688", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Armagh (b. c. 610)", "html": "688 - <a href=\"https://wikipedia.org/wiki/S%C3%A9g%C3%A9ne\" title=\"Ségé<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of Armagh (b. c. 610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9g%C3%A9ne\" title=\"Ségé<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of Armagh (b. c. 610)", "links": [{"title": "Ségéne", "link": "https://wikipedia.org/wiki/S%C3%A9g%C3%A9ne"}]}, {"year": "1089", "text": "<PERSON><PERSON><PERSON><PERSON>, Archbishop of Canterbury", "html": "1089 - <a href=\"https://wikipedia.org/wiki/La<PERSON><PERSON><PERSON>\" title=\"Lan<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Archbishop of Canterbury", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lan<PERSON><PERSON>\" title=\"Lan<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Archbishop of Canterbury", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nfra<PERSON>"}]}, {"year": "1136", "text": "<PERSON><PERSON>, first Grand Master of the Knights Templar (b. c. 1070)", "html": "1136 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> de <PERSON>\"><PERSON><PERSON></a>, first <a href=\"https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Grand Masters of the Knights Templar\">Grand Master of the Knights Templar</a> (b. c. 1070)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> de <PERSON>\"><PERSON><PERSON></a>, first <a href=\"https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Grand Masters of the Knights Templar\">Grand Master of the Knights Templar</a> (b. c. 1070)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Grand Masters of the Knights Templar", "link": "https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar"}]}, {"year": "1153", "text": "<PERSON> of Scotland (b. 1083)", "html": "1153 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1083)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1083)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Scotland"}]}, {"year": "1201", "text": "<PERSON><PERSON><PERSON> <PERSON>, Count of Champagne (b. 1179)", "html": "1201 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Champagne\" title=\"<PERSON><PERSON><PERSON> III, Count of Champagne\"><PERSON><PERSON><PERSON> III, Count of Champagne</a> (b. 1179)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Champagne\" title=\"<PERSON><PERSON><PERSON> III, Count of Champagne\"><PERSON><PERSON><PERSON>, Count of Champagne</a> (b. 1179)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Count of Champagne", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Champagne"}]}, {"year": "1351", "text": "<PERSON> ibn <PERSON>, Moroccan sultan (b. 1297)", "html": "1351 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Moroccan sultan (b. 1297)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Moroccan sultan (b. 1297)", "links": [{"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1408", "text": "<PERSON><PERSON><PERSON> of Joseon  (b. 1335)", "html": "1408 - <a href=\"https://wikipedia.org/wiki/Taejo_of_Joseon\" title=\"Taejo of Joseon\"><PERSON><PERSON><PERSON> of Joseon</a> (b. 1335)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taejo_of_Joseon\" title=\"Taejo of Joseon\"><PERSON><PERSON>o of Joseon</a> (b. 1335)", "links": [{"title": "Taejo of Joseon", "link": "https://wikipedia.org/wiki/Taejo_of_Joseon"}]}, {"year": "1425", "text": "<PERSON>, 2nd Duke of Albany, Scottish politician (b. 1362)", "html": "1425 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Albany\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Duke of Albany\"><PERSON>, 2nd Duke of Albany</a>, Scottish politician (b. 1362)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Albany\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Duke of Albany\"><PERSON>, 2nd Duke of Albany</a>, Scottish politician (b. 1362)", "links": [{"title": "<PERSON>, 2nd Duke of Albany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Albany"}]}, {"year": "1456", "text": "<PERSON><PERSON><PERSON>, French commander (b. 1396)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/Ambro<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French commander (b. 1396)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ambro<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French commander (b. 1396)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ambro<PERSON>_de_<PERSON>r%C3%A9"}]}, {"year": "1543", "text": "<PERSON><PERSON>, Polish mathematician and astronomer (b. 1473)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mathematician and astronomer (b. 1473)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mathematician and astronomer (b. 1473)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Copernicus"}]}, {"year": "1612", "text": "<PERSON>, 1st Earl of Salisbury, English politician, Lord High Treasurer (b. 1563)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Salisbury\" title=\"<PERSON>, 1st Earl of Salisbury\"><PERSON>, 1st Earl of Salisbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Salisbury\" title=\"<PERSON>, 1st Earl of Salisbury\"><PERSON>, 1st Earl of Salisbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1563)", "links": [{"title": "<PERSON>, 1st Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Salisbury"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1627", "text": "<PERSON>, Spanish poet and cleric (b. 1561)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3ngora\" title=\"<PERSON>ón<PERSON>\"><PERSON></a>, Spanish poet and cleric (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3ngora\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and cleric (b. 1561)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_de_G%C3%B3ngora"}]}, {"year": "1632", "text": "<PERSON>, English mathematician and geographer (b. 1553)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and geographer (b. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and geographer (b. 1553)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1665", "text": "<PERSON> of <PERSON> of Ágreda, Spanish Franciscan abbess and mystic (b. 1602)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>_of_%C3%81greda\" title=\"<PERSON> of Jesus of Ágreda\"><PERSON> of Jesus of Ágreda</a>, Spanish <a href=\"https://wikipedia.org/wiki/Franciscan\" class=\"mw-redirect\" title=\"Franciscan\">Franciscan</a> <a href=\"https://wikipedia.org/wiki/Abbess\" title=\"Abbess\">abbess</a> and <a href=\"https://wikipedia.org/wiki/Spanish_mystics\" title=\"Spanish mystics\">mystic</a> (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mary_of_Jesus_of_%C3%81greda\" title=\"<PERSON> of Jesus of Ágreda\"><PERSON> of Jesus of Ágreda</a>, Spanish <a href=\"https://wikipedia.org/wiki/Franciscan\" class=\"mw-redirect\" title=\"Franciscan\">Franciscan</a> <a href=\"https://wikipedia.org/wiki/Abbess\" title=\"Abbess\">abbess</a> and <a href=\"https://wikipedia.org/wiki/Spanish_mystics\" title=\"Spanish mystics\">mystic</a> (b. 1602)", "links": [{"title": "<PERSON> of Jesus of Ágreda", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_%C3%81greda"}, {"title": "Franciscan", "link": "https://wikipedia.org/wiki/Franciscan"}, {"title": "Abbess", "link": "https://wikipedia.org/wiki/Abbess"}, {"title": "Spanish mystics", "link": "https://wikipedia.org/wiki/Spanish_mystics"}]}, {"year": "1734", "text": "<PERSON>, German physician and chemist (b. 1660)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and chemist (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and chemist (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, 1st Baron <PERSON>, English admiral and politician, 16th Governor of Newfoundland (b. 1718)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Newfoundland\" class=\"mw-redirect\" title=\"Governor of Newfoundland\">Governor of Newfoundland</a> (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Newfoundland\" class=\"mw-redirect\" title=\"Governor of Newfoundland\">Governor of Newfoundland</a> (b. 1718)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Governor of Newfoundland", "link": "https://wikipedia.org/wiki/Governor_of_Newfoundland"}]}, {"year": "1806", "text": "<PERSON>, 5th Duke of Argyll, Scottish field marshal and politician, Lord Lieutenant of Argyllshire (b. 1723)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Duke_of_Argyll\" title=\"<PERSON>, 5th Duke of Argyll\"><PERSON>, 5th Duke of Argyll</a>, Scottish field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Argyllshire\" title=\"Lord Lieutenant of Argyllshire\">Lord Lieutenant of Argyllshire</a> (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Duke_of_Argyll\" title=\"<PERSON>, 5th Duke of Argyll\"><PERSON>, 5th Duke of Argyll</a>, Scottish field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Argyllshire\" title=\"Lord Lieutenant of Argyllshire\">Lord Lieutenant of Argyllshire</a> (b. 1723)", "links": [{"title": "<PERSON>, 5th Duke of Argyll", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Duke_of_Argyll"}, {"title": "Lord Lieutenant of Argyllshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Argyllshire"}]}, {"year": "1843", "text": "<PERSON><PERSON><PERSON><PERSON>, French mathematician and academic (b. 1765)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/Sylves<PERSON>_<PERSON>an%C3%A7ois_<PERSON>x\" title=\"<PERSON>ylves<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mathematician and academic (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syl<PERSON><PERSON>_<PERSON>an%C3%A7ois_Lacroix\" title=\"<PERSON>yl<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mathematician and academic (b. 1765)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sylvestre_Fran%C3%A7ois_Lacroix"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German author and composer (b. 1797)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-H%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and composer (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and composer (b. 1797)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-H%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American colonel (b. 1837)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON> von <PERSON>, German painter and illustrator (b. 1794)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_Carolsfeld\" title=\"<PERSON> von Carolsfeld\"><PERSON> von <PERSON>feld</a>, German painter and illustrator (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_Carolsfeld\" title=\"<PERSON> von Carolsfeld\"><PERSON> von Carolsfeld</a>, German painter and illustrator (b. 1794)", "links": [{"title": "<PERSON> von Carol<PERSON>feld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American journalist and activist (b. 1805)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, English painter and illustrator (b. 1805)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian bishop (b. 1824)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Louis-Z%C3%A9<PERSON><PERSON>_<PERSON>au\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian bishop (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis-Z%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian bishop (b. 1824)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-Z%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON> <PERSON>, Scottish golfer and architect (b. 1821)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Old_Tom_Morris\" title=\"Old Tom Morris\">Old <PERSON></a>, Scottish golfer and architect (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Old_Tom_Morris\" title=\"Old Tom Morris\">Old <PERSON></a>, Scottish golfer and architect (b. 1821)", "links": [{"title": "Old <PERSON>", "link": "https://wikipedia.org/wiki/Old_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Irish-English soldier (b. 1896)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_soldier)\" title=\"<PERSON> (British Army soldier)\"><PERSON></a>, Irish-English soldier (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_soldier)\" title=\"<PERSON> (British Army soldier)\"><PERSON></a>, Irish-English soldier (b. 1896)", "links": [{"title": "<PERSON> (British Army soldier)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_soldier)"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Mexican poet, journalist, and educator (b. 1870)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Amado_Nervo\" title=\"Amado Nervo\"><PERSON><PERSON> Nervo</a>, Mexican poet, journalist, and educator (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amado_Nervo\" title=\"Amado Nervo\"><PERSON><PERSON> Nervo</a>, Mexican poet, journalist, and educator (b. 1870)", "links": [{"title": "Amado Nervo", "link": "https://wikipedia.org/wiki/Amado_Nervo"}]}, {"year": "1923", "text": "<PERSON>, Norwegian engineer (b. 1941)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>k%C3%A5r\" title=\"<PERSON>\"><PERSON></a>, Norwegian engineer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>k%C3%A5r\" title=\"<PERSON>\"><PERSON></a>, Norwegian engineer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rolf_Sk%C3%A5r"}]}, {"year": "1929", "text": "<PERSON>, Russian engineer (b. 1863)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American biologist (b. 1851)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rls"}]}, {"year": "1941", "text": "<PERSON><PERSON>, English admiral (b. 1887)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Lancelot_Holland\" title=\"Lancelot Holland\"><PERSON><PERSON></a>, English admiral (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lancelot_Holland\" title=\"Lancelot Holland\"><PERSON><PERSON></a>, English admiral (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lancelot_Holland"}]}, {"year": "1945", "text": "<PERSON>, German field marshal and pilot (b. 1892)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal and pilot (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal and pilot (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Belgian actor, director, and screenwriter (b. 1885)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian actor, director, and screenwriter (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian actor, director, and screenwriter (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Russian architect, designed Lenin's Mausoleum and Moscow Kazanskaya railway station (b. 1873)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian architect, designed <a href=\"https://wikipedia.org/wiki/Lenin%27s_Mausoleum\" title=\"<PERSON>'s Mausoleum\"><PERSON>'s Mausoleum</a> and <a href=\"https://wikipedia.org/wiki/Moscow_Kazanskaya_railway_station\" class=\"mw-redirect\" title=\"Moscow Kazanskaya railway station\">Moscow Kazanskaya railway station</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian architect, designed <a href=\"https://wikipedia.org/wiki/Lenin%27s_Mausoleum\" title=\"<PERSON>'s Mausoleum\"><PERSON>'s Mausoleum</a> and <a href=\"https://wikipedia.org/wiki/Moscow_Kazanskaya_railway_station\" class=\"mw-redirect\" title=\"Moscow Kazanskaya railway station\">Moscow Kazanskaya railway station</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>'s Mausoleum", "link": "https://wikipedia.org/wiki/Lenin%27s_Mausoleum"}, {"title": "Moscow Kazanskaya railway station", "link": "https://wikipedia.org/wiki/Moscow_Kazanskaya_railway_station"}]}, {"year": "1950", "text": "<PERSON>, 1st <PERSON>, English field marshal and politician, 43rd Governor-General of India (b. 1883)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English field marshal and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English field marshal and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (b. 1883)", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>"}, {"title": "Governor-General of India", "link": "https://wikipedia.org/wiki/Governor-General_of_India"}]}, {"year": "1951", "text": "<PERSON>, American actor, director, screenwriter (b. 1872)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, screenwriter (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, screenwriter (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English chemist and mathematician (b. 1866)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and mathematician (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and mathematician (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Australian public servant (b. 1895)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1895)", "links": [{"title": "<PERSON> (public servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(public_servant)"}]}, {"year": "1959", "text": "<PERSON>, American soldier, lawyer, and politician, 52nd United States Secretary of State (b. 1888)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 52nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 52nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1918)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and harmonica player (b. 1908)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Williamson_II\" title=\"Sonny Boy Williamson II\"><PERSON></a>, American singer-songwriter and harmonica player (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Boy_<PERSON>_II\" title=\"Sonny Boy Williamson II\"><PERSON></a>, American singer-songwriter and harmonica player (b. 1908)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_II"}]}, {"year": "1974", "text": "<PERSON>, American pianist and composer (b. 1899)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian actress (b. 1923)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English organist, composer, and educator (b. 1890)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer, and educator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer, and educator (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Swiss race car driver (b. 1940)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Swiss race car driver (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Swiss race car driver (b. 1940)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Herbert_M%C3%BCller_(racing_driver)"}]}, {"year": "1984", "text": "<PERSON>, American wrestling promoter and businessman, founded WWE (b. 1914)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Sr.\"><PERSON> Sr.</a>, American wrestling promoter and businessman, founded <a href=\"https://wikipedia.org/wiki/WWE\" title=\"WWE\">WWE</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American wrestling promoter and businessman, founded <a href=\"https://wikipedia.org/wiki/WWE\" title=\"WWE\">WWE</a> (b. 1914)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "WWE", "link": "https://wikipedia.org/wiki/WWE"}]}, {"year": "1988", "text": "<PERSON>, English motorcycle road racer (b. 1909)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle road racer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle road racer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian painter (b. 1910)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American singer-songwriter and guitarist  (b. 1944)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese race car driver (b. 1956)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English academic and politician, Prime Minister of the United Kingdom (b. 1916)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1996", "text": "<PERSON>, American admiral (b. 1909)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Mexican actor (b. 1934)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON>ez_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, Mexican actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON>ez_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, Mexican actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrique_%C3%81l<PERSON><PERSON>_F%C3%A9lix"}]}, {"year": "1996", "text": "<PERSON>, American journalist and author (b. 1908)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American journalist and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American journalist and author (b. 1908)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1997", "text": "<PERSON>, Irish actor (b. 1923)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American journalist and scholar (b. 1947)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and scholar (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and scholar (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Indian poet and songwriter (b. 1919)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Majrooh_Sultanpuri\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and songwriter (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Majroo<PERSON>_Sultanpuri\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and songwriter (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Majrooh_Sultanpuri"}]}, {"year": "2002", "text": "<PERSON>, American author (b. 1926)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English actress (b. 1910)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, German-American photographer (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American photographer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American photographer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian author and critic (b. 1913)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and critic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and critic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American critic and educator (b. 1900)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic and educator (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic and educator (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, German activist and author (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Belgian journalist and poet (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian journalist and poet (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian journalist and poet (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian academic and politician (b. 1935)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American art director and production designer (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art director and production designer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art director and production designer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, French actor (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9plu\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9plu\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Claude_Pi%C3%A9plu"}]}, {"year": "2008", "text": "<PERSON>, American actor, comedian, and director (b. 1922)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" class=\"mw-redirect\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor, comedian, and director (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" class=\"mw-redirect\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor, comedian, and director (b. 1922)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)"}]}, {"year": "2008", "text": "<PERSON>, American organist and bandleader (b. 1936)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and bandleader (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and bandleader (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, British-American astronomer (b.1947)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American astronomer (b.1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American astronomer (b.1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1963)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English ventriloquist, actor, and screenwriter (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ventriloquist, actor, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ventriloquist, actor, and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American bass player and songwriter (b. 1972)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American bass player and songwriter (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American bass player and songwriter (b. 1972)", "links": [{"title": "<PERSON> (American musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(American_musician)"}]}, {"year": "2010", "text": "<PERSON>, American businessman and activist (b. 1920)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and activist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and activist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Czech singer-songwriter and guitarist (b. 1965)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech singer-songwriter and guitarist (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech singer-songwriter and guitarist (b. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, German soprano and actress (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soprano and actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soprano and actress (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, American heiress, painter, and philanthropist (b. 1906)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American heiress, painter, and philanthropist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American heiress, painter, and philanthropist (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Indian-Pakistani businessman and politician (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Pakistani businessman and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Pakistani businessman and politician (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Dutch-German SS officer (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American journalist and author (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Goldmark\" title=\"<PERSON>hi Kamen Goldmark\"><PERSON><PERSON></a>, American journalist and author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Goldmark\" title=\"<PERSON><PERSON> Kamen Goldmark\"><PERSON><PERSON></a>, American journalist and author (b. 1948)", "links": [{"title": "<PERSON><PERSON> Goldmark", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>mark"}]}, {"year": "2012", "text": "<PERSON>, Belgian psychoanalyst and author (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian psychoanalyst and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian psychoanalyst and author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Argentinian footballer (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American production manager and producer (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American production manager and producer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lee Rich\"><PERSON></a>, American production manager and producer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German-American violinist and composer (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American violinist and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American violinist and composer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Welsh footballer (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1942)\" title=\"<PERSON> (footballer, born 1942)\"><PERSON></a>, Welsh footballer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1942)\" title=\"<PERSON> (footballer, born 1942)\"><PERSON></a>, Welsh footballer (b. 1942)", "links": [{"title": "<PERSON> (footballer, born 1942)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1942)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, German painter (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> G<PERSON>ubner\"><PERSON><PERSON><PERSON></a>, German painter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> G<PERSON>ubner\"><PERSON><PERSON><PERSON></a>, German painter (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist and author (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian director and screenwriter (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian director and screenwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian director and screenwriter (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English cricketer (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1935)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, known as the \"Rosa Parks of the lesbian community\" (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Storm%C3%A9_DeLarverie\" title=\"<PERSON>é <PERSON>\"><PERSON><PERSON></a>, known as the \"Rosa Parks of the lesbian community\" (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Storm%C3%A9_DeLarverie\" title=\"<PERSON>é <PERSON>\"><PERSON><PERSON></a>, known as the \"Rosa Parks of the lesbian community\" (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Storm%C3%A9_DeLar<PERSON>ie"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian businessman (b. 1969)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian businessman (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian businessman (b. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Canadian journalist and author (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>lton_Nash\" title=\"Knowlton Nash\"><PERSON><PERSON></a>, Canadian journalist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lton_Nash\" title=\"Knowlton Nash\"><PERSON><PERSON></a>, Canadian journalist and author (b. 1927)", "links": [{"title": "<PERSON><PERSON> Nash", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Nash"}]}, {"year": "2014", "text": "<PERSON>, American lieutenant, lawyer, and politician (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English rugby player (b. 1962)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian lawyer and judge (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and judge (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and judge (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, English author (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON> (TotalBiscuit), English gaming commentator and critic (b. 1984)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/TotalBiscuit\" title=\"TotalBiscuit\"><PERSON></a> (TotalBiscuit), English gaming commentator and critic (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TotalBiscuit\" title=\"TotalBiscuit\"><PERSON></a> (TotalBiscuit), English gaming commentator and critic (b. 1984)", "links": [{"title": "TotalBiscuit", "link": "https://wikipedia.org/wiki/TotalBiscuit"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, daughter of <PERSON><PERSON> and <PERSON> (b. 1929)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American-Swiss rock and pop singer, dancer, actress and author (b. 1939)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss rock and pop singer, dancer, actress and author (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss rock and pop singer, dancer, actress and author (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American musician (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Japanese dog and Internet meme celebrity (b. 2005)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(dog)\" title=\"<PERSON><PERSON><PERSON> (dog)\"><PERSON><PERSON><PERSON></a>, Japanese dog and Internet meme celebrity (b. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(dog)\" title=\"<PERSON><PERSON><PERSON> (dog)\"><PERSON><PERSON><PERSON></a>, Japanese dog and Internet meme celebrity (b. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON> (dog)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(dog)"}]}]}}