{"date": "February 1", "url": "https://wikipedia.org/wiki/February_1", "data": {"Events": [{"year": "1327", "text": "The teenaged <PERSON> is crowned King of England, but the country is ruled by his mother <PERSON> <PERSON> and her lover <PERSON>.", "html": "1327 - The teenaged <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a> is crowned King of England, but the country is ruled by his mother <a href=\"https://wikipedia.org/wiki/Isabella_of_France\" title=\"<PERSON> of France\">Queen <PERSON></a> and her lover <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_<PERSON>\" title=\"<PERSON>, 1st Earl of <PERSON>\"><PERSON></a>.", "no_year_html": "The teenaged <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a> is crowned King of England, but the country is ruled by his mother <a href=\"https://wikipedia.org/wiki/Isabella_of_France\" title=\"<PERSON> of France\">Queen <PERSON></a> and her lover <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_<PERSON>\" title=\"<PERSON>, 1st Earl of <PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Isabella_of_France"}, {"title": "<PERSON>, 1st Earl of March", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_<PERSON>"}]}, {"year": "1411", "text": "The First Peace of Thorn is signed in Thorn (Toruń), Monastic State of the Teutonic Knights (Prussia).", "html": "1411 - The <a href=\"https://wikipedia.org/wiki/Peace_of_Thorn_(1411)\" title=\"Peace of Thorn (1411)\">First Peace of Thorn</a> is signed in <a href=\"https://wikipedia.org/wiki/Toru%C5%84\" title=\"Toruń\"><PERSON> (Toruń)</a>, <a href=\"https://wikipedia.org/wiki/Monastic_State_of_the_Teutonic_Knights\" class=\"mw-redirect\" title=\"Monastic State of the Teutonic Knights\">Monastic State of the Teutonic Knights</a> (Prussia).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peace_of_Thorn_(1411)\" title=\"Peace of Thorn (1411)\">First Peace of Thorn</a> is signed in <a href=\"https://wikipedia.org/wiki/Toru%C5%84\" title=\"Toruń\"><PERSON> (Toruń)</a>, <a href=\"https://wikipedia.org/wiki/Monastic_State_of_the_Teutonic_Knights\" class=\"mw-redirect\" title=\"Monastic State of the Teutonic Knights\">Monastic State of the Teutonic Knights</a> (Prussia).", "links": [{"title": "Peace of Thorn (1411)", "link": "https://wikipedia.org/wiki/Peace_of_Thorn_(1411)"}, {"title": "Toruń", "link": "https://wikipedia.org/wiki/Toru%C5%84"}, {"title": "Monastic State of the Teutonic Knights", "link": "https://wikipedia.org/wiki/Monastic_State_of_the_Teutonic_Knights"}]}, {"year": "1662", "text": "The Chinese general <PERSON><PERSON><PERSON> seizes the island of Taiwan after a nine-month siege.", "html": "1662 - The Chinese general <a href=\"https://wikipedia.org/wiki/Koxinga\" title=\"Koxinga\"><PERSON><PERSON><PERSON></a> seizes the island of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> after a <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Zeelandia\" title=\"Siege of Fort Zeelandia\">nine-month siege</a>.", "no_year_html": "The Chinese general <a href=\"https://wikipedia.org/wiki/Koxinga\" title=\"Koxinga\"><PERSON><PERSON><PERSON></a> seizes the island of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> after a <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Zeelandia\" title=\"Siege of Fort Zeelandia\">nine-month siege</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "Siege of Fort Zeelandia", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Zeelandia"}]}, {"year": "1713", "text": "The Kalabalik or Skirmish at Bender results from the Ottoman Sultan <PERSON>'s order that his unwelcome guest, King <PERSON> of Sweden, be seized.", "html": "1713 - The <i><PERSON><PERSON><PERSON><PERSON></i> or <i><a href=\"https://wikipedia.org/wiki/Skirmish_at_Bender\" title=\"Skirmish at Bender\">Skirmish at Bender</a></i> results from the Ottoman Sultan <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> III</a>'s order that his unwelcome guest, King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>, be seized.", "no_year_html": "The <i>Kalabalik</i> or <i><a href=\"https://wikipedia.org/wiki/Skirmish_at_Bender\" title=\"Skirmish at Bender\">Skirmish at Bender</a></i> results from the Ottoman Sultan <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s order that his unwelcome guest, King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>, be seized.", "links": [{"title": "Skirmish at Bender", "link": "https://wikipedia.org/wiki/Skirmish_at_Bender"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1793", "text": "French Revolutionary Wars: France declares war on the United Kingdom and the Netherlands.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: France declares war on the United Kingdom and the Netherlands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: France declares war on the United Kingdom and the Netherlands.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}]}, {"year": "1796", "text": "The capital of Upper Canada is moved from Newark to York.", "html": "1796 - The capital of <a href=\"https://wikipedia.org/wiki/Upper_Canada\" title=\"Upper Canada\">Upper Canada</a> is moved from <a href=\"https://wikipedia.org/wiki/Newark,_Upper_Canada\" class=\"mw-redirect\" title=\"Newark, Upper Canada\">Newark</a> to <a href=\"https://wikipedia.org/wiki/York,_Upper_Canada\" title=\"York, Upper Canada\">York</a>.", "no_year_html": "The capital of <a href=\"https://wikipedia.org/wiki/Upper_Canada\" title=\"Upper Canada\">Upper Canada</a> is moved from <a href=\"https://wikipedia.org/wiki/Newark,_Upper_Canada\" class=\"mw-redirect\" title=\"Newark, Upper Canada\">Newark</a> to <a href=\"https://wikipedia.org/wiki/York,_Upper_Canada\" title=\"York, Upper Canada\">York</a>.", "links": [{"title": "Upper Canada", "link": "https://wikipedia.org/wiki/Upper_Canada"}, {"title": "Newark, Upper Canada", "link": "https://wikipedia.org/wiki/Newark,_Upper_Canada"}, {"title": "York, Upper Canada", "link": "https://wikipedia.org/wiki/York,_Upper_Canada"}]}, {"year": "1814", "text": "Mayon in the Philippines erupts, killing around 1,200 people, which was the most devastating eruption of the volcano.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Mayon\" title=\"Mayon\">Mayon</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> erupts, killing around 1,200 people, which was the most devastating eruption of the volcano.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mayon\" title=\"Mayon\">Mayon</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> erupts, killing around 1,200 people, which was the most devastating eruption of the volcano.", "links": [{"title": "Mayon", "link": "https://wikipedia.org/wiki/Mayon"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1835", "text": "Slavery is abolished in Mauritius.", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Abolition_of_slavery\" class=\"mw-redirect\" title=\"Abolition of slavery\">Slavery is abolished</a> in <a href=\"https://wikipedia.org/wiki/Mauritius\" title=\"Mauritius\">Mauritius</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abolition_of_slavery\" class=\"mw-redirect\" title=\"Abolition of slavery\">Slavery is abolished</a> in <a href=\"https://wikipedia.org/wiki/Mauritius\" title=\"Mauritius\">Mauritius</a>.", "links": [{"title": "Abolition of slavery", "link": "https://wikipedia.org/wiki/Abolition_of_slavery"}, {"title": "Mauritius", "link": "https://wikipedia.org/wiki/Mauritius"}]}, {"year": "1861", "text": "American Civil War: Texas secedes from the United States and joins the Confederacy a week later.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> <a href=\"https://wikipedia.org/wiki/Texas_in_the_American_Civil_War#Seccession\" title=\"Texas in the American Civil War\">secedes</a> from the United States and joins the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederacy</a> a week later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> <a href=\"https://wikipedia.org/wiki/Texas_in_the_American_Civil_War#Seccession\" title=\"Texas in the American Civil War\">secedes</a> from the United States and joins the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederacy</a> a week later.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Texas in the American Civil War", "link": "https://wikipedia.org/wiki/Texas_in_the_American_Civil_War#Seccession"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1864", "text": "Second Schleswig War: Prussian forces crossed the border into Schleswig, starting the war.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Second_Schleswig_War\" title=\"Second Schleswig War\">Second Schleswig War</a>: Prussian forces crossed the border into Schleswig, starting the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Schleswig_War\" title=\"Second Schleswig War\">Second Schleswig War</a>: Prussian forces crossed the border into Schleswig, starting the war.", "links": [{"title": "Second Schleswig War", "link": "https://wikipedia.org/wiki/Second_Schleswig_War"}]}, {"year": "1865", "text": "President <PERSON> signs the Thirteenth Amendment to the United States Constitution.", "html": "1865 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment to the United States Constitution</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment to the United States Constitution</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Thirteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1884", "text": "The first volume (A to Ant) of the Oxford English Dictionary is published.", "html": "1884 - The first volume (<i>A</i> to <i>Ant</i>) of the <i><a href=\"https://wikipedia.org/wiki/Oxford_English_Dictionary\" title=\"Oxford English Dictionary\">Oxford English Dictionary</a></i> is published.", "no_year_html": "The first volume (<i>A</i> to <i>Ant</i>) of the <i><a href=\"https://wikipedia.org/wiki/Oxford_English_Dictionary\" title=\"Oxford English Dictionary\">Oxford English Dictionary</a></i> is published.", "links": [{"title": "Oxford English Dictionary", "link": "https://wikipedia.org/wiki/Oxford_English_Dictionary"}]}, {"year": "1893", "text": "Thomas A. Edison finishes construction of the first motion picture studio, the Black Maria in West Orange, New Jersey.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Thomas A<PERSON> Edison\"><PERSON></a> finishes construction of the first <a href=\"https://wikipedia.org/wiki/Motion_picture_studio\" class=\"mw-redirect\" title=\"Motion picture studio\">motion picture studio</a>, the <a href=\"https://wikipedia.org/wiki/Edison%27s_Black_Maria\" title=\"Edison's Black Maria\">Black Maria</a> in <a href=\"https://wikipedia.org/wiki/West_Orange,_New_Jersey\" title=\"West Orange, New Jersey\">West Orange, New Jersey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Edison\" class=\"mw-redirect\" title=\"Thomas A. Edison\"><PERSON></a> finishes construction of the first <a href=\"https://wikipedia.org/wiki/Motion_picture_studio\" class=\"mw-redirect\" title=\"Motion picture studio\">motion picture studio</a>, the <a href=\"https://wikipedia.org/wiki/Edison%27s_Black_Maria\" title=\"Edison's Black Maria\">Black Maria</a> in <a href=\"https://wikipedia.org/wiki/West_Orange,_New_Jersey\" title=\"West Orange, New Jersey\">West Orange, New Jersey</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Motion picture studio", "link": "https://wikipedia.org/wiki/Motion_picture_studio"}, {"title": "Edison's Black Maria", "link": "https://wikipedia.org/wiki/Edison%27s_Black_Maria"}, {"title": "West Orange, New Jersey", "link": "https://wikipedia.org/wiki/West_Orange,_New_Jersey"}]}, {"year": "1895", "text": "Fountains Valley, Pretoria, the oldest nature reserve in Africa, is proclaimed by President <PERSON>.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Fountains_Valley,_Pretoria\" title=\"Fountains Valley, Pretoria\">Fountains Valley, Pretoria</a>, the oldest <a href=\"https://wikipedia.org/wiki/Nature_reserve\" title=\"Nature reserve\">nature reserve</a> in Africa, is proclaimed by President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fountains_Valley,_Pretoria\" title=\"Fountains Valley, Pretoria\">Fountains Valley, Pretoria</a>, the oldest <a href=\"https://wikipedia.org/wiki/Nature_reserve\" title=\"Nature reserve\">nature reserve</a> in Africa, is proclaimed by President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Fountains Valley, Pretoria", "link": "https://wikipedia.org/wiki/Fountains_Valley,_Pretoria"}, {"title": "Nature reserve", "link": "https://wikipedia.org/wiki/Nature_reserve"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1896", "text": "La bohème premieres in Turin at the Teatro Regio (Turin), conducted by the young <PERSON>.", "html": "1896 - <i><a href=\"https://wikipedia.org/wiki/La_boh%C3%A8me\" title=\"<PERSON> bohème\"><PERSON> bohème</a></i> premieres in Turin at the <a href=\"https://wikipedia.org/wiki/Teatro_Regio_(Turin)\" title=\"Teatro Regio (Turin)\">Teatro Regio (Turin)</a>, conducted by the young <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/La_boh%C3%A8me\" title=\"La bohème\"><PERSON> bohème</a></i> premieres in Turin at the <a href=\"https://wikipedia.org/wiki/Teatro_Regio_(Turin)\" title=\"Teatro Regio (Turin)\">Teatro Regio (Turin)</a>, conducted by the young <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "La bohème", "link": "https://wikipedia.org/wiki/La_boh%C3%A8me"}, {"title": "Teatro Regio (Turin)", "link": "https://wikipedia.org/wiki/Teatro_Regio_(Turin)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "Shinhan Bank, the oldest bank in South Korea, opens in Seoul.", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Shinhan_Bank\" title=\"Shinhan Bank\">Shinhan Bank</a>, the <a href=\"https://wikipedia.org/wiki/List_of_banks_in_South_Korea\" title=\"List of banks in South Korea\">oldest bank in South Korea</a>, opens in <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shinhan_Bank\" title=\"Shinhan Bank\">Shinhan Bank</a>, the <a href=\"https://wikipedia.org/wiki/List_of_banks_in_South_Korea\" title=\"List of banks in South Korea\">oldest bank in South Korea</a>, opens in <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>.", "links": [{"title": "Shinhan Bank", "link": "https://wikipedia.org/wiki/Shinhan_Bank"}, {"title": "List of banks in South Korea", "link": "https://wikipedia.org/wiki/List_of_banks_in_South_Korea"}, {"title": "Seoul", "link": "https://wikipedia.org/wiki/Seoul"}]}, {"year": "1900", "text": "Great Britain, defeated by Boers in key battles, names <PERSON> commander of British forces in South Africa.", "html": "1900 - Great Britain, defeated by Boers in key battles, names <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\">Lord <PERSON></a> commander of British forces in South Africa.", "no_year_html": "Great Britain, defeated by Boers in key battles, names <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\">Lord <PERSON></a> commander of British forces in South Africa.", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "Lisbon Regicide: King <PERSON> of Portugal and <PERSON><PERSON><PERSON> are shot dead in Lisbon.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Lisbon_Regicide\" title=\"Lisbon Regicide\">Lisbon Regicide</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> and <a href=\"https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON>,_Prince_<PERSON>_of_Portugal\" title=\"<PERSON><PERSON>, Prince <PERSON> of Portugal\">Infant<PERSON></a> are shot dead in <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lisbon_Regicide\" title=\"Lisbon Regicide\">Lisbon Regicide</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> and <a href=\"https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON><PERSON>,_Prince_<PERSON>_of_Portugal\" title=\"<PERSON><PERSON>, Prince <PERSON> of Portugal\">Infant<PERSON></a> are shot dead in <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a>.", "links": [{"title": "Lisbon Regicide", "link": "https://wikipedia.org/wiki/Lisbon_Regicide"}, {"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}, {"title": "<PERSON><PERSON>, Prince <PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>,_Prince_Royal_of_Portugal"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}]}, {"year": "1924", "text": "Russia-United Kingdom relations are restored, over six years after the Communist revolution.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Russia%E2%80%93United_Kingdom_relations\" title=\"Russia-United Kingdom relations\">Russia-United Kingdom relations</a> are restored, over six years after the Communist revolution.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russia%E2%80%93United_Kingdom_relations\" title=\"Russia-United Kingdom relations\">Russia-United Kingdom relations</a> are restored, over six years after the Communist revolution.", "links": [{"title": "Russia-United Kingdom relations", "link": "https://wikipedia.org/wiki/Russia%E2%80%93United_Kingdom_relations"}]}, {"year": "1942", "text": "World War II: <PERSON>, Reichskommissar of German-occupied Norway, appoints <PERSON><PERSON><PERSON><PERSON> the Minister President of the National Government.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Reichskommissar\" title=\"Reichskommissar\">Reichskommissar</a> of <a href=\"https://wikipedia.org/wiki/German_occupation_of_Norway\" title=\"German occupation of Norway\">German-occupied Norway</a>, appoints <a href=\"https://wikipedia.org/wiki/Vidkun_Quisling\" title=\"Vidku<PERSON>\">V<PERSON><PERSON><PERSON></a> the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Minister President</a> of the <a href=\"https://wikipedia.org/wiki/Quisling_regime\" title=\"Quisling regime\">National Government</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Reichskommissar\" title=\"Reichskommissar\">Reichskommissar</a> of <a href=\"https://wikipedia.org/wiki/German_occupation_of_Norway\" title=\"German occupation of Norway\">German-occupied Norway</a>, appoints <a href=\"https://wikipedia.org/wiki/Vidkun_Quisling\" title=\"Vidku<PERSON>\">V<PERSON><PERSON><PERSON></a> the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Minister President</a> of the <a href=\"https://wikipedia.org/wiki/Quisling_regime\" title=\"Quisling regime\">National Government</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reichskommissar", "link": "https://wikipedia.org/wiki/Reichskommissar"}, {"title": "German occupation of Norway", "link": "https://wikipedia.org/wiki/German_occupation_of_Norway"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vidku<PERSON>_<PERSON>ui<PERSON>ling"}, {"title": "Prime Minister of Norway", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Norway"}, {"title": "<PERSON><PERSON><PERSON><PERSON> regime", "link": "https://wikipedia.org/wiki/Quisling_regime"}]}, {"year": "1942", "text": "World War II: U.S. Navy conducts Marshalls-Gilberts raids, the first offensive action by the United States against Japanese forces in the Pacific Theater.", "html": "1942 - World War II: U.S. Navy conducts <a href=\"https://wikipedia.org/wiki/Marshalls%E2%80%93Gilberts_raids\" title=\"Marshalls-Gilberts raids\">Marshalls-Gilberts raids</a>, the first offensive action by the United States against Japanese forces in the <a href=\"https://wikipedia.org/wiki/Pacific_Ocean_theater_of_World_War_II\" title=\"Pacific Ocean theater of World War II\">Pacific Theater</a>.", "no_year_html": "World War II: U.S. Navy conducts <a href=\"https://wikipedia.org/wiki/Marshalls%E2%80%93Gilberts_raids\" title=\"Marshalls-Gilberts raids\">Marshalls-Gilberts raids</a>, the first offensive action by the United States against Japanese forces in the <a href=\"https://wikipedia.org/wiki/Pacific_Ocean_theater_of_World_War_II\" title=\"Pacific Ocean theater of World War II\">Pacific Theater</a>.", "links": [{"title": "Marshalls-Gilberts raids", "link": "https://wikipedia.org/wiki/Marshalls%E2%80%93Gilberts_raids"}, {"title": "Pacific Ocean theater of World War II", "link": "https://wikipedia.org/wiki/Pacific_Ocean_theater_of_World_War_II"}]}, {"year": "1942", "text": "Voice of America, the official external radio and television service of the United States government, begins broadcasting with programs aimed at areas controlled by the Axis powers.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Voice_of_America\" title=\"Voice of America\">Voice of America</a>, the official external radio and television service of the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">United States government</a>, begins broadcasting with programs aimed at areas controlled by the <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis powers</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Voice_of_America\" title=\"Voice of America\">Voice of America</a>, the official external radio and television service of the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">United States government</a>, begins broadcasting with programs aimed at areas controlled by the <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis powers</a>.", "links": [{"title": "Voice of America", "link": "https://wikipedia.org/wiki/Voice_of_America"}, {"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}, {"title": "Axis powers", "link": "https://wikipedia.org/wiki/Axis_powers"}]}, {"year": "1942", "text": "<PERSON> makes a speech on \"Reform in Learning, the Party and Literature\", which puts into motion the Yan'an Rectification Movement.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes a speech on \"Reform in Learning, the Party and Literature\", which puts into motion the <a href=\"https://wikipedia.org/wiki/Yan%27an_Rectification_Movement\" title=\"Yan'an Rectification Movement\">Yan'an Rectification Movement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes a speech on \"Reform in Learning, the Party and Literature\", which puts into motion the <a href=\"https://wikipedia.org/wiki/Yan%27an_Rectification_Movement\" title=\"Yan'an Rectification Movement\">Yan'an Rectification Movement</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Yan'an Rectification Movement", "link": "https://wikipedia.org/wiki/Yan%27an_Rectification_Movement"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON> of Norway is picked to be the first United Nations Secretary-General.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Tryg<PERSON>_Lie\" title=\"Trygve Lie\"><PERSON>g<PERSON></a> of Norway is picked to be the first <a href=\"https://wikipedia.org/wiki/United_Nations_Secretary-General\" class=\"mw-redirect\" title=\"United Nations Secretary-General\">United Nations Secretary-General</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tryg<PERSON>_Lie\" title=\"Trygve Lie\"><PERSON>g<PERSON></a> of Norway is picked to be the first <a href=\"https://wikipedia.org/wiki/United_Nations_Secretary-General\" class=\"mw-redirect\" title=\"United Nations Secretary-General\">United Nations Secretary-General</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trygve_Lie"}, {"title": "United Nations Secretary-General", "link": "https://wikipedia.org/wiki/United_Nations_Secretary-General"}]}, {"year": "1946", "text": "The Parliament of Hungary abolishes the monarchy after nine centuries, and proclaims the Hungarian Republic.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/National_Assembly_(Hungary)\" title=\"National Assembly (Hungary)\">Parliament of Hungary</a> abolishes the monarchy after nine centuries, and proclaims the <a href=\"https://wikipedia.org/wiki/Republic_of_Hungary_(1946%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of Hungary (1946-49)\">Hungarian Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Assembly_(Hungary)\" title=\"National Assembly (Hungary)\">Parliament of Hungary</a> abolishes the monarchy after nine centuries, and proclaims the <a href=\"https://wikipedia.org/wiki/Republic_of_Hungary_(1946%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of Hungary (1946-49)\">Hungarian Republic</a>.", "links": [{"title": "National Assembly (Hungary)", "link": "https://wikipedia.org/wiki/National_Assembly_(Hungary)"}, {"title": "Republic of Hungary (1946-49)", "link": "https://wikipedia.org/wiki/Republic_of_Hungary_(1946%E2%80%9349)"}]}, {"year": "1950", "text": "The first prototype of the MiG-17 makes its maiden flight.", "html": "1950 - The first prototype of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-17\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-17\">MiG-17</a> makes its maiden flight.", "no_year_html": "The first prototype of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-17\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-17\">MiG-17</a> makes its maiden flight.", "links": [{"title": "Mikoyan<PERSON><PERSON><PERSON><PERSON> MiG-17", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-17"}]}, {"year": "1957", "text": "Northeast Airlines Flight 823 crashes on Rikers Island in New York City, killing 20 people and injuring 78 others.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Northeast_Airlines_Flight_823\" title=\"Northeast Airlines Flight 823\">Northeast Airlines Flight 823</a> crashes on <a href=\"https://wikipedia.org/wiki/Rikers_Island\" title=\"Rikers Island\">Rikers Island</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, killing 20 people and injuring 78 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northeast_Airlines_Flight_823\" title=\"Northeast Airlines Flight 823\">Northeast Airlines Flight 823</a> crashes on <a href=\"https://wikipedia.org/wiki/Rikers_Island\" title=\"Rikers Island\">Rikers Island</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, killing 20 people and injuring 78 others.", "links": [{"title": "Northeast Airlines Flight 823", "link": "https://wikipedia.org/wiki/Northeast_Airlines_Flight_823"}, {"title": "Rikers Island", "link": "https://wikipedia.org/wiki/Rikers_Island"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1960", "text": "Four black students stage the first of the Greensboro sit-ins at a lunch counter in Greensboro, North Carolina.", "html": "1960 - Four black students stage the first of the <a href=\"https://wikipedia.org/wiki/Greensboro_sit-ins\" title=\"Greensboro sit-ins\">Greensboro sit-ins</a> at a lunch counter in <a href=\"https://wikipedia.org/wiki/Greensboro,_North_Carolina\" title=\"Greensboro, North Carolina\">Greensboro, North Carolina</a>.", "no_year_html": "Four black students stage the first of the <a href=\"https://wikipedia.org/wiki/Greensboro_sit-ins\" title=\"Greensboro sit-ins\">Greensboro sit-ins</a> at a lunch counter in <a href=\"https://wikipedia.org/wiki/Greensboro,_North_Carolina\" title=\"Greensboro, North Carolina\">Greensboro, North Carolina</a>.", "links": [{"title": "Greensboro sit-ins", "link": "https://wikipedia.org/wiki/Greensboro_sit-ins"}, {"title": "Greensboro, North Carolina", "link": "https://wikipedia.org/wiki/Greensboro,_North_Carolina"}]}, {"year": "1964", "text": "The Beatles have their first number one hit in the United States with \"I Want to Hold Your Hand\".", "html": "1964 - <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> have their first number one hit in the United States with \"<a href=\"https://wikipedia.org/wiki/I_Want_to_Hold_Your_Hand\" title=\"I Want to Hold Your Hand\">I Want to Hold Your Hand</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> have their first number one hit in the United States with \"<a href=\"https://wikipedia.org/wiki/I_Want_to_Hold_Your_Hand\" title=\"I Want to Hold Your Hand\">I Want to Hold Your Hand</a>\".", "links": [{"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "I Want to Hold Your Hand", "link": "https://wikipedia.org/wiki/I_Want_to_Hold_Your_Hand"}]}, {"year": "1968", "text": "Vietnam War: The execution of Viet Cong officer <PERSON><PERSON><PERSON><PERSON> by South Vietnamese National Police Chief <PERSON><PERSON><PERSON><PERSON> is recorded on motion picture film, as well as in an iconic still photograph taken by <PERSON>.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The execution of <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> officer <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_L%C3%A9m\" class=\"mw-redirect\" title=\"Nguyễn Văn Lém\">Nguyễn <PERSON></a> by <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> National Police Chief <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ng%E1%BB%8Dc_Loan\" title=\"Nguyễn <PERSON>\"><PERSON>uy<PERSON><PERSON></a> is recorded on motion picture film, as well as in an iconic still photograph taken by <a href=\"https://wikipedia.org/wiki/<PERSON>(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The execution of <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> officer <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_L%C3%A9m\" class=\"mw-redirect\" title=\"Nguyễn Văn Lém\">Nguyễn <PERSON></a> by <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> National Police Chief <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ng%E1%BB%8Dc_Loan\" title=\"Nguyễn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is recorded on motion picture film, as well as in an iconic still photograph taken by <a href=\"https://wikipedia.org/wiki/<PERSON>(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_L%C3%A9m"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ng%E1%BB%8Dc_Loan"}, {"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)"}]}, {"year": "1968", "text": "Canada's three military services, the Royal Canadian Navy, the Canadian Army and the Royal Canadian Air Force, are unified into the Canadian Forces.", "html": "1968 - Canada's three military services, the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Navy\" title=\"Royal Canadian Navy\">Royal Canadian Navy</a>, the <a href=\"https://wikipedia.org/wiki/Canadian_Army\" title=\"Canadian Army\">Canadian Army</a> and the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Air_Force\" title=\"Royal Canadian Air Force\">Royal Canadian Air Force</a>, are unified into the <a href=\"https://wikipedia.org/wiki/Canadian_Forces\" class=\"mw-redirect\" title=\"Canadian Forces\">Canadian Forces</a>.", "no_year_html": "Canada's three military services, the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Navy\" title=\"Royal Canadian Navy\">Royal Canadian Navy</a>, the <a href=\"https://wikipedia.org/wiki/Canadian_Army\" title=\"Canadian Army\">Canadian Army</a> and the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Air_Force\" title=\"Royal Canadian Air Force\">Royal Canadian Air Force</a>, are unified into the <a href=\"https://wikipedia.org/wiki/Canadian_Forces\" class=\"mw-redirect\" title=\"Canadian Forces\">Canadian Forces</a>.", "links": [{"title": "Royal Canadian Navy", "link": "https://wikipedia.org/wiki/Royal_Canadian_Navy"}, {"title": "Canadian Army", "link": "https://wikipedia.org/wiki/Canadian_Army"}, {"title": "Royal Canadian Air Force", "link": "https://wikipedia.org/wiki/Royal_Canadian_Air_Force"}, {"title": "Canadian Forces", "link": "https://wikipedia.org/wiki/Canadian_Forces"}]}, {"year": "1968", "text": "The New York Central Railroad and the Pennsylvania Railroad are merged to form Penn Central Transportation.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/New_York_Central_Railroad\" title=\"New York Central Railroad\">New York Central Railroad</a> and the <a href=\"https://wikipedia.org/wiki/Pennsylvania_Railroad\" title=\"Pennsylvania Railroad\">Pennsylvania Railroad</a> are merged to form <a href=\"https://wikipedia.org/wiki/Penn_Central_Transportation\" class=\"mw-redirect\" title=\"Penn Central Transportation\">Penn Central Transportation</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_York_Central_Railroad\" title=\"New York Central Railroad\">New York Central Railroad</a> and the <a href=\"https://wikipedia.org/wiki/Pennsylvania_Railroad\" title=\"Pennsylvania Railroad\">Pennsylvania Railroad</a> are merged to form <a href=\"https://wikipedia.org/wiki/Penn_Central_Transportation\" class=\"mw-redirect\" title=\"Penn Central Transportation\">Penn Central Transportation</a>.", "links": [{"title": "New York Central Railroad", "link": "https://wikipedia.org/wiki/New_York_Central_Railroad"}, {"title": "Pennsylvania Railroad", "link": "https://wikipedia.org/wiki/Pennsylvania_Railroad"}, {"title": "Penn Central Transportation", "link": "https://wikipedia.org/wiki/Penn_Central_Transportation"}]}, {"year": "1972", "text": "Kuala Lumpur becomes a city by a royal charter granted by the Yang di-Pertuan Agong of Malaysia.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Kuala_Lumpur\" title=\"Kuala Lumpur\">Kuala Lumpur</a> becomes a city by a royal charter granted by the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuala_Lumpur\" title=\"Kuala Lumpur\">Kuala Lumpur</a> becomes a city by a royal charter granted by the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>.", "links": [{"title": "Kuala Lumpur", "link": "https://wikipedia.org/wiki/Kuala_Lumpur"}, {"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}, {"year": "1974", "text": "A fire in the 25-story Joelma Building in São Paulo, Brazil kills 189 and injures 293.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Joelma_fire\" class=\"mw-redirect\" title=\"Joelma fire\">A fire</a> in the 25-story <a href=\"https://wikipedia.org/wiki/Joelma_Building\" class=\"mw-redirect\" title=\"Joelma Building\">Joelma Building</a> in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo\" title=\"São Paulo\">São Paulo</a>, Brazil kills 189 and injures 293.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_fire\" class=\"mw-redirect\" title=\"Joelma fire\">A fire</a> in the 25-story <a href=\"https://wikipedia.org/wiki/Joelma_Building\" class=\"mw-redirect\" title=\"Joelma Building\">Joelma Building</a> in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo\" title=\"São Paulo\">São Paulo</a>, Brazil kills 189 and injures 293.", "links": [{"title": "Joelma fire", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_fire"}, {"title": "Joelma Building", "link": "https://wikipedia.org/wiki/Joelma_Building"}, {"title": "São Paulo", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo"}]}, {"year": "1979", "text": "Iranian A<PERSON><PERSON><PERSON> returns to Tehran after nearly 15 years of exile.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> <a href=\"https://wikipedia.org/wiki/Ayato<PERSON>\" title=\"Ayatollah\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/R<PERSON>ollah_Khomeini\" title=\"Ruholl<PERSON> Khomeini\"><PERSON><PERSON><PERSON><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a> after nearly 15 years of exile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> <a href=\"https://wikipedia.org/wiki/Ayato<PERSON>\" title=\"Ayatollah\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/R<PERSON>ollah_Khomeini\" title=\"Ruh<PERSON><PERSON> Khomeini\"><PERSON><PERSON><PERSON><PERSON>ein<PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a> after nearly 15 years of exile.", "links": [{"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>ollah_Khomeini"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}]}, {"year": "1981", "text": "The Underarm bowling incident of 1981 occurred when <PERSON> bowls underarm on the final delivery of a game between Australia and New Zealand at the Melbourne Cricket Ground (MCG).", "html": "1981 - The <a href=\"https://wikipedia.org/wiki/Underarm_bowling_incident_of_1981\" title=\"Underarm bowling incident of 1981\">Underarm bowling incident of 1981</a> occurred when <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> bowls underarm on the final delivery of a game between <a href=\"https://wikipedia.org/wiki/Australian_men%27s_national_cricket_team\" class=\"mw-redirect\" title=\"Australian men's national cricket team\">Australia</a> and <a href=\"https://wikipedia.org/wiki/New_Zealand_men%27s_national_cricket_team\" class=\"mw-redirect\" title=\"New Zealand men's national cricket team\">New Zealand</a> at the <a href=\"https://wikipedia.org/wiki/Melbourne_Cricket_Ground\" title=\"Melbourne Cricket Ground\">Melbourne Cricket Ground</a> (MCG).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Underarm_bowling_incident_of_1981\" title=\"Underarm bowling incident of 1981\">Underarm bowling incident of 1981</a> occurred when <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> bowls underarm on the final delivery of a game between <a href=\"https://wikipedia.org/wiki/Australian_men%27s_national_cricket_team\" class=\"mw-redirect\" title=\"Australian men's national cricket team\">Australia</a> and <a href=\"https://wikipedia.org/wiki/New_Zealand_men%27s_national_cricket_team\" class=\"mw-redirect\" title=\"New Zealand men's national cricket team\">New Zealand</a> at the <a href=\"https://wikipedia.org/wiki/Melbourne_Cricket_Ground\" title=\"Melbourne Cricket Ground\">Melbourne Cricket Ground</a> (MCG).", "links": [{"title": "Underarm bowling incident of 1981", "link": "https://wikipedia.org/wiki/Underarm_bowling_incident_of_1981"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Australian men's national cricket team", "link": "https://wikipedia.org/wiki/Australian_men%27s_national_cricket_team"}, {"title": "New Zealand men's national cricket team", "link": "https://wikipedia.org/wiki/New_Zealand_men%27s_national_cricket_team"}, {"title": "Melbourne Cricket Ground", "link": "https://wikipedia.org/wiki/Melbourne_Cricket_Ground"}]}, {"year": "1991", "text": "A runway collision between USAir Flight 1493 and SkyWest Flight 5569 at Los Angeles International Airport results in the deaths of 34 people, and injuries to 30 others.", "html": "1991 - A runway <a href=\"https://wikipedia.org/wiki/Los_Angeles_runway_disaster\" class=\"mw-redirect\" title=\"Los Angeles runway disaster\">collision</a> between USAir Flight 1493 and SkyWest Flight 5569 at <a href=\"https://wikipedia.org/wiki/Los_Angeles_International_Airport\" title=\"Los Angeles International Airport\">Los Angeles International Airport</a> results in the deaths of 34 people, and injuries to 30 others.", "no_year_html": "A runway <a href=\"https://wikipedia.org/wiki/Los_Angeles_runway_disaster\" class=\"mw-redirect\" title=\"Los Angeles runway disaster\">collision</a> between USAir Flight 1493 and SkyWest Flight 5569 at <a href=\"https://wikipedia.org/wiki/Los_Angeles_International_Airport\" title=\"Los Angeles International Airport\">Los Angeles International Airport</a> results in the deaths of 34 people, and injuries to 30 others.", "links": [{"title": "Los Angeles runway disaster", "link": "https://wikipedia.org/wiki/Los_Angeles_runway_disaster"}, {"title": "Los Angeles International Airport", "link": "https://wikipedia.org/wiki/Los_Angeles_International_Airport"}]}, {"year": "1991", "text": "A magnitude 6.8 earthquake strikes the Hindu Kush region, killing at least 848 people in Afghanistan, Pakistan and present-day Tajikistan.", "html": "1991 - A <a href=\"https://wikipedia.org/wiki/1991_Hindu_Kush_earthquake\" title=\"1991 Hindu Kush earthquake\">magnitude 6.8 earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Hindu_Kush\" title=\"Hindu Kush\">Hindu Kush</a> region, killing at least 848 people in Afghanistan, Pakistan and present-day Tajikistan.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1991_Hindu_Kush_earthquake\" title=\"1991 Hindu Kush earthquake\">magnitude 6.8 earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Hindu_Kush\" title=\"Hindu Kush\">Hindu Kush</a> region, killing at least 848 people in Afghanistan, Pakistan and present-day Tajikistan.", "links": [{"title": "1991 Hindu Kush earthquake", "link": "https://wikipedia.org/wiki/1991_Hindu_Kush_earthquake"}, {"title": "Hindu Kush", "link": "https://wikipedia.org/wiki/Hindu_<PERSON><PERSON>"}]}, {"year": "1992", "text": "The Chief Judicial Magistrate of Bhopal court declares <PERSON>, ex-CEO of Union Carbide, a fugitive under Indian law for failing to appear in the Bhopal disaster case.", "html": "1992 - The Chief Judicial Magistrate of <a href=\"https://wikipedia.org/wiki/Bhopal\" title=\"Bhopal\">Bhopal</a> court declares <a href=\"https://wikipedia.org/wiki/<PERSON>(American_businessman)\" title=\"<PERSON> (American businessman)\"><PERSON></a>, ex-<a href=\"https://wikipedia.org/wiki/Chief_executive_officer\" title=\"Chief executive officer\">CEO</a> of <a href=\"https://wikipedia.org/wiki/Union_Carbide\" title=\"Union Carbide\">Union Carbide</a>, a fugitive under Indian law for failing to appear in the <a href=\"https://wikipedia.org/wiki/Bhopal_disaster\" title=\"Bhopal disaster\">Bhopal disaster</a> case.", "no_year_html": "The Chief Judicial Magistrate of <a href=\"https://wikipedia.org/wiki/Bhopal\" title=\"Bhopal\">Bhopal</a> court declares <a href=\"https://wikipedia.org/wiki/<PERSON>(American_businessman)\" title=\"<PERSON> (American businessman)\"><PERSON></a>, ex-<a href=\"https://wikipedia.org/wiki/Chief_executive_officer\" title=\"Chief executive officer\">CEO</a> of <a href=\"https://wikipedia.org/wiki/Union_Carbide\" title=\"Union Carbide\">Union Carbide</a>, a fugitive under Indian law for failing to appear in the <a href=\"https://wikipedia.org/wiki/Bhopal_disaster\" title=\"Bhopal disaster\">Bhopal disaster</a> case.", "links": [{"title": "Bhopal", "link": "https://wikipedia.org/wiki/Bhopal"}, {"title": "<PERSON> (American businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_businessman)"}, {"title": "Chief executive officer", "link": "https://wikipedia.org/wiki/Chief_executive_officer"}, {"title": "Union Carbide", "link": "https://wikipedia.org/wiki/Union_Carbide"}, {"title": "Bhopal disaster", "link": "https://wikipedia.org/wiki/Bhopal_disaster"}]}, {"year": "1996", "text": "The Communications Decency Act is passed by the U.S. Congress.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Communications_Decency_Act\" title=\"Communications Decency Act\">Communications Decency Act</a> is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Communications_Decency_Act\" title=\"Communications Decency Act\">Communications Decency Act</a> is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "links": [{"title": "Communications Decency Act", "link": "https://wikipedia.org/wiki/Communications_Decency_Act"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1998", "text": "Rear Admiral <PERSON> becomes the first female African American to be promoted to rear admiral.", "html": "1998 - Rear Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female African American to be promoted to rear admiral.", "no_year_html": "Rear Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female African American to be promoted to rear admiral.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American journalist and South Asia Bureau Chief of The Wall Street Journal, kidnapped on January 23, is beheaded and mutilated by his captors.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and South Asia Bureau Chief of <i><a href=\"https://wikipedia.org/wiki/The_Wall_Street_Journal\" title=\"The Wall Street Journal\">The Wall Street Journal</a></i>, kidnapped on January 23, is beheaded and mutilated by his captors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and South Asia Bureau Chief of <i><a href=\"https://wikipedia.org/wiki/The_Wall_Street_Journal\" title=\"The Wall Street Journal\">The Wall Street Journal</a></i>, kidnapped on January 23, is beheaded and mutilated by his captors.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Wall Street Journal", "link": "https://wikipedia.org/wiki/The_Wall_Street_Journal"}]}, {"year": "2003", "text": "Space Shuttle Columbia disintegrated during the reentry of mission STS-107 into the Earth's atmosphere, killing all seven astronauts aboard.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster\" title=\"Space Shuttle Columbia disaster\">disintegrated during the reentry</a> of mission <a href=\"https://wikipedia.org/wiki/STS-107\" title=\"STS-107\">STS-107</a> into the Earth's atmosphere, killing all seven astronauts aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster\" title=\"Space Shuttle Columbia disaster\">disintegrated during the reentry</a> of mission <a href=\"https://wikipedia.org/wiki/STS-107\" title=\"STS-107\">STS-107</a> into the Earth's atmosphere, killing all seven astronauts aboard.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "Space Shuttle Columbia disaster", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster"}, {"title": "STS-107", "link": "https://wikipedia.org/wiki/STS-107"}]}, {"year": "2004", "text": "Hajj pilgrimage stampede: In a stampede at the Hajj pilgrimage in Saudi Arabia, 251 people are trampled to death and 244 injured.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Incidents_during_the_Hajj#Failures_in_crowd_control\" title=\"Incidents during the Hajj\">Hajj pilgrimage stampede</a>: In a stampede at the <a href=\"https://wikipedia.org/wiki/Hajj\" title=\"Hajj\">Hajj</a> pilgrimage in <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, 251 people are trampled to death and 244 injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Incidents_during_the_Hajj#Failures_in_crowd_control\" title=\"Incidents during the Hajj\">Hajj pilgrimage stampede</a>: In a stampede at the <a href=\"https://wikipedia.org/wiki/Hajj\" title=\"Hajj\">Hajj</a> pilgrimage in <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, 251 people are trampled to death and 244 injured.", "links": [{"title": "Incidents during the Hajj", "link": "https://wikipedia.org/wiki/Incidents_during_the_Hajj#Failures_in_crowd_control"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hajj"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}, {"year": "2004", "text": "Double suicide attack in Erbil on the offices of Iraqi Kurdish political parties by members of Jama'at al-Tawhid wal-Jihad", "html": "2004 - <a href=\"https://wikipedia.org/wiki/2004_Erbil_bombings\" title=\"2004 Erbil bombings\">Double suicide attack in Erbil</a> on the offices of <a href=\"https://wikipedia.org/wiki/Iraqi_Kurdistan\" title=\"Iraqi Kurdistan\">Iraqi Kurdish</a> political parties by members of <a href=\"https://wikipedia.org/wiki/Jama%27at_al-Tawhid_wal-Jihad\" title=\"Jama'at al-Tawhid wal-Jihad\">Jama'at al-Tawhid wal-Jihad</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2004_Erbil_bombings\" title=\"2004 Erbil bombings\">Double suicide attack in Erbil</a> on the offices of <a href=\"https://wikipedia.org/wiki/Iraqi_Kurdistan\" title=\"Iraqi Kurdistan\">Iraqi Kurdish</a> political parties by members of <a href=\"https://wikipedia.org/wiki/Jama%27at_al-Tawhid_wal-Jihad\" title=\"Jama'at al-Tawhid wal-Jihad\">Jam<PERSON>'at al-Tawhid wal-Jihad</a>", "links": [{"title": "2004 Erbil bombings", "link": "https://wikipedia.org/wiki/2004_Erbil_bombings"}, {"title": "Iraqi Kurdistan", "link": "https://wikipedia.org/wiki/Iraqi_Kurdistan"}, {"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wal-Jihad", "link": "https://wikipedia.org/wiki/Jama%27at_al-Tawhi<PERSON>_wal-<PERSON>had"}]}, {"year": "2005", "text": "King <PERSON><PERSON><PERSON> of Nepal carries out a coup d'état to capture the democracy, becoming Chairman of the Councils of ministers.", "html": "2005 - King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> carries out a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> to capture the democracy, becoming Chairman of the Councils of ministers.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> carries out a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> to capture the democracy, becoming Chairman of the Councils of ministers.", "links": [{"title": "<PERSON><PERSON><PERSON> of Nepal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>endra_of_Nepal"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}]}, {"year": "2007", "text": "The National Weather Service in the United States switches from the Fujita scale to the new Enhanced Fujita scale to measure the intensity and strength of tornadoes.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/National_Weather_Service\" title=\"National Weather Service\">National Weather Service</a> in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> switches from the <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">Fujita scale</a> to the new <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_scale\" title=\"Enhanced Fujita scale\">Enhanced Fujita scale</a> to measure the intensity and strength of tornadoes.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Weather_Service\" title=\"National Weather Service\">National Weather Service</a> in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> switches from the <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">Fujita scale</a> to the new <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_scale\" title=\"Enhanced Fujita scale\">Enhanced Fujita scale</a> to measure the intensity and strength of tornadoes.", "links": [{"title": "National Weather Service", "link": "https://wikipedia.org/wiki/National_Weather_Service"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Fujita scale", "link": "https://wikipedia.org/wiki/Fujita_scale"}, {"title": "Enhanced Fujita scale", "link": "https://wikipedia.org/wiki/Enhanced_Fujita_scale"}]}, {"year": "2009", "text": "The first cabinet of <PERSON><PERSON><PERSON> was formed in Iceland, making her the country's first female prime minister and the world's first openly gay head of government.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/First_cabinet_of_J%C3%B3<PERSON>_Sigur%C3%B0ard%C3%B3ttir\" title=\"First cabinet of <PERSON><PERSON><PERSON>\">first cabinet of <PERSON><PERSON><PERSON></a> was formed in <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>, making <a href=\"https://wikipedia.org/wiki/J%C3%B3hanna_Sigur%C3%B0ard%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON>\">her</a> the country's first female prime minister and the world's first openly <a href=\"https://wikipedia.org/wiki/LGBT\" class=\"mw-redirect\" title=\"LGBT\">gay</a> head of government.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_cabinet_of_J%C3%B3<PERSON>_Sigur%C3%B0ard%C3%B3ttir\" title=\"First cabinet of <PERSON><PERSON><PERSON>\">first cabinet of <PERSON><PERSON><PERSON></a> was formed in <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>, making <a href=\"https://wikipedia.org/wiki/J%C3%B3hanna_Sigur%C3%B0ard%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON>\">her</a> the country's first female prime minister and the world's first openly <a href=\"https://wikipedia.org/wiki/LGBT\" class=\"mw-redirect\" title=\"LGBT\">gay</a> head of government.", "links": [{"title": "First cabinet of <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/First_cabinet_of_J%C3%B3hanna_Sigur%C3%B0ard%C3%B3ttir"}, {"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3hanna_Sigur%C3%B0ard%C3%B3ttir"}, {"title": "LGBT", "link": "https://wikipedia.org/wiki/LGBT"}]}, {"year": "2012", "text": "Seventy-four people are killed and over 500 injured as a result of clashes between fans of Egyptian football teams Al Masry and Al Ahly in the city of Port Said.", "html": "2012 - Seventy-four people are killed and over 500 injured as a result of <a href=\"https://wikipedia.org/wiki/Port_Said_Stadium_riot\" title=\"Port Said Stadium riot\">clashes</a> between fans of Egyptian football teams <a href=\"https://wikipedia.org/wiki/Al_Masry_SC\" title=\"Al Masry SC\">Al Masry</a> and <a href=\"https://wikipedia.org/wiki/Al_Ahly_SC\" title=\"Al Ahly SC\"><PERSON></a> in the city of <a href=\"https://wikipedia.org/wiki/Port_Said\" title=\"Port Said\">Port Said</a>.", "no_year_html": "Seventy-four people are killed and over 500 injured as a result of <a href=\"https://wikipedia.org/wiki/Port_Said_Stadium_riot\" title=\"Port Said Stadium riot\">clashes</a> between fans of Egyptian football teams <a href=\"https://wikipedia.org/wiki/Al_Masry_SC\" title=\"Al Masry SC\">Al Masry</a> and <a href=\"https://wikipedia.org/wiki/Al_Ahly_SC\" title=\"Al Ahly SC\"><PERSON>ly</a> in the city of <a href=\"https://wikipedia.org/wiki/Port_Said\" title=\"Port Said\">Port Said</a>.", "links": [{"title": "Port Said Stadium riot", "link": "https://wikipedia.org/wiki/Port_Said_Stadium_riot"}, {"title": "Al Masry SC", "link": "https://wikipedia.org/wiki/Al_Masry_SC"}, {"title": "Al Ahly SC", "link": "https://wikipedia.org/wiki/Al_<PERSON>ly_SC"}, {"title": "Port Said", "link": "https://wikipedia.org/wiki/Port_Said"}]}, {"year": "2013", "text": "The Shard, the sixth-tallest building in Europe, opens its viewing gallery to the public.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/The_Shard\" title=\"The Shard\">The Shard</a>, the <a href=\"https://wikipedia.org/wiki/List_of_tallest_buildings_in_Europe\" title=\"List of tallest buildings in Europe\">sixth-tallest building in Europe</a>, opens its viewing gallery to the public.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Shard\" title=\"The Shard\">The Shard</a>, the <a href=\"https://wikipedia.org/wiki/List_of_tallest_buildings_in_Europe\" title=\"List of tallest buildings in Europe\">sixth-tallest building in Europe</a>, opens its viewing gallery to the public.", "links": [{"title": "The Shard", "link": "https://wikipedia.org/wiki/The_Shard"}, {"title": "List of tallest buildings in Europe", "link": "https://wikipedia.org/wiki/List_of_tallest_buildings_in_Europe"}]}, {"year": "2021", "text": "A coup d'état in Myanmar removes <PERSON><PERSON> from power and restores military rule.", "html": "2021 - A <a href=\"https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat\" title=\"2021 Myanmar coup d'état\">coup d'état</a> in <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a> removes <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> from power and restores military rule.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat\" title=\"2021 Myanmar coup d'état\">coup d'état</a> in <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a> removes <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> from power and restores military rule.", "links": [{"title": "2021 Myanmar coup d'état", "link": "https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "Aung San Suu Kyi", "link": "https://wikipedia.org/wiki/Aung_San_Suu_K<PERSON>"}]}, {"year": "2022", "text": "Five-year-old Moroccan boy <PERSON><PERSON> falls into a 32-meter (105 feet) deep well in Ighran village in Tamorot commune, Chefchaouen Province, Morocco, but dies four days later, before rescue workers reached him.", "html": "2022 - Five-year-old Moroccan boy <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>\" title=\"Death of <PERSON><PERSON>\"><PERSON><PERSON></a> falls into a 32-meter (105 feet) deep well in Ighran village in <a href=\"https://wikipedia.org/wiki/Tamorot\" title=\"Tamorot\">Tamorot</a> commune, <a href=\"https://wikipedia.org/wiki/Chefchaouen_Province\" title=\"Chefchaouen Province\">Chefchaouen Province</a>, Morocco, but dies four days later, before rescue workers reached him.", "no_year_html": "Five-year-old Moroccan boy <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>\" title=\"Death of <PERSON><PERSON>\"><PERSON><PERSON></a> falls into a 32-meter (105 feet) deep well in Ighran village in <a href=\"https://wikipedia.org/wiki/Tamorot\" title=\"Tamorot\">Tamorot</a> commune, <a href=\"https://wikipedia.org/wiki/Chefchaouen_Province\" title=\"Chefchaouen Province\">Chefchaouen Province</a>, Morocco, but dies four days later, before rescue workers reached him.", "links": [{"title": "Death of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>"}, {"title": "Tamorot", "link": "https://wikipedia.org/wiki/Tamorot"}, {"title": "Chefchaouen Province", "link": "https://wikipedia.org/wiki/Chefchaouen_Province"}]}], "Births": [{"year": "1261", "text": "<PERSON>, English bishop and politician, Lord High Treasurer (d. 1326)", "html": "1261 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (d. 1326)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (d. 1326)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1435", "text": "<PERSON><PERSON><PERSON>, Duke of Savoy (d. 1472)", "html": "1435 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON>, Duke of Savoy</a> (d. 1472)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON>, Duke of Savoy</a> (d. 1472)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1447", "text": "<PERSON><PERSON><PERSON>, Duke of Württemberg (d. 1504)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON><PERSON>, Duke of Württemberg\"><PERSON><PERSON><PERSON>, Duke of Württemberg</a> (d. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON><PERSON>, Duke of Württemberg\"><PERSON><PERSON><PERSON>, Duke of Württemberg</a> (d. 1504)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg"}]}, {"year": "1459", "text": "<PERSON>, German poet and scholar (d. 1508)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and scholar (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and scholar (d. 1508)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1462", "text": "<PERSON>, German lexicographer, historian, and cryptographer (d. 1516)", "html": "1462 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lexicographer, historian, and cryptographer (d. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lexicographer, historian, and cryptographer (d. 1516)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1552", "text": "<PERSON>, English lawyer, judge, and politician, Attorney General for England and Wales (d. 1634)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (d. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1561", "text": "<PERSON>, British mathematician (d. 1630)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, British mathematician (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, British mathematician (d. 1630)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_(mathematician)"}]}, {"year": "1635", "text": "<PERSON><PERSON><PERSON>, German archaeologist and scholar (d. 1689)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German archaeologist and scholar (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German archaeologist and scholar (d. 1689)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON><PERSON><PERSON>, English poet and playwright (d. 1724)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/Elkanah_Settle\" title=\"Elkan<PERSON> Settle\"><PERSON><PERSON><PERSON></a>, English poet and playwright (d. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El<PERSON><PERSON>_Settle\" title=\"El<PERSON><PERSON> Settle\"><PERSON><PERSON><PERSON></a>, English poet and playwright (d. 1724)", "links": [{"title": "Elkanah Settle", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tle"}]}, {"year": "1659", "text": "<PERSON>, Dutch explorer (d. 1729)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch explorer (d. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch explorer (d. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1663", "text": "<PERSON><PERSON><PERSON> Espíritu <PERSON>, Filipino nun, founded the Religious of the Virgin Mary (d. 1748)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/Ignacia_del_Esp%C3%ADritu_Santo\" title=\"Ignacia del Espíritu Santo\"><PERSON><PERSON><PERSON> del Espíritu Santo</a>, Filipino nun, founded the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary\" title=\"Religious of the Virgin Mary\">Religious of the Virgin Mary</a> (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignacia_del_Esp%C3%ADritu_Santo\" title=\"Ignacia del Espíritu Santo\"><PERSON><PERSON><PERSON> del Espíritu Santo</a>, Filipino nun, founded the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary\" title=\"Religious of the Virgin Mary\">Religious of the Virgin Mary</a> (d. 1748)", "links": [{"title": "Ignacia del Espíritu Santo", "link": "https://wikipedia.org/wiki/Ignacia_del_Esp%C3%ADritu_Santo"}, {"title": "Religious of the Virgin Mary", "link": "https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary"}]}, {"year": "1666", "text": "<PERSON>, Princess of Conti and titular queen of Poland (d. 1732)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/Marie_Th%C3%A9r%C3%A8<PERSON>_de_<PERSON>\" title=\"<PERSON> Thérèse <PERSON>\"><PERSON></a>, Princess of Conti and titular queen of Poland (d. <a href=\"https://wikipedia.org/wiki/1732\" title=\"1732\">1732</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marie_Th%C3%A9r%C3%A8<PERSON>_de_<PERSON>\" title=\"<PERSON> Thérèse de <PERSON>\"><PERSON>é<PERSON></a>, Princess of Conti and titular queen of Poland (d. <a href=\"https://wikipedia.org/wiki/1732\" title=\"1732\">1732</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marie_Th%C3%A9r%C3%A8se_de_<PERSON>"}, {"title": "1732", "link": "https://wikipedia.org/wiki/1732"}]}, {"year": "1687", "text": "<PERSON>, German violinist and composer (d. 1733)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (d. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (d. 1733)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON>, Italian violinist and composer (d. 1768)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, Swedish-German pianist and composer (d. 1765)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-German pianist and composer (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-German pianist and composer (d. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON><PERSON>, South African-French mycologist and academic (d. 1836)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> He<PERSON>\"><PERSON><PERSON></a>, South African-French mycologist and academic (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> He<PERSON>\"><PERSON><PERSON></a>, South African-French mycologist and academic (d. 1836)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, Irish minister and theologian (d. 1854)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, Irish minister and theologian (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, Irish minister and theologian (d. 1854)", "links": [{"title": "<PERSON> (minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)"}]}, {"year": "1796", "text": "<PERSON>, Swiss minister, poet, and educator (d. 1865)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6hlich\" title=\"<PERSON>\"><PERSON></a>, Swiss minister, poet, and educator (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6hlich\" title=\"<PERSON>\"><PERSON></a>, Swiss minister, poet, and educator (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6hlich"}]}, {"year": "1801", "text": "<PERSON><PERSON>, French lexicographer and philosopher (d. 1881)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Littr%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lexicographer and philosopher (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Littr%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lexicographer and philosopher (d. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Littr%C3%A9"}]}, {"year": "1820", "text": "<PERSON>, American clergyman and theologian (d. 1897)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clergyman and theologian (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clergyman and theologian (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, Danish organist and composer (d. 1898)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish organist and composer (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish organist and composer (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON>, American psychologist and academic (d. 1924)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"G. <PERSON>\"><PERSON><PERSON></a>, American psychologist and academic (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"G. <PERSON>\"><PERSON><PERSON></a>, American psychologist and academic (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, American lawyer and diplomat (d. 1908)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Durham_Stevens\" title=\"<PERSON> Stevens\"><PERSON></a>, American lawyer and diplomat (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Durham_Stevens\" title=\"<PERSON> Stevens\"><PERSON></a>, American lawyer and diplomat (d. 1908)", "links": [{"title": "Durham Stevens", "link": "https://wikipedia.org/wiki/Durham_Stevens"}]}, {"year": "1858", "text": "<PERSON>, Mexican diplomat (d. 1942)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican diplomat (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican diplomat (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, Irish-American cellist, composer, and conductor (d. 1924)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American cellist, composer, and conductor (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American cellist, composer, and conductor (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, Swedish nurse and healthcare activist (d. 1924)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish nurse and healthcare activist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish nurse and healthcare activist (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ag<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Romanian painter and illustrator (d. 1917)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/%C8%98te<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian painter and illustrator (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C8%98<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian painter and illustrator (d. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C8%98te<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Finnish physician (d. 1949)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish physician (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish physician (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, English opera singer (d. 1936)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English opera singer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English opera singer (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, American lawyer and politician (d. 1949)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Irish soldier, Victoria Cross recipient (d. 1901)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(VC_recipient)\" class=\"mw-redirect\" title=\"<PERSON> (VC recipient)\"><PERSON></a>, Irish soldier, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(VC_recipient)\" class=\"mw-redirect\" title=\"<PERSON> (VC recipient)\"><PERSON></a>, Irish soldier, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1901)", "links": [{"title": "<PERSON> (VC recipient)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(VC_recipient)"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1874", "text": "<PERSON>, Austrian author, poet, and playwright (d. 1929)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author, poet, and playwright (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author, poet, and playwright (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian swimmer and architect, designed the Grand Hotel Aranybika (d. 1955)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Alfr%C3%A9d_Haj%C3%B3s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian swimmer and architect, designed the <a href=\"https://wikipedia.org/wiki/Grand_Hotel_Aranybika\" title=\"Grand Hotel Aranybika\">Grand Hotel Aranybika</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfr%C3%A9d_Haj%C3%B3s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian swimmer and architect, designed the <a href=\"https://wikipedia.org/wiki/Grand_Hotel_Aranybika\" title=\"Grand Hotel Aranybika\">Grand Hotel Aranybika</a> (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alfr%C3%A9d_Haj%C3%B3s"}, {"title": "Grand Hotel Aranybika", "link": "https://wikipedia.org/wiki/Grand_Hotel_Aranybika"}]}, {"year": "1878", "text": "<PERSON>, Slovak journalist and politician, 10th Prime Minister of Czechoslovakia (d. 1944)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Milan_Hod%C5%BEa\" title=\"Milan Hodža\"><PERSON></a>, Slovak journalist and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Czechoslovakia\" class=\"mw-redirect\" title=\"Prime Minister of Czechoslovakia\">Prime Minister of Czechoslovakia</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Hod%C5%BEa\" title=\"Milan Hodža\"><PERSON></a>, Slovak journalist and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Czechoslovakia\" class=\"mw-redirect\" title=\"Prime Minister of Czechoslovakia\">Prime Minister of Czechoslovakia</a> (d. 1944)", "links": [{"title": "Milan Hodža", "link": "https://wikipedia.org/wiki/Milan_Hod%C5%BEa"}, {"title": "Prime Minister of Czechoslovakia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Czechoslovakia"}]}, {"year": "1881", "text": "<PERSON><PERSON>, South African cricketer (d. 1966)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Tip_Snooke\" title=\"Tip Snooke\"><PERSON><PERSON></a>, South African cricketer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tip_Snooke\" title=\"Tip Snooke\"><PERSON><PERSON></a>, South African cricketer (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tip_Snooke"}]}, {"year": "1882", "text": "<PERSON>, Bulgarian artist (d. 1960)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian artist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian artist (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Canadian lawyer and politician, 12th Prime Minister of Canada (d. 1973)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Louis_St._Laurent\" title=\"Louis St. Laurent\"><PERSON> St. Laurent</a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_St._Laurent\" title=\"Louis St. Laurent\"><PERSON> St. Laurent</a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1973)", "links": [{"title": "Louis St. Laurent", "link": "https://wikipedia.org/wiki/Louis_St._Laurent"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1884", "text": "<PERSON><PERSON>, American football player and physician (d. 1949)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Robinson\"><PERSON><PERSON></a>, American football player and physician (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Robinson\" title=\"<PERSON><PERSON> Robinson\"><PERSON><PERSON></a>, American football player and physician (d. 1949)", "links": [{"title": "Bradbury Robinson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Robinson"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Russian journalist and author (d. 1937)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist and author (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist and author (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, English-American lieutenant, pilot, and author (d. 1947)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lieutenant, pilot, and author (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lieutenant, pilot, and author (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Estonian general and politician, 11th Estonian Minister of War (d. 1942)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian general and politician, 11th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian general and politician, 11th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Estonian Minister of War", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_War"}]}, {"year": "1894", "text": "<PERSON>, American director and producer (d. 1973)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Ford\"><PERSON></a>, American director and producer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American pianist and composer (d. 1955)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Canadian businessman (d. 1980)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Conn_<PERSON><PERSON><PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English journalist and author (d. 1985)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, American pediatrician and author (d. 2012)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Leila_Denmark\" title=\"Leila Denmark\"><PERSON><PERSON></a>, American pediatrician and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leila_Denmark\" title=\"Leila Denmark\"><PERSON><PERSON></a>, American pediatrician and author (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leila_Denmark"}]}, {"year": "1901", "text": "<PERSON>, American soldier (d. 2011)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American actor (d. 1960)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, American actor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gable\"><PERSON></a>, American actor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>able"}]}, {"year": "1902", "text": "<PERSON><PERSON>, German concentration camp guard (d. 1947)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"There<PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camp\" class=\"mw-redirect\" title=\"Nazi concentration camp\">concentration camp</a> guard (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"There<PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camp\" class=\"mw-redirect\" title=\"Nazi concentration camp\">concentration camp</a> guard (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}, {"title": "Nazi concentration camp", "link": "https://wikipedia.org/wiki/Nazi_concentration_camp"}]}, {"year": "1902", "text": "<PERSON><PERSON>, American poet, social activist, novelist, and playwright (d. 1967)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hughes\"><PERSON><PERSON></a>, American poet, social activist, novelist, and playwright (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, social activist, novelist, and playwright (d. 1967)", "links": [{"title": "<PERSON><PERSON> Hughes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON><PERSON>, American humorist and screenwriter (d. 1979)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/S<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American humorist and screenwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American humorist and screenwriter (d. 1979)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Italian-American physicist and academic, Nobel Prize laureate (d. 1989)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8\" title=\"<PERSON>\"><PERSON></a>, Italian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A8\" title=\"<PERSON>\"><PERSON></a>, Italian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emilio_Segr%C3%A8"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Nigerian lawyer and jurist, 2nd Chief Justice of Nigeria (d. 1993)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Ad<PERSON><PERSON><PERSON><PERSON>_Ademola\" title=\"Adetoku<PERSON><PERSON> Ademola\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Nigerian lawyer and jurist, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Nigeria\" title=\"Chief Justice of Nigeria\">Chief Justice of Nigeria</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ad<PERSON><PERSON><PERSON><PERSON>_Ademola\" title=\"Ad<PERSON>ku<PERSON><PERSON> Ademola\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Nigerian lawyer and jurist, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Nigeria\" title=\"Chief Justice of Nigeria\">Chief Justice of Nigeria</a> (d. 1993)", "links": [{"title": "Adetokunbo Ademola", "link": "https://wikipedia.org/wiki/Adetokunbo_Ademola"}, {"title": "Chief Justice of Nigeria", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Nigeria"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, German author and songwriter (d. 1972)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and songwriter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and songwriter (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BC<PERSON>_<PERSON>ich"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Brazilian pianist and composer (d. 1993)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Camar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian pianist and composer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Camar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian pianist and composer (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camargo_<PERSON>ieri"}]}, {"year": "1908", "text": "<PERSON>, Hungarian-American animator and producer (d. 1980)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American animator and producer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American animator and producer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Canadian economist and banker (d. 1998)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and banker (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and banker (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Canadian-American singer-songwriter (d. 2013)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Chinese general and politician (d. 2009)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>apoi_<PERSON>awang_Jigme\" title=\"Ngapoi Ngawang Jigme\"><PERSON><PERSON><PERSON></a>, Chinese general and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ap<PERSON>_<PERSON>awang_Jigme\" title=\"Ngapoi Ngawang Jigme\"><PERSON><PERSON><PERSON></a>, Chinese general and politician (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>awang_Jigme"}]}, {"year": "1915", "text": "<PERSON>, English footballer and manager (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Spanish economist and author (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish economist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish economist and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Japanese baseball player and soldier (d. 1944)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player and soldier (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player and soldier (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Scottish novelist (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rk\" title=\"<PERSON><PERSON> Spark\"><PERSON><PERSON></a>, Scottish novelist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rk\" title=\"<PERSON><PERSON> Spark\"><PERSON><PERSON></a>, Scottish novelist (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rk"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Polish archbishop (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Igna<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish archbishop (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Igna<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish archbishop (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Igna<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Chinese-French painter (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Chinese-French painter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Chinese-French painter (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Italian feminist partisan and politician (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian feminist partisan and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian feminist partisan and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English writer and WAAF officer (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer and WAAF officer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer and WAAF officer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English actor (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Italian soprano and actress (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Canadian businessman, co-founded the International Federation of BodyBuilding & Fitness (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded the <a href=\"https://wikipedia.org/wiki/International_Federation_of_BodyBuilding_%26_Fitness\" class=\"mw-redirect\" title=\"International Federation of BodyBuilding &amp; Fitness\">International Federation of BodyBuilding &amp; Fitness</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded the <a href=\"https://wikipedia.org/wiki/International_Federation_of_BodyBuilding_%26_Fitness\" class=\"mw-redirect\" title=\"International Federation of BodyBuilding &amp; Fitness\">International Federation of BodyBuilding &amp; Fitness</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "International Federation of BodyBuilding & Fitness", "link": "https://wikipedia.org/wiki/International_Federation_of_BodyBuilding_%26_Fitness"}]}, {"year": "1924", "text": "<PERSON>, German-Israeli footballer, coach, and manager (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli footballer, coach, and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli footballer, coach, and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American street photographer (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American street photographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American street photographer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American poet and academic (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Galway_<PERSON>ll\" title=\"Galway Kinnell\"><PERSON></a>, American poet and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Galway_Kinnell\" title=\"<PERSON> Kinnell\"><PERSON></a>, American poet and academic (d. 2014)", "links": [{"title": "<PERSON> Kinnell", "link": "https://wikipedia.org/wiki/Galway_<PERSON>ll"}]}, {"year": "1928", "text": "<PERSON>, Welsh physicist and academic (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, Welsh physicist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, Welsh physicist and academic (d. 2015)", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)"}]}, {"year": "1928", "text": "<PERSON>, Hungarian-American academic and politician (d. 2008)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American academic and politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American academic and politician (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Bangladeshi judge and politician, 12th President of Bangladesh (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi judge and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi judge and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}]}, {"year": "1930", "text": "<PERSON>, Bangladeshi general and politician, 10th President of Bangladesh (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi general and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi general and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}]}, {"year": "1931", "text": "<PERSON>, Russian politician, 1st President of Russia (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Russia", "link": "https://wikipedia.org/wiki/President_of_Russia"}]}, {"year": "1932", "text": "<PERSON>, British politician (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Sudanese activist and politician (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese activist and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese activist and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Turkish actor, playwright, and director (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>nce<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, playwright, and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, playwright, and director (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor and comedian", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American drummer and singer (d. 2008)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and singer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and singer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American golfer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cupit\" title=\"<PERSON><PERSON> Cupit\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>it\" title=\"<PERSON><PERSON> Cupit\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jacky_Cupit"}]}, {"year": "1938", "text": "<PERSON>, American actor and singer (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian physicist, author, and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Frit<PERSON><PERSON>_<PERSON>\" title=\"Fritjo<PERSON>ra\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian physicist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fritjo<PERSON>_<PERSON>\" title=\"Fritjo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian physicist, author, and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Frit<PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1939", "text": "<PERSON>, Egyptian-French singer-songwriter and dancer (d. 1978)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter and dancer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter and dancer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and politician (d. 2007)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Russian ballerina (d. 2009)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ballerina (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ballerina (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American pianist and composer (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Austrian-American actress (d. 1996)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Welsh actor, director, and screenwriter (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor, director, and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor, director, and screenwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, German footballer and manager (d. 2010)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer and manager (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian lawyer and politician, 50th Secretary of State for Canada", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and manager (d. 2013)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American cyclist (d. 1993)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian tennis player (d. 1977)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actress (d. 2011)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish computer programmer and politician, Minister of State for the Armed Forces", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Labour_politician)\" title=\"<PERSON> (Labour politician)\"><PERSON></a>, Scottish computer programmer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces\" title=\"Minister of State for the Armed Forces\">Minister of State for the Armed Forces</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Labour_politician)\" title=\"<PERSON> (Labour politician)\"><PERSON></a>, Scottish computer programmer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces\" title=\"Minister of State for the Armed Forces\">Minister of State for the Armed Forces</a>", "links": [{"title": "<PERSON> (Labour politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Labour_politician)"}, {"title": "Minister of State for the Armed Forces", "link": "https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Australian singer-songwriter and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American journalist (d. 1983)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and producer (d. 2004)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Australian actor (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lex_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1950", "text": "<PERSON>, Turkish politician, 4th Turkish Minister of European Union Affairs", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_of_European_Union_Affairs_(Turkey)\" class=\"mw-redirect\" title=\"Minister of European Union Affairs (Turkey)\">Turkish Minister of European Union Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_of_European_Union_Affairs_(Turkey)\" class=\"mw-redirect\" title=\"Minister of European Union Affairs (Turkey)\">Turkish Minister of European Union Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of European Union Affairs (Turkey)", "link": "https://wikipedia.org/wiki/Minister_of_European_Union_Affairs_(Turkey)"}]}, {"year": "1950", "text": "<PERSON>, American guitarist and songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American guitarist and songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Nigerian general (d. 2012)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian general (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian general (d. 2012)", "links": [{"title": "Owoye <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor, writer, and musician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, writer, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, writer, and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Exene_Cervenka\" title=\"Exene Cervenka\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Exene_Cervenka\" title=\"Exene Cervenka\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Exene_Cervenka"}]}, {"year": "1957", "text": "<PERSON>, American author and illustrator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Saudi Arabian businessman (d. 2007)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian businessman (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Jamaican-English footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish lawyer and politician, Shadow Secretary of State for Scotland", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland\" title=\"Shadow Secretary of State for Scotland\">Shadow Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland\" title=\"Shadow Secretary of State for Scotland\">Shadow Secretary of State for Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland"}]}, {"year": "1959", "text": "<PERSON>, American football player and coach (d. 2019)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2019)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, German field hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ied\" title=\"<PERSON>ker Fried\"><PERSON><PERSON></a>, German field hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ied\" title=\"<PERSON><PERSON> Fried\"><PERSON><PERSON></a>, German field hockey player and coach", "links": [{"title": "Volker Fried", "link": "https://wikipedia.org/wiki/Volker_Fried"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician (d. 2018)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American engineer and astronaut", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Argentinian footballer (d. 2004)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ei\" title=\"<PERSON><PERSON><PERSON> Hotei\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ei\" title=\"<PERSON><PERSON><PERSON> Hotei\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>su_Hotei"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Japanese painter and sculptor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter and sculptor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2011)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Israeli football player, and club chairman", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli football player, and club chairman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli football player, and club chairman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, English actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1965", "text": "<PERSON>, American actor and martial artist (d. 1993)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON> of Monaco", "html": "1965 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phanie_of_Monaco\" class=\"mw-redirect\" title=\"Stéphanie of Monaco\">Stéphanie of Monaco</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9phanie_of_Monaco\" class=\"mw-redirect\" title=\"Stéphanie of Monaco\">Stéphanie of Monaco</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Monaco", "link": "https://wikipedia.org/wiki/St%C3%A9phanie_of_Monaco"}]}, {"year": "1966", "text": "<PERSON>, American soccer player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American author and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and actress (d. 2023)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American actor and comedian", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Argentinian footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American journalist, author, and publisher (d. 2012)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and publisher (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and publisher (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American musician and composer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Jamaican cricketer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rose\"><PERSON><PERSON></a>, Jamaican cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rose\"><PERSON><PERSON></a>, Jamaican cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Rose"}]}, {"year": "1969", "text": "<PERSON>, American musician and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_(drummer)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Japanese racing driver", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and actor (d. 2000)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Michael <PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michael_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Swedish ice hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Liberian peace activist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Liberian peace activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Liberian peace activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, German footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_P%C3%<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_P%C3%A<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_P%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American rapper", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Big_Boi\" title=\"Big Boi\"><PERSON> <PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Boi\" title=\"Big Boi\"><PERSON> <PERSON></a>, American rapper", "links": [{"title": "Big Boi", "link": "https://wikipedia.org/wiki/Big_Boi"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Vlas%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Vlas%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Vlas%C3%A1k"}]}, {"year": "1976", "text": "<PERSON>, American poker player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player (d. 2011)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian singer and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer and actor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Mexican singer-songwriter (d. 2006)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Valent%C3%ADn_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valent%C3%ADn_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican singer-songwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valent%C3%ADn_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Brazilian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Luna"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Hong Kong singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentinian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>im%C3%A<PERSON><PERSON><PERSON>_(footballer,_born_1981)\" title=\"<PERSON> (footballer, born 1981)\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A<PERSON><PERSON><PERSON>_(footballer,_born_1981)\" title=\"<PERSON> (footballer, born 1981)\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON> (footballer, born 1981)", "link": "https://wikipedia.org/wiki/Christian_Gim%C3%A9<PERSON><PERSON>_(footballer,_born_1981)"}]}, {"year": "1981", "text": "<PERSON>, South African cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Welsh rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1983)\" title=\"<PERSON> (basketball, born 1983)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1983)\" title=\"<PERSON> (basketball, born 1983)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1983)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1983)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Belgian cyclist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter and musician", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Scottish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Irish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Dutch speed skater", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch speed skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American fashion designer and author", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Ladislav_%C5%A0m%C3%ADd\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ladislav_%C5%A0m%C3%ADd\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ladislav_%C5%A0m%C3%ADd"}]}, {"year": "1987", "text": "<PERSON>, Polish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Portuguese-Australian cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese-Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese-Australian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ises_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1987", "text": "<PERSON>, American actress, singer, and dancer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American mixed martial artist, wrestler and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ron<PERSON>_Rousey\" title=\"Ronda Rousey\"><PERSON><PERSON></a>, American mixed martial artist, wrestler and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ron<PERSON>_Rousey\" title=\"Ronda Rousey\"><PERSON><PERSON></a>, American mixed martial artist, wrestler and actress", "links": [{"title": "<PERSON><PERSON>y", "link": "https://wikipedia.org/wiki/Ronda_Rousey"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1989", "text": "<PERSON>, Portuguese footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American-Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Blake <PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Blake <PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Blake_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Italian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, German tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English singer-songwriter and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Styles\"><PERSON></a>, English singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Jordanian taekwondo athlete", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jordanian taekwondo athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jordanian taekwondo athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, South Korean singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>ung", "link": "https://wikipedia.org/wiki/<PERSON>young"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, South Korean singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>hy<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hy<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hyo"}]}, {"year": "1999", "text": "<PERSON>, Egyptian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Talan<PERSON>_<PERSON>\" title=\"Talan<PERSON> Hufanga\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Talan<PERSON>_<PERSON>\" title=\"Talan<PERSON> Hu<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Talan<PERSON>_<PERSON>ga"}]}], "Deaths": [{"year": "583", "text": "<PERSON><PERSON>, ruler of Palenque (b. 524)", "html": "583 - <a href=\"https://wikipedia.org/wiki/Kan_<PERSON><PERSON>_<PERSON>\" title=\"Kan Bahl<PERSON> I\"><PERSON>n <PERSON><PERSON> I</a>, ruler of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Pa<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan_<PERSON><PERSON>_<PERSON>\" title=\"Kan <PERSON><PERSON> I\"><PERSON>n <PERSON><PERSON> I</a>, ruler of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palen<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 524)", "links": [{"title": "Kan <PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palenque"}]}, {"year": "772", "text": "<PERSON> (b. 720)", "html": "772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON> III</a> (b. 720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> III</a> (b. 720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "850", "text": "<PERSON><PERSON>, king of Asturias", "html": "850 - <a href=\"https://wikipedia.org/wiki/Ramiro_I_of_Asturias\" title=\"<PERSON><PERSON> I of Asturias\"><PERSON><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Asturias\" title=\"Kingdom of Asturias\">Asturias</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ramiro_I_of_Asturias\" title=\"<PERSON><PERSON> I of Asturias\"><PERSON><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Asturias\" title=\"Kingdom of Asturias\">Asturias</a>", "links": [{"title": "Ramiro I of Asturias", "link": "https://wikipedia.org/wiki/Ramiro_I_of_Asturias"}, {"title": "Kingdom of Asturias", "link": "https://wikipedia.org/wiki/Kingdom_of_Asturias"}]}, {"year": "1222", "text": "<PERSON><PERSON>, first Emperor of Trebizond", "html": "1222 - <a href=\"https://wikipedia.org/wiki/Alexios_I_of_Trebizond\" title=\"Alexios I of Trebizond\">Alexios Megas Komnenos</a>, first <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Emperor of Trebizond</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alexios_I_of_Trebizond\" title=\"Alexios I of Trebizond\">Alexios Megas Komnenos</a>, first <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Emperor of Trebizond</a>", "links": [{"title": "<PERSON><PERSON> I of Trebizond", "link": "https://wikipedia.org/wiki/Alexios_I_of_Trebizond"}, {"title": "Empire of Trebizond", "link": "https://wikipedia.org/wiki/Empire_of_Trebizond"}]}, {"year": "1248", "text": "<PERSON>, Duke of Brabant (b. 1207)", "html": "1248 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (b. 1207)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (b. 1207)", "links": [{"title": "<PERSON>, Duke of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1328", "text": "<PERSON> of France (b. 1294)", "html": "1328 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (b. 1294)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (b. 1294)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1501", "text": "<PERSON><PERSON><PERSON> of Bavaria (b. 1439)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Bavaria\"><PERSON><PERSON><PERSON> of Bavaria</a> (b. 1439)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Bavaria\"><PERSON><PERSON><PERSON> of Bavaria</a> (b. 1439)", "links": [{"title": "<PERSON><PERSON><PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bavaria"}]}, {"year": "1542", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (b. 1480)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Al<PERSON>o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1480)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Al<PERSON><PERSON>o"}]}, {"year": "1563", "text": "Menas of Ethiopia", "html": "1563 - <a href=\"https://wikipedia.org/wiki/Menas_of_Ethiopia\" title=\"Menas of Ethiopia\">Menas of Ethiopia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Menas_of_Ethiopia\" title=\"Menas of Ethiopia\">Menas of Ethiopia</a>", "links": [{"title": "Menas of Ethiopia", "link": "https://wikipedia.org/wiki/Menas_of_Ethiopia"}]}, {"year": "1590", "text": "<PERSON>, English theologian and academic (b. 1527)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and academic (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and academic (b. 1527)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON> (b. 1610)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Alexander <PERSON>\">Pope <PERSON> VIII</a> (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Alexander <PERSON>\">Pope <PERSON> VIII</a> (b. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, 1st Duke of Shrewsbury, English politician, Lord High Treasurer (b. 1660)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Shrewsbury\" title=\"<PERSON>, 1st Duke of Shrewsbury\"><PERSON>, 1st Duke of Shrewsbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Shrewsbury\" title=\"<PERSON>, 1st Duke of Shrewsbury\"><PERSON>, 1st Duke of Shrewsbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1660)", "links": [{"title": "<PERSON>, 1st Duke of Shrewsbury", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Shrewsbury"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1733", "text": "<PERSON> the Strong, Polish king (b. 1670)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_the_Strong\" title=\"Augustus II the Strong\"><PERSON> II the Strong</a>, Polish king (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_the_Strong\" title=\"Augustus II the Strong\"><PERSON> <PERSON> the Strong</a>, Polish king (b. 1670)", "links": [{"title": "<PERSON> the Strong", "link": "https://wikipedia.org/wiki/<PERSON>_II_the_Strong"}]}, {"year": "1734", "text": "<PERSON>, English physician and author (b. 1649)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and author (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and author (b. 1649)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "1743", "text": "<PERSON>, Italian organist and composer (b. 1657)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1657)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON><PERSON> of Georgia (b. 1699)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Georgia\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Georgia\"><PERSON><PERSON> of Georgia</a> (b. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Georgia\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Georgia\"><PERSON><PERSON> of Georgia</a> (b. 1699)", "links": [{"title": "<PERSON><PERSON> of Georgia", "link": "https://wikipedia.org/wiki/Bakar_of_Georgia"}]}, {"year": "1761", "text": "<PERSON>, French priest and historian (b. 1682)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and historian (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>rle<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and historian (b. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "Sir <PERSON>, 4th Baronet, English field marshal and politician (b. 1685)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, English field marshal and politician (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, English field marshal and politician (b. 1685)", "links": [{"title": "Sir <PERSON>, 4th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet"}]}, {"year": "1793", "text": "<PERSON>, 2nd Viscount <PERSON>, English politician, Chancellor of the Exchequer (b. 1717)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_Barrington\" title=\"<PERSON>, 2nd Viscount Barrington\"><PERSON>, 2nd Viscount Barr<PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_Barrington\" title=\"<PERSON>, 2nd Viscount Barrington\"><PERSON>, 2nd Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1717)", "links": [{"title": "<PERSON>, 2nd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1803", "text": "<PERSON>, Finnish economist, philosopher and Lutheran priest (b. 1729)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish economist, philosopher and Lutheran priest (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish economist, philosopher and Lutheran priest (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, American judge and politician (b. 1777)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, English novelist and playwright (b. 1797)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Russian composer and critic (b. 1820)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and critic (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and critic (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American lawyer and politician, 22nd Mayor of San Francisco (b. 1824)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 22nd <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 22nd <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of San Francisco", "link": "https://wikipedia.org/wiki/Mayor_of_San_Francisco"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Austrian geologist and botanist (b. 1826)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_von_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\">Con<PERSON><PERSON> <PERSON></a>, Austrian geologist and botanist (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\">Con<PERSON><PERSON> <PERSON></a>, Austrian geologist and botanist (b. 1826)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "Sir <PERSON>, Anglo-Irish physicist, mathematician, and politician (b. 1819)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a>, Anglo-Irish physicist, mathematician, and politician (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a>, Anglo-Irish physicist, mathematician, and politician (b. 1819)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}]}, {"year": "1907", "text": "<PERSON>, French businessman (b. 1858)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_<PERSON>let"}]}, {"year": "1908", "text": "<PERSON> of Portugal (b. 1863)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1863)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1916", "text": "<PERSON>, English-Australian politician, 11th Premier of South Australia (b. 1831)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1917", "text": "<PERSON>, Norwegian architect (b. 1829)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian architect (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian architect (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor and director (b. 1872)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American painter (b. 1858)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1869)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Greek general and politician, 128th Prime Minister of Greece (b. 1878)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician, 128th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician, 128th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_<PERSON>lis"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1940", "text": "<PERSON>, American author, created <PERSON> (b. 1888)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Greek journalist and critic (b. 1877)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek journalist and critic (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek journalist and critic (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Dutch-American painter (b. 1872)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American painter (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American painter (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mondrian"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Romanian journalist, author, and activist (b. 1880)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist, author, and activist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist, author, and activist (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American conductor and composer (b. 1885)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and composer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and composer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German general (b. 1890)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1888)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1959", "text": "<PERSON>, American actress (b. 1873)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>\" title=\"Madame <PERSON>\">Madame <PERSON></a>, American actress (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madame_<PERSON>\" title=\"Madame <PERSON>\">Madame <PERSON></a>, American actress (b. 1873)", "links": [{"title": "Madame <PERSON>-Te<PERSON>Wan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Norwegian psychiatrist (b. 1869)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian psychiatrist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian psychiatrist (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American actress and journalist (b. 1885)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Hedd<PERSON>_<PERSON>\" title=\"<PERSON>dd<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and journalist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>dd<PERSON>_<PERSON>\" title=\"<PERSON>dd<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and journalist (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hedda_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1895)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON> and <PERSON> - sparking the Memphis Sanitation Workers Strike", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Death of <PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a> - sparking the Memphis Sanitation Workers Strike", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>_and_<PERSON>\" title=\"Death of <PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a> - sparking the Memphis Sanitation Workers Strike", "links": [{"title": "Death of <PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian mathematician and academic (b. 1921)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Alfr%C3%A9d_R%C3%A9nyi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian mathematician and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfr%C3%A9d_R%C3%A9nyi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian mathematician and academic (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alfr%C3%A9d_R%C3%A9nyi"}]}, {"year": "1976", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (b. 1901)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1976", "text": "<PERSON>, American physician and pathologist, Nobel Prize laureate (b. 1878)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Turkish journalist and activist (b. 1929)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Abdi_%C4%B0pek%C3%A7i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist and activist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdi_%C4%B0pek%C3%A7i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist and activist (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdi_%C4%B0pek%C3%A7i"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON> (activist), Basque activist (b.1961)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_(activist)\" title=\"<PERSON><PERSON><PERSON> (activist)\"><PERSON><PERSON><PERSON> (activist)</a>, Basque activist (b.1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_(activist)\" title=\"<PERSON><PERSON><PERSON> (activist)\"><PERSON><PERSON><PERSON> (activist)</a>, Basque activist (b.1961)", "links": [{"title": "<PERSON><PERSON><PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_(activist)"}]}, {"year": "1981", "text": "<PERSON>, Sr., American engineer and businessman, founded the Douglas Aircraft Company (b. 1892)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Douglas_Aircraft_Company\" title=\"Douglas Aircraft Company\">Douglas Aircraft Company</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Douglas_Aircraft_Company\" title=\"Douglas Aircraft Company\">Douglas Aircraft Company</a> (b. 1892)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr."}, {"title": "Douglas Aircraft Company", "link": "https://wikipedia.org/wiki/Douglas_Aircraft_Company"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Norwegian pianist and composer (b. 1908)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tveitt\" title=\"<PERSON><PERSON><PERSON> Tveitt\"><PERSON><PERSON><PERSON></a>, Norwegian pianist and composer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tveitt\" title=\"<PERSON><PERSON><PERSON> Tveitt\"><PERSON><PERSON><PERSON></a>, Norwegian pianist and composer (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tveitt"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Swedish sociologist and politician, Nobel Prize laureate (b. 1902)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sociologist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sociologist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>va_<PERSON>rdal"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1987", "text": "<PERSON>, Italian director and screenwriter (b. 1900)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American child actress (b. 1975)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, American child actress (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, American child actress (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heather_O%27Rourke"}]}, {"year": "1989", "text": "<PERSON>, American painter and academic (b. 1918)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Uruguayan lead singer of the band \"Los Iracundos\" (b. 1945)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Uruguayan lead singer of the band \"Los Iracundos\" (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Uruguayan lead singer of the band \"Los Iracundos\" (b. 1945)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1991", "text": "<PERSON>, Saudi Arabian writer and journalist (b. 1916)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>tar\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian writer and journalist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian writer and journalist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, French physician and surgeon (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and surgeon (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and surgeon (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Swedish modern pentathlete and épée fencer (b. 1904)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish modern pentathlete and épée fencer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish modern pentathlete and épée fencer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American race car driver, pilot, and businessman (b. 1915)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver, pilot, and businessman (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver, pilot, and businessman (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American journalist and author (b. 1916)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American art collector and philanthropist (b. 1907)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and philanthropist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and philanthropist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Canadian political scientist and academic (b. 1929)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_D%27Allemagne\" title=\"<PERSON>Allemagne\"><PERSON></a>, Canadian political scientist and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_D%27Allemagne\" title=\"<PERSON>\"><PERSON></a>, Canadian political scientist and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_D%27Allemagne"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Turkish geologist and academic (b. 1951)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish geologist and academic (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish geologist and academic (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ut_Barka"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, German actress and singer (b. 1925)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nef\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actress and singer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nef\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actress and singer (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nef"}]}, {"year": "2003", "text": "Space Shuttle Columbia crew\n<PERSON>, American colonel, pilot, and astronaut (b. 1959)\n<PERSON>, American captain, pilot, and astronaut (b. 1956)\n<PERSON><PERSON><PERSON><PERSON>, Indian-American engineer and astronaut (b. 1961)\n<PERSON>, American captain, surgeon, and astronaut (b. 1961)\n<PERSON>, American colonel, pilot, and astronaut (b. 1957)\n<PERSON>, American commander, pilot, and astronaut (b. 1961)\n<PERSON><PERSON>, Israeli colonel, pilot, and astronaut (b. 1954)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> crew\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1959)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"David <PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1956)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Kalpana_<PERSON>wla\" title=\"Kalpana Chawla\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-American engineer and astronaut (b. 1961)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, surgeon, and astronaut (b. 1961)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1957)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_C._McCool\" title=\"William C. McCool\">William C. McCool</a>, American commander, pilot, and astronaut (b. 1961)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Ilan_Ramon\" title=\"Ilan <PERSON>\">Ilan Ramon</a>, Israeli colonel, pilot, and astronaut (b. 1954)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> crew\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1959)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1956)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Kalpana_Chawla\" title=\"Kalpana Chawla\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-American engineer and astronaut (b. 1961)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, surgeon, and astronaut (b. 1961)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1957)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/William_C._McCool\" title=\"William C. McCool\">William C. McCool</a>, American commander, pilot, and astronaut (b. 1961)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Ilan_<PERSON>\" title=\"Ilan Ramon\">Ilan <PERSON></a>, Israeli colonel, pilot, and astronaut (b. 1954)</li>\n</ul>", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>c<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American colonel, pilot, and astronaut (b. 1959)", "text": null, "html": "<PERSON>, American colonel, pilot, and astronaut (b. 1959) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American captain, pilot, and astronaut (b. 1956)", "text": null, "html": "<PERSON>, American captain, pilot, and astronaut (b. 1956) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON><PERSON>, Indian-American engineer and astronaut (b. 1961)", "text": null, "html": "<PERSON><PERSON><PERSON><PERSON>, Indian-American engineer and astronaut (b. 1961) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-American engineer and astronaut (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-American engineer and astronaut (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "<PERSON>, American captain, surgeon, and astronaut (b. 1961)", "text": null, "html": "<PERSON>, American captain, surgeon, and astronaut (b. 1961) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, surgeon, and astronaut (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, surgeon, and astronaut (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American colonel, pilot, and astronaut (b. 1957)", "text": null, "html": "<PERSON>, American colonel, pilot, and astronaut (b. 1957) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>sband\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American commander, pilot, and astronaut (b. 1961)", "text": null, "html": "<PERSON>, American commander, pilot, and astronaut (b. 1961) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>cCool\" title=\"<PERSON>\"><PERSON></a>, American commander, pilot, and astronaut (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_McCool\" title=\"<PERSON>\"><PERSON></a>, American commander, pilot, and astronaut (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>c<PERSON>"}]}, {"year": "<PERSON><PERSON>, Israeli colonel, pilot, and astronaut (b. 1954)", "text": null, "html": "<PERSON><PERSON>, Israeli colonel, pilot, and astronaut (b. 1954) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli colonel, pilot, and astronaut (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli colonel, pilot, and astronaut (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Cuban-American drummer and bandleader (b. 1922)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Mongo_Santamar%C3%ADa\" title=\"Mongo Santamar<PERSON>\"><PERSON><PERSON></a>, Cuban-American drummer and bandleader (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongo_Santamar%C3%ADa\" title=\"Mongo <PERSON>mar<PERSON>\"><PERSON><PERSON></a>, Cuban-American drummer and bandleader (b. 1922)", "links": [{"title": "Mongo <PERSON>", "link": "https://wikipedia.org/wiki/Mongo_Santamar%C3%ADa"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Turkish director, producer, and screenwriter (b. 1942)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Suha_Ar%C4%B1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suha_Ar%C4%B1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suha_Ar%C4%B1n"}]}, {"year": "2005", "text": "<PERSON>, Canadian-American actor (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Italian-American playwright and composer (b. 1911)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian-American playwright and composer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian-American playwright and composer (b. 1911)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Brazilian actor and businessman (b. 1937)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actor and businessman (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actor and businessman (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beto_Carrero"}]}, {"year": "2012", "text": "<PERSON>, American television host and producer (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and producer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and producer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Polish poet and translator, Nobel Prize laureate (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Wis%C5%82awa_Szymborska\" title=\"<PERSON><PERSON><PERSON> Szymborsk<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wis%C5%82awa_Szymborska\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wis%C5%82awa_Szymborska"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American politician (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer, judge, and politician, 105th Mayor of New York City (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 105th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 105th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Indian painter and educator (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian painter and educator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian painter and educator (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter and producer (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Spanish footballer and manager (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Luis_Aragon%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luis_Aragon%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Aragon%C3%A9s"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Russian marshal (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(military)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (military)\"><PERSON><PERSON></a>, Russian marshal (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(military)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (military)\"><PERSON><PERSON></a>, Russian marshal (b. 1917)", "links": [{"title": "<PERSON><PERSON> (military)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(military)"}]}, {"year": "2014", "text": "<PERSON>, American poet, painter, and critic (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, painter, and critic (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, painter, and critic (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Austrian-Swiss actor, director, producer, and screenwriter (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss actor, director, producer, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Maximilian <PERSON>\"><PERSON></a>, Austrian-Swiss actor, director, producer, and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Italian-French pianist (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French pianist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French pianist (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, German footballer, manager, and sportscaster (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Udo_<PERSON>ttek\" title=\"Udo <PERSON>ttek\"><PERSON><PERSON></a>, German footballer, manager, and sportscaster (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Udo_<PERSON>ttek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer, manager, and sportscaster (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Udo_<PERSON>k"}]}, {"year": "2015", "text": "<PERSON>, American animator, director, and screenwriter (b. 1981)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and screenwriter (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and screenwriter (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Guatemalan general and politician, 27th President of Guatemala (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_Humberto_Mej%C3%ADa_Victores\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Guatemalan general and politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_Humberto_Mej%C3%ADa_Victor<PERSON>\" class=\"mw-redirect\" title=\"Óscar <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Guatemalan general and politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_Humberto_Mej%C3%ADa_Victores"}, {"title": "President of Guatemala", "link": "https://wikipedia.org/wiki/President_of_Guatemala"}]}, {"year": "2017", "text": "<PERSON>, British actor and broadcaster (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and broadcaster (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and broadcaster (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Belarusian rocket scientist (b. 1910)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kit\" title=\"Barys Kit\"><PERSON><PERSON></a>, Belarusian rocket scientist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kit\" title=\"Barys Kit\"><PERSON><PERSON></a>, Belarusian rocket scientist (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barys_Kit"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>y Radio, Ugandan singer and songwriter (b. 1985)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Mowzey_Radio\" title=\"Mowzey Radio\">Mowzey Radio</a>, Ugandan singer and songwriter (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mowzey_Radio\" title=\"Mowzey Radio\">Mowzey Radio</a>, Ugandan singer and songwriter (b. 1985)", "links": [{"title": "Mowzey Radio", "link": "https://wikipedia.org/wiki/Mowzey_Radio"}]}, {"year": "2019", "text": "<PERSON>, English comedian, radio host and panelist (b. 1961)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, radio host and panelist (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, radio host and panelist (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, English actor (b. 1936)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American football player and coach (b. 1959)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1959)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2021", "text": "<PERSON>, American actor, director, stand-up comedian, and musician (b. 1977)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dustin Diamond\"><PERSON></a>, American actor, director, stand-up comedian, and musician (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dustin Diamond\"><PERSON></a>, American actor, director, stand-up comedian, and musician (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Georgian pop singer and actor (b. 1946)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian pop singer and actor (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian pop singer and actor (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Canadian bishop of the Catholic Church (b. 1924)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian bishop of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian bishop of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "2025", "text": "<PERSON><PERSON>, Polish-German economist and politician, 9th President of Germany (b. 1943)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German economist and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German economist and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a> (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horst_K%C3%B6hler"}, {"title": "President of Germany", "link": "https://wikipedia.org/wiki/President_of_Germany"}]}, {"year": "2025", "text": "<PERSON>, American lawyer and businessman, 8th Commissioner of Baseball (b. 1938)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman, 8th <a href=\"https://wikipedia.org/wiki/Commissioner_of_Baseball\" title=\"Commissioner of Baseball\">Commissioner of Baseball</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman, 8th <a href=\"https://wikipedia.org/wiki/Commissioner_of_Baseball\" title=\"Commissioner of Baseball\">Commissioner of Baseball</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Commissioner of Baseball", "link": "https://wikipedia.org/wiki/Commissioner_of_Baseball"}]}]}}