{"date": "April 27", "url": "https://wikipedia.org/wiki/April_27", "data": {"Events": [{"year": "247", "text": "<PERSON> the Arab marks the millennium of Rome with a celebration of the ludi saeculares.", "html": "247 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Arab\" title=\"<PERSON> the Arab\"><PERSON> the Arab</a> marks the millennium of Rome with a celebration of the <i><a href=\"https://wikipedia.org/wiki/Secular_Games\" title=\"Secular Games\">ludi saeculares</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Arab\" title=\"<PERSON> the Arab\"><PERSON> the Arab</a> marks the millennium of Rome with a celebration of the <i><a href=\"https://wikipedia.org/wiki/Secular_Games\" title=\"Secular Games\">ludi saeculares</a></i>.", "links": [{"title": "<PERSON> the Arab", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arab"}, {"title": "Secular Games", "link": "https://wikipedia.org/wiki/Secular_Games"}]}, {"year": "395", "text": "Emperor <PERSON><PERSON><PERSON> marries <PERSON><PERSON><PERSON>, daughter of the Frankish general <PERSON><PERSON><PERSON>. She becomes one of the more powerful Roman empresses of Late Antiquity.", "html": "395 - Emperor <a href=\"https://wikipedia.org/wiki/Arcadius\" title=\"Arcadius\"><PERSON><PERSON><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of the <a href=\"https://wikipedia.org/wiki/Franks\" title=\"<PERSON>\">Frankish</a> general <a href=\"https://wikipedia.org/wiki/Flavius_Bauto\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Bauto\"><PERSON><PERSON><PERSON></a>. She becomes one of the more powerful Roman empresses of <a href=\"https://wikipedia.org/wiki/Late_Antiquity\" class=\"mw-redirect\" title=\"Late Antiquity\">Late Antiquity</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Arcadius\" title=\"Arcadi<PERSON>\"><PERSON><PERSON><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_E<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of the <a href=\"https://wikipedia.org/wiki/Franks\" title=\"<PERSON>\">Frankish</a> general <a href=\"https://wikipedia.org/wiki/F<PERSON><PERSON>_Bauto\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Bauto\"><PERSON><PERSON><PERSON></a>. She becomes one of the more powerful Roman empresses of <a href=\"https://wikipedia.org/wiki/Late_Antiquity\" class=\"mw-redirect\" title=\"Late Antiquity\">Late Antiquity</a>.", "links": [{"title": "Arcadius", "link": "https://wikipedia.org/wiki/Arcadius"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franks"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Late Antiquity", "link": "https://wikipedia.org/wiki/Late_Antiquity"}]}, {"year": "711", "text": "Islamic conquest of Hispania: Moorish troops led by <PERSON><PERSON><PERSON> land at Gibraltar to begin their invasion of the Iberian Peninsula (Al-Andalus).", "html": "711 - <a href=\"https://wikipedia.org/wiki/Umayyad_conquest_of_Hispania\" class=\"mw-redirect\" title=\"Umayyad conquest of Hispania\">Islamic conquest of Hispania</a>: <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moorish</a> troops led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn Z<PERSON>d\"><PERSON><PERSON><PERSON> ibn <PERSON></a> land at <a href=\"https://wikipedia.org/wiki/Gibraltar\" title=\"Gibraltar\">Gibraltar</a> to begin their invasion of the <a href=\"https://wikipedia.org/wiki/Iberian_Peninsula\" title=\"Iberian Peninsula\">Iberian Peninsula</a> (<a href=\"https://wikipedia.org/wiki/Al-Andalus\" title=\"Al-Andalus\">Al-Andalus</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umayyad_conquest_of_Hispania\" class=\"mw-redirect\" title=\"Umayyad conquest of Hispania\">Islamic conquest of Hispania</a>: <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moorish</a> troops led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a> land at <a href=\"https://wikipedia.org/wiki/Gibraltar\" title=\"Gibraltar\">Gibraltar</a> to begin their invasion of the <a href=\"https://wikipedia.org/wiki/Iberian_Peninsula\" title=\"Iberian Peninsula\">Iberian Peninsula</a> (<a href=\"https://wikipedia.org/wiki/Al-Andalus\" title=\"Al-Andalus\">Al-Andalus</a>).", "links": [{"title": "Umayyad conquest of Hispania", "link": "https://wikipedia.org/wiki/Umayyad_conquest_of_Hispania"}, {"title": "Moors", "link": "https://wikipedia.org/wiki/Moors"}, {"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Gibraltar", "link": "https://wikipedia.org/wiki/Gibraltar"}, {"title": "Iberian Peninsula", "link": "https://wikipedia.org/wiki/Iberian_Peninsula"}, {"title": "Al-Andalus", "link": "https://wikipedia.org/wiki/Al-Andalus"}]}, {"year": "1296", "text": "First War of Scottish Independence: <PERSON>'s Scottish army is defeated by an English army commanded by <PERSON>, 6th Earl of Surrey at the Battle of Dunbar.", "html": "1296 - <a href=\"https://wikipedia.org/wiki/First_War_of_Scottish_Independence\" title=\"First War of Scottish Independence\">First War of Scottish Independence</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s Scottish army is defeated by an English army commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_<PERSON>_Surrey\" title=\"<PERSON>, 6th Earl of Surrey\"><PERSON>, 6th Earl of Surrey</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Dunbar_(1296)\" title=\"Battle of Dunbar (1296)\">Battle of Dunbar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_War_of_Scottish_Independence\" title=\"First War of Scottish Independence\">First War of Scottish Independence</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s Scottish army is defeated by an English army commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_<PERSON>_Surrey\" title=\"<PERSON>, 6th Earl of Surrey\"><PERSON>, 6th Earl of Surrey</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Dunbar_(1296)\" title=\"Battle of Dunbar (1296)\">Battle of Dunbar</a>.", "links": [{"title": "First War of Scottish Independence", "link": "https://wikipedia.org/wiki/First_War_of_Scottish_Independence"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>, 6th Earl of Surrey", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_Surrey"}, {"title": "Battle of Dunbar (1296)", "link": "https://wikipedia.org/wiki/Battle_of_Dunbar_(1296)"}]}, {"year": "1509", "text": "<PERSON> <PERSON> places the Italian state of Venice under interdict.", "html": "1509 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Julius_II\" title=\"Pope Julius II\">Pope <PERSON> II</a> places the Italian state of <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venice</a> under <a href=\"https://wikipedia.org/wiki/Interdict_(Catholic_canon_law)\" class=\"mw-redirect\" title=\"Interdict (Catholic canon law)\">interdict</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Julius_II\" title=\"Pope Julius II\">Pope <PERSON> II</a> places the Italian state of <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venice</a> under <a href=\"https://wikipedia.org/wiki/Interdict_(Catholic_canon_law)\" class=\"mw-redirect\" title=\"Interdict (Catholic canon law)\">interdict</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "Interdict (Catholic canon law)", "link": "https://wikipedia.org/wiki/Interdict_(Catholic_canon_law)"}]}, {"year": "1521", "text": "Battle of Mactan: Explorer <PERSON> is killed by natives in the Philippines led by chief <PERSON><PERSON><PERSON><PERSON>.", "html": "1521 - <a href=\"https://wikipedia.org/wiki/Battle_of_Mactan\" title=\"Battle of Mactan\">Battle of Mactan</a>: Explorer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed by natives in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> led by chief <a href=\"https://wikipedia.org/wiki/Lapulapu\" title=\"Lapulapu\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Mactan\" title=\"Battle of Mactan\">Battle of Mactan</a>: Explorer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed by natives in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> led by chief <a href=\"https://wikipedia.org/wiki/Lapulapu\" title=\"Lapulapu\">La<PERSON><PERSON>u</a>.", "links": [{"title": "Battle of Mactan", "link": "https://wikipedia.org/wiki/Battle_of_Mactan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>lapu"}]}, {"year": "1539", "text": "Official founding of the city of Bogotá, New Granada (nowadays Colombia), by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.", "html": "1539 - Official founding of the city of <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a>, New Granada (nowadays <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>), by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Federmann\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar\" title=\"Sebas<PERSON><PERSON> de Belalcázar\"><PERSON>bas<PERSON><PERSON> Belalcázar</a>.", "no_year_html": "Official founding of the city of <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a>, New Granada (nowadays <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>), by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar\" title=\"<PERSON><PERSON><PERSON><PERSON> de Belalcázar\">Sebastián de Belalcázar</a>.", "links": [{"title": "Bogotá", "link": "https://wikipedia.org/wiki/Bogot%C3%A1"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> de Belalcázar", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar"}]}, {"year": "1565", "text": "Cebu is established becoming the first Spanish settlement in the Philippines.", "html": "1565 - <a href=\"https://wikipedia.org/wiki/Cebu_City\" title=\"Cebu City\">Cebu</a> is established becoming the first Spanish settlement in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cebu_City\" title=\"Cebu City\">Cebu</a> is established becoming the first Spanish settlement in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "Cebu City", "link": "https://wikipedia.org/wiki/Cebu_City"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1595", "text": "The relics of <PERSON> are incinerated in Belgrade on the Vračar plateau by Ottoman Grand Vizier <PERSON>; the site of the incineration is now the location of the Church of Saint Sava, one of the largest Orthodox churches in the world", "html": "1595 - The relics of <a href=\"https://wikipedia.org/wiki/Saint_Sava\" title=\"Saint <PERSON>va\">Saint <PERSON></a> are incinerated in <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a> on the Vračar plateau by Ottoman Grand Vizier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>; the site of the incineration is now the location of the <a href=\"https://wikipedia.org/wiki/Church_of_Saint_Sava\" title=\"Church of Saint Sava\">Church of Saint Sava</a>, one of the largest Orthodox churches in the world", "no_year_html": "The relics of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>va\" title=\"Saint <PERSON>\">Saint <PERSON></a> are incinerated in <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a> on the Vračar plateau by Ottoman Grand Vizier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>; the site of the incineration is now the location of the <a href=\"https://wikipedia.org/wiki/Church_of_Saint_Sava\" title=\"Church of Saint Sava\">Church of Saint Sava</a>, one of the largest Orthodox churches in the world", "links": [{"title": "Saint <PERSON>", "link": "https://wikipedia.org/wiki/Saint_Sava"}, {"title": "Belgrade", "link": "https://wikipedia.org/wiki/Belgrade"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Church of Saint Sava", "link": "https://wikipedia.org/wiki/Church_of_Saint_Sava"}]}, {"year": "1650", "text": "The Battle of Carbisdale: A Royalist army from Orkney invades mainland Scotland but is defeated by a Covenanter army.", "html": "1650 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Carbisdale\" title=\"Battle of Carbisdale\">Battle of Carbisdale</a>: A <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalist</a> army from <a href=\"https://wikipedia.org/wiki/Orkney\" title=\"Orkney\">Orkney</a> invades mainland Scotland but is defeated by a <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanter</a> army.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Carbisdale\" title=\"Battle of Carbisdale\">Battle of Carbisdale</a>: A <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalist</a> army from <a href=\"https://wikipedia.org/wiki/Orkney\" title=\"Orkney\">Orkney</a> invades mainland Scotland but is defeated by a <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanter</a> army.", "links": [{"title": "Battle of Carbisdale", "link": "https://wikipedia.org/wiki/Battle_of_Carbisdale"}, {"title": "Cavalier", "link": "https://wikipedia.org/wiki/Cavalier"}, {"title": "Orkney", "link": "https://wikipedia.org/wiki/Orkney"}, {"title": "Covenanter", "link": "https://wikipedia.org/wiki/Covenanter"}]}, {"year": "1667", "text": "Blind and impoverished, <PERSON> sells Paradise Lost to a printer for £10, so that it could be entered into the Stationers' Register.", "html": "1667 - Blind and impoverished, <PERSON> sells <i><a href=\"https://wikipedia.org/wiki/Paradise_Lost\" title=\"Paradise Lost\">Paradise Lost</a></i> to a printer for £10, so that it could be entered into the <a href=\"https://wikipedia.org/wiki/Stationers%27_Register\" title=\"Stationers' Register\">Stationers' Register</a>.", "no_year_html": "Blind and impoverished, <PERSON> sells <i><a href=\"https://wikipedia.org/wiki/Paradise_Lost\" title=\"Paradise Lost\">Paradise Lost</a></i> to a printer for £10, so that it could be entered into the <a href=\"https://wikipedia.org/wiki/Stationers%27_Register\" title=\"Stationers' Register\">Stationers' Register</a>.", "links": [{"title": "Paradise Lost", "link": "https://wikipedia.org/wiki/Paradise_Lost"}, {"title": "Stationers' Register", "link": "https://wikipedia.org/wiki/Stationers%27_Register"}]}, {"year": "1805", "text": "First Barbary War: United States Marines and Berbers attack the Tripolitan city of Derna (The \"shores of Tripoli\" part of the Marines' Hymn).", "html": "1805 - <a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marines\" class=\"mw-redirect\" title=\"United States Marines\">United States Marines</a> and <a href=\"https://wikipedia.org/wiki/Berbers\" title=\"Berbers\">Berbers</a> attack the <a href=\"https://wikipedia.org/wiki/Tripoli,_Libya\" title=\"Tripoli, Libya\">Tripolitan</a> city of <a href=\"https://wikipedia.org/wiki/Derna,_Libya\" title=\"Derna, Libya\">Derna</a> (The \"shores of Tripoli\" part of the <a href=\"https://wikipedia.org/wiki/Marines%27_Hymn\" title=\"Marines' Hymn\">Marines' Hymn</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marines\" class=\"mw-redirect\" title=\"United States Marines\">United States Marines</a> and <a href=\"https://wikipedia.org/wiki/Berbers\" title=\"Berbers\">Berbers</a> attack the <a href=\"https://wikipedia.org/wiki/Tripoli,_Libya\" title=\"Tripoli, Libya\">Tripolitan</a> city of <a href=\"https://wikipedia.org/wiki/Derna,_Libya\" title=\"Derna, Libya\">Derna</a> (The \"shores of Tripoli\" part of the <a href=\"https://wikipedia.org/wiki/Marines%27_Hymn\" title=\"Marines' Hymn\">Marines' Hymn</a>).", "links": [{"title": "First Barbary War", "link": "https://wikipedia.org/wiki/First_Barbary_War"}, {"title": "United States Marines", "link": "https://wikipedia.org/wiki/United_States_Marines"}, {"title": "Berbers", "link": "https://wikipedia.org/wiki/Berbers"}, {"title": "Tripoli, Libya", "link": "https://wikipedia.org/wiki/Tripoli,_Libya"}, {"title": "Derna, Libya", "link": "https://wikipedia.org/wiki/Derna,_Libya"}, {"title": "Marines' Hymn", "link": "https://wikipedia.org/wiki/Marines%27_Hymn"}]}, {"year": "1813", "text": "War of 1812: American troops capture York, the capital of Upper Canada, in the Battle of York.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: American troops capture <a href=\"https://wikipedia.org/wiki/York,_Upper_Canada\" title=\"York, Upper Canada\">York</a>, the capital of <a href=\"https://wikipedia.org/wiki/Upper_Canada\" title=\"Upper Canada\">Upper Canada</a>, in the <a href=\"https://wikipedia.org/wiki/Battle_of_York\" title=\"Battle of York\">Battle of York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: American troops capture <a href=\"https://wikipedia.org/wiki/York,_Upper_Canada\" title=\"York, Upper Canada\">York</a>, the capital of <a href=\"https://wikipedia.org/wiki/Upper_Canada\" title=\"Upper Canada\">Upper Canada</a>, in the <a href=\"https://wikipedia.org/wiki/Battle_of_York\" title=\"Battle of York\">Battle of York</a>.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "York, Upper Canada", "link": "https://wikipedia.org/wiki/York,_Upper_Canada"}, {"title": "Upper Canada", "link": "https://wikipedia.org/wiki/Upper_Canada"}, {"title": "Battle of York", "link": "https://wikipedia.org/wiki/Battle_of_York"}]}, {"year": "1861", "text": "American President <PERSON> suspends the writ of habeas corpus.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">American President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> suspends the writ of <i><a href=\"https://wikipedia.org/wiki/Habeas_corpus_in_the_United_States\" title=\"Habeas corpus in the United States\">habeas corpus</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">American President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> suspends the writ of <i><a href=\"https://wikipedia.org/wiki/Habeas_corpus_in_the_United_States\" title=\"Habeas corpus in the United States\">habeas corpus</a></i>.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Habeas corpus in the United States", "link": "https://wikipedia.org/wiki/Habeas_corpus_in_the_United_States"}]}, {"year": "1906", "text": "The State Duma of the Russian Empire meets for the first time.", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/State_Duma_(Russian_Empire)\" title=\"State Duma (Russian Empire)\">State Duma</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a> meets for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/State_Duma_(Russian_Empire)\" title=\"State Duma (Russian Empire)\">State Duma</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a> meets for the first time.", "links": [{"title": "State Duma (Russian Empire)", "link": "https://wikipedia.org/wiki/State_Duma_(Russian_Empire)"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1909", "text": "Sultan of Ottoman Empire <PERSON> is overthrown, and is succeeded by his brother, <PERSON><PERSON><PERSON>.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">Sultan</a> of <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is overthrown, and is succeeded by his brother, <a href=\"https://wikipedia.org/wiki/Mehmed_V\" title=\"Mehmed V\">Mehmed V</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">Sultan</a> of <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is overthrown, and is succeeded by his brother, <a href=\"https://wikipedia.org/wiki/Mehmed_V\" title=\"Mehmed V\">Mehmed V</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sultan"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mehmed_V"}]}, {"year": "1911", "text": "The Second Canton Uprising took place in Guangzhou, Qing China but was suppressed.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/Huanghuagang_Uprising\" class=\"mw-redirect\" title=\"Huanghuagang Uprising\">Second Canton Uprising</a> took place in <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a>, <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing China</a> but was suppressed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Huanghuagang_Uprising\" class=\"mw-redirect\" title=\"Huanghuagang Uprising\">Second Canton Uprising</a> took place in <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a>, <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing China</a> but was suppressed.", "links": [{"title": "Huanghuagang Uprising", "link": "https://wikipedia.org/wiki/Huanghuagang_Uprising"}, {"title": "Guangzhou", "link": "https://wikipedia.org/wiki/Guangzhou"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}]}, {"year": "1927", "text": "Carabineros de Chile (Chilean national police force and gendarmerie) are created.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Carabineros_de_Chile\" title=\"Carabineros de Chile\">Carabineros de Chile</a> (<a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> national <a href=\"https://wikipedia.org/wiki/Police\" title=\"Police\">police</a> force and <a href=\"https://wikipedia.org/wiki/Gendarmerie\" title=\"Gendarmerie\">gendarmerie</a>) are created.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carabineros_de_Chile\" title=\"Carabineros de Chile\">Carabineros de Chile</a> (<a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> national <a href=\"https://wikipedia.org/wiki/Police\" title=\"Police\">police</a> force and <a href=\"https://wikipedia.org/wiki/Gendarmerie\" title=\"Gendarmerie\">gendarmerie</a>) are created.", "links": [{"title": "Carabineros de Chile", "link": "https://wikipedia.org/wiki/Carabineros_de_Chile"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "Police", "link": "https://wikipedia.org/wiki/Police"}, {"title": "Gendarmerie", "link": "https://wikipedia.org/wiki/Gendarmerie"}]}, {"year": "1936", "text": "The United Auto Workers (UAW) gains autonomy from the American Federation of Labor.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> (UAW) gains autonomy from the <a href=\"https://wikipedia.org/wiki/American_Federation_of_Labor\" title=\"American Federation of Labor\">American Federation of Labor</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> (UAW) gains autonomy from the <a href=\"https://wikipedia.org/wiki/American_Federation_of_Labor\" title=\"American Federation of Labor\">American Federation of Labor</a>.", "links": [{"title": "United Auto Workers", "link": "https://wikipedia.org/wiki/United_Auto_Workers"}, {"title": "American Federation of Labor", "link": "https://wikipedia.org/wiki/American_Federation_of_Labor"}]}, {"year": "1941", "text": "World War II: German troops enter Athens.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German troops <a href=\"https://wikipedia.org/wiki/Axis_occupation_of_Greece\" title=\"Axis occupation of Greece\">enter Athens</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German troops <a href=\"https://wikipedia.org/wiki/Axis_occupation_of_Greece\" title=\"Axis occupation of Greece\">enter Athens</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Axis occupation of Greece", "link": "https://wikipedia.org/wiki/Axis_occupation_of_Greece"}]}, {"year": "1945", "text": "World War II: The last German formations withdraw from Finland to Norway. The Lapland War and thus, World War II in Finland, comes to an end and the Raising the Flag on the Three-Country Cairn photograph is taken.", "html": "1945 - World War II: The last German formations withdraw from Finland to Norway. The <a href=\"https://wikipedia.org/wiki/Lapland_War\" title=\"Lapland War\">Lapland War</a> and thus, <a href=\"https://wikipedia.org/wiki/Military_history_of_Finland_during_World_War_II\" class=\"mw-redirect\" title=\"Military history of Finland during World War II\">World War II in Finland</a>, comes to an end and the <i><a href=\"https://wikipedia.org/wiki/Raising_the_Flag_on_the_Three-Country_Cairn\" title=\"Raising the Flag on the Three-Country Cairn\">Raising the Flag on the Three-Country Cairn</a></i> photograph is taken.", "no_year_html": "World War II: The last German formations withdraw from Finland to Norway. The <a href=\"https://wikipedia.org/wiki/Lapland_War\" title=\"Lapland War\">Lapland War</a> and thus, <a href=\"https://wikipedia.org/wiki/Military_history_of_Finland_during_World_War_II\" class=\"mw-redirect\" title=\"Military history of Finland during World War II\">World War II in Finland</a>, comes to an end and the <i><a href=\"https://wikipedia.org/wiki/Raising_the_Flag_on_the_Three-Country_Cairn\" title=\"Raising the Flag on the Three-Country Cairn\">Raising the Flag on the Three-Country Cairn</a></i> photograph is taken.", "links": [{"title": "Lapland War", "link": "https://wikipedia.org/wiki/Lapland_War"}, {"title": "Military history of Finland during World War II", "link": "https://wikipedia.org/wiki/Military_history_of_Finland_during_World_War_II"}, {"title": "Raising the Flag on the Three-Country Cairn", "link": "https://wikipedia.org/wiki/Raising_the_Flag_on_the_Three-Country_Cairn"}]}, {"year": "1945", "text": "World War II: <PERSON> is arrested by Italian partisans in Dongo, while attempting escape disguised as a German soldier.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito Mussolini\"><PERSON></a> is arrested by <a href=\"https://wikipedia.org/wiki/Italian_resistance_movement\" title=\"Italian resistance movement\">Italian partisans</a> in <a href=\"https://wikipedia.org/wiki/Dongo,_Lombardy\" title=\"Dongo, Lombardy\"><PERSON><PERSON></a>, while attempting escape disguised as a German soldier.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a> is arrested by <a href=\"https://wikipedia.org/wiki/Italian_resistance_movement\" title=\"Italian resistance movement\">Italian partisans</a> in <a href=\"https://wikipedia.org/wiki/Dongo,_Lombardy\" title=\"Dongo, Lombardy\"><PERSON><PERSON></a>, while attempting escape disguised as a German soldier.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Italian resistance movement", "link": "https://wikipedia.org/wiki/Italian_resistance_movement"}, {"title": "Dongo, Lombardy", "link": "https://wikipedia.org/wiki/Dongo,_Lombardy"}]}, {"year": "1953", "text": "Operation Moolah offers $50,000 to any pilot who defects with a fully mission-capable <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-15 to South Korea. The first pilot was to receive $100,000.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Operation_Moolah\" title=\"Operation Moolah\">Operation Moolah</a> offers $50,000 to any pilot who defects with a fully mission-capable <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-15\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-15\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-15</a> to South Korea. The first pilot was to receive $100,000.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Moolah\" title=\"Operation Moolah\">Operation Moolah</a> offers $50,000 to any pilot who defects with a fully mission-capable <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-15\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>vich MiG-15\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-15</a> to South Korea. The first pilot was to receive $100,000.", "links": [{"title": "Operation Moolah", "link": "https://wikipedia.org/wiki/Operation_Moolah"}, {"title": "Mikoyan<PERSON><PERSON><PERSON><PERSON> MiG-15", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-15"}]}, {"year": "1967", "text": "Expo 67 officially opens in Montreal, Quebec, Canada with a large opening ceremony broadcast around the world. It opens to the public the next day.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Expo_67\" title=\"Expo 67\">Expo 67</a> officially opens in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal, Quebec</a>, Canada with a large opening ceremony broadcast around the world. It opens to the public the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Expo_67\" title=\"Expo 67\">Expo 67</a> officially opens in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal, Quebec</a>, Canada with a large opening ceremony broadcast around the world. It opens to the public the next day.", "links": [{"title": "Expo 67", "link": "https://wikipedia.org/wiki/Expo_67"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}]}, {"year": "1974", "text": "109 people are killed in a plane crash near Pulkovo Airport.", "html": "1974 - 109 people are killed in a <a href=\"https://wikipedia.org/wiki/1974_Leningrad_Aeroflot_Il-18_crash\" title=\"1974 Leningrad Aeroflot Il-18 crash\">plane crash</a> near <a href=\"https://wikipedia.org/wiki/Pulkovo_Airport\" title=\"Pulkovo Airport\">Pulkovo Airport</a>.", "no_year_html": "109 people are killed in a <a href=\"https://wikipedia.org/wiki/1974_Leningrad_Aeroflot_Il-18_crash\" title=\"1974 Leningrad Aeroflot Il-18 crash\">plane crash</a> near <a href=\"https://wikipedia.org/wiki/Pulkovo_Airport\" title=\"Pulkovo Airport\">Pulkovo Airport</a>.", "links": [{"title": "1974 Leningrad Aeroflot Il-18 crash", "link": "https://wikipedia.org/wiki/1974_Leningrad_Aeroflot_Il-18_crash"}, {"title": "Pulkovo Airport", "link": "https://wikipedia.org/wiki/Pulkovo_Airport"}]}, {"year": "1976", "text": "Thirty-seven people are killed when American Airlines Flight 625 crashes at Cyril E. King Airport in Saint Thomas, U.S. Virgin Islands.", "html": "1976 - Thirty-seven people are killed when <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_625\" title=\"American Airlines Flight 625\">American Airlines Flight 625</a> crashes at <a href=\"https://wikipedia.org/wiki/Cyril_<PERSON>._King_Airport\" title=\"Cyril <PERSON> King Airport\">Cyril <PERSON> Airport</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>_Thomas,_U.S._Virgin_Islands\" title=\"Saint Thomas, U.S. Virgin Islands\">Saint Thomas, U.S. Virgin Islands</a>.", "no_year_html": "Thirty-seven people are killed when <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_625\" title=\"American Airlines Flight 625\">American Airlines Flight 625</a> crashes at <a href=\"https://wikipedia.org/wiki/Cyril_<PERSON>_King_Airport\" title=\"Cyril <PERSON> King Airport\">Cyril <PERSON> Airport</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>_Thomas,_U.S._Virgin_Islands\" title=\"Saint Thomas, U.S. Virgin Islands\">Saint Thomas, U.S. Virgin Islands</a>.", "links": [{"title": "American Airlines Flight 625", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_625"}, {"title": "Cyril <PERSON> King Airport", "link": "https://wikipedia.org/wiki/Cyril_<PERSON>._King_Airport"}, {"title": "Saint Thomas, U.S. Virgin Islands", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_U.S._Virgin_Islands"}]}, {"year": "1978", "text": "<PERSON>, a former aide to U.S. President <PERSON>, is released from the Federal Correctional Institution, Safford, Arizona, after serving 18 months for Watergate-related crimes.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a former aide to U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is released from the <a href=\"https://wikipedia.org/wiki/Federal_Correctional_Institution,_Safford\" title=\"Federal Correctional Institution, Safford\">Federal Correctional Institution, Safford</a>, Arizona, after serving 18 months for <a href=\"https://wikipedia.org/wiki/Watergate\" class=\"mw-redirect\" title=\"Watergate\">Watergate</a>-related crimes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a former aide to U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is released from the <a href=\"https://wikipedia.org/wiki/Federal_Correctional_Institution,_Safford\" title=\"Federal Correctional Institution, Safford\">Federal Correctional Institution, Safford</a>, Arizona, after serving 18 months for <a href=\"https://wikipedia.org/wiki/Watergate\" class=\"mw-redirect\" title=\"Watergate\">Watergate</a>-related crimes.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Correctional Institution, Safford", "link": "https://wikipedia.org/wiki/Federal_Correctional_Institution,_Safford"}, {"title": "Watergate", "link": "https://wikipedia.org/wiki/Watergate"}]}, {"year": "1978", "text": "The Saur Revolution begins in Afghanistan, ending the following morning with the murder of Afghan President <PERSON> and the establishment of the Democratic Republic of Afghanistan.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Saur_Revolution\" title=\"Saur Revolution\">Saur Revolution</a> begins in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, ending the following morning with the murder of Afghan President <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and the establishment of the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan\" title=\"Democratic Republic of Afghanistan\">Democratic Republic of Afghanistan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Saur_Revolution\" title=\"Saur Revolution\">Saur Revolution</a> begins in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, ending the following morning with the murder of Afghan President <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and the establishment of the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan\" title=\"Democratic Republic of Afghanistan\">Democratic Republic of Afghanistan</a>.", "links": [{"title": "Saur Revolution", "link": "https://wikipedia.org/wiki/Saur_Revolution"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Democratic Republic of Afghanistan", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan"}]}, {"year": "1978", "text": "Willow Island disaster: In the deadliest construction accident in United States history, 51 construction workers are killed when a cooling tower under construction collapses at the Pleasants Power Station in Willow Island, West Virginia.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Willow_Island_disaster\" title=\"Willow Island disaster\">Willow Island disaster</a>: In the deadliest construction accident in United States history, 51 construction workers are killed when a <a href=\"https://wikipedia.org/wiki/Cooling_tower\" title=\"Cooling tower\">cooling tower</a> under construction collapses at the <a href=\"https://wikipedia.org/wiki/Pleasants_Power_Station\" title=\"Pleasants Power Station\">Pleasants Power Station</a> in <a href=\"https://wikipedia.org/wiki/Willow_Island,_West_Virginia\" title=\"Willow Island, West Virginia\">Willow Island, West Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Willow_Island_disaster\" title=\"Willow Island disaster\">Willow Island disaster</a>: In the deadliest construction accident in United States history, 51 construction workers are killed when a <a href=\"https://wikipedia.org/wiki/Cooling_tower\" title=\"Cooling tower\">cooling tower</a> under construction collapses at the <a href=\"https://wikipedia.org/wiki/Pleasants_Power_Station\" title=\"Pleasants Power Station\">Pleasants Power Station</a> in <a href=\"https://wikipedia.org/wiki/Willow_Island,_West_Virginia\" title=\"Willow Island, West Virginia\">Willow Island, West Virginia</a>.", "links": [{"title": "Willow Island disaster", "link": "https://wikipedia.org/wiki/Willow_Island_disaster"}, {"title": "Cooling tower", "link": "https://wikipedia.org/wiki/Cooling_tower"}, {"title": "Pleasants Power Station", "link": "https://wikipedia.org/wiki/Pleasants_Power_Station"}, {"title": "Willow Island, West Virginia", "link": "https://wikipedia.org/wiki/Willow_Island,_West_Virginia"}]}, {"year": "1986", "text": "The city of Pripyat and surrounding areas are evacuated due to the Chernobyl disaster.", "html": "1986 - The city of <a href=\"https://wikipedia.org/wiki/Pripyat\" title=\"Pripyat\">Pripyat</a> and surrounding areas are evacuated due to the <a href=\"https://wikipedia.org/wiki/Chernobyl_disaster\" title=\"Chernobyl disaster\">Chernobyl disaster</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Pripyat\" title=\"Pripyat\">Pripyat</a> and surrounding areas are evacuated due to the <a href=\"https://wikipedia.org/wiki/Chernobyl_disaster\" title=\"Chernobyl disaster\">Chernobyl disaster</a>.", "links": [{"title": "Pripyat", "link": "https://wikipedia.org/wiki/Pripyat"}, {"title": "Chernobyl disaster", "link": "https://wikipedia.org/wiki/Chernobyl_disaster"}]}, {"year": "1987", "text": "The U.S. Department of Justice bars Austrian President <PERSON> (and his wife, <PERSON>, who had also been a Nazi) from entering the US, charging that he had aided in the deportations and executions of thousands of Jews and others as a German Army officer during World War II.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">U.S. Department of Justice</a> bars Austrian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (and his wife, <PERSON>, who had also been a Nazi) from entering the US, charging that he had aided in the deportations and executions of thousands of <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> and others as a German Army officer during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">U.S. Department of Justice</a> bars Austrian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (and his wife, <PERSON>, who had also been a Nazi) from entering the US, charging that he had aided in the deportations and executions of thousands of <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> and others as a German Army officer during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "United States Department of Justice", "link": "https://wikipedia.org/wiki/United_States_Department_of_Justice"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1989", "text": "The April 27 demonstrations, student-led protests responding to the April 26 Editorial, during the Tiananmen Square protests of 1989.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/April_27_demonstrations\" title=\"April 27 demonstrations\">April 27 demonstrations</a>, student-led protests responding to the <a href=\"https://wikipedia.org/wiki/April_26_Editorial\" title=\"April 26 Editorial\">April 26 Editorial</a>, during the <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">Tiananmen Square protests of 1989</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/April_27_demonstrations\" title=\"April 27 demonstrations\">April 27 demonstrations</a>, student-led protests responding to the <a href=\"https://wikipedia.org/wiki/April_26_Editorial\" title=\"April 26 Editorial\">April 26 Editorial</a>, during the <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">Tiananmen Square protests of 1989</a>.", "links": [{"title": "April 27 demonstrations", "link": "https://wikipedia.org/wiki/April_27_demonstrations"}, {"title": "April 26 Editorial", "link": "https://wikipedia.org/wiki/April_26_Editorial"}, {"title": "Tiananmen Square protests of 1989", "link": "https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989"}]}, {"year": "1992", "text": "The Federal Republic of Yugoslavia, comprising Serbia and Montenegro, is proclaimed.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Federal Republic of Yugoslavia</a>, comprising <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a> and <a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegro</a>, is proclaimed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Federal Republic of Yugoslavia</a>, comprising <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a> and <a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegro</a>, is proclaimed.", "links": [{"title": "Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Montenegro", "link": "https://wikipedia.org/wiki/Montenegro"}]}, {"year": "1992", "text": "<PERSON> becomes the first woman to be elected Speaker of the British House of Commons in its 700-year history.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to be <a href=\"https://wikipedia.org/wiki/Speaker_of_the_British_House_of_Commons_election,_1992\" class=\"mw-redirect\" title=\"Speaker of the British House of Commons election, 1992\">elected</a> <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker</a> of the <a href=\"https://wikipedia.org/wiki/British_House_of_Commons\" class=\"mw-redirect\" title=\"British House of Commons\">British House of Commons</a> in its 700-year history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to be <a href=\"https://wikipedia.org/wiki/Speaker_of_the_British_House_of_Commons_election,_1992\" class=\"mw-redirect\" title=\"Speaker of the British House of Commons election, 1992\">elected</a> <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker</a> of the <a href=\"https://wikipedia.org/wiki/British_House_of_Commons\" class=\"mw-redirect\" title=\"British House of Commons\">British House of Commons</a> in its 700-year history.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the British House of Commons election, 1992", "link": "https://wikipedia.org/wiki/Speaker_of_the_British_House_of_Commons_election,_1992"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}, {"title": "British House of Commons", "link": "https://wikipedia.org/wiki/British_House_of_Commons"}]}, {"year": "1992", "text": "The Russian Federation and 12 other former Soviet republics become members of the International Monetary Fund and the World Bank.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Russian_Federation\" class=\"mw-redirect\" title=\"Russian Federation\">Russian Federation</a> and 12 other former <a href=\"https://wikipedia.org/wiki/Republics_of_the_Soviet_Union\" title=\"Republics of the Soviet Union\">Soviet republics</a> become members of the <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a> and the <a href=\"https://wikipedia.org/wiki/World_Bank\" title=\"World Bank\">World Bank</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russian_Federation\" class=\"mw-redirect\" title=\"Russian Federation\">Russian Federation</a> and 12 other former <a href=\"https://wikipedia.org/wiki/Republics_of_the_Soviet_Union\" title=\"Republics of the Soviet Union\">Soviet republics</a> become members of the <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a> and the <a href=\"https://wikipedia.org/wiki/World_Bank\" title=\"World Bank\">World Bank</a>.", "links": [{"title": "Russian Federation", "link": "https://wikipedia.org/wiki/Russian_Federation"}, {"title": "Republics of the Soviet Union", "link": "https://wikipedia.org/wiki/Republics_of_the_Soviet_Union"}, {"title": "International Monetary Fund", "link": "https://wikipedia.org/wiki/International_Monetary_Fund"}, {"title": "World Bank", "link": "https://wikipedia.org/wiki/World_Bank"}]}, {"year": "1993", "text": "Most of the Zambia national football team lose their lives in a plane crash off Libreville, Gabon en route to Dakar, Senegal to play a 1994 FIFA World Cup qualifying match against Senegal.", "html": "1993 - Most of the <a href=\"https://wikipedia.org/wiki/Zambia_national_football_team\" title=\"Zambia national football team\">Zambia national football team</a> <a href=\"https://wikipedia.org/wiki/1993_Zambia_national_football_team_plane_crash\" title=\"1993 Zambia national football team plane crash\">lose their lives in a plane crash</a> off <a href=\"https://wikipedia.org/wiki/Libreville\" title=\"Libreville\">Libreville</a>, Gabon en route to <a href=\"https://wikipedia.org/wiki/Dakar\" title=\"Dakar\">Dakar</a>, Senegal to play a <a href=\"https://wikipedia.org/wiki/1994_FIFA_World_Cup\" title=\"1994 FIFA World Cup\">1994 FIFA World Cup</a> <a href=\"https://wikipedia.org/wiki/1994_FIFA_World_Cup_qualification_(CAF)\" title=\"1994 FIFA World Cup qualification (CAF)\">qualifying match</a> against <a href=\"https://wikipedia.org/wiki/Senegal_national_football_team\" title=\"Senegal national football team\">Senegal</a>.", "no_year_html": "Most of the <a href=\"https://wikipedia.org/wiki/Zambia_national_football_team\" title=\"Zambia national football team\">Zambia national football team</a> <a href=\"https://wikipedia.org/wiki/1993_Zambia_national_football_team_plane_crash\" title=\"1993 Zambia national football team plane crash\">lose their lives in a plane crash</a> off <a href=\"https://wikipedia.org/wiki/Libreville\" title=\"Libreville\">Libreville</a>, Gabon en route to <a href=\"https://wikipedia.org/wiki/Dakar\" title=\"Dakar\">Dakar</a>, Senegal to play a <a href=\"https://wikipedia.org/wiki/1994_FIFA_World_Cup\" title=\"1994 FIFA World Cup\">1994 FIFA World Cup</a> <a href=\"https://wikipedia.org/wiki/1994_FIFA_World_Cup_qualification_(CAF)\" title=\"1994 FIFA World Cup qualification (CAF)\">qualifying match</a> against <a href=\"https://wikipedia.org/wiki/Senegal_national_football_team\" title=\"Senegal national football team\">Senegal</a>.", "links": [{"title": "Zambia national football team", "link": "https://wikipedia.org/wiki/Zambia_national_football_team"}, {"title": "1993 Zambia national football team plane crash", "link": "https://wikipedia.org/wiki/1993_Zambia_national_football_team_plane_crash"}, {"title": "Libreville", "link": "https://wikipedia.org/wiki/Libreville"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>kar"}, {"title": "1994 FIFA World Cup", "link": "https://wikipedia.org/wiki/1994_FIFA_World_Cup"}, {"title": "1994 FIFA World Cup qualification (CAF)", "link": "https://wikipedia.org/wiki/1994_FIFA_World_Cup_qualification_(CAF)"}, {"title": "Senegal national football team", "link": "https://wikipedia.org/wiki/Senegal_national_football_team"}]}, {"year": "1994", "text": "South African general election: The first democratic general election in South Africa, in which black citizens could vote. The Interim Constitution comes into force.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/South_African_general_election,_1994\" class=\"mw-redirect\" title=\"South African general election, 1994\">South African general election</a>: The first democratic <a href=\"https://wikipedia.org/wiki/General_election\" title=\"General election\">general election</a> in South Africa, in which black citizens could vote. The <a href=\"https://wikipedia.org/wiki/South_African_Constitution_of_1993\" class=\"mw-redirect\" title=\"South African Constitution of 1993\">Interim Constitution</a> comes into force.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_African_general_election,_1994\" class=\"mw-redirect\" title=\"South African general election, 1994\">South African general election</a>: The first democratic <a href=\"https://wikipedia.org/wiki/General_election\" title=\"General election\">general election</a> in South Africa, in which black citizens could vote. The <a href=\"https://wikipedia.org/wiki/South_African_Constitution_of_1993\" class=\"mw-redirect\" title=\"South African Constitution of 1993\">Interim Constitution</a> comes into force.", "links": [{"title": "South African general election, 1994", "link": "https://wikipedia.org/wiki/South_African_general_election,_1994"}, {"title": "General election", "link": "https://wikipedia.org/wiki/General_election"}, {"title": "South African Constitution of 1993", "link": "https://wikipedia.org/wiki/South_African_Constitution_of_1993"}]}, {"year": "2005", "text": "Airbus A380 aircraft has its maiden test flight.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Airbus_A380\" title=\"Airbus A380\">Airbus A380</a> aircraft has its maiden test flight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Airbus_A380\" title=\"Airbus A380\">Airbus A380</a> aircraft has its maiden test flight.", "links": [{"title": "Airbus A380", "link": "https://wikipedia.org/wiki/Airbus_A380"}]}, {"year": "2006", "text": "Construction begins on the Freedom Tower (later renamed One World Trade Center) in New York City.", "html": "2006 - Construction begins on <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">the Freedom Tower</a> (later renamed <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">One World Trade Center</a>) in New York City.", "no_year_html": "Construction begins on <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">the Freedom Tower</a> (later renamed <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">One World Trade Center</a>) in New York City.", "links": [{"title": "One World Trade Center", "link": "https://wikipedia.org/wiki/One_World_Trade_Center"}, {"title": "One World Trade Center", "link": "https://wikipedia.org/wiki/One_World_Trade_Center"}]}, {"year": "2007", "text": "Estonian authorities remove the Bronze Soldier, a Soviet Red Army war memorial in Tallinn, amid political controversy with Russia.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonian</a> authorities remove the <a href=\"https://wikipedia.org/wiki/Bronze_Soldier_of_Tallinn\" title=\"Bronze Soldier of Tallinn\">Bronze Soldier</a>, a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> <a href=\"https://wikipedia.org/wiki/War_memorial\" title=\"War memorial\">war memorial</a> in <a href=\"https://wikipedia.org/wiki/Tallinn\" title=\"Tallinn\">Tallinn</a>, amid political controversy with Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonian</a> authorities remove the <a href=\"https://wikipedia.org/wiki/Bronze_Soldier_of_Tallinn\" title=\"Bronze Soldier of Tallinn\">Bronze Soldier</a>, a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> <a href=\"https://wikipedia.org/wiki/War_memorial\" title=\"War memorial\">war memorial</a> in <a href=\"https://wikipedia.org/wiki/Tallinn\" title=\"Tallinn\">Tallinn</a>, amid political controversy with Russia.", "links": [{"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Bronze Soldier of Tallinn", "link": "https://wikipedia.org/wiki/Bronze_Soldier_of_Tallinn"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "War memorial", "link": "https://wikipedia.org/wiki/War_memorial"}, {"title": "Tallinn", "link": "https://wikipedia.org/wiki/Tallinn"}]}, {"year": "2007", "text": "Israeli archaeologists discover the tomb of <PERSON><PERSON> the Great south of Jerusalem.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> archaeologists discover the tomb of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\">Hero<PERSON> the Great</a> south of <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> archaeologists discover the tomb of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\">Hero<PERSON> the Great</a> south of <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Hero<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "2011", "text": "The 2011 Super Outbreak devastates parts of the Southeastern United States, especially the states of Alabama, Mississippi, Georgia, and Tennessee. Two hundred five tornadoes touched down on April 27 alone, killing more than 300 and injuring hundreds more.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/2011_Super_Outbreak\" title=\"2011 Super Outbreak\">2011 Super Outbreak</a> devastates parts of the <a href=\"https://wikipedia.org/wiki/Southeastern_United_States\" title=\"Southeastern United States\">Southeastern United States</a>, especially the states of <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a>, <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>, <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>, and <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>. Two hundred five tornadoes touched down on April 27 alone, killing more than 300 and injuring hundreds more.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2011_Super_Outbreak\" title=\"2011 Super Outbreak\">2011 Super Outbreak</a> devastates parts of the <a href=\"https://wikipedia.org/wiki/Southeastern_United_States\" title=\"Southeastern United States\">Southeastern United States</a>, especially the states of <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a>, <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>, <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>, and <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>. Two hundred five tornadoes touched down on April 27 alone, killing more than 300 and injuring hundreds more.", "links": [{"title": "2011 Super Outbreak", "link": "https://wikipedia.org/wiki/2011_Super_Outbreak"}, {"title": "Southeastern United States", "link": "https://wikipedia.org/wiki/Southeastern_United_States"}, {"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}]}, {"year": "2012", "text": "At least four explosions hit the Ukrainian city of Dnipropetrovsk with at least 27 people injured.", "html": "2012 - At least <a href=\"https://wikipedia.org/wiki/2012_Dnipropetrovsk_explosions\" title=\"2012 Dnipropetrovsk explosions\">four explosions</a> hit the <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukrainian</a> city of <a href=\"https://wikipedia.org/wiki/Dnipropetrovsk\" class=\"mw-redirect\" title=\"Dnipropetrovsk\">Dnipropetrovsk</a> with at least 27 people injured.", "no_year_html": "At least <a href=\"https://wikipedia.org/wiki/2012_Dnipropetrovsk_explosions\" title=\"2012 Dnipropetrovsk explosions\">four explosions</a> hit the <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukrainian</a> city of <a href=\"https://wikipedia.org/wiki/Dnipropetrovsk\" class=\"mw-redirect\" title=\"Dnipropetrovsk\">Dnipropetrovsk</a> with at least 27 people injured.", "links": [{"title": "2012 Dnipropetrovsk explosions", "link": "https://wikipedia.org/wiki/2012_Dnipropetrovsk_explosions"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Dnipropetrovsk", "link": "https://wikipedia.org/wiki/Dnipropetrovsk"}]}, {"year": "2018", "text": "The Panmunjom Declaration is signed between North and South Korea, officially declaring their intentions to end the Korean conflict.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/Panmunjom_Declaration\" title=\"Panmunjom Declaration\">Panmunjom Declaration</a> is signed between <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North</a> and <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>, officially declaring their intentions to end the <a href=\"https://wikipedia.org/wiki/Korean_conflict\" title=\"Korean conflict\">Korean conflict</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Panmunjom_Declaration\" title=\"Panmunjom Declaration\">Panmunjom Declaration</a> is signed between <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North</a> and <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>, officially declaring their intentions to end the <a href=\"https://wikipedia.org/wiki/Korean_conflict\" title=\"Korean conflict\">Korean conflict</a>.", "links": [{"title": "Panmunjom Declaration", "link": "https://wikipedia.org/wiki/Panmunjom_Declaration"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}, {"title": "Korean conflict", "link": "https://wikipedia.org/wiki/Korean_conflict"}]}, {"year": "2024", "text": "The worst day of the tornado outbreak sequence of April 25-28, 2024, with 42 tornadoes, including one confirmed EF4 tornado, and two confirmed EF3 tornadoes, which killed 4 people in total.", "html": "2024 - The worst day of the <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_sequence_of_April_25%E2%80%9328,_2024\" class=\"mw-redirect\" title=\"Tornado outbreak sequence of April 25-28, 2024\">tornado outbreak sequence of April 25-28, 2024</a>, with 42 tornadoes, including one confirmed <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_Scale\" class=\"mw-redirect\" title=\"Enhanced Fujita Scale\">EF4</a> tornado, and two confirmed EF3 tornadoes, which killed 4 people in total.", "no_year_html": "The worst day of the <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_sequence_of_April_25%E2%80%9328,_2024\" class=\"mw-redirect\" title=\"Tornado outbreak sequence of April 25-28, 2024\">tornado outbreak sequence of April 25-28, 2024</a>, with 42 tornadoes, including one confirmed <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_Scale\" class=\"mw-redirect\" title=\"Enhanced Fujita Scale\">EF4</a> tornado, and two confirmed EF3 tornadoes, which killed 4 people in total.", "links": [{"title": "Tornado outbreak sequence of April 25-28, 2024", "link": "https://wikipedia.org/wiki/Tornado_outbreak_sequence_of_April_25%E2%80%9328,_2024"}, {"title": "Enhanced Fujita Scale", "link": "https://wikipedia.org/wiki/Enhanced_Fujita_Scale"}]}], "Births": [{"year": "85 BC", "text": "<PERSON><PERSON>, Roman politician and general (d. 43 BC)", "html": "85 BC - 85 BC - <a href=\"https://wikipedia.org/wiki/Decimus_Junius_Brutus_<PERSON>us\" title=\"Decimus Junius Brutus <PERSON>us\">Decimus Junius <PERSON>rutus <PERSON></a>, Roman politician and general (d. 43 BC)", "no_year_html": "85 BC - <a href=\"https://wikipedia.org/wiki/Decimus_Junius_Brutus_<PERSON>us\" title=\"Decimus Junius Brutus Albinus\">Decimus <PERSON>ius <PERSON>tus <PERSON></a>, Roman politician and general (d. 43 BC)", "links": [{"title": "<PERSON><PERSON>us", "link": "https://wikipedia.org/wiki/Dec<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>us"}]}, {"year": "1468", "text": "<PERSON>, Primate of Poland (d. 1503)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Primate of Poland (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Primate of Poland (d. 1503)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1564", "text": "<PERSON>, 9th Earl of Northumberland (d. 1632)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Earl_of_Northumberland\" title=\"<PERSON>, 9th Earl of Northumberland\"><PERSON>, 9th Earl of Northumberland</a> (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Earl_of_Northumberland\" title=\"<PERSON>, 9th Earl of Northumberland\"><PERSON>, 9th Earl of Northumberland</a> (d. 1632)", "links": [{"title": "<PERSON>, 9th Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Earl_of_Northumberland"}]}, {"year": "1556", "text": "<PERSON>, French writer (d. 1626)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_B%C3%A9roalde_de_Verville\" title=\"<PERSON> Verville\"><PERSON></a>, French writer (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_B%C3%A9roalde_de_Verville\" title=\"<PERSON> Verville\"><PERSON></a>, French writer (d. 1626)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_B%C3%A9roalde_de_Verville"}]}, {"year": "1593", "text": "<PERSON><PERSON><PERSON>, Mughal empress buried at the Taj Mahal (d. 1631)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mughal empress buried at the Taj Mahal (d. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mughal empress buried at the Taj Mahal (d. 1631)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1650", "text": "<PERSON> of Hesse-Kassel, Queen Consort of Denmark (1670-1699) (d. 1714)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hesse-Kassel\" title=\"<PERSON> of Hesse-Kassel\"><PERSON> of Hesse-Kassel</a>, Queen Consort of Denmark (1670-1699) (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hesse-Kassel\" title=\"<PERSON> of Hesse-Kassel\"><PERSON> of Hesse-Kassel</a>, Queen Consort of Denmark (1670-1699) (d. 1714)", "links": [{"title": "<PERSON> of Hesse-Kassel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_of_Hesse-Kassel"}]}, {"year": "1654", "text": "<PERSON>, English deist and philosopher (d. 1693)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(deist)\" title=\"<PERSON> (deist)\"><PERSON></a>, English deist and philosopher (d. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(deist)\" title=\"<PERSON> (deist)\"><PERSON></a>, English deist and philosopher (d. 1693)", "links": [{"title": "<PERSON> (deist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(deist)"}]}, {"year": "1701", "text": "<PERSON> of Sardinia (d. 1773)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON> of Sardinia</a> (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON> of Sardinia</a> (d. 1773)", "links": [{"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia"}]}, {"year": "1718", "text": "<PERSON>, Irish-born American surveyor and lawyer (d. 1790)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Virginia_politician)\" title=\"<PERSON> (Virginia politician)\"><PERSON></a>, Irish-born American surveyor and lawyer (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Virginia_politician)\" title=\"<PERSON> (Virginia politician)\"><PERSON></a>, Irish-born American surveyor and lawyer (d. 1790)", "links": [{"title": "<PERSON> (Virginia politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Virginia_politician)"}]}, {"year": "1748", "text": "<PERSON><PERSON><PERSON>, Greek-French philosopher and scholar (d. 1833)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/Adamantios_Korais\" title=\"Adamantios Korais\"><PERSON><PERSON><PERSON></a>, Greek-French philosopher and scholar (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adamantios_Korais\" title=\"Adamantios Korais\"><PERSON><PERSON><PERSON></a>, Greek-French philosopher and scholar (d. 1833)", "links": [{"title": "Adamanti<PERSON>", "link": "https://wikipedia.org/wiki/Adamantios_Korais"}]}, {"year": "1755", "text": "<PERSON><PERSON><PERSON>, French mathematician and theorist (d. 1836)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (d. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1759", "text": "<PERSON>, English philosopher, historian, and novelist (d. 1797)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, historian, and novelist (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, historian, and novelist (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>craft"}]}, {"year": "1788", "text": "<PERSON>, English architect, archaeologist, and writer (d. 1863)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, archaeologist, and writer (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, archaeologist, and writer (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, American painter and inventor, co-invented the Morse code (d. 1872)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and inventor, co-invented the <a href=\"https://wikipedia.org/wiki/Morse_code\" title=\"Morse code\">Morse code</a> (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and inventor, co-invented the <a href=\"https://wikipedia.org/wiki/Morse_code\" title=\"Morse code\">Morse code</a> (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Morse code", "link": "https://wikipedia.org/wiki/Morse_code"}]}, {"year": "1812", "text": "<PERSON>, American lawyer and politician (d. 1886)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, German composer (d. 1883)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, English biologist, anthropologist, sociologist, and philosopher (d. 1903)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anthropologist, sociologist, and philosopher (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anthropologist, sociologist, and philosopher (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, American general and politician, 18th President of the United States (d. 1885)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1840", "text": "<PERSON>, English-French mountaineer, explorer, author, and illustrator (d. 1911)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French mountaineer, explorer, author, and illustrator (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French mountaineer, explorer, author, and illustrator (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, King of Bavaria (d. 1916)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Bavaria\" title=\"<PERSON>, King of Bavaria\"><PERSON>, King of Bavaria</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Bavaria\" title=\"<PERSON>, King of Bavaria\"><PERSON>, King of Bavaria</a> (d. 1916)", "links": [{"title": "<PERSON>, King of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Bavaria"}]}, {"year": "1850", "text": "<PERSON>, German general and politician (d. 1921)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and politician (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and politician (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, French playwright and critic (d. 1914)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AEtre\" title=\"<PERSON>\"><PERSON></a>, French playwright and critic (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AEtre\" title=\"<PERSON>\"><PERSON></a>, French playwright and critic (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Le<PERSON>%C3%AEtre"}]}, {"year": "1857", "text": "<PERSON>, Norwegian painter and illustrator (d. 1914)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American composer and music historian (d. 1948)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and music historian (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and music historian (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Arms_Fisher"}]}, {"year": "1866", "text": "<PERSON>, French polo player (d. 1916)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French polo player (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French polo player (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Irish-born, English cricketer (d. 1960)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born, English cricketer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born, English cricketer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, Estonian organist, composer, and conductor (d. 1958)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BCdig\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian organist, composer, and conductor (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BCdig\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian organist, composer, and conductor (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mihkel_L%C3%BCdig"}]}, {"year": "1882", "text": "<PERSON>, American author and poet (d. 1961)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>auset\" class=\"mw-redirect\" title=\"<PERSON> Fauset\"><PERSON></a>, American author and poet (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>auset\" class=\"mw-redirect\" title=\"<PERSON> Fauset\"><PERSON></a>, American author and poet (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>auset"}]}, {"year": "1887", "text": "<PERSON>, American golfer (d. 1926)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Warren Wood\"><PERSON></a>, American golfer (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Warren Wood\"><PERSON></a>, American golfer (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Canadian actress (d. 1917)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Florence_La_Badie\" title=\"Florence La Badie\"><PERSON></a>, Canadian actress (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_La_Badie\" title=\"Florence La Badie\"><PERSON></a>, Canadian actress (d. 1917)", "links": [{"title": "Florence La Badie", "link": "https://wikipedia.org/wiki/Florence_La_Badie"}]}, {"year": "1891", "text": "<PERSON>, Russian pianist, composer, and conductor (d. 1953)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian general (d. 1946)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Dra%C5%BEa_Mihailovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian general (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dra%C5%BEa_Mihailovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian general (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dra%C5%BEa_<PERSON><PERSON><PERSON>i%C4%87"}]}, {"year": "1893", "text": "<PERSON>, American baseball player, coach, and manager (d. 1939)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American painter and illustrator (d. 1975)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Russian pianist, composer, and conductor (d. 1995)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American baseball player, coach, and manager (d. 1963)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>sby\"><PERSON></a>, American baseball player, coach, and manager (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hornsby\"><PERSON></a>, American baseball player, coach, and manager (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hornsby"}]}, {"year": "1896", "text": "<PERSON>, New Zealand-Australian engineer (d. 1978)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, New Zealand-Australian engineer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, New Zealand-Australian engineer (d. 1978)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_(engineer)"}]}, {"year": "1896", "text": "<PERSON>, American chemist and inventor of nylon (d. 1937)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and inventor of nylon (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and inventor of nylon (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>others"}]}, {"year": "1898", "text": "<PERSON>, Italian-American author and illustrator (d. 1962)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American author and illustrator (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American author and illustrator (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American animator, producer, screenwriter, and actor (d. 1994)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, screenwriter, and actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, screenwriter, and actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Estonian politician and diplomat, Estonian Minister of Foreign Affairs in exile (d. 1989)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Foreign_Affairs_in_exile\" class=\"mw-redirect\" title=\"Estonian Minister of Foreign Affairs in exile\">Estonian Minister of Foreign Affairs in exile</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Foreign_Affairs_in_exile\" class=\"mw-redirect\" title=\"Estonian Minister of Foreign Affairs in exile\">Estonian Minister of Foreign Affairs in exile</a> (d. 1989)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>"}, {"title": "Estonian Minister of Foreign Affairs in exile", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_Foreign_Affairs_in_exile"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, Malian educator and activist (d. 1942)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Kouyat%C3%A9\" title=\"Tiemoko Garan Kouyaté\"><PERSON><PERSON><PERSON><PERSON></a>, Malian educator and activist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Kouyat%C3%A9\" title=\"Tiemoko Garan Kouyaté\"><PERSON><PERSON><PERSON><PERSON></a>, Malian educator and activist (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Ko<PERSON>at%C3%A9"}]}, {"year": "1904", "text": "<PERSON>-<PERSON>, Anglo-Irish poet and author (d. 1972)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish poet and author (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish poet and author (d. 1972)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Greek politician (d. 1973)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dis"}]}, {"year": "1905", "text": "<PERSON>, American javelin thrower and shot putter (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American javelin thrower and shot putter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American javelin thrower and shot putter (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Greek author and playwright (d. 1966)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and playwright (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and playwright (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>as"}]}, {"year": "1909", "text": "<PERSON>, Chinese businessman, resistance fighter of Force 136 and war hero of Singapore (d. 1944)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businessman, resistance fighter of <a href=\"https://wikipedia.org/wiki/Force_136\" title=\"Force 136\">Force 136</a> and war hero of <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businessman, resistance fighter of <a href=\"https://wikipedia.org/wiki/Force_136\" title=\"Force 136\">Force 136</a> and war hero of <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Force 136", "link": "https://wikipedia.org/wiki/Force_136"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "1910", "text": "<PERSON>, Chinese politician, 3rd President of the Republic of China (d. 1988)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1911", "text": "<PERSON>, German anthropologist and ethnologist (d. 2009)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist and ethnologist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist and ethnologist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Dutch sprinter and footballer (d. 1965)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sprinter and footballer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sprinter and footballer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, French author and politician (d. 2001)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and politician (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and politician (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Bus<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Indian actress, dancer, and choreographer (d. 2014)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress, dancer, and choreographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress, dancer, and choreographer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American physicist and author (d. 2004)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American mathematician, author, and academic (d. 2012)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, author, and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, author, and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, German long jumper and soldier (d. 1943)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German long jumper and soldier (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German long jumper and soldier (d. 1943)", "links": [{"title": "Luz Long", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Long"}]}, {"year": "1916", "text": "<PERSON>, Jr., American sergeant, lawyer, and judge (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American sergeant, lawyer, and judge (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American sergeant, lawyer, and judge (d. 2013)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1916", "text": "<PERSON><PERSON>, American baseball player and manager (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/En<PERSON>_Slaughter\" title=\"<PERSON><PERSON> Slaughter\"><PERSON><PERSON></a>, American baseball player and manager (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/En<PERSON>_Slaughter\" title=\"<PERSON><PERSON> Slaughter\"><PERSON><PERSON></a>, American baseball player and manager (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enos_Slaughter"}]}, {"year": "1917", "text": "<PERSON>, Estonian violinist, pianist, and conductor (d. 2001)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Roman_Matsov\" title=\"Roman Matsov\"><PERSON></a>, Estonian violinist, pianist, and conductor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Matsov\" title=\"Roman Matsov\"><PERSON></a>, Estonian violinist, pianist, and conductor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>v"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Swedish lawyer and jurist (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Sten_Rudholm\" title=\"Sten Rudholm\"><PERSON><PERSON></a>, Swedish lawyer and jurist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sten_Rudholm\" title=\"Sten Rudholm\"><PERSON><PERSON></a>, Swedish lawyer and jurist (d. 2008)", "links": [{"title": "Sten Rudholm", "link": "https://wikipedia.org/wiki/Sten_Rudholm"}]}, {"year": "1920", "text": "<PERSON>, Italian conductor (d. 1956)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Ukrainian mathematician and academic (d. 1997)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27skii\" class=\"mw-redirect\" title=\"<PERSON>'skii\"><PERSON>ski<PERSON></a>, Ukrainian mathematician and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27skii\" class=\"mw-redirect\" title=\"<PERSON>'skii\"><PERSON></a>, Ukrainian mathematician and academic (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27skii"}]}, {"year": "1920", "text": "<PERSON>, American colonel, lawyer, and politician (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(South_Carolina)\" class=\"mw-redirect\" title=\"<PERSON> (South Carolina)\"><PERSON></a>, American colonel, lawyer, and politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(South_Carolina)\" class=\"mw-redirect\" title=\"<PERSON> (South Carolina)\"><PERSON></a>, American colonel, lawyer, and politician (d. 2010)", "links": [{"title": "<PERSON> (South Carolina)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(South_Carolina)"}]}, {"year": "1920", "text": "<PERSON>, Scottish poet and translator (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and translator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and translator (d. 2010)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1921", "text": "<PERSON>, French actor, director, and screenwriter (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_<PERSON>h%C3%A9ry"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English nurse and pilot (d. 1988)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and pilot (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and pilot (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Seminole chief (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mae_Tiger_Jumper\" title=\"Betty Mae Tiger Jumper\"><PERSON> Jumper</a>, Seminole chief (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mae_Tiger_Jumper\" title=\"Betty Mae Tiger Jumper\"><PERSON>er</a>, Seminole chief (d. 2011)", "links": [{"title": "<PERSON> Jumper", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tiger_Jumper"}]}, {"year": "1924", "text": "<PERSON>, American lawyer and politician, 14th Attorney General of Utah (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Utah\" class=\"mw-redirect\" title=\"Attorney General of Utah\">Attorney General of Utah</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Utah\" class=\"mw-redirect\" title=\"Attorney General of Utah\">Attorney General of Utah</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Attorney General of Utah", "link": "https://wikipedia.org/wiki/Attorney_General_of_Utah"}]}, {"year": "1925", "text": "<PERSON>, English broadcaster (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English broadcaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English broadcaster (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American minister, activist, and author (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, activist, and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, activist, and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American lawyer and politician, 59th Secretary of State of New York (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 59th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_New_York\" title=\"Secretary of State of New York\">Secretary of State of New York</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 59th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_New_York\" title=\"Secretary of State of New York\">Secretary of State of New York</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of State of New York", "link": "https://wikipedia.org/wiki/Secretary_of_State_of_New_York"}]}, {"year": "1926", "text": "<PERSON>, English painter and educator (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and educator (d. 2014)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1927", "text": "<PERSON><PERSON>, African-American activist and author (d. 2006)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Core<PERSON> King\"><PERSON><PERSON></a>, African-American activist and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Core<PERSON> Scott King\"><PERSON><PERSON></a>, African-American activist and author (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Scott_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American soldier, lawyer, and politician (d. 2001)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Russian discus thrower and coach (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian discus thrower and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian discus thrower and coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Ukrainian violinist and educator (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian violinist and educator (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian violinist and educator (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, French actress (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Aim%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Aim%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anouk_Aim%C3%A9e"}]}, {"year": "1932", "text": "<PERSON><PERSON>, South African lawyer, politician, and diplomat, 8th South African Ambassador to the United States (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African lawyer, politician, and diplomat, 8th <a href=\"https://wikipedia.org/wiki/South_African_Ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"South African Ambassador to the United States\">South African Ambassador to the United States</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African lawyer, politician, and diplomat, 8th <a href=\"https://wikipedia.org/wiki/South_African_Ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"South African Ambassador to the United States\">South African Ambassador to the United States</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}, {"title": "South African Ambassador to the United States", "link": "https://wikipedia.org/wiki/South_African_Ambassador_to_the_United_States"}]}, {"year": "1932", "text": "<PERSON>, American disc jockey, radio celebrity, and voice actor; co-created American Top 40 (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American disc jockey, radio celebrity, and voice actor; co-created <i><a href=\"https://wikipedia.org/wiki/American_Top_40\" title=\"American Top 40\">American Top 40</a></i> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American disc jockey, radio celebrity, and voice actor; co-created <i><a href=\"https://wikipedia.org/wiki/American_Top_40\" title=\"American Top 40\">American Top 40</a></i> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Top 40", "link": "https://wikipedia.org/wiki/American_Top_40"}]}, {"year": "1932", "text": "<PERSON>, American football coach (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English motorcycle racer (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-American mathematician and philosopher (d. 1999)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-American mathematician and philosopher (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-American mathematician and philosopher (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Baron <PERSON>, English police officer and politician, Lord Lieutenant for Greater London (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON><PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English police officer and politician, Lord Lieutenant for Greater London (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English police officer and politician, Lord Lieutenant for Greater London (d. 2017)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Greek director, producer, and screenwriter (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American pole vaulter and coach (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American pole vaulter and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American pole vaulter and coach (d. 2024)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1936", "text": "<PERSON>, English singer and illustrator (d. 2016)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and illustrator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and illustrator (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actress (d. 1992)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Irish Anglican archbishop", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Anglican archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Anglican archbishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English biologist and academic (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American bowler and sportscaster (d. 2001)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bowler and sportscaster (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bowler and sportscaster (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian ice hockey player (d. 1986)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 1986)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1939", "text": "<PERSON>, English actress and comedian (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Polish cardinal", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish cardinal", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish preacher and theologian (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Fethullah_G%C3%BClen\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish preacher and theologian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fethullah_G%C3%BClen\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish preacher and theologian (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fethullah_G%C3%BClen"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Indian archaeologist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian archaeologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian archaeologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American football player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American drummer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Austrian race car driver and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English meteorologist and journalist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English meteorologist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English meteorologist and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "Cuba Gooding Sr., American singer (d. 2017)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Cuba_Gooding_Sr.\" title=\"Cuba Gooding Sr.\">Cuba Gooding Sr.</a>, American singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuba_Gooding_Sr.\" title=\"Cuba Gooding Sr.\">Cuba Gooding Sr.</a>, American singer (d. 2017)", "links": [{"title": "Cuba Gooding Sr.", "link": "https://wikipedia.org/wiki/Cuba_Gooding_Sr."}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English footballer and manager", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian journalist and television host", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American author and playwright (d. 2005)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, American author and playwright (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, American author and playwright (d. 2005)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, German footballer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON> <PERSON><PERSON>, African-American soldier, lawyer, and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, African-American soldier, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, African-American soldier, lawyer, and politician", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Hungarian-Australian politician, 37th Premier of New South Wales", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1947", "text": "<PERSON>, Welsh singer-songwriter and guitarist (d. 1975)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter and guitarist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter and guitarist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2003)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American soul singer-songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American security consultant and criminal", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"Frank <PERSON> Jr.\"><PERSON>.</a>, American security consultant and criminal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"Frank <PERSON> Jr.\"><PERSON>.</a>, American security consultant and criminal", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/Frank_Abagnale_Jr."}]}, {"year": "1948", "text": "<PERSON>, Austrian footballer, coach, and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Filipino politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American television writer and producer (d. 2025)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer and producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer and producer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American guitarist and songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American lawyer and talk show host", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American basketball player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Finnish race car driver and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, French-American actress and model", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Fijian commander and politician, 8th Prime Minister of Fiji", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Fijian commander and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Fiji\" title=\"Prime Minister of Fiji\">Prime Minister of Fiji</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Fijian commander and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Fiji\" title=\"Prime Minister of Fiji\">Prime Minister of Fiji</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Fiji", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Fiji"}]}, {"year": "1954", "text": "<PERSON>, American football player, coach, and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian singer, actor, and lawyer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer, actor, and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer, actor, and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American engineer and businessman", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2006)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2006)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Austrian politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American baseball player and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Scottish-American singer-songwriter, actress, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American singer-songwriter, actress, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American singer-songwriter, actress, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and activist, founded Conservapedia", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist, founded <a href=\"https://wikipedia.org/wiki/Conservapedia\" title=\"Conservapedia\">Conservapedia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist, founded <a href=\"https://wikipedia.org/wiki/Conservapedia\" title=\"Conservapedia\">Conservapedia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Conservapedia", "link": "https://wikipedia.org/wiki/Conservapedia"}]}, {"year": "1962", "text": "<PERSON>, Argentinian footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_<PERSON>mizzo"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Finnish javelin thrower and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Seppo_R%C3%A4ty\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish javelin thrower and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seppo_R%C3%A4ty\" title=\"<PERSON><PERSON> Rät<PERSON>\"><PERSON><PERSON></a>, Finnish javelin thrower and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Seppo_R%C3%A4ty"}]}, {"year": "1962", "text": "<PERSON><PERSON>, South Korean director and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON>-<PERSON>o\" title=\"<PERSON><PERSON>-soo\"><PERSON><PERSON></a>, South Korean director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-so<PERSON>\"><PERSON><PERSON></a>, South Korean director and screenwriter", "links": [{"title": "<PERSON><PERSON>o", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON>-<PERSON>o"}]}, {"year": "1962", "text": "<PERSON>, English soldier and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Welsh screenwriter and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chancellor\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chancellor\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian cricketer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Japanese illustrator", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, King of the Netherlands", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_of_the_Netherlands\" title=\"<PERSON><PERSON><PERSON> of the Netherlands\"><PERSON><PERSON></a>, King of the Netherlands", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands\" title=\"<PERSON><PERSON> of the Netherlands\"><PERSON><PERSON></a>, King of the Netherlands", "links": [{"title": "<PERSON><PERSON><PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_of_the_Netherlands"}]}, {"year": "1967", "text": "<PERSON>, Scottish saxophonist, composer, and educator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saxophonist)\" title=\"<PERSON> (saxophonist)\"><PERSON></a>, Scottish saxophonist, composer, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(saxophonist)\" title=\"<PERSON> (saxophonist)\"><PERSON></a>, Scottish saxophonist, composer, and educator", "links": [{"title": "<PERSON> (saxophonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saxophonist)"}]}, {"year": "1967", "text": "<PERSON>, Scottish-New Zealand actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American football player and journalist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American journalist and author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Dana_Milbank\" title=\"<PERSON> Milbank\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dana_Milbank\" title=\"<PERSON> Milbank\"><PERSON></a>, American journalist and author", "links": [{"title": "Dana Milbank", "link": "https://wikipedia.org/wiki/Dana_Milbank"}]}, {"year": "1969", "text": "<PERSON>, African-American lawyer and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, English ballerina", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English ballerina", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English ballerina", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English photographer and author", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer and author", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>(photographer)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Swedish bass player and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON>_D%27Angelo\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>Angelo\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Swedish bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON>_D%27Angelo\" title=\"<PERSON><PERSON><PERSON> D'Angelo\"><PERSON><PERSON><PERSON> <PERSON></a>, Swedish bass player and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sharlee_D%27Angelo"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian tennis player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Dominican baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese ski jumper", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese ski jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Scottish singer-songwriter and cellist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish singer-songwriter and cellist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish singer-songwriter and cellist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Uruguayan footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Ukrainian actor and wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian actor and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian actor and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Bammer\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Bammer\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Ecuadorian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON>, American actress and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1984", "text": "<PERSON>, American musician, singer, and songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ump"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Meselech_Melkamu\" title=\"Meselech Melkamu\">Meselech <PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meselech_Melkamu\" title=\"Meselech Melkamu\"><PERSON>sel<PERSON></a>, Ethiopian runner", "links": [{"title": "Meselech Melkamu", "link": "https://wikipedia.org/wiki/Meselech_Melkamu"}]}, {"year": "1986", "text": "<PERSON>, English actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Russian tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a\" title=\"<PERSON><PERSON>fin<PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a\" title=\"<PERSON><PERSON>fin<PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1987", "text": "<PERSON>, Chinese singer and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON></a>, Chinese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON></a>, Chinese singer and actress", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American singer and rapper", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American race car driver", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Austin Dillon\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Austin Dillon\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Dillon"}]}, {"year": "1991", "text": "<PERSON>, Swiss skier", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Gut\"><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Gut\"><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Argentine_footballer)\" title=\"<PERSON><PERSON><PERSON> (Argentine footballer)\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Argentine_footballer)\" title=\"<PERSON><PERSON><PERSON> (Argentine footballer)\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (Argentine footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Argentine_footballer)"}]}, {"year": "1999", "text": "<PERSON>, Australian rugby league player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American football player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "630", "text": "<PERSON><PERSON><PERSON><PERSON> of Persia (b. 621)", "html": "630 - <a href=\"https://wikipedia.org/wiki/Ardashir_III\" title=\"Ardashir III\">Ardashir III</a> of Persia (b. 621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ardashir_III\" title=\"Ardashir III\">Ardashir III</a> of Persia (b. 621)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arda<PERSON>r_III"}]}, {"year": "1160", "text": "<PERSON>, Count of Bregenz (b. 1081)", "html": "1160 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Bregenz\" title=\"<PERSON>, Count of Bregenz\"><PERSON>, Count of Bregenz</a> (b. 1081)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Bregenz\" title=\"<PERSON>, Count of Bregenz\"><PERSON>, Count of Bregenz</a> (b. 1081)", "links": [{"title": "<PERSON>, Count of Bregenz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_Bregen<PERSON>"}]}, {"year": "1272", "text": "<PERSON><PERSON>, Italian saint (b. 1212)", "html": "1272 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian saint (b. 1212)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian saint (b. 1212)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zita"}]}, {"year": "1321", "text": "<PERSON><PERSON><PERSON>, Italian cardinal statesman (b. c. 1250)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal statesman (b. c. 1250)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal statesman (b. c. 1250)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON><PERSON>"}]}, {"year": "1353", "text": "<PERSON><PERSON><PERSON> of Moscow, Grand Prince of Moscow and Vladimir", "html": "1353 - <a href=\"https://wikipedia.org/wiki/Simeon_of_Moscow\" title=\"Simeon of Moscow\"><PERSON><PERSON><PERSON> of Moscow</a>, Grand Prince of Moscow and Vladimir", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simeon_of_Moscow\" title=\"Simeon of Moscow\"><PERSON><PERSON><PERSON> of Moscow</a>, Grand Prince of Moscow and Vladimir", "links": [{"title": "Simeon of Moscow", "link": "https://wikipedia.org/wiki/Simeon_of_Moscow"}]}, {"year": "1403", "text": "<PERSON> of Bosnia, Countess of Helfenstein (b. 1335)", "html": "1403 - <a href=\"https://wikipedia.org/wiki/Maria_of_Bosnia\" title=\"<PERSON> of Bosnia\"><PERSON> of Bosnia</a>, Countess of Helfenstein (b. 1335)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Bosnia\" title=\"<PERSON> of Bosnia\"><PERSON> of Bosnia</a>, Countess of Helfenstein (b. 1335)", "links": [{"title": "Maria of Bosnia", "link": "https://wikipedia.org/wiki/Maria_of_Bosnia"}]}, {"year": "1404", "text": "<PERSON>, Duke of Burgundy (b. 1342)", "html": "1404 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1342)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1342)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1463", "text": "<PERSON><PERSON><PERSON> of Kiev (b. 1385)", "html": "1463 - <a href=\"https://wikipedia.org/wiki/Isidore_of_Kiev\" title=\"<PERSON><PERSON><PERSON> of Kiev\"><PERSON><PERSON><PERSON> of Kiev</a> (b. 1385)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isidore_of_Kiev\" title=\"Is<PERSON><PERSON> of Kiev\"><PERSON><PERSON><PERSON> of Kiev</a> (b. 1385)", "links": [{"title": "<PERSON><PERSON><PERSON> of Kiev", "link": "https://wikipedia.org/wiki/Isidore_of_Kiev"}]}, {"year": "1521", "text": "<PERSON>, Portuguese sailor and explorer (b. 1480)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese sailor and explorer (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese sailor and explorer (b. 1480)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON><PERSON>, Japanese general (b. 1538)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/Maeda_Toshiie\" title=\"Maeda Toshiie\"><PERSON><PERSON></a>, Japanese general (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maeda_Toshiie\" title=\"Maeda Toshiie\"><PERSON><PERSON></a>, Japanese general (b. 1538)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ie"}]}, {"year": "1605", "text": "<PERSON> (b. 1535)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a> (b. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Leo_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, 3rd Baron <PERSON>, Governor of Lecale (b. 1560)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Governor of Lecale (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Governor of Lecale (b. 1560)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1613", "text": "<PERSON>, Scottish priest and missionary (b. 1532)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(missionary)\" class=\"mw-redirect\" title=\"<PERSON> (missionary)\"><PERSON></a>, Scottish priest and missionary (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(missionary)\" class=\"mw-redirect\" title=\"<PERSON> (missionary)\"><PERSON></a>, Scottish priest and missionary (b. 1532)", "links": [{"title": "<PERSON> (missionary)", "link": "https://wikipedia.org/wiki/<PERSON>_(missionary)"}]}, {"year": "1656", "text": "<PERSON>, Dutch painter and illustrator (b. 1596)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and illustrator (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and illustrator (b. 1596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, Elector of Saxony (b. 1668)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" class=\"mw-redirect\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" class=\"mw-redirect\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1668)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1695", "text": "<PERSON>, English politician, Secretary of State for the Northern Department (b. 1640)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (b. 1640)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Secretary of State for the Northern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department"}]}, {"year": "1702", "text": "<PERSON>, French admiral (b. 1651)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (b. 1651)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, 1st Earl <PERSON>, English politician, Lord <PERSON>ard of the Household (b. 1710)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON> of the Household</a> (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON> of the Household</a> (b. 1710)", "links": [{"title": "<PERSON>, 1st Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>"}, {"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON>, American general and explorer (b. 1779)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Zebulon_Pike\" title=\"Zebulon Pike\"><PERSON><PERSON><PERSON> Pike</a>, American general and explorer (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zebulon_Pike\" title=\"Zebulon Pike\"><PERSON>eb<PERSON> Pike</a>, American general and explorer (b. 1779)", "links": [{"title": "Zebulon Pike", "link": "https://wikipedia.org/wiki/Zebulon_Pike"}]}, {"year": "1873", "text": "<PERSON>, English actor and manager (b. 1793)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and manager (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and manager (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American poet and philosopher (b. 1803)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and philosopher (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and philosopher (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Irish-born New Zealand journalist and politician, 14th Prime Minister of New Zealand (b. 1839)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born New Zealand journalist and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born New Zealand journalist and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1896", "text": "<PERSON>, English-Australian businessman and politician, 7th Premier of New South Wales (b. 1815)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henry_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1915", "text": "<PERSON>, Canadian businessman (b. 1838)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Russian pianist and composer (b. 1872)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American poet (b. 1899)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English mathematician and academic (b. 1857)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Italian sociologist, linguist, and politician (b. 1891)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sociologist, linguist, and politician (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sociologist, linguist, and politician (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Czech mathematician and philosopher (b. 1859)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech mathematician and philosopher (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech mathematician and philosopher (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American druggist and businessman (b. 1873)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American druggist and businessman (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American druggist and businessman (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nce"}]}, {"year": "1952", "text": "<PERSON>, Italian mathematician and statistician (b. 1865)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and statistician (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and statistician (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American director, producer, and screenwriter (b. 1893)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON> <PERSON><PERSON>, Bangladeshi-Pakistani lawyer and politician, Pakistani Minister of the Interior (b. 1873)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Fazlul_Huq\" title=\"<PERSON><PERSON> <PERSON>. Fazlul Huq\"><PERSON><PERSON> <PERSON><PERSON></a>, Bangladeshi-Pakistani lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Pakistan)\" title=\"Ministry of Interior (Pakistan)\">Pakistani Minister of the Interior</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>azlul_Hu<PERSON>\" title=\"<PERSON><PERSON> <PERSON>. <PERSON>lul <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Bangladeshi-Pakistani lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Pakistan)\" title=\"Ministry of Interior (Pakistan)\">Pakistani Minister of the Interior</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Interior (Pakistan)", "link": "https://wikipedia.org/wiki/Ministry_of_Interior_(Pakistan)"}]}, {"year": "1965", "text": "<PERSON>, American journalist (b. 1908)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, New Zealand farmer, founded the Eastwoodhill Arboretum (b. 1884)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand farmer, founded the <a href=\"https://wikipedia.org/wiki/Eastwoodhill_Arboretum\" title=\"Eastwoodhill Arboretum\">Eastwoodhill Arboretum</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand farmer, founded the <a href=\"https://wikipedia.org/wiki/Eastwoodhill_Arboretum\" title=\"Eastwoodhill Arboretum\">Eastwoodhill Arboretum</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Eastwoodhill Arboretum", "link": "https://wikipedia.org/wiki/Eastwoodhill_Arboretum"}]}, {"year": "1969", "text": "<PERSON>, Bolivian soldier, pilot, and politician, 55th President of Bolivia (b. 1919)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Barr<PERSON>os\" title=\"<PERSON>\"><PERSON></a>, Bolivian soldier, pilot, and politician, 55th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian soldier, pilot, and politician, 55th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Barrientos"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1970", "text": "<PERSON>, Irish rebel and actor (b. 1896)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rebel and actor (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rebel and actor (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Ghanaian politician, 1st President of Ghana (b. 1909)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President of Ghana</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President of Ghana</a> (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wa<PERSON>_<PERSON>ah"}, {"title": "President of Ghana", "link": "https://wikipedia.org/wiki/President_of_Ghana"}]}, {"year": "1973", "text": "<PERSON>, Argentinian race car driver and polo player (b. 1914)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver and polo player (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver and polo player (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor and screenwriter (b. 1915)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (b. 1915)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1988", "text": "<PERSON>, American hunter and author (b. 1902)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fred Bear\"><PERSON></a>, American hunter and author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fred Bear\"><PERSON></a>, American hunter and author (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Japanese businessman, founded Panasonic (b. 1894)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Panasonic\" title=\"Panasonic\">Panasonic</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Panasonic\" title=\"Panasonic\">Panasonic</a> (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Panasonic", "link": "https://wikipedia.org/wiki/Panasonic"}]}, {"year": "1992", "text": "<PERSON>, French organist and composer (b. 1908)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American physicist and astronomer (b. 1927)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Neill"}]}, {"year": "1995", "text": "<PERSON>, Canadian-American actress (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Dutch author, poet, and playwright (b. 1921)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, poet, and playwright (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, poet, and playwright (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American diplomat, 10th Director of Central Intelligence (b. 1920)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, 10th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, 10th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "1996", "text": "<PERSON>, French director and screenwriter (b. 1911)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian journalist and politician (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Peruvian-American anthropologist and author (b. 1925)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-American anthropologist and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-American anthropologist and author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, French journalist and author (b. 1907)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American runner and soldier (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ross\" title=\"Browning Ross\"><PERSON></a>, American runner and soldier (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ross\" title=\"Browning Ross\"><PERSON></a>, American runner and soldier (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American trumpet player and bandleader (b. 1922)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hirt"}]}, {"year": "1999", "text": "<PERSON>, Canadian historian, author, and academic (b. 1923)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian, author, and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian, author, and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English cricketer (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American author (b. 1947)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American inventor and businesswoman, created the Barbie doll (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businesswoman, created the <a href=\"https://wikipedia.org/wiki/Barbie\" title=\"Barbie\">Barbie doll</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businesswoman, created the <a href=\"https://wikipedia.org/wiki/Barbie\" title=\"Barbie\">Barbie doll</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Barbie", "link": "https://wikipedia.org/wiki/Barbie"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player (b. 1909)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Horner"}]}, {"year": "2006", "text": "<PERSON>, American author (b. 1944)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Russian cellist and conductor (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian cellist and conductor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian cellist and conductor (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American dancer and choreographer (b. 1914)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, South Korean model and actress (b. 1983)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Woo_<PERSON><PERSON>-yeon\" title=\"Woo <PERSON>-yeon\"><PERSON><PERSON>-yeon</a>, South Korean model and actress (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woo_<PERSON><PERSON>-yeon\" title=\"Woo <PERSON>-yeon\"><PERSON><PERSON>-yeon</a>, South Korean model and actress (b. 1983)", "links": [{"title": "<PERSON><PERSON>yeon", "link": "https://wikipedia.org/wiki/Woo_<PERSON><PERSON>-yeon"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON> (actor), Indian Actor, Film Director & Producer (b. 1939)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON> (actor)</a>, Indian Actor, Film Director &amp; Producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON> (actor)</a>, Indian Actor, Film Director &amp; Producer (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "2011", "text": "<PERSON>, American actress and singer (b. 1935)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American soldier and politician (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Boatwright\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Boatwright\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>wright"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Argentinian screenwriter (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/A%C3%ADda_Bortnik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A%C3%ADda_Bortnik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian screenwriter (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A%C3%ADda_Bortnik"}]}, {"year": "2013", "text": "<PERSON>, Scottish archaeologist (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish archaeologist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lorraine_<PERSON>land\" title=\"<PERSON>\"><PERSON></a>, Scottish archaeologist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lorraine_Copeland"}]}, {"year": "2013", "text": "<PERSON>, Spanish footballer (b. 1969)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Antonio_D%C3%ADaz_Jurado\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonio_D%C3%ADaz_Jurado\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_D%C3%ADaz_Jurado"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch journalist and author (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch journalist and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch journalist and author (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Chinese bishop (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese bishop (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese bishop (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Kenyan lawyer and politician, Kenyan Minister of Justice (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Kenya)\" title=\"Minister of Justice (Kenya)\">Kenyan Minister of Justice</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Kenya)\" title=\"Minister of Justice (Kenya)\">Kenyan Minister of Justice</a> (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Justice (Kenya)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Kenya)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Israeli lawyer (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli lawyer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON>rno<PERSON>\"><PERSON><PERSON></a>, Israeli lawyer (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian footballer, coach, and manager (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Vujadin_Bo%C5%A1kov\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer, coach, and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vujadin_Bo%C5%A1kov\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer, coach, and manager (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vujadin_Bo%C5%A1kov"}]}, {"year": "2014", "text": "<PERSON>, American football player and coach (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Australian race car driver and manager (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and manager (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and manager (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American boxer (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American football player, wrestler, and trainer (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>erne Gagne\"><PERSON><PERSON></a>, American football player, wrestler, and trainer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gagne\"><PERSON><PERSON></a>, American football player, wrestler, and trainer (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON>_<PERSON>ne"}]}, {"year": "2015", "text": "<PERSON>, American biologist, biophysicist, and academic (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, biophysicist, and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, biophysicist, and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Indian actor, producer and politician (b. 1946)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, producer and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, producer and politician (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>na"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler (b. 1938)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>matsu\" title=\"<PERSON><PERSON><PERSON> Shinmatsu\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>su\" title=\"<PERSON><PERSON><PERSON> Shinmatsu\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Indian writer (b. 1934)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian writer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian writer (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Chinese politician  (b. 1963)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American politician and actor (b. 1944)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and actor (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON> <PERSON><PERSON>, British author (b. 1952)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C<PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British author (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C. J<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British author (b. 1952)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>som"}]}]}}