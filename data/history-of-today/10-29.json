{"date": "October 29", "url": "https://wikipedia.org/wiki/October_29", "data": {"Events": [{"year": "312", "text": "<PERSON> the Great enters Rome after his victory at the Battle of the Milvian Bridge, stages a grand adventus in the city, and is met with popular jubilation. <PERSON><PERSON><PERSON>' body is fished out of the Tiber and beheaded.", "html": "312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> enters Rome after his victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Milvian_Bridge\" title=\"Battle of the Milvian Bridge\">Battle of the Milvian Bridge</a>, stages a grand <i><a href=\"https://wikipedia.org/wiki/Adventus_(ceremony)\" title=\"Adventus (ceremony)\">adventus</a></i> in the city, and is met with popular jubilation. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>' body is fished out of the <a href=\"https://wikipedia.org/wiki/Tiber\" title=\"Tiber\">Tiber</a> and beheaded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> enters Rome after his victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Milvian_Bridge\" title=\"Battle of the Milvian Bridge\">Battle of the Milvian Bridge</a>, stages a grand <i><a href=\"https://wikipedia.org/wiki/Adventus_(ceremony)\" title=\"Adventus (ceremony)\">adventus</a></i> in the city, and is met with popular jubilation. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>' body is fished out of the <a href=\"https://wikipedia.org/wiki/Tiber\" title=\"Tiber\">Tiber</a> and beheaded.", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Battle of the Milvian Bridge", "link": "https://wikipedia.org/wiki/Battle_of_the_Milvian_Bridge"}, {"title": "<PERSON><PERSON><PERSON> (ceremony)", "link": "https://wikipedia.org/wiki/Adventus_(ceremony)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ent<PERSON>"}, {"title": "Tiber", "link": "https://wikipedia.org/wiki/Tiber"}]}, {"year": "437", "text": "<PERSON><PERSON><PERSON> <PERSON>, Western Roman Emperor, marries <PERSON><PERSON><PERSON>, daughter of his cousin <PERSON><PERSON><PERSON>, Eastern Roman Emperor in Constantinople unifying the two branches of the House of <PERSON><PERSON>ius.", "html": "437 - <a href=\"https://wikipedia.org/wiki/Valentinian_III\" title=\"Valentinian III\">Valentinian III</a>, <a href=\"https://wikipedia.org/wiki/Western_Roman_Emperor\" class=\"mw-redirect\" title=\"Western Roman Emperor\">Western Roman Emperor</a>, marries <a href=\"https://wikipedia.org/wiki/Licinia_Eudoxia\" title=\"Licinia Eudoxia\"><PERSON><PERSON><PERSON></a>, daughter of his cousin <a href=\"https://wikipedia.org/wiki/Theodosius_II\" title=\"Theodosius II\"><PERSON><PERSON><PERSON> II</a>, <a href=\"https://wikipedia.org/wiki/Eastern_Roman_Emperor\" class=\"mw-redirect\" title=\"Eastern Roman Emperor\">Eastern Roman Emperor</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> unifying the two branches of the <a href=\"https://wikipedia.org/wiki/House_of_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"House of Theodosius\">House of Theodosius</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentinian_III\" title=\"Valentinian III\">Valentinian III</a>, <a href=\"https://wikipedia.org/wiki/Western_Roman_Emperor\" class=\"mw-redirect\" title=\"Western Roman Emperor\">Western Roman Emperor</a>, marries <a href=\"https://wikipedia.org/wiki/Licinia_Eudoxia\" title=\"Licinia Eudoxia\"><PERSON><PERSON><PERSON> E<PERSON>xia</a>, daughter of his cousin <a href=\"https://wikipedia.org/wiki/Theodosius_II\" title=\"Theodosius II\"><PERSON><PERSON><PERSON> II</a>, <a href=\"https://wikipedia.org/wiki/Eastern_Roman_Emperor\" class=\"mw-redirect\" title=\"Eastern Roman Emperor\">Eastern Roman Emperor</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> unifying the two branches of the <a href=\"https://wikipedia.org/wiki/House_of_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"House of Theodosius\">House of Theodosius</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valentinian_III"}, {"title": "Western Roman Emperor", "link": "https://wikipedia.org/wiki/Western_Roman_Emperor"}, {"title": "Licinia Eudoxia", "link": "https://wikipedia.org/wiki/Licinia_Eudoxia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theodosius_II"}, {"title": "Eastern Roman Emperor", "link": "https://wikipedia.org/wiki/Eastern_Roman_Emperor"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "House of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1390", "text": "First trial for witchcraft in Paris leading to the death of three people.", "html": "1390 - First trial for <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> in Paris leading to the death of three people.", "no_year_html": "First trial for <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> in Paris leading to the death of three people.", "links": [{"title": "Witchcraft", "link": "https://wikipedia.org/wiki/Witchcraft"}]}, {"year": "1467", "text": "Battle of Brustem: <PERSON> the Bold defeats Prince-Bishopric of Liège.", "html": "1467 - <a href=\"https://wikipedia.org/wiki/Battle_of_Brustem\" title=\"Battle of Brustem\">Battle of Brustem</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a> defeats <a href=\"https://wikipedia.org/wiki/Prince-Bishopric_of_Li%C3%A8ge\" title=\"Prince-Bishopric of Liège\">Prince-Bishopric of Liège</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Brustem\" title=\"Battle of Brustem\">Battle of Brustem</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a> defeats <a href=\"https://wikipedia.org/wiki/Prince-Bishopric_of_Li%C3%A8ge\" title=\"Prince-Bishopric of Liège\">Prince-Bishopric of Liège</a>.", "links": [{"title": "Battle of Brustem", "link": "https://wikipedia.org/wiki/Battle_of_Brustem"}, {"title": "<PERSON> the Bold", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prince-Bishopric of Liège", "link": "https://wikipedia.org/wiki/Prince-Bishopric_of_Li%C3%A8ge"}]}, {"year": "1611", "text": "Russian homage to the King of Poland, <PERSON><PERSON><PERSON>.", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tribute\" title=\"Shuysky Tribute\">Russian homage</a> to the King of Poland, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_Vasa\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON> III Vasa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tribute\" title=\"Shuysky Tribute\">Russian homage</a> to the King of Poland, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_Vasa\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON> III Vasa</a>.", "links": [{"title": "Shuysky Tribute", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "The London Pageant of 1621 celebrates the inauguration of <PERSON> (Lord Mayor).", "html": "1621 - <a href=\"https://wikipedia.org/wiki/The_London_Pageant_of_1621\" title=\"The London Pageant of 1621\">The London Pageant of 1621</a> celebrates the inauguration of <a href=\"https://wikipedia.org/wiki/<PERSON>(Lord_Mayor)\" title=\"<PERSON> (Lord Mayor)\"><PERSON> (Lord Mayor)</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_London_Pageant_of_1621\" title=\"The London Pageant of 1621\">The London Pageant of 1621</a> celebrates the inauguration of <a href=\"https://wikipedia.org/wiki/<PERSON>_(Lord_Mayor)\" title=\"<PERSON> (Lord Mayor)\"><PERSON> (Lord Mayor)</a>.", "links": [{"title": "The London Pageant of 1621", "link": "https://wikipedia.org/wiki/The_London_Pageant_of_1621"}, {"title": "<PERSON> (Lord Mayor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Mayor)"}]}, {"year": "1658", "text": "Second Northern War: Naval forces of the Dutch Republic defeat the Swedes in the Battle of the Sound.", "html": "1658 - <a href=\"https://wikipedia.org/wiki/Second_Northern_War\" class=\"mw-redirect\" title=\"Second Northern War\">Second Northern War</a>: Naval forces of the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a> defeat the Swedes in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Sound\" title=\"Battle of the Sound\">Battle of the Sound</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Northern_War\" class=\"mw-redirect\" title=\"Second Northern War\">Second Northern War</a>: Naval forces of the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a> defeat the Swedes in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Sound\" title=\"Battle of the Sound\">Battle of the Sound</a>.", "links": [{"title": "Second Northern War", "link": "https://wikipedia.org/wiki/Second_Northern_War"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}, {"title": "Battle of the Sound", "link": "https://wikipedia.org/wiki/Battle_of_the_Sound"}]}, {"year": "1665", "text": "Portuguese forces defeat the Kingdom of Kongo and decapitate King <PERSON><PERSON><PERSON><PERSON> of Kongo, also known as N<PERSON>ta a Nkanga.", "html": "1665 - Portuguese forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ambuila\" class=\"mw-redirect\" title=\"Battle of Ambuila\">defeat</a> the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kongo\" title=\"Kingdom of Kongo\">Kingdom of Kongo</a> and decapitate King <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_I_of_Kongo\" title=\"António I of Kongo\">António I of Kongo</a>, also known as <PERSON><PERSON>ta a <PERSON>.", "no_year_html": "Portuguese forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ambuila\" class=\"mw-redirect\" title=\"Battle of Ambuila\">defeat</a> the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kongo\" title=\"Kingdom of Kongo\">Kingdom of Kongo</a> and decapitate King <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_I_of_Kongo\" title=\"António I of Kongo\">António I of Kongo</a>, also known as <PERSON><PERSON><PERSON> a <PERSON>.", "links": [{"title": "Battle of Ambuila", "link": "https://wikipedia.org/wiki/Battle_of_Ambuila"}, {"title": "Kingdom of Kongo", "link": "https://wikipedia.org/wiki/Kingdom_of_Kongo"}, {"title": "António I of Kongo", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_I_of_Kongo"}]}, {"year": "1675", "text": "<PERSON><PERSON><PERSON><PERSON> makes the first use of the long s (∫) as a symbol of the integral in calculus.", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Leibniz</a> makes the first use of the <a href=\"https://wikipedia.org/wiki/Long_s\" title=\"Long s\">long s</a> (∫) as a symbol of the <a href=\"https://wikipedia.org/wiki/Integral\" title=\"Integral\">integral</a> in <a href=\"https://wikipedia.org/wiki/Calculus\" title=\"Calculus\">calculus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Leibniz</a> makes the first use of the <a href=\"https://wikipedia.org/wiki/Long_s\" title=\"Long s\">long s</a> (∫) as a symbol of the <a href=\"https://wikipedia.org/wiki/Integral\" title=\"Integral\">integral</a> in <a href=\"https://wikipedia.org/wiki/Calculus\" title=\"Calculus\">calculus</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Long s", "link": "https://wikipedia.org/wiki/Long_s"}, {"title": "Integral", "link": "https://wikipedia.org/wiki/Integral"}, {"title": "Calculus", "link": "https://wikipedia.org/wiki/Calculus"}]}, {"year": "1792", "text": "Mount Hood (Oregon) is named after <PERSON>, 1st Viscount <PERSON> by Lt. <PERSON> who sighted the mountain near the mouth of the Willamette River.", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Mount_Hood\" title=\"Mount Hood\">Mount Hood</a> (<a href=\"https://wikipedia.org/wiki/Oregon\" title=\"Oregon\">Oregon</a>) is named after <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a> by Lt. <PERSON> who sighted the mountain near the mouth of the <a href=\"https://wikipedia.org/wiki/Willamette_River\" title=\"Willamette River\">Willamette River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mount_Hood\" title=\"Mount Hood\">Mount Hood</a> (<a href=\"https://wikipedia.org/wiki/Oregon\" title=\"Oregon\">Oregon</a>) is named after <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a> by Lt. <PERSON> who sighted the mountain near the mouth of the <a href=\"https://wikipedia.org/wiki/Willamette_River\" title=\"Willamette River\">Willamette River</a>.", "links": [{"title": "Mount Hood", "link": "https://wikipedia.org/wiki/Mount_Hood"}, {"title": "Oregon", "link": "https://wikipedia.org/wiki/Oregon"}, {"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Willamette River", "link": "https://wikipedia.org/wiki/Willamette_River"}]}, {"year": "1863", "text": "Eighteen countries meet in Geneva and agree to form the International Red Cross.", "html": "1863 - Eighteen countries meet in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a> and agree to form the <a href=\"https://wikipedia.org/wiki/International_Committee_of_the_Red_Cross\" title=\"International Committee of the Red Cross\">International Red Cross</a>.", "no_year_html": "Eighteen countries meet in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a> and agree to form the <a href=\"https://wikipedia.org/wiki/International_Committee_of_the_Red_Cross\" title=\"International Committee of the Red Cross\">International Red Cross</a>.", "links": [{"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "International Committee of the Red Cross", "link": "https://wikipedia.org/wiki/International_Committee_of_the_Red_Cross"}]}, {"year": "1863", "text": "American Civil War: Battle of Wauhatchie: Forces under Union General <PERSON> repel a Confederate attack led by General <PERSON> in one of the few night battles of the war, protecting the Union's recently opened supply line into Chattanooga, Tennessee.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Wauhatchie\" title=\"Battle of Wauhatchie\">Battle of Wauhatchie</a>: Forces under <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses S. Grant\"><PERSON></a> repel a <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> attack led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in one of the few night battles of the war, protecting the Union's recently opened <a href=\"https://wikipedia.org/wiki/Military_supply-chain_management\" title=\"Military supply-chain management\">supply line</a> into <a href=\"https://wikipedia.org/wiki/Chattanooga,_Tennessee\" title=\"Chattanooga, Tennessee\">Chattanooga, Tennessee</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Wauhatchie\" title=\"Battle of Wauhatchie\">Battle of Wauhatchie</a>: Forces under <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses S. Grant\"><PERSON></a> repel a <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> attack led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in one of the few night battles of the war, protecting the Union's recently opened <a href=\"https://wikipedia.org/wiki/Military_supply-chain_management\" title=\"Military supply-chain management\">supply line</a> into <a href=\"https://wikipedia.org/wiki/Chattanooga,_Tennessee\" title=\"Chattanooga, Tennessee\">Chattanooga, Tennessee</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Wauhatchie", "link": "https://wikipedia.org/wiki/Battle_of_Wauhatchie"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Military supply-chain management", "link": "https://wikipedia.org/wiki/Military_supply-chain_management"}, {"title": "Chattanooga, Tennessee", "link": "https://wikipedia.org/wiki/Chattanooga,_Tennessee"}]}, {"year": "1888", "text": "The Convention of Constantinople is signed, guaranteeing free maritime passage through the Suez Canal during war and peace.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/Convention_of_Constantinople\" title=\"Convention of Constantinople\">Convention of Constantinople</a> is signed, guaranteeing free maritime passage through the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> during war and peace.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Convention_of_Constantinople\" title=\"Convention of Constantinople\">Convention of Constantinople</a> is signed, guaranteeing free maritime passage through the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> during war and peace.", "links": [{"title": "Convention of Constantinople", "link": "https://wikipedia.org/wiki/Convention_of_Constantinople"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}, {"year": "1914", "text": "Ottoman entry into World War I.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Ottoman_entry_into_World_War_I\" title=\"Ottoman entry into World War I\">Ottoman entry into World War I</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman_entry_into_World_War_I\" title=\"Ottoman entry into World War I\">Ottoman entry into World War I</a>.", "links": [{"title": "Ottoman entry into World War I", "link": "https://wikipedia.org/wiki/Ottoman_entry_into_World_War_I"}]}, {"year": "1918", "text": "The German High Seas Fleet is incapacitated when sailors mutiny, an action which would trigger the German Revolution of 1918-19.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German</a> <a href=\"https://wikipedia.org/wiki/High_Seas_Fleet\" title=\"High Seas Fleet\">High Seas Fleet</a> is incapacitated when sailors <a href=\"https://wikipedia.org/wiki/Wilhelmshaven_mutiny\" class=\"mw-redirect\" title=\"Wilhelmshaven mutiny\">mutiny</a>, an action which would trigger the <a href=\"https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319\" class=\"mw-redirect\" title=\"German Revolution of 1918-19\">German Revolution of 1918-19</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German</a> <a href=\"https://wikipedia.org/wiki/High_Seas_Fleet\" title=\"High Seas Fleet\">High Seas Fleet</a> is incapacitated when sailors <a href=\"https://wikipedia.org/wiki/Wilhelmshaven_mutiny\" class=\"mw-redirect\" title=\"Wilhelmshaven mutiny\">mutiny</a>, an action which would trigger the <a href=\"https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319\" class=\"mw-redirect\" title=\"German Revolution of 1918-19\">German Revolution of 1918-19</a>.", "links": [{"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "High Seas Fleet", "link": "https://wikipedia.org/wiki/High_Seas_Fleet"}, {"title": "Wilhelmshaven mutiny", "link": "https://wikipedia.org/wiki/Wilhelmshaven_mutiny"}, {"title": "German Revolution of 1918-19", "link": "https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319"}]}, {"year": "1921", "text": "United States: Second trial of <PERSON><PERSON> and <PERSON><PERSON><PERSON> in Boston, Massachusetts.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>: Second trial of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Boston,_Massachusetts\" class=\"mw-redirect\" title=\"Boston, Massachusetts\">Boston, Massachusetts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>: Second trial of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> and <PERSON><PERSON><PERSON>\"><PERSON><PERSON> and <PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Boston,_Massachusetts\" class=\"mw-redirect\" title=\"Boston, Massachusetts\">Boston, Massachusetts</a>.", "links": [{"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>"}, {"title": "Boston, Massachusetts", "link": "https://wikipedia.org/wiki/Boston,_Massachusetts"}]}, {"year": "1923", "text": "Turkey becomes a republic following the dissolution of the Ottoman Empire.", "html": "1923 - Turkey <a href=\"https://wikipedia.org/wiki/Republic_Day_(Turkey)\" title=\"Republic Day (Turkey)\">becomes a republic</a> following the dissolution of the Ottoman Empire.", "no_year_html": "Turkey <a href=\"https://wikipedia.org/wiki/Republic_Day_(Turkey)\" title=\"Republic Day (Turkey)\">becomes a republic</a> following the dissolution of the Ottoman Empire.", "links": [{"title": "Republic Day (Turkey)", "link": "https://wikipedia.org/wiki/Republic_Day_(Turkey)"}]}, {"year": "1929", "text": "Black Tuesday: The New York Stock Exchange crashes, ending the Great Bull Market of the 1920s and beginning the Great Depression.", "html": "1929 - Black Tuesday: The <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a> <a href=\"https://wikipedia.org/wiki/Wall_Street_Crash_of_1929\" class=\"mw-redirect\" title=\"Wall Street Crash of 1929\">crashes</a>, ending the Great Bull Market of the 1920s and beginning the <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>.", "no_year_html": "Black Tuesday: The <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a> <a href=\"https://wikipedia.org/wiki/Wall_Street_Crash_of_1929\" class=\"mw-redirect\" title=\"Wall Street Crash of 1929\">crashes</a>, ending the Great Bull Market of the 1920s and beginning the <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>.", "links": [{"title": "New York Stock Exchange", "link": "https://wikipedia.org/wiki/New_York_Stock_Exchange"}, {"title": "Wall Street Crash of 1929", "link": "https://wikipedia.org/wiki/Wall_Street_Crash_of_1929"}, {"title": "Great Depression", "link": "https://wikipedia.org/wiki/Great_Depression"}]}, {"year": "1941", "text": "The Holocaust: In the Kaunas Ghetto, over 10,000 Jews are shot by German occupiers at the Ninth Fort, a massacre known as the \"Great Action\".", "html": "1941 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: In the <a href=\"https://wikipedia.org/wiki/Kaunas_Ghetto\" class=\"mw-redirect\" title=\"Kaunas Ghetto\">Kaunas Ghetto</a>, <a href=\"https://wikipedia.org/wiki/Kaunas_massacre_of_October_29,_1941\" title=\"Kaunas massacre of October 29, 1941\">over 10,000 Jews are shot</a> by <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> occupiers at the <a href=\"https://wikipedia.org/wiki/Ninth_Fort\" title=\"Ninth Fort\">Ninth Fort</a>, a massacre known as the \"Great Action\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: In the <a href=\"https://wikipedia.org/wiki/Kaunas_Ghetto\" class=\"mw-redirect\" title=\"Kaunas Ghetto\">Kaunas Ghetto</a>, <a href=\"https://wikipedia.org/wiki/Kaunas_massacre_of_October_29,_1941\" title=\"Kaunas massacre of October 29, 1941\">over 10,000 Jews are shot</a> by <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> occupiers at the <a href=\"https://wikipedia.org/wiki/Ninth_Fort\" title=\"Ninth Fort\">Ninth Fort</a>, a massacre known as the \"Great Action\".", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Kaunas Ghetto", "link": "https://wikipedia.org/wiki/Kaunas_Ghetto"}, {"title": "Kaunas massacre of October 29, 1941", "link": "https://wikipedia.org/wiki/Kaunas_massacre_of_October_29,_1941"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Ninth Fort", "link": "https://wikipedia.org/wiki/Ninth_Fort"}]}, {"year": "1942", "text": "The Holocaust: In the United Kingdom, leading clergymen and political figures hold a public meeting to register outrage over Nazi Germany's persecution of Jews.", "html": "1942 - The Holocaust: In the United Kingdom, leading clergymen and political figures hold a public meeting to register outrage over <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>'s persecution of Jews.", "no_year_html": "The Holocaust: In the United Kingdom, leading clergymen and political figures hold a public meeting to register outrage over <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>'s persecution of Jews.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1944", "text": "The Dutch city of Breda is liberated by 1st Polish Armoured Division.", "html": "1944 - The Dutch city of <a href=\"https://wikipedia.org/wiki/Breda\" title=\"Breda\">Breda</a> is liberated by <a href=\"https://wikipedia.org/wiki/1st_Polish_Armoured_Division\" class=\"mw-redirect\" title=\"1st Polish Armoured Division\">1st Polish Armoured Division</a>.", "no_year_html": "The Dutch city of <a href=\"https://wikipedia.org/wiki/Breda\" title=\"Breda\">Breda</a> is liberated by <a href=\"https://wikipedia.org/wiki/1st_Polish_Armoured_Division\" class=\"mw-redirect\" title=\"1st Polish Armoured Division\">1st Polish Armoured Division</a>.", "links": [{"title": "Breda", "link": "https://wikipedia.org/wiki/Breda"}, {"title": "1st Polish Armoured Division", "link": "https://wikipedia.org/wiki/1st_Polish_Armoured_Division"}]}, {"year": "1944", "text": "World War II: The Soviet Red Army enters Hungary.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> enters Hungary.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> enters Hungary.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1948", "text": "Israeli-Palestinian conflict: Safsaf massacre: Israeli soldiers capture the Palestinian village of Safsaf in the Galilee; afterwards, between 52 and 64 villagers are massacred by the IDF.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: <a href=\"https://wikipedia.org/wiki/Safsaf_massacre\" title=\"Safsaf massacre\">Safsaf massacre</a>: Israeli soldiers capture the Palestinian village of <a href=\"https://wikipedia.org/wiki/Safsaf\" title=\"Safsaf\">Safsaf</a> in the <a href=\"https://wikipedia.org/wiki/Galilee\" title=\"Galilee\">Galilee</a>; afterwards, between 52 and 64 villagers are massacred by the IDF.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: <a href=\"https://wikipedia.org/wiki/Safsaf_massacre\" title=\"Safsaf massacre\">Safsaf massacre</a>: Israeli soldiers capture the Palestinian village of <a href=\"https://wikipedia.org/wiki/Safsaf\" title=\"Safsaf\">Safsaf</a> in the <a href=\"https://wikipedia.org/wiki/Galilee\" title=\"Galilee\">Galilee</a>; afterwards, between 52 and 64 villagers are massacred by the IDF.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "Safsaf massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Safsaf", "link": "https://wikipedia.org/wiki/<PERSON>fsaf"}, {"title": "G<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1953", "text": "BCPA Flight 304 DC-6 crashes near San Francisco.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/BCPA_Flight_304\" title=\"BCPA Flight 304\">BCPA Flight 304</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">DC-6</a> crashes near <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BCPA_Flight_304\" title=\"BCPA Flight 304\">BCPA Flight 304</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">DC-6</a> crashes near <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "links": [{"title": "BCPA Flight 304", "link": "https://wikipedia.org/wiki/BCPA_Flight_304"}, {"title": "Douglas DC-6", "link": "https://wikipedia.org/wiki/Douglas_DC-6"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1955", "text": "The Soviet battleship Novorossiysk strikes a World War II mine in the harbor at Sevastopol.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Italian_battleship_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Italian battleship <PERSON><PERSON><PERSON>\">Soviet battleship <i>Novorossiysk</i></a> strikes a World War II mine in the harbor at <a href=\"https://wikipedia.org/wiki/Sevastopol\" title=\"Sevastopol\">Sevastopol</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Italian_battleship_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Italian battleship <PERSON><PERSON><PERSON>\">Soviet battleship <i>Novorossiysk</i></a> strikes a World War II mine in the harbor at <a href=\"https://wikipedia.org/wiki/Sevastopol\" title=\"Sevastopol\">Sevastopol</a>.", "links": [{"title": "Italian battleship <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Italian_battleship_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Sevastopol", "link": "https://wikipedia.org/wiki/Sevastopol"}]}, {"year": "1956", "text": "Suez Crisis begins: Israeli forces invade the Sinai Peninsula and push Egyptian forces back toward the Suez Canal.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a> begins: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> forces invade the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a> and push <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> forces back toward the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a> begins: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> forces invade the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a> and push <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> forces back toward the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "links": [{"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Sinai Peninsula", "link": "https://wikipedia.org/wiki/Sinai_Peninsula"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}, {"year": "1957", "text": "Israel's prime minister <PERSON> and five of his ministers are injured when <PERSON><PERSON> throws a grenade into the Knesset.", "html": "1957 - Israel's prime minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and five of his ministers are injured when <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> throws a grenade into the <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a>.", "no_year_html": "Israel's prime minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and five of his ministers are injured when <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> throws a grenade into the <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k"}, {"title": "Knesset", "link": "https://wikipedia.org/wiki/Knesset"}]}, {"year": "1960", "text": "An airplane carrying the Cal Poly football team crashes on takeoff in Toledo, Ohio.", "html": "1960 - An airplane carrying the <a href=\"https://wikipedia.org/wiki/California_Polytechnic_State_University_football_team_plane_crash\" title=\"California Polytechnic State University football team plane crash\">Cal Poly football team crashes on takeoff</a> in Toledo, Ohio.", "no_year_html": "An airplane carrying the <a href=\"https://wikipedia.org/wiki/California_Polytechnic_State_University_football_team_plane_crash\" title=\"California Polytechnic State University football team plane crash\">Cal Poly football team crashes on takeoff</a> in Toledo, Ohio.", "links": [{"title": "California Polytechnic State University football team plane crash", "link": "https://wikipedia.org/wiki/California_Polytechnic_State_University_football_team_plane_crash"}]}, {"year": "1964", "text": "The United Republic of Tanganyika and Zanzibar is renamed to the United Republic of Tanzania.", "html": "1964 - The United Republic of <a href=\"https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)\" title=\"Tanganyika (1961-1964)\">Tanganyika</a> and <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a> is renamed to the <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">United Republic of Tanzania</a>.", "no_year_html": "The United Republic of <a href=\"https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)\" title=\"Tanganyika (1961-1964)\">Tanganyika</a> and <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a> is renamed to the <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">United Republic of Tanzania</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (1961-1964)", "link": "https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)"}, {"title": "Zanzibar", "link": "https://wikipedia.org/wiki/Zanzibar"}, {"title": "Tanzania", "link": "https://wikipedia.org/wiki/Tanzania"}]}, {"year": "1964", "text": "Biggest jewel heist; involving the Star of India in the American Museum of Natural History in New York City by <PERSON><PERSON><PERSON> the <PERSON><PERSON> and gang.", "html": "1964 - Biggest <a href=\"https://wikipedia.org/wiki/Gemstone\" title=\"Gemstone\">jewel</a> <a href=\"https://wikipedia.org/wiki/Robbery\" title=\"Robbery\">heist</a>; involving the <a href=\"https://wikipedia.org/wiki/Star_of_India_(gem)\" title=\"Star of India (gem)\">Star of India</a> in the <a href=\"https://wikipedia.org/wiki/American_Museum_of_Natural_History\" title=\"American Museum of Natural History\">American Museum of Natural History</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Murph the Surf</a> and gang.", "no_year_html": "Biggest <a href=\"https://wikipedia.org/wiki/Gemstone\" title=\"Gemstone\">jewel</a> <a href=\"https://wikipedia.org/wiki/Robbery\" title=\"Robbery\">heist</a>; involving the <a href=\"https://wikipedia.org/wiki/Star_of_India_(gem)\" title=\"Star of India (gem)\">Star of India</a> in the <a href=\"https://wikipedia.org/wiki/American_Museum_of_Natural_History\" title=\"American Museum of Natural History\">American Museum of Natural History</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Murph the Surf</a> and gang.", "links": [{"title": "Gemstone", "link": "https://wikipedia.org/wiki/Gemstone"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Robbery"}, {"title": "Star of India (gem)", "link": "https://wikipedia.org/wiki/Star_of_India_(gem)"}, {"title": "American Museum of Natural History", "link": "https://wikipedia.org/wiki/American_Museum_of_Natural_History"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "Montreal's World Fair, Expo 67, closes with over 50 million visitors.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>'s World Fair, <a href=\"https://wikipedia.org/wiki/Expo_67\" title=\"Expo 67\">Expo 67</a>, closes with over 50 million visitors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>'s World Fair, <a href=\"https://wikipedia.org/wiki/Expo_67\" title=\"Expo 67\">Expo 67</a>, closes with over 50 million visitors.", "links": [{"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}, {"title": "Expo 67", "link": "https://wikipedia.org/wiki/Expo_67"}]}, {"year": "1969", "text": "The first-ever computer-to-computer link is established on ARPANET, the precursor to the Internet.", "html": "1969 - The first-ever <a href=\"https://wikipedia.org/wiki/Computer\" title=\"Computer\">computer</a>-to-computer link is established on <a href=\"https://wikipedia.org/wiki/ARPANET\" title=\"ARPANET\">ARPANET</a>, the precursor to the <a href=\"https://wikipedia.org/wiki/Internet\" title=\"Internet\">Internet</a>.", "no_year_html": "The first-ever <a href=\"https://wikipedia.org/wiki/Computer\" title=\"Computer\">computer</a>-to-computer link is established on <a href=\"https://wikipedia.org/wiki/ARPANET\" title=\"ARPANET\">ARPANET</a>, the precursor to the <a href=\"https://wikipedia.org/wiki/Internet\" title=\"Internet\">Internet</a>.", "links": [{"title": "Computer", "link": "https://wikipedia.org/wiki/Computer"}, {"title": "ARPANET", "link": "https://wikipedia.org/wiki/ARPANET"}, {"title": "Internet", "link": "https://wikipedia.org/wiki/Internet"}]}, {"year": "1972", "text": "The three surviving perpetrators of the Munich massacre are released from prison in exchange for the hostages of the hijacked Lufthansa Flight 615.", "html": "1972 - The three surviving perpetrators of the <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a> are released from prison in exchange for the hostages of the hijacked <a href=\"https://wikipedia.org/wiki/Lufthansa_Flight_615\" title=\"Lufthansa Flight 615\">Lufthansa Flight 615</a>.", "no_year_html": "The three surviving perpetrators of the <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a> are released from prison in exchange for the hostages of the hijacked <a href=\"https://wikipedia.org/wiki/Lufthansa_Flight_615\" title=\"Lufthansa Flight 615\">Lufthansa Flight 615</a>.", "links": [{"title": "Munich massacre", "link": "https://wikipedia.org/wiki/Munich_massacre"}, {"title": "Lufthansa Flight 615", "link": "https://wikipedia.org/wiki/Lufthansa_Flight_615"}]}, {"year": "1980", "text": "Demonstration flight of a secretly modified C-130 for an Iran hostage crisis rescue attempt ends in a crash landing at Eglin Air Force Base's Duke Field, Florida, leading to the cancellation of Operation Credible Sport.", "html": "1980 - Demonstration flight of a secretly modified <a href=\"https://wikipedia.org/wiki/C-130_Hercules\" class=\"mw-redirect\" title=\"C-130 Hercules\">C-130</a> for an <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a> rescue attempt ends in a crash landing at <a href=\"https://wikipedia.org/wiki/Eglin_Air_Force_Base\" title=\"Eglin Air Force Base\">Eglin Air Force Base</a>'s Duke Field, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, leading to the cancellation of <a href=\"https://wikipedia.org/wiki/Operation_Credible_Sport\" title=\"Operation Credible Sport\">Operation Credible Sport</a>.", "no_year_html": "Demonstration flight of a secretly modified <a href=\"https://wikipedia.org/wiki/C-130_Hercules\" class=\"mw-redirect\" title=\"C-130 Hercules\">C-130</a> for an <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a> rescue attempt ends in a crash landing at <a href=\"https://wikipedia.org/wiki/Eglin_Air_Force_Base\" title=\"Eglin Air Force Base\">Eglin Air Force Base</a>'s Duke Field, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, leading to the cancellation of <a href=\"https://wikipedia.org/wiki/Operation_Credible_Sport\" title=\"Operation Credible Sport\">Operation Credible Sport</a>.", "links": [{"title": "C-130 Hercules", "link": "https://wikipedia.org/wiki/C-130_Hercules"}, {"title": "Iran hostage crisis", "link": "https://wikipedia.org/wiki/Iran_hostage_crisis"}, {"title": "Eglin Air Force Base", "link": "https://wikipedia.org/wiki/Eglin_Air_Force_Base"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Operation Credible Sport", "link": "https://wikipedia.org/wiki/Operation_Credible_Sport"}]}, {"year": "1985", "text": "Major General <PERSON> is announced as the winner of the first multi-party election in Liberia.", "html": "1985 - Major General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is announced as the winner of the first multi-party election in <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberia</a>.", "no_year_html": "Major General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is announced as the winner of the first multi-party election in <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Liberia", "link": "https://wikipedia.org/wiki/Liberia"}]}, {"year": "1986", "text": "British Prime Minister <PERSON> opens the last stretch of the M25 motorway.", "html": "1986 - British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> opens the last stretch of the <a href=\"https://wikipedia.org/wiki/M25_motorway\" title=\"M25 motorway\">M25 motorway</a>.", "no_year_html": "British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> opens the last stretch of the <a href=\"https://wikipedia.org/wiki/M25_motorway\" title=\"M25 motorway\">M25 motorway</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "M25 motorway", "link": "https://wikipedia.org/wiki/M25_motorway"}]}, {"year": "1991", "text": "The American Galileo spacecraft makes its closest approach to 951 Gaspra, becoming the first probe to visit an asteroid.", "html": "1991 - The American <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\"><i>Galileo</i> spacecraft</a> makes its closest approach to <a href=\"https://wikipedia.org/wiki/951_Gaspra\" title=\"951 Gaspra\">951 Gaspra</a>, becoming the first probe to visit an <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">asteroid</a>.", "no_year_html": "The American <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\"><i>Galileo</i> spacecraft</a> makes its closest approach to <a href=\"https://wikipedia.org/wiki/951_Gaspra\" title=\"951 Gaspra\">951 Gaspra</a>, becoming the first probe to visit an <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">asteroid</a>.", "links": [{"title": "<PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>_(spacecraft)"}, {"title": "951 Gaspra", "link": "https://wikipedia.org/wiki/951_Gaspra"}, {"title": "Asteroid", "link": "https://wikipedia.org/wiki/Asteroid"}]}, {"year": "1994", "text": "<PERSON> fires over two dozen shots at the White House; he is later convicted of trying to kill U.S. President <PERSON>.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> fires over two dozen shots at the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>; he is later convicted of trying to kill U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> fires over two dozen shots at the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>; he is later convicted of trying to kill U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "In South Africa, the Truth and Reconciliation Commission presents its report, which condemns both sides for committing atrocities.", "html": "1998 - In South Africa, the <a href=\"https://wikipedia.org/wiki/Truth_and_Reconciliation_Commission_(South_Africa)\" title=\"Truth and Reconciliation Commission (South Africa)\">Truth and Reconciliation Commission</a> presents its report, which condemns both sides for committing atrocities.", "no_year_html": "In South Africa, the <a href=\"https://wikipedia.org/wiki/Truth_and_Reconciliation_Commission_(South_Africa)\" title=\"Truth and Reconciliation Commission (South Africa)\">Truth and Reconciliation Commission</a> presents its report, which condemns both sides for committing atrocities.", "links": [{"title": "Truth and Reconciliation Commission (South Africa)", "link": "https://wikipedia.org/wiki/Truth_and_Reconciliation_Commission_(South_Africa)"}]}, {"year": "1998", "text": "Space Shuttle Discovery blasts off on STS-95 with 77-year-old <PERSON> on board, making him the oldest person to go into space at that time.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> blasts off on <a href=\"https://wikipedia.org/wiki/STS-95\" title=\"STS-95\">STS-95</a> with 77-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> on board, making him the oldest person to go into space at that time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> blasts off on <a href=\"https://wikipedia.org/wiki/STS-95\" title=\"STS-95\">STS-95</a> with 77-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> on board, making him the oldest person to go into space at that time.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-95", "link": "https://wikipedia.org/wiki/STS-95"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "ATSC HDTV broadcasting in the United States is inaugurated with the launch of the STS-95 space shuttle mission.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/ATSC\" class=\"mw-redirect\" title=\"ATSC\">ATSC</a> <a href=\"https://wikipedia.org/wiki/HDTV\" class=\"mw-redirect\" title=\"HDTV\">HDTV</a> broadcasting in the United States is inaugurated with the launch of the <a href=\"https://wikipedia.org/wiki/STS-95\" title=\"STS-95\">STS-95</a> space shuttle mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ATSC\" class=\"mw-redirect\" title=\"ATSC\">ATSC</a> <a href=\"https://wikipedia.org/wiki/HDTV\" class=\"mw-redirect\" title=\"HDTV\">HDTV</a> broadcasting in the United States is inaugurated with the launch of the <a href=\"https://wikipedia.org/wiki/STS-95\" title=\"STS-95\">STS-95</a> space shuttle mission.", "links": [{"title": "ATSC", "link": "https://wikipedia.org/wiki/ATSC"}, {"title": "HDTV", "link": "https://wikipedia.org/wiki/HDTV"}, {"title": "STS-95", "link": "https://wikipedia.org/wiki/STS-95"}]}, {"year": "1998", "text": "Hurricane <PERSON>, the second deadliest Atlantic hurricane in history, makes landfall in Honduras.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Hurricane_Mitch\" title=\"Hurricane Mitch\">Hurricane <PERSON></a>, the second deadliest Atlantic hurricane in history, makes <a href=\"https://wikipedia.org/wiki/Landfall_(meteorology)\" class=\"mw-redirect\" title=\"Landfall (meteorology)\">landfall</a> in <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Mitch\" title=\"Hurricane Mitch\">Hurricane Mitch</a>, the second deadliest Atlantic hurricane in history, makes <a href=\"https://wikipedia.org/wiki/Landfall_(meteorology)\" class=\"mw-redirect\" title=\"Landfall (meteorology)\">landfall</a> in <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a>.", "links": [{"title": "Hurricane Mitch", "link": "https://wikipedia.org/wiki/Hurricane_Mitch"}, {"title": "Landfall (meteorology)", "link": "https://wikipedia.org/wiki/Landfall_(meteorology)"}, {"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}]}, {"year": "1998", "text": "The Gothenburg discothèque fire in Sweden kills 63 and injures over 200.", "html": "1998 - The <a href=\"https://wikipedia.org/wiki/Gothenburg_discoth%C3%A8que_fire\" title=\"Gothenburg discothèque fire\">Gothenburg discothèque fire</a> in Sweden kills 63 and injures over 200.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Gothenburg_discoth%C3%A8que_fire\" title=\"Gothenburg discothèque fire\">Gothenburg discothèque fire</a> in Sweden kills 63 and injures over 200.", "links": [{"title": "Gothenburg discothèque fire", "link": "https://wikipedia.org/wiki/Gothenburg_discoth%C3%A8que_fire"}]}, {"year": "1999", "text": "A large cyclone devastates Odisha, India.", "html": "1999 - A <a href=\"https://wikipedia.org/wiki/1999_Odisha_cyclone\" title=\"1999 Odisha cyclone\">large cyclone</a> devastates <a href=\"https://wikipedia.org/wiki/Odisha\" title=\"Odisha\">Odisha</a>, India.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1999_Odisha_cyclone\" title=\"1999 Odisha cyclone\">large cyclone</a> devastates <a href=\"https://wikipedia.org/wiki/Odisha\" title=\"Odisha\">Odisha</a>, India.", "links": [{"title": "1999 Odisha cyclone", "link": "https://wikipedia.org/wiki/1999_Odisha_cyclone"}, {"title": "Odisha", "link": "https://wikipedia.org/wiki/Odisha"}]}, {"year": "2002", "text": "A fire destroys a luxurious department store in Ho Chi Minh City, where 1,500 people are shopping. More than 60 people die and over 100 are unaccounted for in the deadliest peacetime disaster in Vietnam.", "html": "2002 - A <a href=\"https://wikipedia.org/wiki/2002_Ho_Chi_Minh_City_ITC_fire\" title=\"2002 Ho Chi Minh City ITC fire\">fire destroys a luxurious department store</a> in Ho Chi Minh City, where 1,500 people are shopping. More than 60 people die and over 100 are unaccounted for in the deadliest peacetime disaster in Vietnam.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2002_Ho_Chi_Minh_City_ITC_fire\" title=\"2002 Ho Chi Minh City ITC fire\">fire destroys a luxurious department store</a> in Ho Chi Minh City, where 1,500 people are shopping. More than 60 people die and over 100 are unaccounted for in the deadliest peacetime disaster in Vietnam.", "links": [{"title": "2002 Ho Chi Minh City ITC fire", "link": "https://wikipedia.org/wiki/2002_<PERSON>_Chi_Minh_City_ITC_fire"}]}, {"year": "2004", "text": "The Arabic-language news network Al Jazeera broadcasts an excerpt from a 2004 <PERSON><PERSON><PERSON> bin Laden video in which the terrorist leader first admits direct responsibility for the September 11, 2001 attacks and references the 2004 U.S. presidential election.", "html": "2004 - The Arabic-language news network <a href=\"https://wikipedia.org/wiki/Al_Jazeera_Media_Network\" title=\"Al Jazeera Media Network\">Al Jazeera</a> broadcasts an excerpt from a <a href=\"https://wikipedia.org/wiki/2004_<PERSON><PERSON><PERSON>_bin_<PERSON>_video\" title=\"2004 <PERSON><PERSON><PERSON> bin Laden video\">2004 <PERSON><PERSON><PERSON> bin Laden video</a> in which the terrorist leader first admits direct responsibility for the <a href=\"https://wikipedia.org/wiki/September_11,_2001_attacks\" class=\"mw-redirect\" title=\"September 11, 2001 attacks\">September 11, 2001 attacks</a> and references the <a href=\"https://wikipedia.org/wiki/2004_U.S._presidential_election\" class=\"mw-redirect\" title=\"2004 U.S. presidential election\">2004 U.S. presidential election</a>.", "no_year_html": "The Arabic-language news network <a href=\"https://wikipedia.org/wiki/Al_Jazeera_Media_Network\" title=\"Al Jazeera Media Network\">Al Jazeera</a> broadcasts an excerpt from a <a href=\"https://wikipedia.org/wiki/2004_<PERSON><PERSON><PERSON>_bin_<PERSON>_video\" title=\"2004 <PERSON><PERSON><PERSON> bin Laden video\">2004 <PERSON><PERSON><PERSON> bin Laden video</a> in which the terrorist leader first admits direct responsibility for the <a href=\"https://wikipedia.org/wiki/September_11,_2001_attacks\" class=\"mw-redirect\" title=\"September 11, 2001 attacks\">September 11, 2001 attacks</a> and references the <a href=\"https://wikipedia.org/wiki/2004_U.S._presidential_election\" class=\"mw-redirect\" title=\"2004 U.S. presidential election\">2004 U.S. presidential election</a>.", "links": [{"title": "Al Jazeera Media Network", "link": "https://wikipedia.org/wiki/Al_Jazeera_Media_Network"}, {"title": "2004 <PERSON><PERSON><PERSON> bin Laden video", "link": "https://wikipedia.org/wiki/2004_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_video"}, {"title": "September 11, 2001 attacks", "link": "https://wikipedia.org/wiki/September_11,_2001_attacks"}, {"title": "2004 U.S. presidential election", "link": "https://wikipedia.org/wiki/2004_U.S._presidential_election"}]}, {"year": "2005", "text": "Bombings in Delhi, India kill 67 and injure over 200 people.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/2005_Delhi_bombings\" title=\"2005 Delhi bombings\">Bombings in Delhi, India</a> kill 67 and injure over 200 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2005_Delhi_bombings\" title=\"2005 Delhi bombings\">Bombings in Delhi, India</a> kill 67 and injure over 200 people.", "links": [{"title": "2005 Delhi bombings", "link": "https://wikipedia.org/wiki/2005_Delhi_bombings"}]}, {"year": "2006", "text": "ADC Airlines Flight 053 crashes after takeoff from Nnamdi Azikiwe International Airport in Abuja, Nigeria killing 96 people and injuring nine.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/ADC_Airlines_Flight_053\" title=\"ADC Airlines Flight 053\">ADC Airlines Flight 053</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Nnamdi_Azikiwe_International_Airport\" title=\"Nnamdi Azikiwe International Airport\">Nnamdi Azikiwe International Airport</a> in <a href=\"https://wikipedia.org/wiki/Abuja\" title=\"Abuja\">Abuja</a>, Nigeria killing 96 people and injuring nine.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ADC_Airlines_Flight_053\" title=\"ADC Airlines Flight 053\">ADC Airlines Flight 053</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Nnamdi_Azikiwe_International_Airport\" title=\"Nnamdi Azikiwe International Airport\">Nnamdi Azikiwe International Airport</a> in <a href=\"https://wikipedia.org/wiki/Abuja\" title=\"Abuja\">Abuja</a>, Nigeria killing 96 people and injuring nine.", "links": [{"title": "ADC Airlines Flight 053", "link": "https://wikipedia.org/wiki/ADC_Airlines_Flight_053"}, {"title": "Nnamdi Azikiwe International Airport", "link": "https://wikipedia.org/wiki/Nnamdi_Azikiwe_International_Airport"}, {"title": "<PERSON>ja", "link": "https://wikipedia.org/wiki/Abuja"}]}, {"year": "2008", "text": "Delta Air Lines merges with Northwest Airlines, creating the world's largest airline and reducing the number of US legacy carriers to five.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines\" title=\"Delta Air Lines\">Delta Air Lines</a> merges with <a href=\"https://wikipedia.org/wiki/Northwest_Airlines\" title=\"Northwest Airlines\">Northwest Airlines</a>, creating the world's largest airline and reducing the number of US legacy carriers to five.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delta_Air_Lines\" title=\"Delta Air Lines\">Delta Air Lines</a> merges with <a href=\"https://wikipedia.org/wiki/Northwest_Airlines\" title=\"Northwest Airlines\">Northwest Airlines</a>, creating the world's largest airline and reducing the number of US legacy carriers to five.", "links": [{"title": "Delta Air Lines", "link": "https://wikipedia.org/wiki/Delta_Air_Lines"}, {"title": "Northwest Airlines", "link": "https://wikipedia.org/wiki/Northwest_Airlines"}]}, {"year": "2008", "text": "A pair of deadly earthquakes hits Baluchistan, Pakistan, killing 215.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/2008_Ziarat_earthquakes\" title=\"2008 Ziarat earthquakes\">A pair of deadly earthquakes</a> hits <a href=\"https://wikipedia.org/wiki/Baluchistan\" class=\"mw-redirect\" title=\"Baluchistan\">Baluchistan</a>, <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, killing 215.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2008_Ziarat_earthquakes\" title=\"2008 Ziarat earthquakes\">A pair of deadly earthquakes</a> hits <a href=\"https://wikipedia.org/wiki/Baluchistan\" class=\"mw-redirect\" title=\"Baluchistan\">Baluchistan</a>, <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, killing 215.", "links": [{"title": "2008 Ziarat earthquakes", "link": "https://wikipedia.org/wiki/2008_Ziarat_earthquakes"}, {"title": "Baluchistan", "link": "https://wikipedia.org/wiki/Baluchistan"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "2012", "text": "Hurricane Sandy hits the east coast of the United States, killing hundreds, while leaving nearly $70 billion in damages and causing major power outages.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Hurricane_Sandy\" title=\"Hurricane Sandy\">Hurricane Sandy</a> hits the east coast of the United States, killing hundreds, while leaving nearly $70 billion in damages and causing major power outages.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Sandy\" title=\"Hurricane Sandy\">Hurricane Sandy</a> hits the east coast of the United States, killing hundreds, while leaving nearly $70 billion in damages and causing major power outages.", "links": [{"title": "Hurricane Sandy", "link": "https://wikipedia.org/wiki/Hurricane_Sandy"}]}, {"year": "2014", "text": "A mud slide; the 2014 Badulla landslide, in south-central Sri Lanka, kills at least 16 people, and leaves hundreds of people missing.", "html": "2014 - A mud slide; the <a href=\"https://wikipedia.org/wiki/2014_Badulla_landslide\" title=\"2014 Badulla landslide\">2014 Badulla landslide</a>, in south-central <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, kills at least 16 people, and leaves hundreds of people missing.", "no_year_html": "A mud slide; the <a href=\"https://wikipedia.org/wiki/2014_Badulla_landslide\" title=\"2014 Badulla landslide\">2014 Badulla landslide</a>, in south-central <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, kills at least 16 people, and leaves hundreds of people missing.", "links": [{"title": "2014 Badulla landslide", "link": "https://wikipedia.org/wiki/2014_<PERSON><PERSON><PERSON>_landslide"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "2015", "text": "China announces the end of its one-child policy after 35 years.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> announces the end of its <a href=\"https://wikipedia.org/wiki/One-child_policy\" title=\"One-child policy\">one-child policy</a> after 35 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> announces the end of its <a href=\"https://wikipedia.org/wiki/One-child_policy\" title=\"One-child policy\">one-child policy</a> after 35 years.", "links": [{"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "One-child policy", "link": "https://wikipedia.org/wiki/One-child_policy"}]}, {"year": "2018", "text": "A Boeing 737 MAX plane crashes after taking off from Jakarta, Indonesia killing 189 people on board.", "html": "2018 - A <a href=\"https://wikipedia.org/wiki/Boeing_737_MAX\" title=\"Boeing 737 MAX\">Boeing 737 MAX</a> plane <a href=\"https://wikipedia.org/wiki/Lion_Air_Flight_610\" title=\"Lion Air Flight 610\">crashes</a> after taking off from <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> killing 189 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Boeing_737_MAX\" title=\"Boeing 737 MAX\">Boeing 737 MAX</a> plane <a href=\"https://wikipedia.org/wiki/Lion_Air_Flight_610\" title=\"Lion Air Flight 610\">crashes</a> after taking off from <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> killing 189 people on board.", "links": [{"title": "Boeing 737 MAX", "link": "https://wikipedia.org/wiki/Boeing_737_MAX"}, {"title": "Lion Air Flight 610", "link": "https://wikipedia.org/wiki/Lion_Air_Flight_610"}, {"title": "Jakarta", "link": "https://wikipedia.org/wiki/Jakarta"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}]}, {"year": "2020", "text": "<PERSON>, Leader of the Labour Party and of the Opposition in the United Kingdom is suspended from the Labour Party following his response to findings from the EHRC on the issue of antisemitism within the party.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Leader_of_the_Labour_Party_(UK)\" title=\"Leader of the Labour Party (UK)\">Leader of the Labour Party</a> and of the <a href=\"https://wikipedia.org/wiki/Leader_of_the_Opposition_(United_Kingdom)\" title=\"Leader of the Opposition (United Kingdom)\">Opposition</a> in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> is suspended from the <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party</a> following his response to findings from the EHRC on the issue of <a href=\"https://wikipedia.org/wiki/Antisemitism_in_the_UK_Labour_Party\" class=\"mw-redirect\" title=\"Antisemitism in the UK Labour Party\">antisemitism within the party</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Leader_of_the_Labour_Party_(UK)\" title=\"Leader of the Labour Party (UK)\">Leader of the Labour Party</a> and of the <a href=\"https://wikipedia.org/wiki/Leader_of_the_Opposition_(United_Kingdom)\" title=\"Leader of the Opposition (United Kingdom)\">Opposition</a> in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> is suspended from the <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party</a> following his response to findings from the EHRC on the issue of <a href=\"https://wikipedia.org/wiki/Antisemitism_in_the_UK_Labour_Party\" class=\"mw-redirect\" title=\"Antisemitism in the UK Labour Party\">antisemitism within the party</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Leader of the Labour Party (UK)", "link": "https://wikipedia.org/wiki/Leader_of_the_Labour_Party_(UK)"}, {"title": "Leader of the Opposition (United Kingdom)", "link": "https://wikipedia.org/wiki/Leader_of_the_Opposition_(United_Kingdom)"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Labour Party (UK)", "link": "https://wikipedia.org/wiki/Labour_Party_(UK)"}, {"title": "Antisemitism in the UK Labour Party", "link": "https://wikipedia.org/wiki/Antisemitism_in_the_UK_Labour_Party"}]}, {"year": "2022", "text": "At least 156 die at a crowd crush during a Halloween celebration in Itaewon district, Seoul, South Korea.", "html": "2022 - At least 156 die at <a href=\"https://wikipedia.org/wiki/Seoul_Halloween_crowd_crush\" title=\"Seoul Halloween crowd crush\">a crowd crush during a Halloween celebration</a> in <a href=\"https://wikipedia.org/wiki/Itaewon\" title=\"Itaewon\">Itaewon district</a>, <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>, South Korea.", "no_year_html": "At least 156 die at <a href=\"https://wikipedia.org/wiki/Seoul_Halloween_crowd_crush\" title=\"Seoul Halloween crowd crush\">a crowd crush during a Halloween celebration</a> in <a href=\"https://wikipedia.org/wiki/Itaewon\" title=\"Itaewon\">Itaewon district</a>, <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>, South Korea.", "links": [{"title": "Seoul Halloween crowd crush", "link": "https://wikipedia.org/wiki/Seoul_Halloween_crowd_crush"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Itaewon"}, {"title": "Seoul", "link": "https://wikipedia.org/wiki/Seoul"}]}, {"year": "2022", "text": "At least 100 people are killed and over 300 are injured by a double car bombing in Mogadishu, Somalia.", "html": "2022 - At least 100 people are killed and over 300 are injured by a <a href=\"https://wikipedia.org/wiki/2022_Somali_Ministry_of_Education_bombings\" title=\"2022 Somali Ministry of Education bombings\">double car bombing</a> in <a href=\"https://wikipedia.org/wiki/Mogadishu\" title=\"Mogadishu\">Mogadishu</a>, Somalia.", "no_year_html": "At least 100 people are killed and over 300 are injured by a <a href=\"https://wikipedia.org/wiki/2022_Somali_Ministry_of_Education_bombings\" title=\"2022 Somali Ministry of Education bombings\">double car bombing</a> in <a href=\"https://wikipedia.org/wiki/Mogadishu\" title=\"Mogadishu\">Mogadishu</a>, Somalia.", "links": [{"title": "2022 Somali Ministry of Education bombings", "link": "https://wikipedia.org/wiki/2022_Somali_Ministry_of_Education_bombings"}, {"title": "Mogadishu", "link": "https://wikipedia.org/wiki/Mogadishu"}]}], "Births": [{"year": "1463", "text": "<PERSON>, Italian physician and philosopher (d. 1512)", "html": "1463 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and philosopher (d. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and philosopher (d. 1512)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1497", "text": "<PERSON><PERSON><PERSON> the Younger, Italian cardinal (d. 1549)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Younger\" title=\"<PERSON><PERSON><PERSON> the Younger\"><PERSON><PERSON><PERSON> the Younger</a>, Italian cardinal (d. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Younger\" title=\"<PERSON><PERSON><PERSON> the Younger\"><PERSON><PERSON><PERSON> the Younger</a>, Italian cardinal (d. 1549)", "links": [{"title": "<PERSON><PERSON><PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_the_Younger"}]}, {"year": "1504", "text": "<PERSON>, South Korean painter and poet (d. 1551)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sai<PERSON>\" title=\"<PERSON> Saimdang\"><PERSON></a>, South Korean painter and poet (d. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sai<PERSON>dan<PERSON>\" title=\"Shin Saimdang\"><PERSON></a>, South Korean painter and poet (d. 1551)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Shin_Sai<PERSON>dang"}]}, {"year": "1507", "text": "<PERSON>, Spanish general (d. 1582)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish general (d. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish general (d. 1582)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON>, English archbishop and academic (d. 1633)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Abbot_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop and academic (d. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Abbot_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop and academic (d. 1633)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1682", "text": "<PERSON>, French historian, explorer, and author (d. 1761)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, explorer, and author (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>rle<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, explorer, and author (d. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON>, English mathematician and astronomer (d. 1754)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (d. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, English admiral and politician, 11th Commodore Governor of Newfoundland (d. 1757)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of lieutenant governors of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador"}]}, {"year": "1711", "text": "<PERSON>, Italian physicist and academic, first woman to have doctorate in science (d. 1778)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic, first woman to have doctorate in science (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic, first woman to have doctorate in science (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, Scottish lawyer and author (d. 1795)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and author (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and author (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON><PERSON>, Italian astronomer and meteorologist (d. 1873)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian astronomer and meteorologist (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian astronomer and meteorologist (d. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Swedish playwright (d. 1907)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish playwright (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish playwright (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, American composer (d. 1904)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Russian-Polish cardinal (d. 1902)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Mieczys%C5%82aw_Halka-Led%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Russian-Polish cardinal (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mieczys%C5%82aw_Halka-Led%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Russian-Polish cardinal (d. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mieczys%C5%82aw_Hal<PERSON>-Led%C3%B3<PERSON><PERSON>"}]}, {"year": "1831", "text": "<PERSON>, English-Australian politician, 11th Premier of South Australia (d. 1916)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1832", "text": "<PERSON><PERSON><PERSON>, Ecuadorian saint (d. 1869)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Narc<PERSON>_<PERSON>_<PERSON>%C3%BAs\" title=\"Narc<PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian saint (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narc<PERSON>_<PERSON>_<PERSON>%C3%BAs\" title=\"Narc<PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian saint (d. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narc<PERSON>_<PERSON>_<PERSON>%C3%BAs"}]}, {"year": "1837", "text": "<PERSON>, American folk artist and quilter (d. 1910)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk artist and quilter (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk artist and quilter (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, Canadian archbishop (d. 1939)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9si\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9si\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bruch%C3%A9si"}]}, {"year": "1855", "text": "<PERSON><PERSON><PERSON>, French physicist and academic (d. 1941)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and academic (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, Russian painter (d. 1904)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Filipino general and politician (d. 1899)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Antonio <PERSON>\"><PERSON></a>, Filipino general and politician (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Antonio <PERSON>\"><PERSON></a>, Filipino general and politician (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, German astronomer (d. 1946)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON> of Romania (d. 1938)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Marie_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marie_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a> (d. 1938)", "links": [{"title": "Marie of Romania", "link": "https://wikipedia.org/wiki/Marie_of_Romania"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, Filipino film producer (d. 1966)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino film producer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino film producer (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, English cricketer and coach (d. 1973)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer and coach (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a>, English cricketer and coach (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, American lawyer and politician (d. 1941)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> B<PERSON> Adams\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician (d. 1941)", "links": [{"title": "Alva B. Adams", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, German soldier and politician, Chancellor of Germany (d. 1969)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Chancellors of Germany", "link": "https://wikipedia.org/wiki/List_of_Chancellors_of_Germany"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, Russian physicist and academic (d. 1960)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1881", "text": "<PERSON>, American football player and hammer thrower (d. 1930)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American football player and hammer thrower (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American football player and hammer thrower (d. 1930)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1882", "text": "<PERSON>, French author and playwright (d. 1944)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, French swimmer and water polo player (d. 1966)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French swimmer and water polo player (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French swimmer and water polo player (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_Hochepied"}]}, {"year": "1891", "text": "<PERSON>, American actress and singer (d. 1951)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>\" title=\"<PERSON> Brice\"><PERSON></a>, American actress and singer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Brice\" title=\"<PERSON> Brice\"><PERSON></a>, American actress and singer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German lawyer and politician, Chancellor of Nazi Germany (d. 1945)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Nazi <PERSON></a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Nazi <PERSON></a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Chancellors of Germany", "link": "https://wikipedia.org/wiki/List_of_Chancellors_of_Germany"}]}, {"year": "1897", "text": "<PERSON>, English footballer (d. 1964)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1897)\" title=\"<PERSON> (footballer, born 1897)\"><PERSON></a>, English footballer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1897)\" title=\"<PERSON> (footballer, born 1897)\"><PERSON></a>, English footballer (d. 1964)", "links": [{"title": "<PERSON> (footballer, born 1897)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1897)"}]}, {"year": "1898", "text": "<PERSON>, English soldier (d. 1984)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Georgian-American actor (d. 1972)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian-American actor (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian-American actor (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English author (d. 1973)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henry Green\"><PERSON></a>, English author (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, American author (d. 1972)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, French actress (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Ed<PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edwige_Fe<PERSON>l%C3%A8re"}]}, {"year": "1910", "text": "<PERSON><PERSON> <PERSON><PERSON>, English philosopher and author (d. 1989)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Ayer\" title=\"<PERSON><PERSON> <PERSON><PERSON> Ayer\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Ayer\" title=\"<PERSON><PERSON> <PERSON>. Ayer\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and author (d. 1989)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>r"}]}, {"year": "1913", "text": "<PERSON>, American ice hockey player and referee (d. 2014)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Al_<PERSON>omi\" title=\"Al <PERSON>omi\"><PERSON></a>, American ice hockey player and referee (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_<PERSON>omi\" title=\"Al Suomi\"><PERSON></a>, American ice hockey player and referee (d. 2014)", "links": [{"title": "Al Suomi", "link": "https://wikipedia.org/wiki/<PERSON>_Suomi"}]}, {"year": "1914", "text": "<PERSON> of Bulgaria, Bulgarian patriarch (d. 2012)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bulgaria\" title=\"Maxim of Bulgaria\"><PERSON> of Bulgaria</a>, Bulgarian patriarch (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maxim_of_Bulgaria\" title=\"Maxim of Bulgaria\"><PERSON> of Bulgaria</a>, Bulgarian patriarch (d. 2012)", "links": [{"title": "Maxim of Bulgaria", "link": "https://wikipedia.org/wiki/Maxim_of_Bulgaria"}]}, {"year": "1915", "text": "<PERSON>, American physician and academic (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American screenwriter and producer (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (d. 2007)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(writer)"}]}, {"year": "1918", "text": "<PERSON>, American actress and author (d. 2020)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Venezuelan-American physician and immunologist, Nobel Prize laureate (d. 2011)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>u<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan-American physician and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>u<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan-American physician and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>j_Benacerraf"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech violinist and conductor (d. 1995)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech violinist and conductor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech violinist and conductor (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Indian metropolitan (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Baselios_Thoma_Didymos_I\" class=\"mw-redirect\" title=\"Baselios Thoma Didymos I\">Baselios Thoma Didymos I</a>, Indian metropolitan (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baselios_Thoma_Didymos_I\" class=\"mw-redirect\" title=\"Baselios Thoma Didymos I\">Baselios Thoma Didymos I</a>, Indian metropolitan (d. 2014)", "links": [{"title": "<PERSON>ios <PERSON>", "link": "https://wikipedia.org/wiki/Basel<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_I"}]}, {"year": "1921", "text": "<PERSON>, Sri Lankan civil servant and academic (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Baku_Ma<PERSON>\" title=\"<PERSON> Mahadeva\"><PERSON></a>, Sri Lankan civil servant and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baku_Ma<PERSON>eva\" title=\"<PERSON> Mahadeva\"><PERSON></a>, Sri Lankan civil servant and academic (d. 2013)", "links": [{"title": "<PERSON> Mahadeva", "link": "https://wikipedia.org/wiki/Baku_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American soldier and cartoonist (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and cartoonist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and cartoonist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American trumpet player and composer (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1923", "text": "<PERSON>, Austrian-American chemist, author, and playwright (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American chemist, author, and playwright (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American chemist, author, and playwright (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Dutch runner, hurdler, and long jumper (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch runner, hurdler, and long jumper (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch runner, hurdler, and long jumper (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, British restoration bookbinder (d. 2019)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British restoration bookbinder (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British restoration bookbinder (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American journalist and author (d. 2009)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English actor (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Polish-Israeli songwriter and poet (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli songwriter and poet (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli songwriter and poet (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, British mathematician (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British mathematician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British mathematician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American saxophonist and composer (d. 1985)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sims\"><PERSON><PERSON></a>, American saxophonist and composer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sims\" title=\"<PERSON><PERSON> Sims\"><PERSON><PERSON></a>, American saxophonist and composer (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sims"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish engineer and politician, 23rd Prime Minister of Turkey (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Nec<PERSON>in_E<PERSON>n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, 23rd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nec<PERSON>in_<PERSON>\" title=\"<PERSON>ec<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, 23rd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Necmettin_<PERSON>n"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "1926", "text": "<PERSON>, Canadian tenor and actor (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor and actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian tennis player", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian journalist and politician, 32nd Prime Minister of Russia (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian journalist and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian journalist and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Russia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Russia"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Dutch sprinter (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sprinter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sprinter (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, French sculptor and painter (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Niki_de_Saint_Phalle\" title=\"Niki de Saint Phalle\"><PERSON><PERSON> <PERSON> Phalle</a>, French sculptor and painter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niki_de_Saint_Phalle\" title=\"Niki de Saint Phalle\"><PERSON><PERSON> <PERSON> Saint Phalle</a>, French sculptor and painter (d. 2002)", "links": [{"title": "Niki de Saint Phalle", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>_Saint_Phalle"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Cuban singer and dancer", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Port<PERSON>\"><PERSON><PERSON></a>, Cuban singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Omara Portuondo\"><PERSON><PERSON></a>, Cuban singer and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Omara_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American pianist and composer (d. 1992)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American basketball player (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, <PERSON> of Potternewton, English pharmacist and politician", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Potternewton\" title=\"<PERSON>, <PERSON> of Potternewton\"><PERSON>, Baroness <PERSON> of Potternewton</a>, English pharmacist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>_of_Potternewton\" title=\"<PERSON>, <PERSON> of Potternewton\"><PERSON>, Baroness <PERSON> of Potternewton</a>, English pharmacist and politician", "links": [{"title": "<PERSON>, <PERSON> of Potternewton", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Potternewton"}]}, {"year": "1933", "text": "<PERSON>, American author and screenwriter (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (d. 2013)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1935", "text": "<PERSON>, English cricketer (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 2014)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1935", "text": "<PERSON>, English footballer (d. 2004)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English actor (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American bluegrass singer and banjo player (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass singer and banjo player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass singer and banjo player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Liberian politician, President of Liberia, Nobel Prize laureate", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian politician, <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian politician, <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1938", "text": "<PERSON>, American fiddle player, violinist, and singer-songwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fiddle player, violinist, and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fiddle player, violinist, and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English actor, director, and playwright", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, and playwright", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1940", "text": "<PERSON>, English-Canadian businessman and philanthropist, founded George Weston Limited (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/George_Weston_Limited\" title=\"George Weston Limited\">George Weston Limited</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/George_Weston_Limited\" title=\"George Weston Limited\">George Weston Limited</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "George Weston Limited", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Limited"}]}, {"year": "1941", "text": "<PERSON>, English fashion designer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(retailer)\" title=\"<PERSON> (retailer)\"><PERSON></a>, English fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(retailer)\" title=\"<PERSON> (retailer)\"><PERSON></a>, English fashion designer", "links": [{"title": "<PERSON> (retailer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(retailer)"}]}, {"year": "1941", "text": "<PERSON>, <PERSON>, English politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American rock/country musician and songwriter (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American rock/country musician and songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American rock/country musician and songwriter (d. 2023)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1942", "text": "<PERSON>, American painter and television host (d. 1995)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and television host (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and television host (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor, producer, and screenwriter (d. 1996)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian businessman", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Turkish surgeon and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish surgeon and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish surgeon and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>al"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and musician (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Dutch musician and songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English keyboard player and songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American businessman and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Dutch civil servant and politician (d. 2012)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch civil servant and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch civil servant and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ger<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2020)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON>, Australian lawyer and politician, 52nd Australian Minister for Communications", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Minister_for_Communications_(Australia)\" title=\"Minister for Communications (Australia)\">Australian Minister for Communications</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Minister_for_Communications_(Australia)\" title=\"Minister for Communications (Australia)\">Australian Minister for Communications</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Communications (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Communications_(Australia)"}]}, {"year": "1947", "text": "<PERSON>, American actor and activist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Dutch-American ethologist, author, and academic (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>aal\"><PERSON><PERSON></a>, Dutch-American ethologist, author, and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>aal\"><PERSON><PERSON></a>, Dutch-American ethologist, author, and academic (d. 2024)", "links": [{"title": "Fr<PERSON>", "link": "https://wikipedia.org/wiki/Fr<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actress, director, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, English footballer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American surgeon and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American football player and wrestler (d. 2021)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Scottish guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1950", "text": "<PERSON>, Turkish academic and politician, 11th President of Turkey", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCl\" title=\"<PERSON>\"><PERSON></a>, Turkish academic and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCl\" title=\"<PERSON>\"><PERSON></a>, Turkish academic and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Abdullah_G%C3%BCl"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1951", "text": "<PERSON>, American businessman and politician, 49th United States Secretary of the Interior", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 49th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 49th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1951", "text": "<PERSON><PERSON>, English race car driver and television host", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Tiff_<PERSON><PERSON>\" title=\"Tiff Needell\">T<PERSON></a>, English race car driver and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiff_<PERSON><PERSON>\" title=\"Tiff Needell\"><PERSON><PERSON></a>, English race car driver and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiff_<PERSON>ell"}]}, {"year": "1952", "text": "<PERSON>, American lawyer and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American heavy metal singer-songwriter (d. 2007)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American heavy metal singer-songwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American heavy metal singer-songwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Puerto Rican-American boxer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Wilfredo_G%C3%B3mez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Puerto Rican-American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilfredo_G%C3%B3mez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Puerto Rican-American boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilfredo_G%C3%B3mez"}]}, {"year": "1957", "text": "<PERSON>, American actor, voice artist, comedian, singer and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, comedian, singer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, comedian, singer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovak painter, sculptor, and illustrator", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Bla%C5%BEej_Bal%C3%A1%C5%BE\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak painter, sculptor, and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bla%C5%BEej_Bal%C3%A1%C5%BE\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak painter, sculptor, and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bla%C5%BEej_Bal%C3%A1%C5%BE"}]}, {"year": "1958", "text": "<PERSON>, American journalist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American basketball player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, English actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hughes"}]}, {"year": "1959", "text": "<PERSON>, Tanzanian politician, 5th President of Tanzania (d. 2021)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Tanzania", "link": "https://wikipedia.org/wiki/President_of_Tanzania"}]}, {"year": "1960", "text": "<PERSON>, American football player and athlete", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(nose_tackle)\" title=\"<PERSON> (nose tackle)\"><PERSON></a>, American football player and athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(nose_tackle)\" title=\"<PERSON> (nose tackle)\"><PERSON></a>, American football player and athlete", "links": [{"title": "<PERSON> (nose tackle)", "link": "https://wikipedia.org/wiki/<PERSON>_(nose_tackle)"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Italian physicist and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>abi<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, German footballer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and dancer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Jacksons)\" class=\"mw-redirect\" title=\"<PERSON> (The Jacksons)\"><PERSON></a>, American singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Jacksons)\" class=\"mw-redirect\" title=\"<PERSON> (The Jacksons)\"><PERSON></a>, American singer-songwriter and dancer", "links": [{"title": "<PERSON> (The Jacksons)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Jacksons)"}]}, {"year": "1961", "text": "<PERSON>, American ice hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Icelandic singer, trumpet player, and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Einar_%C3%96<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>ar <PERSON>\"><PERSON><PERSON></a>, Icelandic singer, trumpet player, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ar_%C3%96<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON>ar <PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic singer, trumpet player, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Einar_%C3%96<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, English model", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ya<PERSON><PERSON>\">Ya<PERSON><PERSON></a>, English model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ya<PERSON><PERSON>\">Ya<PERSON><PERSON></a>, English model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian businessman and television host", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby league player and television host", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American linguist and academic", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American reality television star (d. 2019)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bounty_hunter)\" class=\"mw-redirect\" title=\"<PERSON> (bounty hunter)\"><PERSON></a>, American reality television star (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bounty_hunter)\" class=\"mw-redirect\" title=\"<PERSON> (bounty hunter)\"><PERSON></a>, American reality television star (d. 2019)", "links": [{"title": "<PERSON> (bounty hunter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bounty_hunter)"}]}, {"year": "1967", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American actress and director", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Norwegian speed skater and physician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian speed skater and physician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian speed skater and physician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English director and playwright", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatre_director)\" title=\"<PERSON> (theatre director)\"><PERSON></a>, English director and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatre_director)\" title=\"<PERSON> (theatre director)\"><PERSON></a>, English director and playwright", "links": [{"title": "<PERSON> (theatre director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatre_director)"}]}, {"year": "1969", "text": "<PERSON>, American photographer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Dutch footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian ice hockey player and coach and lawyer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Estonian physicist and academic", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lt\" title=\"<PERSON><PERSON>lt\"><PERSON><PERSON></a>, Estonian physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lt\" title=\"<PERSON><PERSON>lt\"><PERSON><PERSON></a>, Estonian physicist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Reivelt"}]}, {"year": "1970", "text": "<PERSON>, English keyboardist and songwriter (d. 2017)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboardist and songwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboardist and songwriter (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Dutch footballer and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American mathematician, cryptologist, and academic", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, cryptologist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, cryptologist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian cricketer, coach, and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian cricketer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ryder\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ryder\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ryder"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese businessman, founded Livedoor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ka<PERSON><PERSON> Horie\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Livedoor\" title=\"Livedoor\">Livedoor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ho<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Livedoor\" title=\"Livedoor\">Livedoor</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Livedoor", "link": "https://wikipedia.org/wiki/Livedoor"}]}, {"year": "1972", "text": "<PERSON>, American actress and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gabrielle Union\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gabrielle_<PERSON>\" title=\"Gabrielle Union\"><PERSON></a>, American actress and producer", "links": [{"title": "Gabrielle <PERSON>", "link": "https://wikipedia.org/wiki/Gabrielle_Union"}]}, {"year": "1973", "text": "<PERSON>, South African cricketer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American bobsledder, sprinter, and long jumper", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bobsledder, sprinter, and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bobsledder, sprinter, and long jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/%C3%89ric_Messier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89ric_Messier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ric_Messier"}]}, {"year": "1973", "text": "<PERSON>, French footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English cricketer and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Indonesian activist and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>d\"><PERSON><PERSON></a>, Indonesian activist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ye<PERSON>_<PERSON>d"}]}, {"year": "1975", "text": "<PERSON>, Iranian-American comedian, games developer, businessman, and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Baba Ali\"><PERSON></a>, Iranian-American comedian, games developer, businessman, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ali\"><PERSON></a>, Iranian-American comedian, games developer, businessman, and actor", "links": [{"title": "Baba <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Irish footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American actress, singer, and dancer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Indian actor, director, and choreographer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and choreographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Irish guitarist (d. 2023)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Vag<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ag<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ag<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vaggel<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Catalan lawyer and politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Igna<PERSON>_Gim%C3%A9nez_Renom\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Catalan lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_Gim%C3%A9nez_Renom\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Catalan lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Igna<PERSON>_Gim%C3%A9nez_Renom"}]}, {"year": "1979", "text": "Andrew<PERSON><PERSON>, English actor, director, and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actor, director, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Nadej<PERSON>_<PERSON>\" title=\"Nadej<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nadej<PERSON>_<PERSON>\" title=\"Nadej<PERSON>\">Na<PERSON><PERSON><PERSON></a>, Belarusian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nadejda_Ostrovskaya"}]}, {"year": "1980", "text": "<PERSON><PERSON>, New Zealand-Italian rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Italian rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Filipino actress and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Greek footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fota<PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Fotakis"}]}, {"year": "1982", "text": "<PERSON>, Taiwanese actress and singer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Syrian revolutionary and military leader, Transitional President of Syria", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian revolutionary and military leader, Transitional President of Syria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian revolutionary and military leader, Transitional President of Syria", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Canadian model and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Welsh footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%A9my_<PERSON>ieu"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Turkish weightlifter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Nurcan_Ta<PERSON>n\" title=\"Nurcan Taylan\"><PERSON><PERSON><PERSON></a>, Turkish weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nurcan_Tayla<PERSON>\" title=\"Nurcan Taylan\"><PERSON><PERSON><PERSON></a>, Turkish weightlifter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nurcan_<PERSON><PERSON>n"}]}, {"year": "1984", "text": "<PERSON>, American bass player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, South Korean actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Welsh footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English motorcycle racer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Cal_Crutchlow\" title=\"Cal Crutchlow\"><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cal_Crutchlow\" title=\"Cal Crutchlow\"><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cal_Crutchlow"}]}, {"year": "1985", "text": "<PERSON>, English actress and dancer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Mexican singer-songwriter and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ximena_Sari%C3%B1ana\" title=\"Ximena Sariñana\"><PERSON><PERSON><PERSON></a>, Mexican singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ximena_Sari%C3%B1ana\" title=\"Ximen<PERSON> Sariñana\"><PERSON><PERSON><PERSON></a>, Mexican singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ximena_Sari%C3%B1ana"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian boxer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Nataly Dawn\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Nataly Dawn\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nataly_Dawn"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Filipino actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Sarita_P%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sarita_P%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sarita_P%C3%A9<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Italia_Ricci\" title=\"Italia Ricci\"><PERSON> Ricci</a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italia_Ricci\" title=\"Italia Ricci\"><PERSON> Ricci</a>, Canadian actress", "links": [{"title": "Italia Ricci", "link": "https://wikipedia.org/wiki/Italia_Ricci"}]}, {"year": "1986", "text": "<PERSON>, American actor and model", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian figure skater", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Swedish singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tove Lo\"><PERSON><PERSON></a>, Swedish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tove Lo\"><PERSON><PERSON></a>, Swedish singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/To<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Romanian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Florin_Gardo%C8%99\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florin_G<PERSON>o%C8%99\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Florin_Gardo%C8%99"}]}, {"year": "1988", "text": "<PERSON>, English golfer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, New Zealand cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Roman <PERSON>\"><PERSON></a>, New Zealand cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Roman <PERSON>\"><PERSON></a>, New Zealand cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Chilean anthropologist and political scientist, First Lady of Chile", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean anthropologist and political scientist, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Chile\" title=\"First Lady of Chile\">First Lady of Chile</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean anthropologist and political scientist, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Chile\" title=\"First Lady of Chile\">First Lady of Chile</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "First Lady of Chile", "link": "https://wikipedia.org/wiki/First_Lady_of_Chile"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian ski jumper and cyclist", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Primo%C5%BE_Rogli%C4%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian ski jumper and cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Primo%C5%BE_Rogli%C4%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian ski jumper and cyclist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Primo%C5%BE_Rogli%C4%8D"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Venezuelan baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Ender_<PERSON>iarte\" title=\"Ender Inciarte\"><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ender_<PERSON>iarte\" title=\"Ender Inciarte\"><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>iarte"}]}, {"year": "1990", "text": "<PERSON>, Canadian filmmaker", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>foot"}]}, {"year": "1990", "text": "<PERSON>, Swedish singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, New Zealand dancer and choreographer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand dancer and choreographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, French basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1992)\" title=\"<PERSON> (ice hockey, born 1992)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1992)\" title=\"<PERSON> (ice hockey, born 1992)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1992)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1992)"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Hungarian tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/%C3%81gne<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81gne<PERSON>_<PERSON><PERSON>ta"}]}, {"year": "1993", "text": "<PERSON> <PERSON><PERSON><PERSON>, American actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/India_E<PERSON>ley\" title=\"India Eisley\">India <PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/India_E<PERSON>ley\" title=\"India Eisley\">India <PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "India Eisley", "link": "https://wikipedia.org/wiki/India_E<PERSON>ley"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Norwegian singer and songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>trid_S\" title=\"Astrid S\"><PERSON><PERSON><PERSON></a>, Norwegian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>tri<PERSON>_<PERSON>\" title=\"Astrid S\"><PERSON><PERSON><PERSON></a>, Norwegian singer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>trid_<PERSON>"}]}], "Deaths": [{"year": "1050", "text": "<PERSON><PERSON><PERSON>, archbishop of Canterbury", "html": "1050 - <a href=\"https://wikipedia.org/wiki/Eadsige\" title=\"Eadsige\"><PERSON><PERSON><PERSON></a>, archbishop of Canterbury", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eadsige\" title=\"Eadsige\"><PERSON><PERSON><PERSON></a>, archbishop of Canterbury", "links": [{"title": "Eadsige", "link": "https://wikipedia.org/wiki/Eadsige"}]}, {"year": "1266", "text": "<PERSON> of Austria, Queen of Bohemia (b. 1204)", "html": "1266 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Queen_of_Bohemia\" title=\"<PERSON> of Austria, Queen of Bohemia\"><PERSON> of Austria, Queen of Bohemia</a> (b. 1204)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Queen_of_Bohemia\" title=\"<PERSON> of Austria, Queen of Bohemia\"><PERSON> of Austria, Queen of Bohemia</a> (b. 1204)", "links": [{"title": "<PERSON> of Austria, Queen of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Austria,_Queen_of_Bohemia"}]}, {"year": "1268", "text": "<PERSON><PERSON>, King of Sicily (b. 1252)", "html": "1268 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/List_of_monarchs_of_Sicily\" class=\"mw-redirect\" title=\"List of monarchs of Sicily\">Sicily</a> (b. 1252)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/List_of_monarchs_of_Sicily\" class=\"mw-redirect\" title=\"List of monarchs of Sicily\">Sicily</a> (b. 1252)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>in"}, {"title": "List of monarchs of Sicily", "link": "https://wikipedia.org/wiki/List_of_monarchs_of_Sicily"}]}, {"year": "1268", "text": "<PERSON>, Margrave of Baden (b. 1249)", "html": "1268 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden\" title=\"<PERSON>, Margrave of Baden\"><PERSON>, Margrave of Baden</a> (b. 1249)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden\" title=\"<PERSON>, Margrave of Baden\"><PERSON>, Margrave of Baden</a> (b. 1249)", "links": [{"title": "<PERSON>, Margrave of Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden"}]}, {"year": "1321", "text": "<PERSON>, King of Serbia (b. 1253)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, King of Serbia (b. 1253)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, King of Serbia (b. 1253)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1339", "text": "<PERSON> of Tver, Grand Prince of Vladimir (b. 1301)", "html": "1339 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Tver\" class=\"mw-redirect\" title=\"<PERSON> of Tver\"><PERSON> of Tver</a>, <a href=\"https://wikipedia.org/wiki/Grand_Prince_of_Vladimir\" title=\"Grand Prince of Vladimir\">Grand Prince of Vladimir</a> (b. 1301)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Tver\" class=\"mw-redirect\" title=\"<PERSON> of Tver\"><PERSON> of Tver</a>, <a href=\"https://wikipedia.org/wiki/Grand_Prince_of_Vladimir\" title=\"Grand Prince of Vladimir\">Grand Prince of Vladimir</a> (b. 1301)", "links": [{"title": "<PERSON> of Tver", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Tver"}, {"title": "Grand Prince of Vladimir", "link": "https://wikipedia.org/wiki/Grand_Prince_of_Vladimir"}]}, {"year": "1590", "text": "<PERSON><PERSON><PERSON>, Dutch philosopher, theologian, and politician (b. 1522)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch philosopher, theologian, and politician (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch philosopher, theologian, and politician (b. 1522)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}]}, {"year": "1618", "text": "<PERSON>, English admiral, explorer, and politician, Lieutenant Governor of Jersey (b. 1554)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral, explorer, and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jersey\" title=\"Lieutenant Governor of Jersey\">Lieutenant Governor of Jersey</a> (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral, explorer, and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jersey\" title=\"Lieutenant Governor of Jersey\">Lieutenant Governor of Jersey</a> (b. 1554)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Raleigh"}, {"title": "Lieutenant Governor of Jersey", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Jersey"}]}, {"year": "1650", "text": "<PERSON>, Scottish historian and theologian (b. 1575)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and theologian (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and theologian (b. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1666", "text": "<PERSON> the Elder, English minister and activist (b. 1600)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, English minister and activist (b. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, English minister and activist (b. 1600)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}]}, {"year": "1666", "text": "<PERSON>, English dramatist (b. 1596)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dramatist (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dramatist (b. 1596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, French mathematician, physicist, and philosopher (b. 1717)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, and philosopher (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, and philosopher (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27A<PERSON><PERSON>"}]}, {"year": "1804", "text": "<PERSON>, English Methodist preacher (b. 1729)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Methodism", "link": "https://wikipedia.org/wiki/Methodism"}]}, {"year": "1829", "text": "<PERSON>, Austrian pianist (b. 1751)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist (b. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Maltese trader and explorer (b. 1821)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese trader and explorer (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese trader and explorer (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American general and KKK leader (b. 1821)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and <a href=\"https://wikipedia.org/wiki/KKK\" class=\"mw-redirect\" title=\"KK<PERSON>\"><PERSON><PERSON><PERSON></a> leader (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and <a href=\"https://wikipedia.org/wiki/KKK\" class=\"mw-redirect\" title=\"KK<PERSON>\"><PERSON><PERSON><PERSON></a> leader (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "KKK", "link": "https://wikipedia.org/wiki/KKK"}]}, {"year": "1892", "text": "<PERSON>, American painter (b. 1848)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American journalist, philosopher, and economist (b. 1839)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, philosopher, and economist (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, philosopher, and economist (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American assassin of <PERSON> (b. 1873)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Canadian weight thrower and shot putter (b. 1873)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian weight thrower and shot putter (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian weight thrower and shot putter (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Hungarian-American publisher, lawyer, and politician, founded Pulitzer, Inc. (b. 1847)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American publisher, lawyer, and politician, founded <a href=\"https://wikipedia.org/wiki/Pulitzer,_Inc.\" title=\"Pulitzer, Inc.\">Pulitzer, Inc.</a> (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American publisher, lawyer, and politician, founded <a href=\"https://wikipedia.org/wiki/Pulitzer,_Inc.\" title=\"Pulitzer, Inc.\">Pulitzer, Inc.</a> (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pulitzer, Inc.", "link": "https://wikipedia.org/wiki/Pulitzer,_Inc."}]}, {"year": "1916", "text": "<PERSON>, American lawyer and politician, 21st Governor of Arkansas (b. 1851)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1918", "text": "<PERSON>, Estonian-German organist and composer (b. 1873)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German organist and composer (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German organist and composer (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Canadian preacher, theologian, and author, founded the Christian and Missionary Alliance (b. 1843)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian preacher, theologian, and author, founded the <a href=\"https://wikipedia.org/wiki/Christian_and_Missionary_Alliance\" class=\"mw-redirect\" title=\"Christian and Missionary Alliance\">Christian and Missionary Alliance</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian preacher, theologian, and author, founded the <a href=\"https://wikipedia.org/wiki/Christian_and_Missionary_Alliance\" class=\"mw-redirect\" title=\"Christian and Missionary Alliance\">Christian and Missionary Alliance</a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Christian and Missionary Alliance", "link": "https://wikipedia.org/wiki/Christian_and_Missionary_Alliance"}]}, {"year": "1924", "text": "<PERSON>, English-American novelist and playwright (b. 1849)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American novelist and playwright (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American novelist and playwright (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, French neurologist and academic (b. 1857)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French neurologist and academic (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French neurologist and academic (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, French physician, bacteriologist, and immunologist (b. 1863)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, bacteriologist, and immunologist (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, bacteriologist, and immunologist (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American painter and illustrator (b. 1867)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, French mathematician and politician, 84th Prime Minister of France (b. 1853)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician and politician, 84th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician and politician, 84th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Spanish journalist and theorist (b. 1874)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish journalist and theorist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>tu\"><PERSON><PERSON></a>, Spanish journalist and theorist (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American historian and academic (b. 1864)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player (b. 1897)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American politician and lawyer (b. 1882)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and lawyer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and lawyer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>ne"}]}, {"year": "1949", "text": "<PERSON>, Armenian-French monk, psychologist, and philosopher (b. 1872)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-French monk, psychologist, and philosopher (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-French monk, psychologist, and philosopher (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, British chemist (b. 1875)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON> Sweden (b. 1858)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Gustaf_V_of_Sweden\" class=\"mw-redirect\" title=\"Gustaf V of Sweden\">Gustaf V of Sweden</a> (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustaf_V_of_Sweden\" class=\"mw-redirect\" title=\"Gustaf V of Sweden\">Gustaf V of Sweden</a> (b. 1858)", "links": [{"title": "Gustaf V of Sweden", "link": "https://wikipedia.org/wiki/Gustaf_V_of_Sweden"}]}, {"year": "1951", "text": "<PERSON>, American astronomer (b. 1864)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American pianist (b. 1922)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, French race car driver (b. 1905)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Belarusian-American production manager and producer (b. 1885)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American production manager and producer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American production manager and producer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American author, poet, and playwright (b. 1886)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Akins\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, poet, and playwright (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Akins\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, poet, and playwright (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zo%C3%AB_Akins"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Danish actress (b. 1893)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish actress (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish actress (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1890)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1946)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Swedish biochemist and academic, Nobel Prize laureate (b. 1902)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1975", "text": "<PERSON>, British chemist (b. 1898)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 41st <PERSON><PERSON><PERSON><PERSON> (b. 1926)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Masanobu\" title=\"<PERSON><PERSON><PERSON><PERSON> Masanobu\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 41st <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Masanobu\" title=\"<PERSON><PERSON><PERSON><PERSON> Masanobu\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 41st <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chiyonoyama_<PERSON>u"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1980", "text": "<PERSON>, Maltese lawyer and politician, 7th Prime Minister of Malta (b. 1911)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Maltese lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Maltese lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giorgio_Bor%C4%A1_Olivier"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1981", "text": "<PERSON>, French singer-songwriter and guitarist (b. 1921)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Soviet physicist (b. 1915)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet physicist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet physicist (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ev<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Greek actor, singer, and academic (b. 1913)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor, singer, and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor, singer, and academic (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ulos"}]}, {"year": "1987", "text": "<PERSON>, American singer, clarinet player, saxophonist, and bandleader (b. 1913)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, clarinet player, saxophonist, and bandleader (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Herman\"><PERSON></a>, American singer, clarinet player, saxophonist, and bandleader (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Indian author and activist (b. 1903)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hyay\" title=\"<PERSON><PERSON><PERSON>hyay\"><PERSON><PERSON><PERSON></a>, Indian author and activist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hyay\" title=\"<PERSON><PERSON><PERSON>hyay\"><PERSON><PERSON><PERSON></a>, Indian author and activist (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Chattopadhyay"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Latvian-American mathematician and academic (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian-American mathematician and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian-American mathematician and academic (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Israeli rabbi, general, and scholar (b. 1918)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli rabbi, general, and scholar (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli rabbi, general, and scholar (b. 1918)", "links": [{"title": "S<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shlomo_<PERSON>n"}]}, {"year": "1995", "text": "<PERSON>, American novelist, essayist, screenwriter (b. 1924)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Terry Southern\"><PERSON></a>, American novelist, essayist, screenwriter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Estonian composer and educator (b. 1908)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and educator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and educator (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American occultist, founded the Church of Satan (b. 1930)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American occultist, founded the <a href=\"https://wikipedia.org/wiki/Church_of_Satan\" title=\"Church of Satan\">Church of Satan</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American occultist, founded the <a href=\"https://wikipedia.org/wiki/Church_of_Satan\" title=\"Church of Satan\">Church of Satan</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Church of Satan", "link": "https://wikipedia.org/wiki/Church_of_Satan"}]}, {"year": "1997", "text": "<PERSON>, Greek-American astronomer and astrophysicist (b. 1947)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American astronomer and astrophysicist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American astronomer and astrophysicist (b. 1947)", "links": [{"title": "Andreas Gerasi<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_G<PERSON>si<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Turkish-French pianist and composer (b. 1908)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-French pianist and composer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-French pianist and composer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Belgian author and illustrator (b. 1931)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Belgian author and illustrator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Belgian author and illustrator (b. 1931)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "2000", "text": "<PERSON>, Argentinian pianist and composer (b. 1912)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist and composer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist and composer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian-American animator (b. 1960)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American animator (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American animator (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American pilot, author, and educator (b. 1922)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, author, and educator (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, author, and educator (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Italian tenor and actor (b. 1921)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "Princess <PERSON>, Duchess of Gloucester (b. 1901)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester\" title=\"Princess <PERSON>, Duchess of Gloucester\">Princess <PERSON>, Duchess of Gloucester</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester\" title=\"Princess <PERSON>, Duchess of Gloucester\">Princess <PERSON>, Duchess of Gloucester</a> (b. 1901)", "links": [{"title": "Princess <PERSON>, Duchess of Gloucester", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Turkish physicist and academic (b. 1946)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Ordal_De<PERSON>kan\" title=\"Ordal Demokan\"><PERSON><PERSON></a>, Turkish physicist and academic (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ordal_De<PERSON>kan\" title=\"Ordal Demokan\"><PERSON><PERSON></a>, Turkish physicist and academic (b. 1946)", "links": [{"title": "Ordal Demokan", "link": "https://wikipedia.org/wiki/Ordal_Demokan"}]}, {"year": "2004", "text": "<PERSON>, Dominican lawyer and politician, Premier of Dominica (b. 1923)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Dominica\" title=\"List of heads of government of Dominica\">Premier of Dominica</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Dominica\" title=\"List of heads of government of Dominica\">Premier of Dominica</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of government of Dominica", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Dominica"}]}, {"year": "2004", "text": "<PERSON>, English mathematician and entomologist (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and entomologist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and entomologist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian-American actor (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Romanian sculptor and illustrator (b. 1903)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian sculptor and illustrator (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian sculptor and illustrator (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American singer-songwriter (b. 1963)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1963)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "2011", "text": "<PERSON>, English radio and television host (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American etiquette expert and author (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Letitia_<PERSON>ldrige\" title=\"Letitia Baldrige\"><PERSON><PERSON><PERSON></a>, American etiquette expert and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>ld<PERSON>\" title=\"Letitia Baldrige\">Let<PERSON><PERSON></a>, American etiquette expert and author (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>e"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Dutch author, poet, and songwriter (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author, poet, and songwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author, poet, and songwriter (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American academic (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English-American astronomer and academic (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American astronomer and academic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American astronomer and academic (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American boxer and diplomat (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American boxer and diplomat (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American boxer and diplomat (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Haitian priest and politician, Foreign Minister of Haiti (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nald_Cl%C3%A9rism%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Haitian priest and politician, <a href=\"https://wikipedia.org/wiki/Foreign_Ministers_of_Haiti\" class=\"mw-redirect\" title=\"Foreign Ministers of Haiti\">Foreign Minister of Haiti</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nald_Cl%C3%A9rism%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Haitian priest and politician, <a href=\"https://wikipedia.org/wiki/Foreign_Ministers_of_Haiti\" class=\"mw-redirect\" title=\"Foreign Ministers of Haiti\">Foreign Minister of Haiti</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nald_Cl%C3%A9rism%C3%A9"}, {"title": "Foreign Ministers of Haiti", "link": "https://wikipedia.org/wiki/Foreign_Ministers_of_Haiti"}]}, {"year": "2013", "text": "<PERSON>, American director and producer (b. 1957)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American soldier and engineer (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(frogman)\" title=\"<PERSON> (frogman)\"><PERSON></a>, American soldier and engineer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(frogman)\" title=\"<PERSON> (frogman)\"><PERSON></a>, American soldier and engineer (b. 1918)", "links": [{"title": "<PERSON> (frogman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(frogman)"}]}, {"year": "2013", "text": "<PERSON>, English actor, director, producer, and screenwriter (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician (b. 1965)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American lawyer and politician (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American lawyer and politician (b. 1965)", "links": [{"title": "<PERSON> (American politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Swedish footballer and manager (b. 1968)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer and manager (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer and manager (b. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON>, American businessman (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1953)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Filipino businessman and politician (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Filipino businessman and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Filipino businessman and politician (b. 1942)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "2015", "text": "<PERSON>, Slovene basketball player and coach (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Slovene basketball player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Slovene basketball player and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_<PERSON>%C4%8Di%C4%8D"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Serbian basketball player and coach (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ranko_%C5%BDeravica\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranko_%C5%BDeravica\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player and coach (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ranko_%C5%BDeravica"}]}, {"year": "2019", "text": "<PERSON>, American actor and comedian (b. 1942)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and comedian (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and comedian (b. 1942)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Austrian-American molecular and cell biologist (b. 1967)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Am<PERSON>\"><PERSON><PERSON></a>, Austrian-American molecular and cell biologist (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Am<PERSON>\"><PERSON><PERSON></a>, Austrian-American molecular and cell biologist (b. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>on"}]}, {"year": "2021", "text": "<PERSON>, Australian cricketer (b. 1945)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Japanese musician and singer-songwriter (b. 1968)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Japanese musician and singer-songwriter (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Japanese musician and singer-songwriter (b. 1968)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American actress and comedian (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}]}]}}