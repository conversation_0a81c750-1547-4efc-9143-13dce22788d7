{"date": "May 9", "url": "https://wikipedia.org/wiki/May_9", "data": {"Events": [{"year": "328", "text": "<PERSON><PERSON><PERSON> is elected Patriarch of Alexandria.", "html": "328 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/Patriarch_of_Alexandria\" title=\"Patriarch of Alexandria\">Patriarch of Alexandria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/Patriarch_of_Alexandria\" title=\"Patriarch of Alexandria\">Patriarch of Alexandria</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria"}, {"title": "Patriarch of Alexandria", "link": "https://wikipedia.org/wiki/Patriarch_of_Alexandria"}]}, {"year": "1009", "text": "Lombard Revolt: Lombard forces led by <PERSON><PERSON> revolt in Bari against the Byzantine Catepanate of Italy.", "html": "1009 - <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_southern_Italy#Lombard_revolt,_1009-1022\" title=\"Norman conquest of southern Italy\">Lombard Revolt</a>: Lombard forces led by <a href=\"https://wikipedia.org/wiki/Melus_of_Bari\" title=\"<PERSON><PERSON> of Bari\"><PERSON><PERSON></a> revolt in <a href=\"https://wikipedia.org/wiki/Bari\" title=\"Bari\">Bari</a> against the Byzantine <a href=\"https://wikipedia.org/wiki/Catepanate_of_Italy\" title=\"Catepanate of Italy\">Catepanate of Italy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_conquest_of_southern_Italy#Lombard_revolt,_1009-1022\" title=\"Norman conquest of southern Italy\">Lombard Revolt</a>: Lombard forces led by <a href=\"https://wikipedia.org/wiki/Melus_of_Bari\" title=\"<PERSON><PERSON> of Bari\"><PERSON><PERSON></a> revolt in <a href=\"https://wikipedia.org/wiki/Bari\" title=\"Bari\">Bari</a> against the Byzantine <a href=\"https://wikipedia.org/wiki/Catepanate_of_Italy\" title=\"Catepanate of Italy\">Catepanate of Italy</a>.", "links": [{"title": "Norman conquest of southern Italy", "link": "https://wikipedia.org/wiki/Norman_conquest_of_southern_Italy#Lombard_revolt,_1009-1022"}, {"title": "<PERSON><PERSON> of Bari", "link": "https://wikipedia.org/wiki/Melus_of_Bari"}, {"title": "Bari", "link": "https://wikipedia.org/wiki/Bari"}, {"title": "Catepanate of Italy", "link": "https://wikipedia.org/wiki/Catepanate_of_Italy"}]}, {"year": "1386", "text": "England and Portugal formally ratify their alliance with the signing of the Treaty of Windsor, making it the oldest diplomatic alliance in the world which is still in force.", "html": "1386 - <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a> and <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> formally ratify <a href=\"https://wikipedia.org/wiki/Anglo-Portuguese_Alliance\" title=\"Anglo-Portuguese Alliance\">their alliance</a> with the signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Windsor_(1386)\" title=\"Treaty of Windsor (1386)\">Treaty of Windsor</a>, making it the oldest diplomatic alliance in the world which is still in force.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a> and <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> formally ratify <a href=\"https://wikipedia.org/wiki/Anglo-Portuguese_Alliance\" title=\"Anglo-Portuguese Alliance\">their alliance</a> with the signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Windsor_(1386)\" title=\"Treaty of Windsor (1386)\">Treaty of Windsor</a>, making it the oldest diplomatic alliance in the world which is still in force.", "links": [{"title": "England", "link": "https://wikipedia.org/wiki/England"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}, {"title": "Anglo-Portuguese Alliance", "link": "https://wikipedia.org/wiki/Anglo-Portuguese_Alliance"}, {"title": "Treaty of Windsor (1386)", "link": "https://wikipedia.org/wiki/Treaty_of_Windsor_(1386)"}]}, {"year": "1450", "text": "Timurid monarch '<PERSON> is assassinated.", "html": "1450 - <a href=\"https://wikipedia.org/wiki/Timurid_dynasty\" title=\"Timurid dynasty\"><PERSON><PERSON><PERSON></a> monarch <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\">'<PERSON></a> is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Timurid_dynasty\" title=\"Timurid dynasty\"><PERSON><PERSON><PERSON></a> monarch <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\">'<PERSON></a> is assassinated.", "links": [{"title": "Timurid dynasty", "link": "https://wikipedia.org/wiki/Timurid_dynasty"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1540", "text": "<PERSON><PERSON><PERSON> sets sail on an expedition to the Gulf of California.", "html": "1540 - <a href=\"https://wikipedia.org/wiki/Hernan<PERSON>_de_Alarc%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> sets sail on an expedition to the <a href=\"https://wikipedia.org/wiki/Gulf_of_California\" title=\"Gulf of California\">Gulf of California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hernan<PERSON>_de_Alarc%C3%B3n\" title=\"<PERSON>nan<PERSON>\"><PERSON><PERSON><PERSON></a> sets sail on an expedition to the <a href=\"https://wikipedia.org/wiki/Gulf_of_California\" title=\"Gulf of California\">Gulf of California</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hernan<PERSON>_de_Alarc%C3%B3n"}, {"title": "Gulf of California", "link": "https://wikipedia.org/wiki/Gulf_of_California"}]}, {"year": "1662", "text": "The figure who later became Mr. <PERSON> makes his first recorded appearance in England.", "html": "1662 - The figure who later became <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\">Mr. <PERSON></a> makes his first recorded appearance in England.", "no_year_html": "The figure who later became <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\">Mr. <PERSON></a> makes his first recorded appearance in England.", "links": [{"title": "<PERSON> and Judy", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>"}]}, {"year": "1671", "text": "<PERSON>, disguised as a clergyman, attempts to steal England's Crown Jewels from the Tower of London.", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Blood\"><PERSON></a>, disguised as a <a href=\"https://wikipedia.org/wiki/Clergyman\" class=\"mw-redirect\" title=\"Clergyman\">clergyman</a>, attempts to steal England's <a href=\"https://wikipedia.org/wiki/Crown_Jewels_of_the_United_Kingdom\" title=\"Crown Jewels of the United Kingdom\">Crown Jewels</a> from the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Blood\"><PERSON></a>, disguised as a <a href=\"https://wikipedia.org/wiki/Clergyman\" class=\"mw-redirect\" title=\"Clergyman\">clergyman</a>, attempts to steal England's <a href=\"https://wikipedia.org/wiki/Crown_Jewels_of_the_United_Kingdom\" title=\"Crown Jewels of the United Kingdom\">Crown Jewels</a> from the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Clergyman", "link": "https://wikipedia.org/wiki/Clergyman"}, {"title": "Crown Jewels of the United Kingdom", "link": "https://wikipedia.org/wiki/Crown_Jewels_of_the_United_Kingdom"}, {"title": "Tower of London", "link": "https://wikipedia.org/wiki/Tower_of_London"}]}, {"year": "1726", "text": "Five men arrested during a raid on <PERSON>'s molly house in London are executed at Tyburn.", "html": "1726 - Five men arrested during a raid on <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Molly_house\" title=\"Molly house\">molly house</a> in London are executed at <a href=\"https://wikipedia.org/wiki/Tyburn,_London\" class=\"mw-redirect\" title=\"Tyburn, London\">Tyburn</a>.", "no_year_html": "Five men arrested during a raid on <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Molly_house\" title=\"Molly house\">molly house</a> in London are executed at <a href=\"https://wikipedia.org/wiki/Tyburn,_London\" class=\"mw-redirect\" title=\"Tyburn, London\">Tyburn</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Molly house", "link": "https://wikipedia.org/wiki/<PERSON>_house"}, {"title": "Tyburn, London", "link": "https://wikipedia.org/wiki/Tyburn,_London"}]}, {"year": "1761", "text": "Exhibition of 1761, the innaugaral exhibition of the Society of Artists of Great Britain opens at Spring Gardens in London.", "html": "1761 - <a href=\"https://wikipedia.org/wiki/Exhibition_of_1761\" title=\"Exhibition of 1761\">Exhibition of 1761</a>, the innaugaral exhibition of the <a href=\"https://wikipedia.org/wiki/Society_of_Artists_of_Great_Britain\" title=\"Society of Artists of Great Britain\">Society of Artists of Great Britain</a> opens at <a href=\"https://wikipedia.org/wiki/Spring_Gardens\" title=\"Spring Gardens\">Spring Gardens</a> in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Exhibition_of_1761\" title=\"Exhibition of 1761\">Exhibition of 1761</a>, the innaugaral exhibition of the <a href=\"https://wikipedia.org/wiki/Society_of_Artists_of_Great_Britain\" title=\"Society of Artists of Great Britain\">Society of Artists of Great Britain</a> opens at <a href=\"https://wikipedia.org/wiki/Spring_Gardens\" title=\"Spring Gardens\">Spring Gardens</a> in London.", "links": [{"title": "Exhibition of 1761", "link": "https://wikipedia.org/wiki/Exhibition_of_1761"}, {"title": "Society of Artists of Great Britain", "link": "https://wikipedia.org/wiki/Society_of_Artists_of_Great_Britain"}, {"title": "Spring Gardens", "link": "https://wikipedia.org/wiki/Spring_Gardens"}]}, {"year": "1864", "text": "Second Schleswig War: The Danish navy defeats the Austrian and Prussian fleets in the Battle of Heligoland.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Second_Schleswig_War\" title=\"Second Schleswig War\">Second Schleswig War</a>: The Danish navy defeats the <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> fleets in the <a href=\"https://wikipedia.org/wiki/Battle_of_Heligoland_(1864)\" title=\"Battle of Heligoland (1864)\">Battle of Heligoland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Schleswig_War\" title=\"Second Schleswig War\">Second Schleswig War</a>: The Danish navy defeats the <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> fleets in the <a href=\"https://wikipedia.org/wiki/Battle_of_Heligoland_(1864)\" title=\"Battle of Heligoland (1864)\">Battle of Heligoland</a>.", "links": [{"title": "Second Schleswig War", "link": "https://wikipedia.org/wiki/Second_Schleswig_War"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}, {"title": "Battle of Heligoland (1864)", "link": "https://wikipedia.org/wiki/Battle_of_Heligoland_(1864)"}]}, {"year": "1865", "text": "American Civil War: <PERSON> surrenders his forces at Gainesville, Alabama.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> surrenders his forces at <a href=\"https://wikipedia.org/wiki/Gainesville,_Alabama\" title=\"Gainesville, Alabama\">Gainesville, Alabama</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> surrenders his forces at <a href=\"https://wikipedia.org/wiki/Gainesville,_Alabama\" title=\"Gainesville, Alabama\">Gainesville, Alabama</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Gainesville, Alabama", "link": "https://wikipedia.org/wiki/Gainesville,_Alabama"}]}, {"year": "1865", "text": "American Civil War: President <PERSON> issues a proclamation ending belligerent rights of the rebels and enjoining foreign nations to intern or expel Confederate ships.", "html": "1865 - American Civil War: <a href=\"https://wikipedia.org/wiki/President_<PERSON>\" class=\"mw-redirect\" title=\"President <PERSON>\">President <PERSON></a> issues a <a href=\"https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War#<PERSON>'s_May_9_declaration_(May_9)\" title=\"Conclusion of the American Civil War\">proclamation ending belligerent rights</a> of the rebels and enjoining foreign nations to intern or expel Confederate ships.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/President_<PERSON>\" class=\"mw-redirect\" title=\"President <PERSON>\">President <PERSON></a> issues a <a href=\"https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War#<PERSON>'s_May_9_declaration_(May_9)\" title=\"Conclusion of the American Civil War\">proclamation ending belligerent rights</a> of the rebels and enjoining foreign nations to intern or expel Confederate ships.", "links": [{"title": "President <PERSON>", "link": "https://wikipedia.org/wiki/President_<PERSON>_<PERSON>"}, {"title": "Conclusion of the American Civil War", "link": "https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War#<PERSON>_<PERSON>'s_May_9_declaration_(May_9)"}]}, {"year": "1873", "text": "Der Krach: The Vienna stock exchange crash heralds the Long Depression.", "html": "1873 - <i><a href=\"https://wikipedia.org/wiki/Panic_of_1873\" title=\"Panic of 1873\"><PERSON></a></i>: The <a href=\"https://wikipedia.org/wiki/Wiener_B%C3%B6rse\" title=\"Wiener Börse\">Vienna stock exchange</a> crash heralds the <a href=\"https://wikipedia.org/wiki/Long_Depression\" title=\"Long Depression\">Long Depression</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Panic_of_1873\" title=\"Panic of 1873\"><PERSON></a></i>: The <a href=\"https://wikipedia.org/wiki/Wiener_B%C3%B6rse\" title=\"Wiener Börse\">Vienna stock exchange</a> crash heralds the <a href=\"https://wikipedia.org/wiki/Long_Depression\" title=\"Long Depression\">Long Depression</a>.", "links": [{"title": "Panic of 1873", "link": "https://wikipedia.org/wiki/Panic_of_1873"}, {"title": "Wiener Börse", "link": "https://wikipedia.org/wiki/Wiener_B%C3%B6rse"}, {"title": "Long Depression", "link": "https://wikipedia.org/wiki/Long_Depression"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON> reads, in the Chamber of Deputies, the Declaration of Independence of Romania. The date will become recognised as the Independence Day of Romania.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kog%C4%83lniceanu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> reads, in the <a href=\"https://wikipedia.org/wiki/Chamber_of_Deputies_(Romania)\" title=\"Chamber of Deputies (Romania)\">Chamber of Deputies</a>, the Declaration of Independence of <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>. The date will become recognised as the Independence Day of Romania.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>g%C4%83lniceanu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> reads, in the <a href=\"https://wikipedia.org/wiki/Chamber_of_Deputies_(Romania)\" title=\"Chamber of Deputies (Romania)\">Chamber of Deputies</a>, the Declaration of Independence of <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>. The date will become recognised as the Independence Day of Romania.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kog%C4%83lniceanu"}, {"title": "Chamber of Deputies (Romania)", "link": "https://wikipedia.org/wiki/Chamber_of_Deputies_(Romania)"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}]}, {"year": "1901", "text": "Australia opens its first national parliament in Melbourne.", "html": "1901 - Australia opens its first <a href=\"https://wikipedia.org/wiki/Parliament_of_Australia\" title=\"Parliament of Australia\">national parliament</a> in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>.", "no_year_html": "Australia opens its first <a href=\"https://wikipedia.org/wiki/Parliament_of_Australia\" title=\"Parliament of Australia\">national parliament</a> in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>.", "links": [{"title": "Parliament of Australia", "link": "https://wikipedia.org/wiki/Parliament_of_Australia"}, {"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}]}, {"year": "1915", "text": "World War I: Second Battle of Artois between German and French forces.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Artois\" title=\"Second Battle of Artois\">Second Battle of Artois</a> between German and French forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Artois\" title=\"Second Battle of Artois\">Second Battle of Artois</a> between German and French forces.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Second Battle of Artois", "link": "https://wikipedia.org/wiki/Second_Battle_of_Artois"}]}, {"year": "1918", "text": "World War I: Germany repels Britain's second attempt to blockade the port of Ostend, Belgium.", "html": "1918 - World War I: Germany repels Britain's <a href=\"https://wikipedia.org/wiki/Second_Ostend_Raid\" title=\"Second Ostend Raid\">second attempt</a> to blockade the port of <a href=\"https://wikipedia.org/wiki/Ostend\" title=\"Ostend\">Ostend</a>, Belgium.", "no_year_html": "World War I: Germany repels Britain's <a href=\"https://wikipedia.org/wiki/Second_Ostend_Raid\" title=\"Second Ostend Raid\">second attempt</a> to blockade the port of <a href=\"https://wikipedia.org/wiki/Ostend\" title=\"Ostend\">Ostend</a>, Belgium.", "links": [{"title": "Second Ostend Raid", "link": "https://wikipedia.org/wiki/Second_Ostend_Raid"}, {"title": "Ostend", "link": "https://wikipedia.org/wiki/Ostend"}]}, {"year": "1920", "text": "Polish-Soviet War: The Polish army under General <PERSON> celebrates its capture of Kiev with a victory parade on Khreshchatyk.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: The Polish army under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-%C5%9Amig%C5%82y\" title=\"<PERSON>\"><PERSON></a> celebrates its <a href=\"https://wikipedia.org/wiki/Kiev_offensive_(1920)\" title=\"Kiev offensive (1920)\">capture of Kiev</a> with a <a href=\"https://wikipedia.org/wiki/Victory_parade\" title=\"Victory parade\">victory parade</a> on <a href=\"https://wikipedia.org/wiki/Khreshchatyk\" title=\"Khreshchatyk\">Khreshchatyk</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: The Polish army under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-%C5%9Amig%C5%82y\" title=\"<PERSON>\"><PERSON></a> celebrates its <a href=\"https://wikipedia.org/wiki/Kiev_offensive_(1920)\" title=\"Kiev offensive (1920)\">capture of Kiev</a> with a <a href=\"https://wikipedia.org/wiki/Victory_parade\" title=\"Victory parade\">victory parade</a> on <a href=\"https://wikipedia.org/wiki/Khreshchatyk\" title=\"Khreshchatyk\">Khreshchatyk</a>.", "links": [{"title": "Polish-Soviet War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-%C5%9Amig%C5%82y"}, {"title": "Kiev offensive (1920)", "link": "https://wikipedia.org/wiki/Kiev_offensive_(1920)"}, {"title": "Victory parade", "link": "https://wikipedia.org/wiki/Victory_parade"}, {"title": "Khreshchatyk", "link": "https://wikipedia.org/wiki/Khreshchatyk"}]}, {"year": "1926", "text": "Admiral <PERSON> and <PERSON> claim to have flown over the North Pole (later discovery of <PERSON>'s diary appears to cast some doubt on the claim.)", "html": "1926 - Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> claim to have flown over the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a> (later discovery of <PERSON>'s diary appears to cast some doubt on the claim.)", "no_year_html": "Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> claim to have flown over the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a> (later discovery of <PERSON>'s diary appears to cast some doubt on the claim.)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "North Pole", "link": "https://wikipedia.org/wiki/North_Pole"}]}, {"year": "1927", "text": "The Old Parliament House, Canberra, Australia, officially opens.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Old_Parliament_House,_Canberra\" title=\"Old Parliament House, Canberra\">Old Parliament House, Canberra</a>, Australia, officially opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Old_Parliament_House,_Canberra\" title=\"Old Parliament House, Canberra\">Old Parliament House, Canberra</a>, Australia, officially opens.", "links": [{"title": "Old Parliament House, Canberra", "link": "https://wikipedia.org/wiki/Old_Parliament_House,_Canberra"}]}, {"year": "1936", "text": "Italy formally annexes Ethiopia after taking the capital Addis Ababa on May 5.", "html": "1936 - Italy formally annexes <a href=\"https://wikipedia.org/wiki/Ethiopian_Empire\" title=\"Ethiopian Empire\">Ethiopia</a> after taking the capital <a href=\"https://wikipedia.org/wiki/Addis_Ababa\" title=\"Addis Ababa\">Addis Ababa</a> on <a href=\"https://wikipedia.org/wiki/May_5\" title=\"May 5\">May 5</a>.", "no_year_html": "Italy formally annexes <a href=\"https://wikipedia.org/wiki/Ethiopian_Empire\" title=\"Ethiopian Empire\">Ethiopia</a> after taking the capital <a href=\"https://wikipedia.org/wiki/Addis_Ababa\" title=\"Addis Ababa\">Addis Ababa</a> on <a href=\"https://wikipedia.org/wiki/May_5\" title=\"May 5\">May 5</a>.", "links": [{"title": "Ethiopian Empire", "link": "https://wikipedia.org/wiki/Ethiopian_Empire"}, {"title": "Addis A<PERSON>ba", "link": "https://wikipedia.org/wiki/Addis_Ababa"}, {"title": "May 5", "link": "https://wikipedia.org/wiki/May_5"}]}, {"year": "1941", "text": "World War II: The German submarine U-110 is captured by the Royal Navy. On board is the latest Enigma machine which Allied cryptographers later use to break coded German messages.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German submarine <a href=\"https://wikipedia.org/wiki/German_submarine_U-110_(1940)\" title=\"German submarine U-110 (1940)\"><i>U-110</i></a> is captured by the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>. On board is the latest <a href=\"https://wikipedia.org/wiki/Enigma_machine\" title=\"Enigma machine\">Enigma machine</a> which <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> <a href=\"https://wikipedia.org/wiki/Cryptographer\" class=\"mw-redirect\" title=\"Cryptographer\">cryptographers</a> later use to break coded German messages.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German submarine <a href=\"https://wikipedia.org/wiki/German_submarine_U-110_(1940)\" title=\"German submarine U-110 (1940)\"><i>U-110</i></a> is captured by the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>. On board is the latest <a href=\"https://wikipedia.org/wiki/Enigma_machine\" title=\"Enigma machine\">Enigma machine</a> which <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> <a href=\"https://wikipedia.org/wiki/Cryptographer\" class=\"mw-redirect\" title=\"Cryptographer\">cryptographers</a> later use to break coded German messages.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "German submarine U-110 (1940)", "link": "https://wikipedia.org/wiki/German_submarine_U-110_(1940)"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Enigma machine", "link": "https://wikipedia.org/wiki/Enigma_machine"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Cryptographer", "link": "https://wikipedia.org/wiki/Cryptographer"}]}, {"year": "1942", "text": "The Holocaust in Ukraine: The SS executes 588 Jewish residents of the Podolian town of Zinkiv (Khmelnytska oblast. The Zoludek Ghetto (in Belarus) is destroyed and all its inhabitants executed or deported.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/The_Holocaust_in_Ukraine\" title=\"The Holocaust in Ukraine\">The Holocaust in Ukraine</a>: The SS executes 588 Jewish residents of the <a href=\"https://wikipedia.org/wiki/Podolia\" title=\"Podolia\">Podolian</a> town of <a href=\"https://wikipedia.org/wiki/Zinkiv\" class=\"mw-redirect\" title=\"Zinkiv\">Zinkiv</a> (<a href=\"https://wikipedia.org/wiki/Khmelnytska_oblast\" class=\"mw-redirect\" title=\"Khmelnytska oblast\">Khmelnytska oblast</a>. The <a href=\"https://wikipedia.org/wiki/Ghettos_in_Nazi-occupied_Europe\" class=\"mw-redirect\" title=\"Ghettos in Nazi-occupied Europe\">Zoludek Ghetto</a> (in <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a>) is destroyed and all its inhabitants executed or deported.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust_in_Ukraine\" title=\"The Holocaust in Ukraine\">The Holocaust in Ukraine</a>: The SS executes 588 Jewish residents of the <a href=\"https://wikipedia.org/wiki/Podolia\" title=\"Podolia\">Podolian</a> town of <a href=\"https://wikipedia.org/wiki/Zinkiv\" class=\"mw-redirect\" title=\"Zinkiv\">Zinkiv</a> (<a href=\"https://wikipedia.org/wiki/Khmelnytska_oblast\" class=\"mw-redirect\" title=\"Khmelnytska oblast\">Khmelnytska oblast</a>. The <a href=\"https://wikipedia.org/wiki/Ghettos_in_Nazi-occupied_Europe\" class=\"mw-redirect\" title=\"Ghettos in Nazi-occupied Europe\">Zoludek Ghetto</a> (in <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a>) is destroyed and all its inhabitants executed or deported.", "links": [{"title": "The Holocaust in Ukraine", "link": "https://wikipedia.org/wiki/The_Holocaust_in_Ukraine"}, {"title": "Podolia", "link": "https://wikipedia.org/wiki/Podolia"}, {"title": "Zinkiv", "link": "https://wikipedia.org/wiki/<PERSON>inkiv"}, {"title": "Khmelnytska oblast", "link": "https://wikipedia.org/wiki/Khmelnytska_oblast"}, {"title": "Ghettos in Nazi-occupied Europe", "link": "https://wikipedia.org/wiki/Ghettos_in_Nazi-occupied_Europe"}, {"title": "Belarus", "link": "https://wikipedia.org/wiki/Belarus"}]}, {"year": "1946", "text": "King <PERSON> of Italy abdicates and is succeeded by <PERSON><PERSON>.", "html": "1946 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> abdicates and is succeeded by <a href=\"https://wikipedia.org/wiki/Umberto_II_of_Italy\" title=\"Umberto II of Italy\">Umberto II</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> abdicates and is succeeded by <a href=\"https://wikipedia.org/wiki/Umberto_II_of_Italy\" title=\"Umberto II of Italy\">Umberto II</a>.", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy"}, {"title": "<PERSON>berto II of Italy", "link": "https://wikipedia.org/wiki/Umberto_II_of_Italy"}]}, {"year": "1948", "text": "Czechoslovakia's Ninth-of-May Constitution comes into effect.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>'s <a href=\"https://wikipedia.org/wiki/Ninth-of-May_Constitution\" title=\"Ninth-of-May Constitution\">Ninth-of-May Constitution</a> comes into effect.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>'s <a href=\"https://wikipedia.org/wiki/Ninth-of-May_Constitution\" title=\"Ninth-of-May Constitution\">Ninth-of-May Constitution</a> comes into effect.", "links": [{"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Ninth-of-May Constitution", "link": "https://wikipedia.org/wiki/Ninth-of-May_Constitution"}]}, {"year": "1950", "text": "<PERSON> presents the \"Schuman Declaration\", considered by some to be the beginning of the creation of what is now the European Union.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents the \"<a href=\"https://wikipedia.org/wiki/Schuman_Declaration\" title=\"Schuman Declaration\">Schuman Declaration</a>\", considered by some to be the beginning of the creation of what is now the European Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents the \"<a href=\"https://wikipedia.org/wiki/Schuman_Declaration\" title=\"Schuman Declaration\">Schuman Declaration</a>\", considered by some to be the beginning of the creation of what is now the European Union.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Schuman Declaration", "link": "https://wikipedia.org/wiki/Schuman_Declaration"}]}, {"year": "1955", "text": "Cold War: West Germany joins NATO.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> joins <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> joins <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "1960", "text": "The Food and Drug Administration announces it will approve birth control as an additional indication for <PERSON>rle's Enovid, making Enovid the world's first approved oral contraceptive pill.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> announces it will approve <a href=\"https://wikipedia.org/wiki/Birth_control\" title=\"Birth control\">birth control</a> as an additional <a href=\"https://wikipedia.org/wiki/Indication_(medicine)\" title=\"Indication (medicine)\">indication</a> for <a href=\"https://wikipedia.org/wiki/G._D._Searle_%26_Company\" class=\"mw-redirect\" title=\"G. D. Searle &amp; Company\">Searle</a>'s <a href=\"https://wikipedia.org/wiki/Mestranol/noretynodrel\" title=\"Mestranol/noretynodrel\">Enovid</a>, making Enovid the world's first approved <a href=\"https://wikipedia.org/wiki/Combined_oral_contraceptive_pill\" title=\"Combined oral contraceptive pill\">oral contraceptive pill</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> announces it will approve <a href=\"https://wikipedia.org/wiki/Birth_control\" title=\"Birth control\">birth control</a> as an additional <a href=\"https://wikipedia.org/wiki/Indication_(medicine)\" title=\"Indication (medicine)\">indication</a> for <a href=\"https://wikipedia.org/wiki/G._D._Searle_%26_Company\" class=\"mw-redirect\" title=\"G. D. Searle &amp; Company\">Searle</a>'s <a href=\"https://wikipedia.org/wiki/Mestranol/noretynodrel\" title=\"Mestranol/noretynodrel\">Enovid</a>, making Enovid the world's first approved <a href=\"https://wikipedia.org/wiki/Combined_oral_contraceptive_pill\" title=\"Combined oral contraceptive pill\">oral contraceptive pill</a>.", "links": [{"title": "Food and Drug Administration", "link": "https://wikipedia.org/wiki/Food_and_Drug_Administration"}, {"title": "Birth control", "link": "https://wikipedia.org/wiki/Birth_control"}, {"title": "Indication (medicine)", "link": "https://wikipedia.org/wiki/Indication_(medicine)"}, {"title": "G. D. Searle & Company", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Sea<PERSON>_%26_Company"}, {"title": "Mestranol/noretynodrel", "link": "https://wikipedia.org/wiki/Mestranol/noretynodrel"}, {"title": "Combined oral contraceptive pill", "link": "https://wikipedia.org/wiki/Combined_oral_contraceptive_pill"}]}, {"year": "1969", "text": "<PERSON> leads the first urban guerrilla action against the military dictatorship of Brazil in São Paulo, by robbing two banks.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first urban guerrilla action against the <a href=\"https://wikipedia.org/wiki/Brazilian_military_government\" class=\"mw-redirect\" title=\"Brazilian military government\">military dictatorship</a> of <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo\" title=\"São Paulo\">São Paulo</a>, by robbing two banks.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first urban guerrilla action against the <a href=\"https://wikipedia.org/wiki/Brazilian_military_government\" class=\"mw-redirect\" title=\"Brazilian military government\">military dictatorship</a> of <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo\" title=\"São Paulo\">São Paulo</a>, by robbing two banks.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Brazilian military government", "link": "https://wikipedia.org/wiki/Brazilian_military_government"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "São Paulo", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo"}]}, {"year": "1974", "text": "Watergate scandal: The  United States House Committee on the Judiciary opens formal and public impeachment hearings against President <PERSON>.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary\" title=\"United States House Committee on the Judiciary\">United States House Committee on the Judiciary</a> opens formal and public <a href=\"https://wikipedia.org/wiki/Federal_impeachment_in_the_United_States\" title=\"Federal impeachment in the United States\">impeachment</a> hearings against President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary\" title=\"United States House Committee on the Judiciary\">United States House Committee on the Judiciary</a> opens formal and public <a href=\"https://wikipedia.org/wiki/Federal_impeachment_in_the_United_States\" title=\"Federal impeachment in the United States\">impeachment</a> hearings against President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "United States House Committee on the Judiciary", "link": "https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary"}, {"title": "Federal impeachment in the United States", "link": "https://wikipedia.org/wiki/Federal_impeachment_in_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "Iranian Jewish businessman <PERSON><PERSON><PERSON> is executed by firing squad in Tehran, prompting the mass exodus of the once 100,000-strong Jewish community of Iran.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Persian_Jews\" class=\"mw-redirect\" title=\"Persian Jews\">Iranian Jewish</a> businessman <a href=\"https://wikipedia.org/wiki/Habib_Elghanian\" title=\"Habib <PERSON>\"><PERSON><PERSON><PERSON></a> is executed by firing squad in Tehran, prompting the mass exodus of the once 100,000-strong Jewish community of Iran.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Persian_Jews\" class=\"mw-redirect\" title=\"Persian Jews\">Iranian Jewish</a> businessman <a href=\"https://wikipedia.org/wiki/Habib_Elghanian\" title=\"Habib <PERSON>\"><PERSON><PERSON><PERSON></a> is executed by firing squad in Tehran, prompting the mass exodus of the once 100,000-strong Jewish community of Iran.", "links": [{"title": "Persian Jews", "link": "https://wikipedia.org/wiki/Persian_Jews"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>b_Elghanian"}]}, {"year": "1980", "text": "In Florida, United States, Liberian freighter MV Summit Venture collides with the Sunshine Skyway Bridge over Tampa Bay, making a 430-meter (1,400 ft) section of the southbound span collapse. Thirty-five people in six cars and a Greyhound bus fall 46 metres (150 ft) into the water and die.", "html": "1980 - In <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, United States, <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberian</a> freighter <a href=\"https://wikipedia.org/wiki/MV_Summit_Venture\" title=\"MV Summit Venture\">MV <i>Summit Venture</i></a> collides with the <a href=\"https://wikipedia.org/wiki/Sunshine_Skyway_Bridge\" title=\"Sunshine Skyway Bridge\">Sunshine Skyway Bridge</a> over <a href=\"https://wikipedia.org/wiki/Tampa_Bay\" title=\"Tampa Bay\">Tampa Bay</a>, making a 430-meter (1,400 ft) section of the southbound span collapse. Thirty-five people in six cars and a <a href=\"https://wikipedia.org/wiki/Greyhound_bus\" class=\"mw-redirect\" title=\"Greyhound bus\">Greyhound bus</a> fall 46 metres (150 ft) into the water and die.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, United States, <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberian</a> freighter <a href=\"https://wikipedia.org/wiki/MV_Summit_Venture\" title=\"MV Summit Venture\">MV <i>Summit Venture</i></a> collides with the <a href=\"https://wikipedia.org/wiki/Sunshine_Skyway_Bridge\" title=\"Sunshine Skyway Bridge\">Sunshine Skyway Bridge</a> over <a href=\"https://wikipedia.org/wiki/Tampa_Bay\" title=\"Tampa Bay\">Tampa Bay</a>, making a 430-meter (1,400 ft) section of the southbound span collapse. Thirty-five people in six cars and a <a href=\"https://wikipedia.org/wiki/Greyhound_bus\" class=\"mw-redirect\" title=\"Greyhound bus\">Greyhound bus</a> fall 46 metres (150 ft) into the water and die.", "links": [{"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Liberia", "link": "https://wikipedia.org/wiki/Liberia"}, {"title": "MV Summit Venture", "link": "https://wikipedia.org/wiki/MV_Summit_Venture"}, {"title": "Sunshine Skyway Bridge", "link": "https://wikipedia.org/wiki/Sunshine_Skyway_Bridge"}, {"title": "Tampa Bay", "link": "https://wikipedia.org/wiki/Tampa_Bay"}, {"title": "Greyhound bus", "link": "https://wikipedia.org/wiki/Greyhound_bus"}]}, {"year": "1980", "text": "In Norco, California, United States, five masked gunmen hold up a Security Pacific bank, leading to a violent shoot-out and one of the largest pursuits in California history. Two of the gunmen and one police officer are killed and thirty-three police and civilian vehicles are destroyed in the chase.", "html": "1980 - In <a href=\"https://wikipedia.org/wiki/Norco,_California\" title=\"Norco, California\">Norco, California</a>, United States, five masked gunmen hold up a Security Pacific bank, leading to <a href=\"https://wikipedia.org/wiki/Norco_shootout\" title=\"Norco shootout\">a violent shoot-out</a> and one of the largest pursuits in <a href=\"https://wikipedia.org/wiki/History_of_California\" title=\"History of California\">California history</a>. Two of the gunmen and one police officer are killed and thirty-three police and civilian vehicles are destroyed in the chase.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Norco,_California\" title=\"Norco, California\">Norco, California</a>, United States, five masked gunmen hold up a Security Pacific bank, leading to <a href=\"https://wikipedia.org/wiki/Norco_shootout\" title=\"Norco shootout\">a violent shoot-out</a> and one of the largest pursuits in <a href=\"https://wikipedia.org/wiki/History_of_California\" title=\"History of California\">California history</a>. Two of the gunmen and one police officer are killed and thirty-three police and civilian vehicles are destroyed in the chase.", "links": [{"title": "Norco, California", "link": "https://wikipedia.org/wiki/Norco,_California"}, {"title": "Norco shootout", "link": "https://wikipedia.org/wiki/Norco_shootout"}, {"title": "History of California", "link": "https://wikipedia.org/wiki/History_of_California"}]}, {"year": "1987", "text": "LOT Flight 5055 <PERSON><PERSON><PERSON> crashes after takeoff in Warsaw, Poland, killing all 183 people on board.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/LOT_Flight_5055\" class=\"mw-redirect\" title=\"LOT Flight 5055\">LOT Flight 5055 <i><PERSON><PERSON><PERSON></i> crashes after takeoff</a> in <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>, Poland, killing all 183 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LOT_Flight_5055\" class=\"mw-redirect\" title=\"LOT Flight 5055\">LOT Flight 5055 <i><PERSON><PERSON><PERSON></i> crashes after takeoff</a> in <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>, Poland, killing all 183 people on board.", "links": [{"title": "LOT Flight 5055", "link": "https://wikipedia.org/wiki/LOT_Flight_5055"}, {"title": "Warsaw", "link": "https://wikipedia.org/wiki/Warsaw"}]}, {"year": "1988", "text": "New Parliament House, Canberra officially opens.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Parliament_House,_Canberra\" title=\"Parliament House, Canberra\">New Parliament House, Canberra</a> officially opens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Parliament_House,_Canberra\" title=\"Parliament House, Canberra\">New Parliament House, Canberra</a> officially opens.", "links": [{"title": "Parliament House, Canberra", "link": "https://wikipedia.org/wiki/Parliament_House,_Canberra"}]}, {"year": "1992", "text": "Armenian forces capture Shusha, marking a major turning point in the First Nagorno-Karabakh War.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenian</a> forces capture <a href=\"https://wikipedia.org/wiki/Shusha\" title=\"Shusha\">Shusha</a>, marking a major turning point in the <a href=\"https://wikipedia.org/wiki/First_Nagorno-Karabakh_War\" title=\"First Nagorno-Karabakh War\">First Nagorno-Karabakh War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenian</a> forces capture <a href=\"https://wikipedia.org/wiki/Shusha\" title=\"Shusha\">Shusha</a>, marking a major turning point in the <a href=\"https://wikipedia.org/wiki/First_Nagorno-Karabakh_War\" title=\"First Nagorno-Karabakh War\">First Nagorno-Karabakh War</a>.", "links": [{"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "First Nagorno-Karabakh War", "link": "https://wikipedia.org/wiki/First_Nagorno-Karabakh_War"}]}, {"year": "1992", "text": "Westray Mine disaster kills 26 workers in Nova Scotia, Canada.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Westray_Mine_disaster\" class=\"mw-redirect\" title=\"Westray Mine disaster\">Westray Mine disaster</a> kills 26 workers in <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Westray_Mine_disaster\" class=\"mw-redirect\" title=\"Westray Mine disaster\">Westray Mine disaster</a> kills 26 workers in <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada.", "links": [{"title": "Westray Mine disaster", "link": "https://wikipedia.org/wiki/Westray_Mine_disaster"}, {"title": "Nova Scotia", "link": "https://wikipedia.org/wiki/Nova_Scotia"}]}, {"year": "2001", "text": "In Ghana, 129 football fans die in what became known as the Accra Sports Stadium disaster. The deaths are caused by a stampede (caused by the firing of tear gas by police personnel at the stadium) that followed a controversial decision by the referee.", "html": "2001 - In <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a>, 129 football fans die in what became known as the <a href=\"https://wikipedia.org/wiki/Accra_Sports_Stadium_disaster\" title=\"Accra Sports Stadium disaster\">Accra Sports Stadium disaster</a>. The deaths are caused by a stampede (caused by the firing of tear gas by police personnel at the stadium) that followed a controversial decision by the referee.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a>, 129 football fans die in what became known as the <a href=\"https://wikipedia.org/wiki/Accra_Sports_Stadium_disaster\" title=\"Accra Sports Stadium disaster\">Accra Sports Stadium disaster</a>. The deaths are caused by a stampede (caused by the firing of tear gas by police personnel at the stadium) that followed a controversial decision by the referee.", "links": [{"title": "Ghana", "link": "https://wikipedia.org/wiki/Ghana"}, {"title": "Accra Sports Stadium disaster", "link": "https://wikipedia.org/wiki/Accra_Sports_Stadium_disaster"}]}, {"year": "2002", "text": "The 38-day stand-off in the Church of the Nativity in Bethlehem comes to an end when the Palestinians inside agree to have 13 suspected terrorists among them deported to several different countries.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Siege_of_the_Church_of_the_Nativity_in_Bethlehem\" class=\"mw-redirect\" title=\"Siege of the Church of the Nativity in Bethlehem\">38-day stand-off</a> in the <a href=\"https://wikipedia.org/wiki/Church_of_the_Nativity\" title=\"Church of the Nativity\">Church of the Nativity</a> in <a href=\"https://wikipedia.org/wiki/Bethlehem\" title=\"Bethlehem\">Bethlehem</a> comes to an end when the <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinians</a> inside agree to have 13 suspected terrorists among them deported to several different countries.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_the_Church_of_the_Nativity_in_Bethlehem\" class=\"mw-redirect\" title=\"Siege of the Church of the Nativity in Bethlehem\">38-day stand-off</a> in the <a href=\"https://wikipedia.org/wiki/Church_of_the_Nativity\" title=\"Church of the Nativity\">Church of the Nativity</a> in <a href=\"https://wikipedia.org/wiki/Bethlehem\" title=\"Bethlehem\">Bethlehem</a> comes to an end when the <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinians</a> inside agree to have 13 suspected terrorists among them deported to several different countries.", "links": [{"title": "Siege of the Church of the Nativity in Bethlehem", "link": "https://wikipedia.org/wiki/Siege_of_the_Church_of_the_Nativity_in_Bethlehem"}, {"title": "Church of the Nativity", "link": "https://wikipedia.org/wiki/Church_of_the_Nativity"}, {"title": "Bethlehem", "link": "https://wikipedia.org/wiki/Bethlehem"}, {"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}]}, {"year": "2018", "text": "The historic defeat for Barisan Nasional, the governing coalition of Malaysia since the country's independence in 1957 in 2018 Malaysian general election.", "html": "2018 - The historic defeat for <a href=\"https://wikipedia.org/wiki/Barisan_Nasional\" title=\"Barisan Nasional\">Barisan Nasional</a>, the governing coalition of Malaysia since the country's independence in 1957 in <a href=\"https://wikipedia.org/wiki/2018_Malaysian_general_election\" title=\"2018 Malaysian general election\">2018 Malaysian general election</a>.", "no_year_html": "The historic defeat for <a href=\"https://wikipedia.org/wiki/Barisan_Nasional\" title=\"Barisan Nasional\">Barisan Nasional</a>, the governing coalition of Malaysia since the country's independence in 1957 in <a href=\"https://wikipedia.org/wiki/2018_Malaysian_general_election\" title=\"2018 Malaysian general election\">2018 Malaysian general election</a>.", "links": [{"title": "<PERSON>san <PERSON>", "link": "https://wikipedia.org/wiki/Barisan_Nasional"}, {"title": "2018 Malaysian general election", "link": "https://wikipedia.org/wiki/2018_Malaysian_general_election"}]}, {"year": "2020", "text": "The COVID-19 recession causes the U.S. unemployment rate to hit 14.9 percent, its worst rate since the Great Depression.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/COVID-19_recession\" title=\"COVID-19 recession\">COVID-19 recession</a> causes the <a href=\"https://wikipedia.org/wiki/Unemployment_in_the_United_States\" title=\"Unemployment in the United States\">U.S. unemployment rate</a> to hit 14.9 percent, its worst rate since the <a href=\"https://wikipedia.org/wiki/Great_Depression_in_the_United_States\" title=\"Great Depression in the United States\">Great Depression</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/COVID-19_recession\" title=\"COVID-19 recession\">COVID-19 recession</a> causes the <a href=\"https://wikipedia.org/wiki/Unemployment_in_the_United_States\" title=\"Unemployment in the United States\">U.S. unemployment rate</a> to hit 14.9 percent, its worst rate since the <a href=\"https://wikipedia.org/wiki/Great_Depression_in_the_United_States\" title=\"Great Depression in the United States\">Great Depression</a>.", "links": [{"title": "COVID-19 recession", "link": "https://wikipedia.org/wiki/COVID-19_recession"}, {"title": "Unemployment in the United States", "link": "https://wikipedia.org/wiki/Unemployment_in_the_United_States"}, {"title": "Great Depression in the United States", "link": "https://wikipedia.org/wiki/Great_Depression_in_the_United_States"}]}, {"year": "2022", "text": "Russo-Ukrainian War: United States President <PERSON> signs the 2022 Lend-Lease Act into law, a rebooted World War II-era policy expediting American equipment to Ukraine and other Eastern European countries.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Ukraine_Democracy_Defense_Lend-Lease_Act_of_2022\" title=\"Ukraine Democracy Defense Lend-Lease Act of 2022\">2022 Lend-Lease Act</a> into law, a rebooted <a href=\"https://wikipedia.org/wiki/Lend-Lease\" title=\"Lend-Lease\">World War II-era policy</a> expediting American equipment to Ukraine and other Eastern European countries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Ukraine_Democracy_Defense_Lend-Lease_Act_of_2022\" title=\"Ukraine Democracy Defense Lend-Lease Act of 2022\">2022 Lend-Lease Act</a> into law, a rebooted <a href=\"https://wikipedia.org/wiki/Lend-Lease\" title=\"Lend-Lease\">World War II-era policy</a> expediting American equipment to Ukraine and other Eastern European countries.", "links": [{"title": "Russo-Ukrainian War", "link": "https://wikipedia.org/wiki/Russo-Ukrainian_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ukraine Democracy Defense Lend-Lease Act of 2022", "link": "https://wikipedia.org/wiki/Ukraine_Democracy_Defense_Lend-Lease_Act_of_2022"}, {"title": "Lend-Lease", "link": "https://wikipedia.org/wiki/Lend-Lease"}]}, {"year": "2023", "text": "The May 9 riots following the arrest of <PERSON><PERSON><PERSON> in Pakistan.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/May_9_riots\" title=\"May 9 riots\">May 9</a> riots following the <a href=\"https://wikipedia.org/wiki/Arrest_of_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Arrest of <PERSON><PERSON><PERSON>\">arrest of <PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/May_9_riots\" title=\"May 9 riots\">May 9</a> riots following the <a href=\"https://wikipedia.org/wiki/Arrest_of_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Arrest of <PERSON><PERSON><PERSON>\">arrest of <PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "links": [{"title": "May 9 riots", "link": "https://wikipedia.org/wiki/May_9_riots"}, {"title": "Arrest of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arrest_of_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}], "Births": [{"year": "1147", "text": "<PERSON><PERSON>, Japanese shōgun (d. 1199)", "html": "1147 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoritomo\" title=\"Minamoto no Yoritomo\"><PERSON><PERSON> no Yoritomo</a>, Japanese shōgun (d. 1199)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoritomo\" title=\"Minamoto no Yoritomo\"><PERSON><PERSON> no Yoritomo</a>, Japanese shōgun (d. 1199)", "links": [{"title": "<PERSON><PERSON> no Yoritomo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1151", "text": "<PERSON><PERSON><PERSON><PERSON>, last <PERSON><PERSON>id caliph (d. 1171)", "html": "1151 - <a href=\"https://wikipedia.org/wiki/Al-<PERSON>\" title=\"Al<PERSON>Adid\"><PERSON>-<PERSON><PERSON></a>, last Fatimid caliph (d. 1171)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Adid\" title=\"Al<PERSON>Adid\"><PERSON><PERSON><PERSON><PERSON></a>, last Fatimid caliph (d. 1171)", "links": [{"title": "Al-Adid", "link": "https://wikipedia.org/wiki/Al-Adid"}]}, {"year": "1540", "text": "<PERSON><PERSON><PERSON>, Indian ruler (d. 1597)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>na_<PERSON><PERSON>p\" title=\"<PERSON><PERSON>na <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian ruler (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>na <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian ruler (d. 1597)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>p"}]}, {"year": "1555", "text": "<PERSON><PERSON><PERSON><PERSON> la Asunción, Spanish Catholic nun and founder of the first monastery in Manila (d. 1630)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/Jer%C3%B3nima_de_la_Asunci%C3%B3n\" title=\"<PERSON><PERSON><PERSON><PERSON> de la Asunción\"><PERSON><PERSON><PERSON><PERSON> <PERSON> la Asunción</a>, Spanish Catholic nun and founder of the first monastery in Manila (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jer%C3%B3nima_de_la_Asunci%C3%B3n\" title=\"<PERSON>r<PERSON><PERSON> de la Asunción\"><PERSON><PERSON><PERSON><PERSON> <PERSON> la Asunción</a>, Spanish Catholic nun and founder of the first monastery in Manila (d. 1630)", "links": [{"title": "Jerónima de la Asunción", "link": "https://wikipedia.org/wiki/Jer%C3%B3ni<PERSON>_de_la_Asunci%C3%B3n"}]}, {"year": "1594", "text": "<PERSON>, Prince of Nassau-Dillenburg, military leader in the Thirty Years' War (d. 1662)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Dillenburg\" title=\"<PERSON>, Prince of Nassau-Dillenburg\"><PERSON>, Prince of Nassau-Dillenburg</a>, military leader in the Thirty Years' War (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Dillenburg\" title=\"<PERSON>, Prince of Nassau-Dillenburg\"><PERSON>, Prince of Nassau-Dillenburg</a>, military leader in the Thirty Years' War (d. 1662)", "links": [{"title": "<PERSON>, Prince of Nassau-Dillenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Dillenburg"}]}, {"year": "1617", "text": "<PERSON>, Landgrave of Hesse-Eschwege (d. 1655)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Landgrave_of_Hesse-Eschwege\" title=\"<PERSON>, Landgrave of Hesse-Eschwege\"><PERSON>, Landgrave of Hesse-Eschwege</a> (d. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Landgrave_of_Hesse-Eschwege\" title=\"<PERSON>, Landgrave of Hesse-Eschwege\"><PERSON>, Landgrave of Hesse-Eschwege</a> (d. 1655)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Eschwege", "link": "https://wikipedia.org/wiki/<PERSON>,_Land<PERSON>_of_Hesse-Eschwege"}]}, {"year": "1740", "text": "<PERSON>, Italian composer and educator (probable; d. 1816)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (probable; d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (probable; d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1746", "text": "<PERSON><PERSON>, French mathematician and engineer (d. 1818)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Monge\" title=\"<PERSON>pard Monge\"><PERSON><PERSON></a>, French mathematician and engineer (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Monge\" title=\"<PERSON>pard Monge\"><PERSON><PERSON></a>, French mathematician and engineer (d. 1818)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>pard_Monge"}]}, {"year": "1763", "text": "<PERSON><PERSON><PERSON>, Hungarian-Austrian poet and author (d. 1845)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Bats%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian poet and author (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Bats%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian poet and author (d. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Bats%C3%A1nyi"}]}, {"year": "1800", "text": "<PERSON>, American activist (d. 1859)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(abolitionist)\" title=\"<PERSON> (abolitionist)\"><PERSON></a>, American activist (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(abolitionist)\" title=\"<PERSON> (abolitionist)\"><PERSON></a>, American activist (d. 1859)", "links": [{"title": "<PERSON> (abolitionist)", "link": "https://wikipedia.org/wiki/<PERSON>_(abolitionist)"}]}, {"year": "1801", "text": "<PERSON><PERSON>, English politician, founded the town of Fleetwood (d. 1866)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, founded the town of <a href=\"https://wikipedia.org/wiki/Fleetwood\" title=\"<PERSON>\"><PERSON></a> (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, founded the town of <a href=\"https://wikipedia.org/wiki/Fleetwood\" title=\"<PERSON>\">Fleetwood</a> (d. 1866)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fleetwood", "link": "https://wikipedia.org/wiki/Fleetwood"}]}, {"year": "1814", "text": "<PERSON>, Irish-American actor and playwright (d. 1880)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor and playwright (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor and playwright (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, English-New Zealand politician, 6th Prime Minister of New Zealand (d. 1891)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1824", "text": "<PERSON>, Polish apologist and author (d. 1896)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ben <PERSON>\"><PERSON> ben <PERSON></a>, Polish apologist and author (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ben <PERSON>\"><PERSON> ben <PERSON></a>, Polish apologist and author (d. 1896)", "links": [{"title": "<PERSON> ben <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Victorian painter (d. 1881)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Victorian painter (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Victorian painter (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, French ophthalmologist, invented the <PERSON><PERSON><PERSON> chart (d. 1912)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ophthalmologist, invented the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_chart\" title=\"<PERSON><PERSON><PERSON> chart\"><PERSON><PERSON><PERSON> chart</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ophthalmologist, invented the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_chart\" title=\"<PERSON><PERSON><PERSON> chart\"><PERSON><PERSON><PERSON> chart</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Monoyer chart", "link": "https://wikipedia.org/wiki/<PERSON>oy<PERSON>_chart"}]}, {"year": "1837", "text": "<PERSON>, German engineer, founded the Opel Company (d. 1895)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, founded the <a href=\"https://wikipedia.org/wiki/Opel\" title=\"Opel\">Opel Company</a> (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adam Opel\"><PERSON></a>, German engineer, founded the <a href=\"https://wikipedia.org/wiki/Opel\" title=\"Opel\">Opel Company</a> (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Opel"}, {"title": "Opel", "link": "https://wikipedia.org/wiki/Opel"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON>, Swedish engineer and businessman (d. 1913)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish engineer and businessman (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish engineer and businessman (d. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, English-American chemist (d. 1936)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English-American chemist (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English-American chemist (d. 1936)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(chemist)"}]}, {"year": "1855", "text": "<PERSON>, German-Dutch composer (d. 1932)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%B6ntgen\" title=\"<PERSON>\"><PERSON></a>, German-Dutch composer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%B6ntgen\" title=\"<PERSON>\"><PERSON></a>, German-Dutch composer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julius_R%C3%B6ntgen"}]}, {"year": "1860", "text": "<PERSON><PERSON> <PERSON><PERSON>, Scottish novelist and playwright (d. 1937)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish novelist and playwright (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish novelist and playwright (d. 1937)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON>, Indian economist and politician (d. 1915)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Gopal_Krishna_Gokhale\" title=\"Gopal Krishna Gokhale\"><PERSON><PERSON></a>, Indian economist and politician (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gopal_<PERSON>_Gokhale\" title=\"Gopal Krishna Gokhale\"><PERSON><PERSON></a>, Indian economist and politician (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Krishna_Gokhale"}]}, {"year": "1870", "text": "<PERSON>, British golfer (d. 1937)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British golfer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British golfer (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Czech-American captain and politician, 44th Mayor of Chicago (d. 1933)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American captain and politician, 44th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American captain and politician, 44th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1874", "text": "<PERSON>, English archaeologist and historian (d. 1939)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and historian (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and historian (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American painter (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter (d. 1965)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1882", "text": "<PERSON>, American shipbuilder and businessman, founded Kaiser Shipyards (d. 1967)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Henry <PERSON>\"><PERSON></a>, American shipbuilder and businessman, founded <a href=\"https://wikipedia.org/wiki/Kaiser_Shipyards\" title=\"Kaiser Shipyards\">Kaiser Shipyards</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shipbuilder and businessman, founded <a href=\"https://wikipedia.org/wiki/Kaiser_Shipyards\" title=\"Kaiser Shipyards\">Kaiser Shipyards</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Kaiser Shipyards", "link": "https://wikipedia.org/wiki/Kaiser_Shipyards"}]}, {"year": "1883", "text": "<PERSON>, Spanish philosopher, author, and critic (d. 1955)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ortega_y_Gasset\" title=\"<PERSON> y Gasset\"><PERSON></a>, Spanish philosopher, author, and critic (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ortega_y_Gasset\" title=\"<PERSON> y Gasset\"><PERSON></a>, Spanish philosopher, author, and critic (d. 1955)", "links": [{"title": "<PERSON> y Gasset", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ortega_y_Gasset"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Danish actor (d. 1917)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Valdemar_Psilander\" title=\"Valdemar Psilander\"><PERSON><PERSON><PERSON></a>, Danish actor (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valdemar_Psilander\" title=\"Valdemar Psilander\"><PERSON><PERSON><PERSON></a>, Danish actor (d. 1917)", "links": [{"title": "Valdemar <PERSON>", "link": "https://wikipedia.org/wiki/Valdemar_Psilander"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Maltese artist (d. 1977)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese artist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese artist (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Italian fighter pilot (d. 1918)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fighter pilot (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fighter pilot (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Swedish art collector (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Swedish art collector (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Swedish art collector (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_de_Mar%C3%A9"}]}, {"year": "1892", "text": "<PERSON><PERSON> of Bourbon-Parma, last Empress of the Austro-Hungarian Empire (d. 1989)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bourbon-Parma\" title=\"<PERSON><PERSON> of Bourbon-Parma\"><PERSON><PERSON> of Bourbon-Parma</a>, last Empress of the <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Empire\" class=\"mw-redirect\" title=\"Austro-Hungarian Empire\">Austro-Hungarian Empire</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bourbon-Parma\" title=\"<PERSON><PERSON> of Bourbon-Parma\"><PERSON><PERSON> of Bourbon-Parma</a>, last Empress of the <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Empire\" class=\"mw-redirect\" title=\"Austro-Hungarian Empire\">Austro-Hungarian Empire</a> (d. 1989)", "links": [{"title": "<PERSON><PERSON> of Bourbon-Parma", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bourbon-Parma"}, {"title": "Austro-Hungarian Empire", "link": "https://wikipedia.org/wiki/Austro-Hungarian_Empire"}]}, {"year": "1893", "text": "<PERSON>, American psychologist and author (d. 1947)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, British-American economist, professor, and investor (d. 1976)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American economist, professor, and investor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American economist, professor, and investor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American actor (d. 1963)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Romanian poet, playwright, and philosopher (d. 1961)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, playwright, and philosopher (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, playwright, and philosopher (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lucian_<PERSON>a"}]}, {"year": "1895", "text": "<PERSON>, American pole vaulter (d. 1989)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American pole vaulter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American pole vaulter (d. 1989)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1896", "text": "<PERSON>, Canadian-American art director and set decorator (d. 1972)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_director)\" title=\"<PERSON> (art director)\"><PERSON></a>, Canadian-American art director and set decorator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_director)\" title=\"<PERSON> (art director)\"><PERSON></a>, Canadian-American art director and set decorator (d. 1972)", "links": [{"title": "<PERSON> (art director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_director)"}]}, {"year": "1900", "text": "<PERSON>, Polish stage and film actress (d. 1992)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Polish stage and film actress (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Polish stage and film actress (d. 1992)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(actress)"}]}, {"year": "1907", "text": "<PERSON>, Trinidadian cricketer (d. 1978)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, German politician (d. 1974)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Ba<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German politician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ba<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German politician (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American baseball player and manager (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American architect, designed the Solow Building (d. 1990)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Solow_Building\" title=\"Solow Building\">Solow Building</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Solow_Building\" title=\"Solow Building\">Solow Building</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gordon_<PERSON>haft"}, {"title": "Solow Building", "link": "https://wikipedia.org/wiki/Solow_Building"}]}, {"year": "1912", "text": "<PERSON>, Mexican-American actor (d. 1963)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1riz\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Armend%C3%A1riz"}]}, {"year": "1914", "text": "<PERSON>, Italian conductor and director (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor and director (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor and director (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American musicologist (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musicologist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musicologist (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American country music singer-songwriter and guitarist (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American journalist (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American priest, poet, and activist (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, poet, and activist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, poet, and activist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, German activist (d. 1943)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Russian singer, poet, and author (d. 1997)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian singer, poet, and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian singer, poet, and author (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>igen"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American tennis player (d. 1995)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON></a>, American tennis player (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gonzales\"><PERSON><PERSON></a>, American tennis player (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pan<PERSON>_<PERSON>les"}]}, {"year": "1928", "text": "<PERSON>, Canadian figure skater (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English actress (d. 2001)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American pilot, engineer, and astronaut", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Vance <PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, English actress (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English screenwriter, playwright, and novelist", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter, playwright, and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter, playwright, and novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American guitarist (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English actor (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, English actress and politician (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and politician (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer (d. 1988)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American serial killer (d. 1985)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Serbian-American poet and editor (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Serbian-American poet and editor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Serbian-American poet and editor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Simi%C4%87"}]}, {"year": "1939", "text": "<PERSON>, Romanian tennis player and manager", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C8%9A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian tennis player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C8%9A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian tennis player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ion_%C8%9Airiac"}]}, {"year": "1940", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English sprinter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American lawyer and politician, 79th United States Attorney General", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 79th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 79th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1943", "text": "<PERSON>, English economist and politician, former Secretary of State for Business, Innovation and Skills", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cable\"><PERSON></a>, English economist and politician, former <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Vince Cable\"><PERSON></a>, English economist and politician, former <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a>", "links": [{"title": "Vince <PERSON>", "link": "https://wikipedia.org/wiki/Vince_Cable"}, {"title": "Secretary of State for Business, Innovation and Skills", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills"}]}, {"year": "1943", "text": "<PERSON>, English astronomer, chemist, and academic (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer, chemist, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer, chemist, and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American actress and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Candice_Bergen\" title=\"Candice Bergen\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Candice_Bergen\" title=\"Candice Bergen\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Candice_Bergen"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Japanese diplomat (d. 2019)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese diplomat (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese diplomat (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American basketball player and radio host", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Alley_Mills\" title=\"Alley Mills\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alley_Mills\" title=\"Alley Mills\"><PERSON></a>, American actress", "links": [{"title": "Alley Mills", "link": "https://wikipedia.org/wiki/Alley_Mills"}]}, {"year": "1951", "text": "<PERSON>, American poet, musician, playwright and author, 23rd United States Poet Laureate", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, musician, playwright and author, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Poet_Laureate\" title=\"United States Poet Laureate\">United States Poet Laureate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, musician, playwright and author, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Poet_Laureate\" title=\"United States Poet Laureate\">United States Poet Laureate</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "United States Poet Laureate", "link": "https://wikipedia.org/wiki/United_States_Poet_Laureate"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Prime Minister of Ethiopia (d. 2012)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Ethiopia\" title=\"Prime Minister of Ethiopia\">Prime Minister of Ethiopia</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Ethiopia\" title=\"Prime Minister of Ethiopia\">Prime Minister of Ethiopia</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>awi"}, {"title": "Prime Minister of Ethiopia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Ethiopia"}]}, {"year": "1955", "text": "<PERSON>, Swedish soprano and actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian actress and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and coach (d. 2014)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American philosopher and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, British economist and politician, Secretary of State for Transport", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British economist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British economist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Transport", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Transport"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, French sprinter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_P%C3%A9rec\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9_P%C3%A9rec\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A9_P%C3%A9rec"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Chinese footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Hao_Haidong\" title=\"Hao Hai<PERSON>\"><PERSON><PERSON></a>, Chinese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hao_Haidong\" title=\"Hao Haidong\"><PERSON><PERSON></a>, Chinese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hao_Haidong"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American rapper and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Ghostface_Killah\" title=\"Ghostface Killah\"><PERSON><PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ghostface_Killah\" title=\"Ghostface Killah\"><PERSON><PERSON></a>, American rapper and actor", "links": [{"title": "Ghostface <PERSON>", "link": "https://wikipedia.org/wiki/Ghost<PERSON>_<PERSON>ah"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Kenyan runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Tegla_Loroupe\" title=\"Tegla Loroupe\"><PERSON><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tegla_Loroupe\" title=\"Tegla Loroupe\"><PERSON><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "Tegla Lo<PERSON>e", "link": "https://wikipedia.org/wiki/Tegla_Loroupe"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Canadian singer-songwriter, producer, and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter, producer, and actress", "links": [{"title": "Tam<PERSON>", "link": "https://wikipedia.org/wiki/Tamia"}]}, {"year": "1975", "text": "<PERSON>, American motocross rider", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rider)\" class=\"mw-redirect\" title=\"<PERSON> (rider)\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Motocross\" title=\"Motocross\">motocross</a> rider", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rider)\" class=\"mw-redirect\" title=\"<PERSON> (rider)\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Motocross\" title=\"Motocross\">motocross</a> rider", "links": [{"title": "<PERSON> (rider)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rider)"}, {"title": "Motocross", "link": "https://wikipedia.org/wiki/Motocross"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Mexican wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Czech footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Canadian cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Svein_Tuft\" title=\"Svein Tuft\"><PERSON><PERSON><PERSON></a>, Canadian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Svein_Tuft\" title=\"Svein Tuft\"><PERSON><PERSON><PERSON> <PERSON></a>, Canadian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Svein_Tuft"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian swimmer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Luxembourgian tennis player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilles_M%C3%BCller"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>er\" title=\"<PERSON> Fielder\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>er\" title=\"<PERSON> Fielder\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, French footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian racing driver", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian racing driver", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1989", "text": "<PERSON>, German musician", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Ko<PERSON>var judoka", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> judoka", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> judoka", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1992", "text": "<PERSON>, English footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Burn\" title=\"Dan Burn\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dan_Burn\" title=\"Dan Burn\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Beth Mead\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Beth Mead\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "480", "text": "<PERSON>, Western Roman Emperor", "html": "480 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Western Roman Emperor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Western Roman Emperor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julius_Nepos"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "729", "text": "<PERSON><PERSON><PERSON>, king of Northumbria", "html": "729 - <a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Northumbria\" title=\"Northumbria\">Northumbria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Northumbria\" title=\"Northumbria\">Northumbria</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/Osric_of_Northumbria"}, {"title": "Northumbria", "link": "https://wikipedia.org/wiki/Northumbria"}]}, {"year": "893", "text": "<PERSON>, warlord of the Tang Dynasty", "html": "893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, warlord of the Tang Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, warlord of the Tang Dynasty", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}]}, {"year": "909", "text": "<PERSON><PERSON><PERSON>, archbishop of Hamburg-Bremen", "html": "909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, archbishop of Hamburg-Bremen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, archbishop of Hamburg-Bremen", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ada<PERSON>gar"}]}, {"year": "934", "text": "<PERSON>, Chinese general and governor (b. 892)", "html": "934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and governor (b. 892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and governor (b. 892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1280", "text": "<PERSON> of Norway", "html": "1280 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Norway\" class=\"mw-redirect\" title=\"Magnus VI of Norway\"><PERSON> VI of Norway</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Norway\" class=\"mw-redirect\" title=\"Magnus VI of Norway\"><PERSON> VI of Norway</a>", "links": [{"title": "<PERSON> of Norway", "link": "https://wikipedia.org/wiki/Magnus_VI_of_Norway"}]}, {"year": "1315", "text": "<PERSON>, Duke of Burgundy (b. 1282)", "html": "1315 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1282)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1282)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1329", "text": "<PERSON>, Bishop of Bath and Wells", "html": "1329 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bishop of Bath and Wells", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bishop of Bath and Wells", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1443", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian Cardinal and diplomat (b. 1373)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Albergati\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian Cardinal and diplomat (b. 1373)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON>bergati\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian Cardinal and diplomat (b. 1373)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON>ati"}]}, {"year": "1446", "text": "<PERSON> Enghien (b. 1368)", "html": "1446 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Enghien\" title=\"<PERSON> of Enghien\"><PERSON> of Enghien</a> (b. 1368)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Enghien\" title=\"<PERSON> of Enghien\"><PERSON> of Enghien</a> (b. 1368)", "links": [{"title": "<PERSON> of Enghien", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1590", "text": "<PERSON> French cardinal and pretender to the throne (b. 1523)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>_(born_1523)\" title=\"<PERSON>, <PERSON> (born 1523)\"><PERSON></a> French cardinal and pretender to the throne (b. 1523)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>_(born_1523)\" title=\"<PERSON>, <PERSON> (born 1523)\"><PERSON></a> French cardinal and pretender to the throne (b. 1523)", "links": [{"title": "<PERSON>, <PERSON> (born 1523)", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>_(born_1523)"}]}, {"year": "1657", "text": "<PERSON>, English-American politician, 2nd Governor of Plymouth Colony (b. 1590)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Plymouth_Colony_governor)\" class=\"mw-redirect\" title=\"<PERSON> (Plymouth Colony governor)\"><PERSON></a>, English-American politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Plymouth_Colony\" class=\"mw-redirect\" title=\"Governor of Plymouth Colony\">Governor of Plymouth Colony</a> (b. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Plymouth_Colony_governor)\" class=\"mw-redirect\" title=\"<PERSON> (Plymouth Colony governor)\"><PERSON></a>, English-American politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Plymouth_Colony\" class=\"mw-redirect\" title=\"Governor of Plymouth Colony\">Governor of Plymouth Colony</a> (b. 1590)", "links": [{"title": "<PERSON> (Plymouth Colony governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Plymouth_Colony_governor)"}, {"title": "Governor of Plymouth Colony", "link": "https://wikipedia.org/wiki/Governor_of_Plymouth_Colony"}]}, {"year": "1707", "text": "<PERSON><PERSON><PERSON>, German-Danish organist and composer (b. 1637)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bux<PERSON>hude\" title=\"<PERSON><PERSON><PERSON> Buxtehude\"><PERSON><PERSON><PERSON></a>, German-Danish organist and composer (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bux<PERSON>hude\" title=\"<PERSON><PERSON><PERSON> Buxtehude\"><PERSON><PERSON><PERSON></a>, German-Danish organist and composer (b. 1637)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>de"}]}, {"year": "1736", "text": "<PERSON><PERSON>-Real, Portuguese judge and politician (b. 1658)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_Mendon%C3%A7a_Corte-Real\" title=\"<PERSON><PERSON> de Mendonça Corte-Real\"><PERSON><PERSON> de Mendonça Corte-Real</a>, Portuguese judge and politician (b. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Mendon%C3%A7a_Corte-Real\" title=\"<PERSON>ogo de Mendonça Corte-Real\"><PERSON><PERSON> de Mendonça Corte-Real</a>, Portuguese judge and politician (b. 1658)", "links": [{"title": "<PERSON>ogo de Mendonça Corte-Real", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Mendon%C3%A7a_Corte-Real"}]}, {"year": "1745", "text": "<PERSON><PERSON>, Italian violinist and composer (b. 1663)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian violinist and composer (b. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian violinist and composer (b. 1663)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON>, 2nd Earl of Stair, Scottish field marshal and diplomat, British Ambassador to France (b. 1673)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Stair\" title=\"<PERSON>, 2nd Earl of Stair\"><PERSON>, 2nd Earl of Stair</a>, Scottish field marshal and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France\" title=\"List of ambassadors of the United Kingdom to France\">British Ambassador to France</a> (b. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Stair\" title=\"<PERSON>, 2nd Earl of Stair\"><PERSON>, 2nd Earl of Stair</a>, Scottish field marshal and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France\" title=\"List of ambassadors of the United Kingdom to France\">British Ambassador to France</a> (b. 1673)", "links": [{"title": "<PERSON>, 2nd Earl of Stair", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Stair"}, {"title": "List of ambassadors of the United Kingdom to France", "link": "https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France"}]}, {"year": "1760", "text": "<PERSON><PERSON>, German bishop and saint (b. 1700)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German bishop and saint (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German bishop and saint (b. 1700)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON><PERSON><PERSON>, French general and engineer (b. 1715)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>auval\"><PERSON><PERSON><PERSON></a>, French general and engineer (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>auval\"><PERSON><PERSON><PERSON></a>, French general and engineer (b. 1715)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, American politician (b. 1721)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, American judge and politician (b. 1737)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (b. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, German poet, playwright, and historian (b. 1759)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, playwright, and historian (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, playwright, and historian (b. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, French chemist and physicist (b. 1778)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and physicist (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and physicist (b. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian author and activist (b. 1769)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Gar<PERSON><PERSON>_<PERSON>el\" title=\"Garlie<PERSON> Merkel\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian author and activist (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ar<PERSON><PERSON> Merkel\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian author and activist (b. 1769)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, German philologist and politician (b. 1805)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and politician (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and politician (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American general and educator (b. 1813)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and educator (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and educator (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American general (b. 1800)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German theologian and academic (b. 1844)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American abolitionist (b. 1823)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American abolitionist (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American abolitionist (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON> <PERSON><PERSON>, American businessman, founded Post Foods (b. 1854)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/C._W._Post\" title=\"C. W. Post\">C. <PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Post_Foods\" class=\"mw-redirect\" title=\"Post Foods\">Post Foods</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._W._Post\" title=\"C. W. Post\">C. W<PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Post_Foods\" class=\"mw-redirect\" title=\"Post Foods\">Post Foods</a> (b. 1854)", "links": [{"title": "C. W. Post", "link": "https://wikipedia.org/wiki/C._W._Post"}, {"title": "Post Foods", "link": "https://wikipedia.org/wiki/Post_Foods"}]}, {"year": "1915", "text": "<PERSON>, Luxembourgian-French cyclist and soldier (b. 1887)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian-French cyclist and soldier (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian-French cyclist and soldier (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, New Zealand tennis player and cricketer (b. 1883)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand tennis player and cricketer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand tennis player and cricketer (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Romanian journalist and poet (b. 1866)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>%C8%99buc\" title=\"<PERSON>\"><PERSON></a>, Romanian journalist and poet (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Co%C8%99buc\" title=\"<PERSON>\"><PERSON></a>, Romanian journalist and poet (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Co%C8%99buc"}]}, {"year": "1931", "text": "<PERSON>, German-American physicist and academic, Nobel Prize laureate (b. 1852)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1933", "text": "<PERSON>, English swimmer (b. 1872)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, German zoologist (b. 1877)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German zoologist (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German zoologist (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Danish businessman (b. 1866)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hrige\" title=\"<PERSON>\"><PERSON></a>, Danish businessman (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Thrige\" title=\"<PERSON>hrige\"><PERSON></a>, Danish businessman (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hrige"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Polish priest and saint (b. 1902)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/J%C3%B3zef_<PERSON>la\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish priest and saint (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3zef_<PERSON>la\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish priest and saint (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_<PERSON>la"}]}, {"year": "1944", "text": "<PERSON>, Korean poet and social reformer (b. 1879)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean poet and social reformer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean poet and social reformer (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Prince of Monaco (b. 1870)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (b. 1870)", "links": [{"title": "<PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Spanish mathematician and engineer (b. 1883)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Esteban_Terradas_i_Illa\" title=\"Esteban Terradas i Illa\"><PERSON>steban Terradas i Illa</a>, Spanish mathematician and engineer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Terradas_i_Illa\" title=\"Esteban Terradas i Illa\">Esteban Terradas i Illa</a>, Spanish mathematician and engineer (b. 1883)", "links": [{"title": "Esteban Terradas i Illa", "link": "https://wikipedia.org/wiki/Esteban_Terradas_i_Illa"}]}, {"year": "1957", "text": "<PERSON>, Sri Lankan banker and businessman (b. 1887)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan banker and businessman (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan banker and businessman (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Italian actor and singer (b. 1892)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and singer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and singer (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian activist and educator (b. 1887)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>il\" title=\"<PERSON><PERSON><PERSON><PERSON> Pat<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian activist and educator (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Patil\"><PERSON><PERSON><PERSON><PERSON></a>, Indian activist and educator (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Patil"}]}, {"year": "1965", "text": "<PERSON>, Austrian engineer and politician, 18th Chancellor of Austria (b. 1902)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian engineer and politician, 18th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian engineer and politician, 18th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_Figl"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1968", "text": "<PERSON>, American author, poet, and playwright (b. 1893)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_de_Acosta\" title=\"Mercedes de Acosta\"><PERSON> Acosta</a>, American author, poet, and playwright (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_de_Acosta\" title=\"Mercedes de Acosta\"><PERSON> Acos<PERSON></a>, American author, poet, and playwright (b. 1893)", "links": [{"title": "Mercedes de Acosta", "link": "https://wikipedia.org/wiki/Mercedes_de_Acosta"}]}, {"year": "1968", "text": "<PERSON>, American cartoonist, created <PERSON><PERSON><PERSON> (b. 1894)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_Orpha<PERSON>_<PERSON>\" title=\"Little Orphan <PERSON>\"><PERSON>n <PERSON></a></i> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_Orpha<PERSON>_<PERSON>\" title=\"Little Orphan <PERSON>\"><PERSON>n <PERSON></a></i> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress (b. 1883)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, British actor (b. 1878)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actor (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actor (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American union leader (b. 1907)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American union leader (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American union leader (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Norwegian author, poet, and playwright (b. 1920)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Jen<PERSON>_Bj%C3%B8rne<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian author, poet, and playwright (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jen<PERSON>_Bj%C3%B8rneboe\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian author, poet, and playwright (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jens_Bj%C3%B8rne<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, German militant, co-founded the Red Army Faction (b. 1934)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German militant, co-founded the <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German militant, co-founded the <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a> (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Red Army Faction", "link": "https://wikipedia.org/wiki/Red_Army_Faction"}]}, {"year": "1977", "text": "<PERSON>, American novelist (b. 1921)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist (b. 1921)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1978", "text": "<PERSON>, Italian journalist and activist (b. 1948)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Impastato\"><PERSON></a>, Italian journalist and activist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Impastato\"><PERSON></a>, Italian journalist and activist (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ato"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Italian lawyer and politician, 38th Prime Minister of Italy (b. 1916)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Moro"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1979", "text": "<PERSON>, Canadian-American banker, businessman, and philanthropist (b. 1883)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American banker, businessman, and philanthropist (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American banker, businessman, and philanthropist (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer and lyricist (b. 1918)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and lyricist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and lyricist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, South African activist (b. 1928)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American novelist and short story writer (b. 1909)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Norwegian singer and actor (b. 1931)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer and actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer and actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian soldier and railway engineer (b. 1891)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and railway engineer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and railway engineer (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor and director (b. 1915)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edmond_O%27Brien"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Nepalese mountaineer (b. 1914)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Tenzing_Norgay\" title=\"Tenzing Norgay\"><PERSON><PERSON> Nor<PERSON></a>, Nepalese mountaineer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tenzing_Norgay\" title=\"Tenzing Norgay\"><PERSON><PERSON> Nor<PERSON></a>, Nepalese mountaineer (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tenzing_Norgay"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian lawyer and politician (b. 1909)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian lawyer and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian lawyer and politician (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oba<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1989", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1954)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English novelist, short story writer, and critic (b. 1932)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, short story writer, and critic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, short story writer, and critic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South African activist (b. 1924)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Egyptian captain and politician (b. 1926)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian captain and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian captain and politician (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ya_At<PERSON>a"}]}, {"year": "1997", "text": "<PERSON>, Italian actor, director, and screenwriter (b. 1928)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress and singer (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Indian singer and actor (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer and actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer and actor (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American lieutenant, lawyer, and politician (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Chechen cleric and politician, 1st President of the Chechen Republic (b. 1951)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chechen cleric and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Chechen_Republic\" class=\"mw-redirect\" title=\"President of the Chechen Republic\">President of the Chechen Republic</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chechen cleric and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Chechen_Republic\" class=\"mw-redirect\" title=\"President of the Chechen Republic\">President of the Chechen Republic</a> (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Chechen Republic", "link": "https://wikipedia.org/wiki/President_of_the_Chechen_Republic"}]}, {"year": "2004", "text": "<PERSON>, American actor, producer, and screenwriter (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, South African singer (b. 1964)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African singer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African singer (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian soldier (b. 1901)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veteran)\" title=\"<PERSON> (veteran)\"><PERSON></a>, Canadian soldier (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veteran)\" title=\"<PERSON> (veteran)\"><PERSON></a>, Canadian soldier (b. 1901)", "links": [{"title": "<PERSON> (veteran)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veteran)"}]}, {"year": "2008", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster (b. 1929)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player, coach, and sportscaster (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player, coach, and sportscaster (b. 1929)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2008", "text": "<PERSON>, American football player (b. 1920)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Irish journalist and producer (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Nuala_O%27Faolain\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish journalist and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuala_O%27Faolain\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish journalist and producer (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuala_O%27F<PERSON>lain"}]}, {"year": "2008", "text": "<PERSON>, French singer, television host, and author (b. 1945)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer, television host, and author (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer, television host, and author (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American basketball player and coach (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American singer, actress, and activist (b. 1917)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actress, and activist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actress, and activist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Czech lawyer and politician (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech lawyer and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech lawyer and politician (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>jl"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist (b. 1984)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Wouter_<PERSON>\" title=\"Wout<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wout<PERSON>_<PERSON>\" title=\"Wout<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (b. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American psychologist, psychoanalyst, and academic (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychologist, psychoanalyst, and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychologist, psychoanalyst, and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Cook Islander lawyer and politician, 3rd Prime Minister of the Cook Islands (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cook Islander lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Cook_Islands\" title=\"Prime Minister of the Cook Islands\">Prime Minister of the Cook Islands</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cook Islander lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Cook_Islands\" title=\"Prime Minister of the Cook Islands\">Prime Minister of the Cook Islands</a> (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Cook Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Cook_Islands"}]}, {"year": "2012", "text": "<PERSON><PERSON>, English-American hairdresser and businessman (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Vidal_Sassoon\" title=\"Vidal Sassoon\"><PERSON><PERSON></a>, English-American hairdresser and businessman (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vidal_Sassoon\" title=\"Vidal Sassoon\"><PERSON><PERSON>sso<PERSON></a>, English-American hairdresser and businessman (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vidal_Sassoon"}]}, {"year": "2013", "text": "<PERSON>, Spanish footballer and manager (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Blanco_Rodr%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Blanco_Rodr%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Blanco_Rodr%C3%ADguez"}]}, {"year": "2013", "text": "<PERSON>, American soldier and politician, 36th Governor of Pennsylvania (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Leader\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Leader\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Leader"}, {"title": "Governor of Pennsylvania", "link": "https://wikipedia.org/wiki/Governor_of_Pennsylvania"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Mexican lawyer and politician, 23rd Governor of Hidalgo (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lu<PERSON>_Gil\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_Hidalgo\" class=\"mw-redirect\" title=\"Governor of Hidalgo\">Governor of Hidalgo</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lu<PERSON>_Gil\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_Hidalgo\" class=\"mw-redirect\" title=\"Governor of Hidalgo\">Governor of Hidalgo</a> (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lugo_Gil"}, {"title": "Governor of Hidalgo", "link": "https://wikipedia.org/wiki/Governor_of_Hidalgo"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Italian hurdler and fashion designer, founded <PERSON><PERSON> (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian hurdler and fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian hurdler and fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Italian priest and missionary (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and missionary (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and missionary (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician, 12th Chief Minister of Andhra Pradesh (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Nedurumalli_Janardhana_Reddy\" class=\"mw-redirect\" title=\"Nedurumalli Janardhana Reddy\"><PERSON><PERSON><PERSON><PERSON> Janardhana Reddy</a>, Indian politician, 12th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Andhra_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Andhra Pradesh\">Chief Minister of Andhra Pradesh</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nedurumalli_Janardhana_Reddy\" class=\"mw-redirect\" title=\"Nedurumalli Janardhana Reddy\"><PERSON><PERSON><PERSON><PERSON> Janardhana Reddy</a>, Indian politician, 12th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Andhra_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Andhra Pradesh\">Chief Minister of Andhra Pradesh</a> (b. 1935)", "links": [{"title": "Neduru<PERSON>li Jana<PERSON>hana Reddy", "link": "https://wikipedia.org/wiki/Nedurumalli_Janardhana_Reddy"}, {"title": "Chief Minister of Andhra Pradesh", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Andhra_Pradesh"}]}, {"year": "2014", "text": "<PERSON>, British author and poet (b. 1916)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, British author and poet (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, British author and poet (b. 1916)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "2015", "text": "<PERSON>, American football player and journalist (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and journalist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and journalist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Turkish general and politician, 7th President of Turkey (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "2015", "text": "<PERSON>, American actress (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, a Swiss-born Italian record producer, composer, musician and DJ (b. 1969)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Swiss-born Italian record producer, composer, musician and DJ (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Swiss-born Italian record producer, composer, musician and DJ (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Danish painter, poet, film maker and sculptor (b. 1938)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter, poet, film maker and sculptor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter, poet, film maker and sculptor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>,  English comedian, impressionist, singer and actor (b. 1943)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, impressionist, singer and actor (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, impressionist, singer and actor (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American singer, songwriter, and pianist (b. 1932)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richard\"><PERSON></a>, American singer, songwriter, and pianist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richard\"><PERSON></a>, American singer, songwriter, and pianist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American a writer and journalist (b. 1935)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American a writer and journalist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American a writer and journalist (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Japanese game developer (b. 1963)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Riek<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Kodam<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese game developer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Riek<PERSON>_<PERSON>\" title=\"<PERSON>iek<PERSON> Kodam<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese game developer (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Riek<PERSON>_<PERSON><PERSON>a"}]}, {"year": "2024", "text": "<PERSON>, American baseball player (b. 1980)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American film director, producer, and actor (b. 1926)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director, producer, and actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director, producer, and actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian political commentator (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian political commentator (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian political commentator (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}