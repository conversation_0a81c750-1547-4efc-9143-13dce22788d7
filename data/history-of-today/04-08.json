{"date": "April 8", "url": "https://wikipedia.org/wiki/April_8", "data": {"Events": [{"year": "217", "text": "Roman emperor <PERSON><PERSON><PERSON> is assassinated and is succeeded by his Praetorian Guard prefect, <PERSON>.", "html": "217 - <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Caracalla\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is assassinated and is succeeded by his <a href=\"https://wikipedia.org/wiki/Praetorian_Guard\" title=\"Praetorian Guard\">Praetorian Guard</a> <a href=\"https://wikipedia.org/wiki/Prefect\" title=\"Prefect\">prefect</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rinus\" class=\"mw-redirect\" title=\"<PERSON>ellius <PERSON>us\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Caracalla\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is assassinated and is succeeded by his <a href=\"https://wikipedia.org/wiki/Praetorian_Guard\" title=\"Praetorian Guard\">Praetorian Guard</a> <a href=\"https://wikipedia.org/wiki/Prefect\" title=\"Prefect\">prefect</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ellius_Macrinus\" class=\"mw-redirect\" title=\"<PERSON>ellius <PERSON>rinus\"><PERSON></a>.", "links": [{"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "Caracal<PERSON>", "link": "https://wikipedia.org/wiki/Caracalla"}, {"title": "Praetorian Guard", "link": "https://wikipedia.org/wiki/Praetorian_Guard"}, {"title": "Prefect", "link": "https://wikipedia.org/wiki/Prefect"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "876", "text": "The Battle of Dayr al-'Aqul saves Baghdad from the Saffarids.", "html": "876 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Dayr_al-%27Aqul\" class=\"mw-redirect\" title=\"Battle of Dayr al-'Aqul\">Battle of Dayr al-'Aqul</a> saves <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a> from the <a href=\"https://wikipedia.org/wiki/Saffarids\" class=\"mw-redirect\" title=\"Saffarids\">Saffarids</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Dayr_al-%27Aqul\" class=\"mw-redirect\" title=\"Battle of Dayr al-'Aqul\">Battle of Dayr al-'Aqul</a> saves <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a> from the <a href=\"https://wikipedia.org/wiki/Saffarids\" class=\"mw-redirect\" title=\"Saffarids\">Saffarids</a>.", "links": [{"title": "Battle of Dayr al-'Aqul", "link": "https://wikipedia.org/wiki/Battle_of_Dayr_al-%27Aqul"}, {"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}, {"title": "Saffarids", "link": "https://wikipedia.org/wiki/Saffarids"}]}, {"year": "1139", "text": "<PERSON> of Sicily is excommunicated by <PERSON> for supporting <PERSON><PERSON><PERSON> as pope for seven years, even though <PERSON> had already publicly recognized <PERSON>'s claim to the papacy.", "html": "1139 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> II of Sicily\"><PERSON> of Sicily</a> is excommunicated by <a href=\"https://wikipedia.org/wiki/Innocent_II\" class=\"mw-redirect\" title=\"Innocent II\"><PERSON> II</a> for supporting <a href=\"https://wikipedia.org/wiki/Antipope_Anacletus_II\" title=\"Antipope Anacletus II\"><PERSON><PERSON><PERSON> II</a> as <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a> for seven years, even though <PERSON> had already publicly recognized <PERSON>'s claim to the papacy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> II of Sicily\"><PERSON> of Sicily</a> is excommunicated by <a href=\"https://wikipedia.org/wiki/Innocent_II\" class=\"mw-redirect\" title=\"Innocent II\"><PERSON> II</a> for supporting <a href=\"https://wikipedia.org/wiki/Antipope_Anacletus_II\" title=\"Antipope Anacletus II\"><PERSON><PERSON><PERSON> II</a> as <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a> for seven years, even though <PERSON> had already publicly recognized <PERSON>'s claim to the papacy.", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}, {"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/Innocent_II"}, {"title": "Antipope <PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/Antipope_Anacletus_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}]}, {"year": "1232", "text": "Mongol-Jin War: The Mongols begin their siege on Kaifeng, the capital of the Jin dynasty.", "html": "1232 - <a href=\"https://wikipedia.org/wiki/Mongol_conquest_of_the_Jin_dynasty\" title=\"Mongol conquest of the Jin dynasty\">Mongol-Jin War</a>: The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> begin their <a href=\"https://wikipedia.org/wiki/Mongol_siege_of_Kaifeng\" class=\"mw-redirect\" title=\"Mongol siege of Kaifeng\">siege on Kaifeng</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)\" title=\"Jin dynasty (1115-1234)\">Jin dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongol_conquest_of_the_Jin_dynasty\" title=\"Mongol conquest of the Jin dynasty\">Mongol-Jin War</a>: The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> begin their <a href=\"https://wikipedia.org/wiki/Mongol_siege_of_Kaifeng\" class=\"mw-redirect\" title=\"Mongol siege of Kaifeng\">siege on Kaifeng</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)\" title=\"Jin dynasty (1115-1234)\">Jin dynasty</a>.", "links": [{"title": "Mongol conquest of the Jin dynasty", "link": "https://wikipedia.org/wiki/Mongol_conquest_of_the_Jin_dynasty"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}, {"title": "Mongol siege of Kaifeng", "link": "https://wikipedia.org/wiki/Mongol_siege_of_Kaifeng"}, {"title": "Jin dynasty (1115-1234)", "link": "https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)"}]}, {"year": "1250", "text": "Seventh Crusade: Ayyubids of Egypt capture King <PERSON> of France in the Battle of Fariskur.", "html": "1250 - <a href=\"https://wikipedia.org/wiki/Seventh_Crusade\" title=\"Seventh Crusade\">Seventh Crusade</a>: <a href=\"https://wikipedia.org/wiki/Ayyubid_dynasty\" title=\"Ayyubid dynasty\">Ayyubids</a> of Egypt capture King <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"Louis IX of France\"><PERSON> of France</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Fariskur_(1250)\" title=\"Battle of Fariskur (1250)\">Battle of Fariskur</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seventh_Crusade\" title=\"Seventh Crusade\">Seventh Crusade</a>: <a href=\"https://wikipedia.org/wiki/Ayyubid_dynasty\" title=\"Ayyubid dynasty\">Ayyubids</a> of Egypt capture King <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"Louis IX of France\"><PERSON> of France</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Fariskur_(1250)\" title=\"Battle of Fariskur (1250)\">Battle of Fariskur</a>.", "links": [{"title": "Seventh Crusade", "link": "https://wikipedia.org/wiki/Seventh_Crusade"}, {"title": "Ayyubid dynasty", "link": "https://wikipedia.org/wiki/Ayyubid_dynasty"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}, {"title": "Battle of Fariskur (1250)", "link": "https://wikipedia.org/wiki/Battle_of_Fariskur_(1250)"}]}, {"year": "1271", "text": "In Syria, sultan <PERSON><PERSON><PERSON> conquers the Krak des Chevaliers.", "html": "1271 - In <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, sultan <a href=\"https://wikipedia.org/wiki/Baibars\" class=\"mw-redirect\" title=\"Baibars\">Baibars</a> conquers the <a href=\"https://wikipedia.org/wiki/Krak_des_Chevaliers\" title=\"Krak des Chevaliers\">Krak des Chevaliers</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, sultan <a href=\"https://wikipedia.org/wiki/Baibars\" class=\"mw-redirect\" title=\"Baibars\">Baibars</a> conquers the <a href=\"https://wikipedia.org/wiki/Krak_des_Chevaliers\" title=\"Krak des Chevaliers\"><PERSON><PERSON> des Chevaliers</a>.", "links": [{"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "Baibars", "link": "https://wikipedia.org/wiki/Baibars"}, {"title": "Krak des Chevaliers", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>s"}]}, {"year": "1605", "text": "The city of Oulu, Finland, is founded by <PERSON> of Sweden.", "html": "1605 - The city of <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"Oulu\">Oulu</a>, Finland, is founded by <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_Sweden\" title=\"Charles IX of Sweden\"><PERSON> of Sweden</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"Oulu\">Oulu</a>, Finland, is founded by <a href=\"https://wikipedia.org/wiki/Charles_IX_of_Sweden\" title=\"Charles IX of Sweden\"><PERSON> of Sweden</a>.", "links": [{"title": "Oulu", "link": "https://wikipedia.org/wiki/Oulu"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_IX_of_Sweden"}]}, {"year": "1730", "text": "Shearith Israel, the first synagogue in continental North America, is dedicated.", "html": "1730 - <a href=\"https://wikipedia.org/wiki/Congregation_Shearith_Israel\" title=\"Congregation Shearith Israel\">Shearith Israel</a>, the first <a href=\"https://wikipedia.org/wiki/Synagogue\" title=\"Synagogue\">synagogue</a> in continental North America, is dedicated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Congregation_Shearith_Israel\" title=\"Congregation Shearith Israel\">Shearith Israel</a>, the first <a href=\"https://wikipedia.org/wiki/Synagogue\" title=\"Synagogue\">synagogue</a> in continental North America, is dedicated.", "links": [{"title": "Congregation Shearith Israel", "link": "https://wikipedia.org/wiki/Congregation_Shearith_Israel"}, {"title": "Synagogue", "link": "https://wikipedia.org/wiki/Synagogue"}]}, {"year": "1812", "text": "<PERSON><PERSON>, the Russian Emperor and the Grand Duke of Finland, officially announces the transfer of the status of the Finnish capital from Turku to Helsinki.", "html": "1812 - <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> I of Russia\"><PERSON> I</a>, the <a href=\"https://wikipedia.org/wiki/Emperor_of_Russia\" title=\"Emperor of Russia\">Russian Emperor</a> and the <a href=\"https://wikipedia.org/wiki/Grand_Duke_of_Finland\" title=\"Grand Duke of Finland\">Grand Duke of Finland</a>, officially announces the transfer of the status of the Finnish capital from <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a> to <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "no_year_html": "<PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> I</a>, the <a href=\"https://wikipedia.org/wiki/Emperor_of_Russia\" title=\"Emperor of Russia\">Russian Emperor</a> and the <a href=\"https://wikipedia.org/wiki/Grand_Duke_of_Finland\" title=\"Grand Duke of Finland\">Grand Duke of Finland</a>, officially announces the transfer of the status of the Finnish capital from <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a> to <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}, {"title": "Emperor of Russia", "link": "https://wikipedia.org/wiki/Emperor_of_Russia"}, {"title": "Grand Duke of Finland", "link": "https://wikipedia.org/wiki/Grand_Duke_of_Finland"}, {"title": "Turku", "link": "https://wikipedia.org/wiki/Turku"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1820", "text": "The Venus de Milo is discovered on the Aegean island of Milos.", "html": "1820 - The <i><a href=\"https://wikipedia.org/wiki/Venus_de_Milo\" title=\"Venus de Milo\">Venus de Milo</a></i> is discovered on the <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean</a> island of <a href=\"https://wikipedia.org/wiki/Milos\" title=\"Milos\">Milos</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Venus_de_Milo\" title=\"Venus de Milo\">Venus de Milo</a></i> is discovered on the <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean</a> island of <a href=\"https://wikipedia.org/wiki/Milos\" title=\"Milos\">Milo<PERSON></a>.", "links": [{"title": "Venus de Milo", "link": "https://wikipedia.org/wiki/Venus_de_Milo"}, {"title": "Aegean Sea", "link": "https://wikipedia.org/wiki/Aegean_Sea"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milos"}]}, {"year": "1832", "text": "Black Hawk War: Around 300 United States 6th Infantry troops leave St. Louis, Missouri to fight the Sauk Native Americans.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Black_Hawk_War\" title=\"Black Hawk War\">Black Hawk War</a>: Around 300 United States 6th Infantry troops leave <a href=\"https://wikipedia.org/wiki/St._Louis,_Missouri\" class=\"mw-redirect\" title=\"St. Louis, Missouri\">St. Louis, Missouri</a> to fight the <a href=\"https://wikipedia.org/wiki/Sauk_people\" title=\"Sauk people\">Sauk</a> <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native Americans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Hawk_War\" title=\"Black Hawk War\">Black Hawk War</a>: Around 300 United States 6th Infantry troops leave <a href=\"https://wikipedia.org/wiki/St._Louis,_Missouri\" class=\"mw-redirect\" title=\"St. Louis, Missouri\">St. Louis, Missouri</a> to fight the <a href=\"https://wikipedia.org/wiki/Sauk_people\" title=\"Sauk people\">Sauk</a> <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native Americans</a>.", "links": [{"title": "Black Hawk War", "link": "https://wikipedia.org/wiki/Black_Hawk_War"}, {"title": "St. Louis, Missouri", "link": "https://wikipedia.org/wiki/St._Louis,_Missouri"}, {"title": "Sauk people", "link": "https://wikipedia.org/wiki/Sauk_people"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}]}, {"year": "1866", "text": "Austro-Prussian War: Italy and Prussia sign a secret alliance against the Austrian Empire.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Austro-Prussian_War\" title=\"Austro-Prussian War\">Austro-Prussian War</a>: <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> sign a secret alliance against the <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austro-Prussian_War\" title=\"Austro-Prussian War\">Austro-Prussian War</a>: <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> sign a secret alliance against the <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian Empire</a>.", "links": [{"title": "Austro-Prussian War", "link": "https://wikipedia.org/wiki/Austro-Prussian_War"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}]}, {"year": "1886", "text": "<PERSON> introduces the first Irish Home Rule Bill into the British House of Commons.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> introduces the first <a href=\"https://wikipedia.org/wiki/Irish_Government_Bill_1886\" class=\"mw-redirect\" title=\"Irish Government Bill 1886\">Irish Home Rule Bill</a> into the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">British House of Commons</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> introduces the first <a href=\"https://wikipedia.org/wiki/Irish_Government_Bill_1886\" class=\"mw-redirect\" title=\"Irish Government Bill 1886\">Irish Home Rule Bill</a> into the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">British House of Commons</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Irish Government Bill 1886", "link": "https://wikipedia.org/wiki/Irish_Government_Bill_1886"}, {"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}]}, {"year": "1895", "text": "In <PERSON>ock v. Farmers' Loan & Trust Co. the Supreme Court of the United States declares unapportioned income tax to be unconstitutional.", "html": "1895 - In <i><a href=\"https://wikipedia.org/wiki/Pollock_v._Farmers%27_<PERSON>an_%26_Trust_Co.\" title=\"Pollock v. Farmers' Loan &amp; Trust Co.\">Pollock v. Farmers' Loan &amp; Trust Co.</a></i> the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> declares unapportioned <a href=\"https://wikipedia.org/wiki/Income_tax_in_the_United_States\" title=\"Income tax in the United States\">income tax</a> to be <a href=\"https://wikipedia.org/wiki/Constitutionality\" title=\"Constitutionality\">unconstitutional</a>.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/Pollock_v._Farmers%27_<PERSON>an_%26_Trust_Co.\" title=\"Pollock v. Farmers' Loan &amp; Trust Co.\">Pollock v. Farmers' Loan &amp; Trust Co.</a></i> the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> declares unapportioned <a href=\"https://wikipedia.org/wiki/Income_tax_in_the_United_States\" title=\"Income tax in the United States\">income tax</a> to be <a href=\"https://wikipedia.org/wiki/Constitutionality\" title=\"Constitutionality\">unconstitutional</a>.", "links": [{"title": "Pollock v. Farmers' Loan & Trust Co.", "link": "https://wikipedia.org/wiki/Pollock_v._Farmers%27_<PERSON><PERSON>_%26_Trust_Co."}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Income tax in the United States", "link": "https://wikipedia.org/wiki/Income_tax_in_the_United_States"}, {"title": "Constitutionality", "link": "https://wikipedia.org/wiki/Constitutionality"}]}, {"year": "1904", "text": "The French Third Republic and the United Kingdom of Great Britain and Ireland sign the Entente cordiale.", "html": "1904 - The <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">French Third Republic</a> and the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a> sign the <i><a href=\"https://wikipedia.org/wiki/Entente_cordiale\" class=\"mw-redirect\" title=\"Entente cordiale\">Entente cordiale</a></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">French Third Republic</a> and the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a> sign the <i><a href=\"https://wikipedia.org/wiki/Entente_cordiale\" class=\"mw-redirect\" title=\"Entente cordiale\">Entente cordiale</a></i>.", "links": [{"title": "French Third Republic", "link": "https://wikipedia.org/wiki/French_Third_Republic"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "Entente cordiale", "link": "https://wikipedia.org/wiki/Entente_cordiale"}]}, {"year": "1906", "text": "<PERSON>, the first person to be diagnosed with Alzheimer's disease, dies.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first person to be diagnosed with <a href=\"https://wikipedia.org/wiki/Alzheimer%27s_disease\" title=\"Alzheimer's disease\">Alzheimer's disease</a>, dies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first person to be diagnosed with <a href=\"https://wikipedia.org/wiki/Alzheimer%27s_disease\" title=\"Alzheimer's disease\">Alzheimer's disease</a>, dies.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Alzheimer's disease", "link": "https://wikipedia.org/wiki/Alzheimer%27s_disease"}]}, {"year": "1908", "text": "Harvard University votes to establish the Harvard Business School.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Harvard_University\" title=\"Harvard University\">Harvard University</a> votes to establish the <a href=\"https://wikipedia.org/wiki/Harvard_Business_School\" title=\"Harvard Business School\">Harvard Business School</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harvard_University\" title=\"Harvard University\">Harvard University</a> votes to establish the <a href=\"https://wikipedia.org/wiki/Harvard_Business_School\" title=\"Harvard Business School\">Harvard Business School</a>.", "links": [{"title": "Harvard University", "link": "https://wikipedia.org/wiki/Harvard_University"}, {"title": "Harvard Business School", "link": "https://wikipedia.org/wiki/Harvard_Business_School"}]}, {"year": "1911", "text": "Dutch physicist <PERSON><PERSON> discovers superconductivity.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Dutch_people\" title=\"Dutch people\">Dutch</a> physicist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Superconductivity\" title=\"Superconductivity\">superconductivity</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dutch_people\" title=\"Dutch people\">Dutch</a> physicist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>nes\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Superconductivity\" title=\"Superconductivity\">superconductivity</a>.", "links": [{"title": "Dutch people", "link": "https://wikipedia.org/wiki/Dutch_people"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Superconductivity", "link": "https://wikipedia.org/wiki/Superconductivity"}]}, {"year": "1913", "text": "The Seventeenth Amendment to the United States Constitution, requiring direct election of Senators, becomes law.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Seventeenth_Amendment_to_the_United_States_Constitution\" title=\"Seventeenth Amendment to the United States Constitution\">Seventeenth Amendment to the United States Constitution</a>, requiring direct <a href=\"https://wikipedia.org/wiki/Election\" title=\"Election\">election</a> of <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senators</a>, becomes law.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Seventeenth_Amendment_to_the_United_States_Constitution\" title=\"Seventeenth Amendment to the United States Constitution\">Seventeenth Amendment to the United States Constitution</a>, requiring direct <a href=\"https://wikipedia.org/wiki/Election\" title=\"Election\">election</a> of <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senators</a>, becomes law.", "links": [{"title": "Seventeenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Seventeenth_Amendment_to_the_United_States_Constitution"}, {"title": "Election", "link": "https://wikipedia.org/wiki/Election"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1918", "text": "World War I: Actors <PERSON> and <PERSON> sell war bonds on the streets of New York City's financial district.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Actors <a href=\"https://wikipedia.org/wiki/Douglas_Fairbanks\" title=\"Douglas Fairbanks\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sell <a href=\"https://wikipedia.org/wiki/War_bond\" title=\"War bond\">war bonds</a> on the streets of New York City's financial district.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Actors <a href=\"https://wikipedia.org/wiki/<PERSON>_Fairbanks\" title=\"Douglas Fairbanks\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sell <a href=\"https://wikipedia.org/wiki/War_bond\" title=\"War bond\">war bonds</a> on the streets of New York City's financial district.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "War bond", "link": "https://wikipedia.org/wiki/War_bond"}]}, {"year": "1924", "text": "Sharia courts are abolished in Turkey, as part of Atatürk's Reforms.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Sharia\" title=\"Sharia\">Sharia</a> courts are abolished in <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, as part of <a href=\"https://wikipedia.org/wiki/Atat%C3%BCrk%27s_Reforms\" class=\"mw-redirect\" title=\"Atatürk's Reforms\">Atatürk's Reforms</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sharia\" title=\"Sharia\">Sharia</a> courts are abolished in <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, as part of <a href=\"https://wikipedia.org/wiki/Atat%C3%BCrk%27s_Reforms\" class=\"mw-redirect\" title=\"Atatürk's Reforms\">Atatürk's Reforms</a>.", "links": [{"title": "Sharia", "link": "https://wikipedia.org/wiki/Sharia"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Atatürk's Reforms", "link": "https://wikipedia.org/wiki/Atat%C3%BCrk%27s_Reforms"}]}, {"year": "1929", "text": "Indian independence movement: At the Delhi Central Assembly, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> throw handouts and bombs to court arrest.", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Indian_independence_movement\" title=\"Indian independence movement\">Indian independence movement</a>: At the Delhi Central Assembly, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> throw handouts and bombs to court arrest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_independence_movement\" title=\"Indian independence movement\">Indian independence movement</a>: At the Delhi Central Assembly, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> throw handouts and bombs to court arrest.", "links": [{"title": "Indian independence movement", "link": "https://wikipedia.org/wiki/Indian_independence_movement"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "The Works Progress Administration is formed when the Emergency Relief Appropriation Act of 1935 becomes law.", "html": "1935 - The <a href=\"https://wikipedia.org/wiki/Works_Progress_Administration\" title=\"Works Progress Administration\">Works Progress Administration</a> is formed when the <a href=\"https://wikipedia.org/wiki/Emergency_Relief_Appropriation_Act_of_1935\" title=\"Emergency Relief Appropriation Act of 1935\">Emergency Relief Appropriation Act of 1935</a> becomes law.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Works_Progress_Administration\" title=\"Works Progress Administration\">Works Progress Administration</a> is formed when the <a href=\"https://wikipedia.org/wiki/Emergency_Relief_Appropriation_Act_of_1935\" title=\"Emergency Relief Appropriation Act of 1935\">Emergency Relief Appropriation Act of 1935</a> becomes law.", "links": [{"title": "Works Progress Administration", "link": "https://wikipedia.org/wiki/Works_Progress_Administration"}, {"title": "Emergency Relief Appropriation Act of 1935", "link": "https://wikipedia.org/wiki/Emergency_Relief_Appropriation_Act_of_1935"}]}, {"year": "1940", "text": "The Central Committee of the Mongolian People's Revolutionary Party elects <PERSON><PERSON><PERSON><PERSON><PERSON> as General Secretary, marking the beginning of his 44-year-long tenure as de facto leader of Mongolia.", "html": "1940 - The Central Committee of the <a href=\"https://wikipedia.org/wiki/Mongolian_People%27s_Party\" title=\"Mongolian People's Party\">Mongolian People's Revolutionary Party</a> elects <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>enbal\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Mongolian_People%27s_Party\" class=\"mw-redirect\" title=\"General Secretary of the Mongolian People's Party\">General Secretary</a>, marking the beginning of his 44-year-long tenure as <i>de facto</i> leader of <a href=\"https://wikipedia.org/wiki/Mongolian_People%27s_Republic\" title=\"Mongolian People's Republic\">Mongolia</a>.", "no_year_html": "The Central Committee of the <a href=\"https://wikipedia.org/wiki/Mongolian_People%27s_Party\" title=\"Mongolian People's Party\">Mongolian People's Revolutionary Party</a> elects <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>enbal\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Mongolian_People%27s_Party\" class=\"mw-redirect\" title=\"General Secretary of the Mongolian People's Party\">General Secretary</a>, marking the beginning of his 44-year-long tenure as <i>de facto</i> leader of <a href=\"https://wikipedia.org/wiki/Mongolian_People%27s_Republic\" title=\"Mongolian People's Republic\">Mongolia</a>.", "links": [{"title": "Mongolian People's Party", "link": "https://wikipedia.org/wiki/Mongolian_People%27s_Party"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_T<PERSON>enbal"}, {"title": "General Secretary of the Mongolian People's Party", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Mongolian_People%27s_Party"}, {"title": "Mongolian People's Republic", "link": "https://wikipedia.org/wiki/Mongolian_People%27s_Republic"}]}, {"year": "1942", "text": "World War II: The Japanese take Bataan in the Philippines.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> take <a href=\"https://wikipedia.org/wiki/Bataan\" title=\"Bataan\">Bataan</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> take <a href=\"https://wikipedia.org/wiki/Bataan\" title=\"Bataan\">Bataan</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Bataan", "link": "https://wikipedia.org/wiki/Bataan"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1943", "text": "U.S. President <PERSON>, in an attempt to check inflation, freezes wages and prices, prohibits workers from changing jobs unless the war effort would be aided thereby, and bars rate increases by common carriers and public utilities.", "html": "1943 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in an attempt to check <a href=\"https://wikipedia.org/wiki/Inflation\" title=\"Inflation\">inflation</a>, freezes wages and prices, prohibits workers from changing jobs unless the war effort would be aided thereby, and bars rate increases by <a href=\"https://wikipedia.org/wiki/Common_carrier\" title=\"Common carrier\">common carriers</a> and <a href=\"https://wikipedia.org/wiki/Public_utilities\" class=\"mw-redirect\" title=\"Public utilities\">public utilities</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in an attempt to check <a href=\"https://wikipedia.org/wiki/Inflation\" title=\"Inflation\">inflation</a>, freezes wages and prices, prohibits workers from changing jobs unless the war effort would be aided thereby, and bars rate increases by <a href=\"https://wikipedia.org/wiki/Common_carrier\" title=\"Common carrier\">common carriers</a> and <a href=\"https://wikipedia.org/wiki/Public_utilities\" class=\"mw-redirect\" title=\"Public utilities\">public utilities</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Inflation", "link": "https://wikipedia.org/wiki/Inflation"}, {"title": "Common carrier", "link": "https://wikipedia.org/wiki/Common_carrier"}, {"title": "Public utilities", "link": "https://wikipedia.org/wiki/Public_utilities"}]}, {"year": "1943", "text": "<PERSON> and <PERSON> are executed in Berlin for their anti-Nazi activities.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are executed in <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a> for their anti-Nazi activities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are executed in <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a> for their anti-Nazi activities.", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Berlin", "link": "https://wikipedia.org/wiki/Berlin"}]}, {"year": "1945", "text": "World War II: After an air raid accidentally destroys a train carrying about 4,000 Nazi concentration camp internees in Prussian Hanover, the survivors are massacred by Nazis.", "html": "1945 - World War II: After an air raid accidentally destroys a train carrying about 4,000 <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camp\" class=\"mw-redirect\" title=\"Nazi concentration camp\">Nazi concentration camp</a> internees in <a href=\"https://wikipedia.org/wiki/Prussian_Hanover\" class=\"mw-redirect\" title=\"Prussian Hanover\">Prussian Hanover</a>, the survivors are <a href=\"https://wikipedia.org/wiki/Celler_Hasenjagd\" class=\"mw-redirect\" title=\"Celler Hasenjagd\">massacred</a> by Nazis.", "no_year_html": "World War II: After an air raid accidentally destroys a train carrying about 4,000 <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camp\" class=\"mw-redirect\" title=\"Nazi concentration camp\">Nazi concentration camp</a> internees in <a href=\"https://wikipedia.org/wiki/Prussian_Hanover\" class=\"mw-redirect\" title=\"Prussian Hanover\">Prussian Hanover</a>, the survivors are <a href=\"https://wikipedia.org/wiki/Celler_Hasenjagd\" class=\"mw-redirect\" title=\"Celler Hasenjagd\">massacred</a> by Nazis.", "links": [{"title": "Nazi concentration camp", "link": "https://wikipedia.org/wiki/Nazi_concentration_camp"}, {"title": "Prussian Hanover", "link": "https://wikipedia.org/wiki/Prussian_Hanover"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>jagd"}]}, {"year": "1946", "text": "Électricité de France, the world's largest utility company, is formed as a result of the nationalisation of a number of electricity producers, transporters and distributors.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/%C3%89lectricit%C3%A9_de_France\" title=\"Électricité de France\">Électricité de France</a>, the world's largest <a href=\"https://wikipedia.org/wiki/Utility_company\" class=\"mw-redirect\" title=\"Utility company\">utility company</a>, is formed as a result of the <a href=\"https://wikipedia.org/wiki/Nationalisation\" class=\"mw-redirect\" title=\"Nationalisation\">nationalisation</a> of a number of electricity producers, transporters and distributors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lectricit%C3%A9_de_France\" title=\"Électricité de France\">Électricité de France</a>, the world's largest <a href=\"https://wikipedia.org/wiki/Utility_company\" class=\"mw-redirect\" title=\"Utility company\">utility company</a>, is formed as a result of the <a href=\"https://wikipedia.org/wiki/Nationalisation\" class=\"mw-redirect\" title=\"Nationalisation\">nationalisation</a> of a number of electricity producers, transporters and distributors.", "links": [{"title": "Électricité de France", "link": "https://wikipedia.org/wiki/%C3%89lectricit%C3%A9_de_France"}, {"title": "Utility company", "link": "https://wikipedia.org/wiki/Utility_company"}, {"title": "Nationalisation", "link": "https://wikipedia.org/wiki/Nationalisation"}]}, {"year": "1950", "text": "India and Pakistan sign the Liaquat-Nehru Pact.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> and <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> sign the <a href=\"https://wikipedia.org/wiki/Liaquat%E2%80%93Nehru_Pact\" title=\"Liaquat-Nehru Pact\">Liaquat-Nehru Pact</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> and <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> sign the <a href=\"https://wikipedia.org/wiki/Liaquat%E2%80%93Nehru_Pact\" title=\"Liaquat-Nehru Pact\">Liaquat-Nehru Pact</a>.", "links": [{"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Liaquat-Nehru Pact", "link": "https://wikipedia.org/wiki/Liaquat%E2%80%93Nehru_Pact"}]}, {"year": "1952", "text": "U.S. President <PERSON> calls for the seizure of all domestic steel mills in an attempt to prevent the 1952 steel strike.", "html": "1952 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> calls for the seizure of all domestic steel mills in an attempt to prevent the <a href=\"https://wikipedia.org/wiki/1952_steel_strike\" title=\"1952 steel strike\">1952 steel strike</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> calls for the seizure of all domestic steel mills in an attempt to prevent the <a href=\"https://wikipedia.org/wiki/1952_steel_strike\" title=\"1952 steel strike\">1952 steel strike</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1952 steel strike", "link": "https://wikipedia.org/wiki/1952_steel_strike"}]}, {"year": "1953", "text": "Mau Ma<PERSON> leader <PERSON><PERSON> is convicted by British Kenya's rulers.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Mau_<PERSON>u_rebellion\" title=\"Mau Mau rebellion\"><PERSON><PERSON> <PERSON></a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is convicted by <a href=\"https://wikipedia.org/wiki/British_Kenya\" class=\"mw-redirect\" title=\"British Kenya\">British Kenya</a>'s rulers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mau_<PERSON>u_rebellion\" title=\"Mau Mau rebellion\"><PERSON><PERSON> <PERSON></a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is convicted by <a href=\"https://wikipedia.org/wiki/British_Kenya\" class=\"mw-redirect\" title=\"British Kenya\">British Kenya</a>'s rulers.", "links": [{"title": "Mau Mau rebellion", "link": "https://wikipedia.org/wiki/Mau_<PERSON>u_rebellion"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jomo_Kenyatta"}, {"title": "British Kenya", "link": "https://wikipedia.org/wiki/British_Kenya"}]}, {"year": "1954", "text": "A Royal Canadian Air Force Canadair Harvard collides with a Trans-Canada Airlines Canadair North Star over Moose Jaw, Saskatchewan, killing 37 people.", "html": "1954 - A Royal Canadian Air Force Canadair <a href=\"https://wikipedia.org/wiki/T-6_Texan\" class=\"mw-redirect\" title=\"T-6 Texan\">Harvard</a> <a href=\"https://wikipedia.org/wiki/Trans-Canada_Air_Lines_Flight_9\" title=\"Trans-Canada Air Lines Flight 9\">collides</a> with a <a href=\"https://wikipedia.org/wiki/Trans-Canada_Air_Lines\" title=\"Trans-Canada Air Lines\">Trans-Canada Airlines</a> <a href=\"https://wikipedia.org/wiki/Canadair_North_Star\" title=\"Canadair North Star\">Canadair North Star</a> over <a href=\"https://wikipedia.org/wiki/Moose_Jaw,_Saskatchewan\" class=\"mw-redirect\" title=\"Moose Jaw, Saskatchewan\">Moose Jaw, Saskatchewan</a>, killing 37 people.", "no_year_html": "A Royal Canadian Air Force Canadair <a href=\"https://wikipedia.org/wiki/T-6_Texan\" class=\"mw-redirect\" title=\"T-6 Texan\">Harvard</a> <a href=\"https://wikipedia.org/wiki/Trans-Canada_Air_Lines_Flight_9\" title=\"Trans-Canada Air Lines Flight 9\">collides</a> with a <a href=\"https://wikipedia.org/wiki/Trans-Canada_Air_Lines\" title=\"Trans-Canada Air Lines\">Trans-Canada Airlines</a> <a href=\"https://wikipedia.org/wiki/Canadair_North_Star\" title=\"Canadair North Star\">Canadair North Star</a> over <a href=\"https://wikipedia.org/wiki/Moose_Jaw,_Saskatchewan\" class=\"mw-redirect\" title=\"Moose Jaw, Saskatchewan\">Moose Jaw, Saskatchewan</a>, killing 37 people.", "links": [{"title": "T-6 Texan", "link": "https://wikipedia.org/wiki/T-6_Texan"}, {"title": "Trans-Canada Air Lines Flight 9", "link": "https://wikipedia.org/wiki/Trans-Canada_Air_Lines_Flight_9"}, {"title": "Trans-Canada Air Lines", "link": "https://wikipedia.org/wiki/Trans-Canada_Air_Lines"}, {"title": "Canadair North Star", "link": "https://wikipedia.org/wiki/Canadair_North_Star"}, {"title": "Moose Jaw, Saskatchewan", "link": "https://wikipedia.org/wiki/Moose_Jaw,_Saskatchewan"}]}, {"year": "1954", "text": "South African Airways Flight 201: A de Havilland DH.106 Comet 1 crashes into the sea during night killing 21 people.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/South_African_Airways_Flight_201\" title=\"South African Airways Flight 201\">South African Airways Flight 201</a>: A <a href=\"https://wikipedia.org/wiki/De_Havilland_Comet\" title=\"De Havilland Comet\">de Havilland DH.106 Comet 1</a> crashes into the sea during night killing 21 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_African_Airways_Flight_201\" title=\"South African Airways Flight 201\">South African Airways Flight 201</a>: A <a href=\"https://wikipedia.org/wiki/De_Havilland_Comet\" title=\"De Havilland Comet\">de Havilland DH.106 Comet 1</a> crashes into the sea during night killing 21 people.", "links": [{"title": "South African Airways Flight 201", "link": "https://wikipedia.org/wiki/South_African_Airways_Flight_201"}, {"title": "De Havilland Comet", "link": "https://wikipedia.org/wiki/De_Havilland_Comet"}]}, {"year": "1959", "text": "A team of computer manufacturers, users, and university people led by <PERSON> meets to discuss the creation of a new programming language that would be called COBOL.", "html": "1959 - A team of computer manufacturers, users, and university people led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> meets to discuss the creation of a new <a href=\"https://wikipedia.org/wiki/Programming_language\" title=\"Programming language\">programming language</a> that would be called <a href=\"https://wikipedia.org/wiki/COBOL\" title=\"COBOL\">COBOL</a>.", "no_year_html": "A team of computer manufacturers, users, and university people led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> meets to discuss the creation of a new <a href=\"https://wikipedia.org/wiki/Programming_language\" title=\"Programming language\">programming language</a> that would be called <a href=\"https://wikipedia.org/wiki/COBOL\" title=\"COBOL\">COBOL</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Programming language", "link": "https://wikipedia.org/wiki/Programming_language"}, {"title": "COBOL", "link": "https://wikipedia.org/wiki/COBOL"}]}, {"year": "1959", "text": "The Organization of American States drafts an agreement to create the Inter-American Development Bank.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Organization_of_American_States\" title=\"Organization of American States\">Organization of American States</a> drafts an agreement to create the <a href=\"https://wikipedia.org/wiki/Inter-American_Development_Bank\" title=\"Inter-American Development Bank\">Inter-American Development Bank</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Organization_of_American_States\" title=\"Organization of American States\">Organization of American States</a> drafts an agreement to create the <a href=\"https://wikipedia.org/wiki/Inter-American_Development_Bank\" title=\"Inter-American Development Bank\">Inter-American Development Bank</a>.", "links": [{"title": "Organization of American States", "link": "https://wikipedia.org/wiki/Organization_of_American_States"}, {"title": "Inter-American Development Bank", "link": "https://wikipedia.org/wiki/Inter-American_Development_Bank"}]}, {"year": "1960", "text": "The Netherlands and West Germany sign an agreement to negotiate the return of German land annexed by the Dutch in return for 280 million German marks as Wiedergutmachung.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a> and <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> sign an agreement to negotiate the return of <a href=\"https://wikipedia.org/wiki/Dutch_annexation_of_German_territory_after_the_Second_World_War\" title=\"Dutch annexation of German territory after the Second World War\">German land annexed by the Dutch</a> in return for 280 million <a href=\"https://wikipedia.org/wiki/German_mark\" class=\"mw-redirect\" title=\"German mark\">German marks</a> as <a href=\"https://wikipedia.org/wiki/Wiedergutmachung\" title=\"Wiedergutmachung\">Wiedergutmachung</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a> and <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> sign an agreement to negotiate the return of <a href=\"https://wikipedia.org/wiki/Dutch_annexation_of_German_territory_after_the_Second_World_War\" title=\"Dutch annexation of German territory after the Second World War\">German land annexed by the Dutch</a> in return for 280 million <a href=\"https://wikipedia.org/wiki/German_mark\" class=\"mw-redirect\" title=\"German mark\">German marks</a> as <a href=\"https://wikipedia.org/wiki/Wiedergutmachung\" title=\"Wiedergutmachung\">Wiedergutmachung</a>.", "links": [{"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}, {"title": "Dutch annexation of German territory after the Second World War", "link": "https://wikipedia.org/wiki/Dutch_annexation_of_German_territory_after_the_Second_World_War"}, {"title": "German mark", "link": "https://wikipedia.org/wiki/German_mark"}, {"title": "Wiedergutmachung", "link": "https://wikipedia.org/wiki/Wiedergutmachung"}]}, {"year": "1968", "text": "BOAC Flight 712 catches fire shortly after takeoff. As a result of her actions in the accident, <PERSON> is awarded a posthumous George Cross, the only GC awarded to a woman in peacetime.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/BOAC_Flight_712\" title=\"BOAC Flight 712\">BOAC Flight 712</a> catches fire shortly after takeoff. As a result of her actions in the accident, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded a posthumous <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the only GC awarded to a woman in peacetime.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BOAC_Flight_712\" title=\"BOAC Flight 712\">BOAC Flight 712</a> catches fire shortly after takeoff. As a result of her actions in the accident, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded a posthumous <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the only GC awarded to a woman in peacetime.", "links": [{"title": "BOAC Flight 712", "link": "https://wikipedia.org/wiki/BOAC_Flight_712"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "Bahr <PERSON> primary school bombing: Israeli bombers strike an Egyptian school. Forty-six children are killed.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Bahr_El-Baqar_primary_school_bombing\" title=\"Bahr El-Baqar primary school bombing\">Bahr El-Baqar primary school bombing</a>: Israeli bombers strike an Egyptian school. Forty-six children are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bahr_El-Baqar_primary_school_bombing\" title=\"Bahr El-Baqar primary school bombing\">Bahr El-Baqar primary school bombing</a>: Israeli bombers strike an Egyptian school. Forty-six children are killed.", "links": [{"title": "Bahr El<PERSON> primary school bombing", "link": "https://wikipedia.org/wiki/<PERSON>hr_El-Baqar_primary_school_bombing"}]}, {"year": "1974", "text": "<PERSON> passes <PERSON> as the all-time leader in career home runs by hitting his 715th home run off of <PERSON> at Atlanta-Fulton County Stadium.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> passes <a href=\"https://wikipedia.org/wiki/<PERSON>_Ruth\" title=\"<PERSON> Ruth\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/List_of_Major_League_Baseball_career_home_run_leaders\" title=\"List of Major League Baseball career home run leaders\">all-time leader in career home runs</a> by hitting his 715th home run off of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Atlanta-Fulton_County_Stadium\" class=\"mw-redirect\" title=\"Atlanta-Fulton County Stadium\">Atlanta-Fulton County Stadium</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> passes <a href=\"https://wikipedia.org/wiki/<PERSON>_Ruth\" title=\"<PERSON> Ruth\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/List_of_Major_League_Baseball_career_home_run_leaders\" title=\"List of Major League Baseball career home run leaders\">all-time leader in career home runs</a> by hitting his 715th home run off of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Atlanta-Fulton_County_Stadium\" class=\"mw-redirect\" title=\"Atlanta-Fulton County Stadium\">Atlanta-Fulton County Stadium</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Major League Baseball career home run leaders", "link": "https://wikipedia.org/wiki/List_of_Major_League_Baseball_career_home_run_leaders"}, {"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ing_(baseball)"}, {"title": "Atlanta-Fulton County Stadium", "link": "https://wikipedia.org/wiki/Atlanta-Fulton_County_Stadium"}]}, {"year": "1975", "text": "<PERSON> manages the Cleveland Indians in his first game as major league baseball's first African American manager.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> manages the <a href=\"https://wikipedia.org/wiki/Cleveland_Indians\" class=\"mw-redirect\" title=\"Cleveland Indians\">Cleveland Indians</a> in his first game as major league <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a>'s first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> manager.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> manages the <a href=\"https://wikipedia.org/wiki/Cleveland_Indians\" class=\"mw-redirect\" title=\"Cleveland Indians\">Cleveland Indians</a> in his first game as major league <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a>'s first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> manager.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cleveland Indians", "link": "https://wikipedia.org/wiki/Cleveland_Indians"}, {"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}]}, {"year": "1987", "text": "Los Angeles Dodgers executive <PERSON> resigns amid controversy over racist remarks he had made while on Nightline.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Los_Angeles_Dodgers\" title=\"Los Angeles Dodgers\">Los Angeles Dodgers</a> executive <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>s\" title=\"<PERSON>\"><PERSON></a> resigns amid controversy over <a href=\"https://wikipedia.org/wiki/Racism\" title=\"Racism\">racist</a> remarks he had made while on <i><a href=\"https://wikipedia.org/wiki/Nightline\" title=\"Nightline\">Nightline</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Los_Angeles_Dodgers\" title=\"Los Angeles Dodgers\">Los Angeles Dodgers</a> executive <a href=\"https://wikipedia.org/wiki/Al_<PERSON>anis\" title=\"<PERSON>\"><PERSON></a> resigns amid controversy over <a href=\"https://wikipedia.org/wiki/Racism\" title=\"Racism\">racist</a> remarks he had made while on <i><a href=\"https://wikipedia.org/wiki/Nightline\" title=\"Nightline\">Nightline</a></i>.", "links": [{"title": "Los Angeles Dodgers", "link": "https://wikipedia.org/wiki/Los_Angeles_Dodgers"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Campanis"}, {"title": "Racism", "link": "https://wikipedia.org/wiki/Racism"}, {"title": "Nightline", "link": "https://wikipedia.org/wiki/Nightline"}]}, {"year": "1992", "text": "Retired tennis great <PERSON> announces that he has AIDS, acquired from blood transfusions during one of his two heart surgeries.", "html": "1992 - Retired <a href=\"https://wikipedia.org/wiki/Tennis\" title=\"Tennis\">tennis</a> great <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that he has <a href=\"https://wikipedia.org/wiki/AIDS\" class=\"mw-redirect\" title=\"AIDS\">AIDS</a>, acquired from <a href=\"https://wikipedia.org/wiki/Blood_transfusion\" title=\"Blood transfusion\">blood transfusions</a> during one of his two heart surgeries.", "no_year_html": "Retired <a href=\"https://wikipedia.org/wiki/Tennis\" title=\"Tennis\">tennis</a> great <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that he has <a href=\"https://wikipedia.org/wiki/AIDS\" class=\"mw-redirect\" title=\"AIDS\">AIDS</a>, acquired from <a href=\"https://wikipedia.org/wiki/Blood_transfusion\" title=\"Blood transfusion\">blood transfusions</a> during one of his two heart surgeries.", "links": [{"title": "Tennis", "link": "https://wikipedia.org/wiki/Tennis"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "AIDS", "link": "https://wikipedia.org/wiki/AIDS"}, {"title": "Blood transfusion", "link": "https://wikipedia.org/wiki/Blood_transfusion"}]}, {"year": "1993", "text": "The Republic of North Macedonia joins the United Nations.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Republic_of_North_Macedonia\" class=\"mw-redirect\" title=\"Republic of North Macedonia\">Republic of North Macedonia</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_North_Macedonia\" class=\"mw-redirect\" title=\"Republic of North Macedonia\">Republic of North Macedonia</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Republic of North Macedonia", "link": "https://wikipedia.org/wiki/Republic_of_North_Macedonia"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1993", "text": "The Space Shuttle Discovery is launched on mission STS-56.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-56\" title=\"STS-56\">STS-56</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-56\" title=\"STS-56\">STS-56</a>.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-56", "link": "https://wikipedia.org/wiki/STS-56"}]}, {"year": "2002", "text": "The Space Shuttle Atlantis is launched on mission STS-110, carrying the S0 truss to the International Space Station. Astronaut <PERSON> also becomes the first person to fly on seven spaceflights.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-110\" title=\"STS-110\">STS-110</a>, carrying the <a href=\"https://wikipedia.org/wiki/Integrated_Truss_Structure\" title=\"Integrated Truss Structure\">S0 truss</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>. Astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> also becomes the first person to fly on seven spaceflights.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-110\" title=\"STS-110\">STS-110</a>, carrying the <a href=\"https://wikipedia.org/wiki/Integrated_Truss_Structure\" title=\"Integrated Truss Structure\">S0 truss</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>. Astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> also becomes the first person to fly on seven spaceflights.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-110", "link": "https://wikipedia.org/wiki/STS-110"}, {"title": "Integrated Truss Structure", "link": "https://wikipedia.org/wiki/Integrated_Truss_Structure"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "War in Darfur: The Humanitarian Ceasefire Agreement is signed by the Sudanese government, the Justice and Equality Movement, and the Sudan Liberation Movement/Army.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/War_in_Darfur\" title=\"War in Darfur\">War in Darfur</a>: The <a href=\"https://wikipedia.org/wiki/April_8_Humanitarian_Ceasefire_Agreement\" class=\"mw-redirect\" title=\"April 8 Humanitarian Ceasefire Agreement\">Humanitarian Ceasefire Agreement</a> is signed by the <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudanese</a> government, the <a href=\"https://wikipedia.org/wiki/Justice_and_Equality_Movement\" title=\"Justice and Equality Movement\">Justice and Equality Movement</a>, and the <a href=\"https://wikipedia.org/wiki/Sudan_Liberation_Movement/Army\" title=\"Sudan Liberation Movement/Army\">Sudan Liberation Movement/Army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Darfur\" title=\"War in Darfur\">War in Darfur</a>: The <a href=\"https://wikipedia.org/wiki/April_8_Humanitarian_Ceasefire_Agreement\" class=\"mw-redirect\" title=\"April 8 Humanitarian Ceasefire Agreement\">Humanitarian Ceasefire Agreement</a> is signed by the <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudanese</a> government, the <a href=\"https://wikipedia.org/wiki/Justice_and_Equality_Movement\" title=\"Justice and Equality Movement\">Justice and Equality Movement</a>, and the <a href=\"https://wikipedia.org/wiki/Sudan_Liberation_Movement/Army\" title=\"Sudan Liberation Movement/Army\">Sudan Liberation Movement/Army</a>.", "links": [{"title": "War in Darfur", "link": "https://wikipedia.org/wiki/War_in_Darfur"}, {"title": "April 8 Humanitarian Ceasefire Agreement", "link": "https://wikipedia.org/wiki/April_8_Humanitarian_Ceasefire_Agreement"}, {"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}, {"title": "Justice and Equality Movement", "link": "https://wikipedia.org/wiki/Justice_and_Equality_Movement"}, {"title": "Sudan Liberation Movement/Army", "link": "https://wikipedia.org/wiki/Sudan_Liberation_Movement/Army"}]}, {"year": "2005", "text": "A solar eclipse occurs, visible over areas of the Pacific Ocean and Latin American countries such as Costa Rica, Panama, Colombia and Venezuela.", "html": "2005 - A <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_April_8,_2005\" title=\"Solar eclipse of April 8, 2005\">solar eclipse</a> occurs, visible over areas of the <a href=\"https://wikipedia.org/wiki/Pacific_Ocean\" title=\"Pacific Ocean\">Pacific Ocean</a> and Latin American countries such as <a href=\"https://wikipedia.org/wiki/Costa_Rica\" title=\"Costa Rica\">Costa Rica</a>, <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a> and <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_April_8,_2005\" title=\"Solar eclipse of April 8, 2005\">solar eclipse</a> occurs, visible over areas of the <a href=\"https://wikipedia.org/wiki/Pacific_Ocean\" title=\"Pacific Ocean\">Pacific Ocean</a> and Latin American countries such as <a href=\"https://wikipedia.org/wiki/Costa_Rica\" title=\"Costa Rica\">Costa Rica</a>, <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a> and <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "links": [{"title": "Solar eclipse of April 8, 2005", "link": "https://wikipedia.org/wiki/Solar_eclipse_of_April_8,_2005"}, {"title": "Pacific Ocean", "link": "https://wikipedia.org/wiki/Pacific_Ocean"}, {"title": "Costa Rica", "link": "https://wikipedia.org/wiki/Costa_Rica"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "2006", "text": "Shedden massacre: The bodies of eight men, all shot to death, are found in a field in Shedden, Elgin County, Ontario. The murders are soon linked to the Bandidos Motorcycle Club.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Shedden_massacre\" title=\"Shedden massacre\">Shedden massacre</a>: The bodies of eight men, all shot to death, are found in a field in <a href=\"https://wikipedia.org/wiki/Shedden,_Elgin_County,_Ontario\" title=\"Shedden, Elgin County, Ontario\">Shedden, Elgin County, Ontario</a>. The murders are soon linked to the <a href=\"https://wikipedia.org/wiki/Bandidos_Motorcycle_Club\" title=\"Bandidos Motorcycle Club\">Bandidos Motorcycle Club</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shedden_massacre\" title=\"Shedden massacre\">Shedden massacre</a>: The bodies of eight men, all shot to death, are found in a field in <a href=\"https://wikipedia.org/wiki/Shedden,_Elgin_County,_Ontario\" title=\"Shedden, Elgin County, Ontario\">Shedden, Elgin County, Ontario</a>. The murders are soon linked to the <a href=\"https://wikipedia.org/wiki/Bandidos_Motorcycle_Club\" title=\"Bandidos Motorcycle Club\">Bandidos Motorcycle Club</a>.", "links": [{"title": "Shedden massacre", "link": "https://wikipedia.org/wiki/Shedden_massacre"}, {"title": "Shedden, Elgin County, Ontario", "link": "https://wikipedia.org/wiki/She<PERSON>,_Elgin_County,_Ontario"}, {"title": "Bandidos Motorcycle Club", "link": "https://wikipedia.org/wiki/Bandidos_Motorcycle_Club"}]}, {"year": "2008", "text": "The construction of the world's first skyscraper to integrate wind turbines is completed in Bahrain.", "html": "2008 - The construction of the <a href=\"https://wikipedia.org/wiki/Bahrain_World_Trade_Center\" title=\"Bahrain World Trade Center\">world's first skyscraper to integrate wind turbines</a> is completed in <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a>.", "no_year_html": "The construction of the <a href=\"https://wikipedia.org/wiki/Bahrain_World_Trade_Center\" title=\"Bahrain World Trade Center\">world's first skyscraper to integrate wind turbines</a> is completed in <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a>.", "links": [{"title": "Bahrain World Trade Center", "link": "https://wikipedia.org/wiki/Bahrain_World_Trade_Center"}, {"title": "Bahrain", "link": "https://wikipedia.org/wiki/Bahrain"}]}, {"year": "2010", "text": "U.S. President <PERSON> and Russian President <PERSON> sign the New START Treaty.", "html": "2010 - U.S. President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> and Russian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/New_START\" title=\"New START\">New START Treaty</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> and Russian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/New_START\" title=\"New START\">New START Treaty</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New START", "link": "https://wikipedia.org/wiki/New_START"}]}, {"year": "2013", "text": "The Islamic State of Iraq enters the Syrian Civil War and begins by declaring a merger with the Al-Nusra Front under the name Islamic State of Iraq and ash-Sham.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq\" title=\"Islamic State of Iraq\">Islamic State of Iraq</a> enters the <a href=\"https://wikipedia.org/wiki/Syrian_Civil_War\" class=\"mw-redirect\" title=\"Syrian Civil War\">Syrian Civil War</a> and begins by declaring a merger with the <a href=\"https://wikipedia.org/wiki/Al-Nusra_Front\" title=\"Al-Nusra Front\">Al-Nusra Front</a> under the name <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and ash-Sham</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq\" title=\"Islamic State of Iraq\">Islamic State of Iraq</a> enters the <a href=\"https://wikipedia.org/wiki/Syrian_Civil_War\" class=\"mw-redirect\" title=\"Syrian Civil War\">Syrian Civil War</a> and begins by declaring a merger with the <a href=\"https://wikipedia.org/wiki/Al-Nusra_Front\" title=\"Al-Nusra Front\">Al-Nusra Front</a> under the name <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and ash-Sham</a>.", "links": [{"title": "Islamic State of Iraq", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq"}, {"title": "Syrian Civil War", "link": "https://wikipedia.org/wiki/Syrian_Civil_War"}, {"title": "Al-Nusra Front", "link": "https://wikipedia.org/wiki/Al-Nusra_Front"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}]}, {"year": "2014", "text": "Windows XP reaches its standard End Of Life and is no longer supported.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Windows_XP\" title=\"Windows XP\">Windows XP</a> reaches its standard <a href=\"https://wikipedia.org/wiki/End-of-life_product\" title=\"End-of-life product\">End Of Life</a> and is no longer supported.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Windows_XP\" title=\"Windows XP\">Windows XP</a> reaches its standard <a href=\"https://wikipedia.org/wiki/End-of-life_product\" title=\"End-of-life product\">End Of Life</a> and is no longer supported.", "links": [{"title": "Windows XP", "link": "https://wikipedia.org/wiki/Windows_XP"}, {"title": "End-of-life product", "link": "https://wikipedia.org/wiki/End-of-life_product"}]}, {"year": "2020", "text": "<PERSON> ends his presidential campaign, leaving <PERSON> as the Democratic Party's nominee.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> ends his <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_2020_presidential_campaign\" title=\"<PERSON> 2020 presidential campaign\">presidential campaign</a>, leaving <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/Democratic_Party_(United_States)\" title=\"Democratic Party (United States)\">Democratic Party</a>'s nominee.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> ends his <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_2020_presidential_campaign\" title=\"<PERSON> 2020 presidential campaign\">presidential campaign</a>, leaving <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/Democratic_Party_(United_States)\" title=\"Democratic Party (United States)\">Democratic Party</a>'s nominee.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> 2020 presidential campaign", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_2020_presidential_campaign"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Democratic Party (United States)", "link": "https://wikipedia.org/wiki/Democratic_Party_(United_States)"}]}, {"year": "2024", "text": "Solar eclipse of April 8, 2024: A total solar eclipse takes place at the Moon's ascending node, visible across North America.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_April_8,_2024\" title=\"Solar eclipse of April 8, 2024\">Solar eclipse of April 8, 2024</a>: A total <a href=\"https://wikipedia.org/wiki/Solar_eclipse\" title=\"Solar eclipse\">solar eclipse</a> takes place at the Moon's <a href=\"https://wikipedia.org/wiki/Orbital_node\" title=\"Orbital node\">ascending node</a>, visible across North America.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_April_8,_2024\" title=\"Solar eclipse of April 8, 2024\">Solar eclipse of April 8, 2024</a>: A total <a href=\"https://wikipedia.org/wiki/Solar_eclipse\" title=\"Solar eclipse\">solar eclipse</a> takes place at the Moon's <a href=\"https://wikipedia.org/wiki/Orbital_node\" title=\"Orbital node\">ascending node</a>, visible across North America.", "links": [{"title": "Solar eclipse of April 8, 2024", "link": "https://wikipedia.org/wiki/Solar_eclipse_of_April_8,_2024"}, {"title": "Solar eclipse", "link": "https://wikipedia.org/wiki/Solar_eclipse"}, {"title": "Orbital node", "link": "https://wikipedia.org/wiki/Orbital_node"}]}], "Births": [{"year": "1320", "text": "<PERSON> of Portugal (d. 1367)", "html": "1320 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1367)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1367)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Portugal"}]}, {"year": "1408", "text": "<PERSON><PERSON><PERSON><PERSON> of Lithuania, Polish princess (d. 1431)", "html": "1408 - <a href=\"https://wikipedia.org/wiki/Jadwiga_of_Lithuania\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Lithuania\"><PERSON><PERSON><PERSON><PERSON> of Lithuania</a>, Polish princess (d. 1431)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ad<PERSON><PERSON>_of_Lithuania\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Lithuania\"><PERSON><PERSON><PERSON><PERSON> of Lithuania</a>, Polish princess (d. 1431)", "links": [{"title": "Jadwiga of Lithuania", "link": "https://wikipedia.org/wiki/Jadwiga_of_Lithuania"}]}, {"year": "1435", "text": "<PERSON>, 9th Baron <PERSON>, English noble (d. 1461)", "html": "1435 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 9th Baron <PERSON>\"><PERSON>, 9th Baron <PERSON></a>, English noble (d. 1461)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 9th Baron <PERSON>\"><PERSON>, 9th Baron <PERSON></a>, English noble (d. 1461)", "links": [{"title": "<PERSON>, 9th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_9th_Baron_<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON>, Italian organist and composer (d. 1604)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1604)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1536", "text": "<PERSON> of Hesse (d. 1597)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/Barbara_of_Hesse\" title=\"<PERSON> of Hesse\"><PERSON> of Hesse</a> (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbara_of_Hesse\" title=\"<PERSON> of Hesse\"><PERSON> of Hesse</a> (d. 1597)", "links": [{"title": "<PERSON> of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hesse"}]}, {"year": "1541", "text": "<PERSON>, Italian physician and archaeologist (d. 1593)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and archaeologist (d. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and archaeologist (d. 1593)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON>, 3rd Earl of Pembroke, English noble, courtier and patron of the arts (d. 1630)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Pembroke\" title=\"<PERSON>, 3rd Earl of Pembroke\"><PERSON>, 3rd Earl of Pembroke</a>, English noble, courtier and patron of the arts (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Pembroke\" title=\"<PERSON>, 3rd Earl of Pembroke\"><PERSON>, 3rd Earl of Pembroke</a>, English noble, courtier and patron of the arts (d. 1630)", "links": [{"title": "<PERSON>, 3rd Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Pembroke"}]}, {"year": "1596", "text": "<PERSON>, Spanish artist (d. 1631)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish artist (d. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish artist (d. 1631)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON> of Spain (d. 1665)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> IV of Spain\"><PERSON> of Spain</a> (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> IV of Spain\"><PERSON> of Spain</a> (d. 1665)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1605", "text": "<PERSON>, English-Scottish princess (d. 1607)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1605%E2%80%931607)\" title=\"<PERSON> (1605-1607)\"><PERSON></a>, English-Scottish princess (d. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1605%E2%80%931607)\" title=\"<PERSON> (1605-1607)\"><PERSON></a>, English-Scottish princess (d. 1607)", "links": [{"title": "<PERSON> (1605-1607)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1605%E2%80%931607)"}]}, {"year": "1641", "text": "<PERSON>, 1st Earl of Romney, English general and politician, Secretary of State for the Northern Department (d. 1704)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl of Romney</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl of Romney</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (d. 1704)", "links": [{"title": "<PERSON>, 1st Earl of Romney", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_Romney"}, {"title": "Secretary of State for the Northern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department"}]}, {"year": "1692", "text": "<PERSON>, Italian violinist and composer (d. 1770)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON>, American judge and politician (d. 1798)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (d. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1732", "text": "<PERSON>, American astronomer and mathematician (d. 1796)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and mathematician (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and mathematician (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON>, French priest, founded the Society of Mary (d. 1850)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Society_of_Mary_(Marianists)\" title=\"Society of Mary (Marianists)\">Society of Mary</a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Society_of_Mary_(Marianists)\" title=\"Society of Mary (Marianists)\">Society of Mary</a> (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Society of Mary (Marianists)", "link": "https://wikipedia.org/wiki/Society_of_<PERSON>_(Marianists)"}]}, {"year": "1770", "text": "<PERSON>, Irish-Australian banker and politician (d. 1830)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian banker and politician (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian banker and politician (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON>, Greek poet and author (d. 1857)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and author (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and author (d. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dionysios_Solomos"}]}, {"year": "1818", "text": "<PERSON> Denmark (d. 1906)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/Christian_IX_of_Denmark\" title=\"Christian IX of Denmark\">Christian IX of Denmark</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_IX_of_Denmark\" title=\"Christian IX of Denmark\">Christian IX of Denmark</a> (d. 1906)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_IX_of_Denmark"}]}, {"year": "1818", "text": "<PERSON> <PERSON>, German chemist and academic (d. 1892)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German chemist and academic (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German chemist and academic (d. 1892)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON><PERSON>, Costa Rican soldier (d. 1890)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican soldier (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican soldier (d. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>asco"}]}, {"year": "1827", "text": "<PERSON>, Puerto Rican ophthalmologist, journalist, and politician (d. 1898)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Emeterio_Betances\" title=\"Ramón Emeterio Betances\"><PERSON></a>, Puerto Rican ophthalmologist, journalist, and politician (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Emeterio_Betances\" title=\"Ramón Emeterio Betances\"><PERSON></a>, Puerto Rican ophthalmologist, journalist, and politician (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Emeterio_Betances"}]}, {"year": "1842", "text": "<PERSON>, American author and educator (d. 1933)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, German Jewish-Austrian mathematician and philosopher (d. 1938)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Jewish-Austrian mathematician and philosopher (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Jewish-Austrian mathematician and philosopher (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, French rower and rugby player (d. 1920)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rower and rugby player (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rower and rugby player (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American painter and educator (d. 1908)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American surgeon and academic (d. 1939)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, American photographer and educator (d. 1925)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Cuban fencer (d. 1929)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_D%C3%AD<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, Cuban fencer (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, Cuban fencer (d. 1929)", "links": [{"title": "<PERSON> (fencer)", "link": "https://wikipedia.org/wiki/Manuel_D%C3%ADaz_(fencer)"}]}, {"year": "1874", "text": "<PERSON><PERSON>, Polish general (d. 1960)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON>ak\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish general (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish general (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON>ak"}]}, {"year": "1875", "text": "<PERSON> of Belgium (d. 1934)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"<PERSON> of Belgium\"><PERSON> of Belgium</a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"<PERSON> of Belgium\"><PERSON> of Belgium</a> (d. 1934)", "links": [{"title": "<PERSON> of Belgium", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Belgium"}]}, {"year": "1882 (O.S. 27 March)", "text": "<PERSON><PERSON><PERSON>, Lithuanian-Ukrainian historian and politician, Minister of Foreign Affairs of Ukraine and Prime Minister of Ukraine (d. 1951)", "html": "1882 (O.S. 27 March) - <a href=\"https://wikipedia.org/wiki/1882\" title=\"1882\">1882</a> (O.S. 27 March) - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-Ukrainian historian and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Ukraine\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Ukraine\">Minister of Foreign Affairs of Ukraine</a> and Prime Minister of Ukraine (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1882\" title=\"1882\">1882</a> (O.S. 27 March) - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-Ukrainian historian and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Ukraine\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Ukraine\">Minister of Foreign Affairs of Ukraine</a> and Prime Minister of Ukraine (d. 1951)", "links": [{"title": "1882", "link": "https://wikipedia.org/wiki/1882"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs of Ukraine", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Ukraine"}]}, {"year": "1883", "text": "<PERSON><PERSON> <PERSON><PERSON>, English cricketer and academic (d. 1972)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and academic (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and academic (d. 1972)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>win"}]}, {"year": "1883", "text": "<PERSON>, Estonian journalist and politician, Minister of Foreign Affairs of Estonia (d. 1936)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Estonia\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Estonia\">Minister of Foreign Affairs of Estonia</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Estonia\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Estonia\">Minister of Foreign Affairs of Estonia</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs of Estonia", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Estonia"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Greek-French soldier, composer, and educator (d. 1951)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-French soldier, composer, and educator (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-French soldier, composer, and educator (d. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American author and playwright (d. 1967)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American journalist and politician (d. 1962)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1vez\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dennis_Ch%C3%A1vez"}]}, {"year": "1889", "text": "<PERSON>, English conductor (d. 1983)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Austrian-American architect, designer of the Los Angeles County Hall of Records (d. 1970)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American architect, designer of the <a href=\"https://wikipedia.org/wiki/Los_Angeles_County_Hall_of_Records\" title=\"Los Angeles County Hall of Records\">Los Angeles County Hall of Records</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American architect, designer of the <a href=\"https://wikipedia.org/wiki/Los_Angeles_County_Hall_of_Records\" title=\"Los Angeles County Hall of Records\">Los Angeles County Hall of Records</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Los Angeles County Hall of Records", "link": "https://wikipedia.org/wiki/Los_Angeles_County_Hall_of_Records"}]}, {"year": "1892", "text": "<PERSON>, Canadian-American actress, producer, screenwriter and co-founder of United Artists (d. 1979)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress, producer, screenwriter and co-founder of <a href=\"https://wikipedia.org/wiki/United_Artists\" title=\"United Artists\">United Artists</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress, producer, screenwriter and co-founder of <a href=\"https://wikipedia.org/wiki/United_Artists\" title=\"United Artists\">United Artists</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Artists", "link": "https://wikipedia.org/wiki/United_Artists"}]}, {"year": "1896", "text": "<PERSON><PERSON>, American composer (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Yi<PERSON>_<PERSON>urg\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Harburg"}]}, {"year": "1900", "text": "<PERSON>, Australian solicitor (d. 1979)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian solicitor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian solicitor (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English mountaineer and explorer (d. 1924)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, English mountaineer and explorer (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, English mountaineer and explorer (d. 1924)", "links": [{"title": "<PERSON> (mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)"}]}, {"year": "1902", "text": "<PERSON>, Russian soprano (d. 1974)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soprano (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soprano (d. 1974)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1904", "text": "<PERSON>, English economist and academic, Nobel Prize laureate (d. 1989)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American horse trainer (d. 1970)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American horse trainer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American horse trainer (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, German sprinter and graphic designer (d. 1978)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter and graphic designer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter and graphic designer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joachim_B%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English-South African activist (d. 1992)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African activist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African activist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, German field hockey player (d. 1971)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field hockey player (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field hockey player (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Canadian tenor and educator (d. 1974)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor and educator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor and educator (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Argentinian director and screenwriter (d. 1987)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and screenwriter (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American author and screenwriter (d. 1983)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American football player and police officer (d. 2000)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and police officer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and police officer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 1997)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1911", "text": "<PERSON>, Romanian-French philosopher and academic (d. 1995)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French philosopher and academic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French philosopher and academic (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Austrian-German SS officer (d. 2001 or 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Alois_Brunner\" title=\"Alois Brunner\">Alois Brunner</a>, Austrian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 2001 or 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alois_Brunner\" title=\"Alois Brunner\">Alois Brunner</a>, Austrian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 2001 or 2010)", "links": [{"title": "Alois Brunner", "link": "https://wikipedia.org/wiki/Alois_Brunner"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Norwegian-American figure skater and actress (d. 1969)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American figure skater and actress (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American figure skater and actress (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Yaqui/Basque-Mexican actress (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>/<a href=\"https://wikipedia.org/wiki/Basques\" title=\"Basques\">Basque</a>-Mexican actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>/<a href=\"https://wikipedia.org/wiki/Basques\" title=\"Basques\">Basque</a>-Mexican actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_F%C3%A9lix"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON>"}, {"title": "Basques", "link": "https://wikipedia.org/wiki/Basques"}]}, {"year": "1915", "text": "<PERSON>, Croatian physicist, philosopher and writer (d. 2007)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian physicist, philosopher and writer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian physicist, philosopher and writer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, American mathematician and computer scientist (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mathematician and computer scientist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mathematician and computer scientist (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Australian public servant (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Dutch bishop (d. 2017)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch bishop (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch bishop (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Russian-Estonian astronomer (d. 1988)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Estonian astronomer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Estonian astronomer (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American wife of <PERSON>, 40th First Lady of the United States (d. 2011)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 40th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 40th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American author and academic (d. 1992)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Glendon_Swarthout\" title=\"Glendon Swarthout\"><PERSON><PERSON></a>, American author and academic (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glendon_Swarthout\" title=\"Glendon Swarthout\"><PERSON><PERSON></a>, American author and academic (d. 1992)", "links": [{"title": "<PERSON>don Swarthout", "link": "https://wikipedia.org/wiki/Glendon_Swarthout"}]}, {"year": "1919", "text": "<PERSON>, Zimbabwean lieutenant and politician, 1st Prime Minister of Rhodesia (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean lieutenant and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Rhodesia\" title=\"Prime Minister of Rhodesia\">Prime Minister of Rhodesia</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean lieutenant and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Rhodesia\" title=\"Prime Minister of Rhodesia\">Prime Minister of Rhodesia</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Rhodesia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Rhodesia"}]}, {"year": "1921", "text": "<PERSON>, Italian tenor and actor (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American singer-songwriter, pianist, and actress (d. 1994)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actress (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Czech composer (d. 1984)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/1921\" title=\"1921\">1921</a> - <a href=\"https://wikipedia.org/wiki/Jan_Nov%C3%<PERSON><PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Czech composer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1921\" title=\"1921\">1921</a> - <a href=\"https://wikipedia.org/wiki/Jan_Nov%C3%<PERSON><PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Czech composer (d. 1984)", "links": [{"title": "1921", "link": "https://wikipedia.org/wiki/1921"}, {"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/Jan_Nov%C3%<PERSON><PERSON>_(composer)"}]}, {"year": "1921", "text": "<PERSON>, Dutch footballer (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American cartoonist (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (d. 2003)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)"}]}, {"year": "1923", "text": "<PERSON>, Irish-American actor (d. 1997)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German-Canadian animator, director, and screenwriter (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Back\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Back\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German-Canadian animator, director, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Back\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Back\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German-Canadian animator, director, and screenwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Back"}]}, {"year": "1924", "text": "<PERSON>, English general and historian (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and historian (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and historian (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Hindustani classical singer (d. 1992)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hindustani classical singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hindustani classical singer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American occultist (d. 1997)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Hollister\"><PERSON></a>, American occultist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Hollister\"><PERSON></a>, American occultist (d. 1997)", "links": [{"title": "<PERSON>llister", "link": "https://wikipedia.org/wiki/<PERSON>_Northrup_Hollister"}]}, {"year": "1926", "text": "<PERSON>, American architect and academic, co-founded Pei Cobb Freed & Partners (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Freed_%26_Partners\" title=\"Pei Cobb Freed &amp; Partners\">Pei Cobb Freed &amp; Partners</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Freed_%26_Partners\" title=\"Pei Cobb Freed &amp; Partners\">Pei Cobb Freed &amp; Partners</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Pei Cobb Freed & Partners", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Freed_%26_Partners"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American comedian (d. 2023)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, German theologian and academic (d. 2024)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian and academic (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, English author (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American trumpet player and bandleader (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player and bandleader (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player and bandleader (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American lyricist (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lyricist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lyricist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Belgian singer-songwriter and actor (d. 1978)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter and actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter and actor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Italian historian and author (d. 1996)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian historian and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian historian and author (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Duke of Parma (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (d. 2010)", "links": [{"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Parma"}]}, {"year": "1931", "text": "<PERSON>, American actor and diplomat, United States Ambassador to Mexico (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Mexico\" class=\"mw-redirect\" title=\"United States Ambassador to Mexico\">United States Ambassador to Mexico</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Mexico\" class=\"mw-redirect\" title=\"United States Ambassador to Mexico\">United States Ambassador to Mexico</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Mexico", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Mexico"}]}, {"year": "1931", "text": "<PERSON>, French equestrian (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French equestrian (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French equestrian (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON> of Johor (d. 2010)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Is<PERSON><PERSON>_of_Johor\" title=\"Is<PERSON><PERSON> of Johor\"><PERSON><PERSON><PERSON> of Johor</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Johor\" title=\"Is<PERSON><PERSON> of Johor\"><PERSON><PERSON><PERSON> of Johor</a> (d. 2010)", "links": [{"title": "Iskandar of Johor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Johor"}]}, {"year": "1933", "text": "<PERSON>, American scholar of colonial Latin America, especially Nahua peoples (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, American scholar of colonial Latin America, especially <a href=\"https://wikipedia.org/wiki/Nahua_people\" class=\"mw-redirect\" title=\"Nahua people\">Nahua peoples</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, American scholar of colonial Latin America, especially <a href=\"https://wikipedia.org/wiki/Nahua_people\" class=\"mw-redirect\" title=\"Nahua people\">Nahua peoples</a> (d. 2014)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_(historian)"}, {"title": "Nahua people", "link": "https://wikipedia.org/wiki/Nahua_people"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Japanese architect, designed the Nakagin Capsule Tower and Singapore Flyer (d. 2007)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese architect, designed the <a href=\"https://wikipedia.org/wiki/Nakagin_Capsule_Tower\" title=\"Nakagin Capsule Tower\">Nakagin Capsule Tower</a> and <a href=\"https://wikipedia.org/wiki/Singapore_Flyer\" title=\"Singapore Flyer\">Singapore Flyer</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese architect, designed the <a href=\"https://wikipedia.org/wiki/Nakagin_Capsule_Tower\" title=\"Nakagin Capsule Tower\">Nakagin Capsule Tower</a> and <a href=\"https://wikipedia.org/wiki/Singapore_Flyer\" title=\"Singapore Flyer\">Singapore Flyer</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>awa"}, {"title": "Nakagin Capsule Tower", "link": "https://wikipedia.org/wiki/Nakagin_Capsule_Tower"}, {"title": "Singapore Flyer", "link": "https://wikipedia.org/wiki/Singapore_Flyer"}]}, {"year": "1935", "text": "<PERSON>, American lawyer and politician (d. 1974)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Oscar <PERSON>\"><PERSON></a>, American lawyer and politician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Oscar <PERSON>\"><PERSON></a>, American lawyer and politician (d. 1974)", "links": [{"title": "Oscar <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American soldier, educator, and politician (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, educator, and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, educator, and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English footballer and manager (d. 1993)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 1993)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1937", "text": "<PERSON>, American journalist and author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Serbian author and painter (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian author and painter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian author and painter (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Ghanaian economist and diplomat, 7th Secretary-General of the United Nations (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian economist and diplomat, 7th <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian economist and diplomat, 7th <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Annan"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "1938", "text": "<PERSON>, Canadian physician and politician, 25th Premier of Nova Scotia", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Nova Scotia", "link": "https://wikipedia.org/wiki/Premier_of_Nova_Scotia"}]}, {"year": "1938", "text": "<PERSON>, American mathematician, statistician, and lawyer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, statistician, and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, statistician, and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Greek singer, composer and songwriter (d. 1989)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer, composer and songwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer, composer and songwriter (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Scottish microbiologist and academic (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)\" title=\"<PERSON> (microbiologist)\"><PERSON></a>, Scottish microbiologist and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(microbiologist)\" title=\"<PERSON> (microbiologist)\"><PERSON></a>, Scottish microbiologist and academic (d. 2023)", "links": [{"title": "<PERSON> (microbiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American author and illustrator (d. 2004)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American politician, 39th Governor of Wisconsin", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Wisconsin", "link": "https://wikipedia.org/wiki/Governor_of_Wisconsin"}]}, {"year": "1940", "text": "<PERSON>, American basketball player (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, English fashion designer (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English fashion designer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English fashion designer (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, <PERSON>, Northern Irish politician, Minister for Sport and the Olympics (d. 2006)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Northern Irish politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Northern Irish politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a> (d. 2006)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Minister for Sport and the Olympics", "link": "https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics"}]}, {"year": "1942", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American director, producer, and special effects artist (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Trumbull\"><PERSON></a>, American director, producer, and special effects artist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Trumbull\"><PERSON></a>, American director, producer, and special effects artist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American dancer, choreographer, and director (d. 1987)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theater)\" title=\"<PERSON> (theater)\"><PERSON></a>, American dancer, choreographer, and director (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(theater)\" title=\"<PERSON> (theater)\"><PERSON></a>, American dancer, choreographer, and director (d. 1987)", "links": [{"title": "<PERSON> (theater)", "link": "https://wikipedia.org/wiki/<PERSON>_(theater)"}]}, {"year": "1943", "text": "<PERSON>, American football player (d. 2023)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English author and illustrator (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English painter and illustrator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and illustrator", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Welsh actor (d. 2017)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Welsh actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Welsh actor (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Swedish-Norwegian painter and illustrator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Odd_Nerdrum\" title=\"Odd Nerdrum\"><PERSON>erd<PERSON></a>, Swedish-Norwegian painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odd_Nerdrum\" title=\"Odd Nerdrum\"><PERSON></a>, Swedish-Norwegian painter and illustrator", "links": [{"title": "Odd Nerdrum", "link": "https://wikipedia.org/wiki/Odd_Nerdrum"}]}, {"year": "1945", "text": "<PERSON>, Scottish businessman", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, South Korean actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American baseball player (d. 1999)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hunter\" title=\"Cat<PERSON> Hunter\"><PERSON><PERSON></a>, American baseball player (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hunter\" title=\"Cat<PERSON> Hunter\"><PERSON><PERSON></a>, American baseball player (d. 1999)", "links": [{"title": "Catfish Hunter", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hunter"}]}, {"year": "1946", "text": "<PERSON>, American actor and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English guitarist, songwriter, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist, songwriter, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1947", "text": "<PERSON>, French businessman and politician, European Commissioner for Trade", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and politician, <a href=\"https://wikipedia.org/wiki/European_Commissioner_for_Trade\" title=\"European Commissioner for Trade\">European Commissioner for Trade</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and politician, <a href=\"https://wikipedia.org/wiki/European_Commissioner_for_Trade\" title=\"European Commissioner for Trade\">European Commissioner for Trade</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "European Commissioner for Trade", "link": "https://wikipedia.org/wiki/European_Commissioner_for_Trade"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter, and producer (d. 2008)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, <PERSON> of Old Scone, Scottish academic and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Old_Scone\" title=\"<PERSON>, Baroness <PERSON> of Old Scone\"><PERSON>, Baroness <PERSON> of Old Scone</a>, Scottish academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Old_Scone\" title=\"<PERSON>, Baroness <PERSON> of Old Scone\"><PERSON>, Baroness <PERSON> of Old Scone</a>, Scottish academic and politician", "links": [{"title": "<PERSON>, <PERSON> of Old Scone", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Old_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician, 39th Attorney General of Sri Lanka (d. 2007)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 39th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Sri_Lanka\" title=\"Attorney General of Sri Lanka\">Attorney General of Sri Lanka</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 39th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Sri_Lanka\" title=\"Attorney General of Sri Lanka\">Attorney General of Sri Lanka</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Attorney General of Sri Lanka", "link": "https://wikipedia.org/wiki/Attorney_General_of_Sri_Lanka"}]}, {"year": "1949", "text": "<PERSON>, English director and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1949", "text": "<PERSON>, African-American-Canadian singer-songwriter and keyboard player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American-Canadian singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American-Canadian singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English sociologist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sociologist)\" title=\"<PERSON> (sociologist)\"><PERSON></a>, English sociologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(sociologist)\" title=\"<PERSON> (sociologist)\"><PERSON></a>, English sociologist and academic", "links": [{"title": "<PERSON> (sociologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(sociologist)"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, German politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Icelandic economist, journalist, and politician, 23rd Prime Minister of Iceland", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic economist, journalist, and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic economist, journalist, and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1951", "text": "<PERSON>, American bass player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Mexican singer-songwriter and actor (d. 2015)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter and actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American jazz disc jockey and historian (d. 2021)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz disc jockey and historian (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz disc jockey and historian (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Turkish politician (d. 2004)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Ahmet_Piri%C5%9Ftina\" title=\"<PERSON><PERSON> Piriştina\"><PERSON><PERSON></a>, Turkish politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahmet_Piri%C5%9Ftina\" title=\"<PERSON><PERSON> Piriştina\"><PERSON><PERSON></a>, Turkish politician (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ahmet_Piri%C5%9Ftina"}]}, {"year": "1954", "text": "<PERSON>, American baseball player and coach (d. 2012)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "Princess <PERSON><PERSON> of Morocco (d. 2012)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Morocco\" title=\"Princess <PERSON><PERSON> of Morocco\">Princess <PERSON><PERSON> of Morocco</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Morocco\" title=\"Princess <PERSON><PERSON> of Morocco\">Princess <PERSON><PERSON> of Morocco</a> (d. 2012)", "links": [{"title": "Princess <PERSON><PERSON> of Morocco", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON><PERSON>_of_Morocco"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian-American engineer and academic (d. 2007)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-American engineer and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-American engineer and academic (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, South African boxer (d. 2023)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Co<PERSON>zee\"><PERSON><PERSON><PERSON></a>, South African boxer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African boxer (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American businessman and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American novelist, essayist and poet", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kingsolver"}]}, {"year": "1955", "text": "<PERSON>, Taiwanese-American lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish-English paleontologist and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English paleontologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English paleontologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, French actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Czech singer-songwriter and keyboard player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Roman_Dragoun\" title=\"Roman Dragoun\"><PERSON></a>, Czech singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Dragoun\" title=\"Roman Dragoun\"><PERSON></a>, Czech singer-songwriter and keyboard player", "links": [{"title": "Roman Dragoun", "link": "https://wikipedia.org/wiki/Roman_Dragoun"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American javelin thrower and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American javelin thrower and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American javelin thrower and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French cyclist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor and country singer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screen_actor)\" title=\"<PERSON> (screen actor)\"><PERSON></a>, American actor and country singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(screen_actor)\" title=\"<PERSON> (screen actor)\"><PERSON></a>, American actor and country singer", "links": [{"title": "<PERSON> (screen actor)", "link": "https://wikipedia.org/wiki/<PERSON>(screen_actor)"}]}, {"year": "1961", "text": "<PERSON>, American reality contestant", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Survivor_contestant)\" title=\"<PERSON> (Survivor contestant)\"><PERSON></a>, American reality contestant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Survivor_contestant)\" title=\"<PERSON> (Survivor contestant)\"><PERSON></a>, American reality contestant", "links": [{"title": "<PERSON> (Survivor contestant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Survivor_contestant)"}]}, {"year": "1961", "text": "<PERSON>, English footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1962", "text": "<PERSON>, English engineer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American guitarist and songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Norwegian bassist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian bassist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian bassist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American rapper, producer, and actor (d. 2021)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American rapper, producer, and actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American rapper, producer, and actor (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON>_<PERSON>ie"}]}, {"year": "1964", "text": "<PERSON>, Scottish footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian businessman and politician, 5th Canadian Minister of Public Safety", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 5th <a href=\"https://wikipedia.org/wiki/Minister_of_Public_Safety\" title=\"Minister of Public Safety\">Canadian Minister of Public Safety</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 5th <a href=\"https://wikipedia.org/wiki/Minister_of_Public_Safety\" title=\"Minister of Public Safety\">Canadian Minister of Public Safety</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Public Safety", "link": "https://wikipedia.org/wiki/Minister_of_Public_Safety"}]}, {"year": "1965", "text": "<PERSON>, New Zealand rugby player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player and coach", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Czech singer and actress (d. 2014)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ivet<PERSON>_<PERSON>%C5%A1ov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech singer and actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ivet<PERSON>_<PERSON>%C5%A1ov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech singer and actress (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iveta_Barto%C5%A1ov%C3%A1"}]}, {"year": "1966", "text": "<PERSON>, English race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English rugby league player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, New Zealand-Australian television host (d. 2014)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian television host (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian television host (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English high jumper", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Grant\"><PERSON></a>, English high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer, coach, and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Finnish race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Harri_<PERSON>ovanper%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ha<PERSON>_<PERSON>ovanper%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Harri_Rovanper%C3%A4"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Greek lawyer and politician, Greek Minister for the Interior", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_and_Administrative_Reconstruction\" class=\"mw-redirect\" title=\"Ministry of the Interior and Administrative Reconstruction\">Greek Minister for the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_and_Administrative_Reconstruction\" class=\"mw-redirect\" title=\"Ministry of the Interior and Administrative Reconstruction\">Greek Minister for the Interior</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evripid<PERSON>_<PERSON><PERSON>idis"}, {"title": "Ministry of the Interior and Administrative Reconstruction", "link": "https://wikipedia.org/wiki/Ministry_of_the_Interior_and_Administrative_Reconstruction"}]}, {"year": "1966", "text": "<PERSON>, American actress, director, producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Antiguan cricketer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress and director", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, French runner and hurdler", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French runner and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French runner and hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and drummer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American bass player and songwriter (d. 2010)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American bass player and songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American bass player and songwriter (d. 2010)", "links": [{"title": "<PERSON> (American musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(American_musician)"}]}, {"year": "1972", "text": "<PERSON>, Russian lawyer and accountant (d. 2009)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer and accountant (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer and accountant (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Tunisian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tunisian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tunisian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Tongan-Australian rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Toutai_Kefu\" title=\"Toutai Kefu\"><PERSON><PERSON><PERSON></a>, Tongan-Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toutai_Kefu\" title=\"Toutai Kefu\"><PERSON><PERSON><PERSON></a>, Tongan-Australian rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toutai_Kefu"}]}, {"year": "1974", "text": "<PERSON>, American sniper and memoirist (d. 2013)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sniper and memoirist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sniper and memoirist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Nigerian-American author and educator", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Nned<PERSON>_Okorafor\" title=\"<PERSON><PERSON><PERSON> Okorafor\"><PERSON><PERSON><PERSON></a>, Nigerian-American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nned<PERSON>_Okorafor\" title=\"<PERSON><PERSON><PERSON> Okorafor\"><PERSON><PERSON><PERSON></a>, Nigerian-American author and educator", "links": [{"title": "<PERSON>ned<PERSON>", "link": "https://wikipedia.org/wiki/Nnedi_Okorafor"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Bulgarian conductor and culture minister", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian conductor and culture minister", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian conductor and culture minister", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Dutch singer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Dutch singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Dutch singer", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1975", "text": "<PERSON>, Italian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> F<PERSON>chi\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> F<PERSON>chi\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Dominican-American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Timo_P%C3%A9rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Timo_P%C3%A9rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Timo_P%C3%A9rez"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Turkish singer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Funda_Arar\" title=\"Funda Arar\"><PERSON><PERSON></a>, Turkish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Funda_Arar\" title=\"Funda Arar\"><PERSON><PERSON></a>, Turkish singer", "links": [{"title": "Funda Arar", "link": "https://wikipedia.org/wiki/Funda_Arar"}]}, {"year": "1977", "text": "<PERSON>, Mexican actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>uer<PERSON>\" title=\"<PERSON> de la Reguera\"><PERSON></a>, Mexican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>uer<PERSON>\" title=\"<PERSON> de la Reguera\"><PERSON></a>, Mexican actress", "links": [{"title": "<PERSON> de la Reguera", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Iranian journalist and author (d. 2008)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist and author (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American computer programmer and engineer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(computer_engineer)\" title=\"<PERSON> (computer engineer)\"><PERSON></a>, American computer programmer and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(computer_engineer)\" title=\"<PERSON> (computer engineer)\"><PERSON></a>, American computer programmer and engineer", "links": [{"title": "<PERSON> (computer engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(computer_engineer)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese singer-songwriter, actor, and voice actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese singer-songwriter, actor, and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese singer-songwriter, actor, and voice actor", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Austrian-Swiss footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian model and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)\" title=\"<PERSON> (model)\"><PERSON></a>, Canadian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)\" title=\"<PERSON> (model)\"><PERSON></a>, Canadian model and actress", "links": [{"title": "<PERSON> (model)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)"}]}, {"year": "1978", "text": "<PERSON>, Canadian tennis player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Kenyan runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Finnish singer-songwriter and guitarist (d. 2020)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Indian singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Amit_<PERSON>\" title=\"Amit <PERSON>\"><PERSON><PERSON></a>, Indian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amit_<PERSON>\" title=\"Amit <PERSON>ved<PERSON>\"><PERSON><PERSON></a>, Indian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amit_Trivedi"}]}, {"year": "1980", "text": "<PERSON>, Austrian singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Austrian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Austrian singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Japanese announcer, photographer, and model", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese announcer, photographer, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese announcer, photographer, and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9rick_<PERSON>quet"}]}, {"year": "1981", "text": "<PERSON>, Canadian actor and model", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Israeli model, actor, and screenwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli model, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli model, actor, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Kazakhstani boxer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakhstani boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakhstani boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Indian actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Arjun\"><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_A<PERSON><PERSON>\" title=\"Allu Arjun\"><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Russian runner", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, British politician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Mexican singer and actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pablo <PERSON>\"><PERSON></a>, Mexican singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pablo_Portillo"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, German rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>y"}]}, {"year": "1986", "text": "<PERSON>, Russian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Venezuelan baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Hern%C3%A1ndez"}]}, {"year": "1986", "text": "<PERSON>, Dominican baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dr<PERSON>\" title=\"Royston Drenthe\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dr<PERSON>he\" title=\"Royston Drenthe\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON> Drenthe", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Trinidadian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Trinidadian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Trinidadian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1987", "text": "<PERSON>, New Zealand rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, English singer-songwriter and producer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, South Korean singer (d. 2017)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer (d. 2017)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Swedish ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English YouTuber", "html": "1993 - <a href=\"https://wikipedia.org/wiki/TBJZL\" title=\"TBJZL\">TBJZL</a>, English YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TBJZL\" title=\"TBJZL\">TBJZL</a>, English YouTuber", "links": [{"title": "TBJZL", "link": "https://wikipedia.org/wiki/TBJZL"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Turkish professional basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish professional basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish professional basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Greek Olympic shooter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek Olympic shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek Olympic shooter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, South Korean singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" class=\"mw-redirect\" title=\"<PERSON>-jin\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" class=\"mw-redirect\" title=\"<PERSON>-jin\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Australian singer and songwriter", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Saygra<PERSON>\" title=\"Saygra<PERSON>\"><PERSON><PERSON><PERSON></a>, Australian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>gra<PERSON>\" title=\"Saygra<PERSON>\"><PERSON><PERSON><PERSON></a>, Australian singer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saygrace"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>chueren\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>chueren\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arno_V<PERSON>eren"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Ecuadorian nutritionist, businesswoman and First Lady of Ecuador", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Lavinia_Val<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian nutritionist, businesswoman and <a href=\"https://wikipedia.org/wiki/First_Lady_of_Ecuador\" class=\"mw-redirect\" title=\"First Lady of Ecuador\">First Lady of Ecuador</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lavin<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian nutritionist, businesswoman and <a href=\"https://wikipedia.org/wiki/First_Lady_of_Ecuador\" class=\"mw-redirect\" title=\"First Lady of Ecuador\">First Lady of Ecuador</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavinia_Valbonesi"}, {"title": "First Lady of Ecuador", "link": "https://wikipedia.org/wiki/First_Lady_of_Ecuador"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>e<PERSON>ee_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian ice hockey player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Slovak track and field athlete", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Vikt%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ik<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak track and field athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vikt%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ik<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak track and field athlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vikt%C3%B3<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "217", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 188)", "html": "217 - <a href=\"https://wikipedia.org/wiki/Caracalla\" title=\"Caracal<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 188)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caracalla\" title=\"Caracal<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 188)", "links": [{"title": "Caracal<PERSON>", "link": "https://wikipedia.org/wiki/Caracalla"}]}, {"year": "622", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese prince (b. 572)", "html": "622 - <a href=\"https://wikipedia.org/wiki/Prince_Sh%C5%8Dtoku\" title=\"Prince <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese prince (b. 572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_Sh%C5%8Dtoku\" title=\"Prince <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese prince (b. 572)", "links": [{"title": "Prince <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prince_Sh%C5%8Dtoku"}]}, {"year": "632", "text": "<PERSON><PERSON><PERSON> <PERSON>, Frankish king (b. 607)", "html": "632 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, Frankish king (b. 607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, Frankish king (b. 607)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "894", "text": "<PERSON><PERSON><PERSON>, Frankish nobleman", "html": "894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Troyes\" title=\"<PERSON><PERSON><PERSON>, Count of Troyes\"><PERSON><PERSON><PERSON></a>, Frankish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Troyes\" title=\"<PERSON><PERSON><PERSON>, Count of Troyes\"><PERSON><PERSON><PERSON></a>, Frankish nobleman", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Troyes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Troy<PERSON>"}]}, {"year": "944", "text": "<PERSON>, Chinese emperor", "html": "944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wang_<PERSON>"}]}, {"year": "956", "text": "<PERSON>, Frankish nobleman", "html": "956 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON></a>, Frankish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON></a>, Frankish nobleman", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy"}]}, {"year": "967", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> emir (b. 915)", "html": "967 - <a href=\"https://wikipedia.org/wiki/Mu%27izz_al-Dawla\" title=\"<PERSON><PERSON><PERSON><PERSON> al-Dawla\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, <PERSON><PERSON> emir (b. 915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mu%27izz_al-Dawl<PERSON>\" title=\"<PERSON><PERSON>izz al-Dawla\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, <PERSON><PERSON> emir (b. 915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mu%27iz<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1143", "text": "<PERSON> <PERSON>, Byzantine emperor (b. 1087)", "html": "1143 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1087)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1087)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1150", "text": "<PERSON> of Babenberg, duchess of Bohemia (b. 1118)", "html": "1150 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Babenberg,_Duchess_of_Bohemia\" title=\"<PERSON> of Babenberg, Duchess of Bohemia\"><PERSON> of Babenberg</a>, duchess of Bohemia (b. 1118)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Babe<PERSON>,_Duchess_of_Bohemia\" title=\"<PERSON> of Babenberg, Duchess of Bohemia\"><PERSON> of Babenberg</a>, duchess of Bohemia (b. 1118)", "links": [{"title": "<PERSON> Babenberg, Duchess of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Bohemia"}]}, {"year": "1321", "text": "<PERSON> of Tolentino, Italian-Franciscan missionary (b. c. 1255)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tolentino\" title=\"<PERSON> of Tolentino\"><PERSON> of Tolentino</a>, Italian-Franciscan missionary (b. c. 1255)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tolentino\" title=\"<PERSON> of Tolentino\"><PERSON> of Tolentino</a>, Italian-Franciscan missionary (b. c. 1255)", "links": [{"title": "<PERSON> of Tolentino", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tolentino"}]}, {"year": "1338", "text": "<PERSON>, bishop of London", "html": "1338 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of London", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of London", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1364", "text": "<PERSON>, French king (b. 1319)", "html": "1364 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> II</a>, French king (b. 1319)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> II</a>, French king (b. 1319)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1450", "text": "<PERSON><PERSON> the Great, Korean king (b. 1397)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON> the Great</a>, Korean king (b. 1397)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON> the Great</a>, Korean king (b. 1397)", "links": [{"title": "<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great"}]}, {"year": "1461", "text": "<PERSON>, German mathematician and astronomer (b. 1423)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (b. 1423)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (b. 1423)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1492", "text": "<PERSON>, Italian ruler (b. 1449)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ruler (b. 1449)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ruler (b. 1449)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_Medici"}]}, {"year": "1551", "text": "<PERSON><PERSON>, Japanese warlord (b. 1510)", "html": "1551 - <a href=\"https://wikipedia.org/wiki/Oda_Nobuhide\" title=\"Oda Nobuhide\"><PERSON><PERSON></a>, Japanese warlord (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oda_Nobuhide\" title=\"Oda Nobuhide\"><PERSON><PERSON></a>, Japanese warlord (b. 1510)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oda_No<PERSON>hide"}]}, {"year": "1586", "text": "<PERSON>, Lutheran theologian and reformer (b. 1522)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lutheran theologian and reformer (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lutheran theologian and reformer (b. 1522)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON><PERSON><PERSON><PERSON>, English noble (b. 1538)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/Magdalen_<PERSON>\" title=\"<PERSON>gdale<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English noble (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magdalen_<PERSON>\" title=\"Magdale<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English noble (b. 1538)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Magdale<PERSON>_<PERSON>"}]}, {"year": "1612", "text": "<PERSON> Brandenburg (b. 1575)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brandenburg\" title=\"<PERSON> of Brandenburg\"><PERSON></a> (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brandenburg\" title=\"<PERSON> of Brandenburg\"><PERSON> of Brandenburg</a> (b. 1575)", "links": [{"title": "<PERSON> Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON>, Italian architect, designed the Santa Maria dei Miracoli and Santa Maria in Montesanto (b. 1611)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Santa_Maria_dei_Miracoli_and_<PERSON>_Maria_in_Montesanto\" title=\"Santa Maria dei Miracoli and Santa Maria in Montesanto\">Santa Maria dei Miracoli and Santa Maria in Montesanto</a> (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Santa_Maria_dei_Miracoli_and_<PERSON>_Maria_in_Montesanto\" title=\"Santa Maria dei Miracoli and Santa Maria in Montesanto\">Santa Maria dei Miracoli and Santa Maria in Montesanto</a> (b. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Santa Maria dei Miracoli and Santa Maria in Montesanto", "link": "https://wikipedia.org/wiki/Santa_Maria_dei_Mira<PERSON>li_and_<PERSON>_<PERSON>_in_Montesanto"}]}, {"year": "1697", "text": "<PERSON><PERSON>, Norwegian-Danish admiral (b. 1629)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-Danish admiral (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-Danish admiral (b. 1629)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON><PERSON>, German orientalist and philologist (b. 1624)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German orientalist and philologist (b. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German orientalist and philologist (b. 1624)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, 1st Earl of Romney, English colonel and politician, Lord Lieutenant of Ireland (b. 1641)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON> Romney\"><PERSON>, 1st Earl of Romney</a>, English colonel and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl of Romney</a>, English colonel and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1641)", "links": [{"title": "<PERSON>, 1st Earl of Romney", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_Romney"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1709", "text": "<PERSON> of Castell-Remlingen, German nobleman (b. 1641)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castell-Remlingen\" title=\"<PERSON> of Castell-Remlingen\"><PERSON> of Castell-Remlingen</a>, German nobleman (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castell-Remlingen\" title=\"<PERSON> of Castell-Remlingen\"><PERSON> of Castell-Remlingen</a>, German nobleman (b. 1641)", "links": [{"title": "<PERSON> of Castell-Remlingen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castell-Remlingen"}]}, {"year": "1725", "text": "<PERSON>, American minister (b. 1652)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>, American minister (b. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>, American minister (b. 1652)", "links": [{"title": "<PERSON> (clergyman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(clergyman)"}]}, {"year": "1735", "text": "<PERSON>, Hungarian prince (b. 1676)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_R%C3%A1k%C3%B3czi\" title=\"<PERSON> II <PERSON>\"><PERSON></a>, Hungarian prince (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_R%C3%A1k%C3%B3czi\" title=\"<PERSON> II Rák<PERSON>i\"><PERSON></a>, Hungarian prince (b. 1676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francis_II_R%C3%A1k%C3%B3czi"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, Italian composer (b. 1797)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Hungarian statesman and reformer (b. 1791)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Sz%C3%A9chenyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian statesman and reformer (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Sz%C3%A9chenyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian statesman and reformer (b. 1791)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Sz%C3%A9chenyi"}]}, {"year": "1861", "text": "<PERSON><PERSON>, American businessman, founded the Otis Elevator Company (b. 1811)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Otis_Elevator_Company\" class=\"mw-redirect\" title=\"Otis Elevator Company\">Otis Elevator Company</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Otis_Elevator_Company\" class=\"mw-redirect\" title=\"Otis Elevator Company\">Otis Elevator Company</a> (b. 1811)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Otis Elevator Company", "link": "https://wikipedia.org/wiki/Otis_Elevator_Company"}]}, {"year": "1870", "text": "<PERSON>, Belgian violinist and composer (b. 1802)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9riot\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist and composer (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9riot\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist and composer (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9riot"}]}, {"year": "1877", "text": "<PERSON>, Portuguese physician and naturalist (b. 1806)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, Portuguese physician and naturalist (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, Portuguese physician and naturalist (b. 1806)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/Bernardino_Ant%C3%B3<PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1894", "text": "<PERSON><PERSON>, Indian journalist, author, and poet (b. 1838)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>hyay\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist, author, and poet (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>y\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist, author, and poet (b. 1838)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German woman, first person diagnosed with Alzheimer's disease (b. 1850)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German woman, first person diagnosed with <a href=\"https://wikipedia.org/wiki/Alzheimer%27s_disease\" title=\"Alzheimer's disease\">Alzheimer's disease</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German woman, first person diagnosed with <a href=\"https://wikipedia.org/wiki/Alzheimer%27s_disease\" title=\"Alzheimer's disease\">Alzheimer's disease</a> (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Alzheimer's disease", "link": "https://wikipedia.org/wiki/Alzheimer%27s_disease"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian physicist, academic, and politician, Hungarian Minister of Education (b. 1848)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Lor%C3%A1nd_E%C3%B6tv%C3%B6s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian physicist, academic, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_(Hungary)\" title=\"Minister of Education (Hungary)\">Hungarian Minister of Education</a> (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lor%C3%A1nd_E%C3%B6tv%C3%B6s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian physicist, academic, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_(Hungary)\" title=\"Minister of Education (Hungary)\">Hungarian Minister of Education</a> (b. 1848)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lor%C3%A1nd_E%C3%B6tv%C3%B6s"}, {"title": "Minister of Education (Hungary)", "link": "https://wikipedia.org/wiki/Minister_of_Education_(Hungary)"}]}, {"year": "1920", "text": "<PERSON>, American pianist and composer (b. 1884)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Swedish poet Nobel Prize laureate (b. 1864)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Austrian physician and academic, Nobel Prize laureate (b. 1876)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/R%C3%B3bert_B%C3%A1r%C3%A1ny\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%B3bert_B%C3%A1r%C3%A1ny\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3bert_B%C3%A1r%C3%A1ny"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Czech poet and novelist (b. 1873)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Bo%C5%BEena_Bene%C5%A1ov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech poet and novelist (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo%C5%BEena_Bene%C5%A1ov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech poet and novelist (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bo%C5%B<PERSON>ena_Bene%C5%A1ov%C3%A1"}]}, {"year": "1941", "text": "<PERSON>, French novelist and playwright (b. 1862)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9vost\" title=\"<PERSON>\"><PERSON></a>, French novelist and playwright (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vost\" title=\"<PERSON>\"><PERSON></a>, French novelist and playwright (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Pr%C3%A9vost"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Greek guitarist and composer (b. 1880)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek guitarist and composer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek guitarist and composer (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Norwegian target shooter (b. 1862)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian target shooter (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian target shooter (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Polish dancer and choreographer (b. 1890)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish dancer and choreographer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish dancer and choreographer (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Roman Catholic Archbishop of Athens (b. 1913)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman Catholic Archbishop of Athens (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman Catholic Archbishop of Athens (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Australian public servant (b. 1885)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Spanish bullfighter (b. 1892)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish bullfighter (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish bullfighter (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Swedish actor (b. 1886)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Ukrainian astronomer (b. 1900)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian astronomer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian astronomer (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1973", "text": "<PERSON>, Spanish painter and sculptor (b. 1881)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Picasso\" title=\"Pablo Picasso\"><PERSON></a>, Spanish painter and sculptor (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pablo_Picasso\" title=\"Pablo Picasso\"><PERSON></a>, Spanish painter and sculptor (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian cardinal (b. 1894)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American short story writer (b. 1952)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%27J_Pancake\" title=\"<PERSON><PERSON> D'J Pancake\"><PERSON><PERSON>J Pancake</a>, American short story writer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%27J_Pancake\" title=\"<PERSON><PERSON> D'J Pancake\"><PERSON><PERSON> D'J Pan<PERSON></a>, American short story writer (b. 1952)", "links": [{"title": "<PERSON><PERSON> D'J Pancake", "link": "https://wikipedia.org/wiki/<PERSON>ce_D%27J_Pancake"}]}, {"year": "1981", "text": "<PERSON>, American general (b. 1893)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Japanese actor and director (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and director (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and director (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Russian physicist and academic, Nobel Prize laureate (b. 1894)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1985", "text": "<PERSON>, American pianist and composer (b. 1897)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American activist, inspired the Ryan White Care Act (b. 1971)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, inspired the <a href=\"https://wikipedia.org/wiki/<PERSON>_White_Care_Act\" class=\"mw-redirect\" title=\"Ryan White Care Act\">Ryan White Care Act</a> (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, inspired the <a href=\"https://wikipedia.org/wiki/<PERSON>_White_Care_Act\" class=\"mw-redirect\" title=\"Ryan White Care Act\">Ryan White Care Act</a> (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ryan White Care Act", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Care_Act"}]}, {"year": "1991", "text": "<PERSON>, Swedish musician (b. 1969)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Swedish musician (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Swedish musician (b. 1969)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1992", "text": "<PERSON>, Swiss-Italian pharmacologist and academic, Nobel Prize laureate (b. 1907)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1993", "text": "<PERSON>, American operatic singer (b. 1897)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic singer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic singer (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French-Canadian actor (b. 1899)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Rozet\" title=\"<PERSON>\"><PERSON></a>, French-Canadian actor (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Rozet\" title=\"<PERSON>\"><PERSON></a>, French-Canadian actor (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Rozet"}]}, {"year": "1996", "text": "<PERSON>, American actor and stuntman (b. 1918)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and stuntman (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and stuntman (b. 1918)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1996", "text": "<PERSON>, Argentinian-Spanish actor, director, and screenwriter (b. 1906)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Le%C3%B3n_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Spanish actor, director, and screenwriter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%C3%B3n_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Spanish actor, director, and screenwriter (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Le%C3%B3n_<PERSON><PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian politician (b. 1936)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter and pianist (b. 1947)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech motorcycle racer (b. 1927)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_%C5%A0%C5%A5astn%C3%BD\" title=\"<PERSON>anti<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech motorcycle racer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_%C5%A0%C5%A5astn%C3%BD\" title=\"<PERSON>anti<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech motorcycle racer (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_%C5%A0%C5%A5astn%C3%BD"}]}, {"year": "2000", "text": "<PERSON>, American actress (b. 1910)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Mexican actress (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, Mexican actress (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, Mexican actress (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_F%C3%A9lix"}]}, {"year": "2002", "text": "<PERSON>, American painter (b. 1937)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, German actor (b. 1921)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Canadian choreographer and dancer (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian choreographer and dancer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian choreographer and dancer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Dutch author and poet (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American painter and sculptor (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Le<PERSON>itt\" title=\"<PERSON> LeWitt\"><PERSON></a>, American painter and sculptor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Le<PERSON>itt\" title=\"<PERSON> LeWitt\"><PERSON></a>, American painter and sculptor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sol_LeWitt"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Japanese painter (b. 1924)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American Scientologist, author, investigative journalist, and psychologist (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Scientologist, author, investigative journalist, and psychologist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Scientologist, author, investigative journalist, and psychologist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer (b. 1976)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (b. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English philosopher and academic (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lew"}]}, {"year": "2010", "text": "<PERSON>, English singer-songwriter (b. 1946)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Dutch singer (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Romanian-American painter and photographer (b. 1910)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>dd<PERSON>_<PERSON>\" title=\"<PERSON>dd<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-American painter and photographer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>dd<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-American painter and photographer (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hedd<PERSON>_<PERSON>e"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1961)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Kiel\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kiel"}]}, {"year": "2012", "text": "<PERSON>, Polish-American businessman, founded Commodore International (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American businessman, founded <a href=\"https://wikipedia.org/wiki/Commodore_International\" title=\"Commodore International\">Commodore International</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American businessman, founded <a href=\"https://wikipedia.org/wiki/Commodore_International\" title=\"Commodore International\">Commodore International</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Commodore International", "link": "https://wikipedia.org/wiki/Commodore_International"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Polish-American soldier, historian, and political scientist (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Polish-American soldier, historian, and political scientist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Polish-American soldier, historian, and political scientist (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Russian journalist (b. 1958)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress and singer (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Spanish-Mexican actress and singer (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican actress and singer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican actress and singer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Spanish economist and author (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish economist and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish economist and author (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English politician, first female Prime Minister of the United Kingdom (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "2014", "text": "<PERSON>, Iraqi patriarch (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi patriarch (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi patriarch (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, German author and activist (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and activist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and activist (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, New Zealand architect, designed the Te Papa Tongarewa Museum (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand architect, designed the <a href=\"https://wikipedia.org/wiki/Museum_of_New_Zealand_Te_Papa_Tongarewa\" class=\"mw-redirect\" title=\"Museum of New Zealand Te Papa Tongarewa\">Te Papa Tongarewa Museum</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand architect, designed the <a href=\"https://wikipedia.org/wiki/Museum_of_New_Zealand_Te_Papa_Tongarewa\" class=\"mw-redirect\" title=\"Museum of New Zealand Te Papa Tongarewa\">Te Papa Tongarewa Museum</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Museum of New Zealand Te Papa Tongarewa", "link": "https://wikipedia.org/wiki/Museum_of_New_Zealand_Te_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Indian journalist and author (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Hong Kong chemist and academic (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong chemist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong chemist and academic (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Ukrainian kick-boxer (b. 1987)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian kick-boxer (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian kick-boxer (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American journalist and publisher (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Canadian cardinal (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, Romanian-born American art curator (b. 1926)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-born American art curator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-born American art curator (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American-Canadian voice actor  (b. 1940)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian voice actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian voice actor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "2020", "text": "<PERSON>, Bangladeshi Islamic scholar (b. 1930)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi Islamic scholar (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi Islamic scholar (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Jewish Austrian secretary (b. 1915)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish Austrian secretary (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish Austrian secretary (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Welsh-Australian rugby league player and coach (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian rugby league player and coach (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian rugby league player and coach (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, British physicist, Nobel Prize laureate (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British physicist, Nobel Prize laureate (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British physicist, Nobel Prize laureate (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American Army officer, Medal of Honor recipient (b. 1926)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Army officer, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Army officer, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}]}}