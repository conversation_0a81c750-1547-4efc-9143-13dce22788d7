{"date": "October 31", "url": "https://wikipedia.org/wiki/October_31", "data": {"Events": [{"year": "475", "text": "<PERSON><PERSON><PERSON> is proclaimed Western Roman Emperor.", "html": "475 - <a href=\"https://wikipedia.org/wiki/Romulus_Augustulus\" title=\"Romulus Augustulus\">Romulus Augustulus</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Western_Roman_Emperor\" class=\"mw-redirect\" title=\"Western Roman Emperor\">Western Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Romulus_Augustulus\" title=\"Romulus Augustulus\">Romulus Augustulus</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Western_Roman_Emperor\" class=\"mw-redirect\" title=\"Western Roman Emperor\">Western Roman Emperor</a>.", "links": [{"title": "Romulus Augustulus", "link": "https://wikipedia.org/wiki/Romulus_Augustulus"}, {"title": "Western Roman Emperor", "link": "https://wikipedia.org/wiki/Western_Roman_Emperor"}]}, {"year": "683", "text": "During the Siege of Mecca, the Kaaba catches fire and is burned down.", "html": "683 - During the <a href=\"https://wikipedia.org/wiki/Siege_of_Mecca_(683)\" title=\"Siege of Mecca (683)\">Siege of Mecca</a>, the <a href=\"https://wikipedia.org/wiki/Kaaba\" title=\"Kaaba\">Ka<PERSON></a> catches fire and is burned down.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Siege_of_Mecca_(683)\" title=\"Siege of Mecca (683)\">Siege of Mecca</a>, the <a href=\"https://wikipedia.org/wiki/Kaaba\" title=\"Kaaba\">Ka<PERSON></a> catches fire and is burned down.", "links": [{"title": "Siege of Mecca (683)", "link": "https://wikipedia.org/wiki/Siege_of_Mecca_(683)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kaaba"}]}, {"year": "802", "text": "Empress <PERSON> is deposed and banished to Lesbos. Conspirators place <PERSON><PERSON><PERSON><PERSON>, the minister of finance, on the Byzantine throne.", "html": "802 - Empress <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Athens\" title=\"<PERSON> of Athens\"><PERSON></a> is deposed and banished to <a href=\"https://wikipedia.org/wiki/Lesbos\" title=\"Les<PERSON>\"><PERSON><PERSON></a>. Conspirators place <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON><PERSON></a>, the minister of finance, on the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> throne.", "no_year_html": "Empress <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Athens\" title=\"<PERSON> of Athens\"><PERSON></a> is deposed and banished to <a href=\"https://wikipedia.org/wiki/Les<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>. Conspirators place <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON><PERSON></a>, the minister of finance, on the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> throne.", "links": [{"title": "Irene of Athens", "link": "https://wikipedia.org/wiki/<PERSON>_of_Athens"}, {"title": "Les<PERSON>", "link": "https://wikipedia.org/wiki/Lesbos"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "932", "text": "Abbasid caliph <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is killed while fighting against the forces of general <PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s brother <PERSON><PERSON><PERSON><PERSON><PERSON> is chosen to succeed him.", "html": "932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_caliph\" class=\"mw-redirect\" title=\"Abbasid caliph\"><PERSON><PERSON> caliph</a> <a href=\"https://wikipedia.org/wiki/<PERSON>-Muqtadir\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is killed while fighting against the forces of general <a href=\"https://wikipedia.org/wiki/Mu%27<PERSON>_al-Mu<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>. <PERSON><PERSON><PERSON>'s brother <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is chosen to succeed him.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_caliph\" class=\"mw-redirect\" title=\"Abbasid caliph\"><PERSON><PERSON> caliph</a> <a href=\"https://wikipedia.org/wiki/<PERSON>-Muqtadir\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is killed while fighting against the forces of general <a href=\"https://wikipedia.org/wiki/Mu%27<PERSON>_al-Mu<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>. <PERSON><PERSON><PERSON><PERSON><PERSON>'s brother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is chosen to succeed him.", "links": [{"title": "<PERSON><PERSON> caliph", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_caliph"}, {"title": "Al-Muqtadir", "link": "https://wikipedia.org/wiki/Al-Muqtadir"}, {"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Mu%27<PERSON>_<PERSON>-<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1517", "text": "Protestant Reformation: <PERSON> posts his 95 Theses on the door of the Castle Church in Wittenberg.", "html": "1517 - <a href=\"https://wikipedia.org/wiki/Protestant_Reformation\" class=\"mw-redirect\" title=\"Protestant Reformation\">Protestant Reformation</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> posts his <a href=\"https://wikipedia.org/wiki/95_Theses\" class=\"mw-redirect\" title=\"95 Theses\">95 Theses</a> on the door of the <a href=\"https://wikipedia.org/wiki/All_Saints%27_Church,_Wittenberg\" title=\"All Saints' Church, Wittenberg\">Castle Church</a> in <a href=\"https://wikipedia.org/wiki/Wittenberg\" title=\"Wittenberg\">Wittenberg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Protestant_Reformation\" class=\"mw-redirect\" title=\"Protestant Reformation\">Protestant Reformation</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> posts his <a href=\"https://wikipedia.org/wiki/95_Theses\" class=\"mw-redirect\" title=\"95 Theses\">95 Theses</a> on the door of the <a href=\"https://wikipedia.org/wiki/All_Saints%27_Church,_Wittenberg\" title=\"All Saints' Church, Wittenberg\">Castle Church</a> in <a href=\"https://wikipedia.org/wiki/Wittenberg\" title=\"Wittenberg\">Wittenberg</a>.", "links": [{"title": "Protestant Reformation", "link": "https://wikipedia.org/wiki/Protestant_Reformation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "95 Theses", "link": "https://wikipedia.org/wiki/95_Theses"}, {"title": "All Saints' Church, Wittenberg", "link": "https://wikipedia.org/wiki/All_Saints%27_Church,_<PERSON><PERSON>nberg"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Witte<PERSON>"}]}, {"year": "1587", "text": "Leiden University Library opens its doors after its founding in 1575.", "html": "1587 - <a href=\"https://wikipedia.org/wiki/Leiden_University_Library\" title=\"Leiden University Library\">Leiden University Library</a> opens its doors after its founding in <a href=\"https://wikipedia.org/wiki/1575\" title=\"1575\">1575</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leiden_University_Library\" title=\"Leiden University Library\">Leiden University Library</a> opens its doors after its founding in <a href=\"https://wikipedia.org/wiki/1575\" title=\"1575\">1575</a>.", "links": [{"title": "Leiden University Library", "link": "https://wikipedia.org/wiki/Leiden_University_Library"}, {"title": "1575", "link": "https://wikipedia.org/wiki/1575"}]}, {"year": "1822", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> attempts to dissolve the Congress of the Mexican Empire.", "html": "1822 - Emperor <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide\" title=\"Agustín de Iturbide\"><PERSON><PERSON><PERSON><PERSON> de Iturbide</a> attempts to dissolve the Congress of the <a href=\"https://wikipedia.org/wiki/First_Mexican_Empire\" title=\"First Mexican Empire\">Mexican Empire</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide\" title=\"Agus<PERSON>ín de Iturbide\"><PERSON><PERSON><PERSON><PERSON> de Iturbide</a> attempts to dissolve the Congress of the <a href=\"https://wikipedia.org/wiki/First_Mexican_Empire\" title=\"First Mexican Empire\">Mexican Empire</a>.", "links": [{"title": "Agustín de Iturbide", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide"}, {"title": "First Mexican Empire", "link": "https://wikipedia.org/wiki/First_Mexican_Empire"}]}, {"year": "1837", "text": "Approximately 300 Muscogee die in the steamboat Monmouth disaster on the Trail of Tears in the United States.", "html": "1837 - Approximately 300 <a href=\"https://wikipedia.org/wiki/Musco<PERSON>\" title=\"Muscogee\">Muscogee</a> die in the <a href=\"https://wikipedia.org/wiki/Steamboat_Monmouth_disaster\" title=\"Steamboat Monmouth disaster\">steamboat <i>Monmouth</i> disaster</a> on the <a href=\"https://wikipedia.org/wiki/Trail_of_Tears\" title=\"Trail of Tears\">Trail of Tears</a> in the United States.", "no_year_html": "Approximately 300 <a href=\"https://wikipedia.org/wiki/Musco<PERSON>\" title=\"Muscogee\">Muscogee</a> die in the <a href=\"https://wikipedia.org/wiki/Steamboat_Monmouth_disaster\" title=\"Steamboat Monmouth disaster\">steamboat <i>Monmouth</i> disaster</a> on the <a href=\"https://wikipedia.org/wiki/Trail_of_Tears\" title=\"Trail of Tears\">Trail of Tears</a> in the United States.", "links": [{"title": "Muscogee", "link": "https://wikipedia.org/wiki/Muscogee"}, {"title": "Steamboat Monmouth disaster", "link": "https://wikipedia.org/wiki/Steamboat_Monmouth_disaster"}, {"title": "Trail of Tears", "link": "https://wikipedia.org/wiki/Trail_of_Tears"}]}, {"year": "1863", "text": "The New Zealand Wars resume as British forces in New Zealand led by General <PERSON> begin their Invasion of the Waikato.", "html": "1863 - The <a href=\"https://wikipedia.org/wiki/New_Zealand_Wars\" title=\"New Zealand Wars\">New Zealand Wars</a> resume as <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> forces in New Zealand led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> begin their <a href=\"https://wikipedia.org/wiki/Invasion_of_the_Waikato\" title=\"Invasion of the Waikato\">Invasion of the Waikato</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Zealand_Wars\" title=\"New Zealand Wars\">New Zealand Wars</a> resume as <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> forces in New Zealand led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> begin their <a href=\"https://wikipedia.org/wiki/Invasion_of_the_Waikato\" title=\"Invasion of the Waikato\">Invasion of the Waikato</a>.", "links": [{"title": "New Zealand Wars", "link": "https://wikipedia.org/wiki/New_Zealand_Wars"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}, {"title": "Invasion of the Waikato", "link": "https://wikipedia.org/wiki/Invasion_of_the_Waikato"}]}, {"year": "1864", "text": "Nevada is admitted as the 36th U.S. state.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a> is admitted as the 36th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a> is admitted as the 36th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Nevada", "link": "https://wikipedia.org/wiki/Nevada"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1895", "text": "The strongest earthquake in the Midwestern United States since 1812 strikes near Charleston, Missouri, causing damage and killing at least two.", "html": "1895 - The strongest earthquake in the Midwestern United States since 1812 <a href=\"https://wikipedia.org/wiki/1895_Charleston_earthquake\" title=\"1895 Charleston earthquake\">strikes near Charleston, Missouri</a>, causing damage and killing at least two.", "no_year_html": "The strongest earthquake in the Midwestern United States since 1812 <a href=\"https://wikipedia.org/wiki/1895_Charleston_earthquake\" title=\"1895 Charleston earthquake\">strikes near Charleston, Missouri</a>, causing damage and killing at least two.", "links": [{"title": "1895 Charleston earthquake", "link": "https://wikipedia.org/wiki/1895_Charleston_earthquake"}]}, {"year": "1903", "text": "The Purdue Wreck, a railroad train collision in Indianapolis, kills 17 people, including 14 players of the Purdue University football team.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Purdue_Wreck\" class=\"mw-redirect\" title=\"Purdue Wreck\">Purdue Wreck</a>, a railroad train collision in <a href=\"https://wikipedia.org/wiki/Indianapolis\" title=\"Indianapolis\">Indianapolis</a>, kills 17 people, including 14 players of the <a href=\"https://wikipedia.org/wiki/Purdue_University\" title=\"Purdue University\">Purdue University</a> football team.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Purdue_Wreck\" class=\"mw-redirect\" title=\"Purdue Wreck\">Purdue Wreck</a>, a railroad train collision in <a href=\"https://wikipedia.org/wiki/Indianapolis\" title=\"Indianapolis\">Indianapolis</a>, kills 17 people, including 14 players of the <a href=\"https://wikipedia.org/wiki/Purdue_University\" title=\"Purdue University\">Purdue University</a> football team.", "links": [{"title": "<PERSON> Wreck", "link": "https://wikipedia.org/wiki/Purdue_Wreck"}, {"title": "Indianapolis", "link": "https://wikipedia.org/wiki/Indianapolis"}, {"title": "Purdue University", "link": "https://wikipedia.org/wiki/Purdue_University"}]}, {"year": "1907", "text": "The Parliament of Finland approved the Prohibition Act, but the law was not implemented because it was not ratified by Tsar <PERSON> of Russia.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Parliament of Finland</a> approved the <a href=\"https://wikipedia.org/wiki/Prohibition#Nordic_countries\" title=\"Prohibition\">Prohibition Act</a>, but the law was not implemented because it was not ratified by Tsar <a href=\"https://wikipedia.org/wiki/Nicholas_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\"><PERSON> of Russia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Parliament of Finland</a> approved the <a href=\"https://wikipedia.org/wiki/Prohibition#Nordic_countries\" title=\"Prohibition\">Prohibition Act</a>, but the law was not implemented because it was not ratified by Tsar <a href=\"https://wikipedia.org/wiki/Nicholas_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a>.", "links": [{"title": "Parliament of Finland", "link": "https://wikipedia.org/wiki/Parliament_of_Finland"}, {"title": "Prohibition", "link": "https://wikipedia.org/wiki/Prohibition#Nordic_countries"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}]}, {"year": "1913", "text": "Dedication of the Lincoln Highway, the first automobile highway across United States.", "html": "1913 - Dedication of the <a href=\"https://wikipedia.org/wiki/Lincoln_Highway\" title=\"Lincoln Highway\">Lincoln Highway</a>, the first automobile highway across United States.", "no_year_html": "Dedication of the <a href=\"https://wikipedia.org/wiki/Lincoln_Highway\" title=\"Lincoln Highway\">Lincoln Highway</a>, the first automobile highway across United States.", "links": [{"title": "Lincoln Highway", "link": "https://wikipedia.org/wiki/Lincoln_Highway"}]}, {"year": "1913", "text": "The Indianapolis Streetcar Strike and subsequent riot begins.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Indianapolis_streetcar_strike_of_1913\" title=\"Indianapolis streetcar strike of 1913\">Indianapolis Streetcar Strike and subsequent riot</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Indianapolis_streetcar_strike_of_1913\" title=\"Indianapolis streetcar strike of 1913\">Indianapolis Streetcar Strike and subsequent riot</a> begins.", "links": [{"title": "Indianapolis streetcar strike of 1913", "link": "https://wikipedia.org/wiki/Indianapolis_streetcar_strike_of_1913"}]}, {"year": "1917", "text": "World War I: Battle of Beersheba: The \"last successful cavalry charge in history\".", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Beersheba_(1917)\" title=\"Battle of Beersheba (1917)\">Battle of Beersheba</a>: The \"last successful cavalry charge in history\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Beersheba_(1917)\" title=\"Battle of Beersheba (1917)\">Battle of Beersheba</a>: The \"last successful cavalry charge in history\".", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Beersheba (1917)", "link": "https://wikipedia.org/wiki/Battle_of_Beersheba_(1917)"}]}, {"year": "1918", "text": "World War I: The Aster Revolution terminates the Austro-Hungarian Compromise of 1867, and Hungary achieves full sovereignty.", "html": "1918 - World War I: The <a href=\"https://wikipedia.org/wiki/Aster_Revolution\" title=\"Aster Revolution\">Aster Revolution</a> terminates the <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Compromise_of_1867\" title=\"Austro-Hungarian Compromise of 1867\">Austro-Hungarian Compromise of 1867</a>, and Hungary achieves full sovereignty.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/Aster_Revolution\" title=\"Aster Revolution\">Aster Revolution</a> terminates the <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Compromise_of_1867\" title=\"Austro-Hungarian Compromise of 1867\">Austro-Hungarian Compromise of 1867</a>, and Hungary achieves full sovereignty.", "links": [{"title": "Aster Revolution", "link": "https://wikipedia.org/wiki/Aster_Revolution"}, {"title": "Austro-Hungarian Compromise of 1867", "link": "https://wikipedia.org/wiki/Austro-Hungarian_Compromise_of_1867"}]}, {"year": "1922", "text": "<PERSON> is made Prime Minister of Italy.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a> is made <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a> is made <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1923", "text": "The first of 160 consecutive days of 100° Fahrenheit at Marble Bar, Western Australia.", "html": "1923 - The first of 160 consecutive days of 100° <a href=\"https://wikipedia.org/wiki/Fahrenheit\" title=\"Fahrenheit\">Fahrenheit</a> at <a href=\"https://wikipedia.org/wiki/Marble_Bar,_Western_Australia\" title=\"Marble Bar, Western Australia\">Marble Bar, Western Australia</a>.", "no_year_html": "The first of 160 consecutive days of 100° <a href=\"https://wikipedia.org/wiki/Fahrenheit\" title=\"Fahrenheit\">Fahrenheit</a> at <a href=\"https://wikipedia.org/wiki/Marble_Bar,_Western_Australia\" title=\"Marble Bar, Western Australia\">Marble Bar, Western Australia</a>.", "links": [{"title": "Fahrenheit", "link": "https://wikipedia.org/wiki/Fahrenheit"}, {"title": "Marble Bar, Western Australia", "link": "https://wikipedia.org/wiki/Marble_Bar,_Western_Australia"}]}, {"year": "1924", "text": "World Savings Day is announced in Milan, Italy by the Members of the Association at the 1st International Savings Bank Congress (World Society of Savings Banks).", "html": "1924 - <a href=\"https://wikipedia.org/wiki/World_Savings_Day\" title=\"World Savings Day\">World Savings Day</a> is announced in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> by the Members of the Association at the 1st International <a href=\"https://wikipedia.org/wiki/Savings_Bank\" class=\"mw-redirect\" title=\"Savings Bank\">Savings Bank</a> Congress (World Society of Savings Banks).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_Savings_Day\" title=\"World Savings Day\">World Savings Day</a> is announced in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> by the Members of the Association at the 1st International <a href=\"https://wikipedia.org/wiki/Savings_Bank\" class=\"mw-redirect\" title=\"Savings Bank\">Savings Bank</a> Congress (World Society of Savings Banks).", "links": [{"title": "World Savings Day", "link": "https://wikipedia.org/wiki/World_Savings_Day"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Savings Bank", "link": "https://wikipedia.org/wiki/Savings_Bank"}]}, {"year": "1938", "text": "Great Depression: In an effort to restore investor confidence, the New York Stock Exchange unveils a fifteen-point program aimed to upgrade protection for the investing public.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>: In an effort to restore investor confidence, the <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a> unveils a fifteen-point program aimed to upgrade protection for the investing public.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>: In an effort to restore investor confidence, the <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a> unveils a fifteen-point program aimed to upgrade protection for the investing public.", "links": [{"title": "Great Depression", "link": "https://wikipedia.org/wiki/Great_Depression"}, {"title": "New York Stock Exchange", "link": "https://wikipedia.org/wiki/New_York_Stock_Exchange"}]}, {"year": "1940", "text": "World War II: The Battle of Britain ends, causing Germany to abandon Operation Sea Lion.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Britain\" title=\"Battle of Britain\">Battle of Britain</a> ends, causing Germany to abandon <a href=\"https://wikipedia.org/wiki/Operation_Sea_Lion\" title=\"Operation Sea Lion\">Operation Sea Lion</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Britain\" title=\"Battle of Britain\">Battle of Britain</a> ends, causing Germany to abandon <a href=\"https://wikipedia.org/wiki/Operation_Sea_Lion\" title=\"Operation Sea Lion\">Operation Sea Lion</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Britain", "link": "https://wikipedia.org/wiki/Battle_of_Britain"}, {"title": "Operation Sea Lion", "link": "https://wikipedia.org/wiki/Operation_Sea_Lion"}]}, {"year": "1941", "text": "After 14 years of work, Mount Rushmore is completed.", "html": "1941 - After 14 years of work, <a href=\"https://wikipedia.org/wiki/Mount_Rushmore\" title=\"Mount Rushmore\">Mount Rushmore</a> is completed.", "no_year_html": "After 14 years of work, <a href=\"https://wikipedia.org/wiki/Mount_Rushmore\" title=\"Mount Rushmore\">Mount Rushmore</a> is completed.", "links": [{"title": "Mount Rushmore", "link": "https://wikipedia.org/wiki/Mount_Rushmore"}]}, {"year": "1941", "text": "World War II: The destroyer USS Reuben James is torpedoed by a German U-boat near Iceland, killing more than 100 U.S. Navy sailors. It is the first U.S. Navy vessel sunk by enemy action in WWII.", "html": "1941 - World War II: The destroyer <a href=\"https://wikipedia.org/wiki/USS_<PERSON>_<PERSON>_(DD-245)\" title=\"USS Reuben <PERSON> (DD-245)\">USS <i><PERSON></i></a> is torpedoed by a <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> <a href=\"https://wikipedia.org/wiki/U-boat\" title=\"U-boat\">U-boat</a> near <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>, killing more than 100 U.S. Navy sailors. It is the first U.S. Navy vessel sunk by enemy action in WWII.", "no_year_html": "World War II: The destroyer <a href=\"https://wikipedia.org/wiki/USS_<PERSON>_<PERSON>_(DD-245)\" title=\"USS Reuben <PERSON> (DD-245)\">USS <i><PERSON></i></a> is torpedoed by a <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> <a href=\"https://wikipedia.org/wiki/U-boat\" title=\"U-boat\">U-boat</a> near <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>, killing more than 100 U.S. Navy sailors. It is the first U.S. Navy vessel sunk by enemy action in WWII.", "links": [{"title": "<PERSON> <PERSON> (DD-245)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(DD-245)"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "U-boat", "link": "https://wikipedia.org/wiki/U-boat"}, {"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}]}, {"year": "1943", "text": "World War II: An F4U Corsair accomplishes the first successful radar-guided interception by a United States Navy or Marine Corps aircraft.", "html": "1943 - World War II: An <a href=\"https://wikipedia.org/wiki/F4U_Corsair\" class=\"mw-redirect\" title=\"F4U Corsair\">F4U Corsair</a> accomplishes the first successful <a href=\"https://wikipedia.org/wiki/Radar\" title=\"Radar\">radar</a>-guided interception by a <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> or <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">Marine Corps</a> aircraft.", "no_year_html": "World War II: An <a href=\"https://wikipedia.org/wiki/F4U_Corsair\" class=\"mw-redirect\" title=\"F4U Corsair\">F4U Corsair</a> accomplishes the first successful <a href=\"https://wikipedia.org/wiki/Radar\" title=\"Radar\">radar</a>-guided interception by a <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> or <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">Marine Corps</a> aircraft.", "links": [{"title": "F4U Corsair", "link": "https://wikipedia.org/wiki/F4U_Corsair"}, {"title": "Radar", "link": "https://wikipedia.org/wiki/Radar"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}]}, {"year": "1956", "text": "Suez Crisis: The United Kingdom and France begin bombing Egypt to force the reopening of the Suez Canal.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: The United Kingdom and France begin bombing <a href=\"https://wikipedia.org/wiki/Republic_of_Egypt_(1953%E2%80%9358)\" class=\"mw-redirect\" title=\"Republic of Egypt (1953-58)\">Egypt</a> to force the reopening of the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: The United Kingdom and France begin bombing <a href=\"https://wikipedia.org/wiki/Republic_of_Egypt_(1953%E2%80%9358)\" class=\"mw-redirect\" title=\"Republic of Egypt (1953-58)\">Egypt</a> to force the reopening of the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "links": [{"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}, {"title": "Republic of Egypt (1953-58)", "link": "https://wikipedia.org/wiki/Republic_of_Egypt_(1953%E2%80%9358)"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}, {"year": "1956", "text": "Hungarian Revolution of 1956: A Revolutionary Headquarters is established in Hungary. Following <PERSON><PERSON><PERSON>'s announcement of October 30, banned non-Communist political parties are reformed, and the MDP is replaced by the MSZMP.  <PERSON><PERSON><PERSON><PERSON> is released from prison. The Soviet Politburo makes the decision to crush the Revolution.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution of 1956</a>: A Revolutionary Headquarters is established in Hungary. Following <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>gy\" title=\"<PERSON><PERSON><PERSON>gy\"><PERSON><PERSON><PERSON></a>'s announcement of October 30, banned non-Communist political parties are reformed, and the <a href=\"https://wikipedia.org/wiki/Hungarian_Working_People%27s_Party\" title=\"Hungarian Working People's Party\">MDP</a> is replaced by the <a href=\"https://wikipedia.org/wiki/MSZMP\" class=\"mw-redirect\" title=\"MSZMP\">MSZMP</a>. <a href=\"https://wikipedia.org/wiki/J%C3%B3z<PERSON>_Mindszenty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is released from prison. The <a href=\"https://wikipedia.org/wiki/Soviet_Politburo\" class=\"mw-redirect\" title=\"Soviet Politburo\">Soviet Politburo</a> makes the decision to crush the Revolution.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution of 1956</a>: A Revolutionary Headquarters is established in Hungary. Following <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>gy\" title=\"<PERSON><PERSON><PERSON> Nagy\"><PERSON><PERSON><PERSON></a>'s announcement of October 30, banned non-Communist political parties are reformed, and the <a href=\"https://wikipedia.org/wiki/Hungarian_Working_People%27s_Party\" title=\"Hungarian Working People's Party\">MDP</a> is replaced by the <a href=\"https://wikipedia.org/wiki/MSZMP\" class=\"mw-redirect\" title=\"MSZMP\">MSZMP</a>. <a href=\"https://wikipedia.org/wiki/J%C3%B3z<PERSON>_<PERSON>enty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is released from prison. The <a href=\"https://wikipedia.org/wiki/Soviet_Politburo\" class=\"mw-redirect\" title=\"Soviet Politburo\">Soviet Politburo</a> makes the decision to crush the Revolution.", "links": [{"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy"}, {"title": "Hungarian Working People's Party", "link": "https://wikipedia.org/wiki/Hungarian_Working_People%27s_Party"}, {"title": "MSZMP", "link": "https://wikipedia.org/wiki/MSZMP"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>szenty"}, {"title": "Soviet Politburo", "link": "https://wikipedia.org/wiki/Soviet_Politburo"}]}, {"year": "1961", "text": "In the Soviet Union, <PERSON>'s body is removed from <PERSON>'s Mausoleum, also known as the Lenin Tomb.", "html": "1961 - In the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s body is removed from <a href=\"https://wikipedia.org/wiki/Lenin%27s_Mausoleum\" title=\"Lenin's Mausoleum\"><PERSON>'s Mausoleum</a>, also known as the Lenin Tomb.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s body is removed from <a href=\"https://wikipedia.org/wiki/Lenin%27s_Mausoleum\" title=\"<PERSON>'s Mausoleum\"><PERSON>'s Mausoleum</a>, also known as the Lenin Tomb.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>'s Mausoleum", "link": "https://wikipedia.org/wiki/Lenin%27s_Mausoleum"}]}, {"year": "1963", "text": "Indiana State Fairgrounds Coliseum gas explosion: A gas explosion at the Indiana State Fairgrounds Coliseum in Indianapolis kills 81 people and injures another 400 during an ice show.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/1963_Indiana_State_Fairgrounds_Coliseum_gas_explosion\" title=\"1963 Indiana State Fairgrounds Coliseum gas explosion\">Indiana State Fairgrounds Coliseum gas explosion</a>: A <a href=\"https://wikipedia.org/wiki/Gas_explosion\" title=\"Gas explosion\">gas explosion</a> at the <a href=\"https://wikipedia.org/wiki/Indiana_State_Fairgrounds_Coliseum\" class=\"mw-redirect\" title=\"Indiana State Fairgrounds Coliseum\">Indiana State Fairgrounds Coliseum</a> in Indianapolis kills 81 people and injures another 400 during an ice show.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1963_Indiana_State_Fairgrounds_Coliseum_gas_explosion\" title=\"1963 Indiana State Fairgrounds Coliseum gas explosion\">Indiana State Fairgrounds Coliseum gas explosion</a>: A <a href=\"https://wikipedia.org/wiki/Gas_explosion\" title=\"Gas explosion\">gas explosion</a> at the <a href=\"https://wikipedia.org/wiki/Indiana_State_Fairgrounds_Coliseum\" class=\"mw-redirect\" title=\"Indiana State Fairgrounds Coliseum\">Indiana State Fairgrounds Coliseum</a> in Indianapolis kills 81 people and injures another 400 during an ice show.", "links": [{"title": "1963 Indiana State Fairgrounds Coliseum gas explosion", "link": "https://wikipedia.org/wiki/1963_Indiana_State_Fairgrounds_Coliseum_gas_explosion"}, {"title": "Gas explosion", "link": "https://wikipedia.org/wiki/Gas_explosion"}, {"title": "Indiana State Fairgrounds Coliseum", "link": "https://wikipedia.org/wiki/Indiana_State_Fairgrounds_Coliseum"}]}, {"year": "1968", "text": "Vietnam War October surprise: Citing progress with the Paris peace talks, US President <PERSON> announces to the nation that he has ordered a complete cessation of \"all air, naval, and artillery bombardment of North Vietnam\" effective November 1.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a> <a href=\"https://wikipedia.org/wiki/October_surprise\" title=\"October surprise\">October surprise</a>: Citing progress with the Paris peace talks, US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces to the nation that he has ordered a complete cessation of \"all air, naval, and artillery bombardment of <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>\" effective <a href=\"https://wikipedia.org/wiki/November_1\" title=\"November 1\">November 1</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a> <a href=\"https://wikipedia.org/wiki/October_surprise\" title=\"October surprise\">October surprise</a>: Citing progress with the Paris peace talks, US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces to the nation that he has ordered a complete cessation of \"all air, naval, and artillery bombardment of <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>\" effective <a href=\"https://wikipedia.org/wiki/November_1\" title=\"November 1\">November 1</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "October surprise", "link": "https://wikipedia.org/wiki/October_surprise"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "November 1", "link": "https://wikipedia.org/wiki/November_1"}]}, {"year": "1973", "text": "Mountjoy Prison helicopter escape. Three Provisional Irish Republican Army members escape from Mountjoy Prison, Dublin aboard a hijacked helicopter that landed in the exercise yard.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Mountjoy_Prison_helicopter_escape\" title=\"Mountjoy Prison helicopter escape\">Mountjoy Prison helicopter escape</a>. Three <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> members escape from <a href=\"https://wikipedia.org/wiki/Mountjoy_Prison\" title=\"Mountjoy Prison\">Mountjoy Prison</a>, <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a> aboard a hijacked helicopter that landed in the exercise yard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mountjoy_Prison_helicopter_escape\" title=\"Mountjoy Prison helicopter escape\">Mountjoy Prison helicopter escape</a>. Three <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> members escape from <a href=\"https://wikipedia.org/wiki/Mountjoy_Prison\" title=\"Mountjoy Prison\">Mountjoy Prison</a>, <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a> aboard a hijacked helicopter that landed in the exercise yard.", "links": [{"title": "Mountjoy Prison helicopter escape", "link": "https://wikipedia.org/wiki/Mountjoy_Prison_helicopter_escape"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Mountjoy Prison", "link": "https://wikipedia.org/wiki/Mountjoy_Prison"}, {"title": "Dublin", "link": "https://wikipedia.org/wiki/Dublin"}]}, {"year": "1979", "text": "Western Airlines Flight 2605 crashes on landing in Mexico City, killing 73 people.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Western_Airlines_Flight_2605\" title=\"Western Airlines Flight 2605\">Western Airlines Flight 2605</a> crashes on landing in <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a>, killing 73 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Western_Airlines_Flight_2605\" title=\"Western Airlines Flight 2605\">Western Airlines Flight 2605</a> crashes on landing in <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a>, killing 73 people.", "links": [{"title": "Western Airlines Flight 2605", "link": "https://wikipedia.org/wiki/Western_Airlines_Flight_2605"}, {"title": "Mexico City", "link": "https://wikipedia.org/wiki/Mexico_City"}]}, {"year": "1984", "text": "Indian Prime Minister <PERSON><PERSON> is assassinated by two Sikh security guards. Riots break out in New Delhi and other cities and around 3,000 Sikhs are killed.", "html": "1984 - Indian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_Gandhi\" title=\"Assassination of <PERSON><PERSON>\">assassinated</a> by two Sikh security guards. <a href=\"https://wikipedia.org/wiki/1984_anti-Sikh_riots\" title=\"1984 anti-Sikh riots\">Riots break out</a> in New Delhi and other cities and around 3,000 Sikhs are killed.", "no_year_html": "Indian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_Gandhi\" title=\"Assassination of <PERSON><PERSON>\">assassinated</a> by two Sikh security guards. <a href=\"https://wikipedia.org/wiki/1984_anti-Sikh_riots\" title=\"1984 anti-Sikh riots\">Riots break out</a> in New Delhi and other cities and around 3,000 Sikhs are killed.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indira_Gandhi"}, {"title": "Assassination of <PERSON><PERSON> Gandhi", "link": "https://wikipedia.org/wiki/Assassination_of_Indira_Gandhi"}, {"title": "1984 anti-Sikh riots", "link": "https://wikipedia.org/wiki/1984_anti-Sikh_riots"}]}, {"year": "1994", "text": "American Eagle Flight 4184 crashes near Roselawn, Indiana killing all 68 people on board.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/American_Eagle_Flight_4184\" title=\"American Eagle Flight 4184\">American Eagle Flight 4184</a> crashes near <a href=\"https://wikipedia.org/wiki/Roselawn,_Indiana\" title=\"Roselawn, Indiana\">Roselawn, Indiana</a> killing all 68 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Eagle_Flight_4184\" title=\"American Eagle Flight 4184\">American Eagle Flight 4184</a> crashes near <a href=\"https://wikipedia.org/wiki/Roselawn,_Indiana\" title=\"Roselawn, Indiana\">Roselawn, Indiana</a> killing all 68 people on board.", "links": [{"title": "American Eagle Flight 4184", "link": "https://wikipedia.org/wiki/American_Eagle_Flight_4184"}, {"title": "Roselawn, Indiana", "link": "https://wikipedia.org/wiki/Roselawn,_Indiana"}]}, {"year": "1996", "text": "TAM Transportes Aéreos Regionais Flight 402 crashes in São Paulo, Brazil, killing 99 people.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/TAM_Transportes_A%C3%A9reos_Regionais_Flight_402\" title=\"TAM Transportes Aéreos Regionais Flight 402\">TAM Transportes Aéreos Regionais Flight 402</a> crashes in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo,_Brazil\" class=\"mw-redirect\" title=\"São Paulo, Brazil\">São Paulo, Brazil</a>, killing 99 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAM_Transportes_A%C3%A9reos_Regionais_Flight_402\" title=\"TAM Transportes Aéreos Regionais Flight 402\">TAM Transportes Aéreos Regionais Flight 402</a> crashes in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo,_Brazil\" class=\"mw-redirect\" title=\"São Paulo, Brazil\">São Paulo, Brazil</a>, killing 99 people.", "links": [{"title": "TAM Transportes Aéreos Regionais Flight 402", "link": "https://wikipedia.org/wiki/TAM_Transportes_A%C3%A9reos_Regionais_Flight_402"}, {"title": "São Paulo, Brazil", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo,_Brazil"}]}, {"year": "1998", "text": "Iraq disarmament crisis begins: Iraq announces it would no longer cooperate with United Nations weapons inspectors.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Iraq_disarmament_crisis\" title=\"Iraq disarmament crisis\">Iraq disarmament crisis</a> begins: <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> announces it would no longer cooperate with <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> weapons inspectors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_disarmament_crisis\" title=\"Iraq disarmament crisis\">Iraq disarmament crisis</a> begins: <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> announces it would no longer cooperate with <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> weapons inspectors.", "links": [{"title": "Iraq disarmament crisis", "link": "https://wikipedia.org/wiki/Iraq_disarmament_crisis"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1999", "text": "Yachtsman <PERSON> returns to Melbourne after 11 months of circumnavigating the world, solo, non-stop and unassisted.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Yachtsman\" class=\"mw-redirect\" title=\"Yachtsman\">Yachtsman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a> after 11 months of <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigating</a> the world, solo, non-stop and unassisted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yachtsman\" class=\"mw-redirect\" title=\"Yachtsman\">Yachtsman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a> after 11 months of <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigating</a> the world, solo, non-stop and unassisted.", "links": [{"title": "Yachtsman", "link": "https://wikipedia.org/wiki/Yachtsman"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}, {"title": "Circumnavigation", "link": "https://wikipedia.org/wiki/Circumnavigation"}]}, {"year": "1999", "text": "EgyptAir Flight 990 crashes into the Atlantic Ocean near Nantucket, killing all 217 people on board.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_990\" title=\"EgyptAir Flight 990\">EgyptAir Flight 990</a> crashes into the Atlantic Ocean near Nantucket, killing all 217 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_990\" title=\"EgyptAir Flight 990\">EgyptAir Flight 990</a> crashes into the Atlantic Ocean near Nantucket, killing all 217 people on board.", "links": [{"title": "EgyptAir Flight 990", "link": "https://wikipedia.org/wiki/EgyptAir_Flight_990"}]}, {"year": "2000", "text": "Soyuz TM-31 launches, carrying the first resident crew to the International Space Station. The ISS has been crewed continuously since then.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Soyuz_TM-31\" title=\"Soyuz TM-31\">Soyuz TM-31</a> launches, carrying the first resident crew to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>. The ISS has been crewed continuously since then.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soyuz_TM-31\" title=\"Soyuz TM-31\">Soyuz TM-31</a> launches, carrying the first resident crew to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>. The ISS has been crewed continuously since then.", "links": [{"title": "Soyuz TM-31", "link": "https://wikipedia.org/wiki/Soyuz_TM-31"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2000", "text": "Singapore Airlines Flight 006 crashes on takeoff from Taipei, killing 83.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Singapore_Airlines_Flight_006\" title=\"Singapore Airlines Flight 006\">Singapore Airlines Flight 006</a> crashes on takeoff from <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a>, killing 83.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Singapore_Airlines_Flight_006\" title=\"Singapore Airlines Flight 006\">Singapore Airlines Flight 006</a> crashes on takeoff from <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a>, killing 83.", "links": [{"title": "Singapore Airlines Flight 006", "link": "https://wikipedia.org/wiki/Singapore_Airlines_Flight_006"}, {"title": "Taipei", "link": "https://wikipedia.org/wiki/Taipei"}]}, {"year": "2002", "text": "A federal grand jury in Houston, Texas indicts former Enron chief financial officer <PERSON> on 78 counts of wire fraud, money laundering, conspiracy and obstruction of justice related to the collapse of his ex-employer.", "html": "2002 - A federal grand jury in <a href=\"https://wikipedia.org/wiki/Houston,_Texas\" class=\"mw-redirect\" title=\"Houston, Texas\">Houston, Texas</a> indicts former <a href=\"https://wikipedia.org/wiki/Enron\" title=\"Enron\"><PERSON><PERSON></a> chief financial officer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> on 78 counts of <a href=\"https://wikipedia.org/wiki/Wire_fraud\" class=\"mw-redirect\" title=\"Wire fraud\">wire fraud</a>, <a href=\"https://wikipedia.org/wiki/Money_laundering\" title=\"Money laundering\">money laundering</a>, <a href=\"https://wikipedia.org/wiki/Conspiracy_(criminal)\" class=\"mw-redirect\" title=\"Conspiracy (criminal)\">conspiracy</a> and <a href=\"https://wikipedia.org/wiki/Obstruction_of_justice\" class=\"mw-redirect\" title=\"Obstruction of justice\">obstruction of justice</a> related to the collapse of his ex-employer.", "no_year_html": "A federal grand jury in <a href=\"https://wikipedia.org/wiki/Houston,_Texas\" class=\"mw-redirect\" title=\"Houston, Texas\">Houston, Texas</a> indicts former <a href=\"https://wikipedia.org/wiki/Enron\" title=\"Enron\"><PERSON><PERSON></a> chief financial officer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> on 78 counts of <a href=\"https://wikipedia.org/wiki/Wire_fraud\" class=\"mw-redirect\" title=\"Wire fraud\">wire fraud</a>, <a href=\"https://wikipedia.org/wiki/Money_laundering\" title=\"Money laundering\">money laundering</a>, <a href=\"https://wikipedia.org/wiki/Conspiracy_(criminal)\" class=\"mw-redirect\" title=\"Conspiracy (criminal)\">conspiracy</a> and <a href=\"https://wikipedia.org/wiki/Obstruction_of_justice\" class=\"mw-redirect\" title=\"Obstruction of justice\">obstruction of justice</a> related to the collapse of his ex-employer.", "links": [{"title": "Houston, Texas", "link": "https://wikipedia.org/wiki/Houston,_Texas"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enron"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wire fraud", "link": "https://wikipedia.org/wiki/Wire_fraud"}, {"title": "Money laundering", "link": "https://wikipedia.org/wiki/Money_laundering"}, {"title": "Conspiracy (criminal)", "link": "https://wikipedia.org/wiki/Conspiracy_(criminal)"}, {"title": "Obstruction of justice", "link": "https://wikipedia.org/wiki/Obstruction_of_justice"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON> resigns as Prime Minister of Malaysia and is replaced by Deputy Prime Minister <PERSON>, marking an end to <PERSON><PERSON><PERSON>'s 22 years in power.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a> and is replaced by Deputy Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, marking an end to <PERSON><PERSON><PERSON>'s 22 years in power.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a> and is replaced by Deputy Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, marking an end to <PERSON><PERSON><PERSON>'s 22 years in power.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Malaysia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malaysia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "The global population of humans reaches seven billion. This day is now recognized by the United Nations as the Day of Seven Billion.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/Global_population\" class=\"mw-redirect\" title=\"Global population\">global population</a> of humans reaches seven billion. This day is now recognized by the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> as the <a href=\"https://wikipedia.org/wiki/Day_of_Seven_Billion\" title=\"Day of Seven Billion\">Day of Seven Billion</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Global_population\" class=\"mw-redirect\" title=\"Global population\">global population</a> of humans reaches seven billion. This day is now recognized by the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> as the <a href=\"https://wikipedia.org/wiki/Day_of_Seven_Billion\" title=\"Day of Seven Billion\">Day of Seven Billion</a>.", "links": [{"title": "Global population", "link": "https://wikipedia.org/wiki/Global_population"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Day of Seven Billion", "link": "https://wikipedia.org/wiki/Day_of_Seven_Billion"}]}, {"year": "2014", "text": "During a test flight, the VSS Enterprise, a Virgin Galactic experimental spaceflight test vehicle, suffers a catastrophic in-flight breakup and crashes in the Mojave Desert, California.", "html": "2014 - During a test flight, the <a href=\"https://wikipedia.org/wiki/VSS_Enterprise\" title=\"VSS Enterprise\">VSS <i>Enterprise</i></a>, a <a href=\"https://wikipedia.org/wiki/Virgin_Galactic\" title=\"Virgin Galactic\">Virgin Galactic</a> experimental spaceflight test vehicle, suffers a <a href=\"https://wikipedia.org/wiki/Catastrophic_failure\" title=\"Catastrophic failure\">catastrophic in-flight breakup</a> and <a href=\"https://wikipedia.org/wiki/VSS_Enterprise_crash\" title=\"VSS Enterprise crash\">crashes</a> in the <a href=\"https://wikipedia.org/wiki/Mojave_Desert\" title=\"Mojave Desert\">Mojave Desert</a>, <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "no_year_html": "During a test flight, the <a href=\"https://wikipedia.org/wiki/VSS_Enterprise\" title=\"VSS Enterprise\">VSS <i>Enterprise</i></a>, a <a href=\"https://wikipedia.org/wiki/Virgin_Galactic\" title=\"Virgin Galactic\">Virgin Galactic</a> experimental spaceflight test vehicle, suffers a <a href=\"https://wikipedia.org/wiki/Catastrophic_failure\" title=\"Catastrophic failure\">catastrophic in-flight breakup</a> and <a href=\"https://wikipedia.org/wiki/VSS_Enterprise_crash\" title=\"VSS Enterprise crash\">crashes</a> in the <a href=\"https://wikipedia.org/wiki/Mojave_Desert\" title=\"Mojave Desert\">Mojave Desert</a>, <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "links": [{"title": "VSS Enterprise", "link": "https://wikipedia.org/wiki/VSS_Enterprise"}, {"title": "Virgin Galactic", "link": "https://wikipedia.org/wiki/Virgin_Galactic"}, {"title": "Catastrophic failure", "link": "https://wikipedia.org/wiki/Catastrophic_failure"}, {"title": "VSS Enterprise crash", "link": "https://wikipedia.org/wiki/VSS_Enterprise_crash"}, {"title": "Mojave Desert", "link": "https://wikipedia.org/wiki/Mojave_Desert"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "2015", "text": "Metrojet Flight 9268 is bombed over the northern Sinai Peninsula, killing all 224 people on board.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Metrojet_Flight_9268\" title=\"Metrojet Flight 9268\">Metrojet Flight 9268</a> is bombed over the northern <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>, killing all 224 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Metrojet_Flight_9268\" title=\"Metrojet Flight 9268\">Metrojet Flight 9268</a> is bombed over the northern <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>, killing all 224 people on board.", "links": [{"title": "Metrojet Flight 9268", "link": "https://wikipedia.org/wiki/Metrojet_Flight_9268"}, {"title": "Sinai Peninsula", "link": "https://wikipedia.org/wiki/Sinai_Peninsula"}]}, {"year": "2017", "text": "A truck drives into a crowd in Lower Manhattan, New York City, killing eight people.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/2017_New_York_City_truck_attack\" title=\"2017 New York City truck attack\">A truck drives into a crowd</a> in <a href=\"https://wikipedia.org/wiki/Lower_Manhattan\" title=\"Lower Manhattan\">Lower Manhattan</a>, New York City, killing eight people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2017_New_York_City_truck_attack\" title=\"2017 New York City truck attack\">A truck drives into a crowd</a> in <a href=\"https://wikipedia.org/wiki/Lower_Manhattan\" title=\"Lower Manhattan\">Lower Manhattan</a>, New York City, killing eight people.", "links": [{"title": "2017 New York City truck attack", "link": "https://wikipedia.org/wiki/2017_New_York_City_truck_attack"}, {"title": "Lower Manhattan", "link": "https://wikipedia.org/wiki/Lower_Manhattan"}]}, {"year": "2020", "text": "Berlin Brandenburg Airport opens its doors after nearly 10 years of delays due to construction issues and project corruption.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Berlin_Brandenburg_Airport\" title=\"Berlin Brandenburg Airport\">Berlin Brandenburg Airport</a> opens its doors after nearly 10 years of delays due to construction issues and project corruption.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Berlin_Brandenburg_Airport\" title=\"Berlin Brandenburg Airport\">Berlin Brandenburg Airport</a> opens its doors after nearly 10 years of delays due to construction issues and project corruption.", "links": [{"title": "Berlin Brandenburg Airport", "link": "https://wikipedia.org/wiki/Berlin_Brandenburg_Airport"}]}], "Births": [{"year": "1345", "text": "<PERSON>, king of Portugal (d. 1383)", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> I of Portugal\"><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portugal</a> (d. 1383)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> I of Portugal\"><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portugal</a> (d. 1383)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}, {"title": "Kingdom of Portugal", "link": "https://wikipedia.org/wiki/Kingdom_of_Portugal"}]}, {"year": "1391", "text": "<PERSON>, King of Portugal (d. 1438)", "html": "1391 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal\" title=\"<PERSON>, King of Portugal\"><PERSON>, King of Portugal</a> (d. 1438)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal\" title=\"<PERSON>, King of Portugal\"><PERSON>, King of Portugal</a> (d. 1438)", "links": [{"title": "<PERSON>, King of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal"}]}, {"year": "1424", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Poland (d. 1444)", "html": "1424 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland\" title=\"<PERSON><PERSON><PERSON><PERSON> III of Poland\"><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a> (d. 1444)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland\" title=\"<PERSON><PERSON><PERSON><PERSON> III of Poland\"><PERSON><PERSON><PERSON><PERSON> III</a>, king of <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a> (d. 1444)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Poland", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland"}, {"title": "Poland", "link": "https://wikipedia.org/wiki/Poland"}]}, {"year": "1445", "text": "<PERSON><PERSON><PERSON>, Abbess of Quedlinburg, Princess-Abbess of Quedlinburg (d. 1511)", "html": "1445 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON><PERSON><PERSON>, Abbess of Quedlinburg\"><PERSON><PERSON><PERSON>, Abbess of Quedlinburg</a>, Princess-Abbess of Quedlinburg (d. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON><PERSON><PERSON>, Abbess of Quedlinburg\"><PERSON><PERSON><PERSON>, Abbess of Quedlinburg</a>, Princess-Abbess of Quedlinburg (d. 1511)", "links": [{"title": "<PERSON><PERSON><PERSON>, Abbess of Quedlinburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Abbess_of_Quedlinburg"}]}, {"year": "1472", "text": "<PERSON>, Chinese Neo-Confucian scholar (d. 1529)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese Neo-Confucian scholar (d. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese Neo-Confucian scholar (d. 1529)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1542", "text": "<PERSON><PERSON> Cleves, Duchess of Nevers, Countess of Rethel (d. 1601)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cleves\" title=\"<PERSON><PERSON> of Cleves\"><PERSON><PERSON> of Cleves</a>, Duchess of Nevers, Countess of Rethel (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Clev<PERSON>\" title=\"<PERSON><PERSON> of Cleves\"><PERSON><PERSON> of Cleves</a>, Duchess of Nevers, Countess of Rethel (d. 1601)", "links": [{"title": "<PERSON><PERSON> of Cleves", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>, English politician (d. 1680)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English politician (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English politician (d. 1680)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1620", "text": "<PERSON>, English gardener and author (d. 1706)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gardener and author (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gardener and author (d. 1706)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1632", "text": "<PERSON>, Dutch painter (d. 1675)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1636", "text": "<PERSON>, Elector of Bavaria (d. 1679)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria\" title=\"<PERSON>, Elector of Bavaria\"><PERSON>, Elector of Bavaria</a> (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria\" title=\"<PERSON>, Elector of Bavaria\"><PERSON>, Elector of Bavaria</a> (d. 1679)", "links": [{"title": "<PERSON>, Elector of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria"}]}, {"year": "1638", "text": "<PERSON><PERSON><PERSON>, Dutch painter (d. 1709)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Hobbema\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Hobbema\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1709)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON><PERSON><PERSON>, Italian singer and actor (d. 1758)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer and actor (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer and actor (d. 1758)", "links": [{"title": "Senesino", "link": "https://wikipedia.org/wiki/Senesino"}]}, {"year": "1692", "text": "<PERSON>, French archaeologist and author (d. 1765)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archaeologist and author (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archaeologist and author (d. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON><PERSON><PERSON> of Joseon (d. 1776)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Joseon\" title=\"<PERSON><PERSON><PERSON> of Joseon\"><PERSON><PERSON><PERSON> of Joseon</a> (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Joseon\" title=\"<PERSON><PERSON><PERSON> of Joseon\"><PERSON><PERSON><PERSON> of Joseon</a> (d. 1776)", "links": [{"title": "<PERSON><PERSON><PERSON> of Joseon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Joseon"}]}, {"year": "1705", "text": "<PERSON> (d. 1774)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_XIV\" title=\"Pope Clement XIV\"><PERSON></a> (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_<PERSON>\" title=\"Pope Clement XIV\"><PERSON></a> (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON>, Italian physician, physicist, and academic (d. 1778)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician, physicist, and academic (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician, physicist, and academic (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1714", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish courtier (d. 1744)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/Hedvig_<PERSON>\" title=\"Hedvi<PERSON>\">He<PERSON><PERSON><PERSON></a>, Swedish courtier (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hedvi<PERSON>_<PERSON>\" title=\"Hedvig <PERSON>\">Hed<PERSON><PERSON></a>, Swedish courtier (d. 1744)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hedvi<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, English author and poet (d. 1805)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, Spanish cleric, Archbishop of Mexico, Viceroy of New Spain (d. 1800)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA%C3%B1ez_de_Ha<PERSON>_y_Peralta\" title=\"<PERSON>\"><PERSON></a>, Spanish cleric, Archbishop of Mexico, <a href=\"https://wikipedia.org/wiki/Viceroy_of_New_Spain\" class=\"mw-redirect\" title=\"Viceroy of New Spain\">Viceroy of New Spain</a> (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA%C3%B1ez_de_<PERSON>_y_Peralta\" title=\"<PERSON>\"><PERSON></a>, Spanish cleric, Archbishop of Mexico, <a href=\"https://wikipedia.org/wiki/Viceroy_of_New_Spain\" class=\"mw-redirect\" title=\"Viceroy of New Spain\">Viceroy of New Spain</a> (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alonso_N%C3%BA%C3%B1ez_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Viceroy of New Spain", "link": "https://wikipedia.org/wiki/Viceroy_of_New_Spain"}]}, {"year": "1737", "text": "<PERSON>, American educator and politician (d. 1789)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)\" class=\"mw-redirect\" title=\"<PERSON> (Continental Congress)\"><PERSON></a>, American educator and politician (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)\" class=\"mw-redirect\" title=\"<PERSON> (Continental Congress)\"><PERSON></a>, American educator and politician (d. 1789)", "links": [{"title": "<PERSON> (Continental Congress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)"}]}, {"year": "1760", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese artist and printmaker (d. 1849)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/Hokusai\" title=\"Hokusai\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese artist and printmaker (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hokusai\" title=\"Hokusai\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese artist and printmaker (d. 1849)", "links": [{"title": "Hokusai", "link": "https://wikipedia.org/wiki/Hokusai"}]}, {"year": "1795", "text": "<PERSON>, English poet (d. 1821)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, English architect, architectural historian, railway engineer, and sanitary reformer (d. 1877)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, architectural historian, railway engineer, and sanitary reformer (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, architectural historian, railway engineer, and sanitary reformer (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, English-Australian politician, 5th Premier of Tasmania (d. 1884)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1884)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1815", "text": "<PERSON>, German mathematician and academic (d. 1897)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, French-Algerian cardinal and academic (d. 1892)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Algerian cardinal and academic (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Algerian cardinal and academic (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, Italian neurologist, physiologist, and anthropologist (d. 1910)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian neurologist, physiologist, and anthropologist (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian neurologist, physiologist, and anthropologist (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paolo_<PERSON>"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON>, American general and politician, 27th Governor of Mississippi (d. 1933)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Mississippi\" title=\"Governor of Mississippi\">Governor of Mississippi</a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Mississippi\" title=\"Governor of Mississippi\">Governor of Mississippi</a> (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Mississippi", "link": "https://wikipedia.org/wiki/Governor_of_Mississippi"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian linguist and author (d. 1923)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Kri%C5%A1j%C4%81nis_Barons\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian linguist and author (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kri%C5%A1j%C4%81nis_Barons\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian linguist and author (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kri%C5%A1j%C4%81nis_Barons"}]}, {"year": "1835", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1917)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1838", "text": "<PERSON><PERSON> of Portugal (d. 1889)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_I_of_Portugal\" title=\"<PERSON><PERSON> I of Portugal\"><PERSON><PERSON> of Portugal</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_I_of_Portugal\" title=\"<PERSON><PERSON> of Portugal\"><PERSON><PERSON> of Portugal</a> (d. 1889)", "links": [{"title": "<PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Lu%C3%ADs_I_of_Portugal"}]}, {"year": "1847", "text": "<PERSON>, Italian physicist and engineer (d. 1897)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Galileo_Ferraris\" title=\"Galileo Ferraris\">Galileo Ferraris</a>, Italian physicist and engineer (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Galileo_Ferraris\" title=\"Galileo Ferraris\">Galileo Ferraris</a>, Italian physicist and engineer (d. 1897)", "links": [{"title": "Galileo Ferraris", "link": "https://wikipedia.org/wiki/Galileo_Ferraris"}]}, {"year": "1848", "text": "<PERSON>, American soldier (d. 1876)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Boston_Custer\" title=\"Boston Custer\"><PERSON> Custer</a>, American soldier (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boston_Custer\" title=\"Boston Custer\">Boston Custer</a>, American soldier (d. 1876)", "links": [{"title": "Boston Custer", "link": "https://wikipedia.org/wiki/Boston_Custer"}]}, {"year": "1849", "text": "<PERSON>, American story writer and journalist  (d. 1891)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American story writer and journalist (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American story writer and journalist (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON> of Sweden (d. 1926)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (d. 1926)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sweden"}]}, {"year": "1856", "text": "<PERSON>, American balloonist and skydiver (d. 1889)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American balloonist and skydiver (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American balloonist and skydiver (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> of Vattasseril, Indian Orthodox Saint (d. 1934)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_of_Vattasseril\" class=\"mw-redirect\" title=\"Saint <PERSON><PERSON> of Vattasseril\">Saint <PERSON> of Vattasseril</a>, Indian Orthodox Saint (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_of_Vattasseril\" class=\"mw-redirect\" title=\"Saint <PERSON> of Vattasseril\">Saint <PERSON> of Vattasseril</a>, Indian Orthodox Saint (d. 1934)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON> Mar <PERSON><PERSON> of Vattasseril", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_of_Vattasseril"}]}, {"year": "1860", "text": "<PERSON>, American scout leader, founded the Girl Scouts of the United States of America (d. 1927)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scout leader, founded the <a href=\"https://wikipedia.org/wiki/Girl_Scouts_of_the_United_States_of_America\" class=\"mw-redirect\" title=\"Girl Scouts of the United States of America\">Girl Scouts of the United States of America</a> (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scout leader, founded the <a href=\"https://wikipedia.org/wiki/Girl_Scouts_of_the_United_States_of_America\" class=\"mw-redirect\" title=\"Girl Scouts of the United States of America\">Girl Scouts of the United States of America</a> (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Girl Scouts of the United States of America", "link": "https://wikipedia.org/wiki/Girl_Scouts_of_the_United_States_of_America"}]}, {"year": "1860", "text": "<PERSON>, American politician (d. 1947)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>stead"}]}, {"year": "1868", "text": "<PERSON>, American journalist, and politician, 5th Governor of the Territory of Alaska (d. 1942)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Alaska\" class=\"mw-redirect\" title=\"List of Governors of Alaska\">Governor</a> of the <a href=\"https://wikipedia.org/wiki/Territory_of_Alaska\" title=\"Territory of Alaska\">Territory of Alaska</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Alaska\" class=\"mw-redirect\" title=\"List of Governors of Alaska\">Governor</a> of the <a href=\"https://wikipedia.org/wiki/Territory_of_Alaska\" title=\"Territory of Alaska\">Territory of Alaska</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Governors of Alaska", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Alaska"}, {"title": "Territory of Alaska", "link": "https://wikipedia.org/wiki/Territory_of_Alaska"}]}, {"year": "1875", "text": "<PERSON>, American businessman and publisher (d. 1954)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a>, American businessman and publisher (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a>, American businessman and publisher (d. 1954)", "links": [{"title": "<PERSON> (financier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian lawyer, freedom fighter and politician, 1st Deputy Prime Minister of India (d. 1950)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer, freedom fighter and politician, 1st <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer, freedom fighter and politician, 1st <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a> (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of India", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India"}]}, {"year": "1876", "text": "<PERSON>, American poet and playwright (d. 1972)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Czech actor, director, and composer (d. 1941)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech actor, director, and composer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech actor, director, and composer (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ler"}]}, {"year": "1880", "text": "<PERSON>, American author (d. 1961)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Soviet politician, member of the Politburo of the Central Committee of the Communist Party of the Soviet Union (d. 1936)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician, member of the <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Politburo of the Central Committee of the Communist Party of the Soviet Union\">Politburo of the Central Committee of the Communist Party of the Soviet Union</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician, member of the <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Politburo of the Central Committee of the Communist Party of the Soviet Union\">Politburo of the Central Committee of the Communist Party of the Soviet Union</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Politburo of the Central Committee of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese general (d. 1960)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Toshiz%C5%8D_Nishio\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toshiz%C5%8D_Nishio\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toshiz%C5%8D_Nishio"}]}, {"year": "1883", "text": "<PERSON>, French painter and illustrator (d. 1956)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, New Zealand tennis player, cricketer, and soldier (d. 1915)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand tennis player, cricketer, and soldier (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand tennis player, cricketer, and soldier (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Chinese general and politician, 1st President of the Republic of China (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Republic_of_China\" class=\"mw-redirect\" title=\"List of Presidents of the Republic of China\">President of the Republic of China</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Republic_of_China\" class=\"mw-redirect\" title=\"List of Presidents of the Republic of China\">President of the Republic of China</a> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "List of Presidents of the Republic of China", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Republic_of_China"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Canadian ice hockey player and lacrosse player (d. 1970)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Newsy_Lalonde\" title=\"Newsy <PERSON>e\"><PERSON><PERSON></a>, Canadian ice hockey player and lacrosse player (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Newsy_Lalonde\" title=\"Newsy <PERSON>onde\"><PERSON><PERSON></a>, Canadian ice hockey player and lacrosse player (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Newsy_Lalonde"}]}, {"year": "1888", "text": "<PERSON>, Greek poet and author (d. 1944)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek poet and author (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek poet and author (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Russian chess player and author (d. 1946)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and author (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and author (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON> <PERSON><PERSON>, English soldier, historian, and theorist (d. 1970)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English soldier, historian, and theorist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English soldier, historian, and theorist (d. 1970)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American singer and actress (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English author (d. 1999)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Constance Savery\"><PERSON></a>, English author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Constance Savery\"><PERSON></a>, English author (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Constance_Savery"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian sculptor (d. 1976)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Asbj%C3%B8<PERSON>_<PERSON>lt\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian sculptor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asbj%C3%B8<PERSON>_<PERSON>lt\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian sculptor (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asbj%C3%B8rg_Borgfelt"}]}, {"year": "1902", "text": "<PERSON>, Brazilian poet (d. 1987)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American blues singer-songwriter and pianist (d. 1958)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American blues singer-songwriter and pianist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American blues singer-songwriter and pianist (d. 1958)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1902", "text": "<PERSON>, Jewish-Hungarian mathematician and economist (d. 1950)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish-Hungarian mathematician and economist (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish-Hungarian mathematician and economist (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American musician and composer (d. 1973)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and composer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and composer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Canadian activist (d. 2009)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian activist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian activist (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American singer-songwriter and actress (d. 2001)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American animator and voice actor (d. 2008)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator and voice actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator and voice actor (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Dutch engineer and designer (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch engineer and designer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch engineer and designer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American pianist and composer (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "Count <PERSON> of Wisborg (d. 2012)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_of_Wisborg\" class=\"mw-redirect\" title=\"Count <PERSON> of Wisborg\">Count <PERSON> of Wisborg</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_of_Wisborg\" class=\"mw-redirect\" title=\"Count <PERSON> of Wisborg\">Count <PERSON> of Wisborg</a> (d. 2012)", "links": [{"title": "Count <PERSON> of Wisborg", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_of_Wisborg"}]}, {"year": "1917", "text": "<PERSON>, Canadian-American historian and author (d. 2016)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(historian)\" class=\"mw-redirect\" title=\"<PERSON> (historian)\"><PERSON></a>, Canadian-American historian and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(historian)\" class=\"mw-redirect\" title=\"<PERSON> (historian)\"><PERSON></a>, Canadian-American historian and author (d. 2016)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_(historian)"}]}, {"year": "1917", "text": "<PERSON>, Australian soldier and pilot (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and pilot (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and pilot (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American psychiatrist and academic (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English actress (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American mathematician and author (d. 2017)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Welsh-Caymanian jockey and author (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Caymanian jockey and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Caymanian jockey and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French priest and composer (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and composer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and composer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, German-Australian photographer (d. 2004)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian photographer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian photographer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, German footballer (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress (d. 2005)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American saxophonist and composer (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Illinois_Jacquet\" title=\"Illinois Jacquet\">Illinois Jacquet</a>, American saxophonist and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Illinois_Jacquet\" title=\"Illinois Jacquet\">Illinois Jacquet</a>, American saxophonist and composer (d. 2004)", "links": [{"title": "Illinois Jacquet", "link": "https://wikipedia.org/wiki/Illinois_J<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Cambodian politician, 1st Prime Minister of Cambodia (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cambodian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cambodia\" title=\"Prime Minister of Cambodia\">Prime Minister of Cambodia</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cambodian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cambodia\" title=\"Prime Minister of Cambodia\">Prime Minister of Cambodia</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norodom_<PERSON>"}, {"title": "Prime Minister of Cambodia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Cambodia"}]}, {"year": "1925", "text": "<PERSON>, American historian and author (d. 1990)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lawrence <PERSON>\"><PERSON></a>, American historian and author (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English-American chemist and academic, Nobel Prize laureate (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1925", "text": "<PERSON>, American colonel (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English radio and television host (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American critic and educator (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian water polo player and psychiatrist (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(water_polo)\" title=\"<PERSON> (water polo)\"><PERSON></a>, Australian water polo player and psychiatrist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(water_polo)\" title=\"<PERSON> (water polo)\"><PERSON></a>, Australian water polo player and psychiatrist (d. 2014)", "links": [{"title": "<PERSON> (water polo)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(water_polo)"}]}, {"year": "1929", "text": "<PERSON>, Italian swimmer, actor, and screenwriter (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian swimmer, actor, and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian swimmer, actor, and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American general, pilot, and astronaut (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American general, pilot, and astronaut (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American general, pilot, and astronaut (d. 2021)", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_(astronaut)"}]}, {"year": "1930", "text": "<PERSON>, American saxophonist (d. 1970)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American journalist", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Japanese voice actor (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actor (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "Princess <PERSON><PERSON>, Mrs. <PERSON>, Swedish princess", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>,_Mrs._<PERSON><PERSON>\" title=\"Princess <PERSON><PERSON>, Mrs. <PERSON><PERSON>\">Princess <PERSON><PERSON>, Mrs. <PERSON></a>, Swedish princess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>,_Mrs._<PERSON><PERSON>\" title=\"Princess <PERSON><PERSON>, Mrs. <PERSON><PERSON>\">Princess <PERSON><PERSON>, Mrs. <PERSON><PERSON></a>, Swedish princess", "links": [{"title": "Princess <PERSON><PERSON>, Mrs. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>,_Mrs._<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American basketball player and coach", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1935", "text": "<PERSON>, American mathematician and theorist (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English-American geographer and academic", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American geographer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American geographer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1991)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American folk music singer-songwriter and guitarist", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk music singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk music singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English actor and game show host (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English actor and game show host (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English actor and game show host (d. 2021)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(comedian)"}]}, {"year": "1939", "text": "<PERSON>, American actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Malian singer-songwriter and guitarist (d. 2006)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tour%C3%A9\" title=\"Ali <PERSON>ka Touré\"><PERSON></a>, Malian singer-songwriter and guitarist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"Ali Farka Touré\"><PERSON></a>, Malian singer-songwriter and guitarist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ali_Farka_Tour%C3%A9"}]}, {"year": "1940", "text": "<PERSON>, American businessman and activist, founded the Oscar Wilde Bookshop (d. 1993)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and activist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hop\" title=\"Oscar Wilde Bookshop\"><PERSON> Bookshop</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and activist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bookshop\" title=\"Oscar Wilde Bookshop\"><PERSON> Bookshop</a> (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oscar Wilde Bookshop", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bookshop"}]}, {"year": "1940", "text": "<PERSON>, Baroness <PERSON>, English businesswoman and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English businesswoman and politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American scientist (d. 1989)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English race car driver", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American basketball player (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American race car driver", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English-American physicist and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek politician, 13th Greek Minister for the Aegean and Island Policy (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, 13th <a href=\"https://wikipedia.org/wiki/Ministry_for_the_Aegean\" title=\"Ministry for the Aegean\">Greek Minister for the Aegean and Island Policy</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, 13th <a href=\"https://wikipedia.org/wiki/Ministry_for_the_Aegean\" title=\"Ministry for the Aegean\">Greek Minister for the Aegean and Island Policy</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aristotelis_<PERSON>lidis"}, {"title": "Ministry for the Aegean", "link": "https://wikipedia.org/wiki/Ministry_for_the_Aegean"}]}, {"year": "1943", "text": "<PERSON>, American football player (d. 1970)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>-<PERSON>, American actor and comedian", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, English playwright, screenwriter, and producer (d. 2019)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English playwright, screenwriter, and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English playwright, screenwriter, and producer (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Irish actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_Hall\" title=\"Deidre Hall\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>\" title=\"Deidre Hall\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American runner and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Belgian academic and politician, 66th Prime Minister of Belgium", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian academic and politician, 66th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian academic and politician, 66th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1948", "text": "<PERSON>, Italian actor (d. 1999)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Franco_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor (d. 1999)", "links": [{"title": "Franco Gasparri", "link": "https://wikipedia.org/wiki/Franco_Gasparri"}]}, {"year": "1948", "text": "<PERSON>, English actor and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Michael <PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Michael Kitchen\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Estonian journalist and diplomat", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American drummer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English economist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian actor, producer, and screenwriter (d. 1994)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Candy\"><PERSON></a>, Canadian actor, producer, and screenwriter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Candy\"><PERSON></a>, Canadian actor, producer, and screenwriter (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Iraqi-English architect and academic, designed the Bridge Pavilion (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi-English architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Bridge_Pavilion\" title=\"Bridge Pavilion\">Bridge Pavilion</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi-English architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Bridge_Pavilion\" title=\"Bridge Pavilion\">Bridge Pavilion</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Bridge Pavilion", "link": "https://wikipedia.org/wiki/Bridge_Pavilion"}]}, {"year": "1950", "text": "<PERSON>, American journalist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Filipino-American general", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American football player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American bass player, songwriter, and producer (d. 1996)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball umpire", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)\" title=\"<PERSON> (umpire)\"><PERSON></a>, American baseball umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)\" title=\"<PERSON> (umpire)\"><PERSON></a>, American baseball umpire", "links": [{"title": "<PERSON> (umpire)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)"}]}, {"year": "1953", "text": "<PERSON>, American basketball player and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Japanese actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mari_Okamo<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Greek lawyer and politician, Greek Minister of Public Order", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Public_Order_and_Citizen_Protection\" class=\"mw-redirect\" title=\"Ministry of Public Order and Citizen Protection\">Greek Minister of Public Order</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Public_Order_and_Citizen_Protection\" class=\"mw-redirect\" title=\"Ministry of Public Order and Citizen Protection\">Greek Minister of Public Order</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Public Order and Citizen Protection", "link": "https://wikipedia.org/wiki/Ministry_of_Public_Order_and_Citizen_Protection"}]}, {"year": "1955", "text": "<PERSON>, American journalist and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American poet and critic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Filipino actor, director, producer, and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor, director, producer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor, director, producer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Swedish lawyer and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" class=\"mw-redirect\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" class=\"mw-redirect\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(journalist)"}]}, {"year": "1957", "text": "<PERSON>, American singer and actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Mats_N%C3%A4slund\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mat<PERSON>_N%C3%A4slund\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mats_N%C3%A4slund"}]}, {"year": "1959", "text": "<PERSON>, American author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, French director, cinematographer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director, cinematographer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director, cinematographer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Puerto Rican lawyer and politician, 9th Governor of Puerto Rico", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_Puerto_Rico\" title=\"Governor of Puerto Rico\">Governor of Puerto Rico</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_Puerto_Rico\" title=\"Governor of Puerto Rico\">Governor of Puerto Rico</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Fortu%C3%B1o"}, {"title": "Governor of Puerto Rico", "link": "https://wikipedia.org/wiki/Governor_of_Puerto_Rico"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Crown Prince of Iran", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Crown_Prince_of_Iran\" title=\"<PERSON><PERSON>, Crown Prince of Iran\"><PERSON><PERSON>, Crown Prince of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Crown_Prince_of_Iran\" title=\"<PERSON><PERSON>, Crown Prince of Iran\"><PERSON><PERSON>, Crown Prince of Iran</a>", "links": [{"title": "<PERSON><PERSON>, Crown Prince of Iran", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Crown_Prince_of_Iran"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American runner and pilot", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON>_Babers\" title=\"Alonzo Babers\"><PERSON><PERSON><PERSON></a>, American runner and pilot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON>_Babers\" title=\"Alonzo Babers\"><PERSON><PERSON><PERSON></a>, American runner and pilot", "links": [{"title": "Alonzo Babers", "link": "https://wikipedia.org/wiki/Al<PERSON>zo_Babers"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, New Zealand actor, director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Jr., Irish musician, songwriter, and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Irish musician, songwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Irish musician, songwriter, and actor", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "1962", "text": "<PERSON>, American neurosurgeon and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurosurgeon and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurosurgeon and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American historian, author, and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Swedish journalist and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Brazilian guitarist and composer (d. 1995)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian guitarist and composer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian guitarist and composer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Swedish hard rock drummer and musician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish hard rock drummer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish hard rock drummer and musician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Brazilian footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Dunga\" title=\"Dunga\"><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dunga\" title=\"Du<PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "Dunga", "link": "https://wikipedia.org/wiki/Dunga"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor and comedian", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American journalist and critic", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Irish musician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Colm_%C3%93_C%C3%ADos%C3%B3ig\" title=\"Col<PERSON>\"><PERSON><PERSON></a>, Irish musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colm_%C3%93_C%C3%ADos%C3%B3ig\" title=\"Col<PERSON>\"><PERSON><PERSON></a>, Irish musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Colm_%C3%93_C%C3%ADos%C3%B3ig"}]}, {"year": "1964", "text": "<PERSON>, Dutch footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American country music singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, South African painter and sculptor (d. 2014)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African painter and sculptor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African painter and sculptor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Edwards\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Edwards\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Edwards"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Irish footballer and journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English voice actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>w\"><PERSON></a>, English voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>w"}]}, {"year": "1966", "text": "Ad-<PERSON>, American rapper, producer, and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ad-Rock\" title=\"Ad-Rock\">Ad-Rock</a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ad-Rock\" title=\"Ad-Rock\">Ad-Rock</a>, American rapper, producer, and actor", "links": [{"title": "Ad-Rock", "link": "https://wikipedia.org/wiki/Ad-Rock"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Anglo-Burmese singer-songwriter and record producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Anglo-Burmese singer-songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Anglo-Burmese singer-songwriter and record producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor and comedian", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American rapper, television personality, and real estate investor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Van<PERSON>_Ice\" title=\"Vanilla Ice\"><PERSON><PERSON></a>, American rapper, television personality, and real estate investor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vanilla_Ice\" title=\"Vanilla Ice\"><PERSON><PERSON></a>, American rapper, television personality, and real estate investor", "links": [{"title": "Vanilla Ice", "link": "https://wikipedia.org/wiki/Vanilla_Ice"}]}, {"year": "1967", "text": "<PERSON>, American race car driver", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Russian model and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American bass player, songwriter, and producer (d. 2020)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Swedish singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American basketball player (d. 2004)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Alphon<PERSON> Ford\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Alphon<PERSON> Ford\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1973", "text": "<PERSON>, American voice actor, director, producer, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, English-Turkish footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Mu<PERSON>_Izzet\" title=\"Muzzy Izzet\"><PERSON><PERSON> Izz<PERSON></a>, English-Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mu<PERSON>_Izzet\" title=\"Muzzy Izzet\"><PERSON><PERSON> Izzet</a>, English-Turkish footballer", "links": [{"title": "<PERSON>zzy Izzet", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Izzet"}]}, {"year": "1974", "text": "<PERSON>, Brazilian-American singer-songwriter and bass player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Swiss footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1975", "text": "<PERSON>, American mixed martial artist and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Spanish_footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Spanish footballer)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Spanish_footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Spanish footballer)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (Spanish footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Spanish_footballer)"}]}, {"year": "1976", "text": "<PERSON>, American actress and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Perabo\" title=\"Piper Perabo\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Perabo\" title=\"Piper Perabo\"><PERSON></a>, American actress and producer", "links": [{"title": "Piper Perabo", "link": "https://wikipedia.org/wiki/Piper_Perabo"}]}, {"year": "1978", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Inka_Grings\" title=\"Inka Grings\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inka_Grings\" title=\"Inka Grings\"><PERSON><PERSON> G<PERSON></a>, German footballer and manager", "links": [{"title": "Inka Grings", "link": "https://wikipedia.org/wiki/Inka_Grings"}]}, {"year": "1978", "text": "<PERSON>, Nigerian boxer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Polish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Dutch tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Jamaican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Sim%C3%A3o_Sabrosa\" title=\"Si<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim%C3%A3o_Sabrosa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%A3o_<PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American model, actress, and fashion designer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Armstrong\"><PERSON><PERSON></a>, American model, actress, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Armstrong\"><PERSON><PERSON></a>, American model, actress, and fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sam<PERSON>_Armstrong"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Mexican-American pianist and conductor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Alond<PERSON>_de_la_Parra\" title=\"Alond<PERSON> de la Parra\"><PERSON><PERSON><PERSON> <PERSON> la Parra</a>, Mexican-American pianist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON>_de_la_Parra\" title=\"Alondra de la Parra\"><PERSON><PERSON><PERSON> <PERSON> la Parra</a>, Mexican-American pianist and conductor", "links": [{"title": "Alondra de la Parra", "link": "https://wikipedia.org/wiki/Alond<PERSON>_de_la_<PERSON>rra"}]}, {"year": "1980", "text": "<PERSON>, Dutch footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor and voice artist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Russian author", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Taiwanese singer and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian footballer and umpire", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Jordan_Bannister\" title=\"Jordan Bannister\"><PERSON></a>, Australian footballer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Bannister\" title=\"Jordan Bannister\"><PERSON></a>, Australian footballer and umpire", "links": [{"title": "Jordan Bannister", "link": "https://wikipedia.org/wiki/Jordan_Bannister"}]}, {"year": "1982", "text": "<PERSON>, Canadian actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Plekanec\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Plekanec\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Plekanec"}]}, {"year": "1983", "text": "<PERSON>, American photographer and activist, founded the NOH8 Campaign", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and activist, founded the <a href=\"https://wikipedia.org/wiki/NOH8_Campaign\" title=\"NOH8 Campaign\">NOH8 Campaign</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and activist, founded the <a href=\"https://wikipedia.org/wiki/NOH8_Campaign\" title=\"NOH8 Campaign\">NOH8 Campaign</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NOH8 Campaign", "link": "https://wikipedia.org/wiki/NOH8_Campaign"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1984", "text": "<PERSON>, Australian swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, German alpine skier", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German alpine skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German alpine skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American hurdler and sprinter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler and sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler and sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian race car driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian actress and producer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, French race car driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss race car driver", "html": "1988 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, British skeleton racer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British skeleton racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British skeleton racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American rapper", "html": "1990 - <a href=\"https://wikipedia.org/wiki/JID\" title=\"JID\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/JID\" title=\"JID\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "JID", "link": "https://wikipedia.org/wiki/JID"}]}, {"year": "1992", "text": "<PERSON>, American actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian musician", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Mercedes_Arn-Horn\" title=\"Mercedes Arn-Horn\"><PERSON></a>, Canadian musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mercedes_Arn-Horn\" title=\"Mercedes Arn-Horn\"><PERSON></a>, Canadian musician", "links": [{"title": "Mercedes Arn-Horn", "link": "https://wikipedia.org/wiki/Mercedes_Arn-Horn"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Filipino actress and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"Na<PERSON> Lustre\"><PERSON><PERSON></a>, Filipino actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Nadine Lustre\"><PERSON><PERSON></a>, Filipino actress and singer", "links": [{"title": "Nadine <PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON>re"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Guy<PERSON>se-British actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON>-British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guyanese-British actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Portuguese tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Hong Kong-Irish swimmer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Siobh%C3%A1n_Bernadette_Haughey\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hong Kong-Irish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siobh%C3%A1n_Bernadette_Haughey\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hong Kong-Irish swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Siobh%C3%A1n_Bernadette_<PERSON>ey"}]}, {"year": "1997", "text": "<PERSON>, American actress and comedian", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Sydney_Park_(actress)\" title=\"Sydney Park (actress)\"><PERSON> Park</a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Park_(actress)\" title=\"Sydney Park (actress)\"><PERSON> Park</a>, American actress and comedian", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/Sydney_Park_(actress)"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian-American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, French figure skater", "html": "1999 - <a href=\"https://wikipedia.org/wiki/L%C3%A9a_Serna\" title=\"Lé<PERSON>\"><PERSON><PERSON><PERSON></a>, French figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9a_Serna\" title=\"Léa <PERSON>\"><PERSON><PERSON><PERSON></a>, French figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9a_Serna"}]}, {"year": "2000", "text": "<PERSON>, American singer, actress, and dancer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actress, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actress, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Princess of Asturias", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_of_Asturias\" title=\"<PERSON><PERSON>, Princess of Asturias\"><PERSON><PERSON>, Princess of Asturias</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_of_Asturias\" title=\"<PERSON><PERSON>, Princess of Asturias\"><PERSON><PERSON>, Princess of Asturias</a>", "links": [{"title": "<PERSON><PERSON>, Princess of Asturias", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_of_Asturias"}]}], "Deaths": [{"year": "932", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph (b. 895)", "html": "932 - <a href=\"https://wikipedia.org/wiki/Al-Muqtadir\" title=\"Al-Muqtadir\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Muqtadir\" title=\"Al-Muqtadir\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 895)", "links": [{"title": "Al-Muqtadir", "link": "https://wikipedia.org/wiki/Al-Muqtadir"}]}, {"year": "994", "text": "<PERSON> of Regensburg, German bishop and saint (b. 934)", "html": "994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Regensburg\" title=\"<PERSON> of Regensburg\"><PERSON> of Regensburg</a>, German bishop and saint (b. 934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Regensburg\" title=\"<PERSON> of Regensburg\"><PERSON> of Regensburg</a>, German bishop and saint (b. 934)", "links": [{"title": "<PERSON> of Regensburg", "link": "https://wikipedia.org/wiki/Wolfgang_<PERSON>_Regensburg"}]}, {"year": "1005", "text": "<PERSON>, Japanese astrologer (b. 921)", "html": "1005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Abe no Seimei\"><PERSON></a>, Japanese astrologer (b. 921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Abe no Seimei\"><PERSON></a>, Japanese astrologer (b. 921)", "links": [{"title": "<PERSON> no <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>i"}]}, {"year": "1034", "text": "<PERSON><PERSON><PERSON>, Korean ruler (b. 1016)", "html": "1034 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON></a>, Korean ruler (b. 1016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON></a>, Korean ruler (b. 1016)", "links": [{"title": "<PERSON><PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/De<PERSON>jong_of_Goryeo"}]}, {"year": "1147", "text": "<PERSON>, 1st Earl of Gloucester, son of <PERSON> of England (b. 1100)", "html": "1147 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Gloucester\" title=\"<PERSON>, 1st Earl of Gloucester\"><PERSON>, 1st Earl of Gloucester</a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a> (b. 1100)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Gloucester\" title=\"<PERSON>, 1st Earl of Gloucester\"><PERSON>, 1st Earl of Gloucester</a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a> (b. 1100)", "links": [{"title": "<PERSON>, 1st Earl of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Gloucester"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1214", "text": "<PERSON> of England, queen consort of Castile (b. 1163)", "html": "1214 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_England,_Queen_of_Castile\" title=\"<PERSON> of England, Queen of Castile\"><PERSON> of England</a>, queen consort of Castile (b. 1163)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_England,_Queen_of_Castile\" title=\"<PERSON> of England, Queen of Castile\"><PERSON> of England</a>, queen consort of Castile (b. 1163)", "links": [{"title": "<PERSON> of England, Queen of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_England,_Queen_of_Castile"}]}, {"year": "1320", "text": "<PERSON><PERSON> of Monte Croce, Italian Dominican missionary (b. 1242)", "html": "1320 - <a href=\"https://wikipedia.org/wiki/<PERSON>ld_of_Monte_Croce\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Monte Croce\"><PERSON><PERSON> of Monte Croce</a>, Italian Dominican missionary (b. 1242)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Monte_Croce\" class=\"mw-redirect\" title=\"Ricold of Monte Croce\"><PERSON><PERSON> of Monte Croce</a>, Italian Dominican missionary (b. 1242)", "links": [{"title": "<PERSON>ld of Monte Croce", "link": "https://wikipedia.org/wiki/Ricold_of_Monte_C<PERSON>"}]}, {"year": "1335", "text": "<PERSON> Évreux, Duchess Consort of Brabant (b. 1303)", "html": "1335 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89vreux\" title=\"<PERSON> of Évreux\"><PERSON> Évreux</a>, Duchess <PERSON> of Brabant (b. 1303)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89vreux\" title=\"<PERSON> of Évreux\"><PERSON> Évreux</a>, Duchess <PERSON> of Brabant (b. 1303)", "links": [{"title": "<PERSON> of Évreux", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89vreux"}]}, {"year": "1448", "text": "<PERSON>, Byzantine emperor (b. 1390)", "html": "1448 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1390)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> VIII Palaiologos\"><PERSON></a>, Byzantine emperor (b. 1390)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1517", "text": "<PERSON><PERSON>, Italian artist (b. 1472)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian artist (b. 1472)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian artist (b. 1472)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1589", "text": "<PERSON>, German farmer and alleged serial killer (b. 1535)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German farmer and alleged serial killer (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German farmer and alleged serial killer (b. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1641", "text": "<PERSON><PERSON><PERSON>, Dutch admiral (b. 1597)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Corne<PERSON> Jol\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Corne<PERSON> Jol\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1597)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Jol"}]}, {"year": "1659", "text": "<PERSON>, English lawyer and judge, Chancellor of the Duchy of Lancaster (b. 1602)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1602)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1661", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ottoman politician, 109th Grand Vizier of the Ottoman Empire (b. 1575)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman politician, 109th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman politician, 109th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1575)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1723", "text": "<PERSON><PERSON><PERSON>, Grand Duke of Tuscany (b. 1642)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON><PERSON></a>, Grand Duke of Tuscany (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON><PERSON></a>, Grand Duke of Tuscany (b. 1642)", "links": [{"title": "<PERSON><PERSON><PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON>si<PERSON>_III_de%27_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1732", "text": "<PERSON>, Duke of Savoy (b. 1666)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON></a>, Duke of Savoy (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON></a>, Duke of Savoy (b. 1666)", "links": [{"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ade<PERSON>_II_of_Sardinia"}]}, {"year": "1733", "text": "<PERSON><PERSON><PERSON>, Duke of Württemberg (b. 1676)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON><PERSON>, Duke of Württemberg\"><PERSON><PERSON><PERSON></a>, Duke of Württemberg (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON><PERSON>, Duke of Württemberg\"><PERSON><PERSON><PERSON></a>, Duke of Württemberg (b. 1676)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg"}]}, {"year": "1744", "text": "<PERSON>, Italian composer (b. 1694)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Leo\"><PERSON></a>, Italian composer (b. 1694)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, Italian violinist and composer (b. 1690)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1690)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "Princess <PERSON> of Great Britain (b. 1711)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Great_Britain\" title=\"Princess <PERSON> of Great Britain\">Princess <PERSON> of Great Britain</a> (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Great_Britain\" title=\"Princess <PERSON> of Great Britain\">Princess <PERSON> of Great Britain</a> (b. 1711)", "links": [{"title": "Princess <PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/Princess_Amelia_of_Great_Britain"}]}, {"year": "1806", "text": "<PERSON><PERSON><PERSON>, Japanese artist and printmaker (b. ca. 1753)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Utamaro\" title=\"Utamaro\"><PERSON><PERSON><PERSON></a>, Japanese artist and printmaker (b. ca. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Utamaro\" title=\"Utamaro\"><PERSON><PERSON><PERSON></a>, Japanese artist and printmaker (b. ca. 1753)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Utamaro"}]}, {"year": "1860", "text": "<PERSON>, 10th Earl of Dundonald, Scottish-English admiral and politician (b. 1775)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald\" title=\"<PERSON>, 10th Earl of Dundonald\"><PERSON>, 10th Earl of Dundonald</a>, Scottish-English admiral and politician (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald\" title=\"<PERSON>, 10th Earl of Dundonald\"><PERSON>, 10th Earl of Dundonald</a>, Scottish-English admiral and politician (b. 1775)", "links": [{"title": "<PERSON>, 10th Earl of Dundonald", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald"}]}, {"year": "1869", "text": "<PERSON>, American politician, 14th Governor of Kentucky (b. 1788)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Kentucky", "link": "https://wikipedia.org/wiki/Governor_of_Kentucky"}]}, {"year": "1879", "text": "<PERSON>, American author and academic (b. 1803)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American general (b. 1814)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Ukrainian-Russian painter and sculptor (b. 1858)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian painter and sculptor (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian painter and sculptor (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Irish-Australian politician, 13th Premier of Victoria (b. 1828)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Loghlen\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Loghlen\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Loghlen"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1913", "text": "<PERSON><PERSON>, English soldier and politician (b. 1857)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American minister (b. 1852)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Chinese revolutionary leader and statesman (b. 1874)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese revolutionary leader and statesman (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese revolutionary leader and statesman (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Austrian painter (b. 1890)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian painter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian painter (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Canadian businessman (b. 1854)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(co-operator)\" title=\"<PERSON><PERSON><PERSON> (co-operator)\"><PERSON><PERSON><PERSON></a>, Canadian businessman (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(co-operator)\" title=\"<PERSON><PERSON><PERSON> (co-operator)\"><PERSON><PERSON><PERSON></a>, Canadian businessman (b. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON> (co-operator)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(co-operator)"}]}, {"year": "1925", "text": "<PERSON>, French actor, director, and screenwriter (b. 1883)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Linder\"><PERSON></a>, French actor, director, and screenwriter (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Bolshevik leader during and just prior to the Russian Revolution of 1917 (b. 1885)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> leader during and just prior to the <a href=\"https://wikipedia.org/wiki/Russian_Revolution_of_1917\" class=\"mw-redirect\" title=\"Russian Revolution of 1917\">Russian Revolution of 1917</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> leader during and just prior to the <a href=\"https://wikipedia.org/wiki/Russian_Revolution_of_1917\" class=\"mw-redirect\" title=\"Russian Revolution of 1917\">Russian Revolution of 1917</a> (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bolshevik", "link": "https://wikipedia.org/wiki/Bolshevik"}, {"title": "Russian Revolution of 1917", "link": "https://wikipedia.org/wiki/Russian_Revolution_of_1917"}]}, {"year": "1926", "text": "<PERSON>, American magician and stuntman (b. 1874)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and stuntman (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and stuntman (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese physician and politician, 6th President of Portugal (b. 1866)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Jos%C3%A9_de_Almeida\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese physician and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Portugal\" class=\"mw-redirect\" title=\"List of Presidents of Portugal\">President of Portugal</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Jos%C3%A9_de_Almeida\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese physician and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Portugal\" class=\"mw-redirect\" title=\"List of Presidents of Portugal\">President of Portugal</a> (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_Jos%C3%A9_de_Almeida"}, {"title": "List of Presidents of Portugal", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Portugal"}]}, {"year": "1931", "text": "<PERSON><PERSON>, French journalist and author (b. 1851)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Octave_<PERSON><PERSON><PERSON>\" title=\"Octave Uzanne\">Octave <PERSON><PERSON><PERSON></a>, French journalist and author (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octave_<PERSON><PERSON><PERSON>\" title=\"Octave Uzanne\">Octave <PERSON><PERSON><PERSON></a>, French journalist and author (b. 1851)", "links": [{"title": "Octave U<PERSON>ne", "link": "https://wikipedia.org/wiki/Octave_<PERSON><PERSON>ne"}]}, {"year": "1939", "text": "<PERSON>, Austrian psychologist, author, and educator (b. 1884)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Otto Rank\"><PERSON></a>, Austrian psychologist, author, and educator (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Otto_<PERSON>\" title=\"Otto Rank\"><PERSON></a>, Austrian psychologist, author, and educator (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rank"}]}, {"year": "1944", "text": "<PERSON>, British botanist (b. 1883)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British botanist (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British botanist (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Burmese lawyer and politician (b. 1879)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>t_<PERSON>laing\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese lawyer and politician (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>laing\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese lawyer and politician (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>laing"}]}, {"year": "1959", "text": "<PERSON>, French physicist and academic (b. 1885)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and poet (b. 1894)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and poet (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and poet (b. 1894)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French astronomer (b. 1877)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lammarion"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Turkish cellist and composer (b. 1902)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Mesut_Cemil\" title=\"Mesut Cemil\"><PERSON><PERSON><PERSON></a>, Turkish cellist and composer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mesut_Cemil\" title=\"Mesut Cemil\"><PERSON><PERSON><PERSON></a>, Turkish cellist and composer (b. 1902)", "links": [{"title": "Me<PERSON>t <PERSON>l", "link": "https://wikipedia.org/wiki/Mesut_Cemil"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Finnish politician (b. 1881)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1916)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Algerian philosopher and author (b. 1905)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian philosopher and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian philosopher and author (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Indian composer and singer (b. 1906)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian composer and singer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian composer and singer (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Dev_<PERSON>n"}]}, {"year": "1977", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and illustrator (b. 1904)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C. B. Colby\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C. B<PERSON> Colby\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (b. 1904)", "links": [{"title": "C. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Czech actor and playwright (b. 1905)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech actor and playwright (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech actor and playwright (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player and coach (b. 1895)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Chinese self-taught mathematician (b. 1935)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Chinese self-taught mathematician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Chinese self-taught mathematician (b. 1935)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(mathematician)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Uzbek politician, CPSU Politburo candidate member (b. 1917)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uzbek politician, <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Politburo of the Communist Party of the Soviet Union\">CPSU Politburo</a> candidate member (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uzbek politician, <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Politburo of the Communist Party of the Soviet Union\">CPSU Politburo</a> candidate member (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Politburo of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union"}]}, {"year": "1984", "text": "<PERSON>, Italian actor, director, and screenwriter (b. 1900)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Indian politician, Prime Minister of India (b. 1917)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indira_Gandhi"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Greek painter and poet (b. 1907)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and poet (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and poet (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Danish actor and singer (b. 1913)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor and singer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor and singer (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American physicist and chemist, Nobel Prize laureate (b. 1896)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1988", "text": "<PERSON>, Romanian-born American actor, producer, and screenwriter (b. 1902)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-born American actor, producer, and screenwriter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-born American actor, producer, and screenwriter (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian painter and academic (b. 1906)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American stage director and producer (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage director and producer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage director and producer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English ice hockey player (b. 1974)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ice hockey player (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ice hockey player (b. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Italian director and screenwriter (b. 1920)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American actor and singer (b. 1970)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/River_Phoenix\" title=\"River Phoenix\">River Phoenix</a>, American actor and singer (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/River_Phoenix\" title=\"River Phoenix\">River Phoenix</a>, American actor and singer (b. 1970)", "links": [{"title": "River Phoenix", "link": "https://wikipedia.org/wiki/River_Phoenix"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American actress and singer (b. 1938)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cash\"><PERSON><PERSON></a>, American actress and singer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cash\"><PERSON><PERSON></a>, American actress and singer (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, French director and screenwriter (b. 1906)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Carn%C3%A9"}]}, {"year": "1997", "text": "<PERSON>, American gymnast (b. 1911)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian ice hockey player (b. 1935)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON> Purís<PERSON>, Spanish nun and saint (Roman Catholic Church) (b. 1926)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Mar%C3%AD<PERSON>_de_la_Pur%C3%ADsi<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> de la Purís<PERSON>\"><PERSON></a>, Spanish nun and saint (<a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholic Church</a>) (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%AD<PERSON>_de_la_Pur%C3%ADsi<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> de la Purís<PERSON>\"><PERSON>ís<PERSON></a>, Spanish nun and saint (<a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholic Church</a>) (b. 1926)", "links": [{"title": "<PERSON> de la Purísima <PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_de_la_Pur%C3%ADsima_Salvat_Romero"}, {"title": "Roman Catholic Church", "link": "https://wikipedia.org/wiki/Roman_Catholic_Church"}]}, {"year": "1999", "text": "<PERSON>, Canadian race car driver (b. 1975)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Canadian race car driver (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Canadian race car driver (b. 1975)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "2000", "text": "<PERSON>, Jr., American journalist and screenwriter (b. 1915)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and screenwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and screenwriter (b. 1915)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2000", "text": "<PERSON><PERSON>, Japanese songwriter and guitarist (b. 1981)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese songwriter and guitarist (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese songwriter and guitarist (b. 1981)", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, French skier (b. 1970)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/R%C3%A9gine_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French skier (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9gine_<PERSON>ud\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French skier (b. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9gine_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, French banker and businessman (b. 1945)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2ne\" title=\"<PERSON>\"><PERSON></a>, French banker and businessman (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2ne\" title=\"<PERSON>\"><PERSON></a>, French banker and businessman (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lionel_Poil%C3%A2ne"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Greek jurist and politician, President of Greece (b. 1903)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">President of Greece</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">President of Greece</a> (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Greece", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Italian footballer and actor (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and actor (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American political scientist and historian (b. 1919)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and historian (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and historian (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American biophysicist and engineer (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hal Anger\"><PERSON></a>, American biophysicist and engineer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hal Anger\"><PERSON></a>, American biophysicist and engineer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Indian author and poet (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Amrita_Pritam\" title=\"Amrita Pritam\"><PERSON><PERSON>rita<PERSON></a>, Indian author and poet (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amrita_Pritam\" title=\"Amrita Pritam\"><PERSON><PERSON>ritam</a>, Indian author and poet (b. 1919)", "links": [{"title": "Amrita Pritam", "link": "https://wikipedia.org/wiki/Amrita_Pritam"}]}, {"year": "2006", "text": "<PERSON><PERSON> <PERSON><PERSON>, South African soldier and politician, State President of South Africa (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Both<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Both<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African soldier and politician, <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Both<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Both<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African soldier and politician, <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}]}, {"year": "2006", "text": "<PERSON>, English journalist and author (b. 1927)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Turkish physicist and politician, Prime Minister of Turkey (b. 1926)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Erdal_%C4%B0n%C3%B6n%C3%BC\" title=\"<PERSON><PERSON><PERSON>nönü\"><PERSON><PERSON><PERSON></a>, Turkish physicist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erdal_%C4%B0n%C3%B6n%C3%BC\" title=\"<PERSON><PERSON><PERSON>nönü\"><PERSON><PERSON><PERSON></a>, Turkish physicist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erdal_%C4%B0n%C3%B6n%C3%BC"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American historian and author (b. 1912)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Studs_Terkel\" title=\"Studs Terkel\"><PERSON><PERSON> Terkel</a>, American historian and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Studs_Terkel\" title=\"<PERSON>uds Terkel\"><PERSON><PERSON> Terkel</a>, American historian and author (b. 1912)", "links": [{"title": "Studs Terkel", "link": "https://wikipedia.org/wiki/Studs_Terkel"}]}, {"year": "2009", "text": "<PERSON>, Egyptian physician and author (b. 1921)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Egyptian physician and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Egyptian physician and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English businessman, founded the Donington Grand Prix Exhibition (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/Donington_Grand_Prix_Exhibition\" class=\"mw-redirect\" title=\"Donington Grand Prix Exhibition\">Donington Grand Prix Exhibition</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/Donington_Grand_Prix_Exhibition\" class=\"mw-redirect\" title=\"Donington Grand Prix Exhibition\">Donington Grand Prix Exhibition</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Donington Grand Prix Exhibition", "link": "https://wikipedia.org/wiki/Donington_Grand_Prix_Exhibition"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Chinese aerodynamicist and academic (b. 1911)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese aerodynamicist and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese aerodynamicist and academic (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American lawyer, 8th White House Counsel (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, 8th <a href=\"https://wikipedia.org/wiki/White_House_Counsel\" title=\"White House Counsel\">White House Counsel</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, 8th <a href=\"https://wikipedia.org/wiki/White_House_Counsel\" title=\"White House Counsel\">White House Counsel</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Counsel", "link": "https://wikipedia.org/wiki/White_House_Counsel"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian footballer and manager (b. 1941)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Fl%C3%B3ri%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fl%C3%B3ri%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fl%C3%B3ri%C3%A1n_Albert"}]}, {"year": "2011", "text": "<PERSON>, Italian race car driver (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>,  Italian architect and designer (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian architect and designer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian architect and designer (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ae_Aulenti"}]}, {"year": "2012", "text": "<PERSON>, American race car driver and engineer (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver and engineer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver and engineer (b. 1917)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2012", "text": "<PERSON>, American soldier and politician, 67th Governor of Maine (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 67th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 67th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Maine", "link": "https://wikipedia.org/wiki/Governor_of_Maine"}]}, {"year": "2013", "text": "<PERSON> (aka <PERSON>), American actress and author (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (aka <PERSON>), American actress and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Chase\"><PERSON></a> (aka <PERSON>), American actress and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, French journalist and author (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist and author (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English chemist and author (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Filipino lawyer and jurist, 19th Chief Justice of the Supreme Court of the Philippines (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and jurist, 19th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and jurist, 19th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Justice of the Supreme Court of the Philippines", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)"}]}, {"year": "2014", "text": "<PERSON>, American commander and diplomat, United States Permanent Representative to NATO (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Permanent_Representative_to_NATO\" class=\"mw-redirect\" title=\"United States Permanent Representative to NATO\">United States Permanent Representative to NATO</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Permanent_Representative_to_NATO\" class=\"mw-redirect\" title=\"United States Permanent Representative to NATO\">United States Permanent Representative to NATO</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Permanent Representative to NATO", "link": "https://wikipedia.org/wiki/United_States_Permanent_Representative_to_NATO"}]}, {"year": "2014", "text": "<PERSON>, American engineer and pilot (b. 1975)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and pilot (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and pilot (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1981)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Japanese educator and politician (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese educator and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese educator and politician (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tos<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American businessman and politician (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Savage\"><PERSON></a>, American businessman and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American baseball player (b. 1938)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Scottish actor (b. 1930)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, British-American rapper and record producer (b. 1971)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/MF_<PERSON>\" title=\"MF Doom\"><PERSON><PERSON> <PERSON></a>, British-American rapper and record producer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>F_<PERSON>\" title=\"MF Doom\"><PERSON><PERSON> <PERSON></a>, British-American rapper and record producer (b. 1971)", "links": [{"title": "MF Doom", "link": "https://wikipedia.org/wiki/MF_Doom"}]}, {"year": "2021", "text": "<PERSON>, Australian cricketer (b. 1934)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American astronaut (b. 1936)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}