{"date": "September 18", "url": "https://wikipedia.org/wiki/September_18", "data": {"Events": [{"year": "96", "text": "Emperor <PERSON><PERSON><PERSON> is assassinated as a result of a plot by his wife <PERSON><PERSON><PERSON> and two Praetorian prefects. <PERSON><PERSON><PERSON> is then proclaimed as his successor.", "html": "96 - Emperor <a href=\"https://wikipedia.org/wiki/Domitian\" title=\"Domitian\">Dom<PERSON><PERSON></a> is assassinated as a result of a plot by his wife <PERSON><PERSON><PERSON> and two Praetorian prefects. <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\"><PERSON><PERSON><PERSON></a> is then proclaimed as his successor.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Domitian\" title=\"Domitian\">Dom<PERSON><PERSON></a> is assassinated as a result of a plot by his wife <PERSON><PERSON><PERSON> and two Praetorian prefects. <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\"><PERSON><PERSON><PERSON></a> is then proclaimed as his successor.", "links": [{"title": "Domitian", "link": "https://wikipedia.org/wiki/Domitian"}, {"title": "Nerva", "link": "https://wikipedia.org/wiki/Nerva"}]}, {"year": "324", "text": "<PERSON> the Great decisively defeats <PERSON><PERSON><PERSON> in the Battle of Chrysopolis, establishing <PERSON>'s sole control over the Roman Empire.", "html": "324 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> decisively defeats <PERSON><PERSON><PERSON> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Chrysopolis\" title=\"Battle of Chrysopolis\">Battle of Chrysopolis</a>, establishing <PERSON>'s sole control over the Roman Empire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> decisively defeats <PERSON><PERSON><PERSON> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Chrysopolis\" title=\"Battle of Chrysopolis\">Battle of Chrysopolis</a>, establishing <PERSON>'s sole control over the Roman Empire.", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Battle of Chrysopolis", "link": "https://wikipedia.org/wiki/Battle_of_Chrysopolis"}]}, {"year": "1048", "text": "Battle of Kapetron between a combined Byzantine-Georgian army and a Seljuq army.", "html": "1048 - <a href=\"https://wikipedia.org/wiki/Battle_of_Kapetron\" title=\"Battle of Kapetron\">Battle of Kapetron</a> between a combined Byzantine-Georgian army and a <a href=\"https://wikipedia.org/wiki/Seljuq_Empire\" class=\"mw-redirect\" title=\"Seljuq Empire\">Seljuq</a> army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Kapetron\" title=\"Battle of Kapetron\">Battle of Kapetron</a> between a combined Byzantine-Georgian army and a <a href=\"https://wikipedia.org/wiki/Seljuq_Empire\" class=\"mw-redirect\" title=\"Seljuq Empire\">Seljuq</a> army.", "links": [{"title": "Battle of Kapetron", "link": "https://wikipedia.org/wiki/Battle_of_Kapetron"}, {"title": "Seljuq Empire", "link": "https://wikipedia.org/wiki/Seljuq_Empire"}]}, {"year": "1066", "text": "Norwegian king <PERSON> lands with <PERSON><PERSON><PERSON> at the mouth of the Humber River and begins his invasion of England.", "html": "1066 - Norwegian king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> at the mouth of the Humber River and begins his invasion of England.", "no_year_html": "Norwegian king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> at the mouth of the Humber River and begins his invasion of England.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1180", "text": "<PERSON> becomes king of France at the age of fifteen.", "html": "1180 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes king of France at the age of fifteen.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes king of France at the age of fifteen.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1454", "text": "Thirteen Years' War: In the Battle of Chojnice, the Polish army is defeated by the Teutonic knights.", "html": "1454 - <a href=\"https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%931466)\" title=\"Thirteen Years' War (1454-1466)\">Thirteen Years' War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Chojnice_(1454)\" title=\"Battle of Chojnice (1454)\">Battle of Chojnice</a>, the Polish army is defeated by the <a href=\"https://wikipedia.org/wiki/Teutonic_Order\" title=\"Teutonic Order\">Teutonic knights</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%931466)\" title=\"Thirteen Years' War (1454-1466)\">Thirteen Years' War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Chojnice_(1454)\" title=\"Battle of Chojnice (1454)\">Battle of Chojnice</a>, the Polish army is defeated by the <a href=\"https://wikipedia.org/wiki/Teutonic_Order\" title=\"Teutonic Order\">Teutonic knights</a>.", "links": [{"title": "Thirteen Years' War (1454-1466)", "link": "https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%931466)"}, {"title": "Battle of Chojnice (1454)", "link": "https://wikipedia.org/wiki/Battle_of_Chojnice_(1454)"}, {"title": "Teutonic Order", "link": "https://wikipedia.org/wiki/Teutonic_Order"}]}, {"year": "1544", "text": "The expedition of <PERSON> makes landfall in San Pedro Bay, southern Chile, claiming the territory for Spain.", "html": "1544 - The expedition of <a href=\"https://wikipedia.org/wiki/Juan_Bauti<PERSON>_Pastene\" title=\"Juan Bautista Pastene\"><PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/San_Pedro_Bay_(Chile)\" title=\"San Pedro Bay (Chile)\">San Pedro Bay</a>, <a href=\"https://wikipedia.org/wiki/Zona_Sur\" title=\"Zona Sur\">southern Chile</a>, claiming the territory for Spain.", "no_year_html": "The expedition of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ene\" title=\"Juan Ba<PERSON>ene\"><PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/San_Pedro_Bay_(Chile)\" title=\"San Pedro Bay (Chile)\">San Pedro Bay</a>, <a href=\"https://wikipedia.org/wiki/Zona_Sur\" title=\"Zona Sur\">southern Chile</a>, claiming the territory for Spain.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "San Pedro Bay (Chile)", "link": "https://wikipedia.org/wiki/San_Pedro_Bay_(Chile)"}, {"title": "Zona Sur", "link": "https://wikipedia.org/wiki/Zona_Sur"}]}, {"year": "1618", "text": "The twelfth baktun in the Mesoamerican Long Count calendar begins.", "html": "1618 - The twelfth <a href=\"https://wikipedia.org/wiki/Baktun\" title=\"Baktun\">baktun</a> in the Mesoamerican Long Count calendar begins.", "no_year_html": "The twelfth <a href=\"https://wikipedia.org/wiki/Baktun\" title=\"Baktun\">baktun</a> in the Mesoamerican Long Count calendar begins.", "links": [{"title": "Baktun", "link": "https://wikipedia.org/wiki/Baktun"}]}, {"year": "1714", "text": "<PERSON> arrives in Great Britain after becoming king on August 1.", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> I of Great Britain\"><PERSON> I</a> arrives in Great Britain after becoming king on August 1.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> of Great Britain\"><PERSON> I</a> arrives in Great Britain after becoming king on August 1.", "links": [{"title": "<PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain"}]}, {"year": "1739", "text": "The Treaty of Belgrade is signed, whereby Austria cedes lands south of the Sava and Danube rivers to the Ottoman Empire.", "html": "1739 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Belgrade\" title=\"Treaty of Belgrade\">Treaty of Belgrade</a> is signed, whereby Austria cedes lands south of the Sava and Danube rivers to the Ottoman Empire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Belgrade\" title=\"Treaty of Belgrade\">Treaty of Belgrade</a> is signed, whereby Austria cedes lands south of the Sava and Danube rivers to the Ottoman Empire.", "links": [{"title": "Treaty of Belgrade", "link": "https://wikipedia.org/wiki/Treaty_of_Belgrade"}]}, {"year": "1759", "text": "French and Indian War: The Articles of Capitulation of Quebec are signed.", "html": "1759 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Articles_of_Capitulation_of_Quebec\" title=\"Articles of Capitulation of Quebec\">Articles of Capitulation of Quebec</a> are signed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Articles_of_Capitulation_of_Quebec\" title=\"Articles of Capitulation of Quebec\">Articles of Capitulation of Quebec</a> are signed.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "Articles of Capitulation of Quebec", "link": "https://wikipedia.org/wiki/Articles_of_Capitulation_of_Quebec"}]}, {"year": "1793", "text": "The first cornerstone of the United States Capitol is laid by <PERSON>.", "html": "1793 - The first cornerstone of the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> is laid by <PERSON>.", "no_year_html": "The first cornerstone of the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> is laid by <PERSON>.", "links": [{"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}]}, {"year": "1809", "text": "The Royal Opera House in London opens.", "html": "1809 - The <a href=\"https://wikipedia.org/wiki/Royal_Opera_House\" title=\"Royal Opera House\">Royal Opera House</a> in London opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Opera_House\" title=\"Royal Opera House\">Royal Opera House</a> in London opens.", "links": [{"title": "Royal Opera House", "link": "https://wikipedia.org/wiki/Royal_Opera_House"}]}, {"year": "1810", "text": "First Government Junta in Chile. Though supposed to rule only during the Peninsular War in Spain, it is in fact the first step towards independence from Spain, and is commemorated as such.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Government_Junta_of_Chile_(1810)\" title=\"Government Junta of Chile (1810)\">First Government Junta</a> in Chile. Though supposed to rule only during the <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a> in Spain, it is in fact the first step towards independence from Spain, and is commemorated as such.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Government_Junta_of_Chile_(1810)\" title=\"Government Junta of Chile (1810)\">First Government Junta</a> in Chile. Though supposed to rule only during the <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a> in Spain, it is in fact the first step towards independence from Spain, and is commemorated as such.", "links": [{"title": "Government Junta of Chile (1810)", "link": "https://wikipedia.org/wiki/Government_Junta_of_Chile_(1810)"}, {"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}]}, {"year": "1812", "text": "The 1812 Fire of Moscow dies down after destroying more than three-quarters of the city. <PERSON> returns from the Petrovsky Palace to the Moscow Kremlin, spared from the fire.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Fire_of_Moscow_(1812)\" title=\"Fire of Moscow (1812)\">The 1812 Fire of Moscow</a> dies down after destroying more than three-quarters of the city. <PERSON> returns from the <a href=\"https://wikipedia.org/wiki/Petrovsky_Palace\" title=\"Petrovsky Palace\">Petrovsky Palace</a> to the Moscow Kremlin, spared from the fire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fire_of_Moscow_(1812)\" title=\"Fire of Moscow (1812)\">The 1812 Fire of Moscow</a> dies down after destroying more than three-quarters of the city. <PERSON> returns from the <a href=\"https://wikipedia.org/wiki/Petrovsky_Palace\" title=\"Petrovsky Palace\">Petrovsky Palace</a> to the Moscow Kremlin, spared from the fire.", "links": [{"title": "Fire of Moscow (1812)", "link": "https://wikipedia.org/wiki/Fire_of_Moscow_(1812)"}, {"title": "Petrovsky Palace", "link": "https://wikipedia.org/wiki/Petrovsky_Palace"}]}, {"year": "1837", "text": "Tiffany & Co. (first named Tiffany & Young) is founded by <PERSON> and <PERSON> in New York City. The store is called a \"stationery and fancy goods emporium\".", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Tiffany_%26_Co.\" title=\"Tiffany &amp; Co.\">Tiffany &amp; Co.</a> (first named Tiffany &amp; Young) is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <PERSON> in New York City. The store is called a \"stationery and fancy goods emporium\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiffany_%26_Co.\" title=\"Tiffany &amp; Co.\">Tiffany &amp; Co.</a> (first named Tiffany &amp; Young) is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <PERSON> in New York City. The store is called a \"stationery and fancy goods emporium\".", "links": [{"title": "Tiffany & Co.", "link": "https://wikipedia.org/wiki/Tiffany_%26_Co."}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "The Anti-Corn Law League is established by <PERSON>.", "html": "1838 - The <a href=\"https://wikipedia.org/wiki/Anti-Corn_Law_League\" class=\"mw-redirect\" title=\"Anti-Corn Law League\">Anti-Corn Law League</a> is established by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anti-Corn_Law_League\" class=\"mw-redirect\" title=\"Anti-Corn Law League\">Anti-Corn Law League</a> is established by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Anti-Corn Law League", "link": "https://wikipedia.org/wiki/Anti-Corn_Law_League"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "The U.S. Congress passes the Fugitive Slave Act of 1850.", "html": "1850 - The U.S. Congress passes the <a href=\"https://wikipedia.org/wiki/Fugitive_Slave_Act_of_1850\" title=\"Fugitive Slave Act of 1850\">Fugitive Slave Act of 1850</a>.", "no_year_html": "The U.S. Congress passes the <a href=\"https://wikipedia.org/wiki/Fugitive_Slave_Act_of_1850\" title=\"Fugitive Slave Act of 1850\">Fugitive Slave Act of 1850</a>.", "links": [{"title": "Fugitive Slave Act of 1850", "link": "https://wikipedia.org/wiki/Fugitive_Slave_Act_of_1850"}]}, {"year": "1851", "text": "First publication of The New-York Daily Times, which later becomes The New York Times.", "html": "1851 - First publication of <i>The New-York Daily Times</i>, which later becomes <i><a href=\"https://wikipedia.org/wiki/The_New_York_Times\" title=\"The New York Times\">The New York Times</a></i>.", "no_year_html": "First publication of <i>The New-York Daily Times</i>, which later becomes <i><a href=\"https://wikipedia.org/wiki/The_New_York_Times\" title=\"The New York Times\">The New York Times</a></i>.", "links": [{"title": "The New York Times", "link": "https://wikipedia.org/wiki/The_New_York_Times"}]}, {"year": "1860", "text": "Second Opium War: Battle of Zhangjiawan: Now heading towards Beijing after having recently occupied Tianjin, the allied Anglo-French force engages and defeats a larger Qing Chinese army at Zhangjiawan.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Zhangjiawan\" title=\"Battle of Zhangjiawan\">Battle of Zhangjiawan</a>: Now heading towards <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a> after having recently occupied <a href=\"https://wikipedia.org/wiki/Tianjin\" title=\"Tianjin\">Tianjin</a>, the allied <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">Anglo</a>-<a href=\"https://wikipedia.org/wiki/Second_French_Empire\" title=\"Second French Empire\">French</a> force engages and defeats a larger <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing Chinese</a> army at <a href=\"https://wikipedia.org/wiki/Battle_of_Zhangjiawan\" title=\"Battle of Zhangjiawan\">Zhangjiawan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Zhangjiawan\" title=\"Battle of Zhangjiawan\">Battle of Zhangjiawan</a>: Now heading towards <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a> after having recently occupied <a href=\"https://wikipedia.org/wiki/Tianjin\" title=\"Tianjin\">Tianjin</a>, the allied <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">Anglo</a>-<a href=\"https://wikipedia.org/wiki/Second_French_Empire\" title=\"Second French Empire\">French</a> force engages and defeats a larger <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing Chinese</a> army at <a href=\"https://wikipedia.org/wiki/Battle_of_Zhangjiawan\" title=\"Battle of Zhangjiawan\">Zhangjiawan</a>.", "links": [{"title": "Second Opium War", "link": "https://wikipedia.org/wiki/Second_Opium_War"}, {"title": "Battle of Zhangjiawan", "link": "https://wikipedia.org/wiki/Battle_of_Zhangjiawan"}, {"title": "Beijing", "link": "https://wikipedia.org/wiki/Beijing"}, {"title": "Tianjin", "link": "https://wikipedia.org/wiki/Tianjin"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "Second French Empire", "link": "https://wikipedia.org/wiki/Second_French_Empire"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Battle of Zhangjiawan", "link": "https://wikipedia.org/wiki/Battle_of_Zhangjiawan"}]}, {"year": "1862", "text": "The Confederate States celebrate for the first and only time a Thanksgiving Day.", "html": "1862 - The <a href=\"https://wikipedia.org/wiki/Confederate_States\" class=\"mw-redirect\" title=\"Confederate States\">Confederate States</a> celebrate for the first and only time a <a href=\"https://wikipedia.org/wiki/Thanksgiving_Day\" class=\"mw-redirect\" title=\"Thanksgiving Day\">Thanksgiving Day</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Confederate_States\" class=\"mw-redirect\" title=\"Confederate States\">Confederate States</a> celebrate for the first and only time a <a href=\"https://wikipedia.org/wiki/Thanksgiving_Day\" class=\"mw-redirect\" title=\"Thanksgiving Day\">Thanksgiving Day</a>.", "links": [{"title": "Confederate States", "link": "https://wikipedia.org/wiki/Confederate_States"}, {"title": "Thanksgiving Day", "link": "https://wikipedia.org/wiki/Thanksgiving_Day"}]}, {"year": "1863", "text": "American Civil War: The Battle of Chickamauga begins between Confederate and Union forces. It involves the second highest amount of casualties for any American Civil War battle apart from Gettysburg.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Chickamauga\" title=\"Battle of Chickamauga\">Battle of Chickamauga</a> begins between Confederate and Union forces. It involves the second highest amount of casualties for any <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a> battle apart from <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Gettysburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Chickamauga\" title=\"Battle of Chickamauga\">Battle of Chickamauga</a> begins between Confederate and Union forces. It involves the second highest amount of casualties for any <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a> battle apart from <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Gettysburg</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Chickamauga", "link": "https://wikipedia.org/wiki/Battle_of_Chickamauga"}, {"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Gettysburg", "link": "https://wikipedia.org/wiki/Battle_of_Gettysburg"}]}, {"year": "1864", "text": "American Civil War: <PERSON> begins the Franklin-Nashville Campaign in an unsuccessful attempt to draw <PERSON> back out of Georgia.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins the <a href=\"https://wikipedia.org/wiki/Franklin%E2%80%93Nashville_Campaign\" class=\"mw-redirect\" title=\"Franklin-Nashville Campaign\">Franklin-Nashville Campaign</a> in an unsuccessful attempt to draw <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> back out of <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins the <a href=\"https://wikipedia.org/wiki/Franklin%E2%80%93Nashville_Campaign\" class=\"mw-redirect\" title=\"Franklin-Nashville Campaign\">Franklin-Nashville Campaign</a> in an unsuccessful attempt to draw <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> back out of <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>-Nashville Campaign", "link": "https://wikipedia.org/wiki/Franklin%E2%80%93Nashville_Campaign"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}]}, {"year": "1873", "text": "The U.S. bank Jay Cooke & Company declares bankruptcy, contributing to the Panic of 1873.", "html": "1873 - The U.S. bank <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company\" title=\"Jay Cooke &amp; Company\">Jay Cooke &amp; Company</a> declares bankruptcy, contributing to the <a href=\"https://wikipedia.org/wiki/Panic_of_1873\" title=\"Panic of 1873\">Panic of 1873</a>.", "no_year_html": "The U.S. bank <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company\" title=\"Jay Cooke &amp; Company\">Jay Cooke &amp; Company</a> declares bankruptcy, contributing to the <a href=\"https://wikipedia.org/wiki/Panic_of_1873\" title=\"Panic of 1873\">Panic of 1873</a>.", "links": [{"title": "Jay Cooke & Company", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company"}, {"title": "Panic of 1873", "link": "https://wikipedia.org/wiki/Panic_of_1873"}]}, {"year": "1879", "text": "The Blackpool Illuminations are switched on for the first time.", "html": "1879 - The <a href=\"https://wikipedia.org/wiki/Blackpool_Illuminations\" title=\"Blackpool Illuminations\">Blackpool Illuminations</a> are switched on for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Blackpool_Illuminations\" title=\"Blackpool Illuminations\">Blackpool Illuminations</a> are switched on for the first time.", "links": [{"title": "Blackpool Illuminations", "link": "https://wikipedia.org/wiki/Blackpool_Illuminations"}]}, {"year": "1882", "text": "The Pacific Stock Exchange opens.", "html": "1882 - The <a href=\"https://wikipedia.org/wiki/Pacific_Stock_Exchange\" class=\"mw-redirect\" title=\"Pacific Stock Exchange\">Pacific Stock Exchange</a> opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pacific_Stock_Exchange\" class=\"mw-redirect\" title=\"Pacific Stock Exchange\">Pacific Stock Exchange</a> opens.", "links": [{"title": "Pacific Stock Exchange", "link": "https://wikipedia.org/wiki/Pacific_Stock_Exchange"}]}, {"year": "1898", "text": "The Fashoda Incident triggers the last war scare between Britain and France.", "html": "1898 - The <a href=\"https://wikipedia.org/wiki/Fashoda_Incident\" title=\"Fashoda Incident\">Fashoda Incident</a> triggers the last war scare between Britain and France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fashoda_Incident\" title=\"Fashoda Incident\">Fashoda Incident</a> triggers the last war scare between Britain and France.", "links": [{"title": "Fashoda Incident", "link": "https://wikipedia.org/wiki/Fashoda_Incident"}]}, {"year": "1906", "text": "The 1906 Hong Kong typhoon kills an estimated 10,000 people.", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/1906_Hong_Kong_typhoon\" title=\"1906 Hong Kong typhoon\">1906 Hong Kong typhoon</a> kills an estimated 10,000 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1906_Hong_Kong_typhoon\" title=\"1906 Hong Kong typhoon\">1906 Hong Kong typhoon</a> kills an estimated 10,000 people.", "links": [{"title": "1906 Hong Kong typhoon", "link": "https://wikipedia.org/wiki/1906_Hong_Kong_typhoon"}]}, {"year": "1914", "text": "The Irish Home Rule Act becomes law, but is delayed until after World War I.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Government_of_Ireland_Act_1914\" title=\"Government of Ireland Act 1914\">Irish Home Rule Act</a> becomes law, but is delayed until after World War I.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Government_of_Ireland_Act_1914\" title=\"Government of Ireland Act 1914\">Irish Home Rule Act</a> becomes law, but is delayed until after World War I.", "links": [{"title": "Government of Ireland Act 1914", "link": "https://wikipedia.org/wiki/Government_of_Ireland_Act_1914"}]}, {"year": "1919", "text": "<PERSON> becomes the first African American to play professional football for a major team, the Akron Pros.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first African American to play professional football for a major team, the Akron Pros.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first African American to play professional football for a major team, the Akron Pros.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "The Kingdom of Hungary is admitted to the League of Nations.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary_(1920%E2%80%931946)\" title=\"Kingdom of Hungary (1920-1946)\">Kingdom of Hungary</a> is admitted to the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary_(1920%E2%80%931946)\" title=\"Kingdom of Hungary (1920-1946)\">Kingdom of Hungary</a> is admitted to the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "links": [{"title": "Kingdom of Hungary (1920-1946)", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary_(1920%E2%80%931946)"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1927", "text": "The Columbia Broadcasting System goes on the air.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Columbia_Broadcasting_System\" class=\"mw-redirect\" title=\"Columbia Broadcasting System\">Columbia Broadcasting System</a> goes on the air.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Columbia_Broadcasting_System\" class=\"mw-redirect\" title=\"Columbia Broadcasting System\">Columbia Broadcasting System</a> goes on the air.", "links": [{"title": "Columbia Broadcasting System", "link": "https://wikipedia.org/wiki/Columbia_Broadcasting_System"}]}, {"year": "1928", "text": "Juan de la Cierva makes the first Autogyro crossing of the English Channel.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cierva\"><PERSON> C<PERSON></a> makes the first <a href=\"https://wikipedia.org/wiki/Autogyro\" title=\"Autogyro\">Autogyro</a> crossing of the English Channel.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> la Cierva\"><PERSON> Cierva</a> makes the first <a href=\"https://wikipedia.org/wiki/Autogyro\" title=\"Autogyro\">Autogyro</a> crossing of the English Channel.", "links": [{"title": "<PERSON> la Cierva", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Autogyro", "link": "https://wikipedia.org/wiki/Autogyro"}]}, {"year": "1931", "text": "Imperial Japan instigates the Mukden incident as a pretext to invade and occupy Manchuria.", "html": "1931 - Imperial Japan instigates the <a href=\"https://wikipedia.org/wiki/Mukden_incident\" title=\"Mukden incident\">Mukden incident</a> as a pretext to <a href=\"https://wikipedia.org/wiki/Japanese_invasion_of_Manchuria\" title=\"Japanese invasion of Manchuria\">invade and occupy Manchuria</a>.", "no_year_html": "Imperial Japan instigates the <a href=\"https://wikipedia.org/wiki/Mukden_incident\" title=\"Mukden incident\">Mukden incident</a> as a pretext to <a href=\"https://wikipedia.org/wiki/Japanese_invasion_of_Manchuria\" title=\"Japanese invasion of Manchuria\">invade and occupy Manchuria</a>.", "links": [{"title": "Mukden incident", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_incident"}, {"title": "Japanese invasion of Manchuria", "link": "https://wikipedia.org/wiki/Japanese_invasion_of_Manchuria"}]}, {"year": "1934", "text": "The Soviet Union is admitted to the League of Nations.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> is admitted to the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> is admitted to the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1939", "text": "World War II: The Polish government of <PERSON><PERSON><PERSON> flees to Romania.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Polish government of <a href=\"https://wikipedia.org/wiki/Ignacy_<PERSON>%C5%9B<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">I<PERSON><PERSON></a> flees to Romania.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Polish government of <a href=\"https://wikipedia.org/wiki/Ignacy_<PERSON>%C5%9B<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">I<PERSON><PERSON></a> flees to Romania.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ignacy_Mo%C5%9B<PERSON><PERSON>"}]}, {"year": "1939", "text": "World War II: The radio show Germany Calling begins transmitting Nazi propaganda.", "html": "1939 - World War II: The radio show <i><a href=\"https://wikipedia.org/wiki/Germany_Calling\" title=\"Germany Calling\">Germany Calling</a></i> begins transmitting Nazi propaganda.", "no_year_html": "World War II: The radio show <i><a href=\"https://wikipedia.org/wiki/Germany_Calling\" title=\"Germany Calling\">Germany Calling</a></i> begins transmitting Nazi propaganda.", "links": [{"title": "Germany Calling", "link": "https://wikipedia.org/wiki/Germany_Calling"}]}, {"year": "1943", "text": "World War II: <PERSON> orders the deportation of Danish Jews.", "html": "1943 - World War II: Adolf <PERSON> orders the deportation of <a href=\"https://wikipedia.org/wiki/History_of_the_Jews_in_Denmark#The_Nazi_era\" title=\"History of the Jews in Denmark\">Danish Jews</a>.", "no_year_html": "World War II: Adolf <PERSON> orders the deportation of <a href=\"https://wikipedia.org/wiki/History_of_the_Jews_in_Denmark#The_Nazi_era\" title=\"History of the Jews in Denmark\">Danish Jews</a>.", "links": [{"title": "History of the Jews in Denmark", "link": "https://wikipedia.org/wiki/History_of_the_Jews_in_Denmark#The_Nazi_era"}]}, {"year": "1944", "text": "World War II: The British submarine HMS Tradewind torpedoes <PERSON><PERSON><PERSON><PERSON>, killing 5,600, mostly slave labourers and POWs.", "html": "1944 - World War II: The British submarine <a href=\"https://wikipedia.org/wiki/HMS_Tradewind_(P329)\" title=\"HMS Tradewind (P329)\">HMS <i>Tradewind</i></a> torpedoes <i><a href=\"https://wikipedia.org/wiki/Jun%27y%C5%8D_Maru\" title=\"<PERSON>'yō <PERSON>u\"><PERSON>'y<PERSON></a></i>, killing 5,600, mostly slave labourers and POWs.", "no_year_html": "World War II: The British submarine <a href=\"https://wikipedia.org/wiki/HMS_Tradewind_(P329)\" title=\"HMS Tradewind (P329)\">HMS <i>Tradewind</i></a> torpedoes <i><a href=\"https://wikipedia.org/wiki/Jun%27y%C5%8D_Maru\" title=\"Jun'yō Maru\"><PERSON>'y<PERSON></a></i>, killing 5,600, mostly slave labourers and POWs.", "links": [{"title": "HMS Tradewind (P329)", "link": "https://wikipedia.org/wiki/HMS_Tradewind_(P329)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jun%27y%C5%8D_Maru"}]}, {"year": "1944", "text": "World War II: Operation Market Garden results in the liberation of Eindhoven.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Market_Garden\" title=\"Operation Market Garden\">Operation Market Garden</a> results in the liberation of <a href=\"https://wikipedia.org/wiki/Eindhoven\" title=\"Eindhoven\">Eindhoven</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Market_Garden\" title=\"Operation Market Garden\">Operation Market Garden</a> results in the liberation of <a href=\"https://wikipedia.org/wiki/Eindhoven\" title=\"Eindhoven\">Eindhoven</a>.", "links": [{"title": "Operation Market Garden", "link": "https://wikipedia.org/wiki/Operation_Market_Garden"}, {"title": "Eindhoven", "link": "https://wikipedia.org/wiki/Eindhoven"}]}, {"year": "1944", "text": "World War II: The Battle of Arracourt begins.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Arracourt\" title=\"Battle of Arracourt\">Battle of Arracourt</a> begins.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Arracourt\" title=\"Battle of Arracourt\">Battle of Arracourt</a> begins.", "links": [{"title": "Battle of Arracourt", "link": "https://wikipedia.org/wiki/Battle_of_Arracourt"}]}, {"year": "1945", "text": "General <PERSON> moves his general headquarters from Manila to Tokyo.", "html": "1945 - General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> moves his general headquarters from Manila to Tokyo.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> moves his general headquarters from Manila to Tokyo.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "The National Security Act reorganizes the United States government's military and intelligence services.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/National_Security_Act_of_1947\" title=\"National Security Act of 1947\">National Security Act</a> reorganizes the United States government's military and intelligence services.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Security_Act_of_1947\" title=\"National Security Act of 1947\">National Security Act</a> reorganizes the United States government's military and intelligence services.", "links": [{"title": "National Security Act of 1947", "link": "https://wikipedia.org/wiki/National_Security_Act_of_1947"}]}, {"year": "1948", "text": "Operation Polo is terminated after the Indian Army accepts the surrender of the army of Hyderabad.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Operation_Polo\" class=\"mw-redirect\" title=\"Operation Polo\">Operation Polo</a> is terminated after the Indian Army accepts the surrender of the army of Hyderabad.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Polo\" class=\"mw-redirect\" title=\"Operation Polo\">Operation Polo</a> is terminated after the Indian Army accepts the surrender of the army of Hyderabad.", "links": [{"title": "Operation Polo", "link": "https://wikipedia.org/wiki/Operation_Polo"}]}, {"year": "1948", "text": "<PERSON> of Maine becomes the first woman elected to the United States Senate without completing another senator's term.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Maine becomes the first woman elected to the United States Senate without completing another senator's term.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Maine becomes the first woman elected to the United States Senate without completing another senator's term.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "Finnish president <PERSON><PERSON> <PERSON><PERSON> becomes the first Western head of state to be awarded the highest honor of the Soviet Union, the Order of Lenin.", "html": "1954 - Finnish president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> becomes the first Western head of state to be awarded the highest honor of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, the <a href=\"https://wikipedia.org/wiki/Order_of_Lenin\" title=\"Order of Lenin\">Order of Lenin</a>.", "no_year_html": "Finnish president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> becomes the first Western head of state to be awarded the highest honor of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, the <a href=\"https://wikipedia.org/wiki/Order_of_Lenin\" title=\"Order of Lenin\">Order of Lenin</a>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Order of Lenin", "link": "https://wikipedia.org/wiki/Order_of_Lenin"}]}, {"year": "1960", "text": "<PERSON><PERSON> arrives in New York City as the head of the Cuban delegation to the United Nations.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> arrives in New York City as the head of the Cuban delegation to the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> arrives in New York City as the head of the Cuban delegation to the United Nations.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "U.N. Secretary-General <PERSON><PERSON> dies in an air crash while attempting to negotiate peace in the Katanga region of the Democratic Republic of the Congo.", "html": "1961 - U.N. Secretary-General <a href=\"https://wikipedia.org/wiki/Dag_<PERSON>skj%C3%B6ld\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> dies in an <a href=\"https://wikipedia.org/wiki/1961_Ndola_United_Nations_DC-6_crash\" class=\"mw-redirect\" title=\"1961 Ndola United Nations DC-6 crash\">air crash</a> while attempting to negotiate peace in the Katanga region of the Democratic Republic of the Congo.", "no_year_html": "U.N. Secretary-General <a href=\"https://wikipedia.org/wiki/Dag_<PERSON>skj%C3%B6ld\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> dies in an <a href=\"https://wikipedia.org/wiki/1961_Ndola_United_Nations_DC-6_crash\" class=\"mw-redirect\" title=\"1961 Ndola United Nations DC-6 crash\">air crash</a> while attempting to negotiate peace in the Katanga region of the Democratic Republic of the Congo.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dag_Hammarskj%C3%B6ld"}, {"title": "1961 Ndola United Nations DC-6 crash", "link": "https://wikipedia.org/wiki/1961_Ndola_United_Nations_DC-6_crash"}]}, {"year": "1962", "text": "Burundi, Jamaica, Rwanda and Trinidad and Tobago are admitted to the United Nations.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Burundi\" title=\"Burundi\">Burundi</a>, <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a>, <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwanda</a> and <a href=\"https://wikipedia.org/wiki/Trinidad_and_Tobago\" title=\"Trinidad and Tobago\">Trinidad and Tobago</a> are admitted to the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burundi\" title=\"Burundi\">Burundi</a>, <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a>, <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwanda</a> and <a href=\"https://wikipedia.org/wiki/Trinidad_and_Tobago\" title=\"Trinidad and Tobago\">Trinidad and Tobago</a> are admitted to the United Nations.", "links": [{"title": "Burundi", "link": "https://wikipedia.org/wiki/Burundi"}, {"title": "Jamaica", "link": "https://wikipedia.org/wiki/Jamaica"}, {"title": "Rwanda", "link": "https://wikipedia.org/wiki/Rwanda"}, {"title": "Trinidad and Tobago", "link": "https://wikipedia.org/wiki/Trinidad_and_Tobago"}]}, {"year": "1962", "text": "Aeroflot Flight 213 crashes into a mountain near Chersky Airport, killing 32 people.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_213_(1962)\" title=\"Aeroflot Flight 213 (1962)\">Aeroflot Flight 213</a> crashes into a mountain near <a href=\"https://wikipedia.org/wiki/Chersky_Airport\" title=\"Chersky Airport\">Chersky Airport</a>, killing 32 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_213_(1962)\" title=\"Aeroflot Flight 213 (1962)\">Aeroflot Flight 213</a> crashes into a mountain near <a href=\"https://wikipedia.org/wiki/Chersky_Airport\" title=\"Chersky Airport\">Chersky Airport</a>, killing 32 people.", "links": [{"title": "Aeroflot Flight 213 (1962)", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_213_(1962)"}, {"title": "Chersky Airport", "link": "https://wikipedia.org/wiki/Chersky_Airport"}]}, {"year": "1964", "text": "The wedding of <PERSON> of Greece and Princess <PERSON><PERSON><PERSON> of Denmark takes place in Athens.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Wedding_of_Constantine_II_and_Princess_<PERSON><PERSON>\" title=\"Wedding of Constantine II and Princess <PERSON><PERSON><PERSON>\">wedding of <PERSON> of Greece and Princess <PERSON> of Denmark</a> takes place in Athens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wedding_of_Constantine_II_and_Princess_<PERSON><PERSON>\" title=\"Wedding of Constantine II and Princess <PERSON><PERSON><PERSON>\">wedding of <PERSON> of Greece and Princess <PERSON> of Denmark</a> takes place in Athens.", "links": [{"title": "Wedding of <PERSON> and Princess <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wedding_of_<PERSON>_II_and_Princess_<PERSON>"}]}, {"year": "1973", "text": "The Bahamas, East Germany and West Germany are admitted to the United Nations.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/The_Bahamas\" title=\"The Bahamas\">The Bahamas</a>, <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> and <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> are admitted to the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Bahamas\" title=\"The Bahamas\">The Bahamas</a>, <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> and <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> are admitted to the United Nations.", "links": [{"title": "The Bahamas", "link": "https://wikipedia.org/wiki/The_Bahamas"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}]}, {"year": "1974", "text": "Hurricane <PERSON><PERSON> strikes Honduras with 110 mph winds, killing 5,000 people.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Hurricane_Fifi\" class=\"mw-redirect\" title=\"Hurricane Fifi\">Hurricane Fifi</a> strikes Honduras with 110 mph winds, killing 5,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Fifi\" class=\"mw-redirect\" title=\"Hurricane Fifi\">Hurricane Fifi</a> strikes Honduras with 110 mph winds, killing 5,000 people.", "links": [{"title": "Hurricane <PERSON>fi", "link": "https://wikipedia.org/wiki/Hurricane_Fifi"}]}, {"year": "1977", "text": "Voyager I takes the first distant photograph of the Earth and the Moon together.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Voyager_I\" class=\"mw-redirect\" title=\"Voyager I\">Voyager I</a> takes the first distant photograph of the Earth and the Moon together.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Voyager_I\" class=\"mw-redirect\" title=\"Voyager I\">Voyager I</a> takes the first distant photograph of the Earth and the Moon together.", "links": [{"title": "Voyager I", "link": "https://wikipedia.org/wiki/<PERSON>_I"}]}, {"year": "1980", "text": "Soyuz 38 carries two cosmonauts (including one Cuban) to the Salyut 6 space station.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Soyuz_38\" title=\"Soyuz 38\">Soyuz 38</a> carries two cosmonauts (including one Cuban) to the <a href=\"https://wikipedia.org/wiki/Salyut_6\" title=\"Salyut 6\">Salyut 6</a> space station.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soyuz_38\" title=\"Soyuz 38\">Soyuz 38</a> carries two cosmonauts (including one Cuban) to the <a href=\"https://wikipedia.org/wiki/Salyut_6\" title=\"Salyut 6\">Salyut 6</a> space station.", "links": [{"title": "Soyuz 38", "link": "https://wikipedia.org/wiki/Soyuz_38"}, {"title": "Salyut 6", "link": "https://wikipedia.org/wiki/Salyut_6"}]}, {"year": "1981", "text": "The Assemblée Nationale votes to abolish capital punishment in France.", "html": "1981 - The <i>Assemblée Nationale</i> votes to abolish <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_France\" title=\"Capital punishment in France\">capital punishment in France</a>.", "no_year_html": "The <i>Assemblée Nationale</i> votes to abolish <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_France\" title=\"Capital punishment in France\">capital punishment in France</a>.", "links": [{"title": "Capital punishment in France", "link": "https://wikipedia.org/wiki/Capital_punishment_in_France"}]}, {"year": "1984", "text": "<PERSON> completes the first solo balloon crossing of the Atlantic.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> completes the first solo balloon crossing of the Atlantic.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> completes the first solo balloon crossing of the Atlantic.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "The 8888 Uprising in Myanmar comes to an end.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/8888_Uprising\" title=\"8888 Uprising\">8888 Uprising</a> in Myanmar comes to an end.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/8888_Uprising\" title=\"8888 Uprising\">8888 Uprising</a> in Myanmar comes to an end.", "links": [{"title": "8888 Uprising", "link": "https://wikipedia.org/wiki/8888_Uprising"}]}, {"year": "1988", "text": "General <PERSON>, president of Haiti, is ousted from power in a coup d'état led by General <PERSON><PERSON>.", "html": "1988 - General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of Haiti, is ousted from power in a <a href=\"https://wikipedia.org/wiki/September_1988_Haitian_coup_d%27%C3%A9tat\" title=\"September 1988 Haitian coup d'état\">coup d'état</a> led by General <a href=\"https://wikipedia.org/wiki/Prosper_Avril\" title=\"Prosper Avril\">Prosper Avril</a>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of Haiti, is ousted from power in a <a href=\"https://wikipedia.org/wiki/September_1988_Haitian_coup_d%27%C3%A9tat\" title=\"September 1988 Haitian coup d'état\">coup d'état</a> led by General <a href=\"https://wikipedia.org/wiki/Prosper_Avril\" title=\"Prosper Avril\">Prosper Avril</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "September 1988 Haitian coup d'état", "link": "https://wikipedia.org/wiki/September_1988_Haitian_coup_d%27%C3%A9tat"}, {"title": "Prosper A<PERSON>ril", "link": "https://wikipedia.org/wiki/Prosper_Avril"}]}, {"year": "1990", "text": "Liechtenstein becomes a member of the United Nations.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Liechtenstein\" title=\"Liechtenstein\">Liechtenstein</a> becomes a member of the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liechtenstein\" title=\"Liechtenstein\">Liechtenstein</a> becomes a member of the United Nations.", "links": [{"title": "Liechtenstein", "link": "https://wikipedia.org/wiki/Liechtenstein"}]}, {"year": "1992", "text": "An explosion rocks Giant Mine at the height of a labor dispute, killing nine replacement workers in Yellowknife, Canada.", "html": "1992 - An explosion rocks <a href=\"https://wikipedia.org/wiki/Giant_Mine\" title=\"Giant Mine\">Giant Mine</a> at the height of a labor dispute, killing nine replacement workers in Yellowknife, Canada.", "no_year_html": "An explosion rocks <a href=\"https://wikipedia.org/wiki/Giant_Mine\" title=\"Giant Mine\">Giant Mine</a> at the height of a labor dispute, killing nine replacement workers in Yellowknife, Canada.", "links": [{"title": "Giant Mine", "link": "https://wikipedia.org/wiki/Giant_Mine"}]}, {"year": "1997", "text": "United States media magnate <PERSON> donates US$1 billion to the United Nations.", "html": "1997 - United States media magnate <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> donates US$1 billion to the United Nations.", "no_year_html": "United States media magnate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> donates US$1 billion to the United Nations.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "The Anti-Personnel Mine Ban Convention is adopted.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Ottawa_Treaty\" title=\"Ottawa Treaty\">Anti-Personnel Mine Ban Convention</a> is adopted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ottawa_Treaty\" title=\"Ottawa Treaty\">Anti-Personnel Mine Ban Convention</a> is adopted.", "links": [{"title": "Ottawa Treaty", "link": "https://wikipedia.org/wiki/Ottawa_Treaty"}]}, {"year": "2001", "text": "First mailing of anthrax letters from Trenton, New Jersey in the 2001 anthrax attacks.", "html": "2001 - First mailing of <a href=\"https://wikipedia.org/wiki/Anthrax\" title=\"Anthrax\">anthrax</a> letters from <a href=\"https://wikipedia.org/wiki/Trenton,_New_Jersey\" title=\"Trenton, New Jersey\">Trenton, New Jersey</a> in the <a href=\"https://wikipedia.org/wiki/2001_anthrax_attacks\" title=\"2001 anthrax attacks\">2001 anthrax attacks</a>.", "no_year_html": "First mailing of <a href=\"https://wikipedia.org/wiki/Anthrax\" title=\"Anthrax\">anthrax</a> letters from <a href=\"https://wikipedia.org/wiki/Trenton,_New_Jersey\" title=\"Trenton, New Jersey\">Trenton, New Jersey</a> in the <a href=\"https://wikipedia.org/wiki/2001_anthrax_attacks\" title=\"2001 anthrax attacks\">2001 anthrax attacks</a>.", "links": [{"title": "Anthrax", "link": "https://wikipedia.org/wiki/Anthrax"}, {"title": "Trenton, New Jersey", "link": "https://wikipedia.org/wiki/Trenton,_New_Jersey"}, {"title": "2001 anthrax attacks", "link": "https://wikipedia.org/wiki/2001_anthrax_attacks"}]}, {"year": "2007", "text": "Buddhist monks join anti-government protesters in Myanmar, starting what some call the Saffron Revolution.", "html": "2007 - Buddhist monks join anti-government protesters in Myanmar, starting what some call the <a href=\"https://wikipedia.org/wiki/Saffron_Revolution\" title=\"Saffron Revolution\">Saffron Revolution</a>.", "no_year_html": "Buddhist monks join anti-government protesters in Myanmar, starting what some call the <a href=\"https://wikipedia.org/wiki/Saffron_Revolution\" title=\"Saffron Revolution\">Saffron Revolution</a>.", "links": [{"title": "Saffron Revolution", "link": "https://wikipedia.org/wiki/Saffron_Revolution"}]}, {"year": "2011", "text": "The 2011 Sikkim earthquake is felt across northeastern India, Nepal, Bhutan, Bangladesh and southern Tibet.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/2011_Sikkim_earthquake\" title=\"2011 Sikkim earthquake\">2011 Sikkim earthquake</a> is felt across northeastern India, Nepal, Bhutan, Bangladesh and southern Tibet.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2011_Sikkim_earthquake\" title=\"2011 Sikkim earthquake\">2011 Sikkim earthquake</a> is felt across northeastern India, Nepal, Bhutan, Bangladesh and southern Tibet.", "links": [{"title": "2011 Sikkim earthquake", "link": "https://wikipedia.org/wiki/2011_Sikkim_earthquake"}]}, {"year": "2014", "text": "Scotland votes against independence from the United Kingdom, by 55% to 45%.", "html": "2014 - Scotland <a href=\"https://wikipedia.org/wiki/2014_Scottish_independence_referendum\" title=\"2014 Scottish independence referendum\">votes against</a> independence from the United Kingdom, by 55% to 45%.", "no_year_html": "Scotland <a href=\"https://wikipedia.org/wiki/2014_Scottish_independence_referendum\" title=\"2014 Scottish independence referendum\">votes against</a> independence from the United Kingdom, by 55% to 45%.", "links": [{"title": "2014 Scottish independence referendum", "link": "https://wikipedia.org/wiki/2014_Scottish_independence_referendum"}]}, {"year": "2015", "text": "Two security personnel, 17 worshippers in a mosque, and 13 militants are killed during a Tehrik-i-Taliban Pakistan attack on a Pakistan Air Force base on the outskirts of Peshawar.", "html": "2015 - Two security personnel, 17 worshippers in a mosque, and 13 militants are killed during a Tehrik-i-Taliban Pakistan <a href=\"https://wikipedia.org/wiki/2015_Camp_Badaber_attack\" title=\"2015 Camp Badaber attack\">attack</a> on a Pakistan Air Force base on the outskirts of Peshawar.", "no_year_html": "Two security personnel, 17 worshippers in a mosque, and 13 militants are killed during a Tehrik-i-Taliban Pakistan <a href=\"https://wikipedia.org/wiki/2015_Camp_Badaber_attack\" title=\"2015 Camp Badaber attack\">attack</a> on a Pakistan Air Force base on the outskirts of Peshawar.", "links": [{"title": "2015 Camp Badaber attack", "link": "https://wikipedia.org/wiki/2015_Camp_Badaber_attack"}]}, {"year": "2016", "text": "The 2016 Uri attack in Jammu and Kashmir, India by terrorist group Jaish-e-Mohammed results in the deaths of nineteen Indian Army soldiers and all four attackers.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/2016_Uri_attack\" title=\"2016 Uri attack\">2016 Uri attack</a> in <a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)\" title=\"Jammu and Kashmir (state)\">Jammu and Kashmir, India</a> by terrorist group <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON><PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a> results in the deaths of nineteen Indian Army soldiers and all four attackers.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2016_Uri_attack\" title=\"2016 Uri attack\">2016 Uri attack</a> in <a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)\" title=\"Jammu and Kashmir (state)\">Jammu and Kashmir, India</a> by terrorist group <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> results in the deaths of nineteen Indian Army soldiers and all four attackers.", "links": [{"title": "2016 Uri attack", "link": "https://wikipedia.org/wiki/2016_Uri_attack"}, {"title": "Jammu and Kashmir (state)", "link": "https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>"}]}], "Births": [{"year": "53", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 117)", "html": "53 - AD 53 - <a href=\"https://wikipedia.org/wiki/Trajan\" title=\"Trajan\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 117)", "no_year_html": "AD 53 - <a href=\"https://wikipedia.org/wiki/Trajan\" title=\"<PERSON>rajan\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 117)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "524", "text": "<PERSON><PERSON>, ruler of Palenque (d. 583)", "html": "524 - <a href=\"https://wikipedia.org/wiki/Kan_<PERSON><PERSON>_<PERSON>\" title=\"Kan Bahl<PERSON> I\"><PERSON>n <PERSON><PERSON> I</a>, ruler of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Pa<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan_<PERSON><PERSON>_<PERSON>\" title=\"Kan <PERSON><PERSON> I\"><PERSON>n <PERSON><PERSON> I</a>, ruler of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palen<PERSON>\"><PERSON><PERSON><PERSON></a> (d. 583)", "links": [{"title": "Kan <PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palenque"}]}, {"year": "1091", "text": "<PERSON><PERSON><PERSON>, Byzantine prince and general (d. 1130/31)", "html": "1091 - <a href=\"https://wikipedia.org/wiki/<PERSON>ron<PERSON>_<PERSON>_(son_of_<PERSON><PERSON>_<PERSON>)\" title=\"<PERSON><PERSON><PERSON> (son of <PERSON><PERSON>)\"><PERSON><PERSON><PERSON> Komnenos</a>, Byzantine prince and general (d. 1130/31)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(son_of_<PERSON><PERSON>_<PERSON>)\" title=\"<PERSON><PERSON><PERSON> (son of <PERSON><PERSON>)\"><PERSON><PERSON><PERSON></a>, Byzantine prince and general (d. 1130/31)", "links": [{"title": "<PERSON><PERSON><PERSON> (son of <PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Andronikos_Komnenos_(son_of_<PERSON><PERSON>_<PERSON>)"}]}, {"year": "1344", "text": "<PERSON> France, Duchess of Bar (d. 1404)", "html": "1344 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Bar\" title=\"<PERSON> of France, Duchess of Bar\"><PERSON> of France, Duchess of Bar</a> (d. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Bar\" title=\"<PERSON> of France, Duchess of Bar\"><PERSON> of France, Duchess of Bar</a> (d. 1404)", "links": [{"title": "<PERSON> of France, Duchess of Bar", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Bar"}]}, {"year": "1434", "text": "<PERSON> of Portugal, Holy Roman Empress (d. 1467)", "html": "1434 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Holy_Roman_Empress\" title=\"<PERSON> of Portugal, Holy Roman Empress\"><PERSON> of Portugal, Holy Roman Empress</a> (d. 1467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Holy_Roman_Empress\" title=\"<PERSON> of Portugal, Holy Roman Empress\"><PERSON> of Portugal, Holy Roman Empress</a> (d. 1467)", "links": [{"title": "<PERSON> of Portugal, Holy Roman Empress", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Portugal,_Holy_Roman_Empress"}]}, {"year": "1501", "text": "<PERSON>, 1st Baron <PERSON> (d. 1563)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a> (d. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a> (d. 1563)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1554", "text": "<PERSON><PERSON>, Safavid prince (d. 1576)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Safavid prince (d. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Safavid prince (d. 1576)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1587", "text": "<PERSON>, Italian singer-songwriter and lute player (d. 1640)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>te\" title=\"<PERSON><PERSON>\">lute</a> player (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">lute</a> player (d. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lute", "link": "https://wikipedia.org/wiki/Lute"}]}, {"year": "1606", "text": "<PERSON>, Chinese rebel leader (d. 1647)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese rebel leader (d. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese rebel leader (d. 1647)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, Scottish bishop, historian, and theologian (d. 1715)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bishop, historian, and theologian (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Burn<PERSON>\"><PERSON></a>, Scottish bishop, historian, and theologian (d. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1676", "text": "<PERSON><PERSON><PERSON>, Duke of Württemberg (d. 1733)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON><PERSON>, Duke of Württemberg\"><PERSON><PERSON><PERSON>, Duke of Württemberg</a> (d. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON><PERSON>, Duke of Württemberg\"><PERSON><PERSON><PERSON>, Duke of Württemberg</a> (d. 1733)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg"}]}, {"year": "1684", "text": "<PERSON>, German organist and composer (d. 1748)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1709", "text": "<PERSON>, English lexicographer and poet (d. 1784)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lexicographer and poet (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lexicographer and poet (d. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1711", "text": "<PERSON><PERSON><PERSON>, Austrian composer and educator (d. 1783)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian composer and educator (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian composer and educator (d. 1783)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1733", "text": "<PERSON>, American lawyer and politician, 3rd Governor of Delaware (d. 1798)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._statesman)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. statesman)\"><PERSON></a>, American lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Delaware\" class=\"mw-redirect\" title=\"List of Governors of Delaware\">Governor of Delaware</a> (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._statesman)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. statesman)\"><PERSON></a>, American lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Delaware\" class=\"mw-redirect\" title=\"List of Governors of Delaware\">Governor of Delaware</a> (d. 1798)", "links": [{"title": "<PERSON> (U.S. statesman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._statesman)"}, {"title": "List of Governors of Delaware", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Delaware"}]}, {"year": "1750", "text": "<PERSON><PERSON> y Oro<PERSON>, Spanish poet and playwright (d. 1791)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_de_Iriarte_y_Oropesa\" class=\"mw-redirect\" title=\"<PERSON><PERSON> de Iriarte y Oropesa\"><PERSON><PERSON> y Oropesa</a>, Spanish poet and playwright (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_de_Iriarte_y_Oropesa\" class=\"mw-redirect\" title=\"<PERSON><PERSON> de Iriarte y Oropesa\"><PERSON><PERSON>rte y Oropesa</a>, Spanish poet and playwright (d. 1791)", "links": [{"title": "Tomás de Iriarte y Oropesa", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_de_Iriarte_y_Oropesa"}]}, {"year": "1752", "text": "<PERSON><PERSON><PERSON><PERSON>, French mathematician and theorist (d. 1833)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mathematician and theorist (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mathematician and theorist (d. 1833)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1765", "text": "<PERSON> (d. 1846)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XVI\" title=\"Pope Gregory XVI\"><PERSON> <PERSON> XVI</a> (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory XVI\"><PERSON> <PERSON> XVI</a> (d. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, American lawyer, jurist, and politician (d. 1845)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Story\"><PERSON></a>, American lawyer, jurist, and politician (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON> of Denmark (d. 1848)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Christian_VIII_of_Denmark\" title=\"Christian VIII of Denmark\">Christian VIII of Denmark</a> (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_VIII_of_Denmark\" title=\"Christian VIII of Denmark\">Christian VIII of Denmark</a> (d. 1848)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_VIII_of_Denmark"}]}, {"year": "1786", "text": "<PERSON><PERSON>, German poet and author (d. 1862)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and author (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and author (d. 1862)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician, 41st Governor of Georgia (d. 1880)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Vespas<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> V<PERSON>pas<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (d. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "1819", "text": "<PERSON>, French physicist and academic (d. 1868)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON> de Ornelas e V<PERSON>los, Portuguese archbishop (d. 1880)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Aires_de_Ornelas_e_Vasconcelos\" title=\"Aires de Ornelas e Vasconcelos\">Aires de Ornelas e Vasconcelos</a>, Portuguese archbishop (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aires_de_Ornelas_e_Vasconcelos\" title=\"Aires de Ornelas e Vasconcelos\">Aires de Ornelas e Vasconcelos</a>, Portuguese archbishop (d. 1880)", "links": [{"title": "Aires de Ornelas e Vasconcelos", "link": "https://wikipedia.org/wiki/Aires_de_Ornelas_e_Vasconcelos"}]}, {"year": "1838", "text": "<PERSON>, Dutch painter and educator (d. 1888)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and educator (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and educator (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON>, American artist (d. 1934)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>lus <PERSON>\"><PERSON><PERSON></a>, American artist (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American artist (d. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Norwegian captain, businessman, and politician, founded Vesteraalens Dampskibsselskab (d. 1930)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian captain, businessman, and politician, founded <a href=\"https://wikipedia.org/wiki/Vesteraalens_Dampskibsselskab\" title=\"Vesteraalens Dampskibsselskab\">Vesteraalens Dampskibsselskab</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian captain, businessman, and politician, founded <a href=\"https://wikipedia.org/wiki/Vesteraalens_Dampskibsselskab\" title=\"Vesteraalens Dampskibsselskab\">Vesteraalens Dampskibsselskab</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vesteraalens Dampskibsselskab", "link": "https://wikipedia.org/wiki/Vesteraalens_Dampskibsselskab"}]}, {"year": "1848", "text": "<PERSON>, English-American pianist and composer (d. 1927)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American pianist and composer (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American pianist and composer (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, American lawyer and judge (d. 1945)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, English Salvation Army officer (d. 1955)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Salvation_Army\" class=\"mw-redirect\" title=\"Salvation Army\">Salvation Army</a> officer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Salvation_Army\" class=\"mw-redirect\" title=\"Salvation Army\">Salvation Army</a> officer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Salvation Army", "link": "https://wikipedia.org/wiki/Salvation_Army"}]}, {"year": "1859", "text": "<PERSON>, American lawyer and politician, 41st Governor of Massachusetts (d. 1946)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1859", "text": "<PERSON>, American businessman and politician (d. 1940)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Lincoln_Loy_McCandless\" class=\"mw-redirect\" title=\"Lincoln Loy McCandless\"><PERSON></a>, American businessman and politician (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Loy_McCandless\" class=\"mw-redirect\" title=\"Lincoln Loy McCandless\"><PERSON></a>, American businessman and politician (d. 1940)", "links": [{"title": "<PERSON> Loy <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Loy_M<PERSON>Candless"}]}, {"year": "1860", "text": "<PERSON>, Italian-American composer and educator (d. 1942)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American composer and educator (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American composer and educator (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American anthropologist, author, and educator (d. 1947)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist, author, and educator (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist, author, and educator (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, German-Italian pianist and educator (d. 1955)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian pianist and educator (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian pianist and educator (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Austrian fencer and cyclist (d. 1919)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian fencer and cyclist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian fencer and cyclist (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON>, Chilean philanthropist (d. 1945)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Burgos\" title=\"Tomás Burgos\"><PERSON><PERSON></a>, Chilean philanthropist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Burgos\" title=\"Tomás Burgos\"><PERSON><PERSON></a>, Chilean philanthropist (d. 1945)", "links": [{"title": "<PERSON>ás <PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Burgos"}]}, {"year": "1876", "text": "<PERSON>, Australian journalist and politician, 9th Prime Minister of Australia (d. 1953)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1878", "text": "<PERSON>, American admiral (d. 1974)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, 14th Baron <PERSON>, English composer, painter, and author (d. 1950)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 14th Baron <PERSON>\"><PERSON>, 14th Baron <PERSON></a>, English composer, painter, and author (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 14th Baron <PERSON>\"><PERSON>, 14th Baron <PERSON></a>, English composer, painter, and author (d. 1950)", "links": [{"title": "<PERSON>, 14th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_14th_Baron_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON><PERSON>, Azerbaijani composer, conductor, and playwright (d. 1948)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani composer, conductor, and playwright (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani composer, conductor, and playwright (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, American entrepreneur (d. 1961)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON>.\"><PERSON><PERSON><PERSON>.</a>, American entrepreneur (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON> Jr.\"><PERSON><PERSON><PERSON>.</a>, American entrepreneur (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1888", "text": "<PERSON>, English-Canadian environmentalist and author (d. 1938)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Grey_Owl\" title=\"Grey Owl\">Grey Owl</a>, English-Canadian environmentalist and author (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grey_Owl\" title=\"Grey Owl\">Grey Owl</a>, English-Canadian environmentalist and author (d. 1938)", "links": [{"title": "Grey Owl", "link": "https://wikipedia.org/wiki/Grey_Owl"}]}, {"year": "1888", "text": "<PERSON>, Swiss psychologist and author (d. 1953)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychologist and author (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychologist and author (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Australian activist and politician (d. 1970)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist and politician (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist and politician (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Australian general, businessman, and educator (d. 1959)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general, businessman, and educator (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general, businessman, and educator (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Spanish author (d. 1984)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_y_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Spanish author (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_y_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Spanish author (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_P%C3%A9rez_y_P%C3%A9rez"}]}, {"year": "1893", "text": "<PERSON>, Australian pianist, composer, and conductor (d. 1960)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist, composer, and conductor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist, composer, and conductor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American soldier and author (d. 1954)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English actress (d. 1978)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, French footballer and manager (d. 1973)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Canadian lawyer and politician, 13th Prime Minister of Canada (d. 1979)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1895", "text": "<PERSON>, German astrologer and author (d. 1970)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astrologer)\" title=\"<PERSON> (astrologer)\"><PERSON></a>, German astrologer and author (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astrologer)\" title=\"<PERSON> (astrologer)\"><PERSON></a>, German astrologer and author (d. 1970)", "links": [{"title": "<PERSON> (astrologer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astrologer)"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Japanese super-centenarian (d. 2009)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese super-centenarian (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese super-centenarian (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Spanish composer and conductor (d. 1988)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1bal\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and conductor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1bal\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and conductor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pablo_Soroz%C3%A1bal"}]}, {"year": "1900", "text": "<PERSON>, American violinist and educator (d. 1966)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and educator (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and educator (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mauritian philanthropist and politician, 1st Prime Minister of Mauritius (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Seewoosagur_<PERSON>olam\" title=\"Seewoosagur <PERSON>\">See<PERSON><PERSON><PERSON><PERSON></a>, Mauritian philanthropist and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mauritius\" title=\"Prime Minister of Mauritius\">Prime Minister of Mauritius</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seewoosagur_<PERSON>olam\" title=\"Seewoosagu<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mauritian philanthropist and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mauritius\" title=\"Prime Minister of Mauritius\">Prime Minister of Mauritius</a> (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Seewoosagur_Ramgoolam"}, {"title": "Prime Minister of Mauritius", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Mauritius"}]}, {"year": "1901", "text": "<PERSON>, American director and producer (d. 1980)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach (d. 1988)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American soldier and sculptor (d. 1985)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and sculptor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and sculptor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, 1st Viscount <PERSON>, English businessman and politician, Secretary of State for Education (d. 1999)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a> (d. 1999)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Secretary of State for Education", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Education"}]}, {"year": "1905", "text": "<PERSON> \"<PERSON>\" <PERSON>, American actor (d. 1977)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Rochester%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Rochester%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American actor (d. 1977)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Rochester%22_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American dancer and choreographer (d. 1993)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Swedish-American actress (d. 1990)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arbo\" title=\"G<PERSON> Garbo\"><PERSON><PERSON></a>, Swedish-American actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arbo\" title=\"G<PERSON> Garbo\"><PERSON><PERSON></a>, Swedish-American actress (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_Garbo"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Indian poet and author (d. 1995)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and author (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and author (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON>, French actor (d. 1968)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Filipino cardinal (d. 1983)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino cardinal (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino cardinal (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Austrian actor (d. 2005)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American physicist and chemist, Nobel Prize laureate (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1908", "text": "<PERSON>, Georgian-Armenian astrophysicist, astronomer, and academic (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Georgian-Armenian astrophysicist, astronomer, and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Georgian-Armenian astrophysicist, astronomer, and academic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Israeli pianist and composer (d. 2008)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli pianist and composer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli pianist and composer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Canadian ice hockey player (d. 1976)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Canadian ice hockey player (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Chilean journalist and activist (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_de_la_Cruz\" title=\"María de la Cruz\"><PERSON></a>, Chilean journalist and activist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_de_la_Cruz\" title=\"<PERSON> de la Cruz\"><PERSON></a>, Chilean journalist and activist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_Cruz"}]}, {"year": "1914", "text": "<PERSON>, English director, cinematographer, and photographer (d. 2009)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Jack_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, English director, cinematographer, and photographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_<PERSON>\" title=\"Jack Cardiff\"><PERSON></a>, English director, cinematographer, and photographer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Italian actor (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Rossano_Brazzi\" title=\"Rossano Brazzi\"><PERSON><PERSON></a>, Italian actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rossano_B<PERSON>zi\" title=\"Rossano Brazzi\"><PERSON><PERSON></a>, Italian actor (d. 1994)", "links": [{"title": "Rossano Brazzi", "link": "https://wikipedia.org/wiki/Rossano_Brazzi"}]}, {"year": "1916", "text": "<PERSON>, American lawyer and politician (d. 2003)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress and voice artist (d. 2017)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/June_Foray\" title=\"June Foray\">June Foray</a>, American actress and voice artist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Foray\" title=\"June Foray\">June Foray</a>, American actress and voice artist (d. 2017)", "links": [{"title": "June Foray", "link": "https://wikipedia.org/wiki/June_Foray"}]}, {"year": "1917", "text": "<PERSON>, English footballer and manager (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1917)\" title=\"<PERSON> (footballer, born 1917)\"><PERSON></a>, English footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1917)\" title=\"<PERSON> (footballer, born 1917)\"><PERSON></a>, English footballer and manager (d. 2012)", "links": [{"title": "<PERSON> (footballer, born 1917)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1917)"}]}, {"year": "1917", "text": "<PERSON>, American lawyer and philosopher (d. 1960)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and philosopher (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and philosopher (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American race car driver (d. 1972)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American wrestler (d. 2010)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American fiddler (d. 1993)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fiddler)\" title=\"<PERSON> (fiddler)\"><PERSON></a>, American fiddler (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fiddler)\" title=\"<PERSON> (fiddler)\"><PERSON></a>, American fiddler (d. 1993)", "links": [{"title": "<PERSON> (fiddler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fiddler)"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American saxophonist (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress (d. 1985)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Grayson_Hall\" title=\"Grayson Hall\"><PERSON> Hall</a>, American actress (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grayson_Hall\" title=\"Grayson Hall\"><PERSON> Hall</a>, American actress (d. 1985)", "links": [{"title": "Grayson Hall", "link": "https://wikipedia.org/wiki/Grayson_Hall"}]}, {"year": "1922", "text": "<PERSON>-<PERSON>, English composer (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 2014)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1923", "text": "<PERSON> of Romania (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>_of_Romania\" class=\"mw-redirect\" title=\"Queen <PERSON> of Romania\">Queen <PERSON> of Romania</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>_of_Romania\" class=\"mw-redirect\" title=\"Queen <PERSON> of Romania\">Queen <PERSON> of Romania</a> (d. 2016)", "links": [{"title": "Queen <PERSON> of Romania", "link": "https://wikipedia.org/wiki/Queen_<PERSON>_<PERSON>_Romania"}]}, {"year": "1923", "text": "<PERSON>, American politician, 35th Governor of Minnesota (d. 2023)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}, {"title": "Governor of Minnesota", "link": "https://wikipedia.org/wiki/Governor_of_Minnesota"}]}, {"year": "1923", "text": "<PERSON>, English architect, co-designed Robin Hood Gardens (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English architect, co-designed <a href=\"https://wikipedia.org/wiki/Robin_Hood_Gardens\" title=\"Robin Hood Gardens\">Robin Hood Gardens</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English architect, co-designed <a href=\"https://wikipedia.org/wiki/Robin_<PERSON>_Gardens\" title=\"Robin Hood Gardens\">Robin Hood Gardens</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Robin <PERSON> Gardens", "link": "https://wikipedia.org/wiki/Robin_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Scottish-Canadian lawyer and jurist, 60th P<PERSON><PERSON>ne Justice of the Supreme Court of Canada (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-Canadian lawyer and jurist, 60th <a href=\"https://wikipedia.org/wiki/List_of_Justices_of_the_Supreme_Court_of_Canada\" class=\"mw-redirect\" title=\"List of Justices of the Supreme Court of Canada\"><PERSON><PERSON><PERSON><PERSON> Justice of the Supreme Court of Canada</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-Canadian lawyer and jurist, 60th <a href=\"https://wikipedia.org/wiki/List_of_Justices_of_the_Supreme_Court_of_Canada\" class=\"mw-redirect\" title=\"List of Justices of the Supreme Court of Canada\"><PERSON><PERSON><PERSON><PERSON> Justice of the Supreme Court of Canada</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Justices of the Supreme Court of Canada", "link": "https://wikipedia.org/wiki/List_of_Justices_of_the_Supreme_Court_of_Canada"}]}, {"year": "1924", "text": "<PERSON><PERSON> <PERSON><PERSON>, American police officer (d. 1963)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/J._<PERSON>._Tippit\" title=\"J. D. Tippit\"><PERSON><PERSON> <PERSON><PERSON></a>, American police officer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON>._Tippit\" title=\"J. D. Tippit\"><PERSON><PERSON> <PERSON><PERSON></a>, American police officer (d. 1963)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Tippit"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian actress (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Elo%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elo%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian actress (d. 2018)", "links": [{"title": "Eloísa Mafalda", "link": "https://wikipedia.org/wiki/Elo%C3%ADsa_Ma<PERSON>lda"}]}, {"year": "1925", "text": "<PERSON>, American baseball player and coach (d. 1994)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English economist and academic (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American director, producer, and screenwriter (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>pan\"><PERSON></a>, American director, producer, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bud_Greenspan"}]}, {"year": "1926", "text": "<PERSON>, American author and illustrator, founded The Kubert School (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, founded <a href=\"https://wikipedia.org/wiki/The_Kubert_School\" title=\"The Kubert School\">The Kubert School</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, founded <a href=\"https://wikipedia.org/wiki/The_Kubert_School\" title=\"The Kubert School\">The Kubert School</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Kubert School", "link": "https://wikipedia.org/wiki/The_Kubert_School"}]}, {"year": "1927", "text": "<PERSON>, American actress (d. 2006)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, <PERSON> of Camden, English politician (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Camden\" title=\"<PERSON><PERSON>, <PERSON> of Camden\"><PERSON><PERSON>, Baroness <PERSON> of Camden</a>, English politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Camden\" title=\"<PERSON><PERSON>, Baroness <PERSON> of Camden\"><PERSON><PERSON>, Baroness <PERSON> of Camden</a>, English politician (d. 2018)", "links": [{"title": "<PERSON><PERSON>, <PERSON> of Camden", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Camden"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American singer (d. 1977)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> King\"><PERSON><PERSON></a>, American singer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ted<PERSON> King\"><PERSON><PERSON></a>, American singer (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American director and producer (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Greek-Canadian wrestler (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Canadian wrestler (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Canadian wrestler (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Argentinian businessman (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian businessman (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian businessman (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julio_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Russian physicist and astronaut (d. 2002)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and astronaut (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and astronaut (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American soldier and politician (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American soldier and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American soldier and politician (d. 2016)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1933", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, producer, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, producer, and screenwriter (d. 2023)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Italian-American sculptor", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Russian actor and singer (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Russian actor and singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Russian actor and singer (d. 2017)", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)"}]}, {"year": "1933", "text": "<PERSON>, English scholar and critic", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Trinidadian-Canadian lawyer and activist (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-Canadian lawyer and activist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-Canadian lawyer and activist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pop_singer)\" title=\"<PERSON> (pop singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pop_singer)\" title=\"<PERSON> (pop singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "links": [{"title": "<PERSON> (pop singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(pop_singer)"}]}, {"year": "1933", "text": "<PERSON>, American actor and comedian (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English cartoonist (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, English cartoonist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, English cartoonist (d. 2012)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "1935", "text": "<PERSON>, English snooker player and sportscaster (d. 2006)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player and sportscaster (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player and sportscaster (d. 2006)", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)"}]}, {"year": "1936", "text": "<PERSON>, Irish singer-songwriter and guitarist (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Tom\" title=\"Big Tom\"><PERSON></a>, Irish singer-songwriter and guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Tom\" title=\"Big Tom\"><PERSON></a>, Irish singer-songwriter and guitarist (d. 2018)", "links": [{"title": "Big Tom", "link": "https://wikipedia.org/wiki/<PERSON>_Tom"}]}, {"year": "1937", "text": "<PERSON>, Canadian ice hockey player and coach  (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, South African politician (d. 2009)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (d. 2009)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English-American wrestler and trainer (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American wrestler and trainer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American wrestler and trainer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian businessman, co-founded Harvey Norman", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Portuguese lawyer and politician, 18th President of Portugal (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1939", "text": "<PERSON>, Belgian mathematician and theorist (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician and theorist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician and theorist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer and actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Avalon\" title=\"Frankie Avalon\"><PERSON></a>, American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frankie_Avalon\" title=\"Frankie Avalon\"><PERSON></a>, American singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Turkish businessman", "html": "1942 - <a href=\"https://wikipedia.org/wiki/%C5%9Eenes_Erzik\" title=\"Şenes Erzik\"><PERSON><PERSON><PERSON></a>, Turkish businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9Eenes_Erzik\" title=\"Şenes Erzik\"><PERSON><PERSON><PERSON></a>, Turkish businessman", "links": [{"title": "Şenes Erzik", "link": "https://wikipedia.org/wiki/%C5%9Eenes_Erzik"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Spanish singer and actress (d. 2006)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Roc%C3%ADo_Jurado\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish singer and actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roc%C3%ADo_Jurado\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish singer and actress (d. 2006)", "links": [{"title": "R<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roc%C3%ADo_Jurado"}]}, {"year": "1944", "text": "<PERSON>, American colonel, pilot, and astronaut (d. 1995)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer-songwriter and producer  (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and producer (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, British-American computer programmer and businessman, founded McAfee (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American computer programmer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>c<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American computer programmer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>c<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McAfee", "link": "https://wikipedia.org/wiki/McAfee"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish-Venezuelan saxophonist, clarinet player, and conductor (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Benjam%C3%ADn_Brea\" title=\"Benja<PERSON><PERSON> B<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish-Venezuelan saxophonist, clarinet player, and conductor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benjam%C3%ADn_Brea\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish-Venezuelan saxophonist, clarinet player, and conductor (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benjam%C3%ADn_Brea"}]}, {"year": "1946", "text": "<PERSON>, English actor (d. 2000)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Australian ballet dancer (d. 1992)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian ballet dancer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian ballet dancer (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1946", "text": "<PERSON>, Australian-English playwright, translator, and educator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English playwright, translator, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English playwright, translator, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>in"}]}, {"year": "1947", "text": "<PERSON>, English comedian, actor, and singer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American historian and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian businessman, founded the Minardi Racing Team", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian businessman, founded the <a href=\"https://wikipedia.org/wiki/Minardi\" title=\"Minardi\">Minardi Racing Team</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian businessman, founded the <a href=\"https://wikipedia.org/wiki/Minardi\" title=\"Minardi\">Minardi Racing Team</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American computer programmer and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Lynn_Abbey\" title=\"Lynn Abbey\">Lynn Abbey</a>, American computer programmer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lynn_Abbey\" title=\"Lynn Abbey\">Lynn Abbey</a>, American computer programmer and author", "links": [{"title": "Lynn Abbey", "link": "https://wikipedia.org/wiki/Lynn_Abbey"}]}, {"year": "1949", "text": "<PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American guitarist and songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English academic and politician, Minister for the Cabinet Office (d. 2005)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office\" title=\"Minister for the Cabinet Office\">Minister for the Cabinet Office</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office\" title=\"Minister for the Cabinet Office\">Minister for the Cabinet Office</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for the Cabinet Office", "link": "https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office"}]}, {"year": "1949", "text": "<PERSON>, English footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, English dancer and choreographer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English dancer and choreographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Indian actor (d. 2009)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)"}]}, {"year": "1950", "text": "<PERSON>, Swedish politician, Governor of Stockholm County", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Stockholm_County\" title=\"Governor of Stockholm County\">Governor of Stockholm County</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Stockholm_County\" title=\"Governor of Stockholm County\">Governor of Stockholm County</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Stockholm County", "link": "https://wikipedia.org/wiki/Governor_of_Stockholm_County"}]}, {"year": "1950", "text": "<PERSON>, Canadian ice hockey player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actress and playwright", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American neurosurgeon, author, and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurosurgeon, author, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurosurgeon, author, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and bass player (d. 2002)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1951", "text": "<PERSON>, American football player and scout (d. 2007)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and scout (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and scout (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Swiss racing driver and sportscaster", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss racing driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss racing driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Greek politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American basketball player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American conductor and historian (d. 2009)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and historian (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and historian (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Pakistani politician (d. 1996)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Japanese engineer and astronaut", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Takao_Do<PERSON>\" title=\"Takao Doi\"><PERSON><PERSON><PERSON></a>, Japanese engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Takao_Do<PERSON>\" title=\"Takao Doi\"><PERSON><PERSON><PERSON></a>, Japanese engineer and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takao_Doi"}]}, {"year": "1954", "text": "<PERSON>, American basketball player and coach (d. 2007)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian-American psychologist, linguist, and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychologist, linguist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychologist, linguist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American football player, coach, and Senator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and Senator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and Senator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tommy_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English bishop", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Slovak ice hockey player and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0%C5%A5astn%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0%C5%A5astn%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Peter_%C5%A0%C5%A5astn%C3%BD"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Indian politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Anant_<PERSON>l\" title=\"Anant <PERSON>ad<PERSON>l\"><PERSON><PERSON></a>, Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anant_<PERSON>\" title=\"Anant <PERSON>\"><PERSON><PERSON></a>, Indian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anant_G<PERSON>gil"}]}, {"year": "1958", "text": "<PERSON>, English-Irish footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American football player and commentator", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Vincentian cricketer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English ecologist and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Malcolm_<PERSON>\" title=\"Malcolm Press\"><PERSON></a>, English ecologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malcolm_<PERSON>\" title=\"Malcolm Press\">Malcolm <PERSON></a>, English ecologist and academic", "links": [{"title": "Malcolm <PERSON>", "link": "https://wikipedia.org/wiki/Malcolm_Press"}]}, {"year": "1958", "text": "<PERSON>, Kenyan-English cricketer and journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English cricketer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English cricketer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English footballer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American director and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American baseball player, coach, and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>berg"}]}, {"year": "1960", "text": "<PERSON>, American composer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, British politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, British politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1960", "text": "<PERSON>, English lawyer and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Mexican wrestler", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Blue_Panther\" title=\"Blue Panther\">Blue Panther</a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Blue_Panther\" title=\"Blue Panther\"><PERSON> Panther</a>, Mexican wrestler", "links": [{"title": "Blue Panther", "link": "https://wikipedia.org/wiki/Blue_Panther"}]}, {"year": "1961", "text": "<PERSON>, American actor and producer (d. 2013)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Greek-American painter and illustrator", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1962", "text": "<PERSON>, English singer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and actor (d. 2019)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter, guitarist, and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter, guitarist, and actor (d. 2019)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1962", "text": "<PERSON>, Australian public servant and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Aden_Ridgeway\" title=\"Aden Ridgeway\">Aden Ridgeway</a>, Australian public servant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aden_Ridgeway\" title=\"Aden Ridgeway\">Aden Ridgeway</a>, Australian public servant and politician", "links": [{"title": "Aden Ridgeway", "link": "https://wikipedia.org/wiki/Aden_Ridgeway"}]}, {"year": "1962", "text": "<PERSON>, American race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Italian singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Peete\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Peete\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, German-American equestrian", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American equestrian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American ice hockey player and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Croatian basketball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Indian actor, director, and politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian triathlete", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian triathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Cappadonna\" title=\"<PERSON>padonna\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>na\" title=\"<PERSON>pad<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cappadonna"}]}, {"year": "1970", "text": "<PERSON>, English cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress, television host, and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, television host, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, television host, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American cyclist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian-Austrian soprano and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Austrian soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Austrian soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Belgian swimmer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Brig<PERSON> Becue\"><PERSON><PERSON><PERSON></a>, Belgian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Brigitte Becue\"><PERSON><PERSON><PERSON></a>, Belgian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1972", "text": "<PERSON>, English motorcycle racer (d. 2003)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Scottish accountant and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish accountant and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Jardel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Jardel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rio_Jardel"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Spanish footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian wheelchair racer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian wheelchair racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian wheelchair racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, South African-English businessman", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English footballer and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Campbell"}]}, {"year": "1974", "text": "<PERSON>, American football player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Portuguese-American basketball player and agent", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese-American basketball player and agent", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese-American basketball player and agent", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American rapper, actor, and television host", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Xzibit\" title=\"Xzibit\"><PERSON><PERSON><PERSON></a>, American rapper, actor, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xzibit\" title=\"Xzibit\"><PERSON><PERSON><PERSON></a>, American rapper, actor, and television host", "links": [{"title": "Xzibit", "link": "https://wikipedia.org/wiki/Xzibit"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian target shooter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian target shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON><PERSON> Lukas<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian target shooter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>kas<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor and comedian", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Su<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Su<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1975", "text": "<PERSON>, Costa Rican photographer and painter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican photographer and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican photographer and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian soccer player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Brazilian_footballer)\" title=\"<PERSON><PERSON> (Brazilian footballer)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Brazilian_footballer)\" title=\"<PERSON><PERSON> (Brazilian footballer)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (Brazilian footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Brazilian_footballer)"}]}, {"year": "1977", "text": "<PERSON>, American actor, singer, and dancer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Foa\"><PERSON></a>, American actor, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Foa\"><PERSON></a>, American actor, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, English rower", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Kieran_West\" title=\"Kieran West\"><PERSON><PERSON></a>, English rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kieran_West\" title=\"Kieran West\"><PERSON><PERSON></a>, English rower", "links": [{"title": "Kieran West", "link": "https://wikipedia.org/wiki/Kieran_West"}]}, {"year": "1978", "text": "<PERSON>, American actor and comedian", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, New Zealand politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Cameroonian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Spanish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American criminal (d. 2017)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Avi_<PERSON>rool\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avi_<PERSON>rool\" title=\"<PERSON><PERSON>l\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avi_Strool"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Finnish basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Virtanen\" title=\"<PERSON><PERSON> Virtanen\"><PERSON><PERSON></a>, Finnish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>irt<PERSON>n\" title=\"<PERSON><PERSON> Virtanen\"><PERSON><PERSON></a>, Finnish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Virt<PERSON>n"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Austrian politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ps_Valters\" title=\"Kristaps Valters\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Valters\" title=\"Kristaps Valters\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "Kristaps Valters", "link": "https://wikipedia.org/wiki/Kristaps_Valters"}]}, {"year": "1981", "text": "<PERSON>, South Korean actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> Ye-seul\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> Ye-seul\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ul"}]}, {"year": "1982", "text": "<PERSON>, Slovak ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Italian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Ar<PERSON>das_Eitutavi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ar<PERSON><PERSON>_Eitutavi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arvydas_Eitutavi%C4%8Dius"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Mexican wrestler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>o"}]}, {"year": "1982", "text": "<PERSON>, Mexican footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player and politician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American football player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American football player and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Outlaw\" title=\"Travis Outlaw\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Outlaw\" title=\"Travis Outlaw\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Travis_Outlaw"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, British hip hop musician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Dizzee_Rascal\" title=\"Dizzee Rascal\"><PERSON><PERSON><PERSON></a>, British hip hop musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dizzee_Rascal\" title=\"Dizzee Rascal\"><PERSON><PERSON><PERSON></a>, British hip hop musician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dizzee_Rascal"}]}, {"year": "1985", "text": "<PERSON>, Bosnian basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Teletovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Teletovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mirza_Teletovi%C4%87"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Congolese-Spanish basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese-Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese-Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American-Austrian actor and model", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Austrian actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Austrian actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Norwegian professional golfer ", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian professional golfer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian professional golfer ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American soccer player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor and musician", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "96", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. AD 51)", "html": "96 - <a href=\"https://wikipedia.org/wiki/Domitian\" title=\"Domitian\">Dom<PERSON><PERSON></a>, Roman emperor (b. AD 51)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Domitian\" title=\"Domitian\">Dom<PERSON>an</a>, Roman emperor (b. AD 51)", "links": [{"title": "Domitian", "link": "https://wikipedia.org/wiki/Domitian"}]}, {"year": "411", "text": "<PERSON>, Roman usurper", "html": "411 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_(Western_Roman_emperor)\" title=\"<PERSON> III (Western Roman emperor)\"><PERSON> III</a>, Roman usurper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_(Western_Roman_emperor)\" title=\"<PERSON> III (Western Roman emperor)\"><PERSON> III</a>, Roman usurper", "links": [{"title": "<PERSON> (Western Roman emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_III_(Western_Roman_emperor)"}]}, {"year": "869", "text": "<PERSON><PERSON>, Frankish archbishop", "html": "869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Rouen)\" title=\"<PERSON><PERSON> (archbishop of Rouen)\"><PERSON><PERSON></a>, Frankish archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Rouen)\" title=\"<PERSON><PERSON> (archbishop of Rouen)\"><PERSON><PERSON></a>, Frankish archbishop", "links": [{"title": "<PERSON><PERSON> (archbishop of Rouen)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Rouen)"}]}, {"year": "887", "text": "<PERSON>, doge of Venice (b. 842)", "html": "887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of Venice (b. 842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of Venice (b. 842)", "links": [{"title": "Pietro I <PERSON>diano", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>diano"}]}, {"year": "893", "text": "<PERSON>, Chinese warlord", "html": "893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "958", "text": "<PERSON>, Chinese emperor (b. 920)", "html": "958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Han)\" title=\"<PERSON> (Southern Han)\"><PERSON></a>, Chinese emperor (b. 920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Han)\" title=\"<PERSON> (Southern Han)\"><PERSON></a>, Chinese emperor (b. 920)", "links": [{"title": "<PERSON> (Southern Han)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Han)"}]}, {"year": "1137", "text": "<PERSON>, king of Denmark", "html": "1137 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON></a>, king of Denmark", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON></a>, king of Denmark", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1180", "text": "<PERSON>, king of France (b. 1120)", "html": "1180 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VII of France\"><PERSON></a>, king of France (b. 1120)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VII of France\"><PERSON></a>, king of France (b. 1120)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VII_of_France"}]}, {"year": "1261", "text": "<PERSON>, archbishop of Cologne", "html": "1261 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, archbishop of Cologne", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, archbishop of Cologne", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1302", "text": "<PERSON><PERSON><PERSON><PERSON>, empress of Trebizond (b. c. 1265)", "html": "1302 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_Palaiologina\" title=\"<PERSON>udo<PERSON><PERSON> Palaiologina\"><PERSON><PERSON><PERSON><PERSON></a>, empress of Trebizond (b. c. 1265)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Palaiologina\" title=\"Eudo<PERSON><PERSON> Palaiologina\"><PERSON><PERSON><PERSON><PERSON></a>, empress of Trebizond (b. c. 1265)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eudoki<PERSON>_Palaiologina"}]}, {"year": "1345", "text": "<PERSON>, Duke of Calabria (b. 1327)", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Calabria\" title=\"<PERSON>, Duke of Calabria\"><PERSON>, Duke of Calabria</a> (b. 1327)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Calabria\" title=\"<PERSON>, Duke of Calabria\"><PERSON>, Duke of Calabria</a> (b. 1327)", "links": [{"title": "<PERSON>, Duke of Calabria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Calabria"}]}, {"year": "1361", "text": "<PERSON>, duke of Bavaria (b. 1315)", "html": "1361 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria (b. 1315)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria (b. 1315)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1385", "text": "<PERSON><PERSON><PERSON>, ruler of Zeta", "html": "1385 - <a href=\"https://wikipedia.org/wiki/Bal%C5%A1a_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, ruler of Zeta", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bal%C5%A1a_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, ruler of Zeta", "links": [{"title": "Balša II", "link": "https://wikipedia.org/wiki/Bal%C5%A1a_II"}]}, {"year": "1443", "text": "<PERSON> of Luxembourg, archbishop of Rouen", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Luxembourg\" class=\"mw-redirect\" title=\"<PERSON> of Luxembourg\"><PERSON> of Luxembourg</a>, archbishop of Rouen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Luxembourg\" class=\"mw-redirect\" title=\"<PERSON> of Luxembourg\"><PERSON> of Luxembourg</a>, archbishop of Rouen", "links": [{"title": "<PERSON> of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON>_of_Luxembourg"}]}, {"year": "1598", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1536)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hideyoshi\" title=\"<PERSON><PERSON><PERSON> Hideyoshi\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hideyoshi\" title=\"<PERSON><PERSON><PERSON> Hideyoshi\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1536)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>deyoshi"}]}, {"year": "1630", "text": "<PERSON><PERSON><PERSON>, Austrian cardinal (b. 1552)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian cardinal (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian cardinal (b. 1552)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1675", "text": "<PERSON>, Duke of Lorraine (b. 1604)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1604)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1721", "text": "<PERSON>, English poet, politician, and diplomat, British Ambassador to France (b. 1664)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France\" title=\"List of ambassadors of the United Kingdom to France\">British Ambassador to France</a> (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France\" title=\"List of ambassadors of the United Kingdom to France\">British Ambassador to France</a> (b. 1664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of ambassadors of the United Kingdom to France", "link": "https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France"}]}, {"year": "1722", "text": "<PERSON>, French scholar and academic (b. 1651)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and academic (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Da<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and academic (b. 1651)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>cier"}]}, {"year": "1783", "text": "<PERSON><PERSON>, Swiss mathematician and physicist (b. 1707)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss mathematician and physicist (b. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss mathematician and physicist (b. 1707)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, English theologian and scholar (b. 1718)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and scholar (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and scholar (b. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1792", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, German bishop and theologian (b. 1704)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>ie<PERSON>_<PERSON>ngenberg\" title=\"August <PERSON><PERSON><PERSON><PERSON>\">August <PERSON><PERSON><PERSON><PERSON></a>, German bishop and theologian (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>ie<PERSON>_<PERSON>\" title=\"August <PERSON><PERSON>ie<PERSON>\">August <PERSON><PERSON><PERSON><PERSON></a>, German bishop and theologian (b. 1704)", "links": [{"title": "August <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ottoman politician, 186th Grand Vizier of the Ottoman Empire (b. 1743)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Safranbolulu_I<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Safranbolulu Izzet Mehm<PERSON>\">Safranbolulu Izzet <PERSON></a>, Ottoman politician, 186th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Safranbolulu_Izzet_Mehm<PERSON>_<PERSON>\" title=\"Safranbolulu Izzet Mehmet Pasha\">Safranbolulu Izzet <PERSON></a>, Ottoman politician, 186th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1743)", "links": [{"title": "Safranbolulu Izzet <PERSON>", "link": "https://wikipedia.org/wiki/Safranbolulu_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1830", "text": "<PERSON>, English philosopher, painter, and critic (b. 1778)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, painter, and critic (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, painter, and critic (b. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON>, Polish composer and conductor (b. 1785)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish composer and conductor (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish composer and conductor (b. 1785)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%84ski"}]}, {"year": "1860", "text": "<PERSON>, English engineer and politician (b. 1805)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American general (b. 1803)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON> of Sweden (b. 1826)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XV_of_Sweden\" class=\"mw-redirect\" title=\"Charles XV of Sweden\"><PERSON> of Sweden</a> (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XV_of_Sweden\" class=\"mw-redirect\" title=\"Charles XV of Sweden\"><PERSON> of Sweden</a> (b. 1826)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_XV_of_Sweden"}]}, {"year": "1890", "text": "<PERSON>, Irish-American actor and playwright (b. 1820)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor and playwright (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor and playwright (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, French physicist and academic (b. 1819)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Hippolyte_Fizeau\" title=\"Hippolyte Fizeau\"><PERSON><PERSON><PERSON></a>, French physicist and academic (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hippolyte_Fizeau\" title=\"Hippolyte Fizeau\"><PERSON><PERSON><PERSON></a>, French physicist and academic (b. 1819)", "links": [{"title": "Hippolyte <PERSON>au", "link": "https://wikipedia.org/wiki/Hippolyte_Fizeau"}]}, {"year": "1905", "text": "<PERSON>, Scottish minister, author, and poet (b. 1824)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister, author, and poet (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister, author, and poet (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Romanian archaeologist and historian (b. 1850)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian archaeologist and historian (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian archaeologist and historian (b. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Russian lawyer and politician, 3rd Prime Minister of Russia (b. 1862)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia\" title=\"List of heads of government of Russia\">Prime Minister of Russia</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia\" title=\"List of heads of government of Russia\">Prime Minister of Russia</a> (b. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of government of Russia", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia"}]}, {"year": "1915", "text": "<PERSON>, doctor, teacher, and social reformer, first Native American to earn a medical degree", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Picott<PERSON>\"><PERSON></a>, doctor, teacher, and social reformer, first <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> to earn a medical degree", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Picott<PERSON>\"><PERSON></a>, doctor, teacher, and social reformer, first <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> to earn a medical degree", "links": [{"title": "<PERSON> F<PERSON> Pi<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>e"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}]}, {"year": "1924", "text": "<PERSON><PERSON> <PERSON><PERSON>, English philosopher and author (b. 1846)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and author (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and author (b. 1846)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Polish author, painter, and photographer (b. 1885)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_I<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish author, painter, and photographer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_I<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish author, painter, and photographer (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English actor and screenwriter (b. 1866)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American colonel, Medal of Honor recipient (b. 1915)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Russian anarchist intellectual (b. 1882)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Volin\" title=\"Volin\"><PERSON><PERSON></a>, Russian anarchist intellectual (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volin\" title=\"Volin\"><PERSON><PERSON></a>, Russian anarchist intellectual (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Volin"}]}, {"year": "1949", "text": "<PERSON>, American actor (b. 1890)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American author and poet (b. 1866)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and poet (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and poet (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, New Zealand-Australian soprano and actress (b. 1879)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian soprano and actress (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian soprano and actress (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frances_Alda"}]}, {"year": "1953", "text": "<PERSON>, Belgian racing driver (b. 1927)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Canadian agronomist and politician, 15th Premier of Quebec (b. 1892)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Ad%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian agronomist and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Quebec_premiers\" class=\"mw-redirect\" title=\"List of Quebec premiers\">Premier of Quebec</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ad%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian agronomist and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Quebec_premiers\" class=\"mw-redirect\" title=\"List of Quebec premiers\">Premier of Quebec</a> (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ad%C3%A9<PERSON>_<PERSON>bout"}, {"title": "List of Quebec premiers", "link": "https://wikipedia.org/wiki/List_of_Quebec_premiers"}]}, {"year": "1958", "text": "<PERSON>, Norwegian painter and illustrator (b. 1873)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French poet and journalist (b. 1899)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ret\" title=\"<PERSON>\"><PERSON></a>, French poet and journalist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ret\" title=\"<PERSON>\"><PERSON></a>, French poet and journalist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benjamin_P%C3%A9ret"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Swedish economist and diplomat, 2nd Secretary-General of the United Nations, Nobel Prize laureate (b. 1905)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Dag_<PERSON>skj%C3%B6ld\" title=\"<PERSON><PERSON>j<PERSON>\"><PERSON><PERSON></a>, Swedish economist and diplomat, 2nd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dag_<PERSON>j%C3%B6ld\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish economist and diplomat, 2nd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dag_Hammarskj%C3%B6ld"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1962", "text": "<PERSON><PERSON>, German mystic (b. 1898)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/German_mystic\" class=\"mw-redirect\" title=\"German mystic\">German mystic</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/German_mystic\" class=\"mw-redirect\" title=\"German mystic\">German mystic</a> (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "German mystic", "link": "https://wikipedia.org/wiki/German_mystic"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Irish dramatist and memoirist (b. 1880)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_O%27Casey\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish dramatist and memoirist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_O%27Casey\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish dramatist and memoirist (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_O%27Casey"}]}, {"year": "1967", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (b. 1897)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, American actor, singer, and producer (b. 1905)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Franchot_Tone\" title=\"Franchot Tone\"><PERSON><PERSON><PERSON><PERSON></a>, American actor, singer, and producer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran<PERSON><PERSON>_<PERSON>\" title=\"Franchot Tone\"><PERSON><PERSON><PERSON><PERSON></a>, American actor, singer, and producer (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franchot_<PERSON>ne"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer (b. 1942)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Pakistani classical singer (b. 1922)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani classical singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani classical singer (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American painter and critic (b. 1907)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Fairfield_Porter\" title=\"<PERSON> Porter\"><PERSON></a>, American painter and critic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fairfield_Porter\" title=\"Fairfield Porter\"><PERSON></a>, American painter and critic (b. 1907)", "links": [{"title": "Fairfield Porter", "link": "https://wikipedia.org/wiki/Fairfield_Porter"}]}, {"year": "1977", "text": "<PERSON>, English-Swiss mathematician and philosopher (b. 1888)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Swiss mathematician and philosopher (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Swiss mathematician and philosopher (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American short story writer, novelist, and essayist (b. 1890)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, novelist, and essayist (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, novelist, and essayist (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese admiral and politician, 14th President of Portugal (b. 1894)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Am%C3%A9rico_Tom%C3%A1s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese admiral and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am%C3%A9rico_Tom%C3%A1s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese admiral and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am%C3%A9rico_Tom%C3%A1s"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1988", "text": "<PERSON>, Australian public servant and diplomat, Australian Ambassador to Japan (b. 1901)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Australian public servant and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Australian_Ambassadors_to_Japan\" class=\"mw-redirect\" title=\"List of Australian Ambassadors to Japan\">Australian Ambassador to Japan</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Australian public servant and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Australian_Ambassadors_to_Japan\" class=\"mw-redirect\" title=\"List of Australian Ambassadors to Japan\">Australian Ambassador to Japan</a> (b. 1901)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>(diplomat)"}, {"title": "List of Australian Ambassadors to Japan", "link": "https://wikipedia.org/wiki/List_of_Australian_Ambassadors_to_Japan"}]}, {"year": "1992", "text": "<PERSON>, Indian lawyer, judge, and politician, 6th Vice President of India (b. 1905)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian lawyer, judge, and politician, 6th <a href=\"https://wikipedia.org/wiki/Vice_President_of_India\" title=\"Vice President of India\">Vice President of India</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian lawyer, judge, and politician, 6th <a href=\"https://wikipedia.org/wiki/Vice_President_of_India\" title=\"Vice President of India\">Vice President of India</a> (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Vice President of India", "link": "https://wikipedia.org/wiki/Vice_President_of_India"}]}, {"year": "1997", "text": "<PERSON>, American singer (b. 1920)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer and guitarist (<PERSON><PERSON> and <PERSON>) (b. 1939)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a>) (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a>) (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American-Canadian television host (b. 1927)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian television host (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian television host (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American sprinter and football player (b. 1942)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Brazilian footballer and manager (b. 1930)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Serbian keyboard player (b. 1959)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian keyboard player (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian keyboard player (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "2003", "text": "<PERSON>, German rabbi and philosopher (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi and philosopher (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi and philosopher (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English educator and politician (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English educator and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English educator and politician (b. 1927)", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}]}, {"year": "2004", "text": "<PERSON>, Canadian-American historian and educator (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, Canadian-American historian and educator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Can<PERSON>\"><PERSON></a>, Canadian-American historian and educator (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American director, producer, and screenwriter (b. 1922)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English racing driver (b. 1966)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(co-driver)\" title=\"<PERSON> (co-driver)\"><PERSON></a>, English racing driver (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(co-driver)\" title=\"<PERSON> (co-driver)\"><PERSON></a>, English racing driver (b. 1966)", "links": [{"title": "<PERSON> (co-driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(co-driver)"}]}, {"year": "2005", "text": "<PERSON>, Sr., American cartoonist (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American cartoonist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American cartoonist (b. 1914)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr."}]}, {"year": "2006", "text": "<PERSON>, American football player, lawyer, and politician, 66th Governor of Massachusetts (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, lawyer, and politician, 66th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, lawyer, and politician, 66th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Welsh bass player and producer (b. 1965)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/P<PERSON>si_Tate\" title=\"Pepsi Tate\"><PERSON><PERSON><PERSON></a>, Welsh bass player and producer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pepsi_Tate\" title=\"Pep<PERSON> Tate\"><PERSON><PERSON><PERSON></a>, Welsh bass player and producer (b. 1965)", "links": [{"title": "Pepsi Tate", "link": "https://wikipedia.org/wiki/Pepsi_Tate"}]}, {"year": "2008", "text": "<PERSON>, Italian actor and director (b. 1940)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and director (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and director (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Argentinian-German composer and educator (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian-German composer and educator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian-German composer and educator (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American-Canadian football player and coach (b. 1938)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American teenage activist (b. 1997)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Suicide_of_<PERSON><PERSON>_<PERSON>\" title=\"Suicide of <PERSON><PERSON>\"><PERSON><PERSON></a>, American teenage <a href=\"https://wikipedia.org/wiki/Activist\" class=\"mw-redirect\" title=\"Activist\">activist</a> (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suicide_of_<PERSON><PERSON>_<PERSON>\" title=\"Suicide of <PERSON><PERSON>\"><PERSON><PERSON></a>, American teenage <a href=\"https://wikipedia.org/wiki/Activist\" class=\"mw-redirect\" title=\"Activist\">activist</a> (b. 1997)", "links": [{"title": "Suicide of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suicide_of_<PERSON><PERSON>_<PERSON>"}, {"title": "Activist", "link": "https://wikipedia.org/wiki/Activist"}]}, {"year": "2012", "text": "<PERSON>, Spanish theorist and politician (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Santiago_Carrillo\" title=\"Santiago Carrillo\"><PERSON></a>, Spanish theorist and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Carrillo\" title=\"Santiago Carrillo\"><PERSON></a>, Spanish theorist and politician (b. 1915)", "links": [{"title": "Santiago Carrillo", "link": "https://wikipedia.org/wiki/Santiago_Carrillo"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Polish-Israeli songwriter and poet (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli songwriter and poet (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli songwriter and poet (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American director and producer, co-founded NFL Films (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, co-founded <a href=\"https://wikipedia.org/wiki/NFL_Films\" title=\"NFL Films\">NFL Films</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, co-founded <a href=\"https://wikipedia.org/wiki/NFL_Films\" title=\"NFL Films\">NFL Films</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NFL Films", "link": "https://wikipedia.org/wiki/NFL_Films"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian politician (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English composer, bassoon and oboe player (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, bassoon and oboe player (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, bassoon and oboe player (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French-Canadian director, producer, and screenwriter (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian director, producer, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian director, producer, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American boxer (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Polish-German author and critic (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German author and critic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German author and critic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actor, director, and screenwriter (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian ice hockey player (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Milan_Marc<PERSON>\" title=\"Milan Marcetta\"><PERSON></a>, Canadian ice hockey player (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Marcetta\" title=\"Milan Marcetta\"><PERSON></a>, Canadian ice hockey player (b. 1936)", "links": [{"title": "Milan <PERSON>", "link": "https://wikipedia.org/wiki/Milan_Marcetta"}]}, {"year": "2014", "text": "<PERSON>, Canadian racing driver (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian racing driver (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian racing driver (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese economist and academic (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>zawa\" title=\"<PERSON><PERSON><PERSON><PERSON>zawa\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese economist and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese economist and academic (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Uzawa"}]}, {"year": "2014", "text": "<PERSON>, Canadian-English trumpet player and composer (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English trumpet player and composer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English trumpet player and composer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chilean footballer and manager (b. 1955)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer and manager (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer and manager (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American astrophysicist and academic (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Argentinian general and politician (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Men%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Men%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mario_Benjam%C3%ADn_Men%C3%A9ndez"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Pakistani writer, poet, translator and playwright (b. 1937)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani writer, poet, translator and playwright (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani writer, poet, translator and playwright (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, United States Supreme Court justice (b. 1933)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Supreme Court justice (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Supreme Court justice (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, South African politician, 97th Mayor of Johannesburg (b. 1975)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician, 97th <a href=\"https://wikipedia.org/wiki/Mayor_of_Johannesburg\" title=\"Mayor of Johannesburg\">Mayor of Johannesburg</a> (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician, 97th <a href=\"https://wikipedia.org/wiki/Mayor_of_Johannesburg\" title=\"Mayor of Johannesburg\">Mayor of Johannesburg</a> (b. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}, {"title": "Mayor of Johannesburg", "link": "https://wikipedia.org/wiki/Mayor_of_Johannesburg"}]}, {"year": "2021", "text": "<PERSON>, Danish road bicycle racer (b. 1984) ", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish road bicycle racer (b. 1984) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish road bicycle racer (b. 1984) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B8<PERSON><PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, American politician, 58th Governor of Kentucky (b. 1939)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Brereton_<PERSON><PERSON>_<PERSON>\" title=\"Brereton C<PERSON> Jones\">Brereton <PERSON><PERSON></a>, American politician, 58th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brereton_<PERSON><PERSON>_<PERSON>\" title=\"Brereton C<PERSON> Jones\">Brereton <PERSON><PERSON></a>, American politician, 58th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (b. 1939)", "links": [{"title": "Brereton <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Brereton_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Kentucky", "link": "https://wikipedia.org/wiki/Governor_of_Kentucky"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Georgian blogger, actress and model (b. 1987)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Kesar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian blogger, actress and model (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kesar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian blogger, actress and model (b. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kesar<PERSON>_<PERSON>ze"}]}, {"year": "2024", "text": "<PERSON>, American singer-songwriter (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Italian footballer (b. 1964)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}