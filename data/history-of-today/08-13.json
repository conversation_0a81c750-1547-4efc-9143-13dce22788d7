{"date": "August 13", "url": "https://wikipedia.org/wiki/August_13", "data": {"Events": [{"year": "29 BC", "text": "<PERSON><PERSON><PERSON> holds the first of three consecutive triumphs in Rome to celebrate the victory over the Dalmatian tribes.", "html": "29 BC - 29 BC - <a href=\"https://wikipedia.org/wiki/Octavian\" class=\"mw-redirect\" title=\"Octavian\">Octavian</a> holds the first of three consecutive <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumphs</a> in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> to celebrate the victory over the <a href=\"https://wikipedia.org/wiki/Dalmatian_tribes\" class=\"mw-redirect\" title=\"Dalmatian tribes\">Dalmatian tribes</a>.", "no_year_html": "29 BC - <a href=\"https://wikipedia.org/wiki/Octavian\" class=\"mw-redirect\" title=\"Octavian\">Octavian</a> holds the first of three consecutive <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumphs</a> in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> to celebrate the victory over the <a href=\"https://wikipedia.org/wiki/Dalmatian_tribes\" class=\"mw-redirect\" title=\"Dalmatian tribes\">Dalmatian tribes</a>.", "links": [{"title": "Octavian", "link": "https://wikipedia.org/wiki/Octavian"}, {"title": "Roman triumph", "link": "https://wikipedia.org/wiki/Roman_triumph"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "Dalmatian tribes", "link": "https://wikipedia.org/wiki/Dalmatian_tribes"}]}, {"year": "523", "text": "<PERSON> becomes the new Pope after the death of <PERSON> <PERSON><PERSON><PERSON><PERSON>.", "html": "523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John I\"><PERSON> I</a> becomes the new <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">Pope</a> after the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_I\" title=\"Pope John I\"><PERSON> I</a> becomes the new <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">Pope</a> after the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "554", "text": "Emperor <PERSON><PERSON> rewards <PERSON><PERSON><PERSON> for his service in the Pragmatic Sanction, granting him extensive estates in Italy.", "html": "554 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I\" title=\"<PERSON>ian I\"><PERSON><PERSON> I</a> rewards <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(praetorian_prefect)\" title=\"<PERSON><PERSON><PERSON> (praetorian prefect)\"><PERSON><PERSON><PERSON></a> for his service in the <a href=\"https://wikipedia.org/wiki/Pragmatic_Sanction_of_<PERSON>ian_I\" class=\"mw-redirect\" title=\"Pragmatic Sanction of <PERSON>ian I\">Pragmatic Sanction</a>, granting him extensive <a href=\"https://wikipedia.org/wiki/Estate_(land)\" title=\"Estate (land)\">estates</a> in Italy.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I\" title=\"<PERSON>ian I\"><PERSON><PERSON> I</a> rewards <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(praetorian_prefect)\" title=\"<PERSON><PERSON><PERSON> (praetorian prefect)\"><PERSON><PERSON><PERSON></a> for his service in the <a href=\"https://wikipedia.org/wiki/Pragmatic_Sanction_of_Justinian_I\" class=\"mw-redirect\" title=\"Pragmatic Sanction of <PERSON>ian I\">Pragmatic Sanction</a>, granting him extensive <a href=\"https://wikipedia.org/wiki/Estate_(land)\" title=\"Estate (land)\">estates</a> in Italy.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> (praetorian prefect)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(praetorian_prefect)"}, {"title": "Pragmatic Sanction of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pragmatic_Sanction_of_<PERSON><PERSON>_I"}, {"title": "Estate (land)", "link": "https://wikipedia.org/wiki/Estate_(land)"}]}, {"year": "582", "text": "<PERSON> becomes Emperor of the Byzantine Empire.", "html": "582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor</a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor</a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "871", "text": "Emperor <PERSON> of Italy and Empress <PERSON><PERSON><PERSON><PERSON> are captured by Prince <PERSON><PERSON><PERSON><PERSON> of Benevento.", "html": "871 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Italy\" title=\"<PERSON> II of Italy\"><PERSON> of Italy</a> and Empress <a href=\"https://wikipedia.org/wiki/Engelberga\" title=\"Engelberga\">Engel<PERSON><PERSON></a> are captured by Prince <a href=\"https://wikipedia.org/wiki/Adelchis_of_Benevento\" title=\"Adel<PERSON><PERSON> of Benevento\"><PERSON><PERSON><PERSON><PERSON> of Benevento</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Italy\" title=\"<PERSON> II of Italy\"><PERSON> of Italy</a> and Empress <a href=\"https://wikipedia.org/wiki/Engelberga\" title=\"Engelberga\">Engelberg<PERSON></a> are captured by Prince <a href=\"https://wikipedia.org/wiki/Adelchis_of_Benevento\" title=\"<PERSON><PERSON><PERSON><PERSON> of Benevento\"><PERSON><PERSON><PERSON><PERSON> of Benevento</a>.", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/Louis_II_of_Italy"}, {"title": "Engelberga", "link": "https://wikipedia.org/wiki/Engelberga"}, {"title": "<PERSON><PERSON><PERSON><PERSON> of Benevento", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Benevento"}]}, {"year": "900", "text": "Count <PERSON><PERSON> of Hainault rises against <PERSON><PERSON><PERSON><PERSON><PERSON> of Lotharingia and slays him near present-day Susteren.", "html": "900 - Count <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duke_of_Lorraine\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Duke of Lorraine\"><PERSON><PERSON> of Hainault</a> rises against <a href=\"https://wikipedia.org/wiki/Zwentibold\" title=\"Zwentibold\">Zwentibold</a> of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lotharingia</a> and slays him near present-day <a href=\"https://wikipedia.org/wiki/Susteren\" title=\"Susteren\">Susteren</a>.", "no_year_html": "Count <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duke_of_Lorraine\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Duke of Lorraine\"><PERSON><PERSON> of Hainault</a> rises against <a href=\"https://wikipedia.org/wiki/Zwentibold\" title=\"Zwentibold\">Zwentibold</a> of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lotharingia</a> and slays him near present-day <a href=\"https://wikipedia.org/wiki/Susteren\" title=\"Susteren\">Susteren</a>.", "links": [{"title": "<PERSON><PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON>_of_Lorraine"}, {"title": "Zwentibold", "link": "https://wikipedia.org/wiki/Zwentibold"}, {"title": "Lotharingia", "link": "https://wikipedia.org/wiki/Lotharingia"}, {"title": "Susteren", "link": "https://wikipedia.org/wiki/Susteren"}]}, {"year": "1099", "text": "<PERSON><PERSON> is elected as <PERSON> <PERSON><PERSON><PERSON>, who would become deeply entangled in the Investiture Controversy.", "html": "1099 - <PERSON><PERSON> is elected as <a href=\"https://wikipedia.org/wiki/Pope_Paschal_II\" title=\"Pope Paschal II\"><PERSON> II</a>, who would become deeply entangled in the <a href=\"https://wikipedia.org/wiki/Investiture_Controversy\" title=\"Investiture Controversy\">Investiture Controversy</a>.", "no_year_html": "<PERSON><PERSON> is elected as <a href=\"https://wikipedia.org/wiki/Pope_Paschal_II\" title=\"Pope Paschal II\"><PERSON> II</a>, who would become deeply entangled in the <a href=\"https://wikipedia.org/wiki/Investiture_Controversy\" title=\"Investiture Controversy\">Investiture Controversy</a>.", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_II"}, {"title": "Investiture Controversy", "link": "https://wikipedia.org/wiki/Investiture_Controversy"}]}, {"year": "1516", "text": "The Treaty of Noyon between France and Spain is signed. <PERSON> of France recognizes <PERSON>'s claim to Naples, and <PERSON>, Holy Roman Emperor, recognizes <PERSON>'s claim to Milan.", "html": "1516 - The <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cambrai\" title=\"War of the League of Cambrai\">Treaty of Noyon</a> between France and Spain is signed. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> recognizes <PERSON>'s claim to <a href=\"https://wikipedia.org/wiki/Naples\" title=\"Naples\">Naples</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, recognizes <PERSON>'s claim to <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cambrai\" title=\"War of the League of Cambrai\">Treaty of Noyon</a> between France and Spain is signed. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> recognizes <PERSON>'s claim to <a href=\"https://wikipedia.org/wiki/Naples\" title=\"Naples\">Naples</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, recognizes <PERSON>'s claim to <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>.", "links": [{"title": "War of the League of Cambrai", "link": "https://wikipedia.org/wiki/War_of_the_League_of_Cambrai"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Naples", "link": "https://wikipedia.org/wiki/Naples"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}]}, {"year": "1521", "text": "After an extended siege, forces led by Spanish conquistador <PERSON><PERSON><PERSON> Cortés capture Tlatoani Cuauhtémoc and conquer the Aztec capital of Tenochtitlan.", "html": "1521 - After <a href=\"https://wikipedia.org/wiki/Fall_of_Tenochtitlan\" title=\"Fall of Tenochtitlan\">an extended siege</a>, forces led by Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistador</a> <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s\" title=\"Hernán Cortés\">Her<PERSON><PERSON></a> capture <a href=\"https://wikipedia.org/wiki/Tlatoani\" title=\"Tlatoani\">Tlatoani</a> <a href=\"https://wikipedia.org/wiki/Cuauht%C3%A9moc\" title=\"Cuauhtémoc\">Cuauhtémoc</a> and conquer the <a href=\"https://wikipedia.org/wiki/Aztec\" class=\"mw-redirect\" title=\"Aztec\">Aztec</a> capital of <a href=\"https://wikipedia.org/wiki/Tenochtitlan\" title=\"Tenochtitlan\">Tenochtitlan</a>.", "no_year_html": "After <a href=\"https://wikipedia.org/wiki/Fall_of_Tenochtitlan\" title=\"Fall of Tenochtitlan\">an extended siege</a>, forces led by Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistador</a> <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s\" title=\"Hernán Cortés\">Her<PERSON><PERSON></a> capture <a href=\"https://wikipedia.org/wiki/Tlatoani\" title=\"Tlatoani\">Tlatoani</a> <a href=\"https://wikipedia.org/wiki/Cuauht%C3%A9moc\" title=\"Cuauhtémoc\">Cuauhtémoc</a> and conquer the <a href=\"https://wikipedia.org/wiki/Aztec\" class=\"mw-redirect\" title=\"Aztec\">Aztec</a> capital of <a href=\"https://wikipedia.org/wiki/Tenochtitlan\" title=\"Tenochtitlan\">Tenochtitlan</a>.", "links": [{"title": "Fall of Tenochtitlan", "link": "https://wikipedia.org/wiki/Fall_of_Tenochtitlan"}, {"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>oani"}, {"title": "Cuauhtémoc", "link": "https://wikipedia.org/wiki/Cuauht%C3%A9moc"}, {"title": "Aztec", "link": "https://wikipedia.org/wiki/Aztec"}, {"title": "Tenochtitlan", "link": "https://wikipedia.org/wiki/Tenochtitlan"}]}, {"year": "1532", "text": "Union of Brittany and France: The Duchy of Brittany is absorbed into the Kingdom of France.", "html": "1532 - <a href=\"https://wikipedia.org/wiki/Union_of_Brittany_and_France\" class=\"mw-redirect\" title=\"Union of Brittany and France\">Union of Brittany and France</a>: The <a href=\"https://wikipedia.org/wiki/Duchy_of_Brittany\" title=\"Duchy of Brittany\">Duchy of Brittany</a> is absorbed into the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">Kingdom of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Union_of_Brittany_and_France\" class=\"mw-redirect\" title=\"Union of Brittany and France\">Union of Brittany and France</a>: The <a href=\"https://wikipedia.org/wiki/Duchy_of_Brittany\" title=\"Duchy of Brittany\">Duchy of Brittany</a> is absorbed into the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">Kingdom of France</a>.", "links": [{"title": "Union of Brittany and France", "link": "https://wikipedia.org/wiki/Union_of_Brittany_and_France"}, {"title": "Duchy of Brittany", "link": "https://wikipedia.org/wiki/Duchy_of_Brittany"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}]}, {"year": "1536", "text": "Buddhist monks from Kyoto, Japan's Enryaku-ji temple set fire to 21 Nichiren temples throughout Kyoto in what will be known as the Tenbun Hokke Disturbance.", "html": "1536 - Buddhist monks from <a href=\"https://wikipedia.org/wiki/Kyoto\" title=\"Kyoto\">Kyoto</a>, Japan's <a href=\"https://wikipedia.org/wiki/Enryaku-ji\" title=\"Enryaku-ji\">Enryaku-ji</a> temple set fire to 21 <a href=\"https://wikipedia.org/wiki/Nichiren_Buddhism\" title=\"Nichiren Buddhism\">Nichiren</a> temples throughout Kyoto in what will be known as the Tenbun Hokke Disturbance.", "no_year_html": "Buddhist monks from <a href=\"https://wikipedia.org/wiki/Kyoto\" title=\"Kyoto\">Kyoto</a>, Japan's <a href=\"https://wikipedia.org/wiki/Enryaku-ji\" title=\"Enryaku-ji\">Enryaku-ji</a> temple set fire to 21 <a href=\"https://wikipedia.org/wiki/Nichiren_Buddhism\" title=\"Nichiren Buddhism\">Nichiren</a> temples throughout Kyoto in what will be known as the Tenbun Hokke Disturbance.", "links": [{"title": "Kyoto", "link": "https://wikipedia.org/wiki/Kyoto"}, {"title": "Enryaku-ji", "link": "https://wikipedia.org/wiki/Enryaku-ji"}, {"title": "Nichiren Buddhism", "link": "https://wikipedia.org/wiki/Ni<PERSON>ren_Buddhism"}]}, {"year": "1553", "text": "<PERSON> is arrested by <PERSON> in Geneva, Switzerland as a heretic.", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, Switzerland as a <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heretic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, Switzerland as a <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heretic</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "Heresy in Christianity", "link": "https://wikipedia.org/wiki/Heresy_in_Christianity"}]}, {"year": "1624", "text": "The French king <PERSON> appoints <PERSON> as prime minister.", "html": "1624 - The French king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as prime minister.", "no_year_html": "The French king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as prime minister.", "links": [{"title": "Louis XIII", "link": "https://wikipedia.org/wiki/Louis_XIII"}, {"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "Sweden and Denmark sign Peace of Brömsebro.", "html": "1645 - Sweden and Denmark sign <a href=\"https://wikipedia.org/wiki/Peace_of_Br%C3%B6msebro\" class=\"mw-redirect\" title=\"Peace of Brömsebro\">Peace of Brömsebro</a>.", "no_year_html": "Sweden and Denmark sign <a href=\"https://wikipedia.org/wiki/Peace_of_Br%C3%B6msebro\" class=\"mw-redirect\" title=\"Peace of Brömsebro\">Peace of Brömsebro</a>.", "links": [{"title": "Peace of Brömsebro", "link": "https://wikipedia.org/wiki/Peace_of_Br%C3%B6msebro"}]}, {"year": "1650", "text": "Colonel <PERSON> of the English Army forms <PERSON><PERSON>'s Regiment of Foot, which will later become the Coldstream Guards.", "html": "1650 - Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle\" title=\"<PERSON>, 1st Duke of Albemarle\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/English_Army\" title=\"English Army\">English Army</a> forms <PERSON><PERSON>'s Regiment of Foot, which will later become the <a href=\"https://wikipedia.org/wiki/Coldstream_Guards\" title=\"Coldstream Guards\">Coldstream Guards</a>.", "no_year_html": "Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle\" title=\"<PERSON>, 1st Duke of Albemarle\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/English_Army\" title=\"English Army\">English Army</a> forms <PERSON><PERSON>'s Regiment of Foot, which will later become the <a href=\"https://wikipedia.org/wiki/Coldstream_Guards\" title=\"Coldstream Guards\">Coldstream Guards</a>.", "links": [{"title": "<PERSON>, 1st Duke of Albemarle", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle"}, {"title": "English Army", "link": "https://wikipedia.org/wiki/English_Army"}, {"title": "Coldstream Guards", "link": "https://wikipedia.org/wiki/Coldstream_Guards"}]}, {"year": "1704", "text": "War of the Spanish Succession: Battle of Blenheim: English and Imperial forces are victorious over French and Bavarian troops.", "html": "1704 - <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Blenheim\" title=\"Battle of Blenheim\">Battle of Blenheim</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> and <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Imperial</a> forces are victorious over <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> and <a href=\"https://wikipedia.org/wiki/Electorate_of_Bavaria\" title=\"Electorate of Bavaria\">Bavarian</a> troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Blenheim\" title=\"Battle of Blenheim\">Battle of Blenheim</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> and <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Imperial</a> forces are victorious over <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> and <a href=\"https://wikipedia.org/wiki/Electorate_of_Bavaria\" title=\"Electorate of Bavaria\">Bavarian</a> troops.", "links": [{"title": "War of the Spanish Succession", "link": "https://wikipedia.org/wiki/War_of_the_Spanish_Succession"}, {"title": "Battle of Blenheim", "link": "https://wikipedia.org/wiki/Battle_of_Blenheim"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Electorate of Bavaria", "link": "https://wikipedia.org/wiki/Electorate_of_Bavaria"}]}, {"year": "1724", "text": "<PERSON> leads the first performance of <PERSON><PERSON>, <PERSON>, <PERSON>, BWV 101, a chorale cantata on a famous tune.", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_von_<PERSON>,_<PERSON>,_<PERSON>_t<PERSON><PERSON>_<PERSON>,_BWV_101\" title=\"<PERSON><PERSON> von un<PERSON>, <PERSON>, du treuer Gott, BWV 101\"><i><PERSON><PERSON> von un<PERSON>, <PERSON>, du treuer <PERSON></i>, BWV 101</a>, a <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata</a> on <a href=\"https://wikipedia.org/wiki/Vater_unser_im_Himmelreich\" title=\"Vater unser im Himmelreich\">a famous tune</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON>,_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_BWV_101\" title=\"<PERSON><PERSON> von <PERSON>, <PERSON>, du treuer Got<PERSON>, BWV 101\"><i><PERSON><PERSON> von <PERSON>, <PERSON>, du treuer <PERSON></i>, BWV 101</a>, a <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata</a> on <a href=\"https://wikipedia.org/wiki/Vater_unser_im_Himmelreich\" title=\"Vater unser im Himmelreich\">a famous tune</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON>, <PERSON>, <PERSON> <PERSON><PERSON><PERSON>, BWV 101", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON>,_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_101"}, {"title": "Chorale cantata cycle", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_cantata_cycle"}, {"title": "Vater unser im Himmelreich", "link": "https://wikipedia.org/wiki/Vater_unser_im_Himmel<PERSON>ich"}]}, {"year": "1779", "text": "American Revolutionary War: The Royal Navy defeats the Penobscot Expedition with the most significant loss of United States naval forces prior to the attack on Pearl Harbor.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeats the <a href=\"https://wikipedia.org/wiki/Penobscot_Expedition\" title=\"Penobscot Expedition\">Penobscot Expedition</a> with the most significant loss of United States naval forces prior to the <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">attack on Pearl Harbor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeats the <a href=\"https://wikipedia.org/wiki/Penobscot_Expedition\" title=\"Penobscot Expedition\">Penobscot Expedition</a> with the most significant loss of United States naval forces prior to the <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">attack on Pearl Harbor</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Penobscot Expedition", "link": "https://wikipedia.org/wiki/Penobscot_Expedition"}, {"title": "Attack on Pearl Harbor", "link": "https://wikipedia.org/wiki/Attack_on_Pearl_Harbor"}]}, {"year": "1792", "text": "King <PERSON> of France is formally arrested by the National Tribunal, and declared an enemy of the people.", "html": "1792 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> XVI\"><PERSON></a> of France is formally arrested by the National Tribunal, and declared an <a href=\"https://wikipedia.org/wiki/Enemy_of_the_people\" title=\"Enemy of the people\">enemy of the people</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis XVI\"><PERSON></a> of France is formally arrested by the National Tribunal, and declared an <a href=\"https://wikipedia.org/wiki/Enemy_of_the_people\" title=\"Enemy of the people\">enemy of the people</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Enemy of the people", "link": "https://wikipedia.org/wiki/Enemy_of_the_people"}]}, {"year": "1806", "text": "Battle of Mišar during the Serbian Revolution begins. The battle ends two days later with a Serbian victory over the Ottomans.", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Battle_of_Mi%C5%A1ar\" title=\"Battle of Mišar\">Battle of Mišar</a> during the <a href=\"https://wikipedia.org/wiki/Serbian_Revolution\" title=\"Serbian Revolution\">Serbian Revolution</a> begins. The battle ends two days later with a Serbian victory over the Ottomans.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Mi%C5%A1ar\" title=\"Battle of Mišar\">Battle of Mišar</a> during the <a href=\"https://wikipedia.org/wiki/Serbian_Revolution\" title=\"Serbian Revolution\">Serbian Revolution</a> begins. The battle ends two days later with a Serbian victory over the Ottomans.", "links": [{"title": "Battle of Mišar", "link": "https://wikipedia.org/wiki/Battle_of_Mi%C5%A1ar"}, {"title": "Serbian Revolution", "link": "https://wikipedia.org/wiki/Serbian_Revolution"}]}, {"year": "1814", "text": "The Convention of London, a treaty between the United Kingdom and the United Netherlands, is signed in London, England.", "html": "1814 - The <a href=\"https://wikipedia.org/wiki/Anglo-Dutch_Treaty_of_1814\" title=\"Anglo-Dutch Treaty of 1814\">Convention of London</a>, a treaty between the United Kingdom and the <a href=\"https://wikipedia.org/wiki/Sovereign_Principality_of_the_United_Netherlands\" title=\"Sovereign Principality of the United Netherlands\">United Netherlands</a>, is signed in London, England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anglo-Dutch_Treaty_of_1814\" title=\"Anglo-Dutch Treaty of 1814\">Convention of London</a>, a treaty between the United Kingdom and the <a href=\"https://wikipedia.org/wiki/Sovereign_Principality_of_the_United_Netherlands\" title=\"Sovereign Principality of the United Netherlands\">United Netherlands</a>, is signed in London, England.", "links": [{"title": "Anglo-Dutch Treaty of 1814", "link": "https://wikipedia.org/wiki/Anglo-Dutch_Treaty_of_1814"}, {"title": "Sovereign Principality of the United Netherlands", "link": "https://wikipedia.org/wiki/Sovereign_Principality_of_the_United_Netherlands"}]}, {"year": "1868", "text": "The 8.5-9.0 Mw  Arica earthquake struck southern Peru with a maximum Mercalli intensity of XI (Extreme), causing 25,000+ deaths and a destructive basin wide tsunami that affected Hawaii and New Zealand.", "html": "1868 - The 8.5-9.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1868_Arica_earthquake\" title=\"1868 Arica earthquake\">Arica earthquake</a> struck southern <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), causing 25,000+ deaths and a destructive basin wide <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> that affected Hawaii and New Zealand.", "no_year_html": "The 8.5-9.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1868_Arica_earthquake\" title=\"1868 Arica earthquake\">Arica earthquake</a> struck southern <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), causing 25,000+ deaths and a destructive basin wide <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> that affected Hawaii and New Zealand.", "links": [{"title": "1868 Arica earthquake", "link": "https://wikipedia.org/wiki/1868_Arica_earthquake"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}]}, {"year": "1889", "text": "<PERSON> of Hartford, Connecticut is granted United States Patent Number 408,709 for \"Coin-controlled apparatus for telephones.\"", "html": "1889 - <PERSON> of <a href=\"https://wikipedia.org/wiki/Hartford,_Connecticut\" title=\"Hartford, Connecticut\">Hartford, Connecticut</a> is granted United States Patent Number 408,709 for \"Coin-controlled apparatus for telephones.\"", "no_year_html": "<PERSON> of <a href=\"https://wikipedia.org/wiki/Hartford,_Connecticut\" title=\"Hartford, Connecticut\">Hartford, Connecticut</a> is granted United States Patent Number 408,709 for \"Coin-controlled apparatus for telephones.\"", "links": [{"title": "Hartford, Connecticut", "link": "https://wikipedia.org/wiki/Hartford,_Connecticut"}]}, {"year": "1898", "text": "Spanish-American War: Spanish and American forces engage in a mock battle for Manila, after which the Spanish commander surrendered in order to keep the city out of Filipino rebel hands.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: Spanish and American forces engage in a <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_(1898)\" title=\"Battle of Manila (1898)\">mock battle</a> for <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>, after which the Spanish commander surrendered in order to keep the city out of Filipino rebel hands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: Spanish and American forces engage in a <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_(1898)\" title=\"Battle of Manila (1898)\">mock battle</a> for <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>, after which the Spanish commander surrendered in order to keep the city out of Filipino rebel hands.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "Battle of Manila (1898)", "link": "https://wikipedia.org/wiki/Battle_of_Manila_(1898)"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}]}, {"year": "1898", "text": "<PERSON> discovers 433 Eros, the first near-Earth asteroid to be found.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/433_Eros\" title=\"433 Eros\">433 Eros</a>, the first <a href=\"https://wikipedia.org/wiki/Near-Earth_asteroid\" class=\"mw-redirect\" title=\"Near-Earth asteroid\">near-Earth asteroid</a> to be found.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/433_Eros\" title=\"433 Eros\">433 Eros</a>, the first <a href=\"https://wikipedia.org/wiki/Near-Earth_asteroid\" class=\"mw-redirect\" title=\"Near-Earth asteroid\">near-Earth asteroid</a> to be found.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "433 <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/433_Eros"}, {"title": "Near-Earth asteroid", "link": "https://wikipedia.org/wiki/Near-Earth_asteroid"}]}, {"year": "1900", "text": "The steamer Deutschland of Hamburg America Lines set a new record for the eastward passage when it docked on Plymouth, England, five days, 11 hours and 45 minutes after sailing from New York, breaking by three hours, six minutes its previous mark in its maiden voyage in July.", "html": "1900 - The steamer <a href=\"https://wikipedia.org/wiki/SS_Deutschland_(1900)\" title=\"SS Deutschland (1900)\">Deutschland</a> of <a href=\"https://wikipedia.org/wiki/Hamburg_America_Line\" title=\"Hamburg America Line\">Hamburg America Lines</a> set a new record for the eastward passage when it docked on <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a>, England, five days, 11 hours and 45 minutes after sailing from New York, breaking by three hours, six minutes its previous mark in its maiden voyage in July.", "no_year_html": "The steamer <a href=\"https://wikipedia.org/wiki/SS_Deutschland_(1900)\" title=\"SS Deutschland (1900)\">Deutschland</a> of <a href=\"https://wikipedia.org/wiki/Hamburg_America_Line\" title=\"Hamburg America Line\">Hamburg America Lines</a> set a new record for the eastward passage when it docked on <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a>, England, five days, 11 hours and 45 minutes after sailing from New York, breaking by three hours, six minutes its previous mark in its maiden voyage in July.", "links": [{"title": "SS Deutschland (1900)", "link": "https://wikipedia.org/wiki/SS_Deutschland_(1900)"}, {"title": "Hamburg America Line", "link": "https://wikipedia.org/wiki/Hamburg_America_Line"}, {"title": "Plymouth", "link": "https://wikipedia.org/wiki/Plymouth"}]}, {"year": "1905", "text": "Norwegians vote to end the union with Sweden.", "html": "1905 - Norwegians <a href=\"https://wikipedia.org/wiki/1905_Norwegian_union_dissolution_referendum\" title=\"1905 Norwegian union dissolution referendum\">vote</a> to end the union with Sweden.", "no_year_html": "Norwegians <a href=\"https://wikipedia.org/wiki/1905_Norwegian_union_dissolution_referendum\" title=\"1905 Norwegian union dissolution referendum\">vote</a> to end the union with Sweden.", "links": [{"title": "1905 Norwegian union dissolution referendum", "link": "https://wikipedia.org/wiki/1905_Norwegian_union_dissolution_referendum"}]}, {"year": "1906", "text": "The all black infantrymen of the U.S. Army's 25th Infantry Regiment are accused of killing a white bartender and wounding a white police officer in Brownsville, Texas, despite exculpatory evidence; all are later dishonorably discharged. (Their records were later restored to reflect honorable discharges but there were no financial settlements.)", "html": "1906 - The all black infantrymen of the U.S. Army's <a href=\"https://wikipedia.org/wiki/25th_Infantry_Regiment_(United_States)\" title=\"25th Infantry Regiment (United States)\">25th Infantry Regiment</a> <a href=\"https://wikipedia.org/wiki/Brownsville_Affair\" class=\"mw-redirect\" title=\"Brownsville Affair\">are accused</a> of killing a white bartender and wounding a white police officer in <a href=\"https://wikipedia.org/wiki/Brownsville,_Texas\" title=\"Brownsville, Texas\">Brownsville, Texas</a>, despite exculpatory evidence; all are later dishonorably discharged. (Their records were later restored to reflect honorable discharges but there were no financial settlements.)", "no_year_html": "The all black infantrymen of the U.S. Army's <a href=\"https://wikipedia.org/wiki/25th_Infantry_Regiment_(United_States)\" title=\"25th Infantry Regiment (United States)\">25th Infantry Regiment</a> <a href=\"https://wikipedia.org/wiki/Brownsville_Affair\" class=\"mw-redirect\" title=\"Brownsville Affair\">are accused</a> of killing a white bartender and wounding a white police officer in <a href=\"https://wikipedia.org/wiki/Brownsville,_Texas\" title=\"Brownsville, Texas\">Brownsville, Texas</a>, despite exculpatory evidence; all are later dishonorably discharged. (Their records were later restored to reflect honorable discharges but there were no financial settlements.)", "links": [{"title": "25th Infantry Regiment (United States)", "link": "https://wikipedia.org/wiki/25th_Infantry_Regiment_(United_States)"}, {"title": "Brownsville Affair", "link": "https://wikipedia.org/wiki/Brownsville_Affair"}, {"title": "Brownsville, Texas", "link": "https://wikipedia.org/wiki/Brownsville,_Texas"}]}, {"year": "1913", "text": "First production in the UK of stainless steel by <PERSON>.", "html": "1913 - First production in the UK of <a href=\"https://wikipedia.org/wiki/Stainless_steel\" title=\"Stainless steel\">stainless steel</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "First production in the UK of <a href=\"https://wikipedia.org/wiki/Stainless_steel\" title=\"Stainless steel\">stainless steel</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Stainless steel", "link": "https://wikipedia.org/wiki/Stainless_steel"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "Women enlist in the United States Marine Corps for the first time. <PERSON><PERSON> is the first woman to enlist.", "html": "1918 - Women enlist in the <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> for the first time. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is the first woman to enlist.", "no_year_html": "Women enlist in the <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> for the first time. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is the first woman to enlist.", "links": [{"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "Bayerische Motoren Werke AG (BMW) established as a public company in Germany.", "html": "1918 - Bayerische Motoren Werke AG (<a href=\"https://wikipedia.org/wiki/BMW\" title=\"BMW\">BMW</a>) <a href=\"https://wikipedia.org/wiki/History_of_BMW#BMW_GmbH_goes_public\" title=\"History of BMW\">established as a public company</a> in Germany.", "no_year_html": "Bayerische Motoren Werke AG (<a href=\"https://wikipedia.org/wiki/BMW\" title=\"BMW\">BMW</a>) <a href=\"https://wikipedia.org/wiki/History_of_BMW#BMW_GmbH_goes_public\" title=\"History of BMW\">established as a public company</a> in Germany.", "links": [{"title": "BMW", "link": "https://wikipedia.org/wiki/BMW"}, {"title": "History of BMW", "link": "https://wikipedia.org/wiki/History_of_BMW#BMW_GmbH_goes_public"}]}, {"year": "1920", "text": "Polish-Soviet War: The Battle of Warsaw begins and will last till August 25. The Red Army is defeated.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)\" title=\"Battle of Warsaw (1920)\">Battle of Warsaw</a> begins and will last till <a href=\"https://wikipedia.org/wiki/August_25\" title=\"August 25\">August 25</a>. The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> is defeated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)\" title=\"Battle of Warsaw (1920)\">Battle of Warsaw</a> begins and will last till <a href=\"https://wikipedia.org/wiki/August_25\" title=\"August 25\">August 25</a>. The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> is defeated.", "links": [{"title": "Polish-Soviet War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War"}, {"title": "Battle of Warsaw (1920)", "link": "https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)"}, {"title": "August 25", "link": "https://wikipedia.org/wiki/August_25"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1937", "text": "Second Sino-Japanese War: The Battle of Shanghai begins.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Shanghai\" title=\"Battle of Shanghai\">Battle of Shanghai</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Shanghai\" title=\"Battle of Shanghai\">Battle of Shanghai</a> begins.", "links": [{"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}, {"title": "Battle of Shanghai", "link": "https://wikipedia.org/wiki/Battle_of_Shanghai"}]}, {"year": "1942", "text": "Major General <PERSON> of the U.S. Army Corps of Engineers authorizes the construction of facilities that would house the \"Development of Substitute Materials\" project, better known as the Manhattan Project.", "html": "1942 - Major General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/United_States_Army_Corps_of_Engineers\" title=\"United States Army Corps of Engineers\">U.S. Army Corps of Engineers</a> authorizes the construction of facilities that would house the \"Development of Substitute Materials\" project, better known as the <a href=\"https://wikipedia.org/wiki/Manhattan_Project\" title=\"Manhattan Project\">Manhattan Project</a>.", "no_year_html": "Major General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/United_States_Army_Corps_of_Engineers\" title=\"United States Army Corps of Engineers\">U.S. Army Corps of Engineers</a> authorizes the construction of facilities that would house the \"Development of Substitute Materials\" project, better known as the <a href=\"https://wikipedia.org/wiki/Manhattan_Project\" title=\"Manhattan Project\">Manhattan Project</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_Reybold"}, {"title": "United States Army Corps of Engineers", "link": "https://wikipedia.org/wiki/United_States_Army_Corps_of_Engineers"}, {"title": "Manhattan Project", "link": "https://wikipedia.org/wiki/Manhattan_Project"}]}, {"year": "1944", "text": "World War II: German troops begin the pillage and razing of Anogeia in Crete that would continue until September 5.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German troops begin the <a href=\"https://wikipedia.org/wiki/Razing_of_Anogeia\" title=\"Razing of Anogeia\">pillage and razing of Anogeia</a> in <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a> that would continue until <a href=\"https://wikipedia.org/wiki/September_5\" title=\"September 5\">September 5</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German troops begin the <a href=\"https://wikipedia.org/wiki/Razing_of_Anogeia\" title=\"Razing of Anogeia\">pillage and razing of Anogeia</a> in <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a> that would continue until <a href=\"https://wikipedia.org/wiki/September_5\" title=\"September 5\">September 5</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Razing of Anogeia", "link": "https://wikipedia.org/wiki/Ra<PERSON>_of_Anog<PERSON>a"}, {"title": "Crete", "link": "https://wikipedia.org/wiki/Crete"}, {"title": "September 5", "link": "https://wikipedia.org/wiki/September_5"}]}, {"year": "1954", "text": "Radio Pakistan broadcasts the \"Qaumī Tarāna\", the national anthem of Pakistan for the first time.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Pakistan_Broadcasting_Corporation\" class=\"mw-redirect\" title=\"Pakistan Broadcasting Corporation\">Radio Pakistan</a> broadcasts the \"<a href=\"https://wikipedia.org/wiki/Qau<PERSON>_Taranah\" title=\"Qaumi Taranah\"><PERSON><PERSON><PERSON><PERSON></a>\", the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan_Broadcasting_Corporation\" class=\"mw-redirect\" title=\"Pakistan Broadcasting Corporation\">Radio Pakistan</a> broadcasts the \"<a href=\"https://wikipedia.org/wiki/Qau<PERSON>_Taranah\" title=\"Qaumi Taranah\"><PERSON><PERSON><PERSON><PERSON></a>\", the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> for the first time.", "links": [{"title": "Pakistan Broadcasting Corporation", "link": "https://wikipedia.org/wiki/Pakistan_Broadcasting_Corporation"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "1960", "text": "The Central African Republic declares independence from France.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a> declares independence from France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a> declares independence from France.", "links": [{"title": "Central African Republic", "link": "https://wikipedia.org/wiki/Central_African_Republic"}]}, {"year": "1961", "text": "Cold War: East Germany closes the border between the eastern and western sectors of Berlin to thwart its inhabitants' attempts to escape to the West, and construction of the Berlin Wall is started. The day is known as Barbed Wire Sunday.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> closes the border between the <a href=\"https://wikipedia.org/wiki/East_Berlin\" title=\"East Berlin\">eastern</a> and <a href=\"https://wikipedia.org/wiki/West_Berlin\" title=\"West Berlin\">western sectors</a> of <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a> to thwart its inhabitants' attempts to escape to the <a href=\"https://wikipedia.org/wiki/Western_world\" title=\"Western world\">West</a>, and construction of the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a> is started. The day is known as <a href=\"https://wikipedia.org/wiki/Barbed_Wire_Sunday\" title=\"Barbed Wire Sunday\">Barbed Wire Sunday</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> closes the border between the <a href=\"https://wikipedia.org/wiki/East_Berlin\" title=\"East Berlin\">eastern</a> and <a href=\"https://wikipedia.org/wiki/West_Berlin\" title=\"West Berlin\">western sectors</a> of <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a> to thwart its inhabitants' attempts to escape to the <a href=\"https://wikipedia.org/wiki/Western_world\" title=\"Western world\">West</a>, and construction of the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a> is started. The day is known as <a href=\"https://wikipedia.org/wiki/Barbed_Wire_Sunday\" title=\"Barbed Wire Sunday\">Barbed Wire Sunday</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "East Berlin", "link": "https://wikipedia.org/wiki/East_Berlin"}, {"title": "West Berlin", "link": "https://wikipedia.org/wiki/West_Berlin"}, {"title": "Berlin", "link": "https://wikipedia.org/wiki/Berlin"}, {"title": "Western world", "link": "https://wikipedia.org/wiki/Western_world"}, {"title": "Berlin Wall", "link": "https://wikipedia.org/wiki/Berlin_Wall"}, {"title": "Barbed Wire Sunday", "link": "https://wikipedia.org/wiki/Barbed_Wire_Sunday"}]}, {"year": "1964", "text": "<PERSON> and <PERSON><PERSON><PERSON> are hanged for the murder of <PERSON> becoming the last people executed in the United Kingdom.", "html": "1964 - <PERSON> and <PERSON><PERSON><PERSON> are hanged for the <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">murder of <PERSON></a> becoming the last people <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_Kingdom\" title=\"Capital punishment in the United Kingdom\">executed in the United Kingdom</a>.", "no_year_html": "<PERSON> and <PERSON><PERSON><PERSON> are hanged for the <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">murder of <PERSON></a> becoming the last people <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_Kingdom\" title=\"Capital punishment in the United Kingdom\">executed in the United Kingdom</a>.", "links": [{"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Capital punishment in the United Kingdom", "link": "https://wikipedia.org/wiki/Capital_punishment_in_the_United_Kingdom"}]}, {"year": "1967", "text": "Two young women became the first fatal victims of grizzly bear attacks in the 57-year history of Montana's Glacier National Park in separate incidents.", "html": "1967 - Two young women became the first fatal victims of <a href=\"https://wikipedia.org/wiki/Night_of_the_Grizzlies\" title=\"Night of the Grizzlies\">grizzly bear attacks</a> in the 57-year history of Montana's <a href=\"https://wikipedia.org/wiki/Glacier_National_Park_(U.S.)\" title=\"Glacier National Park (U.S.)\">Glacier National Park</a> in separate incidents.", "no_year_html": "Two young women became the first fatal victims of <a href=\"https://wikipedia.org/wiki/Night_of_the_Grizzlies\" title=\"Night of the Grizzlies\">grizzly bear attacks</a> in the 57-year history of Montana's <a href=\"https://wikipedia.org/wiki/Glacier_National_Park_(U.S.)\" title=\"Glacier National Park (U.S.)\">Glacier National Park</a> in separate incidents.", "links": [{"title": "Night of the Grizzlies", "link": "https://wikipedia.org/wiki/Night_of_the_Grizzlies"}, {"title": "Glacier National Park (U.S.)", "link": "https://wikipedia.org/wiki/Glacier_National_Park_(U.S.)"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON> attempts to assassinate the Greek dictator Colonel <PERSON><PERSON> in Varkiza, Athens.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> attempts to assassinate the Greek <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%931974\" class=\"mw-redirect\" title=\"Greek military junta of 1967-1974\">dictator</a> Colonel <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Varkiza\" title=\"Varkiza\">Varkiza</a>, <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> attempts to assassinate the Greek <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%931974\" class=\"mw-redirect\" title=\"Greek military junta of 1967-1974\">dictator</a> Colonel <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Varkiza\" title=\"Varkiza\">Varkiza</a>, <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Greek military junta of 1967-1974", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%931974"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>arkiza"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}]}, {"year": "1969", "text": "The Apollo 11 astronauts enjoy a ticker-tape parade in New York City. That evening, at a state dinner in Los Angeles, they are awarded the Presidential Medal of Freedom by U.S. President <PERSON>.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> astronauts enjoy a <a href=\"https://wikipedia.org/wiki/Ticker-tape_parade\" title=\"Ticker-tape parade\">ticker-tape parade</a> in New York City. That evening, at a <a href=\"https://wikipedia.org/wiki/State_banquet\" title=\"State banquet\">state dinner</a> in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>, they are awarded the <a href=\"https://wikipedia.org/wiki/Presidential_Medal_of_Freedom\" title=\"Presidential Medal of Freedom\">Presidential Medal of Freedom</a> by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> astronauts enjoy a <a href=\"https://wikipedia.org/wiki/Ticker-tape_parade\" title=\"Ticker-tape parade\">ticker-tape parade</a> in New York City. That evening, at a <a href=\"https://wikipedia.org/wiki/State_banquet\" title=\"State banquet\">state dinner</a> in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>, they are awarded the <a href=\"https://wikipedia.org/wiki/Presidential_Medal_of_Freedom\" title=\"Presidential Medal of Freedom\">Presidential Medal of Freedom</a> by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Apollo 11", "link": "https://wikipedia.org/wiki/Apollo_11"}, {"title": "Ticker-tape parade", "link": "https://wikipedia.org/wiki/Ticker-tape_parade"}, {"title": "State banquet", "link": "https://wikipedia.org/wiki/State_banquet"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}, {"title": "Presidential Medal of Freedom", "link": "https://wikipedia.org/wiki/Presidential_Medal_of_Freedom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "Aviaco Flight 118 crashes on approach to A Coruña Airport in A Coruña, Spain, killing all 85 people on the plane and one other one the ground.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Aviaco_Flight_118\" title=\"Aviaco Flight 118\">Aviaco Flight 118</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/A_Coru%C3%B1a_Airport\" title=\"A Coruña Airport\">A Coruña Airport</a> in <a href=\"https://wikipedia.org/wiki/A_Coru%C3%B1a\" title=\"A Coruña\">A Coruña</a>, Spain, killing all 85 people on the plane and one other one the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aviaco_Flight_118\" title=\"Aviaco Flight 118\">Aviaco Flight 118</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/A_Coru%C3%B1a_Airport\" title=\"A Coruña Airport\">A Coruña Airport</a> in <a href=\"https://wikipedia.org/wiki/A_Coru%C3%B1a\" title=\"A Coruña\">A Coruña</a>, Spain, killing all 85 people on the plane and one other one the ground.", "links": [{"title": "Aviaco Flight 118", "link": "https://wikipedia.org/wiki/Aviaco_Flight_118"}, {"title": "A Coruña Airport", "link": "https://wikipedia.org/wiki/A_Coru%C3%B1a_Airport"}, {"title": "A Coruña", "link": "https://wikipedia.org/wiki/A_Coru%C3%B1a"}]}, {"year": "1977", "text": "Members of the British National Front (NF) clash with anti-NF demonstrators in Lewisham, London, resulting in 214 arrests and at least 111 injuries.", "html": "1977 - Members of the <a href=\"https://wikipedia.org/wiki/National_Front_(UK)\" title=\"National Front (UK)\">British National Front</a> (NF) <a href=\"https://wikipedia.org/wiki/Battle_of_Lewisham\" title=\"Battle of Lewisham\">clash</a> with anti-NF demonstrators in <a href=\"https://wikipedia.org/wiki/Lewisham\" title=\"Lewisham\">Lewisham</a>, London, resulting in 214 arrests and at least 111 injuries.", "no_year_html": "Members of the <a href=\"https://wikipedia.org/wiki/National_Front_(UK)\" title=\"National Front (UK)\">British National Front</a> (NF) <a href=\"https://wikipedia.org/wiki/Battle_of_Lewisham\" title=\"Battle of Lewisham\">clash</a> with anti-NF demonstrators in <a href=\"https://wikipedia.org/wiki/Lewisham\" title=\"Lewisham\">Lewisham</a>, London, resulting in 214 arrests and at least 111 injuries.", "links": [{"title": "National Front (UK)", "link": "https://wikipedia.org/wiki/National_Front_(UK)"}, {"title": "Battle of Lewisham", "link": "https://wikipedia.org/wiki/Battle_of_Lewisham"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lewisham"}]}, {"year": "1978", "text": "One hundred fifty Palestinians in Beirut are killed in a terrorist attack during the second phase of the Lebanese Civil War.", "html": "1978 - One hundred fifty <a href=\"https://wikipedia.org/wiki/Palestinian_people\" class=\"mw-redirect\" title=\"Palestinian people\">Palestinians</a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> are <a href=\"https://wikipedia.org/wiki/Tel_al-Zaatar_massacre\" title=\"Tel al-Zaatar massacre\">killed</a> in a <a href=\"https://wikipedia.org/wiki/List_of_terrorist_incidents\" title=\"List of terrorist incidents\">terrorist attack</a> during the <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War#Second_phase_of_the_war,_1977-82\" title=\"Lebanese Civil War\">second phase</a> of the <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War\" title=\"Lebanese Civil War\">Lebanese Civil War</a>.", "no_year_html": "One hundred fifty <a href=\"https://wikipedia.org/wiki/Palestinian_people\" class=\"mw-redirect\" title=\"Palestinian people\">Palestinians</a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> are <a href=\"https://wikipedia.org/wiki/Tel_al-Zaatar_massacre\" title=\"Tel al-Zaatar massacre\">killed</a> in a <a href=\"https://wikipedia.org/wiki/List_of_terrorist_incidents\" title=\"List of terrorist incidents\">terrorist attack</a> during the <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War#Second_phase_of_the_war,_1977-82\" title=\"Lebanese Civil War\">second phase</a> of the <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War\" title=\"Lebanese Civil War\">Lebanese Civil War</a>.", "links": [{"title": "Palestinian people", "link": "https://wikipedia.org/wiki/Palestinian_people"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}, {"title": "Tel al-Zaatar massacre", "link": "https://wikipedia.org/wiki/Tel_al-Zaatar_massacre"}, {"title": "List of terrorist incidents", "link": "https://wikipedia.org/wiki/List_of_terrorist_incidents"}, {"title": "Lebanese Civil War", "link": "https://wikipedia.org/wiki/Lebanese_Civil_War#Second_phase_of_the_war,_1977-82"}, {"title": "Lebanese Civil War", "link": "https://wikipedia.org/wiki/Lebanese_Civil_War"}]}, {"year": "1990", "text": "A mainland Chinese fishing boat Min Ping Yu No. 5202 is hit by a Taiwanese naval vessel and sinks in a repatriation operation of mainland Chinese immigrants, resulting in 21 deaths. This is the second tragedy less than a month after Min Ping Yu No. 5540 incident.", "html": "1990 - A mainland Chinese fishing boat <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Yu_No._5202\" title=\"Min Ping Yu No. 5202\">Min Ping Yu No. 5202</a></i> is hit by a Taiwanese naval vessel and sinks in a repatriation operation of mainland Chinese immigrants, resulting in 21 deaths. This is the second tragedy less than a month after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Yu_No._5540_incident\" title=\"Min Ping Yu No. 5540 incident\"><i>Min Ping Yu No. 5540</i> incident</a>.", "no_year_html": "A mainland Chinese fishing boat <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Yu_No._5202\" title=\"Min Ping Yu No. 5202\">Min Ping Yu No. 5202</a></i> is hit by a Taiwanese naval vessel and sinks in a repatriation operation of mainland Chinese immigrants, resulting in 21 deaths. This is the second tragedy less than a month after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Yu_No._5540_incident\" title=\"Min Ping Yu No. 5540 incident\"><i>Min Ping Yu No. 5540</i> incident</a>.", "links": [{"title": "Min Ping Yu No. 5202", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_No._5202"}, {"title": "<PERSON> Yu No. 5540 incident", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_No._5540_incident"}]}, {"year": "2004", "text": "One hundred fifty-six Congolese Tutsi refugees are massacred at the Gatumba refugee camp in Burundi.", "html": "2004 - One hundred fifty-six <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Congolese</a> <a href=\"https://wikipedia.org/wiki/Tutsi\" title=\"Tutsi\">Tutsi</a> refugees are massacred at the <a href=\"https://wikipedia.org/wiki/Gatumba\" title=\"Gatumba\">Gatumba</a> <a href=\"https://wikipedia.org/wiki/Refugee_camp\" title=\"Refugee camp\">refugee camp</a> in <a href=\"https://wikipedia.org/wiki/Burundi\" title=\"Burundi\">Burundi</a>.", "no_year_html": "One hundred fifty-six <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Congolese</a> <a href=\"https://wikipedia.org/wiki/Tutsi\" title=\"Tutsi\">Tutsi</a> refugees are massacred at the <a href=\"https://wikipedia.org/wiki/Gatumba\" title=\"Gatumba\">Gatumba</a> <a href=\"https://wikipedia.org/wiki/Refugee_camp\" title=\"Refugee camp\">refugee camp</a> in <a href=\"https://wikipedia.org/wiki/Burundi\" title=\"Burundi\">Burundi</a>.", "links": [{"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tutsi"}, {"title": "Gatumba", "link": "https://wikipedia.org/wiki/Gatumba"}, {"title": "Refugee camp", "link": "https://wikipedia.org/wiki/Refugee_camp"}, {"title": "Burundi", "link": "https://wikipedia.org/wiki/Burundi"}]}, {"year": "2008", "text": "Russo-Georgian War: Russian units occupy the Georgian city of Gori.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Russo-Georgian_War\" title=\"Russo-Georgian War\">Russo-Georgian War</a>: Russian units <a href=\"https://wikipedia.org/wiki/Occupation_of_Gori\" title=\"Occupation of Gori\">occupy</a> the Georgian city of <a href=\"https://wikipedia.org/wiki/Gori,_Georgia\" title=\"Gori, Georgia\">Gori</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Georgian_War\" title=\"Russo-Georgian War\">Russo-Georgian War</a>: Russian units <a href=\"https://wikipedia.org/wiki/Occupation_of_Gori\" title=\"Occupation of Gori\">occupy</a> the Georgian city of <a href=\"https://wikipedia.org/wiki/Gori,_Georgia\" title=\"Gori, Georgia\">Gori</a>.", "links": [{"title": "Russo-Georgian War", "link": "https://wikipedia.org/wiki/Russo-Georgian_War"}, {"title": "Occupation of Gori", "link": "https://wikipedia.org/wiki/Occupation_of_Gori"}, {"title": "Gori, Georgia", "link": "https://wikipedia.org/wiki/Gori,_Georgia"}]}, {"year": "2014", "text": "A Cessna Citation Excel crashes in Santos, São Paulo, Brazil killing all seven people aboard, including Brazilian Socialist Party presidential candidate <PERSON>.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/Cessna_Citation_Excel\" title=\"Cessna Citation Excel\">Cessna Citation Excel</a> <a href=\"https://wikipedia.org/wiki/2014_Santos_Cessna_Citation_crash\" title=\"2014 Santos Cessna Citation crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Santos,_S%C3%A3o_Paulo\" title=\"Santos, São Paulo\">Santos, São Paulo</a>, Brazil killing all seven people aboard, including <a href=\"https://wikipedia.org/wiki/Brazilian_Socialist_Party\" title=\"Brazilian Socialist Party\">Brazilian Socialist Party</a> presidential candidate <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Cessna_Citation_Excel\" title=\"Cessna Citation Excel\">Cessna Citation Excel</a> <a href=\"https://wikipedia.org/wiki/2014_Santos_Cessna_Citation_crash\" title=\"2014 Santos Cessna Citation crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Santos,_S%C3%A3o_Paulo\" title=\"Santos, São Paulo\">Santos, São Paulo</a>, Brazil killing all seven people aboard, including <a href=\"https://wikipedia.org/wiki/Brazilian_Socialist_Party\" title=\"Brazilian Socialist Party\">Brazilian Socialist Party</a> presidential candidate <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cessna Citation Excel", "link": "https://wikipedia.org/wiki/Cessna_Citation_Excel"}, {"title": "2014 Santos Cessna Citation crash", "link": "https://wikipedia.org/wiki/2014_<PERSON>_<PERSON>ssna_Citation_crash"}, {"title": "Santos, São Paulo", "link": "https://wikipedia.org/wiki/Santos,_S%C3%A3o_Paulo"}, {"title": "Brazilian Socialist Party", "link": "https://wikipedia.org/wiki/Brazilian_Socialist_Party"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "At least 76 people are killed and 212 others are wounded in a truck bombing in Baghdad, Iraq.", "html": "2015 - At least 76 people are killed and 212 others are wounded in a <a href=\"https://wikipedia.org/wiki/2015_Sadr_City_market_truck_bombing\" title=\"2015 Sadr City market truck bombing\">truck bombing</a> in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>, Iraq.", "no_year_html": "At least 76 people are killed and 212 others are wounded in a <a href=\"https://wikipedia.org/wiki/2015_Sadr_City_market_truck_bombing\" title=\"2015 Sadr City market truck bombing\">truck bombing</a> in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>, Iraq.", "links": [{"title": "2015 Sadr City market truck bombing", "link": "https://wikipedia.org/wiki/2015_Sadr_City_market_truck_bombing"}, {"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}]}, {"year": "2020", "text": "Israel-United Arab Emirates relations are formally established.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93United_Arab_Emirates_relations\" title=\"Israel-United Arab Emirates relations\">Israel-United Arab Emirates relations</a> are formally established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel%E2%80%93United_Arab_Emirates_relations\" title=\"Israel-United Arab Emirates relations\">Israel-United Arab Emirates relations</a> are formally established.", "links": [{"title": "Israel-United Arab Emirates relations", "link": "https://wikipedia.org/wiki/Israel%E2%80%93United_Arab_Emirates_relations"}]}], "Births": [{"year": "985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> caliph (d. 1021)", "html": "985 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_bi-<PERSON><PERSON>_<PERSON>\" title=\"Al-Ha<PERSON> bi-Am<PERSON>\"><PERSON><PERSON><PERSON><PERSON> bi-<PERSON><PERSON></a>, <PERSON><PERSON><PERSON> caliph (d. 1021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_bi-<PERSON><PERSON>_<PERSON>\" title=\"Al-Hakim bi-Am<PERSON>\"><PERSON><PERSON><PERSON><PERSON> bi-<PERSON><PERSON></a>, <PERSON><PERSON><PERSON> caliph (d. 1021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1311", "text": "<PERSON>, king of Castile and León (d. 1350)", "html": "1311 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XI_of_Castile\" title=\"<PERSON> XI of Castile\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> (d. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XI_of_Castile\" title=\"<PERSON> XI of Castile\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> (d. 1350)", "links": [{"title": "Alfonso XI of Castile", "link": "https://wikipedia.org/wiki/Alfonso_XI_of_Castile"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}]}, {"year": "1567", "text": "<PERSON>, French explorer (d. 1635)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer (d. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1584", "text": "<PERSON><PERSON><PERSON>, 2nd Earl of Suffolk, English admiral and politician, Lord Lieutenant of Cumberland (d. 1640)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_2nd_Earl_of_Suffolk\" title=\"<PERSON><PERSON><PERSON>, 2nd Earl of Suffolk\"><PERSON><PERSON><PERSON>, 2nd Earl of Suffolk</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Cumberland\" title=\"Lord Lieutenant of Cumberland\">Lord Lieutenant of Cumberland</a> (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_2nd_Earl_of_Suffolk\" title=\"<PERSON><PERSON><PERSON>, 2nd Earl of Suffolk\"><PERSON><PERSON><PERSON>, 2nd Earl of Suffolk</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Cumberland\" title=\"Lord Lieutenant of Cumberland\">Lord Lieutenant of Cumberland</a> (d. 1640)", "links": [{"title": "<PERSON><PERSON><PERSON>, 2nd Earl of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_2nd_Earl_of_Suffolk"}, {"title": "Lord Lieutenant of Cumberland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Cumberland"}]}, {"year": "1592", "text": "<PERSON>, Count of Nassau-Siegen, German count, field marshal of the Dutch State Army (d. 1642)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count, field marshal of the Dutch State Army (d. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count, field marshal of the Dutch State Army (d. 1642)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Nassau-Siegen"}]}, {"year": "1625", "text": "<PERSON><PERSON>, Danish physician, mathematician, and physicist (d. 1698)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish physician, mathematician, and physicist (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish physician, mathematician, and physicist (d. 1698)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON>, 6th Duke of Somerset, English politician, Lord President of the Council (d. 1748)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Duke_of_Somerset\" title=\"<PERSON>, 6th Duke of Somerset\"><PERSON>, 6th Duke of Somerset</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Somerset\" title=\"<PERSON>, 6th Duke of Somerset\"><PERSON>, 6th Duke of Somerset</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1748)", "links": [{"title": "<PERSON>, 6th Duke of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Duke_of_Somerset"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1666", "text": "<PERSON>, English linguist and scholar (d. 1727)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and scholar (d. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and scholar (d. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1700", "text": "<PERSON>, Polish-German politician (d. 1763)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German politician (d. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%BChl\" title=\"<PERSON>\"><PERSON></a>, Polish-German politician (d. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BChl"}]}, {"year": "1717", "text": "<PERSON>, Prince of Conti (d. 1776)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Prince_of_Conti\" title=\"<PERSON>, Prince of Conti\"><PERSON>, Prince of Conti</a> (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Prince_of_Conti\" title=\"<PERSON>, Prince of Conti\"><PERSON>, Prince of Conti</a> (d. 1776)", "links": [{"title": "<PERSON>, Prince of Conti", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>,_Prince_<PERSON>_<PERSON>"}]}, {"year": "1756", "text": "<PERSON>, English caricaturist and printmaker (d. 1815)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English caricaturist and printmaker (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English caricaturist and printmaker (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, French general (d. 1813)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_d%27Hilliers\" title=\"<PERSON> d'Hilliers\"><PERSON></a>, French general (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>ague<PERSON>_d%27Hilliers\" title=\"<PERSON> d'Hilliers\"><PERSON></a>, French general (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Baraguey_d%27Hilliers"}]}, {"year": "1790", "text": "<PERSON>, Australian journalist, explorer, and politician (d. 1872)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, explorer, and politician (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, explorer, and politician (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, Russian philosopher and critic (d. 1869)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and critic (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and critic (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, Swedish physicist and astronomer (d. 1874)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%85ngstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and astronomer (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%85ngstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and astronomer (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%85ngstr%C3%B6m"}]}, {"year": "1818", "text": "<PERSON>, American abolitionist and suffragist (d. 1893)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American abolitionist and suffragist (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lucy Stone\"><PERSON></a>, American abolitionist and suffragist (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "Sir <PERSON>, 1st Baronet, Anglo-Irish mathematician and physicist (d. 1903)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Anglo-Irish mathematician and physicist (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Anglo-Irish mathematician and physicist (d. 1903)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}]}, {"year": "1820", "text": "<PERSON>, English musicologist and historian (d. 1900)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/George_Grove\" title=\"George Grove\"><PERSON></a>, English musicologist and historian (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Grove\" title=\"George Grove\"><PERSON></a>, English musicologist and historian (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}]}, {"year": "1823", "text": "<PERSON><PERSON>, English-Canadian historian and journalist (d. 1910)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian historian and journalist (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian historian and journalist (d. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, American politician in Michigan (d. 1897)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician in Michigan (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician in Michigan (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON>, German pianist and composer (d. 1902)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/Salomo<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pianist and composer (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salomo<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pianist and composer (d. 1902)", "links": [{"title": "Salomon <PERSON>", "link": "https://wikipedia.org/wiki/Salomon_<PERSON>sso<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, Australian cricketer (d. 1891)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, English brewer, founded Charles Wells Ltd (d. 1914)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)\" title=\"<PERSON> (brewer)\"><PERSON></a>, English brewer, founded <a href=\"https://wikipedia.org/wiki/Charles_Wells_Ltd\" class=\"mw-redirect\" title=\"Charles Wells Ltd\">Charles Wells Ltd</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)\" title=\"<PERSON> (brewer)\"><PERSON></a>, English brewer, founded <a href=\"https://wikipedia.org/wiki/Charles_Wells_Ltd\" class=\"mw-redirect\" title=\"Charles Wells Ltd\">Charles Wells Ltd</a> (d. 1914)", "links": [{"title": "<PERSON> (brewer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)"}, {"title": "Charles Wells Ltd", "link": "https://wikipedia.org/wiki/Charles_Wells_Ltd"}]}, {"year": "1849", "text": "<PERSON><PERSON>, Irish-born American social activist (d. 1930)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born American social activist (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born American social activist (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, German-American religious leader and educator (d. 1933)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)\" title=\"<PERSON> (professor)\"><PERSON></a>, German-American religious leader and educator (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)\" title=\"<PERSON> (professor)\"><PERSON></a>, German-American religious leader and educator (d. 1933)", "links": [{"title": "<PERSON> (professor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)"}]}, {"year": "1860", "text": "<PERSON>, American target shooter (d. 1926)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Italian businessman, founded Fiat S.p.A. (d. 1945)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman, founded <a href=\"https://wikipedia.org/wiki/Fiat\" title=\"Fiat\">Fiat S.p.A.</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman, founded <a href=\"https://wikipedia.org/wiki/Fiat\" title=\"Fiat\">Fiat S.p.A.</a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fiat", "link": "https://wikipedia.org/wiki/Fiat"}]}, {"year": "1867", "text": "<PERSON>, American painter and illustrator (d. 1933)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, German politician, co-founded Communist Party of Germany (d. 1919)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, co-founded <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Germany\" title=\"Communist Party of Germany\">Communist Party of Germany</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, co-founded <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Germany\" title=\"Communist Party of Germany\">Communist Party of Germany</a> (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Communist Party of Germany", "link": "https://wikipedia.org/wiki/Communist_Party_of_Germany"}]}, {"year": "1872", "text": "<PERSON>, German-Swiss chemist and academic, Nobel Prize Laureate (d. 1942)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4tter\" title=\"<PERSON>\"><PERSON></a>, German-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> Laureate (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4tter\" title=\"<PERSON>\"><PERSON></a>, German-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> Laureate (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4tter"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1879", "text": "<PERSON>, English composer and educator (d. 1962)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/John_Ireland_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and educator (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Ireland_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and educator (d. 1962)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/John_<PERSON>_(composer)"}]}, {"year": "1884", "text": "<PERSON>, English cricketer and coach (d. 1957)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and coach (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and coach (d. 1957)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1888", "text": "<PERSON>, Scottish engineer, invented the television (d. 1946)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer, invented the <a href=\"https://wikipedia.org/wiki/History_of_television\" title=\"History of television\">television</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer, invented the <a href=\"https://wikipedia.org/wiki/History_of_television\" title=\"History of television\">television</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "History of television", "link": "https://wikipedia.org/wiki/History_of_television"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON> <PERSON>, Russian-American sculptor (d. 1975)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Gle<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Russian-American sculptor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>le<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>le<PERSON> <PERSON><PERSON>\">Gle<PERSON> <PERSON><PERSON></a>, Russian-American sculptor (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>le<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 34th Mayor of Montreal (d. 1958)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Hungarian water polo player (d. 1948)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Barta\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian water polo player (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_<PERSON>a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian water polo player (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Barta"}]}, {"year": "1895", "text": "<PERSON>, American actor (d. 1967)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, French tennis player (d. 1994)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American actor (d. 1991)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Too<PERSON>y\" title=\"<PERSON> Toomey\"><PERSON></a>, American actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Regis_Toomey\" title=\"<PERSON> Toomey\"><PERSON></a>, American actor (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regis_Toomey"}]}, {"year": "1899", "text": "<PERSON>, English-American director and producer (d. 1980)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hitchcock\"><PERSON></a>, English-American director and producer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hitchcock\"><PERSON></a>, English-American director and producer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Panamanian politician, 17th President of Panama (d. 1964)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_Guizado\" title=\"<PERSON>\"><PERSON></a>, Panamanian politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_Guizado\" title=\"<PERSON>\"><PERSON></a>, Panamanian politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a> (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_Guizado"}, {"title": "President of Panama", "link": "https://wikipedia.org/wiki/President_of_Panama"}]}, {"year": "1902", "text": "<PERSON>, German engineer (d. 1988)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American actor and musician (d. 1999)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and musician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and musician (d. 1999)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1904", "text": "<PERSON>, Native American Pueblo potter (d. 2001)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American Pueblo potter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American Pueblo potter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American football player and lawyer (d. 2003)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and lawyer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and lawyer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American baseball player and boxer (d. 1967)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Art_Shires\" title=\"Art <PERSON>\"><PERSON></a>, American baseball player and boxer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Shires\" title=\"Art Shires\"><PERSON></a>, American baseball player and boxer (d. 1967)", "links": [{"title": "Art <PERSON>s", "link": "https://wikipedia.org/wiki/Art_Shires"}]}, {"year": "1907", "text": "<PERSON>, Scottish architect, designed Coventry Cathedral (d. 1976)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish architect, designed <a href=\"https://wikipedia.org/wiki/Coventry_Cathedral\" title=\"Coventry Cathedral\">Coventry Cathedral</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish architect, designed <a href=\"https://wikipedia.org/wiki/Coventry_Cathedral\" title=\"Coventry Cathedral\">Coventry Cathedral</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coventry Cathedral", "link": "https://wikipedia.org/wiki/Coventry_Cathedral"}]}, {"year": "1908", "text": "<PERSON>, American actor and pilot (d. 1998)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and pilot (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and pilot (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American advertiser, co-founded DDB Worldwide (d. 1982)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American advertiser, co-founded <a href=\"https://wikipedia.org/wiki/DDB_Worldwide\" title=\"DDB Worldwide\">DDB Worldwide</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American advertiser, co-founded <a href=\"https://wikipedia.org/wiki/DDB_Worldwide\" title=\"DDB Worldwide\">DDB Worldwide</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "DDB Worldwide", "link": "https://wikipedia.org/wiki/DDB_Worldwide"}]}, {"year": "1912", "text": "<PERSON>, American basketball player and coach (d. 1985)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American golfer and sportscaster (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Italian-American microbiologist and academic, Nobel Prize laureate (d. 1991)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Salvador_Luria\" title=\"Salvador Lu<PERSON>\"><PERSON></a>, Italian-American microbiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Luria\" title=\"Salvador Luria\"><PERSON></a>, Italian-American microbiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1991)", "links": [{"title": "Salvador Luria", "link": "https://wikipedia.org/wiki/Salvador_Luria"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Greek archbishop and politician, 1st President of Cyprus (d. 1977)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III\" title=\"<PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON> III</a>, Greek archbishop and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III\" title=\"<PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON> III</a>, Greek archbishop and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>kari<PERSON>_III"}, {"title": "President of Cyprus", "link": "https://wikipedia.org/wiki/President_of_Cyprus"}]}, {"year": "1913", "text": "<PERSON>, English snooker player (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player (d. 1998)", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)"}]}, {"year": "1914", "text": "<PERSON>, American mathematician and academic (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball player (d. 1975)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Trinidadian lawyer and politician, 2nd President of Trinidad and Tobago (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago\" title=\"President of Trinidad and Tobago\">President of Trinidad and Tobago</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago\" title=\"President of Trinidad and Tobago\">President of Trinidad and Tobago</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago"}]}, {"year": "1918", "text": "<PERSON>, English biochemist and academic, Nobel Prize laureate (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1919", "text": "<PERSON>, American evangelist and television host (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and television host (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and television host (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English jazz pianist and bandleader (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jazz pianist and bandleader (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jazz pianist and bandleader (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 1992)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brand\"><PERSON></a>, American actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Neville Brand\"><PERSON></a>, American actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French conductor (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>%C3%A9maux\" title=\"<PERSON>\"><PERSON></a>, French conductor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>%C3%A9maux\" title=\"<PERSON>\"><PERSON></a>, French conductor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Fr%C3%A9maux"}]}, {"year": "1921", "text": "<PERSON>, American blues/R&B singer-songwriter and pianist (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues/R&amp;B singer-songwriter and pianist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues/R&amp;B singer-songwriter and pianist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Scottish singer (d. 2022)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer (d. 2022)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1922", "text": "<PERSON>, American basketball player, coach, and educator (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and educator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and educator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American trumpet player, songwriter, and producer (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, songwriter, and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Bailey\"><PERSON></a>, American trumpet player, songwriter, and producer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Argentine executive and policy maker (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_Mart%C3%ADnez_de_Hoz\" title=\"<PERSON>\"><PERSON></a>, Argentine executive and policy maker (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_Mart%C3%ADnez_de_Hoz\" title=\"<PERSON>\"><PERSON></a>, Argentine executive and policy maker (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%ADnez_de_Hoz"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Cuban lawyer and politician, ex-President of Cuba (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban lawyer and politician, ex-<a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban lawyer and politician, ex-<a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Cuba", "link": "https://wikipedia.org/wiki/President_of_Cuba"}]}, {"year": "1928", "text": "<PERSON>,  English journalist and radio host (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and radio host (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and radio host (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Jr., American actor (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor (d. 2016)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, German footballer and referee", "html": "1930 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and referee", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American singer and ukulele player (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and ukulele player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ho\"><PERSON></a>, American singer and ukulele player (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English comedian (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, American baseball player and politician (d. 1999)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and politician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and politician (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American baseball player (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American admiral and physician, 15th Surgeon General of the United States", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Elders\" title=\"Joycelyn Elders\"><PERSON><PERSON> Elders</a>, American admiral and physician, 15th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Elders\" title=\"Joycelyn Elders\"><PERSON><PERSON> Elders</a>, American admiral and physician, 15th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a>", "links": [{"title": "Joycelyn Elders", "link": "https://wikipedia.org/wiki/<PERSON>lyn_Elders"}, {"title": "Surgeon General of the United States", "link": "https://wikipedia.org/wiki/Surgeon_General_of_the_United_States"}]}, {"year": "1935", "text": "<PERSON>, American director and producer (d. 2001)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, American baseball player and sportscaster (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Mu<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and sportscaster (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Mud<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and sportscaster (d. 2021)", "links": [{"title": "Mud<PERSON> Grant", "link": "https://wikipedia.org/wiki/Mudcat_Grant"}]}, {"year": "1938", "text": "<PERSON> \"<PERSON>\" <PERSON>, American R&B pianist, organist, and composer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Baby%22_<PERSON><PERSON><PERSON>\" title='<PERSON> \"<PERSON>\" Co<PERSON><PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American R&amp;B pianist, organist, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Baby%22_<PERSON><PERSON><PERSON>\" title='<PERSON> \"Baby\" Corte<PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American R&amp;B pianist, organist, and composer", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Baby%22_<PERSON><PERSON>z"}]}, {"year": "1938", "text": "<PERSON>, Canadian ice hockey player (d. 1968)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American basketball player and coach (d. 2000)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Chadian politician and war criminal, 5th president of Chad (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Hiss%C3%A8ne_Habr%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chadian politician and war criminal, 5th <a href=\"https://wikipedia.org/wiki/President_of_Chad\" class=\"mw-redirect\" title=\"President of Chad\">president of Chad</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hiss%C3%A8ne_Habr%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chadian politician and war criminal, 5th <a href=\"https://wikipedia.org/wiki/President_of_Chad\" class=\"mw-redirect\" title=\"President of Chad\">president of Chad</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hiss%C3%A8ne_Habr%C3%A9"}, {"title": "President of Chad", "link": "https://wikipedia.org/wiki/President_of_Chad"}]}, {"year": "1943", "text": "<PERSON>, American football player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, President of Haiti", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, President of Haiti", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, President of Haiti", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English sergeant; <PERSON> recipient (d. 1971)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sergeant; <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> recipient (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sergeant; <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> recipient (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Swedish politician, Deputy Prime Minister of Sweden", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Sweden\" title=\"Deputy Prime Minister of Sweden\">Deputy Prime Minister of Sweden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Sweden\" title=\"Deputy Prime Minister of Sweden\">Deputy Prime Minister of Sweden</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Deputy Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Sweden"}]}, {"year": "1945", "text": "<PERSON>, American basketball player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Indian-English cricketer and sportscaster (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English cricketer and sportscaster (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jack<PERSON>\"><PERSON></a>, Indian-English cricketer and sportscaster (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Welsh cannabis smuggler, writer, and legalisation campaigner (d. 2016)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh cannabis smuggler, writer, and legalisation campaigner (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh cannabis smuggler, writer, and legalisation campaigner (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American economist, 78th United States secretary of the treasury", "html": "1946 - <a href=\"https://wikipedia.org/wiki/1946\" title=\"1946\">1946</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, 78th <a href=\"https://wikipedia.org/wiki/United_States_secretary_of_the_treasury\" class=\"mw-redirect\" title=\"United States secretary of the treasury\">United States secretary of the treasury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1946\" title=\"1946\">1946</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, 78th <a href=\"https://wikipedia.org/wiki/United_States_secretary_of_the_treasury\" class=\"mw-redirect\" title=\"United States secretary of the treasury\">United States secretary of the treasury</a>", "links": [{"title": "1946", "link": "https://wikipedia.org/wiki/1946"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States secretary of the treasury", "link": "https://wikipedia.org/wiki/United_States_secretary_of_the_treasury"}]}, {"year": "1947", "text": "<PERSON>, American baseball player and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1947", "text": "<PERSON>, Canadian voice actor and director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(voice_actor)\" title=\"<PERSON> (voice actor)\"><PERSON></a>, Canadian voice actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(voice_actor)\" title=\"<PERSON> (voice actor)\"><PERSON></a>, Canadian voice actor and director", "links": [{"title": "<PERSON> (voice actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(voice_actor)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Swedish politician, Deputy Prime Minister of Sweden", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Sweden\" title=\"Deputy Prime Minister of Sweden\">Deputy Prime Minister of Sweden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Sweden\" title=\"Deputy Prime Minister of Sweden\">Deputy Prime Minister of Sweden</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Sweden"}]}, {"year": "1948", "text": "<PERSON>, American operatic soprano", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kathleen Battle\"><PERSON></a>, American operatic soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kathleen Battle\"><PERSON></a>, American operatic soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American wrestler", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French tightrope walker", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Tightrope_walking\" title=\"Tightrope walking\">tightrope</a> walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Tightrope_walking\" title=\"Tightrope walking\">tightrope</a> walker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Tightrope walking", "link": "https://wikipedia.org/wiki/Tightrope_walking"}]}, {"year": "1949", "text": "<PERSON>, Dutch-Canadian model (d. 1973)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rey\"><PERSON></a>, Dutch-Canadian model (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rey\"><PERSON></a>, Dutch-Canadian model (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2007)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2002)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American football player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American performance artist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American performance artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American performance artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American photographer and director (d. 2002)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2007)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Filipino businessperson, CEO and chairman of ABS-CBN Corporation", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino businessperson, CEO and chairman of <a href=\"https://wikipedia.org/wiki/ABS-CBN_Corporation\" title=\"ABS-CBN Corporation\">ABS-CBN Corporation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino businessperson, CEO and chairman of <a href=\"https://wikipedia.org/wiki/ABS-CBN_Corporation\" title=\"ABS-CBN Corporation\">ABS-CBN Corporation</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "ABS-CBN Corporation", "link": "https://wikipedia.org/wiki/ABS-CBN_Corporation"}]}, {"year": "1953", "text": "<PERSON>, American philosopher, theorist, and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, theorist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, theorist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian rugby league player and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, German philosopher and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English historian and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(journalist)"}]}, {"year": "1954", "text": "<PERSON>, Brazilian bass player (d. 2001)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Nico_Assump%C3%A7%C3%A3o\" title=\"Nico Assumpção\"><PERSON></a>, Brazilian bass player (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nico_Assump%C3%A7%C3%A3o\" title=\"Nico Assumpção\"><PERSON></a>, Brazilian bass player (d. 2001)", "links": [{"title": "Nico Assumpção", "link": "https://wikipedia.org/wiki/Nico_Assump%C3%A7%C3%A3o"}]}, {"year": "1955", "text": "<PERSON>, English race car driver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English director and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Judge of the Supreme Court of India", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Roh<PERSON><PERSON>_<PERSON>ali_Nariman\" title=\"Roh<PERSON>on Fali Nariman\"><PERSON><PERSON><PERSON><PERSON></a>, Judge of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_India\" title=\"Supreme Court of India\">Supreme Court of India</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_<PERSON>ali_Nariman\" title=\"Roh<PERSON>on Fali Nariman\"><PERSON><PERSON><PERSON><PERSON></a>, Judge of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_India\" title=\"Supreme Court of India\">Supreme Court of India</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>n"}, {"title": "Supreme Court of India", "link": "https://wikipedia.org/wiki/Supreme_Court_of_India"}]}, {"year": "1958", "text": "<PERSON>, Northern Irish golfer and sportscaster", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Northern Irish singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ey\" title=\"Feargal Sharkey\"><PERSON><PERSON></a>, Northern Irish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Feargal Sharkey\"><PERSON><PERSON></a>, Northern Irish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American sergeant, Medal of Honor recipient (d. 1993)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1959", "text": "<PERSON>, American actor and wrestler", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bonaduce\"><PERSON></a>, American actor and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English cricketer and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1959", "text": "<PERSON>, American baseball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Estonian swimmer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Japanese composer and sound director", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ko<PERSON>\"><PERSON><PERSON></a>, Japanese composer and sound director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer and sound director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ndo"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English cricketer and umpire", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American novelist and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American talk show co-host and announcer, writer, producer, comedian and impressionist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show co-host and announcer, writer, producer, comedian and impressionist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show co-host and announcer, writer, producer, comedian and impressionist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American CIA agent and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> agent and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> agent and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Indian actress (d. 2018)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1965", "text": "<PERSON>, American baseball player, coach, and radio host", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Japanese composer and conductor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ooter <PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>oot<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>ooter <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oot<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actress, author, and entrepreneur", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, author, and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, author, and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American basketball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Dutch tennis player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>telaar\"><PERSON><PERSON></a>, Dutch tennis player", "links": [{"title": "Digna Ketelaar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aar"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Canadian singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>man\" title=\"Ta<PERSON> Bachman\"><PERSON><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>man\" title=\"Ta<PERSON> Bachman\"><PERSON><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American football player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English sprinter and hurdler", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter and hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Japanese figure skater", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o\" title=\"<PERSON><PERSON> Ito\"><PERSON><PERSON></a>, Japanese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ito\"><PERSON><PERSON></a>, Japanese figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1970", "text": "<PERSON>, American author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American author", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "1970", "text": "<PERSON>, American football player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Elvis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ier"}]}, {"year": "1971", "text": "<PERSON>, American baseball player and journalist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, German actor ", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u\" title=\"<PERSON><PERSON> Bleibtreu\"><PERSON><PERSON></a>, German actor ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bleibtreu\"><PERSON><PERSON></a>, German actor ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1972", "text": "<PERSON>, American businessman, founded Under Armour", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Under_Armour\" title=\"Under Armour\">Under Armour</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Under_Armour\" title=\"Under Armour\">Under Armour</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Under Armour", "link": "https://wikipedia.org/wiki/Under_Armour"}]}, {"year": "1973", "text": "<PERSON>, American journalist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American race car driver (d. 2007)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English snooker player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Swedish musician and artist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish musician and artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish musician and artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American baseball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Geno_Carlisle\" title=\"Geno Carlisle\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Geno_Carlisle\" title=\"Geno Carlisle\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "Geno Carlisle", "link": "https://wikipedia.org/wiki/Geno_Carlisle"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Ecuadorian tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Lapentti\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_La<PERSON>ti\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Lapentti"}]}, {"year": "1977", "text": "<PERSON>, Polish-Australian swimmer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "Kenyan Weaks, American basketball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Kenyan_Weaks\" title=\"Kenyan Weaks\">Kenyan Weaks</a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenyan_Weaks\" title=\"Kenyan Weaks\">Kenyan Weaks</a>, American basketball player and coach", "links": [{"title": "Kenyan Weaks", "link": "https://wikipedia.org/wiki/Kenyan_Weaks"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Rom%C3%A1n_Col%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rom%C3%A1n_Col%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rom%C3%A1n_Col%C3%B3n"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Japanese politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Taiz%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taiz%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taiz%C5%8D_<PERSON>gimura"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Canadian journalist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English fashion designer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, English fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, English fashion designer", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>_(designer)"}]}, {"year": "1982", "text": "<PERSON>, Governor of Arkansas, American political consultant and press secretary", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Governor of Arkansas, American political consultant and press secretary", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Governor of Arkansas, American political consultant and press secretary", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Romanian-American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stan\"><PERSON></a>, Romanian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sebastian Stan\"><PERSON></a>, Romanian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Dallas_Braden\" title=\"<PERSON> Braden\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dallas_Braden\" title=\"Dallas Braden\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dallas_Braden"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ale%C5%A1_Hemsk%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ale%C5%A1_Hemsk%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ale%C5%A1_Hemsk%C3%BD"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Slovak footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_<PERSON><PERSON>%C3%ADk\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_<PERSON><PERSON>%C3%ADk\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_Michal%C3%ADk"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Christian_M%C3%BC<PERSON>_(footballer,_born_1983)\" title=\"<PERSON> (footballer, born 1983)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_M%C3%BC<PERSON>_(footballer,_born_1983)\" title=\"<PERSON> (footballer, born 1983)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer, born 1983)", "link": "https://wikipedia.org/wiki/Christian_M%C3%BCller_(footballer,_born_1983)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Ukrainian tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Niko_Kranj%C4%8Dar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niko_Kranj%C4%8Dar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niko_Kranj%C4%8Dar"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, German rugby player and coach", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> van <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German rugby player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Filipino director, producer, and screenwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Filipino director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Filipino director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Welsh footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>man\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>man"}]}, {"year": "1989", "text": "<PERSON>, New Zealand footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Israel_Jim%C3%A9nez\" title=\"Israel <PERSON>ez\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Jim%C3%A9nez\" title=\"<PERSON>énez\"><PERSON></a>, Mexican footballer", "links": [{"title": "Israel <PERSON>", "link": "https://wikipedia.org/wiki/Israel_Jim%C3%A9nez"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/DeMarcus_Cousins\" title=\"DeMarcus Cousins\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DeMarcus_Cousins\" title=\"DeMarcus Cousins\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/DeMarcus_Cousins"}]}, {"year": "1990", "text": "<PERSON>, French footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Days\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Scottish lawn bowler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawn bowler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawn bowler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gorry\" title=\"Katrina Gorry\"><PERSON></a>, Australian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gorry\" title=\"Katrina Gorry\"><PERSON></a>, Australian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Katrina_Gorry"}]}, {"year": "1992", "text": "<PERSON>, Brazilian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Polish swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tch%C3%B3rz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tch%C3%B3rz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tch%C3%B3rz"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Taiju<PERSON> Walker\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Taiju<PERSON> Walker\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American ice hockey player (d. 2024)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, German tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Hungarian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Dalma_G%C3%A1lfi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dalma_G%C3%A1lfi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dalma_G%C3%A1lfi"}]}, {"year": "1999", "text": "<PERSON>, Canadian singer and actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stella\"><PERSON></a>, Canadian singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, South Korean rapper, singer, dancer and actor", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, South Korean rapper, singer, dancer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, South Korean rapper, singer, dancer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}], "Deaths": [{"year": "587", "text": "<PERSON><PERSON><PERSON><PERSON>, Frankish princess and saint (b. 520)", "html": "587 - <a href=\"https://wikipedia.org/wiki/Radegund\" title=\"Ra<PERSON>gun<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish princess and saint (b. 520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON>gund\" title=\"Radegun<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish princess and saint (b. 520)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radegund"}]}, {"year": "604", "text": "<PERSON>, emperor of the Sui Dynasty (b. 541)", "html": "604 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Su<PERSON>\" title=\"Emperor <PERSON> of Sui\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui Dynasty</a> (b. 541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Su<PERSON>\" title=\"Emperor <PERSON> of Sui\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui Dynasty</a> (b. 541)", "links": [{"title": "Emperor <PERSON> of Sui", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Sui"}, {"title": "Sui dynasty", "link": "https://wikipedia.org/wiki/Sui_dynasty"}]}, {"year": "612", "text": "<PERSON><PERSON><PERSON>, Byzantine empress (b. 580)", "html": "612 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Eudo<PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine empress (b. 580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> E<PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine empress (b. 580)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "662", "text": "<PERSON> the Confessor, Byzantine theologian", "html": "662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a>, Byzantine theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a>, Byzantine theologian", "links": [{"title": "<PERSON> the Confessor", "link": "https://wikipedia.org/wiki/<PERSON>_the_Confessor"}]}, {"year": "696", "text": "<PERSON><PERSON>, Japanese prince", "html": "696 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>\" title=\"Prince <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>\" title=\"Prince <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese prince", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "900", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, king of Lotharingia (b. 870)", "html": "900 - <a href=\"https://wikipedia.org/wiki/Zwentibold\" title=\"Zwentibold\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lotharingia</a> (b. 870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zwentibold\" title=\"Zwentibold\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lotharingia</a> (b. 870)", "links": [{"title": "Zwentibold", "link": "https://wikipedia.org/wiki/Zwentibold"}, {"title": "Lotharingia", "link": "https://wikipedia.org/wiki/Lotharingia"}]}, {"year": "908", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph", "html": "908 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Muktafi\" title=\"Al-Muktafi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Muktafi\" title=\"Al-Muktafi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Muktafi"}]}, {"year": "981", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Goryeo (Korea) (b. 955)", "html": "981 - <a href=\"https://wikipedia.org/wiki/Gyeongjong_of_Goryeo\" title=\"Gyeongjong of Goryeo\"><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Goryeo\" title=\"Goryeo\">Goryeo</a> (<a href=\"https://wikipedia.org/wiki/Korea\" title=\"Korea\">Korea</a>) (b. 955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gyeongjong_of_Goryeo\" title=\"Gyeongjong of Goryeo\"><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Goryeo\" title=\"Goryeo\">Goryeo</a> (<a href=\"https://wikipedia.org/wiki/Korea\" title=\"Korea\">Korea</a>) (b. 955)", "links": [{"title": "<PERSON><PERSON>ongjong of Goryeo", "link": "https://wikipedia.org/wiki/Gyeongjong_of_Goryeo"}, {"title": "Goryeo", "link": "https://wikipedia.org/wiki/Goryeo"}, {"title": "Korea", "link": "https://wikipedia.org/wiki/Korea"}]}, {"year": "1134", "text": "<PERSON> of Hungary, Byzantine empress (b. 1088)", "html": "1134 - <a href=\"https://wikipedia.org/wiki/Irene_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a>, Byzantine empress (b. 1088)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a>, Byzantine empress (b. 1088)", "links": [{"title": "Irene of Hungary", "link": "https://wikipedia.org/wiki/Irene_of_Hungary"}]}, {"year": "1297", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mongol emir", "html": "1297 - <a href=\"https://wikipedia.org/wiki/Nawr%C5%<PERSON><PERSON>_(Mongol_emir)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (Mongol emir)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongol emir", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nawr%C5%<PERSON><PERSON>_(Mongol_emir)\" title=\"<PERSON>w<PERSON><PERSON><PERSON> (Mongol emir)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongol emir", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Mongol emir)", "link": "https://wikipedia.org/wiki/Nawr%C5%ABz_(Mongol_emir)"}]}, {"year": "1311", "text": "<PERSON>, doge of Venice", "html": "1311 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venice</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "1382", "text": "<PERSON> of Aragon, queen of Castile (b. 1358)", "html": "1382 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_Castile\" title=\"<PERSON> of Aragon, Queen of Castile\"><PERSON> of Aragon</a>, queen of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> (b. 1358)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_Castile\" title=\"<PERSON> of Aragon, Queen of Castile\"><PERSON> of Aragon</a>, queen of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> (b. 1358)", "links": [{"title": "<PERSON> of Aragon, Queen of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Castile"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}]}, {"year": "1447", "text": "<PERSON><PERSON><PERSON>, duke of Milan (b. 1392)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Milan\" title=\"Duchy of Milan\">Milan</a> (b. 1392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Milan\" title=\"Duchy of Milan\">Milan</a> (b. 1392)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Duchy of Milan", "link": "https://wikipedia.org/wiki/Duchy_of_Milan"}]}, {"year": "1523", "text": "<PERSON>, Flemish painter (b. 1460)", "html": "1523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (b. 1460)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian sculptor (b. 1529)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/Giam<PERSON>ogna\" title=\"Giam<PERSON>og<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian sculptor (b. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giam<PERSON>ogna\" title=\"Giam<PERSON>og<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian sculptor (b. 1529)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giambologna"}]}, {"year": "1617", "text": "<PERSON>, Swiss clergyman and theologian (b. 1540)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss clergyman and theologian (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss clergyman and theologian (b. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON>, Irish bishop and saint (b. 1613)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish bishop and saint (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish bishop and saint (b. 1613)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, French priest and historian (b. 1610)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and historian (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and historian (b. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, French priest and author (b. 1665)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and author (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and author (b. 1665)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, Danish-American businessman and politician, 39th Mayor of New York City (b. 1678)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American businessman and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American businessman and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1749", "text": "<PERSON>, German poet and critic (b. 1719)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (b. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>-<PERSON><PERSON><PERSON>, English painter (b. 1726)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1726)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1795", "text": "<PERSON><PERSON><PERSON><PERSON>, Queen of Indore (b. 1725)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/Ahily<PERSON><PERSON>_Ho<PERSON>ar\" title=\"Ahily<PERSON><PERSON> Holkar\"><PERSON><PERSON><PERSON><PERSON></a>, Queen of Indore (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahily<PERSON><PERSON>_<PERSON>\" title=\"Ahily<PERSON><PERSON> Holkar\"><PERSON><PERSON><PERSON><PERSON></a>, Queen of Indore (b. 1725)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ar"}]}, {"year": "1826", "text": "<PERSON>, French physician, invented the stethoscope (b. 1781)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, invented the <a href=\"https://wikipedia.org/wiki/Stethoscope\" title=\"Stethoscope\">stethoscope</a> (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, invented the <a href=\"https://wikipedia.org/wiki/Stethoscope\" title=\"Stethoscope\">stethoscope</a> (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>c"}, {"title": "Stethoscope", "link": "https://wikipedia.org/wiki/Stethoscope"}]}, {"year": "1863", "text": "<PERSON>, French painter and lithographer (b. 1798)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>x\" title=\"<PERSON>\"><PERSON></a>, French painter and lithographer (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>x\" title=\"<PERSON>\"><PERSON></a>, French painter and lithographer (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Delacroix"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, Hungarian physician and obstetrician (b. 1818)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian physician and obstetrician (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian physician and obstetrician (b. 1818)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American railway magnate (b. 1821)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Huntington\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American railway magnate (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American railway magnate (b. 1821)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Potter_Huntington"}]}, {"year": "1910", "text": "<PERSON>, Italian-English nurse and theologian (b. 1820)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nightingale\"><PERSON></a>, Italian-English nurse and theologian (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nightingale\"><PERSON></a>, Italian-English nurse and theologian (b. 1820)", "links": [{"title": "Florence Nightingale", "link": "https://wikipedia.org/wiki/Florence_Nightingale"}]}, {"year": "1912", "text": "<PERSON>, French composer (b. 1842)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, German chemist, Nobel Prize laureate (b. 1860)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1934", "text": "<PERSON>, American author and playwright (b. 1868)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Mary <PERSON>\"><PERSON></a>, American author and playwright (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Mary <PERSON>\"><PERSON></a>, American author and playwright (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Soviet aircraft pilot of Polish origin (b. 1902)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Soviet aircraft pilot of Polish origin (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Soviet aircraft pilot of Polish origin (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist, historian, and critic (b. 1866)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"H. G<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, historian, and critic (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"H. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, historian, and critic (b. 1866)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Greek violinist and mandolin player (b. 1886)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek violinist and mandolin player (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek violinist and mandolin player (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Constantine_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American football, basketball player, and coach (b. 1903)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football, basketball player, and coach (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football, basketball player, and coach (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, French cyclist and fencer (b. 1881)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, French cyclist and fencer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, French cyclist and fencer (b. 1881)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Japanese lawyer and politician, 58th Prime Minister of Japan (b. 1899)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Iked<PERSON>\"><PERSON><PERSON></a>, Japanese lawyer and politician, 58th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> I<PERSON>\"><PERSON><PERSON></a>, Japanese lawyer and politician, 58th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1971", "text": "<PERSON><PERSON> <PERSON><PERSON>, English race car driver and engineer, founded Bentley Motors Limited (b. 1888)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"W. O<PERSON> Bentley\"><PERSON><PERSON> <PERSON><PERSON></a>, English race car driver and engineer, founded <a href=\"https://wikipedia.org/wiki/Bentley\" title=\"Bentley\">Bentley Motors Limited</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"W. O<PERSON> Bentley\"><PERSON><PERSON> <PERSON><PERSON></a>, English race car driver and engineer, founded <a href=\"https://wikipedia.org/wiki/Bentley\" title=\"Bentley\">Bentley Motors Limited</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Bentley", "link": "https://wikipedia.org/wiki/Bentley"}]}, {"year": "1974", "text": "<PERSON>, American broadcaster and designer of the flag of South Dakota (b. 1888)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster and designer of the <a href=\"https://wikipedia.org/wiki/Flag_of_South_Dakota\" title=\"Flag of South Dakota\">flag of South Dakota</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_M<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster and designer of the <a href=\"https://wikipedia.org/wiki/Flag_of_South_Dakota\" title=\"Flag of South Dakota\">flag of South Dakota</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flag of South Dakota", "link": "https://wikipedia.org/wiki/Flag_of_South_Dakota"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Brazilian poet and telegrapher (b. 1901)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian poet and telegrapher (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian poet and telegrapher (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>des"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American wrestler (b. 1944)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lonnie_<PERSON>ne"}]}, {"year": "1979", "text": "<PERSON>, American painter and sculptor (b. 1887)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Georgian-Armenian chess player (b. 1929)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian-Armenian chess player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian-Armenian chess player (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tig<PERSON>_Pet<PERSON>ian"}]}, {"year": "1986", "text": "<PERSON>, American actress (b. 1913)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American race car driver (b. 1955)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American police officer and politician (b. 1944)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Larkin <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American police officer and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Larkin <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American police officer and politician (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American general and politician (b. 1907)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English mountaineer (b. 1963)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Czech-English psychologist and author (b. 1926)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Jan_K%C5%99esadlo\" title=\"<PERSON>\"><PERSON></a>, Czech-English psychologist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C5%99esadlo\" title=\"<PERSON>\"><PERSON></a>, Czech-English psychologist and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_K%C5%99esadlo"}]}, {"year": "1995", "text": "<PERSON>, American baseball player and sportscaster (b. 1931)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese general and politician, 14th President of Portugal (b. 1910)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_de_Sp%C3%ADnola\" title=\"<PERSON><PERSON><PERSON><PERSON>p<PERSON>ola\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese general and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_de_Sp%C3%ADnola\" title=\"<PERSON><PERSON><PERSON><PERSON> Spínola\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese general and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1910)", "links": [{"title": "António de Spínola", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_de_Sp%C3%ADnola"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Italian-French singer-songwriter and guitarist (b. 1934)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French singer-songwriter and guitarist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French singer-songwriter and guitarist (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rrer"}]}, {"year": "1998", "text": "<PERSON>, Ukrainian-American physicist and academic (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American physicist and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American physicist and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American author (b. 1900)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Dominican-American baseball player (b. 1947)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, German Jewish religious leader (b. 1927)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Jewish religious leader (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Jewish religious leader (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Colombian journalist and lawyer (b. 1960)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist and lawyer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist and lawyer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jaime_G<PERSON>z%C3%B3n"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Pakistani singer-songwriter (b. 1965)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani singer-songwriter (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani singer-songwriter (b. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Austrian race car driver (b. 1947)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American baseball player and manager (b. 1923)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1950s_pitcher)\" title=\"<PERSON> (1950s pitcher)\"><PERSON></a>, American baseball player and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1950s_pitcher)\" title=\"<PERSON> (1950s pitcher)\"><PERSON></a>, American baseball player and manager (b. 1923)", "links": [{"title": "<PERSON> (1950s pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1950s_pitcher)"}]}, {"year": "2001", "text": "<PERSON>, American author (b. 1909)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter and producer (b. 1929)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American chef, author, and television host (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julia Child\"><PERSON></a>, American chef, author, and television host (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julia Child\"><PERSON></a>, American chef, author, and television host (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Brazilian lawyer and politician (b. 1916)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, New Zealand lawyer and politician, 32nd Prime Minister of New Zealand (b. 1942)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "2006", "text": "<PERSON>, English actor and singer (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jay\"><PERSON></a>, English actor and singer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Swedish musician (b. 1975)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6dtveidt\" title=\"<PERSON>\"><PERSON></a>, Swedish musician (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6dtveidt\" title=\"<PERSON>\"><PERSON></a>, Swedish musician (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jon_N%C3%B6dtveidt"}]}, {"year": "2007", "text": "<PERSON>, American wrestler (b. 1964)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (b. 1964)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>(wrestler)"}]}, {"year": "2007", "text": "<PERSON>, American philanthropist and socialite (b. 1902)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and socialite (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and socialite (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American baseball player and sportscaster (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, French mathematician and academic (b. 1904)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American politician (b. 1959)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American businessman (b. 1901)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, American basketball player (b. 1980)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lton"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer (b. 1976)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panagiot<PERSON>_<PERSON>ramis"}]}, {"year": "2010", "text": "<PERSON>, American wrestler (b. 1981)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cade\"><PERSON></a>, American wrestler (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cade\"><PERSON></a>, American wrestler (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American journalist and author (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Bangladeshi director, producer, and screenwriter (b. 1957)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Tare<PERSON>_Ma<PERSON>\" title=\"Tareque <PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi director, producer, and screenwriter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tare<PERSON>_<PERSON>\" title=\"Ta<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi director, producer, and screenwriter (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>reque_<PERSON>d"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Bangladeshi journalist and cinematographer (b. 1959)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi journalist and cinematographer (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi journalist and cinematographer (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American philosopher and academic (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American journalist and author (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian cricketer and coach (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player and manager (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actress and singer (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, German politician (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Lothar_Bisky\" title=\"Lothar Bisky\"><PERSON><PERSON></a>, German politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lot<PERSON>_Bisky\" title=\"Lothar Bisky\"><PERSON><PERSON></a>, German politician (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lothar_Bisky"}]}, {"year": "2013", "text": "<PERSON>, Jr., American businessman and philanthropist (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman and philanthropist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman and philanthropist (b. 1927)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2013", "text": "<PERSON>, French footballer and manager (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Dutch flute player and conductor (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Frans_Br%C3%BCggen\" title=\"Frans Brüggen\"><PERSON><PERSON></a>, Dutch flute player and conductor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frans_Br%C3%BCggen\" title=\"Frans Brüggen\"><PERSON><PERSON></a>, Dutch flute player and conductor (b. 1934)", "links": [{"title": "Frans Brüggen", "link": "https://wikipedia.org/wiki/Frans_Br%C3%BCggen"}]}, {"year": "2014", "text": "<PERSON>, Brazilian politician, 14th Brazilian Minister of Science and Technology (b. 1965)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian politician, 14th <a href=\"https://wikipedia.org/wiki/Ministry_of_Science,_Technology_and_Innovation_(Brazil)\" title=\"Ministry of Science, Technology and Innovation (Brazil)\">Brazilian Minister of Science and Technology</a> (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian politician, 14th <a href=\"https://wikipedia.org/wiki/Ministry_of_Science,_Technology_and_Innovation_(Brazil)\" title=\"Ministry of Science, Technology and Innovation (Brazil)\">Brazilian Minister of Science and Technology</a> (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Science, Technology and Innovation (Brazil)", "link": "https://wikipedia.org/wiki/Ministry_of_Science,_Technology_and_Innovation_(Brazil)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Italian race car driver (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer and manager (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_Seba\" title=\"Süleyman Seba\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_Seba\" title=\"Süleyman Seba\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and manager (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%BCleyman_Seba"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Iraqi politician, Iraqi Minister of Interior (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Wat<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>at<PERSON> al<PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Iraq)\" title=\"Ministry of Interior (Iraq)\">Iraqi Minister of Interior</a> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wat<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Wat<PERSON> al<PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Iraq)\" title=\"Ministry of Interior (Iraq)\">Iraqi Minister of Interior</a> (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wat<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of Interior (Iraq)", "link": "https://wikipedia.org/wiki/Ministry_of_Interior_(Iraq)"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player and manager (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian businessman and philanthropist, co-founded Hero Cycles (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Prakash <PERSON>\"><PERSON><PERSON></a>, Indian businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Hero_Cycles\" title=\"Hero Cycles\">Hero Cycles</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Hero_Cycles\" title=\"Hero Cycles\">Hero Cycles</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Hero Cycles", "link": "https://wikipedia.org/wiki/Hero_Cycles"}]}, {"year": "2016", "text": "<PERSON>, English actor and musician (b. 1934)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)\" title=\"<PERSON> (English actor)\"><PERSON></a>, English actor and musician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)\" title=\"<PERSON> (English actor)\"><PERSON></a>, English actor and musician (b. 1934)", "links": [{"title": "<PERSON> (English actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian Hindu leader (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Swami_<PERSON>j\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>j\"><PERSON><PERSON><PERSON><PERSON></a>, Indian Hindu leader (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Swami_<PERSON>j\" title=\"<PERSON><PERSON><PERSON><PERSON> Swami Ma<PERSON>j\"><PERSON><PERSON><PERSON><PERSON></a>, Indian Hindu leader (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>j"}]}, {"year": "2018", "text": "<PERSON>, American wrestler (b. 1955)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, American singer-songwriter (b. 1953)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Griffith"}]}, {"year": "2024", "text": "<PERSON>, American politician (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American entrepreneur, founder of <PERSON> (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_Amos\" title=\"<PERSON> Amos\"><PERSON></a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_Amos\" title=\"<PERSON> Amos\"><PERSON></a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Famous Amos", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Italian screenwriter (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian screenwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American basketball player and coach (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}