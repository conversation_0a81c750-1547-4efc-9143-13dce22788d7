{"date": "December 9", "url": "https://wikipedia.org/wiki/December_9", "data": {"Events": [{"year": "536", "text": "Gothic War: The Byzantine general <PERSON><PERSON><PERSON> enters Rome unopposed; the Gothic garrison flees the capital.", "html": "536 - <a href=\"https://wikipedia.org/wiki/Gothic_War_(535%E2%80%93554)\" title=\"Gothic War (535-554)\">Gothic War</a>: The Byzantine general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>rius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> enters Rome unopposed; the <a href=\"https://wikipedia.org/wiki/Goths\" title=\"Goths\">Gothic</a> garrison flees the capital.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gothic_War_(535%E2%80%93554)\" title=\"Gothic War (535-554)\">Gothic War</a>: The Byzantine general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>rius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> enters Rome unopposed; the <a href=\"https://wikipedia.org/wiki/Goths\" title=\"Goths\">Gothic</a> garrison flees the capital.", "links": [{"title": "Gothic War (535-554)", "link": "https://wikipedia.org/wiki/Gothic_War_(535%E2%80%93554)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belisarius"}, {"title": "Goths", "link": "https://wikipedia.org/wiki/Goths"}]}, {"year": "730", "text": "Battle of Marj Ardabil: The Khazars annihilate an Umayyad army and kill its commander, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>.", "html": "730 - <a href=\"https://wikipedia.org/wiki/Battle_of_Marj_Ardabil\" title=\"Battle of Marj Ardabil\">Battle of Marj Ardabil</a>: The <a href=\"https://wikipedia.org/wiki/Khazars\" title=\"Khazars\">Khazars</a> annihilate an <a href=\"https://wikipedia.org/wiki/Umayyad\" class=\"mw-redirect\" title=\"Umayyad\">Umayyad</a> army and kill its commander, <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Marj_Ardabil\" title=\"Battle of Marj Ardabil\">Battle of Marj Ardabil</a>: The <a href=\"https://wikipedia.org/wiki/Khazars\" title=\"Khazars\">Khazars</a> annihilate an <a href=\"https://wikipedia.org/wiki/Umayyad\" class=\"mw-redirect\" title=\"Umayyad\">Umayyad</a> army and kill its commander, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>.", "links": [{"title": "Battle of Marj Ardabil", "link": "https://wikipedia.org/wiki/Battle_of_Marj_Ardabil"}, {"title": "Khazars", "link": "https://wikipedia.org/wiki/Khazars"}, {"title": "Umayyad", "link": "https://wikipedia.org/wiki/Umayyad"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1432", "text": "The first battle between the forces of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> is fought near the town of Oszmiana (Ashmyany), launching the most active phase of the Lithuanian Civil War.", "html": "1432 - The first battle between the forces of <a href=\"https://wikipedia.org/wiki/%C5%A0vitrigaila\" title=\"Švitrigaila\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_K%C4%99stutaitis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is fought near the town of <a href=\"https://wikipedia.org/wiki/Ashmyany\" title=\"Ashmyany\">Oszmiana (Ashmyany)</a>, launching the most active phase of the <a href=\"https://wikipedia.org/wiki/Lithuanian_Civil_War_(1431%E2%80%9335)\" class=\"mw-redirect\" title=\"Lithuanian Civil War (1431-35)\">Lithuanian Civil War</a>.", "no_year_html": "The first battle between the forces of <a href=\"https://wikipedia.org/wiki/%C5%A0vitrigaila\" title=\"Švitrigaila\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_K%C4%99stutaitis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is fought near the town of <a href=\"https://wikipedia.org/wiki/Ashmyany\" title=\"Ashmyany\">Oszmiana (Ashmyany)</a>, launching the most active phase of the <a href=\"https://wikipedia.org/wiki/Lithuanian_Civil_War_(1431%E2%80%9335)\" class=\"mw-redirect\" title=\"Lithuanian Civil War (1431-35)\">Lithuanian Civil War</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%A0vitrigaila"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigismund_K%C4%99stutaitis"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashmyany"}, {"title": "Lithuanian Civil War (1431-35)", "link": "https://wikipedia.org/wiki/Lithuanian_Civil_War_(1431%E2%80%9335)"}]}, {"year": "1531", "text": "The Virgin of Guadalupe first appears to <PERSON> at Tepeyac, Mexico City.", "html": "1531 - <a href=\"https://wikipedia.org/wiki/Our_Lady_of_Guadalupe\" title=\"Our Lady of Guadalupe\">The Virgin of Guadalupe</a> first appears to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Juan Diego\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Tepeyac\" title=\"Tepeyac\">Tepeyac, Mexico City</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Our_Lady_of_Guadalupe\" title=\"Our Lady of Guadalupe\">The Virgin of Guadalupe</a> first appears to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Juan Diego\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Tepeyac\" title=\"Tepeyac\">Tepeyac, Mexico City</a>.", "links": [{"title": "Our Lady of Guadalupe", "link": "https://wikipedia.org/wiki/Our_Lady_of_Guadalupe"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Diego"}, {"title": "Tepeyac", "link": "https://wikipedia.org/wiki/Tepeyac"}]}, {"year": "1636", "text": "The Qing dynasty of China, led by Emperor <PERSON>, invades Joseon.", "html": "1636 - The <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty of China</a>, led by Emperor <a href=\"https://wikipedia.org/wiki/Hong_Taiji\" title=\"Hong Taiji\">Hong Tai<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Qing_invasion_of_Joseon\" title=\"Qing invasion of Joseon\">invades</a> <a href=\"https://wikipedia.org/wiki/Joseon\" title=\"Joseon\">Joseon</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty of China</a>, led by Emperor <a href=\"https://wikipedia.org/wiki/Hong_Taiji\" title=\"Hong Taiji\">Hong Taiji</a>, <a href=\"https://wikipedia.org/wiki/Qing_invasion_of_Joseon\" title=\"Qing invasion of Joseon\">invades</a> <a href=\"https://wikipedia.org/wiki/Joseon\" title=\"Joseon\">Joseon</a>.", "links": [{"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Hong Taiji", "link": "https://wikipedia.org/wiki/Hong_Taiji"}, {"title": "Qing invasion of Joseon", "link": "https://wikipedia.org/wiki/Qing_invasion_of_Joseon"}, {"title": "Joseon", "link": "https://wikipedia.org/wiki/Joseon"}]}, {"year": "1688", "text": "Glorious Revolution: Williamite forces defeat Jacobites at Battle of Reading, forcing <PERSON> to flee England. (Date is Old Style; the date in the New Style modern calendar is 19 December.)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: Williamite forces defeat Jacobites at <a href=\"https://wikipedia.org/wiki/Battle_of_Reading_(1688)\" title=\"Battle of Reading (1688)\">Battle of Reading</a>, forcing <a href=\"https://wikipedia.org/wiki/James_II_of_England\" title=\"James II of England\"><PERSON> II</a> to flee England. (Date is <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">Old Style</a>; the date in the New Style modern calendar is 19 December.)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: Williamite forces defeat Jacobites at <a href=\"https://wikipedia.org/wiki/Battle_of_Reading_(1688)\" title=\"Battle of Reading (1688)\">Battle of Reading</a>, forcing <a href=\"https://wikipedia.org/wiki/James_II_of_England\" title=\"James II of England\"><PERSON> II</a> to flee England. (Date is <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">Old Style</a>; the date in the New Style modern calendar is 19 December.)", "links": [{"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}, {"title": "Battle of Reading (1688)", "link": "https://wikipedia.org/wiki/Battle_of_Reading_(1688)"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}]}, {"year": "1775", "text": "American Revolutionary War: British troops and Loyalists, misinformed about Patriot militia strength, lose the Battle of Great Bridge, ending British rule in Virginia.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British troops and <a href=\"https://wikipedia.org/wiki/Loyalist_(American_Revolution)\" title=\"Loyalist (American Revolution)\">Loyalists</a>, misinformed about <a href=\"https://wikipedia.org/wiki/Patriot_(American_Revolution)\" title=\"Patriot (American Revolution)\">Patriot</a> militia strength, lose the <a href=\"https://wikipedia.org/wiki/Battle_of_Great_Bridge\" title=\"Battle of Great Bridge\">Battle of Great Bridge</a>, ending British rule in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British troops and <a href=\"https://wikipedia.org/wiki/Loyalist_(American_Revolution)\" title=\"Loyalist (American Revolution)\">Loyalists</a>, misinformed about <a href=\"https://wikipedia.org/wiki/Patriot_(American_Revolution)\" title=\"Patriot (American Revolution)\">Patriot</a> militia strength, lose the <a href=\"https://wikipedia.org/wiki/Battle_of_Great_Bridge\" title=\"Battle of Great Bridge\">Battle of Great Bridge</a>, ending British rule in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Loyalist (American Revolution)", "link": "https://wikipedia.org/wiki/Loyalist_(American_Revolution)"}, {"title": "Patriot (American Revolution)", "link": "https://wikipedia.org/wiki/Patriot_(American_Revolution)"}, {"title": "Battle of Great Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Great_Bridge"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}, {"year": "1822", "text": "French physicist <PERSON><PERSON><PERSON><PERSON>, in a memoir read to the Academy of Sciences, coins the terms linear polarization, circular polarization, and elliptical polarization, and reports a direct refraction experiment verifying his theory that optical rotation is a form of birefringence.", "html": "1822 - French physicist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">August<PERSON>-<PERSON></a>, in a memoir read to the <a href=\"https://wikipedia.org/wiki/French_Academy_of_Sciences\" title=\"French Academy of Sciences\">Academy of Sciences</a>, coins the terms <i><a href=\"https://wikipedia.org/wiki/Linear_polarization\" title=\"Linear polarization\">linear polarization</a></i>, <i><a href=\"https://wikipedia.org/wiki/Circular_polarization\" title=\"Circular polarization\">circular polarization</a></i>, and <i><a href=\"https://wikipedia.org/wiki/Elliptical_polarization\" title=\"Elliptical polarization\">elliptical polarization</a></i>, and reports a direct refraction experiment verifying his theory that <a href=\"https://wikipedia.org/wiki/Optical_rotation\" title=\"Optical rotation\">optical rotation</a> is a form of <a href=\"https://wikipedia.org/wiki/Birefringence\" title=\"Birefringence\">birefringence</a>.", "no_year_html": "French physicist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">August<PERSON>-<PERSON></a>, in a memoir read to the <a href=\"https://wikipedia.org/wiki/French_Academy_of_Sciences\" title=\"French Academy of Sciences\">Academy of Sciences</a>, coins the terms <i><a href=\"https://wikipedia.org/wiki/Linear_polarization\" title=\"Linear polarization\">linear polarization</a></i>, <i><a href=\"https://wikipedia.org/wiki/Circular_polarization\" title=\"Circular polarization\">circular polarization</a></i>, and <i><a href=\"https://wikipedia.org/wiki/Elliptical_polarization\" title=\"Elliptical polarization\">elliptical polarization</a></i>, and reports a direct refraction experiment verifying his theory that <a href=\"https://wikipedia.org/wiki/Optical_rotation\" title=\"Optical rotation\">optical rotation</a> is a form of <a href=\"https://wikipedia.org/wiki/Birefringence\" title=\"Birefringence\">birefringence</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}, {"title": "French Academy of Sciences", "link": "https://wikipedia.org/wiki/French_Academy_of_Sciences"}, {"title": "Linear polarization", "link": "https://wikipedia.org/wiki/Linear_polarization"}, {"title": "Circular polarization", "link": "https://wikipedia.org/wiki/Circular_polarization"}, {"title": "Elliptical polarization", "link": "https://wikipedia.org/wiki/Elliptical_polarization"}, {"title": "Optical rotation", "link": "https://wikipedia.org/wiki/Optical_rotation"}, {"title": "Birefringence", "link": "https://wikipedia.org/wiki/Birefringence"}]}, {"year": "1824", "text": "Patriot forces led by General <PERSON> defeat a Royalist army in the Battle of Ayacucho, putting an end to the Peruvian War of Independence.", "html": "1824 - Patriot forces led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat a Royalist army in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ayacucho\" title=\"Battle of Ayacucho\">Battle of Ayacucho</a>, putting an end to the <a href=\"https://wikipedia.org/wiki/Peruvian_War_of_Independence\" title=\"Peruvian War of Independence\">Peruvian War of Independence</a>.", "no_year_html": "Patriot forces led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat a Royalist army in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ayacucho\" title=\"Battle of Ayacucho\">Battle of Ayacucho</a>, putting an end to the <a href=\"https://wikipedia.org/wiki/Peruvian_War_of_Independence\" title=\"Peruvian War of Independence\">Peruvian War of Independence</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>cre"}, {"title": "Battle of Ayacucho", "link": "https://wikipedia.org/wiki/Battle_of_Ayacucho"}, {"title": "Peruvian War of Independence", "link": "https://wikipedia.org/wiki/Peruvian_War_of_Independence"}]}, {"year": "1835", "text": "Texas Revolution: The Texian Army captures San Antonio following the Siege of Béxar.", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Texas_Revolution\" title=\"Texas Revolution\">Texas Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Texian_Army\" title=\"Texian Army\">Texian Army</a> captures <a href=\"https://wikipedia.org/wiki/San_Antonio,_Texas\" class=\"mw-redirect\" title=\"San Antonio, Texas\">San Antonio</a> following the <a href=\"https://wikipedia.org/wiki/Siege_of_B%C3%A9xar\" title=\"Siege of Béxar\">Siege of Béxar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_Revolution\" title=\"Texas Revolution\">Texas Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Texian_Army\" title=\"Texian Army\">Texian Army</a> captures <a href=\"https://wikipedia.org/wiki/San_Antonio,_Texas\" class=\"mw-redirect\" title=\"San Antonio, Texas\">San Antonio</a> following the <a href=\"https://wikipedia.org/wiki/Siege_of_B%C3%A9xar\" title=\"Siege of Béxar\">Siege of Béxar</a>.", "links": [{"title": "Texas Revolution", "link": "https://wikipedia.org/wiki/Texas_Revolution"}, {"title": "Texian Army", "link": "https://wikipedia.org/wiki/Texian_Army"}, {"title": "San Antonio, Texas", "link": "https://wikipedia.org/wiki/San_Antonio,_Texas"}, {"title": "Siege of Béxar", "link": "https://wikipedia.org/wiki/Siege_of_B%C3%A9xar"}]}, {"year": "1851", "text": "The first YMCA in North America is established in Montreal.", "html": "1851 - The first <a href=\"https://wikipedia.org/wiki/YMCA\" title=\"YMCA\">YMCA</a> in North America is established in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/YMCA\" title=\"YMCA\">YMCA</a> in North America is established in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "links": [{"title": "YMCA", "link": "https://wikipedia.org/wiki/YMCA"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}]}, {"year": "1856", "text": "The Iranian city of Bushehr surrenders to occupying British forces.", "html": "1856 - The <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> city of <a href=\"https://wikipedia.org/wiki/Bushehr\" title=\"Bushehr\">Bushehr</a> surrenders to occupying British forces.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> city of <a href=\"https://wikipedia.org/wiki/Bushehr\" title=\"Bushehr\">Bushehr</a> surrenders to occupying British forces.", "links": [{"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1861", "text": "American Civil War: The Joint Committee on the Conduct of the War is established by Congress.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Joint_Committee_on_the_Conduct_of_the_War\" class=\"mw-redirect\" title=\"Joint Committee on the Conduct of the War\">Joint Committee on the Conduct of the War</a> is established by <a href=\"https://wikipedia.org/wiki/Congress_of_the_United_States\" class=\"mw-redirect\" title=\"Congress of the United States\">Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Joint_Committee_on_the_Conduct_of_the_War\" class=\"mw-redirect\" title=\"Joint Committee on the Conduct of the War\">Joint Committee on the Conduct of the War</a> is established by <a href=\"https://wikipedia.org/wiki/Congress_of_the_United_States\" class=\"mw-redirect\" title=\"Congress of the United States\">Congress</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Joint Committee on the Conduct of the War", "link": "https://wikipedia.org/wiki/Joint_Committee_on_the_Conduct_of_the_War"}, {"title": "Congress of the United States", "link": "https://wikipedia.org/wiki/Congress_of_the_United_States"}]}, {"year": "1868", "text": "The first traffic lights are installed, outside the Palace of Westminster in London. Resembling railway signals, they use semaphore arms and are illuminated at night by red and green gas lamps.", "html": "1868 - The first <a href=\"https://wikipedia.org/wiki/Traffic_light\" title=\"Traffic light\">traffic lights</a> are installed, outside the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Palace of Westminster</a> in London. Resembling <a href=\"https://wikipedia.org/wiki/Railway_signal\" title=\"Railway signal\">railway signals</a>, they use <a href=\"https://wikipedia.org/wiki/Railway_semaphore_signal\" title=\"Railway semaphore signal\">semaphore</a> arms and are illuminated at night by red and green <a href=\"https://wikipedia.org/wiki/Gas_lamp\" class=\"mw-redirect\" title=\"Gas lamp\">gas lamps</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Traffic_light\" title=\"Traffic light\">traffic lights</a> are installed, outside the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Palace of Westminster</a> in London. Resembling <a href=\"https://wikipedia.org/wiki/Railway_signal\" title=\"Railway signal\">railway signals</a>, they use <a href=\"https://wikipedia.org/wiki/Railway_semaphore_signal\" title=\"Railway semaphore signal\">semaphore</a> arms and are illuminated at night by red and green <a href=\"https://wikipedia.org/wiki/Gas_lamp\" class=\"mw-redirect\" title=\"Gas lamp\">gas lamps</a>.", "links": [{"title": "Traffic light", "link": "https://wikipedia.org/wiki/Traffic_light"}, {"title": "Palace of Westminster", "link": "https://wikipedia.org/wiki/Palace_of_Westminster"}, {"title": "Railway signal", "link": "https://wikipedia.org/wiki/Railway_signal"}, {"title": "Railway semaphore signal", "link": "https://wikipedia.org/wiki/Railway_semaphore_signal"}, {"title": "Gas lamp", "link": "https://wikipedia.org/wiki/Gas_lamp"}]}, {"year": "1872", "text": "In Louisiana, <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> becomes the first African American governor of a U.S. state following the impeachment of <PERSON>.", "html": "1872 - In <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>, <a href=\"https://wikipedia.org/wiki/P._B._S._Pinchback\" title=\"P. B. S. Pinchback\">P. B. S<PERSON> Pinchback</a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> governor of a U.S. state following the impeachment of <a href=\"https://wikipedia.org/wiki/Henry_<PERSON>._Warmoth\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>, <a href=\"https://wikipedia.org/wiki/P._B._S._Pinchback\" title=\"P. B. S. Pinchback\">P. B. S<PERSON> Pinchback</a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> governor of a U.S. state following the impeachment of <a href=\"https://wikipedia.org/wiki/Henry_<PERSON>._<PERSON>oth\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}, {"title": "P. B. S. <PERSON>back", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON>._<PERSON>._Pinchback"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>oth"}]}, {"year": "1905", "text": "In France, a law separating church and state is passed.", "html": "1905 - In France, a <a href=\"https://wikipedia.org/wiki/1905_French_law_on_the_Separation_of_the_Churches_and_the_State\" title=\"1905 French law on the Separation of the Churches and the State\">law separating church and state</a> is passed.", "no_year_html": "In France, a <a href=\"https://wikipedia.org/wiki/1905_French_law_on_the_Separation_of_the_Churches_and_the_State\" title=\"1905 French law on the Separation of the Churches and the State\">law separating church and state</a> is passed.", "links": [{"title": "1905 French law on the Separation of the Churches and the State", "link": "https://wikipedia.org/wiki/1905_French_law_on_the_Separation_of_the_Churches_and_the_State"}]}, {"year": "1911", "text": "A mine explosion near Briceville, Tennessee, kills 84 miners despite rescue efforts led by the United States Bureau of Mines.", "html": "1911 - A <a href=\"https://wikipedia.org/wiki/Cross_Mountain_Mine_disaster\" title=\"Cross Mountain Mine disaster\">mine explosion</a> near <a href=\"https://wikipedia.org/wiki/Briceville,_Tennessee\" title=\"Briceville, Tennessee\">Briceville, Tennessee</a>, kills 84 miners despite rescue efforts led by the <a href=\"https://wikipedia.org/wiki/United_States_Bureau_of_Mines\" title=\"United States Bureau of Mines\">United States Bureau of Mines</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Cross_Mountain_Mine_disaster\" title=\"Cross Mountain Mine disaster\">mine explosion</a> near <a href=\"https://wikipedia.org/wiki/Briceville,_Tennessee\" title=\"Briceville, Tennessee\">Briceville, Tennessee</a>, kills 84 miners despite rescue efforts led by the <a href=\"https://wikipedia.org/wiki/United_States_Bureau_of_Mines\" title=\"United States Bureau of Mines\">United States Bureau of Mines</a>.", "links": [{"title": "Cross Mountain Mine disaster", "link": "https://wikipedia.org/wiki/Cross_Mountain_Mine_disaster"}, {"title": "Briceville, Tennessee", "link": "https://wikipedia.org/wiki/Briceville,_Tennessee"}, {"title": "United States Bureau of Mines", "link": "https://wikipedia.org/wiki/United_States_Bureau_of_Mines"}]}, {"year": "1917", "text": "World War I: Field Marshal <PERSON><PERSON> captures Jerusalem from the Ottoman Empire.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON></a> captures <a href=\"https://wikipedia.org/wiki/Battle_of_Jerusalem\" title=\"Battle of Jerusalem\">Jerusalem</a> from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON></a> captures <a href=\"https://wikipedia.org/wiki/Battle_of_Jerusalem\" title=\"Battle of Jerusalem\">Jerusalem</a> from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Battle of Jerusalem", "link": "https://wikipedia.org/wiki/Battle_of_Jerusalem"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1917", "text": "World War I: The Kingdom of Romania signs the Armistice of Focșani with the Central Powers.", "html": "1917 - World War I: The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Kingdom of Romania</a> signs the <a href=\"https://wikipedia.org/wiki/Armistice_of_Foc%C8%99ani\" title=\"Armistice of Focșani\">Armistice of Focșani</a> with the <a href=\"https://wikipedia.org/wiki/Central_Powers\" title=\"Central Powers\">Central Powers</a>.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Kingdom of Romania</a> signs the <a href=\"https://wikipedia.org/wiki/Armistice_of_Foc%C8%99ani\" title=\"Armistice of Focșani\">Armistice of Focșani</a> with the <a href=\"https://wikipedia.org/wiki/Central_Powers\" title=\"Central Powers\">Central Powers</a>.", "links": [{"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}, {"title": "Armistice of Focșani", "link": "https://wikipedia.org/wiki/Armistice_of_Foc%C8%99ani"}, {"title": "Central Powers", "link": "https://wikipedia.org/wiki/Central_Powers"}]}, {"year": "1922", "text": "<PERSON> is elected the first president of Poland.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected the first president of Poland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected the first president of Poland.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "The Constituent Cortes approves a constitution which establishes the Second Spanish Republic.", "html": "1931 - The <a href=\"https://wikipedia.org/wiki/Constituent_Cortes\" title=\"Constituent Cortes\">Constituent Cortes</a> approves a <a href=\"https://wikipedia.org/wiki/Spanish_Constitution_of_1931\" title=\"Spanish Constitution of 1931\">constitution</a> which establishes the <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Second Spanish Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constituent_Cortes\" title=\"Constituent Cortes\">Constituent Cortes</a> approves a <a href=\"https://wikipedia.org/wiki/Spanish_Constitution_of_1931\" title=\"Spanish Constitution of 1931\">constitution</a> which establishes the <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Second Spanish Republic</a>.", "links": [{"title": "Constituent Cortes", "link": "https://wikipedia.org/wiki/Constituent_Cortes"}, {"title": "Spanish Constitution of 1931", "link": "https://wikipedia.org/wiki/Spanish_Constitution_of_1931"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}]}, {"year": "1935", "text": "Student protests occur in Beijing's Tiananmen Square, and are subsequently dispersed by government authorities.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/December_9th_Movement\" title=\"December 9th Movement\">Student protests</a> occur in <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a>'s <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a>, and are subsequently dispersed by <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">government authorities</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/December_9th_Movement\" title=\"December 9th Movement\">Student protests</a> occur in <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a>'s <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a>, and are subsequently dispersed by <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">government authorities</a>.", "links": [{"title": "December 9th Movement", "link": "https://wikipedia.org/wiki/December_9th_Movement"}, {"title": "Beijing", "link": "https://wikipedia.org/wiki/Beijing"}, {"title": "Tiananmen Square", "link": "https://wikipedia.org/wiki/Tiananmen_Square"}, {"title": "Kuomintang", "link": "https://wikipedia.org/wiki/Kuomintang"}]}, {"year": "1935", "text": "<PERSON>, an American newspaper editor and muckraker, is killed in a gangland murder.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an American newspaper editor and muckraker, is killed in a gangland murder.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an American newspaper editor and muckraker, is killed in a gangland murder.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "Second Sino-Japanese War: Battle of Nanking: Japanese troops under the command of Lt. Gen. <PERSON><PERSON><PERSON> launch an assault on the Chinese city of Nanking.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Nanking\" title=\"Battle of Nanking\">Battle of Nanking</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> troops under the command of Lt. Gen. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> launch an assault on the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Chinese</a> city of <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanking</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Nanking\" title=\"Battle of Nanking\">Battle of Nanking</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> troops under the command of Lt. Gen. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> launch an assault on the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Chinese</a> city of <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanking</a>.", "links": [{"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}, {"title": "Battle of Nanking", "link": "https://wikipedia.org/wiki/Battle_of_Nanking"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}]}, {"year": "1940", "text": "World War II: Operation Compass: British and Indian troops under the command of Major-General <PERSON> attack Italian forces near Sidi Barrani in Egypt.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Compass\" title=\"Operation Compass\">Operation Compass</a>: British and Indian troops under the command of Major-General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Sidi_Barrani\" title=\"Battle of Sidi Barrani\">attack</a> <a href=\"https://wikipedia.org/wiki/Military_history_of_Italy_during_World_War_II\" title=\"Military history of Italy during World War II\">Italian</a> forces near <a href=\"https://wikipedia.org/wiki/Sidi_Barrani\" title=\"Sidi Barrani\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Compass\" title=\"Operation Compass\">Operation Compass</a>: British and Indian troops under the command of Major-General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Connor\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Sidi_Barrani\" title=\"Battle of Sidi Barrani\">attack</a> <a href=\"https://wikipedia.org/wiki/Military_history_of_Italy_during_World_War_II\" title=\"Military history of Italy during World War II\">Italian</a> forces near <a href=\"https://wikipedia.org/wiki/Sidi_Barrani\" title=\"Sidi Barrani\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Compass", "link": "https://wikipedia.org/wiki/Operation_Compass"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_O%27Connor"}, {"title": "Battle of Sidi Barrani", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>_<PERSON>ani"}, {"title": "Military history of Italy during World War II", "link": "https://wikipedia.org/wiki/Military_history_of_Italy_during_World_War_II"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1941", "text": "World War II: China, Cuba, Guatemala, and the Philippine Commonwealth declare war on Germany and Japan.", "html": "1941 - World War II: <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">China</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>, and <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">the Philippine Commonwealth</a> declare war on <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">China</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>, and <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">the Philippine Commonwealth</a> declare war on <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>.", "links": [{"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}, {"title": "Commonwealth of the Philippines", "link": "https://wikipedia.org/wiki/Commonwealth_of_the_Philippines"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}]}, {"year": "1941", "text": "World War II: The American 19th Bombardment Group attacks Japanese ships off the coast of Vigan, Luzon.", "html": "1941 - World War II: The American <a href=\"https://wikipedia.org/wiki/19th_Operations_Group\" title=\"19th Operations Group\">19th Bombardment Group</a> attacks Japanese ships off the coast of <a href=\"https://wikipedia.org/wiki/Vigan\" title=\"Vigan\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Luzon\" title=\"Luzon\">Luzon</a>.", "no_year_html": "World War II: The American <a href=\"https://wikipedia.org/wiki/19th_Operations_Group\" title=\"19th Operations Group\">19th Bombardment Group</a> attacks Japanese ships off the coast of <a href=\"https://wikipedia.org/wiki/Vigan\" title=\"Vigan\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Luzon\" title=\"Luzon\">Luzon</a>.", "links": [{"title": "19th Operations Group", "link": "https://wikipedia.org/wiki/19th_Operations_Group"}, {"title": "Vigan", "link": "https://wikipedia.org/wiki/Vigan"}, {"title": "Luzon", "link": "https://wikipedia.org/wiki/Luzon"}]}, {"year": "1946", "text": "The subsequent Nuremberg trials begin with the Doctors' Trial, prosecuting physicians and officers alleged to be involved in Nazi human experimentation and mass murder under the guise of euthanasia.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Subsequent_Nuremberg_trials\" title=\"Subsequent Nuremberg trials\">subsequent Nuremberg trials</a> begin with the <a href=\"https://wikipedia.org/wiki/Doctors%27_Trial\" title=\"Doctors' Trial\">Doctors' Trial</a>, prosecuting physicians and officers alleged to be involved in <a href=\"https://wikipedia.org/wiki/Nazi_human_experimentation\" title=\"Nazi human experimentation\">Nazi human experimentation</a> and <a href=\"https://wikipedia.org/wiki/Action_T4\" class=\"mw-redirect\" title=\"Action T4\">mass murder under the guise of euthanasia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Subsequent_Nuremberg_trials\" title=\"Subsequent Nuremberg trials\">subsequent Nuremberg trials</a> begin with the <a href=\"https://wikipedia.org/wiki/Doctors%27_Trial\" title=\"Doctors' Trial\">Doctors' Trial</a>, prosecuting physicians and officers alleged to be involved in <a href=\"https://wikipedia.org/wiki/Nazi_human_experimentation\" title=\"Nazi human experimentation\">Nazi human experimentation</a> and <a href=\"https://wikipedia.org/wiki/Action_T4\" class=\"mw-redirect\" title=\"Action T4\">mass murder under the guise of euthanasia</a>.", "links": [{"title": "Subsequent Nuremberg trials", "link": "https://wikipedia.org/wiki/Subsequent_Nuremberg_trials"}, {"title": "Doctors' Trial", "link": "https://wikipedia.org/wiki/Doctors%27_Trial"}, {"title": "Nazi human experimentation", "link": "https://wikipedia.org/wiki/Nazi_human_experimentation"}, {"title": "Action T4", "link": "https://wikipedia.org/wiki/Action_T4"}]}, {"year": "1946", "text": "The Constituent Assembly of India meets for the first time to write the Constitution of India.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Constituent_Assembly_of_India\" title=\"Constituent Assembly of India\">Constituent Assembly of India</a> meets for the first time to write the <a href=\"https://wikipedia.org/wiki/Constitution_of_India\" title=\"Constitution of India\">Constitution of India</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constituent_Assembly_of_India\" title=\"Constituent Assembly of India\">Constituent Assembly of India</a> meets for the first time to write the <a href=\"https://wikipedia.org/wiki/Constitution_of_India\" title=\"Constitution of India\">Constitution of India</a>.", "links": [{"title": "Constituent Assembly of India", "link": "https://wikipedia.org/wiki/Constituent_Assembly_of_India"}, {"title": "Constitution of India", "link": "https://wikipedia.org/wiki/Constitution_of_India"}]}, {"year": "1948", "text": "The Genocide Convention is adopted.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Genocide_Convention\" title=\"Genocide Convention\">Genocide Convention</a> is adopted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Genocide_Convention\" title=\"Genocide Convention\">Genocide Convention</a> is adopted.", "links": [{"title": "Genocide Convention", "link": "https://wikipedia.org/wiki/Genocide_Convention"}]}, {"year": "1950", "text": "Cold War: <PERSON> is sentenced to 30 years in jail for helping <PERSON> pass information about the Manhattan Project to the Soviet Union. His testimony is later instrumental in the prosecution of <PERSON> and <PERSON>.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harry Gold\"><PERSON></a> is sentenced to 30 years in jail for helping <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pass information about the <a href=\"https://wikipedia.org/wiki/Manhattan_Project\" title=\"Manhattan Project\">Manhattan Project</a> to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>. His testimony is later instrumental in the prosecution of <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gold\"><PERSON></a> is sentenced to 30 years in jail for helping <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pass information about the <a href=\"https://wikipedia.org/wiki/Manhattan_Project\" title=\"Manhattan Project\">Manhattan Project</a> to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>. His testimony is later instrumental in the prosecution of <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Manhattan Project", "link": "https://wikipedia.org/wiki/Manhattan_Project"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>are: General Electric announces that all communist employees will be discharged from the company.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Second_Red_Scare\" class=\"mw-redirect\" title=\"Second Red Scare\">Red Scare</a>: <a href=\"https://wikipedia.org/wiki/General_Electric\" title=\"General Electric\">General Electric</a> announces that all <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">communist</a> employees will be discharged from the company.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Red_Scare\" class=\"mw-redirect\" title=\"Second Red Scare\">Red Scare</a>: <a href=\"https://wikipedia.org/wiki/General_Electric\" title=\"General Electric\">General Electric</a> announces that all <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">communist</a> employees will be discharged from the company.", "links": [{"title": "Second Red Scare", "link": "https://wikipedia.org/wiki/Second_Red_Scare"}, {"title": "General Electric", "link": "https://wikipedia.org/wiki/General_Electric"}, {"title": "Communist", "link": "https://wikipedia.org/wiki/Communist"}]}, {"year": "1956", "text": "Trans-Canada Air Lines Flight 810-9, a Canadair North Star, crashes near Hope, British Columbia, Canada, killing all 62 people on board.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Trans-Canada_Air_Lines_Flight_810-9\" title=\"Trans-Canada Air Lines Flight 810-9\">Trans-Canada Air Lines Flight 810-9</a>, a <a href=\"https://wikipedia.org/wiki/Canadair_North_Star\" title=\"Canadair North Star\">Canadair North Star</a>, crashes near <a href=\"https://wikipedia.org/wiki/Hope,_British_Columbia\" title=\"Hope, British Columbia\">Hope, British Columbia</a>, Canada, killing all 62 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trans-Canada_Air_Lines_Flight_810-9\" title=\"Trans-Canada Air Lines Flight 810-9\">Trans-Canada Air Lines Flight 810-9</a>, a <a href=\"https://wikipedia.org/wiki/Canadair_North_Star\" title=\"Canadair North Star\">Canadair North Star</a>, crashes near <a href=\"https://wikipedia.org/wiki/Hope,_British_Columbia\" title=\"Hope, British Columbia\">Hope, British Columbia</a>, Canada, killing all 62 people on board.", "links": [{"title": "Trans-Canada Air Lines Flight 810-9", "link": "https://wikipedia.org/wiki/Trans-Canada_Air_Lines_Flight_810-9"}, {"title": "Canadair North Star", "link": "https://wikipedia.org/wiki/Canadair_North_Star"}, {"title": "Hope, British Columbia", "link": "https://wikipedia.org/wiki/Hope,_British_Columbia"}]}, {"year": "1956", "text": "An Aeroflot Lisunov Li-2 crashes near Anadyr, killing all 12 people on board.", "html": "1956 - An <a href=\"https://wikipedia.org/wiki/Aeroflot\" title=\"Aeroflot\">Aeroflot</a> <a href=\"https://wikipedia.org/wiki/Lisunov_Li-2\" title=\"Lisunov Li-2\">Lisunov Li-2</a> <a href=\"https://wikipedia.org/wiki/1956_Anadyr_Li-2_crash\" title=\"1956 Anadyr Li-2 crash\">crashes</a> near Anadyr, killing all 12 people on board.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Aeroflot\" title=\"Aeroflot\">Aeroflot</a> <a href=\"https://wikipedia.org/wiki/Lisunov_Li-2\" title=\"Lisunov Li-2\">Lisunov Li-2</a> <a href=\"https://wikipedia.org/wiki/1956_Anadyr_Li-2_crash\" title=\"1956 Anadyr Li-2 crash\">crashes</a> near Anadyr, killing all 12 people on board.", "links": [{"title": "Aeroflot", "link": "https://wikipedia.org/wiki/Aeroflot"}, {"title": "Lisunov Li-2", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ov_Li-2"}, {"title": "1956 Anadyr Li-2 crash", "link": "https://wikipedia.org/wiki/1956_Anady<PERSON>_Li-2_crash"}]}, {"year": "1960", "text": "The first episode of Coronation Street, the world's longest-running television soap opera, is broadcast in the United Kingdom.", "html": "1960 - The first episode of <i><a href=\"https://wikipedia.org/wiki/Coronation_Street\" title=\"Coronation Street\">Coronation Street</a></i>, the world's longest-running television <a href=\"https://wikipedia.org/wiki/Soap_opera\" title=\"Soap opera\">soap opera</a>, is broadcast in the United Kingdom.", "no_year_html": "The first episode of <i><a href=\"https://wikipedia.org/wiki/Coronation_Street\" title=\"Coronation Street\">Coronation Street</a></i>, the world's longest-running television <a href=\"https://wikipedia.org/wiki/Soap_opera\" title=\"Soap opera\">soap opera</a>, is broadcast in the United Kingdom.", "links": [{"title": "Coronation Street", "link": "https://wikipedia.org/wiki/Coronation_Street"}, {"title": "Soap opera", "link": "https://wikipedia.org/wiki/Soap_opera"}]}, {"year": "1961", "text": "Tanganyika becomes independent from Britain.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)\" title=\"Tanganyika (1961-1964)\">Tanganyika</a> becomes independent from Britain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)\" title=\"Tanganyika (1961-1964)\">Tanganyika</a> becomes independent from Britain.", "links": [{"title": "<PERSON><PERSON><PERSON> (1961-1964)", "link": "https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)"}]}, {"year": "1965", "text": "Kecksburg UFO incident: A fireball is seen from Michigan to Pennsylvania; with witnesses reporting something crashing in the woods near Pittsburgh.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Kecksburg_UFO_incident\" title=\"Kecksburg UFO incident\">Kecksburg UFO incident</a>: A fireball is seen from Michigan to Pennsylvania; with witnesses reporting something crashing in the woods near Pittsburgh.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kecksburg_UFO_incident\" title=\"Kecksburg UFO incident\">Kecksburg UFO incident</a>: A fireball is seen from Michigan to Pennsylvania; with witnesses reporting something crashing in the woods near Pittsburgh.", "links": [{"title": "Kecksburg UFO incident", "link": "https://wikipedia.org/wiki/Kecksburg_UFO_incident"}]}, {"year": "1968", "text": "<PERSON> gave what became known as \"The Mother of All Demos\", publicly debuting the computer mouse, hypertext, and the bit-mapped graphical user interface using the oN-Line System (NLS).", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> gave what became known as \"<a href=\"https://wikipedia.org/wiki/The_Mother_of_All_Demos\" title=\"The Mother of All Demos\">The Mother of All Demos</a>\", publicly debuting the <a href=\"https://wikipedia.org/wiki/Computer_mouse\" title=\"Computer mouse\">computer mouse</a>, <a href=\"https://wikipedia.org/wiki/Hypertext\" title=\"Hypertext\">hypertext</a>, and the bit-mapped <a href=\"https://wikipedia.org/wiki/Graphical_user_interface\" title=\"Graphical user interface\">graphical user interface</a> using the <a href=\"https://wikipedia.org/wiki/NLS_(computer_system)\" title=\"NLS (computer system)\">oN-Line System (NLS)</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> gave what became known as \"<a href=\"https://wikipedia.org/wiki/The_Mother_of_All_Demos\" title=\"The Mother of All Demos\">The Mother of All Demos</a>\", publicly debuting the <a href=\"https://wikipedia.org/wiki/Computer_mouse\" title=\"Computer mouse\">computer mouse</a>, <a href=\"https://wikipedia.org/wiki/Hypertext\" title=\"Hypertext\">hypertext</a>, and the bit-mapped <a href=\"https://wikipedia.org/wiki/Graphical_user_interface\" title=\"Graphical user interface\">graphical user interface</a> using the <a href=\"https://wikipedia.org/wiki/NLS_(computer_system)\" title=\"NLS (computer system)\">oN-Line System (NLS)</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Mother of All Demos", "link": "https://wikipedia.org/wiki/The_Mother_of_All_Demos"}, {"title": "Computer mouse", "link": "https://wikipedia.org/wiki/Computer_mouse"}, {"title": "Hypertext", "link": "https://wikipedia.org/wiki/Hypertext"}, {"title": "Graphical user interface", "link": "https://wikipedia.org/wiki/Graphical_user_interface"}, {"title": "NLS (computer system)", "link": "https://wikipedia.org/wiki/NLS_(computer_system)"}]}, {"year": "1969", "text": "U.S. Secretary of State <PERSON> proposes his plan for a ceasefire in the War of Attrition; Egypt and Jordan accept it over the objections of the PLO, which leads to civil war in Jordan in September 1970.", "html": "1969 - U.S. Secretary of State <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> proposes <a href=\"https://wikipedia.org/wiki/Rogers_Plan\" title=\"Rogers Plan\">his plan</a> for a ceasefire in the <a href=\"https://wikipedia.org/wiki/War_of_Attrition\" title=\"War of Attrition\">War of Attrition</a>; Egypt and Jordan accept it over the objections of the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">PLO</a>, which leads to <a href=\"https://wikipedia.org/wiki/Black_September_in_Jordan\" class=\"mw-redirect\" title=\"Black September in Jordan\">civil war in Jordan</a> in September 1970.", "no_year_html": "U.S. Secretary of State <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> proposes <a href=\"https://wikipedia.org/wiki/Rogers_Plan\" title=\"Rogers Plan\">his plan</a> for a ceasefire in the <a href=\"https://wikipedia.org/wiki/War_of_Attrition\" title=\"War of Attrition\">War of Attrition</a>; Egypt and Jordan accept it over the objections of the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">PLO</a>, which leads to <a href=\"https://wikipedia.org/wiki/Black_September_in_Jordan\" class=\"mw-redirect\" title=\"Black September in Jordan\">civil war in Jordan</a> in September 1970.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Rogers Plan", "link": "https://wikipedia.org/wiki/Rogers_Plan"}, {"title": "War of Attrition", "link": "https://wikipedia.org/wiki/War_of_Attrition"}, {"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}, {"title": "Black September in Jordan", "link": "https://wikipedia.org/wiki/Black_September_in_Jordan"}]}, {"year": "1971", "text": "Indo-Pakistani War: The Indian Air Force executes an airdrop of Indian Army units, bypassing Pakistani defences.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1971\">Indo-Pakistani War</a>: The <a href=\"https://wikipedia.org/wiki/Indian_Air_Force\" title=\"Indian Air Force\">Indian Air Force</a> executes <a href=\"https://wikipedia.org/wiki/Meghna_Heli_Bridge\" title=\"Meghna Heli Bridge\">an airdrop</a> of <a href=\"https://wikipedia.org/wiki/Indian_Army\" title=\"Indian Army\">Indian Army</a> units, bypassing Pakistani defences.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1971\">Indo-Pakistani War</a>: The <a href=\"https://wikipedia.org/wiki/Indian_Air_Force\" title=\"Indian Air Force\">Indian Air Force</a> executes <a href=\"https://wikipedia.org/wiki/Meghna_Heli_Bridge\" title=\"Meghna Heli Bridge\">an airdrop</a> of <a href=\"https://wikipedia.org/wiki/Indian_Army\" title=\"Indian Army\">Indian Army</a> units, bypassing Pakistani defences.", "links": [{"title": "Indo-Pakistani War of 1971", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971"}, {"title": "Indian Air Force", "link": "https://wikipedia.org/wiki/Indian_Air_Force"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Indian Army", "link": "https://wikipedia.org/wiki/Indian_Army"}]}, {"year": "1973", "text": "British and Irish authorities sign the Sunningdale Agreement in an attempt to establish a power-sharing Northern Ireland Executive and a cross-border Council of Ireland.", "html": "1973 - British and Irish authorities sign the <a href=\"https://wikipedia.org/wiki/Sunningdale_Agreement\" title=\"Sunningdale Agreement\">Sunningdale Agreement</a> in an attempt to establish a power-sharing <a href=\"https://wikipedia.org/wiki/Executive_of_the_1974_Northern_Ireland_Assembly\" title=\"Executive of the 1974 Northern Ireland Assembly\">Northern Ireland Executive</a> and a cross-border <a href=\"https://wikipedia.org/wiki/Council_of_Ireland\" title=\"Council of Ireland\">Council of Ireland</a>.", "no_year_html": "British and Irish authorities sign the <a href=\"https://wikipedia.org/wiki/Sunningdale_Agreement\" title=\"Sunningdale Agreement\">Sunningdale Agreement</a> in an attempt to establish a power-sharing <a href=\"https://wikipedia.org/wiki/Executive_of_the_1974_Northern_Ireland_Assembly\" title=\"Executive of the 1974 Northern Ireland Assembly\">Northern Ireland Executive</a> and a cross-border <a href=\"https://wikipedia.org/wiki/Council_of_Ireland\" title=\"Council of Ireland\">Council of Ireland</a>.", "links": [{"title": "Sunningdale Agreement", "link": "https://wikipedia.org/wiki/Sunningdale_Agreement"}, {"title": "Executive of the 1974 Northern Ireland Assembly", "link": "https://wikipedia.org/wiki/Executive_of_the_1974_Northern_Ireland_Assembly"}, {"title": "Council of Ireland", "link": "https://wikipedia.org/wiki/Council_of_Ireland"}]}, {"year": "1979", "text": "The eradication of the smallpox virus is certified, making smallpox the first of only two diseases that have been driven to extinction (with rinderpest in 2011 being the other).", "html": "1979 - The eradication of the <a href=\"https://wikipedia.org/wiki/Smallpox\" title=\"Smallpox\">smallpox</a> <a href=\"https://wikipedia.org/wiki/Virus\" title=\"Virus\">virus</a> is certified, making smallpox the first of only two diseases that have been driven to extinction (with <a href=\"https://wikipedia.org/wiki/Rinderpest\" title=\"Rinderpest\">rinderpest</a> in 2011 being the other).", "no_year_html": "The eradication of the <a href=\"https://wikipedia.org/wiki/Smallpox\" title=\"Smallpox\">smallpox</a> <a href=\"https://wikipedia.org/wiki/Virus\" title=\"Virus\">virus</a> is certified, making smallpox the first of only two diseases that have been driven to extinction (with <a href=\"https://wikipedia.org/wiki/Rinderpest\" title=\"Rinderpest\">rinderpest</a> in 2011 being the other).", "links": [{"title": "Smallpox", "link": "https://wikipedia.org/wiki/Smallpox"}, {"title": "Virus", "link": "https://wikipedia.org/wiki/Virus"}, {"title": "Rinderpest", "link": "https://wikipedia.org/wiki/Rinderpest"}]}, {"year": "1987", "text": "Israeli-Palestinian conflict: The First Intifada begins in the Gaza Strip and West Bank.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: The <a href=\"https://wikipedia.org/wiki/First_Intifada\" title=\"First Intifada\">First Intifada</a> begins in the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> and <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: The <a href=\"https://wikipedia.org/wiki/First_Intifada\" title=\"First Intifada\">First Intifada</a> begins in the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> and <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a>.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "First Intifada", "link": "https://wikipedia.org/wiki/First_Intifada"}, {"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}, {"title": "West Bank", "link": "https://wikipedia.org/wiki/West_Bank"}]}, {"year": "1992", "text": "American troops land in Somalia for Operation Restore Hope.", "html": "1992 - American troops land in <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a> for <a href=\"https://wikipedia.org/wiki/Unified_Task_Force\" title=\"Unified Task Force\">Operation Restore Hope</a>.", "no_year_html": "American troops land in <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a> for <a href=\"https://wikipedia.org/wiki/Unified_Task_Force\" title=\"Unified Task Force\">Operation Restore Hope</a>.", "links": [{"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}, {"title": "Unified Task Force", "link": "https://wikipedia.org/wiki/Unified_Task_Force"}]}, {"year": "1996", "text": "<PERSON> is acquitted of committing an indecent act, giving women the right to be topless in Ontario, Canada.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Female_toplessness_in_Canada#<PERSON>_<PERSON>\" title=\"Female toplessness in Canada\"><PERSON> is acquitted</a> of committing an indecent act, giving women the right to be topless in Ontario, Canada.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Female_toplessness_in_Canada#<PERSON>_<PERSON>\" title=\"Female toplessness in Canada\"><PERSON> is acquitted</a> of committing an indecent act, giving women the right to be topless in Ontario, Canada.", "links": [{"title": "Female toplessness in Canada", "link": "https://wikipedia.org/wiki/Female_toplessness_in_Canada#<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "A blast in the center of Moscow kills six people and wounds several more.", "html": "2003 - A <a href=\"https://wikipedia.org/wiki/2003_Red_Square_bombing\" title=\"2003 Red Square bombing\">blast</a> in the center of Moscow kills six people and wounds several more.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2003_Red_Square_bombing\" title=\"2003 Red Square bombing\">blast</a> in the center of Moscow kills six people and wounds several more.", "links": [{"title": "2003 Red Square bombing", "link": "https://wikipedia.org/wiki/2003_Red_Square_bombing"}]}, {"year": "2006", "text": "Space Shuttle program: Space Shuttle Discovery is launched on STS-116 carrying the P5 truss segment of the International Space Station.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-116\" title=\"STS-116\">STS-116</a> carrying the <a href=\"https://wikipedia.org/wiki/Integrated_Truss_Structure\" title=\"Integrated Truss Structure\">P5 truss</a> segment of the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-116\" title=\"STS-116\">STS-116</a> carrying the <a href=\"https://wikipedia.org/wiki/Integrated_Truss_Structure\" title=\"Integrated Truss Structure\">P5 truss</a> segment of the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-116", "link": "https://wikipedia.org/wiki/STS-116"}, {"title": "Integrated Truss Structure", "link": "https://wikipedia.org/wiki/Integrated_Truss_Structure"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2008", "text": "Governor of Illinois <PERSON> is arrested by federal officials for crimes including attempting to sell the U.S. Senate seat being vacated by President-elect <PERSON>.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested by federal officials for crimes including attempting to sell the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senate</a> seat being vacated by <a href=\"https://wikipedia.org/wiki/President-elect_of_the_United_States\" title=\"President-elect of the United States\">President-elect</a> <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested by federal officials for crimes including attempting to sell the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senate</a> seat being vacated by <a href=\"https://wikipedia.org/wiki/President-elect_of_the_United_States\" title=\"President-elect of the United States\">President-elect</a> <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a>.", "links": [{"title": "Governor of Illinois", "link": "https://wikipedia.org/wiki/Governor_of_Illinois"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "President-elect of the United States", "link": "https://wikipedia.org/wiki/President-elect_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}]}, {"year": "2012", "text": "A plane crash in Mexico kills seven people including singer <PERSON><PERSON>.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/2012_Mexico_Learjet_25_crash\" title=\"2012 Mexico Learjet 25 crash\">A plane crash</a> in Mexico kills seven people including singer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2012_Mexico_Learjet_25_crash\" title=\"2012 Mexico Learjet 25 crash\">A plane crash</a> in Mexico kills seven people including singer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "2012 Mexico Learjet 25 crash", "link": "https://wikipedia.org/wiki/2012_Mexico_Learjet_25_crash"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "At least seven are dead and 63 are injured following a train accident near Bintaro, Indonesia.", "html": "2013 - At least seven are dead and 63 are injured following a <a href=\"https://wikipedia.org/wiki/2013_Bintaro_train_crash\" title=\"2013 Bintaro train crash\">train accident</a> near Bintaro, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>.", "no_year_html": "At least seven are dead and 63 are injured following a <a href=\"https://wikipedia.org/wiki/2013_Bintaro_train_crash\" title=\"2013 Bintaro train crash\">train accident</a> near Bintaro, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>.", "links": [{"title": "2013 Bintaro train crash", "link": "https://wikipedia.org/wiki/2013_Bintaro_train_crash"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}]}, {"year": "2016", "text": "President <PERSON> of South Korea is impeached by the country's National Assembly in response to a major political scandal.", "html": "2016 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hye\" title=\"<PERSON>ye\"><PERSON></a> of South Korea is <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>-hye\" title=\"Impeachment of <PERSON>\">impeached</a> by the country's National Assembly in response to a major political scandal.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ye\" title=\"<PERSON>\"><PERSON></a> of South Korea is <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>-hye\" title=\"Impeachment of <PERSON>eu<PERSON>hye\">impeached</a> by the country's National Assembly in response to a major political scandal.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eu<PERSON>-hye"}, {"title": "Impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>eu<PERSON>-hye"}]}, {"year": "2016", "text": "At least 57 people are killed and a further 177 injured when two schoolgirl suicide bombers attack a market area in Madagali, Adamawa, Nigeria in the Madagali suicide bombings.", "html": "2016 - At least 57 people are killed and a further 177 injured when two schoolgirl suicide bombers attack a market area in <a href=\"https://wikipedia.org/wiki/Madagali\" title=\"Madagali\">Madagali</a>, <a href=\"https://wikipedia.org/wiki/Adamawa_State\" title=\"Adamawa State\">Adamawa</a>, Nigeria in the <a href=\"https://wikipedia.org/wiki/Madagali_suicide_bombings\" title=\"Madagali suicide bombings\">Madagali suicide bombings</a>.", "no_year_html": "At least 57 people are killed and a further 177 injured when two schoolgirl suicide bombers attack a market area in <a href=\"https://wikipedia.org/wiki/Madagali\" title=\"Madagali\">Madagali</a>, <a href=\"https://wikipedia.org/wiki/Adamawa_State\" title=\"Adamawa State\">Adamawa</a>, Nigeria in the <a href=\"https://wikipedia.org/wiki/Madagali_suicide_bombings\" title=\"Madagali suicide bombings\">Madagali suicide bombings</a>.", "links": [{"title": "Madagali", "link": "https://wikipedia.org/wiki/Madagali"}, {"title": "Adamawa State", "link": "https://wikipedia.org/wiki/Adamawa_State"}, {"title": "Madagali suicide bombings", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_suicide_bombings"}]}, {"year": "2017", "text": "The Marriage Amendment Bill receives royal assent and comes into effect, making Australia the 26th country to legalize same-sex marriage.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/Marriage_Amendment_(Definition_and_Religious_Freedoms)_Act_2017\" title=\"Marriage Amendment (Definition and Religious Freedoms) Act 2017\">Marriage Amendment Bill</a> receives royal assent and comes into effect, making <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a> the 26th country to legalize <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">same-sex marriage</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Marriage_Amendment_(Definition_and_Religious_Freedoms)_Act_2017\" title=\"Marriage Amendment (Definition and Religious Freedoms) Act 2017\">Marriage Amendment Bill</a> receives royal assent and comes into effect, making <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a> the 26th country to legalize <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">same-sex marriage</a>.", "links": [{"title": "Marriage Amendment (Definition and Religious Freedoms) Act 2017", "link": "https://wikipedia.org/wiki/Marriage_Amendment_(Definition_and_Religious_Freedoms)_Act_2017"}, {"title": "Australia", "link": "https://wikipedia.org/wiki/Australia"}, {"title": "Same-sex marriage", "link": "https://wikipedia.org/wiki/Same-sex_marriage"}]}, {"year": "2019", "text": "A volcano on Whakaari / White Island, New Zealand, kills 22 people after it erupts.", "html": "2019 - A volcano on <a href=\"https://wikipedia.org/wiki/Whakaari_/_White_Island\" title=\"Whakaari / White Island\">Whakaari / White Island</a>, <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>, kills 22 people after it <a href=\"https://wikipedia.org/wiki/2019_Whakaari_/_White_Island_eruption\" title=\"2019 Whakaari / White Island eruption\">erupts</a>.", "no_year_html": "A volcano on <a href=\"https://wikipedia.org/wiki/Whakaari_/_White_Island\" title=\"Whakaari / White Island\">Whakaari / White Island</a>, <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>, kills 22 people after it <a href=\"https://wikipedia.org/wiki/2019_Whakaari_/_White_Island_eruption\" title=\"2019 Whakaari / White Island eruption\">erupts</a>.", "links": [{"title": "Whakaari / White Island", "link": "https://wikipedia.org/wiki/Whakaari_/_White_Island"}, {"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}, {"title": "2019 Whakaari / White Island eruption", "link": "https://wikipedia.org/wiki/2019_Whakaari_/_White_Island_eruption"}]}, {"year": "2021", "text": "Fifty-five people are killed and more than 100 injured when a truck with 160 migrants from Central America overturned in Chiapas, Mexico.", "html": "2021 - Fifty-five people are killed and more than 100 injured when a truck with 160 migrants from <a href=\"https://wikipedia.org/wiki/Central_America\" title=\"Central America\">Central America</a> overturned in <a href=\"https://wikipedia.org/wiki/Chiapas\" title=\"Chiapas\">Chiapas</a>, <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>.", "no_year_html": "Fifty-five people are killed and more than 100 injured when a truck with 160 migrants from <a href=\"https://wikipedia.org/wiki/Central_America\" title=\"Central America\">Central America</a> overturned in <a href=\"https://wikipedia.org/wiki/Chiapas\" title=\"Chiapas\">Chiapas</a>, <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>.", "links": [{"title": "Central America", "link": "https://wikipedia.org/wiki/Central_America"}, {"title": "Chiapas", "link": "https://wikipedia.org/wiki/Chiapas"}, {"title": "Mexico", "link": "https://wikipedia.org/wiki/Mexico"}]}], "Births": [{"year": "1392", "text": "<PERSON>, Duke of Coimbra (d. 1449)", "html": "1392 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Coimbra\" title=\"<PERSON>, Duke of Coimbra\"><PERSON>, Duke of Coimbra</a> (d. 1449)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Coimbra\" title=\"<PERSON>, Duke of Coimbra\"><PERSON>, Duke of Coimbra</a> (d. 1449)", "links": [{"title": "<PERSON>, Duke of Coimbra", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Coimbra"}]}, {"year": "1447", "text": "<PERSON><PERSON> Emperor of China (d. 1487)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/Chenghua_Emperor\" title=\"Chenghua Emperor\">Chenghua Emperor</a> of China (d. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chenghua_Emperor\" title=\"Chenghua Emperor\">Chenghua Emperor</a> of China (d. 1487)", "links": [{"title": "Chenghua Emperor", "link": "https://wikipedia.org/wiki/Cheng<PERSON>_Emperor"}]}, {"year": "1482", "text": "<PERSON>, Elector <PERSON> (d. 1556)", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (d. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (d. 1556)", "links": [{"title": "<PERSON>, Elector <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1493", "text": "<PERSON><PERSON><PERSON>, 4th Duke of the Infantado (d. 1566)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/%C3%8D%C3%B1igo_L%C3%B3<PERSON><PERSON>_<PERSON>_<PERSON>,_4th_Duke_of_the_Infantado\" title=\"<PERSON><PERSON><PERSON>, 4th Duke of the Infantado\"><PERSON><PERSON><PERSON>, 4th Duke of the Infantado</a> (d. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%8D%C3%B1igo_L%C3%B3<PERSON><PERSON>_<PERSON>_<PERSON>,_4th_Duke_of_the_Infantado\" title=\"<PERSON><PERSON><PERSON>, 4th Duke of the Infantado\"><PERSON><PERSON><PERSON>, 4th Duke of the Infantado</a> (d. 1566)", "links": [{"title": "<PERSON><PERSON><PERSON>, 4th Duke of the Infantado", "link": "https://wikipedia.org/wiki/%C3%8D%C3%B1igo_L%C3%B3<PERSON><PERSON>_<PERSON>_<PERSON>,_4th_Duke_of_the_Infantado"}]}, {"year": "1508", "text": "<PERSON>, Dutch mathematician and cartographer (d. 1555)", "html": "1508 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>isi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and cartographer (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and cartographer (d. 1555)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>isius"}]}, {"year": "1561", "text": "<PERSON>, English lawyer and politician (d. 1629)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1629)\" class=\"mw-redirect\" title=\"<PERSON> (died 1629)\"><PERSON></a>, English lawyer and politician (d. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1629)\" class=\"mw-redirect\" title=\"<PERSON> (died 1629)\"><PERSON></a>, English lawyer and politician (d. 1629)", "links": [{"title": "<PERSON> (died 1629)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1629)"}]}, {"year": "1571", "text": "<PERSON><PERSON>, Dutch mathematician and astronomer (d. 1635)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/Metius\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and astronomer (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Met<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and astronomer (d. 1635)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Metius"}]}, {"year": "1579", "text": "<PERSON>, Peruvian saint (d. 1639)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian saint (d. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian saint (d. 1639)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1594", "text": "<PERSON><PERSON> of Sweden (d. 1632)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Sweden\"><PERSON><PERSON> of Sweden</a> (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Sweden\"><PERSON><PERSON> of Sweden</a> (d. 1632)", "links": [{"title": "<PERSON><PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_of_Sweden"}]}, {"year": "1608", "text": "<PERSON>, English poet and philosopher (d. 1674)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and philosopher (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and philosopher (d. 1674)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian singer and actor (d. 1680)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/Balda<PERSON><PERSON>_<PERSON>\" title=\"Balda<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer and actor (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balda<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lda<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer and actor (d. 1680)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ba<PERSON><PERSON><PERSON>_<PERSON>rri"}]}, {"year": "1617", "text": "<PERSON>, English poet (d. 1657)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet (d. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet (d. 1657)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1652", "text": "<PERSON>, German physician and botanist (d. 1723)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Quirinus_Rivinus\" title=\"<PERSON> Quirinus Rivinus\"><PERSON></a>, German physician and botanist (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>uirinus_Rivinus\" title=\"<PERSON> Quirinus Rivinus\"><PERSON></a>, German physician and botanist (d. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON>, English mathematician, historian, and theologian (d. 1752)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, historian, and theologian (d. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, historian, and theologian (d. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, German archaeologist and historian (d. 1768)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist and historian (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist and historian (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, English-American organist and composer (d. 1805)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English-American organist and composer (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English-American organist and composer (d. 1805)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1728", "text": "<PERSON>, Italian composer (d. 1804)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON>, Swedish Pomeranian and German pharmaceutical chemist (d. 1786)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish Pomeranian and German pharmaceutical chemist (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish Pomeranian and German pharmaceutical chemist (d. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1745", "text": "<PERSON><PERSON><PERSON>, Italian violinist and composer (d. 1818)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>na_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian violinist and composer (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Maddale<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian violinist and composer (d. 1818)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, French chemist and academic (d. 1822)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic (d. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, French general and engineer (d. 1813)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89tien<PERSON>_<PERSON>_Tousard\" title=\"<PERSON>\"><PERSON></a>, French general and engineer (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89tien<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and engineer (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%89tien<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, American politician (d. 1842)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, English architect, designed Eldon Square and Lilburn Tower (d. 1865)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Old_Eldon_Square\" title=\"Old Eldon Square\">Eldon Square</a> and <a href=\"https://wikipedia.org/wiki/Lilburn_Tower\" title=\"Lilburn Tower\">Lilburn Tower</a> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Old_Eldon_Square\" title=\"Old Eldon Square\">Eldon Square</a> and <a href=\"https://wikipedia.org/wiki/Lilburn_Tower\" title=\"Lilburn Tower\">Lilburn Tower</a> (d. 1865)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect)"}, {"title": "Old Eldon Square", "link": "https://wikipedia.org/wiki/Old_Eldon_Square"}, {"title": "Lilburn Tower", "link": "https://wikipedia.org/wiki/Lilburn_Tower"}]}, {"year": "1779", "text": "<PERSON>,  American tool maker and inventor (d. 1853)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tool maker and inventor (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tool maker and inventor (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON><PERSON><PERSON>, Canadian physician (d. 1838)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ch%C3%A9nier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian physician (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9nier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian physician (d. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9nier"}]}, {"year": "1813", "text": "<PERSON>, Irish chemist and physicist (d. 1885)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Irish chemist and physicist (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Irish chemist and physicist (d. 1885)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_(scientist)"}]}, {"year": "1837", "text": "<PERSON><PERSON>, French pianist, composer, and conductor (d. 1915)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Waldteufel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pianist, composer, and conductor (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Waldteufel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pianist, composer, and conductor (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Wald<PERSON><PERSON>el"}]}, {"year": "1842", "text": "<PERSON>, Russian zoologist, economist, geographer, and philosopher (d. 1921)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian zoologist, economist, geographer, and philosopher (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian zoologist, economist, geographer, and philosopher (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, American journalist and author (d. 1908)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American soprano and actress (d. 1891)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON><PERSON>, French psychic and occultist (d. 1929)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French psychic and occultist (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French psychic and occultist (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, Greek journalist and author (d. 1951)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek journalist and author (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek journalist and author (d. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Polish-German chemist and academic, Nobel Prize laureate (d. 1934)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1870", "text": "<PERSON>, Indian physician and missionary (d. 1960)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Ida_S._Scudder\" title=\"Ida S<PERSON> Scudder\"><PERSON> <PERSON></a>, Indian physician and missionary (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ida_S._Scudder\" title=\"Ida S. Scudder\"><PERSON> <PERSON></a>, Indian physician and missionary (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_S._<PERSON>udder"}]}, {"year": "1870", "text": "<PERSON>, Mexican lawyer and politician, president 1914 (d. 1932)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Francisco_S._Carvajal\" title=\"Francisco S. Carva<PERSON>l\">Francisco <PERSON></a>, Mexican lawyer and politician, president 1914 (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_S._Carvajal\" title=\"Francisco S. Carvajal\"><PERSON></a>, Mexican lawyer and politician, president 1914 (d. 1932)", "links": [{"title": "Francisco <PERSON>", "link": "https://wikipedia.org/wiki/Francisco_S._Carvajal"}]}, {"year": "1871", "text": "<PERSON>, American baseball player and manager (d. 1943)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Canadian philosopher, author, and academic (d. 1912)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher, author, and academic (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher, author, and academic (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American engineer (d. 1943)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auto_racing)\" title=\"<PERSON> (auto racing)\"><PERSON></a>, American engineer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auto_racing)\" title=\"<PERSON> (auto racing)\"><PERSON></a>, American engineer (d. 1943)", "links": [{"title": "<PERSON> (auto racing)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auto_racing)"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Canadian-American actor and singer (d. 1940)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actor and singer (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actor and singer (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American actor (d. 1915)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish-French composer, critic, and educator (d. 1949)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Turina\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish-French composer, critic, and educator (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Turina\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish-French composer, critic, and educator (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_Turina"}]}, {"year": "1883", "text": "<PERSON>, Russian mathematician, theorist, and academic (d. 1950)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician, theorist, and academic (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician, theorist, and academic (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Greek general and politician, 152nd Prime Minister of Greece (d. 1955)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek general and politician, 152nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek general and politician, 152nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1883", "text": "<PERSON>, German-American fitness expert, developed <PERSON><PERSON> (d. 1967)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American fitness expert, developed <a href=\"https://wikipedia.org/wiki/Pilates\" title=\"Pilates\">Pilates</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American fitness expert, developed <a href=\"https://wikipedia.org/wiki/Pilates\" title=\"Pilates\">Pilates</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pilates", "link": "https://wikipedia.org/wiki/Pilates"}]}, {"year": "1886", "text": "<PERSON>, American businessman, founded Birds Eye (d. 1956)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Birds_Eye\" title=\"Birds Eye\">Birds Eye</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Eye\" title=\"Birds Eye\">Birds Eye</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Birds Eye", "link": "https://wikipedia.org/wiki/Birds_Eye"}]}, {"year": "1887", "text": "<PERSON>, American actor (d. 1958)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor (d. 1958)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Finnish-American runner (d. 1966)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-American runner (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-American runner (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1890", "text": "<PERSON>, Canadian author (d. 1970)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Belarusian poet and critic (d. 1917)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON>%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian poet and critic (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian poet and critic (d. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C4%8D"}]}, {"year": "1892", "text": "<PERSON>, French actor (d. 1974)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Spanish activist, journalist and politician (d. 1989)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish activist, journalist and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish activist, journalist and politician (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dolores_Ib%C3%<PERSON><PERSON>uri"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Spanish soprano and actress (d. 1936)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Conchita_Superv%C3%ADa\" title=\"Conchita Supervía\"><PERSON><PERSON><PERSON></a>, Spanish soprano and actress (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conchita_Superv%C3%ADa\" title=\"Conchita Supervía\"><PERSON><PERSON><PERSON></a>, Spanish soprano and actress (d. 1936)", "links": [{"title": "Conchita Supervía", "link": "https://wikipedia.org/wiki/Conchita_Superv%C3%ADa"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, English actress and singer (d. 1987)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_Gingold\" title=\"Her<PERSON><PERSON> Gingold\"><PERSON><PERSON><PERSON></a>, English actress and singer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_G<PERSON>old\" title=\"Her<PERSON><PERSON> Gingold\"><PERSON><PERSON><PERSON></a>, English actress and singer (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>old"}]}, {"year": "1898", "text": "<PERSON>, Australian radio broadcaster and feminist and peace activist (d. 1992)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio broadcaster and feminist and peace activist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio broadcaster and feminist and peace activist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, American clown and actor (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American clown and actor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American clown and actor (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, French author and illustrator (d. 1937)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American illustrator, known for illustrating pulp magazine Weird Tales (d. 1976)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator, known for illustrating pulp magazine <i><a href=\"https://wikipedia.org/wiki/Weird_Tales\" title=\"Weird Tales\">Weird Tales</a></i> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator, known for illustrating pulp magazine <i><a href=\"https://wikipedia.org/wiki/Weird_Tales\" title=\"Weird Tales\">Weird Tales</a></i> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Weird Tales", "link": "https://wikipedia.org/wiki/Weird_Tales"}]}, {"year": "1900", "text": "<PERSON>, American activist, founded the Communist League of Struggle (d. 1977)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Communist_League_of_Struggle\" title=\"Communist League of Struggle\">Communist League of Struggle</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Communist_League_of_Struggle\" title=\"Communist League of Struggle\">Communist League of Struggle</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Communist League of Struggle", "link": "https://wikipedia.org/wiki/Communist_League_of_Struggle"}]}, {"year": "1901", "text": "<PERSON>, French pilot and politician (d. 1936)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot and politician (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot and politician (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-German author and playwright (d. 1938)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_<PERSON>_<PERSON>rv%C3%A1th\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-German author and playwright (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_von_<PERSON>rv%C3%A1th\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-German author and playwright (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96d%C3%B6n_von_Horv%C3%A1th"}]}, {"year": "1902", "text": "<PERSON>, American schoolteacher, actress and voice artist (d. 1985)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American schoolteacher, actress and voice artist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American schoolteacher, actress and voice artist (d. 1985)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1904", "text": "<PERSON>, American actor and singer (d. 1988)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (d. 1988)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1905", "text": "<PERSON>, American author, screenwriter, and blacklistee (d. 1976)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Trumbo\" title=\"Dalton Trumbo\"><PERSON></a>, American author, screenwriter, and <a href=\"https://wikipedia.org/wiki/Hollywood_blacklist\" title=\"Hollywood blacklist\">blacklistee</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Trumbo\" title=\"Dalton Trumbo\"><PERSON></a>, American author, screenwriter, and <a href=\"https://wikipedia.org/wiki/Hollywood_blacklist\" title=\"Hollywood blacklist\">blacklistee</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dalton_Trumbo"}, {"title": "Hollywood blacklist", "link": "https://wikipedia.org/wiki/Hollywood_blacklist"}]}, {"year": "1906", "text": "<PERSON>, American admiral and computer scientist, designed CO<PERSON><PERSON> (d. 1992)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and computer scientist, designed <a href=\"https://wikipedia.org/wiki/COBOL\" title=\"COBOL\">COBOL</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and computer scientist, designed <a href=\"https://wikipedia.org/wiki/COBOL\" title=\"COBOL\">COBOL</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "COBOL", "link": "https://wikipedia.org/wiki/COBOL"}]}, {"year": "1906", "text": "<PERSON>, American bandleader and tenor saxophonist (d. 1983)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and tenor saxophonist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and tenor saxophonist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>., American captain, actor, and producer (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"Douglas Fairbanks Jr.\"><PERSON> Jr.</a>, American captain, actor, and producer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American captain, actor, and producer (d. 2000)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1910", "text": "<PERSON><PERSON>, first Prime Minister of Antigua and Barbuda (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>ere_Bird\" title=\"Vere Bird\"><PERSON><PERSON> Bird</a>, first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Antigua_and_Barbuda\" title=\"Prime Minister of Antigua and Barbuda\">Prime Minister of Antigua and Barbuda</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ere_Bird\" title=\"Vere Bird\"><PERSON><PERSON> <PERSON></a>, first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Antigua_and_Barbuda\" title=\"Prime Minister of Antigua and Barbuda\">Prime Minister of Antigua and Barbuda</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bird"}, {"title": "Prime Minister of Antigua and Barbuda", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Antigua_and_Barbuda"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, American actor (d. 1986)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese colonel and businessman (d. 2007)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Ry%C5%ABz%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese colonel and businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%ABz%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese colonel and businessman (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%ABz%C5%8D_Sejima"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American lawyer and politician, 55th Speaker of the United States House of Representatives (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Tip_O%27Neill\" title=\"Tip <PERSON>\"><PERSON><PERSON> <PERSON></a>, American lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tip_O%27Neill\" title=\"T<PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tip_O%27Neill"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1912", "text": "<PERSON>, American golfer (d. 1971)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Norwegian lieutenant (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian lieutenant (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Man<PERSON>\"><PERSON></a>, Norwegian lieutenant (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actress (d. 2010)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian painter and illustrator (d. 2009)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Ljubica_Soki%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian painter and illustrator (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ljubica_Soki%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian painter and illustrator (d. 2009)", "links": [{"title": "Ljubic<PERSON>", "link": "https://wikipedia.org/wiki/Ljubica_Soki%C4%87"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American author (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, German-Austrian soprano and actress (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian soprano and actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian soprano and actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American soldier, journalist, and author (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American soldier, journalist, and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American soldier, journalist, and author (d. 2002)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1916", "text": "<PERSON>, American actor, singer, and producer (d. 2020)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian cricketer (d. 1986)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American CIA agent (d. 1987)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> agent (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> agent (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}]}, {"year": "1917", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1986)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Indian singer-songwriter (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/V._<PERSON><PERSON><PERSON>amoorthy\" title=\"<PERSON><PERSON> Daks<PERSON>amoorthy\"><PERSON><PERSON> <PERSON></a>, Indian singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._<PERSON><PERSON><PERSON>amoorthy\" title=\"<PERSON><PERSON> Dakshinamoorthy\"><PERSON><PERSON></a>, Indian singer-songwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON><PERSON><PERSON>orthy"}]}, {"year": "1919", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1920", "text": "<PERSON>, Italian economist and politician, 10th President of Italy (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "1920", "text": "<PERSON>, Italian motorcycle racer and race car driver (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer and race car driver (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer and race car driver (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American actor (d. 1991)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Foxx\" title=\"Redd Foxx\"><PERSON><PERSON></a>, American actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Foxx\" title=\"Redd Foxx\"><PERSON><PERSON></a>, American actor (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Redd_Foxx"}]}, {"year": "1925", "text": "<PERSON>, American basketball player and coach (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 2013)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1926", "text": "<PERSON>, American physicist, photographer, and mountaineer, Nobel Prize laureate (d. 1999)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kendall\"><PERSON></a>, American physicist, photographer, and mountaineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, photographer, and mountaineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1926", "text": "<PERSON>, Czech-English psychologist and author (d. 1995)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Jan_K%C5%99esadlo\" title=\"<PERSON>\"><PERSON></a>, Czech-English psychologist and author (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C5%99esadlo\" title=\"<PERSON>\"><PERSON></a>, Czech-English psychologist and author (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_K%C5%99esadlo"}]}, {"year": "1926", "text": "<PERSON>, British journalist (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, British journalist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, British journalist (d. 2001)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "1926", "text": "<PERSON>, American sprinter and coach (d. 1972)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, French composer (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American author and educator (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Belgian race car driver", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Milhoux\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Milhoux\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actor, director, and screenwriter (d. 1989)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian union leader and politician, 23rd Prime Minister of Australia (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian union leader and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian union leader and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1930", "text": "<PERSON>, American actor, director, and screenwriter (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Henry\"><PERSON></a>, American actor, director, and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Henry\"><PERSON></a>, American actor, director, and screenwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Guatemalan soldier and politician, 27th President of Guatemala (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_Humberto_Mej%C3%ADa_V%C3%ADctores\" title=\"Óscar Hu<PERSON>to Mejía Víctor<PERSON>\"><PERSON><PERSON><PERSON></a>, Guatemalan soldier and politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_Humberto_Mej%C3%ADa_V%C3%ADctores\" title=\"Óscar Hu<PERSON>to Mejía Víctor<PERSON>\"><PERSON><PERSON><PERSON></a>, Guatemalan soldier and politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_Humberto_Mej%C3%ADa_V%C3%ADctores"}, {"title": "President of Guatemala", "link": "https://wikipedia.org/wiki/President_of_Guatemala"}]}, {"year": "1931", "text": "<PERSON>, American basketball player-coach", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player-coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player-coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2022)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Czech actor, director, and screenwriter (d. 2010)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech actor, director, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech actor, director, and screenwriter (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American trumpet player and academic (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American jockey (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter, guitarist, and playwright (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and playwright (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and playwright (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, English-American author and illustrator", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ashleigh_Brilliant\" title=\"Ashleigh Brilliant\"><PERSON><PERSON> Brilliant</a>, English-American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashleigh_Brilliant\" title=\"Ashleigh Brilliant\"><PERSON><PERSON> Brilliant</a>, English-American author and illustrator", "links": [{"title": "Ashleigh Brilliant", "link": "https://wikipedia.org/wiki/Ash<PERSON>_Brilliant"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American decathlete and football player (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American decathlete and football player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American decathlete and football player (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American actor and talk show host (d. 2001)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor and talk show host (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor and talk show host (d. 2001)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1933", "text": "<PERSON><PERSON>, American golfer (d. 2008)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Moody\"><PERSON><PERSON></a>, American golfer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Moody\"><PERSON><PERSON></a>, American golfer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, English actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English composer and teacher (d. 1996)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and teacher (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and teacher (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American blues singer-songwriter and harmonica player (d. 1998)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wells\"><PERSON></a>, American blues singer-songwriter and harmonica player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Wells\" title=\"<PERSON> Wells\"><PERSON></a>, American blues singer-songwriter and harmonica player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1993)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1993)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1938", "text": "<PERSON>, American football player, sportscaster, and actor (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Greek epidemiologist, oncologist, and academic (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek epidemiologist, oncologist, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek epidemiologist, oncologist, and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Jamaican singer-songwriter and producer (d. 2005)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and producer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and author (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actor, director, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bridges\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2016)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1942", "text": "<PERSON>, Scottish footballer and manager (d. 1997)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American football player, sportscaster, and actor (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian ice hockey player (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian rugby league player (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 2021)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1942", "text": "<PERSON>, American journalist and author (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American conservationist (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conservationist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conservationist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian ice hockey player (d. 2008)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Martin\" title=\"Pit Martin\"><PERSON></a>, Canadian ice hockey player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pit_Martin\" title=\"Pit Martin\"><PERSON></a>, Canadian ice hockey player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Martin"}]}, {"year": "1943", "text": "<PERSON>, English author, playwright, and director", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ope\"><PERSON></a>, English author, playwright, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Trollope\"><PERSON></a>, English author, playwright, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Trollope"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and music producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and music producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and music producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American author, playwright, and producer (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American businessman and politician, 57th Mayor of Pittsburgh (d. 2006)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(mayor)\" title=\"<PERSON> (mayor)\"><PERSON></a>, American businessman and politician, 57th <a href=\"https://wikipedia.org/wiki/Mayor_of_Pittsburgh\" class=\"mw-redirect\" title=\"Mayor of Pittsburgh\">Mayor of Pittsburgh</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27<PERSON><PERSON><PERSON>_(mayor)\" title=\"<PERSON> (mayor)\"><PERSON></a>, American businessman and politician, 57th <a href=\"https://wikipedia.org/wiki/Mayor_of_Pittsburgh\" class=\"mw-redirect\" title=\"Mayor of Pittsburgh\">Mayor of Pittsburgh</a> (d. 2006)", "links": [{"title": "<PERSON> (mayor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(mayor)"}, {"title": "Mayor of Pittsburgh", "link": "https://wikipedia.org/wiki/Mayor_of_Pittsburgh"}]}, {"year": "1945", "text": "<PERSON>, American actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, <PERSON> of Marylebone, English economist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Marylebone\" title=\"<PERSON>, <PERSON> of Marylebone\"><PERSON>, Baron <PERSON> of Marylebone</a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Marylebone\" title=\"<PERSON>, Baron <PERSON> of Marylebone\"><PERSON>, Baron <PERSON> of Marylebone</a>, English economist and academic", "links": [{"title": "<PERSON>, Baron <PERSON> of Marylebone", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Mary<PERSON>bone"}]}, {"year": "1946", "text": "<PERSON>, American bass player and songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Italian-Indian politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Indian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English bishop", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American soldier, academic, and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, academic, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Estonian politician, 24th Estonian Minister of Defense", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Jaak_J%C3%B5er%C3%BC%C3%BCt\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician, 24th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defense</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaa<PERSON>_J%C3%B5er%C3%BC%C3%BCt\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician, 24th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defense</a>", "links": [{"title": "Jaak <PERSON>", "link": "https://wikipedia.org/wiki/Jaak_J%C3%B5er%C3%BC%C3%BCt"}, {"title": "Minister of Defence (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)"}]}, {"year": "1947", "text": "<PERSON>, English cricketer and umpire", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Dutch director and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch director and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English historian, author, and judge", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mpt<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American golfer and architect", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kit<PERSON>\"><PERSON></a>, American golfer and architect", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Kit<PERSON>-English singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON>tian-English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kittian-English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Pakistani politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Liaqat_Baloch\" title=\"Liaqat Baloch\"><PERSON><PERSON><PERSON></a>, Pakistani politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liaqat_Baloch\" title=\"Liaqat Baloch\"><PERSON><PERSON><PERSON></a>, Pakistani politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Liaqa<PERSON>_<PERSON>h"}]}, {"year": "1952", "text": "<PERSON>, American actor and voice artist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Dutch composer and educator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Dutch composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch composer and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>t"}]}, {"year": "1953", "text": "<PERSON> <PERSON><PERSON>, American basketball player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/World_B._Free\" title=\"World B. Free\">World B. Free</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_B._Free\" title=\"World B. Free\">World B. Free</a>, American basketball player", "links": [{"title": "World B. Free", "link": "https://wikipedia.org/wiki/World_B._Free"}]}, {"year": "1953", "text": "<PERSON>, American actor and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Luxembourger lawyer and politician, Prime Minister of Luxembourg", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Luxembourger lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Luxembourger lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Luxembourg", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Dutch footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Henk_ten_<PERSON>\" title=\"Henk ten <PERSON>e\"><PERSON><PERSON> <PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON>_ten_<PERSON>\" title=\"Henk ten <PERSON>\"><PERSON><PERSON> <PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>nk ten <PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>_<PERSON>e"}]}, {"year": "1955", "text": "<PERSON>, American basketball player and radio host", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Thai singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON>_<PERSON>ataporn\" title=\"<PERSON><PERSON><PERSON>porn\"><PERSON><PERSON><PERSON></a>, Thai singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON>_<PERSON>ataporn\" title=\"<PERSON><PERSON><PERSON>ataporn\"><PERSON><PERSON><PERSON></a>, Thai singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chamras_<PERSON>ewataporn"}]}, {"year": "1956", "text": "<PERSON>, American country singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, French journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian guitarist and composer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Mara\" title=\"<PERSON>\"><PERSON></a>, Australian guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Mara\" title=\"<PERSON>\"><PERSON></a>, Australian guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Mara"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American singer-songwriter, dancer, and actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, dancer, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English soprano", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American comedian, actor, and writer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, American visual effects designer and director", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Stefen_Fangmeier\" title=\"Stef<PERSON> Fangmeier\"><PERSON><PERSON><PERSON></a>, American visual effects designer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stefen_Fangmeier\" title=\"Stef<PERSON> Fangmeier\"><PERSON><PERSON><PERSON></a>, American visual effects designer and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>efen_Fangmeier"}]}, {"year": "1960", "text": "<PERSON>, English activist and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Croatian politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Dominican baseball player and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ff<PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fe<PERSON>ff<PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "Fe<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Santa Clara Pueblo (Native American) ceramic sculptor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Santa_Clara_Pueblo\" class=\"mw-redirect\" title=\"Santa Clara Pueblo\">Santa Clara Pueblo</a> (Native American) ceramic sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Santa_Clara_Pueblo\" class=\"mw-redirect\" title=\"Santa Clara Pueblo\">Santa Clara Pueblo</a> (Native American) ceramic sculptor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Santa Clara Pueblo", "link": "https://wikipedia.org/wiki/Santa_Clara_Pueblo"}]}, {"year": "1963", "text": "<PERSON>, Canadian boxer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, Canadian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, Canadian boxer", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1963", "text": "Empress <PERSON><PERSON><PERSON>, Japanese consort of Emperor <PERSON><PERSON><PERSON><PERSON>", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON><PERSON>\" title=\"Empress <PERSON><PERSON><PERSON>\">Empress <PERSON><PERSON><PERSON></a>, Japanese consort of <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>o\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON><PERSON>\" title=\"Empress <PERSON><PERSON><PERSON>\">Empress <PERSON><PERSON><PERSON></a>, Japanese consort of <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a>", "links": [{"title": "Empress <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naru<PERSON>o"}]}, {"year": "1964", "text": "<PERSON>, American drummer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German actor and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Ha<PERSON>_Kerkeling\" title=\"Hape Kerkeling\"><PERSON><PERSON></a>, German actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ha<PERSON>_Kerkeling\" title=\"Hape Kerkeling\"><PERSON><PERSON></a>, German actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eling"}]}, {"year": "1964", "text": "<PERSON>, German journalist and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Kiss\" title=\"Les Kiss\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Les_Kiss\" title=\"Les Kiss\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American lawyer and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English snooker player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Israeli lawyer and politician, 24th Israeli Minister of Internal Affairs", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27ar\" title=\"<PERSON>\"><PERSON></a>, Israeli lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Internal_Affairs_Minister_of_Israel\" class=\"mw-redirect\" title=\"Internal Affairs Minister of Israel\">Israeli Minister of Internal Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27ar\" title=\"<PERSON>\"><PERSON></a>, Israeli lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Internal_Affairs_Minister_of_Israel\" class=\"mw-redirect\" title=\"Internal Affairs Minister of Israel\">Israeli Minister of Internal Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gideon_<PERSON>%27ar"}, {"title": "Internal Affairs Minister of Israel", "link": "https://wikipedia.org/wiki/Internal_Affairs_Minister_of_Israel"}]}, {"year": "1966", "text": "<PERSON>, English footballer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1966)\" title=\"<PERSON> (footballer, born 1966)\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1966)\" title=\"<PERSON> (footballer, born 1966)\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON> (footballer, born 1966)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1966)"}]}, {"year": "1967", "text": "<PERSON>, American violinist and conductor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American freestyle and professional wrestler", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American freestyle and professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American freestyle and professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Price"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dylan\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Dylan\" title=\"<PERSON> Dylan\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Jamaican-Canadian singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Saskia_Garel\" title=\"Saskia Garel\"><PERSON><PERSON><PERSON></a>, Jamaican-Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saskia_Garel\" title=\"Saskia Garel\"><PERSON><PERSON><PERSON></a>, Jamaican-Canadian singer-songwriter", "links": [{"title": "Saskia Garel", "link": "https://wikipedia.org/wiki/Saskia_Garel"}]}, {"year": "1969", "text": "<PERSON>, American businesswoman and television personality", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Belgian politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Bixente_Lizaraz<PERSON>\" title=\"Bixente <PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bixente_Lizarazu\" title=\"Bixente <PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bixente_<PERSON>u"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, French mathematician and academic", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_<PERSON>er\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rapha%C3%AB<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rapha%C3%<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> DioGuardi\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> DioGuardi\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English drummer, DJ, composer, and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, DJ, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, DJ, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American pole vaulter and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Czech-Canadian ice hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Petr_Nedv%C4%9Bd\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petr_Nedv%C4%9Bd\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petr_Nedv%C4%9Bd"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, German-American drummer and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Tr%C3%A9_Cool\" title=\"Tré Cool\"><PERSON><PERSON></a>, German-American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tr%C3%A9_Cool\" title=\"Tré Cool\"><PERSON><PERSON></a>, German-American drummer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%C3%A9_Cool"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Tahitian-French tennis player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tahitian-French tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tahitian-French tennis player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Bangladeshi psychologist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wazed\" title=\"Saima Wazed\"><PERSON><PERSON> Wazed</a>, Bangladeshi psychologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wazed\" title=\"Sai<PERSON> Wazed\"><PERSON><PERSON> Wazed</a>, Bangladeshi psychologist", "links": [{"title": "Saima Wazed", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wazed"}]}, {"year": "1973", "text": "<PERSON>, American politician and activist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Fabio_Artico\" title=\"Fabio Artico\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabio_Artico\" title=\"Fabio Artico\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "Fabio Artico", "link": "https://wikipedia.org/wiki/Fabio_Artico"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Burundian runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/V%C3%A9nuste_Niyongabo\" title=\"Vénuste Niyongabo\"><PERSON><PERSON><PERSON><PERSON></a>, Burundian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A9nuste_Niyongabo\" title=\"Vénuste Niyongabo\"><PERSON><PERSON><PERSON><PERSON></a>, Burundian runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A9nuste_Niyongabo"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican-American soprano", "html": "1973 - <a href=\"https://wikipedia.org/wiki/B%C3%A1rbara_Padilla\" title=\"Bárbara Padilla\"><PERSON><PERSON><PERSON><PERSON> Padi<PERSON></a>, Mexican-American soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A1rbara_Padilla\" title=\"Bárbara Padilla\"><PERSON><PERSON><PERSON><PERSON> Padilla</a>, Mexican-American soprano", "links": [{"title": "Bárbara Padilla", "link": "https://wikipedia.org/wiki/B%C3%A1rbara_Padilla"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Jamaican-American rapper", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Canibus\" title=\"Canib<PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican-American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canibus\" title=\"Canib<PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican-American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Canibus"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Brazilian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Alo%C3%AD<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alo%C3%AD<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Alo%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American soccer player, coach, and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Scottish curler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American pediatrician, professor, and public health advocate", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pediatrician, professor, and public health advocate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pediatrician, professor, and public health advocate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter and keyboard player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Imogen_Heap\" title=\"Imogen Heap\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imogen_Heap\" title=\"Imogen Heap\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and keyboard player", "links": [{"title": "Imogen Heap", "link": "https://wikipedia.org/wiki/Imogen_Heap"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Argentinian tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Gast%C3%B3n_Gaudio\" title=\"Gastón Gaudio\"><PERSON><PERSON><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gast%C3%B3n_Gaudio\" title=\"Gastón Gaudio\"><PERSON><PERSON><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gast%C3%B3n_Gaudio"}]}, {"year": "1978", "text": "<PERSON>, American actor and musician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Japanese-American singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Irish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Japanese skier", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor, comedian, and musician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mardy Fish\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mardy Fish\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "Mardy <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Russian cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Belgian runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (running back)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)"}]}, {"year": "1982", "text": "<PERSON>, American ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, German sprinter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Bastian_Swillims\" title=\"Bastian Swillims\">Bast<PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bastian_Swillims\" title=\"Bastian Swillims\"><PERSON><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>will<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, English-Jamaican footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Jamaican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Jamaican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish volleyball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/N<PERSON>li<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Neslihan <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Neslihan <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish volleyball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Polish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish-Filipino footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_G<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-Filipino footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-Filipino footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leon Hall\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Leon Hall\"><PERSON></a>, American football player", "links": [{"title": "Leon Hall", "link": "https://wikipedia.org/wiki/Leon_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Dutch golfer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Wil_<PERSON>ing\" title=\"Wil Besseling\"><PERSON><PERSON></a>, Dutch golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil_<PERSON>\" title=\"Wil Besseling\"><PERSON><PERSON></a>, Dutch golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil_Be<PERSON>ing"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Australian basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nes"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Japanese-American chess player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Ghanaian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>wad<PERSON>_<PERSON>amoah\" title=\"Kwad<PERSON> Asamoah\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>wad<PERSON>_<PERSON>\" title=\"Kwadwo Asamoah\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kwad<PERSON>_<PERSON>ah"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Australian actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Ash<PERSON>_Brewer\" title=\"Ashleigh Brewer\"><PERSON><PERSON> Brewer</a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ash<PERSON>_Brewer\" title=\"Ash<PERSON> Brewer\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "Ashleigh Brewer", "link": "https://wikipedia.org/wiki/Ash<PERSON>_Brewer"}]}, {"year": "1990", "text": "<PERSON>, Dutch cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Langston_Galloway\" title=\"Langston Galloway\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Langston_Galloway\" title=\"Langston Galloway\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "Langston Galloway", "link": "https://wikipedia.org/wiki/Langston_Galloway"}]}, {"year": "1991", "text": "<PERSON>, South Korean singer and actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, German politician", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Cem_Ince\" title=\"Cem Ince\"><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cem_Ince\" title=\"Cem Ince\"><PERSON><PERSON></a>, German politician", "links": [{"title": "Cem Ince", "link": "https://wikipedia.org/wiki/Cem_Ince"}]}, {"year": "1993", "text": "<PERSON>,  Canadian snowboarder", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Dutch cyclist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lders"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Italian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American gymnast", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1996", "text": "<PERSON>, Canadian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, American gymnast", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American gymnast", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Latvian figure skater", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Di%C4%81na_%C5%85ikitina\" title=\"<PERSON><PERSON><PERSON> Ņikitina\"><PERSON><PERSON><PERSON> Ņikitina</a>, Latvian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Di%C4%81na_%C5%85ikitina\" title=\"<PERSON><PERSON><PERSON> Ņikitina\"><PERSON><PERSON><PERSON> Ņikitina</a>, Latvian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON> Ņikitina", "link": "https://wikipedia.org/wiki/Di%C4%81na_%C5%85ikitina"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Japanese singer", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}], "Deaths": [{"year": "638", "text": "<PERSON><PERSON><PERSON> of Constantinople", "html": "638 - <a href=\"https://wikipedia.org/wiki/Sergi<PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON> of Constantinople</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ser<PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON> of Constantinople</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Constantinople"}]}, {"year": "730", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Arab general", "html": "730 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>, Arab general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>, Arab general", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "748", "text": "<PERSON><PERSON><PERSON>, Umayyad general and politician (b. 663)", "html": "748 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn Say<PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, Umayyad general and politician (b. 663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn Sayyar\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, Umayyad general and politician (b. 663)", "links": [{"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "933", "text": "<PERSON>, prince of Later Tang", "html": "933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Congrong\"><PERSON></a>, prince of <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Congrong\"><PERSON></a>, prince of <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ng"}, {"title": "Later Tang", "link": "https://wikipedia.org/wiki/Later_Tang"}]}, {"year": "1117", "text": "<PERSON> of Brunswick, <PERSON><PERSON><PERSON><PERSON><PERSON> of Meißen", "html": "1117 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brunswick\" title=\"<PERSON> of Brunswick\"><PERSON> of Brunswick</a>, <PERSON><PERSON><PERSON><PERSON><PERSON> of <a href=\"https://wikipedia.org/wiki/Meissen\" title=\"Meissen\">Meißen</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gertrude_<PERSON>_Brunswick\" title=\"Gertrude of Brunswick\"><PERSON> of Brunswick</a>, <PERSON><PERSON><PERSON><PERSON><PERSON> of <a href=\"https://wikipedia.org/wiki/Meissen\" title=\"Meissen\">Meißen</a>", "links": [{"title": "Gertrude of Brunswick", "link": "https://wikipedia.org/wiki/Gertrude_of_Brunswick"}, {"title": "Meissen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1165", "text": "<PERSON> of Scotland (b. 1141)", "html": "1165 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> IV of Scotland\"><PERSON> of Scotland</a> (b. 1141)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> IV of Scotland\"><PERSON> of Scotland</a> (b. 1141)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1242", "text": "<PERSON>, Lord Keeper of England and Abbot of Evesham", "html": "1242 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lord Keeper of England and Abbot of Evesham", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lord Keeper of England and Abbot of Evesham", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1268", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Prince of Black Ruthenia, Grand Duke of Lithuania", "html": "1268 - <a href=\"https://wikipedia.org/wiki/Vai%C5%A1vilkas\" title=\"Vaišvilkas\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Prince of Black Ruthenia, <a href=\"https://wikipedia.org/wiki/Grand_Duke_of_Lithuania\" class=\"mw-redirect\" title=\"Grand Duke of Lithuania\">Grand Duke of Lithuania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vai%C5%A1vilkas\" title=\"Vaišvilkas\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Prince of Black Ruthenia, <a href=\"https://wikipedia.org/wiki/Grand_Duke_of_Lithuania\" class=\"mw-redirect\" title=\"Grand Duke of Lithuania\">Grand Duke of Lithuania</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vai%C5%A1vilkas"}, {"title": "Grand Duke of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duke_of_Lithuania"}]}, {"year": "1299", "text": "<PERSON><PERSON><PERSON>, Archbishop of Trier", "html": "1299 - <a href=\"https://wikipedia.org/wiki/Bohemond_<PERSON>_(archbishop_of_Trier)\" title=\"<PERSON><PERSON><PERSON> <PERSON> (archbishop of Trier)\"><PERSON><PERSON><PERSON> <PERSON>, Archbishop of Trier</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bohemond_<PERSON>_(archbishop_of_Trier)\" title=\"<PERSON><PERSON><PERSON> <PERSON> (archbishop of Trier)\"><PERSON><PERSON><PERSON> <PERSON>, Archbishop of Trier</a>", "links": [{"title": "<PERSON><PERSON><PERSON> (archbishop of Trier)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(archbishop_of_Trier)"}]}, {"year": "1437", "text": "<PERSON><PERSON><PERSON>, Holy Roman Emperor (b. 1368)", "html": "1437 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON><PERSON><PERSON>, Holy Roman Emperor\"><PERSON><PERSON><PERSON>, Holy Roman Emperor</a> (b. 1368)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON><PERSON><PERSON>, Holy Roman Emperor\"><PERSON><PERSON><PERSON>, Holy Roman Emperor</a> (b. 1368)", "links": [{"title": "<PERSON><PERSON><PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1544", "text": "<PERSON><PERSON><PERSON>, Italian poet (b. 1491)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet (b. 1491)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet (b. 1491)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON> (b. 1499)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_IV\" title=\"Pope Pius IV\">Pope <PERSON> IV</a> (b. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"Pope Pius IV\"><PERSON> <PERSON> IV</a> (b. 1499)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON>, English priest (b. 1559)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English priest (b. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English priest (b. 1559)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_(priest)"}]}, {"year": "1625", "text": "<PERSON><PERSON><PERSON>, Dutch historian and geographer (b. 1547)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>bb<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch historian and geographer (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch historian and geographer (b. 1547)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ubb<PERSON>_<PERSON>us"}]}, {"year": "1636", "text": "<PERSON>, Polish preacher and author (b. 1566)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish preacher and author (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish preacher and author (b. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1641", "text": "<PERSON>, Belgian-English painter and illustrator (b. 1599)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-English painter and illustrator (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-English painter and illustrator (b. 1599)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1669", "text": "<PERSON> (b. 1600)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_IX\" title=\"Pope Clement IX\"><PERSON></a> (b. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_<PERSON>\" title=\"<PERSON> Clement <PERSON>\"><PERSON></a> (b. 1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1674", "text": "<PERSON>, 1st Earl of Clarendon, English historian and politician, Chancellor of the Exchequer (b. 1609)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Clarendon\" title=\"<PERSON>, 1st Earl of Clarendon\"><PERSON>, 1st Earl of Clarendon</a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Clarendon\" title=\"<PERSON>, 1st Earl of Clarendon\"><PERSON>, 1st Earl of Clarendon</a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1609)", "links": [{"title": "<PERSON>, 1st Earl of Clarendon", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Clarendon"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1706", "text": "<PERSON> of Portugal (b. 1648)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> II of Portugal\"><PERSON> of Portugal</a> (b. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> II of Portugal\"><PERSON> of Portugal</a> (b. 1648)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1718", "text": "<PERSON>, Italian monk and cartographer (b. 1650)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian monk and cartographer (b. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian monk and cartographer (b. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1761", "text": "<PERSON><PERSON><PERSON>, Queen of Chatrapati Rajaram (b. 1675)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/Tarabai\" title=\"Tarabai\"><PERSON><PERSON><PERSON></a>, Queen of Chatrapati Rajaram (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarabai\" title=\"Tarabai\"><PERSON><PERSON><PERSON></a>, Queen of Chatrapati Rajaram (b. 1675)", "links": [{"title": "Tarabai", "link": "https://wikipedia.org/wiki/Tarabai"}]}, {"year": "1793", "text": "<PERSON><PERSON><PERSON>, French-Austrian educator (b. 1749)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Austrian educator (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Polastron\"><PERSON><PERSON><PERSON></a>, French-Austrian educator (b. 1749)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ron"}]}, {"year": "1798", "text": "<PERSON>, German pastor, botanist, and ornithologist (b. 1729)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor, botanist, and ornithologist (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor, botanist, and ornithologist (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Danish surgeon, botanist, and academic (b. 1757)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish surgeon, botanist, and academic (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish surgeon, botanist, and academic (b. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, English army officer (b. 1768)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English army officer (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English army officer (b. 1768)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Portuguese journalist and author (b. 1799)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Almei<PERSON>_Garrett\" title=\"Almei<PERSON> Garrett\"><PERSON><PERSON><PERSON></a>, Portuguese journalist and author (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Almei<PERSON>_Garrett\" title=\"Almei<PERSON> Garrett\"><PERSON><PERSON><PERSON></a>, Portuguese journalist and author (b. 1799)", "links": [{"title": "Almeida Garrett", "link": "https://wikipedia.org/wiki/Almeida_Garrett"}]}, {"year": "1858", "text": "<PERSON>, Canadian lawyer and politician, 3rd Premier of Canada West (b. 1804)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"List of Joint Premiers of the Province of Canada\">Premier of Canada West</a> (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"List of Joint Premiers of the Province of Canada\">Premier of Canada West</a> (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Joint Premiers of the Province of Canada", "link": "https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, Senegalese religious leader", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Senegalese religious leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Senegalese religious leader", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON>, French author and critic (b. 1849)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> Brun<PERSON>\"><PERSON></a>, French author and critic (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_Bruneti%C3%A8re"}]}, {"year": "1907", "text": "<PERSON>, Norwegian mezzo-soprano singer and pioneer on women's skiing (b. 1858)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian mezzo-soprano singer and pioneer on women<span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> skiing (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian mezzo-soprano singer and pioneer on women<span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> skiing (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Japanese author and poet (b. 1867)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Natsume_S%C5%8Dseki\" title=\"Natsume Sōseki\"><PERSON><PERSON></a>, Japanese author and poet (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Natsume_S%C5%8Dseki\" title=\"Natsume Sōseki\"><PERSON><PERSON></a>, Japanese author and poet (b. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Natsume_S%C5%8Dseki"}]}, {"year": "1924", "text": "<PERSON>, Dutch composer and educator (b. 1854)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and educator (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and educator (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1879)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, German photographer, sculptor, and educator (b. 1865)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer, sculptor, and educator (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer, sculptor, and educator (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Bangladeshi social worker and author (b. 1880)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rokeya\" title=\"Begum Rokeya\"><PERSON><PERSON></a>, Bangladeshi social worker and author (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rokeya\" title=\"Begum Rokeya\"><PERSON><PERSON></a>, Bangladeshi social worker and author (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Be<PERSON>_<PERSON>okeya"}]}, {"year": "1935", "text": "<PERSON>, American journalist and activist (b. 1886)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, English phonetician (b. 1882)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English phonetician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English phonetician (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Swedish physicist and engineer, Nobel Prize laureate (b. 1869)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gustaf_Dal%C3%A9n"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1941", "text": "<PERSON>, Russian author, poet, and philosopher (b. 1865)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, poet, and philosopher (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, poet, and philosopher (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French painter (b. 1870)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9noy\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9noy\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9noy"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American actor (b. 1913)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gar"}]}, {"year": "1945", "text": "<PERSON>, South Korean activist and politician (b. 1864)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean activist and politician (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean activist and politician (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Turkish general (b. 1882)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%B0hsan_S%C3%A2bis\" title=\"<PERSON>\"><PERSON></a>, Turkish general (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ali_%C4%B0hsan_S%C3%A2bis\" title=\"<PERSON>\"><PERSON></a>, Turkish general (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ali_%C4%B0hsan_S%C3%A2bis"}]}, {"year": "1963", "text": "<PERSON>, Nigerian author and educator (b. 1903)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian author and educator (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian author and educator (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American historian, author, and academic (b. 1905)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English poet and critic (b. 1887)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and manager (b. 1884)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Luxembourgish lawyer and judge, 3rd President of the European Court of Justice (b. 1898)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Luxembourgish lawyer and judge, 3rd <a href=\"https://wikipedia.org/wiki/List_of_members_of_the_European_Court_of_Justice\" title=\"List of members of the European Court of Justice\">President of the European Court of Justice</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Luxembourgish lawyer and judge, 3rd <a href=\"https://wikipedia.org/wiki/List_of_members_of_the_European_Court_of_Justice\" title=\"List of members of the European Court of Justice\">President of the European Court of Justice</a> (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_L%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "List of members of the European Court of Justice", "link": "https://wikipedia.org/wiki/List_of_members_of_the_European_Court_of_Justice"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American mob boss (b. 1883)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American mob boss (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American mob boss (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Armenian-Russian engineer and businessman, co-founded the Mikoyan-Gurevich Design Bureau (b. 1905)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian-Russian engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> Design Bureau (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian-Russian engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> Design Bureau (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Pakistani politician, 7th Prime Minister of Pakistan (b. 1893)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "1971", "text": "<PERSON>, American political scientist, academic, and diplomat, Nobel Prize laureate (b. 1904)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, academic, and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, academic, and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1971", "text": "<PERSON>, Russian sculptor and painter (b. 1874)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sculptor and painter (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sculptor and painter (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "Rev. <PERSON><PERSON><PERSON>, Church of Scotland Minister, Missionary in India and China, writer and poet (b. 1886)", "html": "1971 - Rev. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Church_of_Scotland\" title=\"Church of Scotland\">Church of Scotland</a> <a href=\"https://wikipedia.org/wiki/Minister_(Christianity)\" title=\"Minister (Christianity)\">Minister</a>, <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">Missionary</a> in <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> and <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, <a href=\"https://wikipedia.org/wiki/Writer\" title=\"Writer\">writer</a> and <a href=\"https://wikipedia.org/wiki/Poet\" title=\"Poet\">poet</a> (b. <a href=\"https://wikipedia.org/wiki/1886\" title=\"1886\">1886</a>)", "no_year_html": "Rev. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Church_of_Scotland\" title=\"Church of Scotland\">Church of Scotland</a> <a href=\"https://wikipedia.org/wiki/Minister_(Christianity)\" title=\"Minister (Christianity)\">Minister</a>, <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">Missionary</a> in <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> and <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, <a href=\"https://wikipedia.org/wiki/Writer\" title=\"Writer\">writer</a> and <a href=\"https://wikipedia.org/wiki/Poet\" title=\"Poet\">poet</a> (b. <a href=\"https://wikipedia.org/wiki/1886\" title=\"1886\">1886</a>)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>as_<PERSON><PERSON>_Williams"}, {"title": "Church of Scotland", "link": "https://wikipedia.org/wiki/Church_of_Scotland"}, {"title": "Minister (Christianity)", "link": "https://wikipedia.org/wiki/Minister_(Christianity)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Missionary"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Writer", "link": "https://wikipedia.org/wiki/Writer"}, {"title": "Poet", "link": "https://wikipedia.org/wiki/Poet"}, {"title": "1886", "link": "https://wikipedia.org/wiki/1886"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American writer and columnist (b. 1881)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer and columnist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer and columnist (b. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1896)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American archbishop (b. 1895)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>en\" title=\"<PERSON>en\"><PERSON></a>, American archbishop (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archbishop (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sheen"}]}, {"year": "1982", "text": "<PERSON>, American lawyer and politician (b. 1905)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian zoologist (b. 1895)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Australian zoologist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Australian zoologist (b. 1895)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(scientist)"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American photographer (b. 1898)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American photographer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American photographer (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ren<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1922)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vincent_Gardenia"}]}, {"year": "1993", "text": "<PERSON>, Northern Irish footballer and manager (b. 1926)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American author and academic (b. 1939)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American pilot (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American singer-songwriter (b. 1956)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English archaeologist and anthropologist (b. 1913)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and anthropologist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and anthropologist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, French lawyer and politician (b. 1909)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Welsh playwright and screenwriter (b. 1908)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, Welsh playwright and screenwriter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, Welsh playwright and screenwriter (b. 1908)", "links": [{"title": "<PERSON> (screenwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician (b. 1948)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Cohen"}]}, {"year": "1998", "text": "<PERSON>, American boxer and actor (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, <PERSON>, English field marshal (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English field marshal (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English field marshal (b. 1915)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Australian singer and guitarist (b. 1966)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and guitarist (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and guitarist (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American painter and sculptor (b. 1944)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American painter and poet (b. 1942)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American basketball player and coach (b. 1926)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American soldier, journalist, and politician, 39th Lieutenant Governor of Illinois (b. 1928)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American soldier, journalist, and politician, 39th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Illinois\" title=\"Lieutenant Governor of Illinois\">Lieutenant Governor of Illinois</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American soldier, journalist, and politician, 39th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Illinois\" title=\"Lieutenant Governor of Illinois\">Lieutenant Governor of Illinois</a> (b. 1928)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}, {"title": "Lieutenant Governor of Illinois", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Illinois"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-American pianist and educator (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_S%C3%A1ndor\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American pianist and educator (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_S%C3%A1ndor\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American pianist and educator (b. 1912)", "links": [{"title": "György <PERSON>", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_S%C3%A1ndor"}]}, {"year": "2005", "text": "<PERSON>, American author (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gibbs\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gibbs\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1919)", "links": [{"title": "<PERSON> Gibbs", "link": "https://wikipedia.org/wiki/Georgia_Gibbs"}]}, {"year": "2007", "text": "<PERSON>, Brazilian race car driver (b. 1981)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_<PERSON>afico"}]}, {"year": "2007", "text": "<PERSON>, American sociologist, author, and academic (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, author, and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, author, and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Ghanaian footballer (b. 1972)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Russian general, pilot, and astronaut (b. 1939)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general, pilot, and astronaut (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general, pilot, and astronaut (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American actor (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American saxophonist, flute player, and composer (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saxophonist)\" title=\"<PERSON> (saxophonist)\"><PERSON></a>, American saxophonist, flute player, and composer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(saxophonist)\" title=\"<PERSON> (saxophonist)\"><PERSON></a>, American saxophonist, flute player, and composer (b. 1925)", "links": [{"title": "<PERSON> (saxophonist)", "link": "https://wikipedia.org/wiki/<PERSON>(saxophonist)"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Lithuanian-Israeli lawyer and politician, 10th Speaker of the Knesset (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-Israeli lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Knesset_speakers\" class=\"mw-redirect\" title=\"List of Knesset speakers\">Speaker of the Knesset</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-Israeli lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Knesset_speakers\" class=\"mw-redirect\" title=\"List of Knesset speakers\">Speaker of the Knesset</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Knesset speakers", "link": "https://wikipedia.org/wiki/List_of_Knesset_speakers"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Hungarian painter and academic (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/B%C3%A9la_<PERSON>_<PERSON>di\" title=\"<PERSON><PERSON><PERSON> Na<PERSON>bo<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian painter and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian painter and academic (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_Na<PERSON>_<PERSON>di"}]}, {"year": "2012", "text": "<PERSON>, English lieutenant, astronomer, and educator (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant, astronomer, and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant, astronomer, and educator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English engineer and businessman, founded the Moulton Bicycle Company (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bicycle\" title=\"Moulton Bicycle\">Moulton Bicycle Company</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bicycle\" title=\"<PERSON>ulton Bicycle\">Moulton Bicycle Company</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Moulton Bicycle", "link": "https://wikipedia.org/wiki/Moulton_Bicycle"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress (b. 1969)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American pianist and musicologist (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and musicologist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and musicologist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian director and producer, co-founded Diva Futura (b. 1953)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian director and producer, co-founded <a href=\"https://wikipedia.org/wiki/Diva_Futura\" title=\"Diva Futura\"><PERSON>va Futura</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian director and producer, co-founded <a href=\"https://wikipedia.org/wiki/Diva_Futura\" title=\"Diva Futura\">Diva Futura</a> (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>va <PERSON>", "link": "https://wikipedia.org/wiki/Diva_Futura"}]}, {"year": "2012", "text": "<PERSON>, American inventor, co-created the bar code (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, American inventor, co-created the <a href=\"https://wikipedia.org/wiki/Bar_code\" class=\"mw-redirect\" title=\"Bar code\">bar code</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, American inventor, co-created the <a href=\"https://wikipedia.org/wiki/Bar_code\" class=\"mw-redirect\" title=\"Bar code\">bar code</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bar code", "link": "https://wikipedia.org/wiki/Bar_code"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Romanian editor, literary critic and writer (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Hristu_C%C3%A2ndroveanu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian editor, literary critic and writer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hristu_C%C3%A2ndroveanu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian editor, literary critic and writer (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hristu_C%C3%A2ndroveanu"}]}, {"year": "2013", "text": "<PERSON>, American soldier, lawyer, and judge (b. 1909)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and judge (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and judge (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish pianist and educator (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish pianist and educator (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish pianist and educator (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1943)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(American_football)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Canadian-American author, critic, and academic (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American author, critic, and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American author, critic, and academic (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American painter and poet (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Argentinian cardinal (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Mej%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Argentinian cardinal (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Mej%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Argentinian cardinal (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jorge_Mar%C3%ADa_Mej%C3%ADa"}]}, {"year": "2014", "text": "<PERSON>, American model and actress, Miss America 1959 (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress, <a href=\"https://wikipedia.org/wiki/Miss_America_1959\" title=\"Miss America 1959\">Miss America 1959</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress, <a href=\"https://wikipedia.org/wiki/Miss_America_1959\" title=\"Miss America 1959\">Miss America 1959</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Miss America 1959", "link": "https://wikipedia.org/wiki/Miss_America_1959"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian footballer and manager (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Blag<PERSON><PERSON>_<PERSON>vi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Blagoje_Paunovi%C4%87"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Slovenian linguist and author (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Jo%C5%BEe_Topori%C5%A1i%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian linguist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C5%BEe_Topori%C5%A1i%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian linguist and author (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C5%BEe_Topori%C5%A1i%C4%8D"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Austrian painter (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>shan<PERSON>_Afroyim\" title=\"<PERSON><PERSON><PERSON> Afroyim\"><PERSON><PERSON><PERSON></a>, Austrian painter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yi<PERSON>\" title=\"<PERSON><PERSON><PERSON> Afroyim\"><PERSON><PERSON><PERSON></a>, Austrian painter (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American statistician and academic (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and academic (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Brazilian lawyer and politician (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Juvenal_Juv%C3%AAncio\" title=\"Juvenal Juvênc<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian lawyer and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juvenal_Juv%C3%AAncio\" title=\"Ju<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian lawyer and politician (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juvenal_Juv%C3%AAncio"}]}, {"year": "2015", "text": "<PERSON>, Bolivian cardinal (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian cardinal (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian cardinal (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, American football player (b. 1942)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Duncan"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player (b. 1987)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player (b. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Filipino singer and actor (b. 1993)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino singer and actor (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino singer and actor (b. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American poet, writer and activist (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, writer and activist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, writer and activist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}