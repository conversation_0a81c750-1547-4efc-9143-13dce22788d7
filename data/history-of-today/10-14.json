{"date": "October 14", "url": "https://wikipedia.org/wiki/October_14", "data": {"Events": [{"year": "1066", "text": "The Norman conquest of England begins with the Battle of Hastings.", "html": "1066 - The <a href=\"https://wikipedia.org/wiki/Norman_Conquest\" title=\"Norman Conquest\">Norman conquest of England</a> begins with the <a href=\"https://wikipedia.org/wiki/Battle_of_Hastings\" title=\"Battle of Hastings\">Battle of Hastings</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Norman_Conquest\" title=\"Norman Conquest\">Norman conquest of England</a> begins with the <a href=\"https://wikipedia.org/wiki/Battle_of_Hastings\" title=\"Battle of Hastings\">Battle of Hastings</a>.", "links": [{"title": "Norman Conquest", "link": "https://wikipedia.org/wiki/Norman_Conquest"}, {"title": "Battle of Hastings", "link": "https://wikipedia.org/wiki/Battle_of_Hastings"}]}, {"year": "1322", "text": "<PERSON> the <PERSON> of Scotland defeats King <PERSON> of England at the Battle of Old Byland, forcing <PERSON> to accept Scotland's independence.", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON> of Scotland</a> defeats King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Old_Byland\" title=\"Battle of Old Byland\">Battle of Old Byland</a>, forcing <PERSON> to accept Scotland's independence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON> of Scotland</a> defeats <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Old_Byland\" title=\"Battle of Old Byland\">Battle of Old Byland</a>, forcing <PERSON> to accept Scotland's independence.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Battle of Old Byland", "link": "https://wikipedia.org/wiki/Battle_of_Old_Byland"}]}, {"year": "1586", "text": "<PERSON>, Queen of Scots, goes on trial for conspiracy against Queen <PERSON> of England.", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, goes on trial for conspiracy against Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, goes on trial for conspiracy against Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> of England</a>.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1656", "text": "The General Court of the Massachusetts Bay Colony enacts the first punitive legislation against the Religious Society of Friends.", "html": "1656 - The <a href=\"https://wikipedia.org/wiki/Massachusetts_General_Court\" title=\"Massachusetts General Court\">General Court</a> of the <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> enacts the first punitive legislation against the <a href=\"https://wikipedia.org/wiki/Quakers\" title=\"Quakers\">Religious Society of Friends</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Massachusetts_General_Court\" title=\"Massachusetts General Court\">General Court</a> of the <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> enacts the first punitive legislation against the <a href=\"https://wikipedia.org/wiki/Quakers\" title=\"Quakers\">Religious Society of Friends</a>.", "links": [{"title": "Massachusetts General Court", "link": "https://wikipedia.org/wiki/Massachusetts_General_Court"}, {"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}, {"title": "Quakers", "link": "https://wikipedia.org/wiki/Quakers"}]}, {"year": "1758", "text": "Seven Years' War: <PERSON> the Great suffers a rare defeat at the Battle of Hochkirch.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> suffers a rare defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Hochkirch\" title=\"Battle of Hochkirch\">Battle of Hochkirch</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> suffers a rare defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Hochkirch\" title=\"Battle of Hochkirch\">Battle of Hochkirch</a>.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Battle of Hochkirch", "link": "https://wikipedia.org/wiki/Battle_of_Hochkirch"}]}, {"year": "1773", "text": "The first recorded ministry of education, the Commission of National Education, is formed in the Polish-Lithuanian Commonwealth.", "html": "1773 - The first recorded ministry of education, the <a href=\"https://wikipedia.org/wiki/Commission_of_National_Education\" title=\"Commission of National Education\">Commission of National Education</a>, is formed in the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a>.", "no_year_html": "The first recorded ministry of education, the <a href=\"https://wikipedia.org/wiki/Commission_of_National_Education\" title=\"Commission of National Education\">Commission of National Education</a>, is formed in the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a>.", "links": [{"title": "Commission of National Education", "link": "https://wikipedia.org/wiki/Commission_of_National_Education"}, {"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}]}, {"year": "1774", "text": "American Revolution: The First Continental Congress denounces the British Parliament's Intolerable Acts and demands British concessions.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/First_Continental_Congress\" title=\"First Continental Congress\">First Continental Congress</a> denounces the <a href=\"https://wikipedia.org/wiki/Parliament_of_Great_Britain\" title=\"Parliament of Great Britain\">British Parliament</a>'s <a href=\"https://wikipedia.org/wiki/Intolerable_Acts\" title=\"Intolerable Acts\">Intolerable Acts</a> and demands British concessions.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/First_Continental_Congress\" title=\"First Continental Congress\">First Continental Congress</a> denounces the <a href=\"https://wikipedia.org/wiki/Parliament_of_Great_Britain\" title=\"Parliament of Great Britain\">British Parliament</a>'s <a href=\"https://wikipedia.org/wiki/Intolerable_Acts\" title=\"Intolerable Acts\">Intolerable Acts</a> and demands British concessions.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "First Continental Congress", "link": "https://wikipedia.org/wiki/First_Continental_Congress"}, {"title": "Parliament of Great Britain", "link": "https://wikipedia.org/wiki/Parliament_of_Great_Britain"}, {"title": "Intolerable Acts", "link": "https://wikipedia.org/wiki/Intolerable_Acts"}]}, {"year": "1791", "text": "The revolutionary group the United Irishmen is formed in Belfast, Ireland leading to the Irish Rebellion of 1798.", "html": "1791 - The revolutionary group the <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irishmen</a> is formed in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, Ireland leading to the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>.", "no_year_html": "The revolutionary group the <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irishmen</a> is formed in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, Ireland leading to the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>.", "links": [{"title": "Society of United Irishmen", "link": "https://wikipedia.org/wiki/Society_of_United_Irishmen"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}, {"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}]}, {"year": "1805", "text": "War of the Third Coalition: A French corps defeats an Austrian attempt to escape encirclement at Ulm.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/War_of_the_Third_Coalition\" title=\"War of the Third Coalition\">War of the Third Coalition</a>: A French corps <a href=\"https://wikipedia.org/wiki/Battle_of_Elchingen\" title=\"Battle of Elchingen\">defeats</a> an Austrian attempt to escape encirclement at Ulm.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Third_Coalition\" title=\"War of the Third Coalition\">War of the Third Coalition</a>: A French corps <a href=\"https://wikipedia.org/wiki/Battle_of_Elchingen\" title=\"Battle of Elchingen\">defeats</a> an Austrian attempt to escape encirclement at Ulm.", "links": [{"title": "War of the Third Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Third_Coalition"}, {"title": "Battle of Elchingen", "link": "https://wikipedia.org/wiki/Battle_of_Elchingen"}]}, {"year": "1806", "text": "War of the Fourth Coalition: <PERSON> decisively defeats Prussia at the Battle of Jena-Auerstedt.", "html": "1806 - <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>: <PERSON> decisively defeats Prussia at the <a href=\"https://wikipedia.org/wiki/Battle_of_Jena%E2%80%93Auerstedt\" title=\"Battle of Jena-Auerstedt\">Battle of Jena-Auerstedt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>: <PERSON> decisively defeats Prussia at the <a href=\"https://wikipedia.org/wiki/Battle_of_Jena%E2%80%93Auerstedt\" title=\"Battle of Jena-Auerstedt\">Battle of Jena-Auerstedt</a>.", "links": [{"title": "War of the Fourth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fourth_Coalition"}, {"title": "Battle of Jena-Auerstedt", "link": "https://wikipedia.org/wiki/Battle_of_Jena%E2%80%93Auerstedt"}]}, {"year": "1808", "text": "The Republic of Ragusa is annexed by France.", "html": "1808 - The <a href=\"https://wikipedia.org/wiki/Republic_of_Ragusa\" title=\"Republic of Ragusa\">Republic of Ragusa</a> is annexed by France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_Ragusa\" title=\"Republic of Ragusa\">Republic of Ragusa</a> is annexed by France.", "links": [{"title": "Republic of Ragusa", "link": "https://wikipedia.org/wiki/Republic_of_Ragusa"}]}, {"year": "1809", "text": "The Treaty of Schönbrunn is signed, ending the War of the Fifth Coalition, the final successful war in <PERSON>'s military career.", "html": "1809 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Sch%C3%B6nbrunn\" title=\"Treaty of Schönbrunn\">Treaty of Schönbrunn</a> is signed, ending the <a href=\"https://wikipedia.org/wiki/War_of_the_Fifth_Coalition\" title=\"War of the Fifth Coalition\">War of the Fifth Coalition</a>, the final successful war in <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> <PERSON>'s military career.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Sch%C3%B6nbrunn\" title=\"Treaty of Schönbrunn\">Treaty of Schönbrunn</a> is signed, ending the <a href=\"https://wikipedia.org/wiki/War_of_the_Fifth_Coalition\" title=\"War of the Fifth Coalition\">War of the Fifth Coalition</a>, the final successful war in <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a> <PERSON>'s military career.", "links": [{"title": "Treaty of Schönbrunn", "link": "https://wikipedia.org/wiki/Treaty_of_Sch%C3%B6nbrunn"}, {"title": "War of the Fifth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fifth_Coalition"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1843", "text": "Irish nationalist <PERSON> is arrested by the British on charges of criminal conspiracy.", "html": "1843 - Irish nationalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a> is arrested by the British on charges of criminal conspiracy.", "no_year_html": "Irish nationalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a> is arrested by the British on charges of criminal conspiracy.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Daniel_O%27Connell"}]}, {"year": "1863", "text": "American Civil War: Confederate troops under the command of <PERSON><PERSON> <PERSON><PERSON> fail to drive the Union Army completely out of Virginia.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Confederate troops under the command of <PERSON><PERSON> <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Bristoe_Station\" title=\"Battle of Bristoe Station\">fail to drive</a> the <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a> completely out of <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Confederate troops under the command of <PERSON><PERSON> <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Bristoe_Station\" title=\"Battle of Bristoe Station\">fail to drive</a> the <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a> completely out of <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Bristoe Station", "link": "https://wikipedia.org/wiki/Battle_of_Bristoe_Station"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}, {"year": "1884", "text": "<PERSON> receives a U.S. Government patent on his new paper-strip photographic film.", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a U.S. Government <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> on his new paper-strip <a href=\"https://wikipedia.org/wiki/Photographic_film\" title=\"Photographic film\">photographic film</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a U.S. Government <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> on his new paper-strip <a href=\"https://wikipedia.org/wiki/Photographic_film\" title=\"Photographic film\">photographic film</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Photographic film", "link": "https://wikipedia.org/wiki/Photographic_film"}]}, {"year": "1888", "text": "<PERSON> films the first motion picture, Roundhay Garden Scene.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> films the first <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">motion picture</a>, <i><a href=\"https://wikipedia.org/wiki/Roundhay_Garden_Scene\" title=\"Roundhay Garden Scene\">Roundhay Garden Scene</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> films the first <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">motion picture</a>, <i><a href=\"https://wikipedia.org/wiki/Roundhay_Garden_Scene\" title=\"Roundhay Garden Scene\">Roundhay Garden Scene</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Film", "link": "https://wikipedia.org/wiki/Film"}, {"title": "Roundhay Garden Scene", "link": "https://wikipedia.org/wiki/Roundhay_Garden_Scene"}]}, {"year": "1898", "text": "The steam ship SS Mohegan sinks near the Lizard peninsula, Cornwall, killing 106.", "html": "1898 - The steam ship <a href=\"https://wikipedia.org/wiki/SS_Mohegan\" title=\"SS Mohegan\">SS <i>Mohegan</i></a> sinks near the <a href=\"https://wikipedia.org/wiki/The_Lizard\" title=\"The Lizard\">Lizard peninsula</a>, <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a>, killing 106.", "no_year_html": "The steam ship <a href=\"https://wikipedia.org/wiki/SS_Mohegan\" title=\"SS Mohegan\">SS <i>Mohegan</i></a> sinks near the <a href=\"https://wikipedia.org/wiki/The_Lizard\" title=\"The Lizard\">Lizard peninsula</a>, <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a>, killing 106.", "links": [{"title": "SS Mohegan", "link": "https://wikipedia.org/wiki/SS_Mohegan"}, {"title": "The Lizard", "link": "https://wikipedia.org/wiki/The_Lizard"}, {"title": "Cornwall", "link": "https://wikipedia.org/wiki/Cornwall"}]}, {"year": "1908", "text": "The Chicago Cubs defeat the Detroit Tigers, 2-0, clinching the 1908 World Series; this would be their last until winning the 2016 World Series.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/Chicago_Cubs\" title=\"Chicago Cubs\">Chicago Cubs</a> defeat the <a href=\"https://wikipedia.org/wiki/Detroit_Tigers\" title=\"Detroit Tigers\">Detroit Tigers</a>, 2-0, clinching the <a href=\"https://wikipedia.org/wiki/1908_World_Series\" title=\"1908 World Series\">1908 World Series</a>; this would be their last until winning the <a href=\"https://wikipedia.org/wiki/2016_World_Series\" title=\"2016 World Series\">2016 World Series</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chicago_Cubs\" title=\"Chicago Cubs\">Chicago Cubs</a> defeat the <a href=\"https://wikipedia.org/wiki/Detroit_Tigers\" title=\"Detroit Tigers\">Detroit Tigers</a>, 2-0, clinching the <a href=\"https://wikipedia.org/wiki/1908_World_Series\" title=\"1908 World Series\">1908 World Series</a>; this would be their last until winning the <a href=\"https://wikipedia.org/wiki/2016_World_Series\" title=\"2016 World Series\">2016 World Series</a>.", "links": [{"title": "Chicago Cubs", "link": "https://wikipedia.org/wiki/Chicago_Cubs"}, {"title": "Detroit Tigers", "link": "https://wikipedia.org/wiki/Detroit_Tigers"}, {"title": "1908 World Series", "link": "https://wikipedia.org/wiki/1908_World_Series"}, {"title": "2016 World Series", "link": "https://wikipedia.org/wiki/2016_World_Series"}]}, {"year": "1910", "text": "English aviator <PERSON> lands his aircraft on Executive Avenue near the White House in Washington, D.C.", "html": "1910 - English aviator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands his aircraft on Executive Avenue near the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> in Washington, D.C.", "no_year_html": "English aviator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands his aircraft on Executive Avenue near the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> in Washington, D.C.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1912", "text": "Former president <PERSON> is shot and mildly wounded by <PERSON>. With the fresh wound in his chest, and the bullet still within it, <PERSON> delivers his scheduled speech.", "html": "1912 - Former president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is shot and mildly wounded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Schrank\" class=\"mw-redirect\" title=\"<PERSON> Schrank\"><PERSON>hrank</a>. With the fresh wound in his chest, and the bullet still within it, <PERSON> delivers his scheduled speech.", "no_year_html": "Former president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is shot and mildly wounded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Schrank\" class=\"mw-redirect\" title=\"<PERSON> Schrank\"><PERSON>hrank</a>. With the fresh wound in his chest, and the bullet still within it, <PERSON> delivers his scheduled speech.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hrank"}]}, {"year": "1913", "text": "Senghenydd colliery disaster, the United Kingdom's worst coal mining accident, claims the lives of 439 miners.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Senghenydd_colliery_disaster\" title=\"Senghenydd colliery disaster\">Senghenydd colliery disaster</a>, the United Kingdom's worst coal mining accident, claims the lives of 439 miners.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Senghenydd_colliery_disaster\" title=\"Senghenydd colliery disaster\">Senghenydd colliery disaster</a>, the United Kingdom's worst coal mining accident, claims the lives of 439 miners.", "links": [{"title": "Senghenydd colliery disaster", "link": "https://wikipedia.org/wiki/Senghenydd_colliery_disaster"}]}, {"year": "1915", "text": "World War I: Bulgaria joins the Central Powers.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Bulgaria <a href=\"https://wikipedia.org/wiki/Bulgaria_during_World_War_I\" title=\"Bulgaria during World War I\">joins the Central Powers</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Bulgaria <a href=\"https://wikipedia.org/wiki/Bulgaria_during_World_War_I\" title=\"Bulgaria during World War I\">joins the Central Powers</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Bulgaria during World War I", "link": "https://wikipedia.org/wiki/Bulgaria_during_World_War_I"}]}, {"year": "1920", "text": "Finland and Soviet Russia sign the Treaty of Tartu, exchanging some territories.", "html": "1920 - Finland and Soviet Russia sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Tartu_(Finland%E2%80%93Russia)\" title=\"Treaty of Tartu (Finland-Russia)\">Treaty of Tartu</a>, exchanging some territories.", "no_year_html": "Finland and Soviet Russia sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Tartu_(Finland%E2%80%93Russia)\" title=\"Treaty of Tartu (Finland-Russia)\">Treaty of Tartu</a>, exchanging some territories.", "links": [{"title": "Treaty of Tartu (Finland-Russia)", "link": "https://wikipedia.org/wiki/Treaty_of_Tartu_(Finland%E2%80%93Russia)"}]}, {"year": "1923", "text": "After the Irish Civil War the 1923 Irish hunger strikes were undertaken by thousands of Irish republican prisoners protesting the continuation of their internment without trial.", "html": "1923 - After the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a> the <a href=\"https://wikipedia.org/wiki/1923_Irish_hunger_strikes\" title=\"1923 Irish hunger strikes\">1923 Irish hunger strikes</a> were undertaken by thousands of <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish republican</a> prisoners protesting the continuation of their <a href=\"https://wikipedia.org/wiki/Internment\" title=\"Internment\">internment</a> without trial.", "no_year_html": "After the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a> the <a href=\"https://wikipedia.org/wiki/1923_Irish_hunger_strikes\" title=\"1923 Irish hunger strikes\">1923 Irish hunger strikes</a> were undertaken by thousands of <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish republican</a> prisoners protesting the continuation of their <a href=\"https://wikipedia.org/wiki/Internment\" title=\"Internment\">internment</a> without trial.", "links": [{"title": "Irish Civil War", "link": "https://wikipedia.org/wiki/Irish_Civil_War"}, {"title": "1923 Irish hunger strikes", "link": "https://wikipedia.org/wiki/1923_Irish_hunger_strikes"}, {"title": "Irish republican", "link": "https://wikipedia.org/wiki/Irish_republican"}, {"title": "Internment", "link": "https://wikipedia.org/wiki/Internment"}]}, {"year": "1930", "text": "The former and first President of Finland, <PERSON><PERSON> <PERSON><PERSON>, and his wife, <PERSON><PERSON>, are kidnapped from their home by members of the far-right Lapua Movement.", "html": "1930 - The former and first <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a>, <a href=\"https://wikipedia.org/wiki/K._J._St%C3%A5hlberg\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, and his wife, <a href=\"https://wikipedia.org/wiki/Ester_St%C3%A5hlberg\" title=\"Ester Ståhlberg\"><PERSON><PERSON></a>, are <a href=\"https://wikipedia.org/wiki/St%C3%A5hlberg_kidnapping\" title=\"St<PERSON>hlberg kidnapping\">kidnapped from their home</a> by members of the far-right <a href=\"https://wikipedia.org/wiki/Lapua_Movement\" title=\"Lapua Movement\">Lapua Movement</a>.", "no_year_html": "The former and first <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a>, <a href=\"https://wikipedia.org/wiki/K._J._St%C3%A5hlberg\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, and his wife, <a href=\"https://wikipedia.org/wiki/Ester_St%C3%A5hlberg\" title=\"Ester Ståhlberg\"><PERSON><PERSON></a>, are <a href=\"https://wikipedia.org/wiki/St%C3%A5hlberg_kidnapping\" title=\"<PERSON><PERSON><PERSON>berg kidnapping\">kidnapped from their home</a> by members of the far-right <a href=\"https://wikipedia.org/wiki/Lapua_Movement\" title=\"Lapua Movement\">Lapua Movement</a>.", "links": [{"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K._J._St%C3%A5hlberg"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ester_St%C3%A5hlberg"}, {"title": "<PERSON><PERSON><PERSON><PERSON> kidnapping", "link": "https://wikipedia.org/wiki/St%C3%A5<PERSON><PERSON>_kidnapping"}, {"title": "Lapua Movement", "link": "https://wikipedia.org/wiki/Lapua_Movement"}]}, {"year": "1933", "text": "Germany withdraws from the League of Nations and World Disarmament Conference.", "html": "1933 - Germany withdraws from the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> and <a href=\"https://wikipedia.org/wiki/World_Disarmament_Conference\" class=\"mw-redirect\" title=\"World Disarmament Conference\">World Disarmament Conference</a>.", "no_year_html": "Germany withdraws from the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> and <a href=\"https://wikipedia.org/wiki/World_Disarmament_Conference\" class=\"mw-redirect\" title=\"World Disarmament Conference\">World Disarmament Conference</a>.", "links": [{"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}, {"title": "World Disarmament Conference", "link": "https://wikipedia.org/wiki/World_Disarmament_Conference"}]}, {"year": "1939", "text": "World War II: The German submarine U-47 sinks the British battleship HMS Royal Oak within her harbour at Scapa Flow, Scotland.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/German_submarine_U-47_(1938)\" title=\"German submarine U-47 (1938)\">German submarine <i>U-47</i></a> sinks the British battleship <a href=\"https://wikipedia.org/wiki/HMS_Royal_Oak_(08)\" title=\"HMS Royal Oak (08)\">HMS <i>Royal Oak</i></a> within her harbour at Scapa Flow, Scotland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/German_submarine_U-47_(1938)\" title=\"German submarine U-47 (1938)\">German submarine <i>U-47</i></a> sinks the British battleship <a href=\"https://wikipedia.org/wiki/HMS_Royal_Oak_(08)\" title=\"HMS Royal Oak (08)\">HMS <i>Royal Oak</i></a> within her harbour at Scapa Flow, Scotland.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "German submarine U-47 (1938)", "link": "https://wikipedia.org/wiki/German_submarine_U-47_(1938)"}, {"title": "HMS Royal Oak (08)", "link": "https://wikipedia.org/wiki/HMS_Royal_Oak_(08)"}]}, {"year": "1940", "text": "World War II: The Balham underground station disaster kills sixty-six people during the London Blitz.", "html": "1940 - World War II: The <a href=\"https://wikipedia.org/wiki/Balham_underground_station_disaster\" class=\"mw-redirect\" title=\"Balham underground station disaster\">Balham underground station disaster</a> kills sixty-six people during the London Blitz.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Balham_underground_station_disaster\" class=\"mw-redirect\" title=\"Balham underground station disaster\">Balham underground station disaster</a> kills sixty-six people during the London Blitz.", "links": [{"title": "Balham underground station disaster", "link": "https://wikipedia.org/wiki/Balham_underground_station_disaster"}]}, {"year": "1942", "text": "World War II: The German submarine U-69 (1940) sinks the Canadian passenger ferry SS Caribou approximately 20 nautical miles southwest of Port aux Basques, Newfoundland.", "html": "1942 - World War II: The <a href=\"https://wikipedia.org/wiki/German_submarine_U-69_(1940)\" title=\"German submarine U-69 (1940)\">German submarine U-69 (1940)</a> sinks the Canadian passenger ferry <a href=\"https://wikipedia.org/wiki/SS_Caribou\" title=\"SS Caribou\">SS Caribou</a> approximately 20 <a href=\"https://wikipedia.org/wiki/Nautical_mile\" title=\"Nautical mile\">nautical miles</a> southwest of <a href=\"https://wikipedia.org/wiki/Channel-Port_aux_Basques\" title=\"Channel-Port aux Basques\">Port aux Basques</a>, Newfoundland.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/German_submarine_U-69_(1940)\" title=\"German submarine U-69 (1940)\">German submarine U-69 (1940)</a> sinks the Canadian passenger ferry <a href=\"https://wikipedia.org/wiki/SS_Caribou\" title=\"SS Caribou\">SS Caribou</a> approximately 20 <a href=\"https://wikipedia.org/wiki/Nautical_mile\" title=\"Nautical mile\">nautical miles</a> southwest of <a href=\"https://wikipedia.org/wiki/Channel-Port_aux_Basques\" title=\"Channel-Port aux Basques\">Port aux Basques</a>, Newfoundland.", "links": [{"title": "German submarine U-69 (1940)", "link": "https://wikipedia.org/wiki/German_submarine_U-69_(1940)"}, {"title": "SS Caribou", "link": "https://wikipedia.org/wiki/SS_Caribou"}, {"title": "Nautical mile", "link": "https://wikipedia.org/wiki/Nautical_mile"}, {"title": "Channel-Port aux Basques", "link": "https://wikipedia.org/wiki/Channel-Port_aux_Basques"}]}, {"year": "1943", "text": "World War II: Prisoners at Sobibor extermination camp covertly assassinate most of the on-duty SS officers and then stage a mass breakout.", "html": "1943 - World War II: Prisoners at <a href=\"https://wikipedia.org/wiki/Sobibor_extermination_camp\" title=\"Sobibor extermination camp\">Sobibor extermination camp</a> covertly assassinate most of the on-duty SS officers and then stage a mass breakout.", "no_year_html": "World War II: Prisoners at <a href=\"https://wikipedia.org/wiki/Sobibor_extermination_camp\" title=\"Sobibor extermination camp\">Sobibor extermination camp</a> covertly assassinate most of the on-duty SS officers and then stage a mass breakout.", "links": [{"title": "Sobibor extermination camp", "link": "https://wikipedia.org/wiki/Sobibor_extermination_camp"}]}, {"year": "1943", "text": "World War II: The United States Eighth Air Force loses 60 of 291 B-17 Flying Fortresses during the Second Raid on Schweinfurt.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Eighth_Air_Force\" title=\"Eighth Air Force\">United States Eighth Air Force</a> loses 60 of 291 <a href=\"https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress\" title=\"Boeing B-17 Flying Fortress\">B-17 Flying Fortresses</a> during the <a href=\"https://wikipedia.org/wiki/Second_Raid_on_Schweinfurt\" class=\"mw-redirect\" title=\"Second Raid on Schweinfurt\">Second Raid on Schweinfurt</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Eighth_Air_Force\" title=\"Eighth Air Force\">United States Eighth Air Force</a> loses 60 of 291 <a href=\"https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress\" title=\"Boeing B-17 Flying Fortress\">B-17 Flying Fortresses</a> during the <a href=\"https://wikipedia.org/wiki/Second_Raid_on_Schweinfurt\" class=\"mw-redirect\" title=\"Second Raid on Schweinfurt\">Second Raid on Schweinfurt</a>.", "links": [{"title": "Eighth Air Force", "link": "https://wikipedia.org/wiki/Eighth_Air_Force"}, {"title": "Boeing B-17 Flying Fortress", "link": "https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress"}, {"title": "Second Raid on Schweinfurt", "link": "https://wikipedia.org/wiki/Second_Raid_on_Schweinfurt"}]}, {"year": "1943", "text": "World War II: The Second Philippine Republic, a puppet state of Japan, is inaugurated with <PERSON> as its president.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Second_Philippine_Republic\" title=\"Second Philippine Republic\">Second Philippine Republic</a>, a puppet state of <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>, is inaugurated with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as its president.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Second_Philippine_Republic\" title=\"Second Philippine Republic\">Second Philippine Republic</a>, a puppet state of <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>, is inaugurated with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as its president.", "links": [{"title": "Second Philippine Republic", "link": "https://wikipedia.org/wiki/Second_Philippine_Republic"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "Flying the Bell XS-1 over Muroc Army Air Field in California, Captain <PERSON> breaks the sound barrier in level flight, reaching Mach 1.05.", "html": "1947 - Flying the <a href=\"https://wikipedia.org/wiki/Bell_X-1\" title=\"Bell X-1\">Bell XS-1</a> over <a href=\"https://wikipedia.org/wiki/Edwards_Air_Force_Base\" title=\"Edwards Air Force Base\">Muroc Army Air Field</a> in California, Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks the <a href=\"https://wikipedia.org/wiki/Sound_barrier\" title=\"Sound barrier\">sound barrier</a> in level flight, reaching <a href=\"https://wikipedia.org/wiki/Mach_number\" title=\"Mach number\">Mach 1.05</a>.", "no_year_html": "Flying the <a href=\"https://wikipedia.org/wiki/Bell_X-1\" title=\"Bell X-1\">Bell XS-1</a> over <a href=\"https://wikipedia.org/wiki/Edwards_Air_Force_Base\" title=\"Edwards Air Force Base\">Muroc Army Air Field</a> in California, Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks the <a href=\"https://wikipedia.org/wiki/Sound_barrier\" title=\"Sound barrier\">sound barrier</a> in level flight, reaching <a href=\"https://wikipedia.org/wiki/Mach_number\" title=\"Mach number\">Mach 1.05</a>.", "links": [{"title": "Bell X-1", "link": "https://wikipedia.org/wiki/Bell_X-1"}, {"title": "Edwards Air Force Base", "link": "https://wikipedia.org/wiki/Edwards_Air_Force_Base"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sound barrier", "link": "https://wikipedia.org/wiki/Sound_barrier"}, {"title": "Mach number", "link": "https://wikipedia.org/wiki/Mach_number"}]}, {"year": "1949", "text": "The Smith Act trials of Communist Party leaders in the United States convicts eleven defendants of conspiring to advocate the violent overthrow of the federal government.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Smith_Act_trials_of_Communist_Party_leaders\" title=\"Smith Act trials of Communist Party leaders\">Smith Act trials of Communist Party leaders</a> in the United States convicts eleven defendants of conspiring to advocate the violent overthrow of the federal government.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Smith_Act_trials_of_Communist_Party_leaders\" title=\"Smith Act trials of Communist Party leaders\">Smith Act trials of Communist Party leaders</a> in the United States convicts eleven defendants of conspiring to advocate the violent overthrow of the federal government.", "links": [{"title": "<PERSON> Act trials of Communist Party leaders", "link": "https://wikipedia.org/wiki/Smith_Act_trials_of_Communist_Party_leaders"}]}, {"year": "1952", "text": "Korean War: The Battle of Triangle Hill is the biggest and bloodiest battle of 1952.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Triangle_Hill\" title=\"Battle of Triangle Hill\">Battle of Triangle Hill</a> is the biggest and bloodiest battle of 1952.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Triangle_Hill\" title=\"Battle of Triangle Hill\">Battle of Triangle Hill</a> is the biggest and bloodiest battle of 1952.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Battle of Triangle Hill", "link": "https://wikipedia.org/wiki/Battle_of_Triangle_Hill"}]}, {"year": "1956", "text": "Dr. <PERSON><PERSON> <PERSON><PERSON>, leader of India's Untouchable caste, converts to Buddhism along with 385,000 of his followers (see Neo-Buddhism).", "html": "1956 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, leader of India's <a href=\"https://wikipedia.org/wiki/Dalit\" title=\"Dalit\">Untouchable caste</a>, converts to Buddhism along with 385,000 of his followers (see <a href=\"https://wikipedia.org/wiki/Neo-Buddhism\" class=\"mw-redirect\" title=\"Neo-Buddhism\">Neo-Buddhism</a>).", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, leader of India's <a href=\"https://wikipedia.org/wiki/Dalit\" title=\"Dalit\">Untouchable caste</a>, converts to Buddhism along with 385,000 of his followers (see <a href=\"https://wikipedia.org/wiki/Neo-Buddhism\" class=\"mw-redirect\" title=\"Neo-Buddhism\">Neo-Buddhism</a>).", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Dalit", "link": "https://wikipedia.org/wiki/Dalit"}, {"title": "Neo-Buddhism", "link": "https://wikipedia.org/wiki/Neo-Buddhism"}]}, {"year": "1957", "text": "The 23rd Canadian Parliament becomes the only one to be personally opened by the Queen of Canada.", "html": "1957 - The <a href=\"https://wikipedia.org/wiki/23rd_Canadian_Parliament\" title=\"23rd Canadian Parliament\">23rd Canadian Parliament</a> becomes the only one to be personally opened by the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Canada\" title=\"Monarchy of Canada\">Queen of Canada</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/23rd_Canadian_Parliament\" title=\"23rd Canadian Parliament\">23rd Canadian Parliament</a> becomes the only one to be personally opened by the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Canada\" title=\"Monarchy of Canada\">Queen of Canada</a>.", "links": [{"title": "23rd Canadian Parliament", "link": "https://wikipedia.org/wiki/23rd_Canadian_Parliament"}, {"title": "Monarchy of Canada", "link": "https://wikipedia.org/wiki/Monarchy_of_Canada"}]}, {"year": "1957", "text": "At least 81 people are killed in the most devastating flood in the history of the Spanish city of Valencia.", "html": "1957 - At least 81 people are killed in the most devastating <a href=\"https://wikipedia.org/wiki/1957_Valencia_flood\" title=\"1957 Valencia flood\">flood</a> in the history of the Spanish city of <a href=\"https://wikipedia.org/wiki/Valencia\" title=\"Valencia\">Valencia</a>.", "no_year_html": "At least 81 people are killed in the most devastating <a href=\"https://wikipedia.org/wiki/1957_Valencia_flood\" title=\"1957 Valencia flood\">flood</a> in the history of the Spanish city of <a href=\"https://wikipedia.org/wiki/Valencia\" title=\"Valencia\">Valencia</a>.", "links": [{"title": "1957 Valencia flood", "link": "https://wikipedia.org/wiki/1957_Valencia_flood"}, {"title": "Valencia", "link": "https://wikipedia.org/wiki/Valencia"}]}, {"year": "1962", "text": "The Cuban Missile Crisis begins when an American reconnaissance aircraft takes photographs of Soviet ballistic missiles being installed in Cuba.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Cuban_Missile_Crisis\" title=\"Cuban Missile Crisis\">Cuban Missile Crisis</a> begins when an American reconnaissance aircraft takes photographs of Soviet ballistic missiles being installed in Cuba.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cuban_Missile_Crisis\" title=\"Cuban Missile Crisis\">Cuban Missile Crisis</a> begins when an American reconnaissance aircraft takes photographs of Soviet ballistic missiles being installed in Cuba.", "links": [{"title": "Cuban Missile Crisis", "link": "https://wikipedia.org/wiki/Cuban_Missile_Crisis"}]}, {"year": "1964", "text": "<PERSON> receives the Nobel Peace Prize for combating racial inequality through nonviolence.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> receives the <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a> for combating <a href=\"https://wikipedia.org/wiki/Racial_inequality_in_the_United_States\" title=\"Racial inequality in the United States\">racial inequality</a> through <a href=\"https://wikipedia.org/wiki/Nonviolence\" title=\"Nonviolence\">nonviolence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> receives the <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a> for combating <a href=\"https://wikipedia.org/wiki/Racial_inequality_in_the_United_States\" title=\"Racial inequality in the United States\">racial inequality</a> through <a href=\"https://wikipedia.org/wiki/Nonviolence\" title=\"Nonviolence\">nonviolence</a>.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}, {"title": "Racial inequality in the United States", "link": "https://wikipedia.org/wiki/Racial_inequality_in_the_United_States"}, {"title": "Nonviolence", "link": "https://wikipedia.org/wiki/Nonviolence"}]}, {"year": "1964", "text": "The Soviet Presidium and the Communist Party Central Committee each vote to accept <PERSON><PERSON>'s \"voluntary\" request to retire from his offices.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Presidium_of_the_Supreme_Soviet\" title=\"Presidium of the Supreme Soviet\">Soviet Presidium</a> and the <a href=\"https://wikipedia.org/wiki/Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Central Committee of the Communist Party of the Soviet Union\">Communist Party Central Committee</a> each vote to accept <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s \"voluntary\" request to retire from his offices.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Presidium_of_the_Supreme_Soviet\" title=\"Presidium of the Supreme Soviet\">Soviet Presidium</a> and the <a href=\"https://wikipedia.org/wiki/Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Central Committee of the Communist Party of the Soviet Union\">Communist Party Central Committee</a> each vote to accept <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s \"voluntary\" request to retire from his offices.", "links": [{"title": "Presidium of the Supreme Soviet", "link": "https://wikipedia.org/wiki/Presidium_of_the_Supreme_Soviet"}, {"title": "Central Committee of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Central_Committee_of_the_Communist_Party_of_the_Soviet_Union"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "The city of Montreal begins the operation of its underground Montreal Metro rapid transit system.", "html": "1966 - The city of <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a> begins the operation of its underground <a href=\"https://wikipedia.org/wiki/Montreal_Metro\" title=\"Montreal Metro\">Montreal Metro</a> rapid transit system.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a> begins the operation of its underground <a href=\"https://wikipedia.org/wiki/Montreal_Metro\" title=\"Montreal Metro\">Montreal Metro</a> rapid transit system.", "links": [{"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}, {"title": "Montreal Metro", "link": "https://wikipedia.org/wiki/Montreal_Metro"}]}, {"year": "1966", "text": "The Dutch Cals cabinet fell after <PERSON><PERSON>, the leader of the government party, filed a successful motion against the budget, in what later became known as the Night of Schmelzer.", "html": "1966 - The Dutch <a href=\"https://wikipedia.org/wiki/Cals_cabinet\" title=\"<PERSON>s cabinet\">Cals cabinet</a> fell after <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the leader of the government party, filed a successful motion against the budget, in what later became known as the <i><a href=\"https://wikipedia.org/wiki/Night_of_Schmelzer\" title=\"Night of Schmelzer\">Night of Schmelzer</a></i>.", "no_year_html": "The Dutch <a href=\"https://wikipedia.org/wiki/Cals_cabinet\" title=\"<PERSON>s cabinet\">Cals cabinet</a> fell after <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the leader of the government party, filed a successful motion against the budget, in what later became known as the <i><a href=\"https://wikipedia.org/wiki/Night_of_Schmelzer\" title=\"Night of Schmelzer\">Night of Schmelzer</a></i>.", "links": [{"title": "<PERSON><PERSON> cabinet", "link": "https://wikipedia.org/wiki/Cals_cabinet"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Night of Schmelzer", "link": "https://wikipedia.org/wiki/Night_of_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1968", "text": "Apollo program: The first live television broadcast by American astronauts in orbit is performed by the Apollo 7 crew.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: The first live television broadcast by American astronauts in orbit is performed by the <a href=\"https://wikipedia.org/wiki/Apollo_7\" title=\"Apollo 7\">Apollo 7</a> crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: The first live television broadcast by American astronauts in orbit is performed by the <a href=\"https://wikipedia.org/wiki/Apollo_7\" title=\"Apollo 7\">Apollo 7</a> crew.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 7", "link": "https://wikipedia.org/wiki/Apollo_7"}]}, {"year": "1968", "text": "The 6.5 Mw  Meckering earthquake shakes the southwest portion of Western Australia with a maximum Mercalli intensity of IX (Violent), causing $2.2 million in damage and leaving 20-28 people injured.", "html": "1968 - The 6.5 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1968_Meckering_earthquake\" title=\"1968 Meckering earthquake\">Meckering earthquake</a> shakes the southwest portion of <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), causing $2.2 million in damage and leaving 20-28 people injured.", "no_year_html": "The 6.5 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1968_Meckering_earthquake\" title=\"1968 Meckering earthquake\">Meckering earthquake</a> shakes the southwest portion of <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), causing $2.2 million in damage and leaving 20-28 people injured.", "links": [{"title": "1968 Meckering earthquake", "link": "https://wikipedia.org/wiki/1968_Meckering_earthquake"}, {"title": "Western Australia", "link": "https://wikipedia.org/wiki/Western_Australia"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1968", "text": "<PERSON> becomes the first man ever to break the so-called \"ten-second barrier\" in the 100-meter sprint with a time of 9.95 seconds.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first man ever to break the so-called \"ten-second barrier\" in the 100-meter sprint with a time of 9.95 seconds.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first man ever to break the so-called \"ten-second barrier\" in the 100-meter sprint with a time of 9.95 seconds.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "In the Thammasat student uprising, over 100,000 people protest in Thailand against the military government. Seventy-seven are killed and 857 are injured by soldiers.", "html": "1973 - In the <a href=\"https://wikipedia.org/wiki/1973_Thai_popular_uprising\" title=\"1973 Thai popular uprising\">Thammasat student uprising</a>, over 100,000 people protest in Thailand against the military government. Seventy-seven are killed and 857 are injured by soldiers.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/1973_Thai_popular_uprising\" title=\"1973 Thai popular uprising\">Thammasat student uprising</a>, over 100,000 people protest in Thailand against the military government. Seventy-seven are killed and 857 are injured by soldiers.", "links": [{"title": "1973 Thai popular uprising", "link": "https://wikipedia.org/wiki/1973_Thai_popular_uprising"}]}, {"year": "1975", "text": "An RAF Avro Vulcan bomber explodes and crashes over Żabbar, Malta after an aborted landing, killing five crew members and one person on the ground.", "html": "1975 - An <a href=\"https://wikipedia.org/wiki/RAF\" class=\"mw-redirect\" title=\"RAF\">RAF</a> <a href=\"https://wikipedia.org/wiki/Avro_Vulcan\" title=\"Avro Vulcan\">Avro Vulcan</a> bomber <a href=\"https://wikipedia.org/wiki/1975_%C5%BBabbar_Avro_Vulcan_crash\" title=\"1975 Żabbar Avro Vulcan crash\">explodes and crashes</a> over <a href=\"https://wikipedia.org/wiki/%C5%BBabbar\" title=\"Żabbar\">Żabbar</a>, <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> after an aborted landing, killing five crew members and one person on the ground.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/RAF\" class=\"mw-redirect\" title=\"RAF\">RAF</a> <a href=\"https://wikipedia.org/wiki/Avro_Vulcan\" title=\"Avro Vulcan\">Avro Vulcan</a> bomber <a href=\"https://wikipedia.org/wiki/1975_%C5%BBabbar_Avro_Vulcan_crash\" title=\"1975 Żabbar Avro Vulcan crash\">explodes and crashes</a> over <a href=\"https://wikipedia.org/wiki/%C5%BBabbar\" title=\"Żabbar\">Żabbar</a>, <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> after an aborted landing, killing five crew members and one person on the ground.", "links": [{"title": "RAF", "link": "https://wikipedia.org/wiki/RAF"}, {"title": "Avro Vulcan", "link": "https://wikipedia.org/wiki/Avro_Vulcan"}, {"title": "1975 Żabbar Avro Vulcan crash", "link": "https://wikipedia.org/wiki/1975_%C5%BBabbar_Avro_Vulcan_crash"}, {"title": "Żabbar", "link": "https://wikipedia.org/wiki/%C5%BBabbar"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}]}, {"year": "1979", "text": "The first National March on Washington for Lesbian and Gay Rights draws approximately 100,000 people.", "html": "1979 - The first <a href=\"https://wikipedia.org/wiki/National_March_on_Washington_for_Lesbian_and_Gay_Rights\" title=\"National March on Washington for Lesbian and Gay Rights\">National March on Washington for Lesbian and Gay Rights</a> draws approximately 100,000 people.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/National_March_on_Washington_for_Lesbian_and_Gay_Rights\" title=\"National March on Washington for Lesbian and Gay Rights\">National March on Washington for Lesbian and Gay Rights</a> draws approximately 100,000 people.", "links": [{"title": "National March on Washington for Lesbian and Gay Rights", "link": "https://wikipedia.org/wiki/National_March_on_Washington_for_Lesbian_and_Gay_Rights"}]}, {"year": "1980", "text": "The 6th Congress of the Workers' Party ended, having anointed North Korean President <PERSON>'s son <PERSON> as his successor.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/6th_Congress_of_the_Workers%27_Party_of_Korea\" title=\"6th Congress of the Workers' Party of Korea\">6th Congress of the Workers' Party</a> ended, having anointed North Korean President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as his successor.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/6th_Congress_of_the_Workers%27_Party_of_Korea\" title=\"6th Congress of the Workers' Party of Korea\">6th Congress of the Workers' Party</a> ended, having anointed North Korean President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as his successor.", "links": [{"title": "6th Congress of the Workers' Party of Korea", "link": "https://wikipedia.org/wiki/6th_Congress_of_the_Workers%27_Party_of_Korea"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "Vice President <PERSON><PERSON><PERSON> is elected as the President of Egypt, one week after the assassination of <PERSON><PERSON>.", "html": "1981 - Vice President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected as the <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a>, one week after the <a href=\"https://wikipedia.org/wiki/Assassination_of_Anwar_Sadat\" title=\"Assassination of Anwar Sadat\">assassination of <PERSON><PERSON></a>.", "no_year_html": "Vice President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected as the <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a>, one week after the <a href=\"https://wikipedia.org/wiki/Assassination_of_Anwar_Sadat\" title=\"Assassination of Anwar Sadat\">assassination of <PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>k"}, {"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}, {"title": "Assassination of Anwar Sadat", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_Sadat"}]}, {"year": "1982", "text": "U.S. President <PERSON> proclaims a War on Drugs.", "html": "1982 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> proclaims a <a href=\"https://wikipedia.org/wiki/War_on_Drugs\" class=\"mw-redirect\" title=\"War on Drugs\">War on Drugs</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> proclaims a <a href=\"https://wikipedia.org/wiki/War_on_Drugs\" class=\"mw-redirect\" title=\"War on Drugs\">War on Drugs</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "War on Drugs", "link": "https://wikipedia.org/wiki/War_on_Drugs"}]}, {"year": "1991", "text": "Burmese opposition leader <PERSON><PERSON> is awarded the Nobel Peace Prize.", "html": "1991 - Burmese opposition leader <a href=\"https://wikipedia.org/wiki/Aung_San_<PERSON>_<PERSON>\" title=\"Aung San Suu K<PERSON>\"><PERSON><PERSON></a> is awarded the Nobel Peace Prize.", "no_year_html": "Burmese opposition leader <a href=\"https://wikipedia.org/wiki/Au<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Aung San Suu Kyi\"><PERSON>ng San Su<PERSON></a> is awarded the Nobel Peace Prize.", "links": [{"title": "Aung San Suu Kyi", "link": "https://wikipedia.org/wiki/Aung_San_Suu_K<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> receive the Nobel Peace Prize for their role in the establishment of the Oslo Accords and the framing of future Palestinian self government.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> receive the Nobel Peace Prize for their role in the establishment of the <a href=\"https://wikipedia.org/wiki/Oslo_Accords\" title=\"Oslo Accords\">Oslo Accords</a> and the framing of future Palestinian self government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> receive the Nobel Peace Prize for their role in the establishment of the <a href=\"https://wikipedia.org/wiki/Oslo_Accords\" title=\"Oslo Accords\">Oslo Accords</a> and the framing of future Palestinian self government.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>es"}, {"title": "Oslo Accords", "link": "https://wikipedia.org/wiki/Oslo_Accords"}]}, {"year": "1998", "text": "<PERSON> is charged with six bombings, including the 1996 Centennial Olympic Park bombing in Atlanta, Georgia.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is charged with six bombings, including the 1996 <a href=\"https://wikipedia.org/wiki/Centennial_Olympic_Park_bombing\" title=\"Centennial Olympic Park bombing\">Centennial Olympic Park bombing</a> in Atlanta, Georgia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is charged with six bombings, including the 1996 <a href=\"https://wikipedia.org/wiki/Centennial_Olympic_Park_bombing\" title=\"Centennial Olympic Park bombing\">Centennial Olympic Park bombing</a> in Atlanta, Georgia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Centennial Olympic Park bombing", "link": "https://wikipedia.org/wiki/Centennial_Olympic_Park_bombing"}]}, {"year": "2003", "text": "The <PERSON> Incident takes place at Wrigley Field in Chicago, Illinois.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Incident\" class=\"mw-redirect\" title=\"Steve <PERSON> Incident\"><PERSON> Incident</a> takes place at <a href=\"https://wikipedia.org/wiki/Wrigley_Field\" title=\"Wrigley Field\">Wrigley Field</a> in Chicago, Illinois.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Incident\" class=\"mw-redirect\" title=\"Steve <PERSON> Incident\"><PERSON> Incident</a> takes place at <a href=\"https://wikipedia.org/wiki/Wrigley_Field\" title=\"Wrigley Field\">Wrigley Field</a> in Chicago, Illinois.", "links": [{"title": "<PERSON> Incident", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Wrigley Field", "link": "https://wikipedia.org/wiki/Wrigley_Field"}]}, {"year": "2004", "text": "MK Airlines Flight 1602 crashes during takeoff from Halifax Stanfield International Airport, killing all seven people on board.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/MK_Airlines_Flight_1602\" title=\"MK Airlines Flight 1602\">MK Airlines Flight 1602</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Halifax_Stanfield_International_Airport\" title=\"Halifax Stanfield International Airport\">Halifax Stanfield International Airport</a>, killing all seven people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/MK_Airlines_Flight_1602\" title=\"MK Airlines Flight 1602\">MK Airlines Flight 1602</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Halifax_Stanfield_International_Airport\" title=\"Halifax Stanfield International Airport\">Halifax Stanfield International Airport</a>, killing all seven people on board.", "links": [{"title": "MK Airlines Flight 1602", "link": "https://wikipedia.org/wiki/MK_Airlines_Flight_1602"}, {"title": "Halifax Stanfield International Airport", "link": "https://wikipedia.org/wiki/Halifax_Stanfield_International_Airport"}]}, {"year": "2004", "text": "Pinnacle Airlines Flight 3701 crashes in Jefferson City, Missouri. The two pilots (the aircraft's only occupants) are killed.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Pinnacle_Airlines_Flight_3701\" title=\"Pinnacle Airlines Flight 3701\">Pinnacle Airlines Flight 3701</a> crashes in <a href=\"https://wikipedia.org/wiki/Jefferson_City,_Missouri\" title=\"Jefferson City, Missouri\">Jefferson City, Missouri</a>. The two pilots (the aircraft's only occupants) are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pinnacle_Airlines_Flight_3701\" title=\"Pinnacle Airlines Flight 3701\">Pinnacle Airlines Flight 3701</a> crashes in <a href=\"https://wikipedia.org/wiki/Jefferson_City,_Missouri\" title=\"Jefferson City, Missouri\">Jefferson City, Missouri</a>. The two pilots (the aircraft's only occupants) are killed.", "links": [{"title": "Pinnacle Airlines Flight 3701", "link": "https://wikipedia.org/wiki/Pinnacle_Airlines_Flight_3701"}, {"title": "Jefferson City, Missouri", "link": "https://wikipedia.org/wiki/Jefferson_City,_Missouri"}]}, {"year": "2012", "text": "<PERSON> successfully jumps to Earth from a balloon in the stratosphere.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> successfully jumps to Earth from a balloon in the stratosphere.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> successfully jumps to Earth from a balloon in the stratosphere.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "A snowstorm and avalanche in the Nepalese Himalayas triggered by the remnants of Cyclone <PERSON><PERSON><PERSON> kills 43 people.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/2014_Nepal_snowstorm_disaster\" title=\"2014 Nepal snowstorm disaster\">snowstorm</a> and avalanche in the Nepalese Himalayas triggered by the remnants of <a href=\"https://wikipedia.org/wiki/Cyclone_Hudhud\" title=\"Cyclone Hudhud\">Cyclone <PERSON></a> kills 43 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2014_Nepal_snowstorm_disaster\" title=\"2014 Nepal snowstorm disaster\">snowstorm</a> and avalanche in the Nepalese Himalayas triggered by the remnants of <a href=\"https://wikipedia.org/wiki/Cyclone_Hudhud\" title=\"Cyclone Hudhud\">Cyclone <PERSON></a> kills 43 people.", "links": [{"title": "2014 Nepal snowstorm disaster", "link": "https://wikipedia.org/wiki/2014_Nepal_snowstorm_disaster"}, {"title": "Cyclone <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ud"}]}, {"year": "2014", "text": "The Serbia vs. Albania UEFA qualifying match is canceled after 42 minutes due to several incidents on and off the pitch. Albania is eventually awarded a win.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/Serbia_v_Albania_(UEFA_Euro_2016_qualifying)\" title=\"Serbia v Albania (UEFA Euro 2016 qualifying)\">Serbia vs. Albania UEFA qualifying match</a> is canceled after 42 minutes due to several incidents on and off the pitch. Albania is eventually awarded a win.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Serbia_v_Albania_(UEFA_Euro_2016_qualifying)\" title=\"Serbia v Albania (UEFA Euro 2016 qualifying)\">Serbia vs. Albania UEFA qualifying match</a> is canceled after 42 minutes due to several incidents on and off the pitch. Albania is eventually awarded a win.", "links": [{"title": "Serbia v Albania (UEFA Euro 2016 qualifying)", "link": "https://wikipedia.org/wiki/Serbia_v_Albania_(UEFA_Euro_2016_qualifying)"}]}, {"year": "2015", "text": "A suicide bomb attack in Pakistan kills at least seven people and injures 13 others.", "html": "2015 - A <a href=\"https://wikipedia.org/wiki/<PERSON>nsa_<PERSON>_bombing\" title=\"Taunsa Sharif bombing\">suicide bomb</a> attack in Pakistan kills at least seven people and injures 13 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Taunsa_<PERSON>_bombing\" title=\"Taunsa Sharif bombing\">suicide bomb</a> attack in Pakistan kills at least seven people and injures 13 others.", "links": [{"title": "<PERSON><PERSON><PERSON> bombing", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_bombing"}]}, {"year": "2017", "text": "A massive truck bombing in Somalia kills 358 people and injures more than 400 others.", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/14_October_2017_Mogadishu_bombings\" title=\"14 October 2017 Mogadishu bombings\">massive truck bombing</a> in Somalia kills 358 people and injures more than 400 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/14_October_2017_Mogadishu_bombings\" title=\"14 October 2017 Mogadishu bombings\">massive truck bombing</a> in Somalia kills 358 people and injures more than 400 others.", "links": [{"title": "14 October 2017 Mogadishu bombings", "link": "https://wikipedia.org/wiki/14_October_2017_Mogadishu_bombings"}]}, {"year": "2021", "text": "About 10,000 American employees of <PERSON> go on strike.", "html": "2021 - About 10,000 American employees of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/2021_<PERSON>_<PERSON>_strike\" title=\"2021 <PERSON> strike\">go on strike</a>.", "no_year_html": "About 10,000 American employees of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/2021_<PERSON>_<PERSON>_strike\" title=\"2021 <PERSON> strike\">go on strike</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2021 <PERSON> strike", "link": "https://wikipedia.org/wiki/2021_<PERSON>_<PERSON>_strike"}]}, {"year": "2023", "text": "Australians vote to reject a constitutional amendment that would have established an Indigenous Voice to Parliament.", "html": "2023 - Australians <a href=\"https://wikipedia.org/wiki/2023_Australian_Indigenous_Voice_referendum\" title=\"2023 Australian Indigenous Voice referendum\">vote to reject a constitutional amendment</a> that would have established an <a href=\"https://wikipedia.org/wiki/Indigenous_Voice_to_Parliament\" title=\"Indigenous Voice to Parliament\">Indigenous Voice to Parliament</a>.", "no_year_html": "Australians <a href=\"https://wikipedia.org/wiki/2023_Australian_Indigenous_Voice_referendum\" title=\"2023 Australian Indigenous Voice referendum\">vote to reject a constitutional amendment</a> that would have established an <a href=\"https://wikipedia.org/wiki/Indigenous_Voice_to_Parliament\" title=\"Indigenous Voice to Parliament\">Indigenous Voice to Parliament</a>.", "links": [{"title": "2023 Australian Indigenous Voice referendum", "link": "https://wikipedia.org/wiki/2023_Australian_Indigenous_Voice_referendum"}, {"title": "Indigenous Voice to Parliament", "link": "https://wikipedia.org/wiki/Indigenous_Voice_to_Parliament"}]}], "Births": [{"year": "1257", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> II of Poland (d. 1296)", "html": "1257 - <a href=\"https://wikipedia.org/wiki/Przemys%C5%82_II\" title=\"Przemysł II\">Przemysł II</a> of Poland (d. 1296)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Przemys%C5%82_II\" title=\"Przemysł II\">Przemysł II</a> of Poland (d. 1296)", "links": [{"title": "Przemysł II", "link": "https://wikipedia.org/wiki/Przemys%C5%82_II"}]}, {"year": "1404", "text": "<PERSON> Anjou (d. 1463)", "html": "1404 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (d. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (d. 1463)", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1425", "text": "<PERSON><PERSON><PERSON>, Italian painter (d. 1499)", "html": "1425 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1499)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1465", "text": "<PERSON>, German humanist and antiquarian (d. 1547)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and antiquarian (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and antiquarian (d. 1547)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1493", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (d. 1568)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1568)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1542", "text": "<PERSON>, Count of Nassau-Weilburg (d. 1602)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Weilburg\" title=\"<PERSON>, Count of Nassau-Weilburg\"><PERSON>, Count of Nassau-Weilburg</a> (d. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Weilburg\" title=\"<PERSON>, Count of Nassau-Weilburg\"><PERSON>, Count of Nassau-Weilburg</a> (d. 1602)", "links": [{"title": "<PERSON>, Count of Nassau-Weilburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Weilburg"}]}, {"year": "1563", "text": "<PERSON><PERSON><PERSON>, Flemish engraver and cartographer (d. 1611)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish engraver and cartographer (d. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Jodo<PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish engraver and cartographer (d. 1611)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jodocus_Hondius"}]}, {"year": "1569", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian poet (d. 1625)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet (d. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet (d. 1625)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1609", "text": "<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg (d. 1689)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg\"><PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg</a> (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg\"><PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg</a> (d. 1689)", "links": [{"title": "<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg"}]}, {"year": "1630", "text": "<PERSON> of Hanover (d. 1714)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/Sophia_of_Hanover\" title=\"Sophia of Hanover\">Sophia of Hanover</a> (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sophia_of_Hanover\" title=\"Sophia of Hanover\"><PERSON> of Hanover</a> (d. 1714)", "links": [{"title": "Sophia of Hanover", "link": "https://wikipedia.org/wiki/Sophia_of_Hanover"}]}, {"year": "1633", "text": "<PERSON> of England (d. 1701)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"James II of England\"><PERSON> II of England</a> (d. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"James II of England\"><PERSON> of England</a> (d. 1701)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1639", "text": "<PERSON>, Dutch commander and politician, 1st Governor of the Dutch Cape Colony (d. 1712)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch commander and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_the_Dutch_Cape_Colony\" class=\"mw-redirect\" title=\"Governor of the Dutch Cape Colony\">Governor of the Dutch Cape Colony</a> (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch commander and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_the_Dutch_Cape_Colony\" class=\"mw-redirect\" title=\"Governor of the Dutch Cape Colony\">Governor of the Dutch Cape Colony</a> (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of the Dutch Cape Colony", "link": "https://wikipedia.org/wiki/Governor_of_the_Dutch_Cape_Colony"}]}, {"year": "1643", "text": "<PERSON>, Mughal emperor (d. 1712)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Bahadur <PERSON>\"><PERSON></a>, Mughal emperor (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bahadur_<PERSON>_<PERSON>\" title=\"Bahadur <PERSON>\"><PERSON></a>, Mughal emperor (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1644", "text": "<PERSON>, English businessman who founded Pennsylvania (d. 1718)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Penn\"><PERSON></a>, English businessman who founded <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> (d. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Penn\" title=\"William Penn\"><PERSON></a>, English businessman who founded <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> (d. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}]}, {"year": "1687", "text": "<PERSON>, Scottish mathematician and academic (d. 1768)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician and academic (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician and academic (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, English lawyer and politician, Prime Minister of Great Britain (d. 1770)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1726", "text": "<PERSON>, 1st Baron <PERSON>, Scottish-English admiral and politician (d. 1813)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron Barham\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English admiral and politician (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron Barham\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English admiral and politician (d. 1813)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1733", "text": "<PERSON>, Count of Clerfayt, Austrian field marshal (d. 1798)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Clerfayt\" title=\"<PERSON>, Count of Clerfayt\"><PERSON>, Count of Clerfayt</a>, Austrian field marshal (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Clerfayt\" title=\"<PERSON>, Count of Clerfayt\"><PERSON>, Count of Clerfayt</a>, Austrian field marshal (d. 1798)", "links": [{"title": "<PERSON>, Count of Clerfayt", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1784", "text": "<PERSON> of Spain (d. 1833)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/Ferdinand_VII_of_Spain\" class=\"mw-redirect\" title=\"Ferdinand VII of Spain\">Ferdinand VII of Spain</a> (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferdinand_VII_of_Spain\" class=\"mw-redirect\" title=\"Ferdinand VII of Spain\">Ferdinand VII of Spain</a> (d. 1833)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Ferdinand_VII_of_Spain"}]}, {"year": "1791", "text": "<PERSON>, Baltic German naturalist (d. 1841)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Baltic German naturalist (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Baltic German naturalist (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Belgian physicist and academic, created the <PERSON><PERSON><PERSON><PERSON><PERSON> (d. 1883)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joseph Plateau\"><PERSON></a>, Belgian physicist and academic, created the <a href=\"https://wikipedia.org/wiki/Phenakistoscope\" class=\"mw-redirect\" title=\"Phenakistoscope\">Phenakistoscope</a> (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joseph<PERSON>\" title=\"Joseph Plateau\"><PERSON></a>, Belgian physicist and academic, created the <a href=\"https://wikipedia.org/wiki/Phenakistoscope\" class=\"mw-redirect\" title=\"Phenakistoscope\">Phenakistoscope</a> (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Phenakistoscope", "link": "https://wikipedia.org/wiki/Phenakistoscope"}]}, {"year": "1806", "text": "<PERSON>, American lawyer and politician (d. 1865)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (d. 1865)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1824", "text": "<PERSON><PERSON><PERSON>, French painter (d. 1886)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (d. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, Russian author and critic (d. 1868)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and critic (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and critic (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, American baseball player and manager (d. 1927)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Start\" title=\"Joe Start\"><PERSON></a>, American baseball player and manager (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Start\" title=\"Joe Start\"><PERSON></a>, American baseball player and manager (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, English-Australian politician, 14th Premier of New South Wales (d. 1907)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_See"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1845", "text": "<PERSON>, American educator and missionary (d. 1900)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and missionary (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and missionary (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Canadian banker and philanthropist (d. 1924)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian banker and philanthropist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian banker and philanthropist (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, American engineer and businessman (d. 1924)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American journalist, editor, and reformer (d. 1891)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Julia <PERSON>\"><PERSON></a>, American journalist, editor, and reformer (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, editor, and reformer (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, Japanese poet, author, and critic (d. 1902)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Masa<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>sa<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet, author, and critic (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Masa<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>sa<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet, author, and critic (d. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Masaoka_Shiki"}]}, {"year": "1869", "text": "<PERSON>, 1st Baron <PERSON>, English art dealer (d. 1939)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English art dealer (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English art dealer (d. 1939)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Austrian composer, conductor, and teacher (d. 1942)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer, conductor, and teacher (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer, conductor, and teacher (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, English tennis player (d. 1910)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, American-Irish rebel and politician, 3rd President of Ireland (d. 1975)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/%C3%89amon_de_<PERSON>ra\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Irish rebel and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89amon_de_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Irish rebel and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89amon_<PERSON>_<PERSON>ra"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "1882", "text": "<PERSON>, English cricketer, coach, and umpire (d. 1959)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer, coach, and umpire (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer, coach, and umpire (d. 1959)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1888", "text": "<PERSON>, New Zealand novelist, short story writer, and essayist (d. 1923)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand novelist, short story writer, and essayist (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand novelist, short story writer, and essayist (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Japanese businessman and politician, 27th Japanese Minister of Finance (d. 1947)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Japan)\" title=\"Minister of Finance (Japan)\">Japanese Minister of Finance</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Japan)\" title=\"Minister of Finance (Japan)\">Japanese Minister of Finance</a> (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Finance (Japan)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Japan)"}]}, {"year": "1890", "text": "<PERSON>, American general and politician, 34th President of the United States (d. 1969)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 34th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 34th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1892", "text": "<PERSON>, American politician and diplomat, 11th Under Secretary of State (d. 1961)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 11th <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_State\" class=\"mw-redirect\" title=\"Under Secretary of State\">Under Secretary of State</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 11th <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_State\" class=\"mw-redirect\" title=\"Under Secretary of State\">Under Secretary of State</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Under Secretary of State", "link": "https://wikipedia.org/wiki/Under_Secretary_of_State"}]}, {"year": "1893", "text": "<PERSON>, American author and illustrator (d. 1974)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American actress (d. 1993)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON> <PERSON><PERSON>, American poet and playwright (d. 1962)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet and playwright (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet and playwright (d. 1962)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, British marine engineer (d. 1978)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Victoria_Drummond\" title=\"<PERSON>\"><PERSON></a>, British marine engineer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Drummond\" title=\"Victoria <PERSON>\"><PERSON></a>, British marine engineer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Drummond"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Algerian anarchist and Spanish Civil War veteran (d. 1953)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian anarchist and <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a> veteran (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian anarchist and <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a> veteran (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Polish chemist (d. 1975)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish chemist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish chemist (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Canadian sergeant and pilot, Victoria Cross recipient (d. 1950)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sergeant and pilot, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sergeant and pilot, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American statistician, author, and academic (d. 1993)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American statistician, author, and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American statistician, author, and academic (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Italian cyclist and manager (d. 1963)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Learc<PERSON>_G<PERSON>\" title=\"Learco G<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist and manager (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Learco_G<PERSON>\" title=\"Learco G<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist and manager (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Learco_Guerra"}]}, {"year": "1902", "text": "<PERSON>, Australian rugby league player, coach, and administrator (d. 1977)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and administrator (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and administrator (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Justice"}]}, {"year": "1904", "text": "<PERSON>, French politician, French Minister of Foreign Affairs (d. 1995)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">French Minister of Foreign Affairs</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">French Minister of Foreign Affairs</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign and European Affairs (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)"}]}, {"year": "1904", "text": "<PERSON>, Soviet politician, First Deputy Premier of the Soviet Union (d. 1978)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician, <a href=\"https://wikipedia.org/wiki/First_Deputy_Premier_of_the_Soviet_Union\" title=\"First Deputy Premier of the Soviet Union\">First Deputy Premier of the Soviet Union</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician, <a href=\"https://wikipedia.org/wiki/First_Deputy_Premier_of_the_Soviet_Union\" title=\"First Deputy Premier of the Soviet Union\">First Deputy Premier of the Soviet Union</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Deputy Premier of the Soviet Union", "link": "https://wikipedia.org/wiki/First_Deputy_Premier_of_the_Soviet_Union"}]}, {"year": "1906", "text": "<PERSON>, Egyptian religious leader, founded the Muslim Brotherhood (d. 1949)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Muslim_Brotherhood\" title=\"Muslim Brotherhood\">Muslim Brotherhood</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Muslim_Brotherhood\" title=\"Muslim Brotherhood\">Muslim Brotherhood</a> (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Muslim Brotherhood", "link": "https://wikipedia.org/wiki/Muslim_Brotherhood"}]}, {"year": "1906", "text": "<PERSON>, German-American philosopher and theorist (d. 1975)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American philosopher and theorist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American philosopher and theorist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American actor and singer (d. 1992)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (d. 1992)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese commander (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese commander (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese commander (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American screenwriter and producer (d. 1997)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, German racing driver (d. 1938)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German racing driver (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German racing driver (d. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American basketball player and coach (d. 2010)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Vietnamese general and politician, Nobel Prize laureate (d. 1990)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/L%C3%AA_%C4%90%E1%BB%A9c_Th%E1%BB%8D\" title=\"Lê Đ<PERSON>\"><PERSON><PERSON></a>, Vietnamese general and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%AA_%C4%90%E1%BB%A9c_Th%E1%BB%8D\" title=\"Lê Đ<PERSON>họ\"><PERSON><PERSON></a>, Vietnamese general and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%AA_%C4%90%E1%BB%A9c_Th%E1%BB%8D"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1914", "text": "<PERSON>, American baseball player and coach (d. 2004)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American chemist and physicist, Nobel Prize laureate (d. 2006)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2006)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1914", "text": "<PERSON>, Estonian poet and critic (d. 1985)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet and critic (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet and critic (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Italian cardinal (d. 2016)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cardinal (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cardinal (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American admiral and surgeon, 13th United States Surgeon General (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American admiral and surgeon, 13th <a href=\"https://wikipedia.org/wiki/United_States_Surgeon_General\" class=\"mw-redirect\" title=\"United States Surgeon General\">United States Surgeon General</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American admiral and surgeon, 13th <a href=\"https://wikipedia.org/wiki/United_States_Surgeon_General\" class=\"mw-redirect\" title=\"United States Surgeon General\">United States Surgeon General</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Surgeon General", "link": "https://wikipedia.org/wiki/United_States_Surgeon_General"}]}, {"year": "1918", "text": "<PERSON>, Canadian biochemist, journalist, and politician (d. 1991)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian biochemist, journalist, and politician (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian biochemist, journalist, and politician (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Australian tennis player and captain (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player and captain (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player and captain (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Australian cricketer and sportscaster (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Chilean journalist and historian (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Arra%C3%B1o_Acevedo\" title=\"<PERSON>\"><PERSON></a>, Chilean journalist and historian (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Arra%C3%B1o_Acevedo\" title=\"<PERSON>\"><PERSON></a>, Chilean journalist and historian (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Arra%C3%B1o_Acevedo"}]}, {"year": "1923", "text": "<PERSON>, English accountant and politician, Chief Secretary to the Treasury (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_to_the_Treasury\" title=\"Chief Secretary to the Treasury\">Chief Secretary to the Treasury</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_to_the_Treasury\" title=\"Chief Secretary to the Treasury\">Chief Secretary to the Treasury</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Secretary to the Treasury", "link": "https://wikipedia.org/wiki/Chief_Secretary_to_the_Treasury"}]}, {"year": "1926", "text": "<PERSON>, Dutch singer and actor (d. 1985)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer and actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer and actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actor and producer (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actress and singer (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American chemist and businessman (d. 1995)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and businessman (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and businessman (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>nik"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Canadian boxer and wrestler (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian boxer and wrestler (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian boxer and wrestler (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American singer and saxophonist (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and saxophonist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and saxophonist (d. 2020)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Congolese soldier and politician, President of Zaire (d. 1997)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Zaire\" class=\"mw-redirect\" title=\"President of Zaire\">President of Zaire</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Zaire\" class=\"mw-redirect\" title=\"President of Zaire\">President of Zaire</a> (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Zaire", "link": "https://wikipedia.org/wiki/President_of_Zaire"}]}, {"year": "1930", "text": "<PERSON>, Welsh journalist and politician, Shadow Secretary of State for Wales (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Swansea_West_MP)\" title=\"<PERSON> (Swansea West MP)\"><PERSON></a>, Welsh journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Swansea_West_MP)\" title=\"<PERSON> (Swansea West MP)\"><PERSON></a>, Welsh journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a> (d. 2014)", "links": [{"title": "<PERSON> (Swansea West MP)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Swansea_West_MP)"}, {"title": "Shadow Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales"}]}, {"year": "1932", "text": "<PERSON>, American tenor and actor (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Russian-American physicist and academic (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American physicist and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American physicist and academic (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Dutch footballer and manager (d. 2017)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager (d. 2017)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Swiss psychotherapist and author (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss psychotherapist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss psychotherapist and author (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON>rg_Schu<PERSON>ger"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and author, 13th White House Counsel", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author, 13th <a href=\"https://wikipedia.org/wiki/White_House_Counsel\" title=\"White House Counsel\">White House Counsel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author, 13th <a href=\"https://wikipedia.org/wiki/White_House_Counsel\" title=\"White House Counsel\">White House Counsel</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Counsel", "link": "https://wikipedia.org/wiki/White_House_Counsel"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, English curator and academic (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English curator and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English curator and academic (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elizabeth_<PERSON>-Coll"}]}, {"year": "1938", "text": "<PERSON>, American-Canadian football player and coach (d. 2008)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, South African historian and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African historian and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American country music singer (d. 2025)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country music singer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country music singer (d. 2025)", "links": [{"title": "<PERSON><PERSON> Montgomery", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Montgomery"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Empress of Iran", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Empress of Iran", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Empress of Iran", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American fashion designer, founded the Ralph Lauren Corporation", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lauren\"><PERSON></a>, American fashion designer, founded the <a href=\"https://wikipedia.org/wiki/Ralph_<PERSON>_Corporation\" title=\"Ralph Lauren Corporation\">Ralph Lauren Corporation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lauren\" title=\"<PERSON> Lauren\"><PERSON></a>, American fashion designer, founded the <a href=\"https://wikipedia.org/wiki/Ralph_Lauren_Corporation\" title=\"Ralph Lauren Corporation\">Ralph Lauren Corporation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ralph Lauren Corporation", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Corporation"}]}, {"year": "1939", "text": "<PERSON>, American golfer and politician (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer and politician (d. 2021)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, South African snooker player (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mans\" title=\"Perrie Mans\"><PERSON><PERSON></a>, South African snooker player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Perrie Mans\"><PERSON><PERSON></a>, South African snooker player (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Indian-English singer-songwriter and actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richard\"><PERSON></a>, Indian-English singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richard\"><PERSON></a>, Indian-English singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON><PERSON>, American golfer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>nead\" title=\"<PERSON><PERSON> <PERSON><PERSON> Snead\"><PERSON><PERSON> <PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_Snead\" title=\"J. <PERSON><PERSON> Snead\"><PERSON><PERSON> <PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>d"}]}, {"year": "1940", "text": "<PERSON>, Welsh actor, director, and screenwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American football player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Irish sportsman", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish sportsman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish sportsman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian rugby player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>sky"}]}, {"year": "1941", "text": "<PERSON>, English tennis player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1942", "text": "<PERSON>, English rugby player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Filipino lawyer and politician (d. 1986)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Hungarian author and playwright", "html": "1942 - <a href=\"https://wikipedia.org/wiki/P%C3%A9ter_N%C3%A1das\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9ter_N%C3%A1das\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author and playwright", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_N%C3%A1das"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Indonesian actress (d. 2008)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>zza<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian actress (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suzzanna"}]}, {"year": "1943", "text": "<PERSON>, Iranian scholar and politician, 5th President of Iran", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian scholar and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian scholar and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Iran", "link": "https://wikipedia.org/wiki/President_of_Iran"}]}, {"year": "1944", "text": "<PERSON><PERSON>, German-American actor and director", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actor and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1945", "text": "<PERSON>, English bass player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Dutch author and illustrator", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1945", "text": "<PERSON>, English actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Gabonese general and politician, President of the Central African Republic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Boziz%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Gabonese general and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"President of the Central African Republic\">President of the Central African Republic</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Boziz%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Gabonese general and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"President of the Central African Republic\">President of the Central African Republic</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Boziz%C3%A9"}, {"title": "President of the Central African Republic", "link": "https://wikipedia.org/wiki/President_of_the_Central_African_Republic"}]}, {"year": "1946", "text": "<PERSON>, Filipino comedian, actor and television host", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino comedian, actor and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino comedian, actor and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish singer-songwriter (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American baseball player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American biologist, geneticist, and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American guitarist, songwriter, and producer (d. 1987)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, songwriter, and producer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, songwriter, and producer (d. 1987)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON>, American football player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Croatian-American wrestler (d. 2018)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-American wrestler (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-American wrestler (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Jamaican-English singer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American political scientist and scholar", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Hong Kong actor, director, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American poet and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>itt"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player and referee", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and referee", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1950", "text": "<PERSON>, American actor, director, and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>volta\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Travolta\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Dutch cyclist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_van_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor and screenwriter (d. 2018)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Russian gymnast and coach (d. 2011)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and coach (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American comedian and actor (d. 1995)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Japanese guitarist and composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese guitarist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Moroccan-Israeli technician and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Mo<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Morde<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan-Israeli technician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Morde<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan-Israeli technician and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, English curator and critic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English curator and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English curator and critic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American actress, producer, and screenwriter (d. 2023)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, producer, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, producer, and screenwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American automotive designer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American automotive designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American automotive designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Turkish singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/%C3%9Cmit_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Cmit_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%9Cmit_<PERSON>sen"}]}, {"year": "1956", "text": "<PERSON>, American golfer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American game designer (d. 2024)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Jennell_Jaquays\" title=\"Jennell Jaquays\"><PERSON><PERSON>aq<PERSON></a>, American game designer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jennell_Jaquays\" title=\"Jennell Jaquays\"><PERSON><PERSON></a>, American game designer (d. 2024)", "links": [{"title": "<PERSON>nell J<PERSON>s", "link": "https://wikipedia.org/wiki/Jennell_Jaquays"}]}, {"year": "1957", "text": "<PERSON>, Canadian lawyer and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9s"}]}, {"year": "1957", "text": "<PERSON>, Japanese lawyer and politician, 13th Japanese Minister of Defense", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Minister_of_Defense_(Japan)\" title=\"Minister of Defense (Japan)\">Japanese Minister of Defense</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Minister_of_Defense_(Japan)\" title=\"Minister of Defense (Japan)\">Japanese Minister of Defense</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defense (Japan)", "link": "https://wikipedia.org/wiki/Minister_of_Defense_(Japan)"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Russian ice hockey player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON> <PERSON><PERSON>, American drummer (d. 2015)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American drummer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"A<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American drummer (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English runner and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>bigniew_Kruszy%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_K<PERSON>zy%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>bigniew_Kruszy%C5%84ski"}]}, {"year": "1961", "text": "<PERSON>, American fashion designer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Estonian chess player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English-American actor (d. 2003)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter, guitarist, producer, and actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Israeli tennis player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English actor, comedian, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Estonian rower and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian rower and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian rower and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Greek-Australian flute player and composer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Australian flute player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Australian flute player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, British racing driver", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American sports television personality", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian guitarist and songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_musician)\" title=\"<PERSON> (Canadian musician)\"><PERSON></a>, Canadian guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_musician)\" title=\"<PERSON> (Canadian musician)\"><PERSON></a>, Canadian guitarist and songwriter", "links": [{"title": "<PERSON> (Canadian musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_musician)"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter, guitarist, producer, and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English footballer and journalist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English footballer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English footballer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American basketball player and coach (d. 2012)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Russian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor (d. 1999)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Czech footballer and coach (d. 2013)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_Barbari%C4%8D"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Japanese actress and singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gasaku"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Bulgarian violinist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Vassile<PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ile<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ask<PERSON> Vassile<PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian violinist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Swedish footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/P%C3%A4r_Zetterberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A4r_Zette<PERSON>g\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A4r_Zetterberg"}]}, {"year": "1971", "text": "<PERSON>, Portuguese footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1971", "text": "<PERSON>, Filipino basketball player and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Filipino basketball player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Filipino basketball player and politician", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1972", "text": "<PERSON><PERSON>, American tennis player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julian_O%27Neill"}]}, {"year": "1973", "text": "<PERSON>, American-British political philosopher and legal scholar", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British political philosopher and legal scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British political philosopher and legal scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American police brutality victim (d. 2020)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police brutality victim (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police brutality victim (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Georgian businessman and politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Lasha_Zhvania\" title=\"Lasha Zhvania\"><PERSON><PERSON></a>, Georgian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lasha_Zhvania\" title=\"Lasha Zhvania\"><PERSON><PERSON></a>, Georgian businessman and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lasha_Zhvania"}]}, {"year": "1974", "text": "<PERSON>, American porn actress and director", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American porn actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American porn actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/T%C3%BCmer_Metin\" title=\"Tü<PERSON>in\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%BCmer_Metin\" title=\"Tü<PERSON> Metin\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%BCmer_Metin"}]}, {"year": "1974", "text": "<PERSON>, Swiss runner", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Viktor_R%C3%B<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Viktor_R%C3%B6thlin"}]}, {"year": "1974", "text": "<PERSON>, Brazilian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1974)\" title=\"<PERSON> (footballer, born 1974)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1974)\" title=\"<PERSON> (footballer, born 1974)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1974)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1974)"}]}, {"year": "1975", "text": "<PERSON>, English footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American cyclist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, English singer and songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English singer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, New Zealand rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Sri Lankan cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Tj%C3%A<PERSON><PERSON>q<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Tj%C3%A<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Tj%C3%A4<PERSON><PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Dutch footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American-Canadian basketball and volleyball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian basketball and volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian basketball and volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American guitarist and songwriter, and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English snooker player (d. 2006)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Czech tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jana_Macurov%C3%A1"}]}, {"year": "1978", "text": "<PERSON>, Scottish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (Scottish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Scottish_footballer)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American singer-songwriter, dancer, and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter, dancer, and actor", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1979", "text": "<PERSON>, American wrestler and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Estonian figure skater and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-G<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Estonian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-G<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Estonian figure skater and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Ecuadorian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Pa%C3%BA<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pa%C3%BA<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pa%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Danish-English cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(English_cricketer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (English cricketer)\"><PERSON><PERSON><PERSON></a>, Danish-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(English_cricketer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (English cricketer)\"><PERSON><PERSON><PERSON></a>, Danish-English cricketer", "links": [{"title": "<PERSON><PERSON><PERSON> (English cricketer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(English_cricketer)"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Danish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)\" title=\"<PERSON> (runner)\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)\" title=\"<PERSON> (runner)\"><PERSON></a>, American runner", "links": [{"title": "<PERSON> (runner)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)"}]}, {"year": "1982", "text": "<PERSON>, Dominican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rmol\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rmol\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_M%C3%A1rmol"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1983", "text": "<PERSON>, German hammer thrower", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German hammer thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Chinese badminton player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lin Dan\"><PERSON></a>, Chinese badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lin Dan\"><PERSON></a>, Chinese badminton player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/LaRon_Landry\" title=\"LaRon Landry\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LaRon_Landry\" title=\"LaRon Landry\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "LaRon Landry", "link": "https://wikipedia.org/wiki/LaRon_Landry"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1984)\" title=\"<PERSON> (footballer, born 1984)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1984)\" title=\"<PERSON> (footballer, born 1984)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1984)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1984)"}]}, {"year": "1985", "text": "<PERSON>, Brazilian racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Negr%C3%A3o"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Irish boxer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Croatian Member of Parliament", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician,_born_1985)\" title=\"<PERSON> (politician, born 1985)\"><PERSON></a>, Croatian Member of Parliament", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician,_born_1985)\" title=\"<PERSON> (politician, born 1985)\"><PERSON></a>, Croatian Member of Parliament", "links": [{"title": "<PERSON> (politician, born 1985)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician,_born_1985)"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>e\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor and comedian", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Thieriot\" title=\"<PERSON> Thieriot\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Thieriot\" title=\"Max Thieriot\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thieriot"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Venezuelan musician", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Venezuelan musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Venezuelan musician", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1990", "text": "<PERSON>, English cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1992", "text": "<PERSON>, Nigerian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1993", "text": "<PERSON>, Australian cricketer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ashton Agar\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ashton Agar\"><PERSON></a>, Australian cricketer", "links": [{"title": "Ashton Agar", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gar"}]}, {"year": "1994", "text": "<PERSON>, English rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "530", "text": "<PERSON><PERSON><PERSON>", "html": "530 - <a href=\"https://wikipedia.org/wiki/Antipope_Dioscorus\" title=\"Antipope Dioscorus\">Antipope Dioscorus</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_Dioscorus\" title=\"Antipope Dioscorus\">Antipope Dioscorus</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antipope_Dioscorus"}]}, {"year": "841", "text": "<PERSON>, Chinese governor", "html": "841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese governor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Yuan<PERSON>\"><PERSON></a>, Chinese governor", "links": [{"title": "Shi <PERSON>", "link": "https://wikipedia.org/wiki/Shi_<PERSON>hong"}]}, {"year": "869", "text": "<PERSON><PERSON>, Chinese rebel leader", "html": "869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese rebel leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese rebel leader", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pang_<PERSON>n"}]}, {"year": "962", "text": "<PERSON><PERSON><PERSON>, Frankish noblewoman", "html": "962 - <a href=\"https://wikipedia.org/wiki/Ger<PERSON>\" title=\"Ger<PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish noblewoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ger<PERSON>\" title=\"Ger<PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish noblewoman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "996", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> caliph (b. 955)", "html": "996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> caliph (b. 955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> caliph (b. 955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1066", "text": "Battle of Hastings:\n<PERSON>, English king (b. 1022)\n<PERSON><PERSON><PERSON>, English nobleman and brother of <PERSON>, English nobleman and brother of <PERSON>", "html": "1066 - <a href=\"https://wikipedia.org/wiki/Battle_of_Hastings\" title=\"Battle of Hastings\">Battle of Hastings</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English king (b. 1022)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Godwinson\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English nobleman and brother of <PERSON></li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Gyr<PERSON>_God<PERSON>son\" title=\"<PERSON>yr<PERSON> Godwinson\"><PERSON><PERSON><PERSON> God<PERSON></a>, English nobleman and brother of <PERSON></li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Hastings\" title=\"Battle of Hastings\">Battle of Hastings</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English king (b. 1022)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English nobleman and brother of <PERSON></li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Gyr<PERSON>_God<PERSON>son\" title=\"<PERSON>yr<PERSON> Godwinson\"><PERSON><PERSON><PERSON> God<PERSON></a>, English nobleman and brother of <PERSON></li>\n</ul>", "links": [{"title": "Battle of Hastings", "link": "https://wikipedia.org/wiki/Battle_of_Hastings"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leofwine_Godwinson"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "<PERSON>, English king (b. 1022)", "text": null, "html": "<PERSON>, English king (b. 1022) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English king (b. 1022)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English king (b. 1022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, English nobleman and brother of <PERSON>", "text": null, "html": "<PERSON><PERSON><PERSON>, English nobleman and brother of <PERSON> - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_God<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English nobleman and brother of <PERSON>", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English nobleman and brother of <PERSON>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>f<PERSON>_God<PERSON>son"}]}, {"year": "<PERSON><PERSON><PERSON>, English nobleman and brother of <PERSON>", "text": null, "html": "<PERSON><PERSON><PERSON>, English nobleman and brother of <PERSON> - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Godwin<PERSON>\"><PERSON><PERSON><PERSON></a>, English nobleman and brother of <PERSON>", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Godwin<PERSON>\"><PERSON><PERSON><PERSON></a>, English nobleman and brother of <PERSON>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>son"}]}, {"year": "1077", "text": "<PERSON><PERSON><PERSON>, Byzantine courtier (b. 1022)", "html": "1077 - <a href=\"https://wikipedia.org/wiki/Andron<PERSON>_<PERSON>_(cousin_of_<PERSON>_<PERSON>)\" title=\"<PERSON><PERSON><PERSON> (cousin of <PERSON>)\"><PERSON><PERSON><PERSON></a>, Byzantine courtier (b. 1022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andron<PERSON>_<PERSON>s_(cousin_of_<PERSON>_<PERSON>)\" title=\"<PERSON><PERSON><PERSON> (cousin of <PERSON>)\"><PERSON><PERSON><PERSON></a>, Byzantine courtier (b. 1022)", "links": [{"title": "<PERSON><PERSON><PERSON> (cousin of <PERSON>)", "link": "https://wikipedia.org/wiki/Andronikos_Doukas_(cousin_of_<PERSON>_<PERSON>)"}]}, {"year": "1092", "text": "<PERSON><PERSON>, Persian scholar and politician (b. 1018)", "html": "1092 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> al<PERSON>\"><PERSON><PERSON></a>, Persian scholar and politician (b. 1018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian scholar and politician (b. 1018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1184", "text": "<PERSON>, <PERSON><PERSON><PERSON> caliph (b. 1135)", "html": "1184 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, <PERSON><PERSON><PERSON> caliph (b. 1135)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, <PERSON><PERSON><PERSON> caliph (b. 1135)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1213", "text": "<PERSON>, 1st Earl of Essex, English sheriff and Chief Justiciar", "html": "1213 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Essex\" title=\"<PERSON>, 1st Earl of Essex\"><PERSON>, 1st Earl of Essex</a>, English sheriff and Chief Just<PERSON>ar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Essex\" title=\"<PERSON>, 1st Earl of Essex\"><PERSON>, 1st Earl of Essex</a>, English sheriff and Chief Just<PERSON>ar", "links": [{"title": "<PERSON>, 1st Earl of Essex", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Essex"}]}, {"year": "1217", "text": "<PERSON>, English noblewoman and wife of <PERSON> of England (b. c. 1173)", "html": "1217 - <a href=\"https://wikipedia.org/wiki/Isabella,_Countess_of_Gloucester\" title=\"<PERSON>, Countess of Gloucester\">Isabella</a>, English noblewoman and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a> (b. c. 1173)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella,_Countess_of_Gloucester\" title=\"<PERSON>, Countess of Gloucester\">Isabella</a>, English noblewoman and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a> (b. c. 1173)", "links": [{"title": "<PERSON>, Countess of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_of_Gloucester"}, {"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}]}, {"year": "1240", "text": "<PERSON><PERSON>, Only female sultan of Delhi (b. c. 1205)", "html": "1240 - <a href=\"https://wikipedia.org/wiki/Razia_Sultana\" title=\"Razia Sultana\"><PERSON><PERSON>a</a>, Only female sultan of Delhi (b. c. 1205)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON>_Sultana\" title=\"Razia Sultana\"><PERSON><PERSON>a</a>, Only female sultan of Delhi (b. c. 1205)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Razia_Sultana"}]}, {"year": "1256", "text": "<PERSON><PERSON>, Japanese shogun (b. 1239)", "html": "1256 - <a href=\"https://wikipedia.org/wiki/Kuj%C5%8D_Yoritsugu\" title=\"Kujō Yoritsugu\"><PERSON><PERSON> Yoritsug<PERSON></a>, Japanese shogun (b. 1239)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuj%C5%8D_Yoritsugu\" title=\"Kujō Yoritsugu\"><PERSON><PERSON> Yoritsugu</a>, Japanese shogun (b. 1239)", "links": [{"title": "Kujō Yoritsugu", "link": "https://wikipedia.org/wiki/Kuj%C5%8D_Yoritsugu"}]}, {"year": "1318", "text": "<PERSON>, High King of Ireland (b. 1275)", "html": "1318 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, High King of Ireland (b. 1275)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, High King of Ireland (b. 1275)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1366", "text": "<PERSON>, Arab poet (b. 1287)", "html": "1366 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arab poet (b. 1287)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arab poet (b. 1287)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1416", "text": "<PERSON>, duke of Brunswick-Lüneburg", "html": "1416 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON> the <PERSON>, Duke of Brunswick-Lüneburg\"><PERSON> the <PERSON></a>, duke of Brunswick-Lüneburg", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON> the <PERSON>, Duke of Brunswick-Lüneburg\"><PERSON> the <PERSON></a>, duke of Brunswick-Lüneburg", "links": [{"title": "<PERSON>, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1536", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Spanish poet (b. 1503)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Vega_(poet)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Spanish poet (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Vega_(poet)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Spanish poet (b. 1503)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_(poet)"}]}, {"year": "1552", "text": "<PERSON>, Swiss theologian and reformer (b. 1488)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and reformer (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and reformer (b. 1488)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON>, English poet and politician (b. 1521)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, English poet and politician (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, English poet and politician (b. 1521)", "links": [{"title": "<PERSON> (statesman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(statesman)"}]}, {"year": "1568", "text": "<PERSON>, Dutch singer and composer (b. 1507)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer and composer (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer and composer (b. 1507)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON><PERSON>, Japanese daimyō (b. 1540)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>ago_Yoshihisa\" title=\"<PERSON>ago Yoshihisa\"><PERSON><PERSON></a>, Japanese daimyō (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ago_Yoshi<PERSON>a\" title=\"<PERSON>ago Yoshihisa\"><PERSON><PERSON></a>, Japanese daimyō (b. 1540)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ago_<PERSON><PERSON>a"}]}, {"year": "1618", "text": "<PERSON><PERSON><PERSON><PERSON>, 1st Baron <PERSON>, English nobleman (b.c. 1570)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English nobleman (b.c. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English nobleman (b.c. 1570)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1619", "text": "<PERSON>, English poet and historian (b. 1562)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and historian (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and historian (b. 1562)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1631", "text": "<PERSON> of Mecklenburg-<PERSON><PERSON><PERSON><PERSON>, queen of Denmark and Norway (b. 1557)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg-G%C3%BCstrow\" title=\"<PERSON> of Mecklenburg-Güstrow\"><PERSON> of Mecklenburg<PERSON>Güstrow</a>, queen of Denmark and Norway (b. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg-G%C3%BCstrow\" title=\"<PERSON> of Mecklenburg-Güstrow\"><PERSON> of Mecklenburg<PERSON>Güstrow</a>, queen of Denmark and Norway (b. 1557)", "links": [{"title": "Sophie of Mecklenburg-Güstrow", "link": "https://wikipedia.org/wiki/Sophie_of_Mecklenburg-G%C3%BCstrow"}]}, {"year": "1637", "text": "<PERSON><PERSON>, Italian poet (b. 1552)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet (b. 1552)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rera"}]}, {"year": "1669", "text": "<PERSON>, Italian organist and composer (b. 1623)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, Danish bishop and poet (b. 1634)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish bishop and poet (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish bishop and poet (b. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian emperor (b. 1708)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/Tewoflos\" title=\"Tewoflos\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian emperor (b. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tewoflos\" title=\"Tewof<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian emperor (b. 1708)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tewoflos"}]}, {"year": "1758", "text": "<PERSON>, Scottish-Prussian field marshal (b. 1696)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Prussian field marshal (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Prussian field marshal (b. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON>, French astronomer and educator (b. 1761)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and educator (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and educator (b. 1761)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American lawyer and politician (b. 1833)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Dutch-Swiss author, poet, and playwright (b. 1848)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-Swiss author, poet, and playwright (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-Swiss author, poet, and playwright (b. 1848)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marcellus_Emants"}]}, {"year": "1929", "text": "<PERSON>, German composer and bandleader (b. 1844)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and bandleader (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and bandleader (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Dutch lawyer and politician, Dutch Minister of the Interior (b. 1837)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_the_Interior_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Ministers of the Interior of the Netherlands\">Dutch Minister of the Interior</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_the_Interior_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Ministers of the Interior of the Netherlands\">Dutch Minister of the Interior</a> (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ministers of the Interior of the Netherlands", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_the_Interior_of_the_Netherlands"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Japanese mob boss (b. 1902)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(yakuza)\" title=\"<PERSON><PERSON><PERSON> (yakuza)\"><PERSON><PERSON><PERSON></a>, Japanese mob boss (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(yakuza)\" title=\"<PERSON><PERSON><PERSON> (yakuza)\"><PERSON><PERSON><PERSON></a>, Japanese mob boss (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON> (yakuza)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_(yakuza)"}]}, {"year": "1943", "text": "Sobibór uprising:\n<PERSON>, German SS officer (b. 1910)\n<PERSON><PERSON><PERSON>, German sergeant (b. 1916)\n<PERSON>, German lieutenant (b. 1913)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Sobib%C3%B3r_uprising\" class=\"mw-redirect\" title=\"Sobibór uprising\">Sobibór uprising</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1910)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sergeant (b. 1916)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (b. 1913)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sobib%C3%B3r_uprising\" class=\"mw-redirect\" title=\"Sobibór uprising\">Sobibór uprising</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1910)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sergeant (b. 1916)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (b. 1913)</li>\n</ul>", "links": [{"title": "Sobibór uprising", "link": "https://wikipedia.org/wiki/Sobib%C3%B3r_uprising"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, German SS officer (b. 1910)", "text": null, "html": "<PERSON>, German SS officer (b. 1910) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>"}, {"title": "SS", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/SS"}]}, {"year": "<PERSON><PERSON><PERSON>, German sergeant (b. 1916)", "text": null, "html": "<PERSON><PERSON><PERSON>, German sergeant (b. 1916) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sergeant (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sergeant (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, German lieutenant (b. 1913)", "text": null, "html": "<PERSON>, German lieutenant (b. 1913) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German field marshal (b. 1891)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, French rugby player and tug of war competitor (b. 1877)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Sarrade\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French rugby player and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>rrade\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French rugby player and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Sarrade"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician (b. 1894)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_To<PERSON>da"}]}, {"year": "1958", "text": "<PERSON>, Australian geologist, academic, and explorer (b. 1882)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian geologist, academic, and explorer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian geologist, academic, and explorer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Russian-Soviet poet and translator (b. 1903)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Soviet poet and translator (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Soviet poet and translator (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, New Zealand-Australian singer and radio host (b. 1907)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer and radio host (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer and radio host (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Australian-American actor, singer, and producer (b. 1909)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-American actor, singer, and producer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-American actor, singer, and producer (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Russian physicist and academic (b. 1880)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1961", "text": "<PERSON>, French politician, 129th Prime Minister of France (b. 1888)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, 129th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, 129th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1961", "text": "<PERSON>, English journalist and activist (b. 1876)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and activist (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and activist (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American sprinter (b. 1884)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American poet and author (b. 1914)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian rugby league player (b. 1900)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (b. 1900)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1966", "text": "<PERSON>, English-Australian rugby league player, coach, and administrator (b. 1904)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian rugby league player, coach, and administrator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian rugby league player, coach, and administrator (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, French author and playwright (b. 1902)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Aym%C3%A9"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 36th <PERSON><PERSON><PERSON><PERSON> (b. 1914)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Masaji\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 36th <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>na_(sumo)\" class=\"mw-redirect\" title=\"Yoko<PERSON>na (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>ji\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 36th <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>na_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1969", "text": "<PERSON>, Estonian poet and translator (b. 1914)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/August_Sang\" title=\"August Sang\">August Sang</a>, Estonian poet and translator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Sang\" title=\"August Sang\">August Sang</a>, Estonian poet and translator (b. 1914)", "links": [{"title": "August Sang", "link": "https://wikipedia.org/wiki/August_Sang"}]}, {"year": "1973", "text": "<PERSON>, American journalist and broadcaster (b. 1897)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and broadcaster (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and broadcaster (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Egyptian general and engineer (b. 1929)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian general and engineer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian general and engineer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English actress (b. 1888)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter and actor (b. 1903)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French philosopher from the Vienna Circle (b. 1889)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher from the Vienna Circle (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher from the Vienna Circle (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>gie<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian-American historian and author (b. 1887)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American historian and author (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American historian and author (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English astronomer and physicist, Nobel Prize laureate (b. 1918)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1985", "text": "<PERSON>, Ukrainian-Russian pianist (b. 1916)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian pianist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian pianist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actor (b. 1916)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ynn\" title=\"<PERSON><PERSON> Wynn\"><PERSON><PERSON></a>, American actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ynn\" title=\"<PERSON><PERSON> Wynn\"><PERSON><PERSON></a>, American actor (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wynn"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese physicist (b. 1902)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American pianist, composer, and conductor (b. 1918)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American author (b. 1915)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American author and activist (b. 1917)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Cleveland_Amory\" title=\"Cleveland Amory\"><PERSON></a>, American author and activist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cleveland_Amory\" title=\"Cleveland Amory\"><PERSON></a>, American author and activist (b. 1917)", "links": [{"title": "Cleveland Amory", "link": "https://wikipedia.org/wiki/Cleveland_Amory"}]}, {"year": "1998", "text": "<PERSON>, American accordion player (b. 1916)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Tanzanian educator and politician, 1st President of Tanzania (b. 1922)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Tanzania", "link": "https://wikipedia.org/wiki/President_of_Tanzania"}]}, {"year": "2000", "text": "<PERSON>, Canadian-American ice hockey player (b. 1909)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Coulter"}]}, {"year": "2000", "text": "<PERSON>, American race car driver (b. 1964)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1964)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2002", "text": "<PERSON><PERSON>, German composer and conductor (b. 1911)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer and conductor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer and conductor (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>-<PERSON>, English linguist, commander, and navigator (b. 1913)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist, commander, and navigator (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist, commander, and navigator (b. 1913)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American historian, activist, and businessman (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, activist, and businessman (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, activist, and businessman (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fender\"><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Freddy Fender\"><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Dutch theologian and journalist (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ia\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch theologian and journalist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch theologian and journalist (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>as_Runia"}]}, {"year": "2006", "text": "<PERSON>, American educator and politician (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American engineer and intelligence officer (b. 1915)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and intelligence officer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and intelligence officer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Lithuanian basketball player and coach (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player and coach (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petkevi%C4%8Dius"}]}, {"year": "2009", "text": "<PERSON><PERSON>, New Zealand actor and screenwriter (b. 1938)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand actor and screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand actor and screenwriter (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American actress (b. 1935)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1935)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_(actress)"}]}, {"year": "2009", "text": "<PERSON>, American professional wrestler (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English actor, director, and producer (b. 1952)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Polish-American mathematician and economist (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American mathematician and economist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American mathematician and economist (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benoit_Mandelbrot"}]}, {"year": "2011", "text": "<PERSON>, Canadian businessman and politician (b. 1948)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, American computer scientist and philanthropist (b. 1949)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American computer scientist and philanthropist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American computer scientist and philanthropist (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English actor and author (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian journalist and author (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American lawyer and politician (b. 1919)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2012", "text": "<PERSON>, American publisher, co-founded <PERSON> (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Stern_Sloan\" title=\"Price <PERSON> Sloan\"><PERSON></a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Stern_Sloan\" title=\"<PERSON>\"><PERSON></a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Price_Stern_Sloan"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American lieutenant and politician (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lieutenant and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lieutenant and politician (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American photographer (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Dutch-American astronomer and academic (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American astronomer and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American astronomer and academic (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and umpire (b. 1965)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German-Catalan historian and politician (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Catalan historian and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Catalan historian and politician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Japanese author and poet (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> I<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and poet (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> I<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and poet (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Dichi_<PERSON><PERSON>ma"}]}, {"year": "2013", "text": "<PERSON>, French footballer and manager (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American painter and poet (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(performance_artist)\" title=\"<PERSON> (performance artist)\"><PERSON></a>, American painter and poet (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(performance_artist)\" title=\"<PERSON> (performance artist)\"><PERSON></a>, American painter and poet (b. 1946)", "links": [{"title": "<PERSON> (performance artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(performance_artist)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Romanian-Dutch chess player (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/K%C3%A4<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Dutch chess player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A4<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Dutch chess player (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A4<PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, English sociologist and academic (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English sociologist and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English sociologist and academic (b. 1923)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American author and academic (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1959)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>e%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elizabeth_Pe%C3%B1a"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Kazakh politician, 3rd Prime Minister of Kazakhstan (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakh politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kazakhstan\" title=\"Prime Minister of Kazakhstan\">Prime Minister of Kazakhstan</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakh politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kazakhstan\" title=\"Prime Minister of Kazakhstan\">Prime Minister of Kazakhstan</a> (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Kazakhstan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Kazakhstan"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Beninese soldier and politician, President of Benin (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_K%C3%A9r%C3%A9kou\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Beninese soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Benin\" title=\"President of Benin\">President of Benin</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9r%C3%A9kou\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Beninese soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Benin\" title=\"President of Benin\">President of Benin</a> (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mathieu_K%C3%A9r%C3%A9kou"}, {"title": "President of Benin", "link": "https://wikipedia.org/wiki/President_of_Benin"}]}, {"year": "2015", "text": "<PERSON>, American historian and academic (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian admiral (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian admiral (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian admiral (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>i"}]}, {"year": "2016", "text": "<PERSON>, New Zealand trade union leader (b. 1964)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)\" title=\"<PERSON> (trade unionist)\"><PERSON></a>, New Zealand trade union leader (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)\" title=\"<PERSON> (trade unionist)\"><PERSON></a>, New Zealand trade union leader (b. 1964)", "links": [{"title": "<PERSON> (trade unionist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)"}]}, {"year": "2019", "text": "<PERSON>, American literary critic (b. 1930)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary critic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary critic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, South Korean actress, singer, and model (b. 1994)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean actress, singer, and model (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean actress, singer, and model (b. 1994)", "links": [{"title": "Sulli", "link": "https://wikipedia.org/wiki/Sulli"}]}, {"year": "2021", "text": "<PERSON>, South Korean politician, 39th Prime Minister of South Korea", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean politician, 39th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Korea\" title=\"Prime Minister of South Korea\">Prime Minister of South Korea</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean politician, 39th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Korea\" title=\"Prime Minister of South Korea\">Prime Minister of South Korea</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-koo"}, {"title": "Prime Minister of South Korea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Korea"}]}, {"year": "2022", "text": "<PERSON>, Scottish actor, comedian and writer (b. 1950)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, comedian and writer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, comedian and writer (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American business executive (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American business executive (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American business executive (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American diplomat and government official (b. 1965)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat and government official (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat and government official (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, British historian (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American psychologist and academic (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}