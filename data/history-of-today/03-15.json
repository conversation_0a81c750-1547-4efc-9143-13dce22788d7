{"date": "March 15", "url": "https://wikipedia.org/wiki/March_15", "data": {"Events": [{"year": "474 BC", "text": "Roman consul <PERSON><PERSON> celebrates an ovation for concluding the war against <PERSON><PERSON><PERSON> and securing a forty years truce.", "html": "474 BC - 474 BC - <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">Roman consul</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Manlius_V<PERSON>so_(decemvir)\" title=\"<PERSON><PERSON>lius <PERSON> (decemvir)\"><PERSON><PERSON></a> celebrates an <a href=\"https://wikipedia.org/wiki/Ovation\" title=\"Ovation\">ovation</a> for concluding the <a href=\"https://wikipedia.org/wiki/Roman%E2%80%93Etruscan_Wars#Veii-Sabine_alliance_475-474_BC\" title=\"Roman-Etruscan Wars\">war</a> against <a href=\"https://wikipedia.org/wiki/Veii\" title=\"Veii\">Veii</a> and securing a forty years truce.", "no_year_html": "474 BC - <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">Roman consul</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Manlius_V<PERSON>so_(decemvir)\" title=\"<PERSON><PERSON>lius <PERSON>so (decemvir)\"><PERSON><PERSON></a> celebrates an <a href=\"https://wikipedia.org/wiki/Ovation\" title=\"Ovation\">ovation</a> for concluding the <a href=\"https://wikipedia.org/wiki/Roman%E2%80%93Etruscan_Wars#Veii-Sabine_alliance_475-474_BC\" title=\"Roman-Etruscan Wars\">war</a> against <a href=\"https://wikipedia.org/wiki/Veii\" title=\"Veii\">Veii</a> and securing a forty years truce.", "links": [{"title": "Roman consul", "link": "https://wikipedia.org/wiki/Roman_consul"}, {"title": "<PERSON><PERSON> (decemvir)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(decemvir)"}, {"title": "Ovation", "link": "https://wikipedia.org/wiki/Ovation"}, {"title": "Roman-Etruscan Wars", "link": "https://wikipedia.org/wiki/Roman%E2%80%93Etruscan_Wars#Veii-Sabine_alliance_475-474_BC"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veii"}]}, {"year": "44 BC", "text": "The assassination of <PERSON> takes place on the Ides of March.", "html": "44 BC - 44 BC - The <a href=\"https://wikipedia.org/wiki/Assassination_of_Julius_Caesar\" title=\"Assassination of <PERSON> Caesar\">assassination of <PERSON> Caesar</a> takes place on the <a href=\"https://wikipedia.org/wiki/Ides_of_March\" title=\"Ides of March\">Ides of March</a>.", "no_year_html": "44 BC - The <a href=\"https://wikipedia.org/wiki/Assassination_of_Julius_Caesar\" title=\"Assassination of Julius Caesar\">assassination of <PERSON></a> takes place on the <a href=\"https://wikipedia.org/wiki/Ides_of_March\" title=\"Ides of March\">Ides of March</a>.", "links": [{"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}, {"title": "Ides of March", "link": "https://wikipedia.org/wiki/Ides_of_March"}]}, {"year": "493", "text": "<PERSON><PERSON><PERSON><PERSON>, the first barbarian King of Italy after the fall of the Western Roman Empire, is slain by <PERSON><PERSON><PERSON> the Great, king of the Ostrogoths, while the two kings were feasting together.", "html": "493 - <a href=\"https://wikipedia.org/wiki/Odoacer\" title=\"Odo<PERSON>r\"><PERSON><PERSON><PERSON><PERSON></a>, the first barbarian <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a> after the fall of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a>, is slain by <a href=\"https://wikipedia.org/wiki/Theo<PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a>, king of the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a>, while the two kings were feasting together.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>r\" title=\"O<PERSON><PERSON>r\"><PERSON><PERSON><PERSON><PERSON></a>, the first barbarian <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a> after the fall of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a>, is slain by <a href=\"https://wikipedia.org/wiki/Theo<PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a>, king of the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a>, while the two kings were feasting together.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odoacer"}, {"title": "King of Italy", "link": "https://wikipedia.org/wiki/King_of_Italy"}, {"title": "Western Roman Empire", "link": "https://wikipedia.org/wiki/Western_Roman_Empire"}, {"title": "Theo<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/Theoder<PERSON>_the_Great"}, {"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}]}, {"year": "856", "text": "<PERSON>, emperor of the Byzantine Empire, overthrows the regency of his mother, empress <PERSON><PERSON> (wife of <PERSON><PERSON><PERSON>) with support of the Byzantine nobility.", "html": "856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> III</a>, emperor of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>, overthrows the regency of his mother, empress <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wife_of_<PERSON><PERSON><PERSON>)\" title=\"<PERSON><PERSON> (wife of <PERSON><PERSON><PERSON>)\"><PERSON><PERSON></a> (wife of <PERSON><PERSON><PERSON>) with support of the Byzantine <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobility</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>, overthrows the regency of his mother, empress <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wife_of_<PERSON><PERSON><PERSON>)\" title=\"<PERSON><PERSON> (wife of <PERSON><PERSON><PERSON>)\"><PERSON><PERSON></a> (wife of <PERSON><PERSON><PERSON>) with support of the Byzantine <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobility</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON> (wife of <PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wife_of_<PERSON><PERSON><PERSON>)"}, {"title": "Nobility", "link": "https://wikipedia.org/wiki/Nobility"}]}, {"year": "897", "text": " <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> enters Sa'dah and founds the Zaydi Imamate of Yemen.", "html": "897 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_ila%27l-<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Al-Hadi ila'l-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> il<PERSON><PERSON>l<PERSON><PERSON><PERSON><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Sa%27dah\" class=\"mw-redirect\" title=\"Sa'dah\">Sa'dah</a> and founds the <a href=\"https://wikipedia.org/wiki/Zaydi\" class=\"mw-redirect\" title=\"Zay<PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Imamate_of_Yemen\" class=\"mw-redirect\" title=\"Imamate of Yemen\">Imamate of Yemen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-Hadi_ila%27l-<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Al-Hadi ila'l-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>l<PERSON><PERSON><PERSON><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Sa%27dah\" class=\"mw-redirect\" title=\"Sa'dah\">Sa'dah</a> and founds the <a href=\"https://wikipedia.org/wiki/Zaydi\" class=\"mw-redirect\" title=\"Zay<PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Imamate_of_Yemen\" class=\"mw-redirect\" title=\"Imamate of Yemen\">Imamate of Yemen</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Hadi_ila%27l-<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Sa'dah", "link": "https://wikipedia.org/wiki/Sa%27dah"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Imamate of Yemen", "link": "https://wikipedia.org/wiki/Imamate_of_Yemen"}]}, {"year": "933", "text": "After a ten-year truce, German King <PERSON> defeats a Hungarian army at the Battle of Riade near the Unstrut river.", "html": "933 - After a ten-year truce, German King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Fowler\"><PERSON> the <PERSON></a> defeats a Hungarian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Riade\" title=\"Battle of Riade\">Battle of Riade</a> near the <a href=\"https://wikipedia.org/wiki/Unstrut\" title=\"Unstrut\">Unstrut</a> river.", "no_year_html": "After a ten-year truce, German King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Fowler\"><PERSON> the Fowler</a> defeats a Hungarian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Riade\" title=\"Battle of Riade\">Battle of Riade</a> near the <a href=\"https://wikipedia.org/wiki/Unstrut\" title=\"Unstrut\">Unstrut</a> river.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Riade", "link": "https://wikipedia.org/wiki/Battle_of_Riade"}, {"title": "Unstrut", "link": "https://wikipedia.org/wiki/Unstrut"}]}, {"year": "1311", "text": "Battle of Halmyros: The Catalan Company defeats <PERSON>, Count of Brienne to take control of the Duchy of Athens, a Crusader state in Greece.", "html": "1311 - <a href=\"https://wikipedia.org/wiki/Battle_of_Halmyros\" title=\"Battle of Halmyros\">Battle of Halmyros</a>: The <a href=\"https://wikipedia.org/wiki/Catalan_Company\" title=\"Catalan Company\">Catalan Company</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Brienne\" title=\"<PERSON>, Count of Brienne\"><PERSON>, Count of Brienne</a> to take control of the <a href=\"https://wikipedia.org/wiki/Duchy_of_Athens\" title=\"Duchy of Athens\">Duchy of Athens</a>, a <a href=\"https://wikipedia.org/wiki/Crusader_states\" title=\"Crusader states\">Crusader state</a> in Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Halmyros\" title=\"Battle of Halmyros\">Battle of Halmyros</a>: The <a href=\"https://wikipedia.org/wiki/Catalan_Company\" title=\"Catalan Company\">Catalan Company</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Brienne\" title=\"<PERSON>, Count of Brienne\"><PERSON>, Count of Brienne</a> to take control of the <a href=\"https://wikipedia.org/wiki/Duchy_of_Athens\" title=\"Duchy of Athens\">Duchy of Athens</a>, a <a href=\"https://wikipedia.org/wiki/Crusader_states\" title=\"Crusader states\">Crusader state</a> in Greece.", "links": [{"title": "Battle of Halmyros", "link": "https://wikipedia.org/wiki/Battle_of_Halmyros"}, {"title": "Catalan Company", "link": "https://wikipedia.org/wiki/Catalan_Company"}, {"title": "<PERSON>, Count of Brienne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}, {"title": "Duchy of Athens", "link": "https://wikipedia.org/wiki/Duchy_of_Athens"}, {"title": "Crusader states", "link": "https://wikipedia.org/wiki/Crusader_states"}]}, {"year": "1564", "text": "Mughal Emperor <PERSON> abolishes the jizya tax on non-Muslim subjects.", "html": "1564 - Mughal Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> abolishes the <i><a href=\"https://wikipedia.org/wiki/Jizya\" title=\"Jizy<PERSON>\">jizya</a></i> tax on <a href=\"https://wikipedia.org/wiki/Dhimmi\" title=\"<PERSON>him<PERSON>\">non-Muslim</a> subjects.", "no_year_html": "Mughal Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> abolishes the <i><a href=\"https://wikipedia.org/wiki/Jizya\" title=\"Jizya\">jizya</a></i> tax on <a href=\"https://wikipedia.org/wiki/Dhimmi\" title=\"<PERSON>him<PERSON>\">non-Muslim</a> subjects.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Akbar"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>zya"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dhimmi"}]}, {"year": "1626", "text": "a dam failure causes the sudden flooding of the mining city of Potosí in present-day Bolivia leading to the death of thousands and the massive release of toxic mercury into the environment.", "html": "1626 - a <a href=\"https://wikipedia.org/wiki/Dam_failure\" title=\"Dam failure\">dam failure</a> causes the sudden <a href=\"https://wikipedia.org/wiki/1626_Potos%C3%AD_flood\" title=\"1626 Potosí flood\">flooding of the mining city of Potosí</a> in present-day Bolivia leading to the death of thousands and the massive release of toxic <a href=\"https://wikipedia.org/wiki/Mercury_(element)\" title=\"Mercury (element)\">mercury</a> into the environment.", "no_year_html": "a <a href=\"https://wikipedia.org/wiki/Dam_failure\" title=\"Dam failure\">dam failure</a> causes the sudden <a href=\"https://wikipedia.org/wiki/1626_Potos%C3%AD_flood\" title=\"1626 Potosí flood\">flooding of the mining city of Potosí</a> in present-day Bolivia leading to the death of thousands and the massive release of toxic <a href=\"https://wikipedia.org/wiki/Mercury_(element)\" title=\"Mercury (element)\">mercury</a> into the environment.", "links": [{"title": "Dam failure", "link": "https://wikipedia.org/wiki/Dam_failure"}, {"title": "1626 Potosí flood", "link": "https://wikipedia.org/wiki/1626_Potos%C3%AD_flood"}, {"title": "Mercury (element)", "link": "https://wikipedia.org/wiki/Mercury_(element)"}]}, {"year": "1672", "text": "King <PERSON> of England issues the Royal Declaration of Indulgence, granting limited religious freedom to all Christians.", "html": "1672 - King <PERSON> of England issues the <a href=\"https://wikipedia.org/wiki/Declaration_of_Indulgence_(1672)\" title=\"Declaration of Indulgence (1672)\">Royal Declaration of Indulgence</a>, granting limited religious freedom to all Christians.", "no_year_html": "King <PERSON> of England issues the <a href=\"https://wikipedia.org/wiki/Declaration_of_Indulgence_(1672)\" title=\"Declaration of Indulgence (1672)\">Royal Declaration of Indulgence</a>, granting limited religious freedom to all Christians.", "links": [{"title": "Declaration of Indulgence (1672)", "link": "https://wikipedia.org/wiki/Declaration_of_Indulgence_(1672)"}]}, {"year": "1783", "text": "In an emotional speech in Newburgh, New York, <PERSON> asks his officers not to support the Newburgh Conspiracy. The plea is successful, and the threatened coup d'état never takes place.", "html": "1783 - In an emotional speech in <a href=\"https://wikipedia.org/wiki/Newburgh,_New_York\" title=\"Newburgh, New York\">Newburgh, New York</a>, <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George <PERSON>\"><PERSON></a> asks his officers not to support the <a href=\"https://wikipedia.org/wiki/Newburgh_Conspiracy\" title=\"Newburgh Conspiracy\">Newburgh Conspiracy</a>. The plea is successful, and the threatened <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> never takes place.", "no_year_html": "In an emotional speech in <a href=\"https://wikipedia.org/wiki/Newburgh,_New_York\" title=\"Newburgh, New York\">Newburgh, New York</a>, <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"<PERSON>\"><PERSON></a> asks his officers not to support the <a href=\"https://wikipedia.org/wiki/Newburgh_Conspiracy\" title=\"Newburgh Conspiracy\">Newburgh Conspiracy</a>. The plea is successful, and the threatened <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> never takes place.", "links": [{"title": "Newburgh, New York", "link": "https://wikipedia.org/wiki/Newburgh,_New_York"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "Newburgh Conspiracy", "link": "https://wikipedia.org/wiki/Newburgh_Conspiracy"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}]}, {"year": "1820", "text": "Maine is admitted as the twenty-third U.S. state.", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Maine\" title=\"Maine\">Maine</a> is admitted as the twenty-third <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maine\" title=\"Maine\">Maine</a> is admitted as the twenty-third <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Maine", "link": "https://wikipedia.org/wiki/Maine"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1823", "text": "Sailor <PERSON> erroneously reported the existence of the island of New South Greenland near Antarctica.", "html": "1823 - Sailor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Phantom_island\" title=\"Phantom island\">erroneously reported</a> the existence of the island of <a href=\"https://wikipedia.org/wiki/New_South_Greenland\" title=\"New South Greenland\">New South Greenland</a> near <a href=\"https://wikipedia.org/wiki/Antarctica\" title=\"Antarctica\">Antarctica</a>.", "no_year_html": "Sailor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Phantom_island\" title=\"Phantom island\">erroneously reported</a> the existence of the island of <a href=\"https://wikipedia.org/wiki/New_South_Greenland\" title=\"New South Greenland\">New South Greenland</a> near <a href=\"https://wikipedia.org/wiki/Antarctica\" title=\"Antarctica\">Antarctica</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Phantom island", "link": "https://wikipedia.org/wiki/Phantom_island"}, {"title": "New South Greenland", "link": "https://wikipedia.org/wiki/New_South_Greenland"}, {"title": "Antarctica", "link": "https://wikipedia.org/wiki/Antarctica"}]}, {"year": "1848", "text": "A revolution breaks out in Hungary, and the Habsburg rulers are compelled to meet the demands of the reform party.", "html": "1848 - A <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1848\" title=\"Hungarian Revolution of 1848\">revolution breaks out in Hungary</a>, and the <a href=\"https://wikipedia.org/wiki/Habsburg\" class=\"mw-redirect\" title=\"Habsburg\">Habsburg</a> rulers are compelled to meet the demands of the reform party.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1848\" title=\"Hungarian Revolution of 1848\">revolution breaks out in Hungary</a>, and the <a href=\"https://wikipedia.org/wiki/Habsburg\" class=\"mw-redirect\" title=\"Habsburg\">Habsburg</a> rulers are compelled to meet the demands of the reform party.", "links": [{"title": "Hungarian Revolution of 1848", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1848"}, {"title": "Habsburg", "link": "https://wikipedia.org/wiki/Habsburg"}]}, {"year": "1874", "text": "France and Vietnam sign the Second Treaty of Saigon, further recognizing the full sovereignty of France over Cochinchina.", "html": "1874 - <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">France</a> and <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Vietnam</a> sign the <a href=\"https://wikipedia.org/wiki/Second_Treaty_of_Saigon\" class=\"mw-redirect\" title=\"Second Treaty of Saigon\">Second Treaty of Saigon</a>, further recognizing the full sovereignty of France over <a href=\"https://wikipedia.org/wiki/Cochinchina\" title=\"Cochinchina\">Cochinchina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">France</a> and <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Vietnam</a> sign the <a href=\"https://wikipedia.org/wiki/Second_Treaty_of_Saigon\" class=\"mw-redirect\" title=\"Second Treaty of Saigon\">Second Treaty of Saigon</a>, further recognizing the full sovereignty of France over <a href=\"https://wikipedia.org/wiki/Cochinchina\" title=\"Cochinchina\">Cochinchina</a>.", "links": [{"title": "French Third Republic", "link": "https://wikipedia.org/wiki/French_Third_Republic"}, {"title": "Nguyễn dynasty", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty"}, {"title": "Second Treaty of Saigon", "link": "https://wikipedia.org/wiki/Second_Treaty_of_Saigon"}, {"title": "Cochinchina", "link": "https://wikipedia.org/wiki/Cochinchina"}]}, {"year": "1875", "text": "Archbishop of New York <PERSON> is named the first cardinal in the United States.", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Archbishop_of_New_York\" class=\"mw-redirect\" title=\"Archbishop of New York\">Archbishop of New York</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is named the first <a href=\"https://wikipedia.org/wiki/<PERSON>_(Catholicism)\" class=\"mw-redirect\" title=\"<PERSON> (Catholicism)\">cardinal</a> in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archbishop_of_New_York\" class=\"mw-redirect\" title=\"Archbishop of New York\">Archbishop of New York</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is named the first <a href=\"https://wikipedia.org/wiki/<PERSON>_(Catholicism)\" class=\"mw-redirect\" title=\"<PERSON> (Catholicism)\">cardinal</a> in the United States.", "links": [{"title": "Archbishop of New York", "link": "https://wikipedia.org/wiki/<PERSON>_of_New_York"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (Catholicism)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholicism)"}]}, {"year": "1877", "text": "First ever official cricket test match is played: Australia vs England at the MCG Stadium, in Melbourne, Australia.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/First_ever_official_cricket_test_match_is_played\" class=\"mw-redirect\" title=\"First ever official cricket test match is played\">First ever official cricket test match is played</a>: Australia vs England at the <a href=\"https://wikipedia.org/wiki/MCG_Stadium\" class=\"mw-redirect\" title=\"MCG Stadium\">MCG Stadium</a>, in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_ever_official_cricket_test_match_is_played\" class=\"mw-redirect\" title=\"First ever official cricket test match is played\">First ever official cricket test match is played</a>: Australia vs England at the <a href=\"https://wikipedia.org/wiki/MCG_Stadium\" class=\"mw-redirect\" title=\"MCG Stadium\">MCG Stadium</a>, in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, Australia.", "links": [{"title": "First ever official cricket test match is played", "link": "https://wikipedia.org/wiki/First_ever_official_cricket_test_match_is_played"}, {"title": "MCG Stadium", "link": "https://wikipedia.org/wiki/MCG_Stadium"}, {"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}]}, {"year": "1888", "text": "Start of the Anglo-Tibetan War of 1888.", "html": "1888 - Start of the <a href=\"https://wikipedia.org/wiki/Sikkim_Expedition\" class=\"mw-redirect\" title=\"Sikkim Expedition\">Anglo-Tibetan War of 1888</a>.", "no_year_html": "Start of the <a href=\"https://wikipedia.org/wiki/Sikkim_Expedition\" class=\"mw-redirect\" title=\"Sikkim Expedition\">Anglo-Tibetan War of 1888</a>.", "links": [{"title": "Sikkim Expedition", "link": "https://wikipedia.org/wiki/Sikkim_Expedition"}]}, {"year": "1907", "text": "The first parliamentary elections of Finland (at the time the Grand Duchy of Finland) are held.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/1907_Finnish_parliamentary_election\" title=\"1907 Finnish parliamentary election\">first parliamentary elections</a> of <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> (at the time the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>) are held.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1907_Finnish_parliamentary_election\" title=\"1907 Finnish parliamentary election\">first parliamentary elections</a> of <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> (at the time the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>) are held.", "links": [{"title": "1907 Finnish parliamentary election", "link": "https://wikipedia.org/wiki/1907_Finnish_parliamentary_election"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}]}, {"year": "1917", "text": "Tsar <PERSON> of Russia abdicates the Russian throne, ending the 304-year Romanov dynasty.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> abdicates the Russian throne, ending the 304-year Romanov dynasty.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> abdicates the Russian throne, ending the 304-year Romanov dynasty.", "links": [{"title": "Tsar", "link": "https://wikipedia.org/wiki/Tsar"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}]}, {"year": "1918", "text": "Finnish Civil War: The battle of Tampere begins.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tampere\" title=\"Battle of Tampere\">battle of Tampere</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tampere\" title=\"Battle of Tampere\">battle of Tampere</a> begins.", "links": [{"title": "Finnish Civil War", "link": "https://wikipedia.org/wiki/Finnish_Civil_War"}, {"title": "Battle of Tampere", "link": "https://wikipedia.org/wiki/Battle_of_Tampere"}]}, {"year": "1919", "text": "Ukrainian War of Independence: The Kontrrazvedka is established as the counterintelligence division of the Revolutionary Insurgent Army of Ukraine.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Kontrrazvedka\" title=\"Kontrrazvedka\">Kontrrazvedka</a> is established as the <a href=\"https://wikipedia.org/wiki/Counterintelligence\" title=\"Counterintelligence\">counterintelligence</a> division of the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Kontrrazvedka\" title=\"Kontrrazvedka\">Kontrrazvedka</a> is established as the <a href=\"https://wikipedia.org/wiki/Counterintelligence\" title=\"Counterintelligence\">counterintelligence</a> division of the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a>.", "links": [{"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}, {"title": "Kontrrazvedka", "link": "https://wikipedia.org/wiki/Kontrrazvedka"}, {"title": "Counterintelligence", "link": "https://wikipedia.org/wiki/Counterintelligence"}, {"title": "Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine"}]}, {"year": "1919", "text": "The American Legion is founded.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/American_Legion\" title=\"American Legion\">American Legion</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_Legion\" title=\"American Legion\">American Legion</a> is founded.", "links": [{"title": "American Legion", "link": "https://wikipedia.org/wiki/American_Legion"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, former Grand Vizir of the Ottoman Empire and chief architect of the Armenian genocide is assassinated in Berlin by a 23-year-old Armenian, <PERSON><PERSON><PERSON><PERSON>.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, former Grand Vizir of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> and chief architect of the <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_Pasha\" class=\"mw-redirect\" title=\"Assassination of <PERSON><PERSON>\">assassinated in Berlin</a> by a 23-year-old Armenian, <a href=\"https://wikipedia.org/wiki/Soghomon_Tehlirian\" title=\"Soghomon Tehlirian\">Soghomon Tehlirian</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, former Grand Vizir of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> and chief architect of the <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Assassination of <PERSON><PERSON>\">assassinated in Berlin</a> by a 23-year-old Armenian, <a href=\"https://wikipedia.org/wiki/Soghomon_Tehlirian\" title=\"Soghomon Tehlirian\">Soghomon Tehl<PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Armenian genocide", "link": "https://wikipedia.org/wiki/Armenian_genocide"}, {"title": "Assassination of Talat Pasha", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>"}, {"title": "Soghomon <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>an"}]}, {"year": "1922", "text": "After Egypt gains nominal independence from the United Kingdom, <PERSON><PERSON> becomes King of Egypt.", "html": "1922 - After <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> gains nominal independence from the United Kingdom, <a href=\"https://wikipedia.org/wiki/Fuad_I_of_Egypt\" title=\"Fuad I of Egypt\">Fuad I</a> becomes <a href=\"https://wikipedia.org/wiki/King_of_Egypt\" title=\"King of Egypt\">King of Egypt</a>.", "no_year_html": "After <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> gains nominal independence from the United Kingdom, <a href=\"https://wikipedia.org/wiki/Fuad_I_of_Egypt\" title=\"Fuad I of Egypt\">Fuad I</a> becomes <a href=\"https://wikipedia.org/wiki/King_of_Egypt\" title=\"King of Egypt\">King of Egypt</a>.", "links": [{"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "<PERSON><PERSON> I of Egypt", "link": "https://wikipedia.org/wiki/Fuad_I_of_Egypt"}, {"title": "King of Egypt", "link": "https://wikipedia.org/wiki/King_of_Egypt"}]}, {"year": "1927", "text": "The first Women's Boat Race between the University of Oxford and the University of Cambridge takes place on The Isis in Oxford.", "html": "1927 - The first <a href=\"https://wikipedia.org/wiki/Women%27s_Boat_Race\" title=\"Women's Boat Race\">Women's Boat Race</a> between the <a href=\"https://wikipedia.org/wiki/University_of_Oxford\" title=\"University of Oxford\">University of Oxford</a> and the <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">University of Cambridge</a> takes place on <a href=\"https://wikipedia.org/wiki/The_Isis\" title=\"The Isis\">The Isis</a> in <a href=\"https://wikipedia.org/wiki/Oxford\" title=\"Oxford\">Oxford</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Women%27s_Boat_Race\" title=\"Women's Boat Race\">Women's Boat Race</a> between the <a href=\"https://wikipedia.org/wiki/University_of_Oxford\" title=\"University of Oxford\">University of Oxford</a> and the <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">University of Cambridge</a> takes place on <a href=\"https://wikipedia.org/wiki/The_Isis\" title=\"The Isis\">The Isis</a> in <a href=\"https://wikipedia.org/wiki/Oxford\" title=\"Oxford\">Oxford</a>.", "links": [{"title": "Women's Boat Race", "link": "https://wikipedia.org/wiki/Women%27s_Boat_Race"}, {"title": "University of Oxford", "link": "https://wikipedia.org/wiki/University_of_Oxford"}, {"title": "University of Cambridge", "link": "https://wikipedia.org/wiki/University_of_Cambridge"}, {"title": "The Isis", "link": "https://wikipedia.org/wiki/The_Isis"}, {"title": "Oxford", "link": "https://wikipedia.org/wiki/Oxford"}]}, {"year": "1939", "text": "Germany occupies Czechoslovakia.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/German_occupation_of_Czechoslovakia\" class=\"mw-redirect\" title=\"German occupation of Czechoslovakia\">Germany occupies Czechoslovakia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/German_occupation_of_Czechoslovakia\" class=\"mw-redirect\" title=\"German occupation of Czechoslovakia\">Germany occupies Czechoslovakia</a>.", "links": [{"title": "German occupation of Czechoslovakia", "link": "https://wikipedia.org/wiki/German_occupation_of_Czechoslovakia"}]}, {"year": "1939", "text": "Carpatho-Ukraine declares itself an independent republic, but is annexed by Hungary the next day.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Carpatho-Ukraine\" title=\"Carpatho-Ukraine\">Carpatho-Ukraine</a> declares itself an independent republic, but is annexed by Hungary the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carpatho-Ukraine\" title=\"Carpatho-Ukraine\">Carpatho-Ukraine</a> declares itself an independent republic, but is annexed by Hungary the next day.", "links": [{"title": "Carpatho-Ukraine", "link": "https://wikipedia.org/wiki/Carpatho-Ukraine"}]}, {"year": "1943", "text": "World War II: Third Battle of Kharkiv: The Germans retake the city of Kharkiv from the Soviet armies.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Kharkov\" title=\"Third Battle of Kharkov\">Third Battle of Kharkiv</a>: The Germans retake the city of <a href=\"https://wikipedia.org/wiki/Kharkiv\" title=\"Kharkiv\">Kharkiv</a> from the Soviet armies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Kharkov\" title=\"Third Battle of Kharkov\">Third Battle of Kharkiv</a>: The Germans retake the city of <a href=\"https://wikipedia.org/wiki/Kharkiv\" title=\"Kharkiv\">Kharkiv</a> from the Soviet armies.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Third Battle of Kharkov", "link": "https://wikipedia.org/wiki/Third_Battle_of_Kharkov"}, {"title": "Kharkiv", "link": "https://wikipedia.org/wiki/Kharkiv"}]}, {"year": "1951", "text": "Iranian oil industry is nationalized.", "html": "1951 - Iranian oil industry is <a href=\"https://wikipedia.org/wiki/Nationalization_of_the_Iranian_oil_industry\" title=\"Nationalization of the Iranian oil industry\">nationalized</a>.", "no_year_html": "Iranian oil industry is <a href=\"https://wikipedia.org/wiki/Nationalization_of_the_Iranian_oil_industry\" title=\"Nationalization of the Iranian oil industry\">nationalized</a>.", "links": [{"title": "Nationalization of the Iranian oil industry", "link": "https://wikipedia.org/wiki/Nationalization_of_the_Iranian_oil_industry"}]}, {"year": "1961", "text": "At the 1961 Commonwealth Prime Ministers' Conference, South Africa announces that it will withdraw from the Commonwealth when the South African Constitution of 1961 comes into effect.", "html": "1961 - At the <a href=\"https://wikipedia.org/wiki/1961_Commonwealth_Prime_Ministers%27_Conference\" title=\"1961 Commonwealth Prime Ministers' Conference\">1961 Commonwealth Prime Ministers' Conference</a>, South Africa announces that it will withdraw from the Commonwealth when the <a href=\"https://wikipedia.org/wiki/South_African_Constitution_of_1961\" title=\"South African Constitution of 1961\">South African Constitution of 1961</a> comes into effect.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/1961_Commonwealth_Prime_Ministers%27_Conference\" title=\"1961 Commonwealth Prime Ministers' Conference\">1961 Commonwealth Prime Ministers' Conference</a>, South Africa announces that it will withdraw from the Commonwealth when the <a href=\"https://wikipedia.org/wiki/South_African_Constitution_of_1961\" title=\"South African Constitution of 1961\">South African Constitution of 1961</a> comes into effect.", "links": [{"title": "1961 Commonwealth Prime Ministers' Conference", "link": "https://wikipedia.org/wiki/1961_Commonwealth_Prime_Ministers%27_Conference"}, {"title": "South African Constitution of 1961", "link": "https://wikipedia.org/wiki/South_African_Constitution_of_1961"}]}, {"year": "1965", "text": "President <PERSON>, responding to the Selma crisis, tells U.S. Congress \"We shall overcome\" while advocating the Voting Rights Act.", "html": "1965 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, responding to the <a href=\"https://wikipedia.org/wiki/Selma,_Alabama\" title=\"Selma, Alabama\"><PERSON><PERSON></a> crisis, tells <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> \"We shall overcome\" while advocating the <a href=\"https://wikipedia.org/wiki/Voting_Rights_Act\" class=\"mw-redirect\" title=\"Voting Rights Act\">Voting Rights Act</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, responding to the <a href=\"https://wikipedia.org/wiki/Selma,_Alabama\" title=\"Selma, Alabama\"><PERSON><PERSON></a> crisis, tells <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> \"We shall overcome\" while advocating the <a href=\"https://wikipedia.org/wiki/Voting_Rights_Act\" class=\"mw-redirect\" title=\"Voting Rights Act\">Voting Rights Act</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Selma, Alabama", "link": "https://wikipedia.org/wiki/Selma,_Alabama"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Voting Rights Act", "link": "https://wikipedia.org/wiki/Voting_Rights_Act"}]}, {"year": "1974", "text": "Fifteen people are killed when Sterling Airways Flight 901, a Sud Aviation Caravelle, catches fire following a landing gear collapse at Mehrabad International Airport in Tehran, Iran.", "html": "1974 - Fifteen people are killed when <a href=\"https://wikipedia.org/wiki/Sterling_Airways_Flight_901\" title=\"Sterling Airways Flight 901\">Sterling Airways Flight 901</a>, a <a href=\"https://wikipedia.org/wiki/Sud_Aviation_Caravelle\" title=\"Sud Aviation Caravelle\">Sud Aviation Caravelle</a>, catches fire following a <a href=\"https://wikipedia.org/wiki/Landing_gear\" title=\"Landing gear\">landing gear</a> collapse at <a href=\"https://wikipedia.org/wiki/Mehrabad_International_Airport\" title=\"Mehrabad International Airport\">Mehrabad International Airport</a> in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, Iran.", "no_year_html": "Fifteen people are killed when <a href=\"https://wikipedia.org/wiki/Sterling_Airways_Flight_901\" title=\"Sterling Airways Flight 901\">Sterling Airways Flight 901</a>, a <a href=\"https://wikipedia.org/wiki/Sud_Aviation_Caravelle\" title=\"Sud Aviation Caravelle\">Sud Aviation Caravelle</a>, catches fire following a <a href=\"https://wikipedia.org/wiki/Landing_gear\" title=\"Landing gear\">landing gear</a> collapse at <a href=\"https://wikipedia.org/wiki/Mehrabad_International_Airport\" title=\"Mehrabad International Airport\">Mehrabad International Airport</a> in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, Iran.", "links": [{"title": "Sterling Airways Flight 901", "link": "https://wikipedia.org/wiki/Sterling_Airways_Flight_901"}, {"title": "Sud Aviation Caravelle", "link": "https://wikipedia.org/wiki/Sud_Aviation_Caravelle"}, {"title": "Landing gear", "link": "https://wikipedia.org/wiki/Landing_gear"}, {"title": "Mehrabad International Airport", "link": "https://wikipedia.org/wiki/Mehrabad_International_Airport"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}]}, {"year": "1978", "text": "Somalia and Ethiopia signed a truce to end the Ethio-Somali War.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a> and <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> signed a truce to end the <a href=\"https://wikipedia.org/wiki/Ethio-Somali_War\" class=\"mw-redirect\" title=\"Ethio-Somali War\">Ethio-Somali War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a> and <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> signed a truce to end the <a href=\"https://wikipedia.org/wiki/Ethio-Somali_War\" class=\"mw-redirect\" title=\"Ethio-Somali War\">Ethio-Somali War</a>.", "links": [{"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Ethio-Somali War", "link": "https://wikipedia.org/wiki/Ethio-Somali_War"}]}, {"year": "1986", "text": "Collapse of Hotel New World: Thirty-three people die when the Hotel New World in Singapore collapses.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Collapse_of_Hotel_New_World\" title=\"Collapse of Hotel New World\">Collapse of Hotel New World</a>: Thirty-three people die when the Hotel New World in Singapore collapses.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Collapse_of_Hotel_New_World\" title=\"Collapse of Hotel New World\">Collapse of Hotel New World</a>: Thirty-three people die when the Hotel New World in Singapore collapses.", "links": [{"title": "Collapse of Hotel New World", "link": "https://wikipedia.org/wiki/Collapse_of_Hotel_New_World"}]}, {"year": "1990", "text": "<PERSON> is elected as the first President of the Soviet Union.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the first <a href=\"https://wikipedia.org/wiki/President_of_the_Soviet_Union\" title=\"President of the Soviet Union\">President of the Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the first <a href=\"https://wikipedia.org/wiki/President_of_the_Soviet_Union\" title=\"President of the Soviet Union\">President of the Soviet Union</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Soviet Union", "link": "https://wikipedia.org/wiki/President_of_the_Soviet_Union"}]}, {"year": "1991", "text": "Cold War: The Treaty on the Final Settlement with Respect to Germany comes into effect, granting full sovereignty to the Federal Republic of Germany.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Final_Settlement_with_Respect_to_Germany\" title=\"Treaty on the Final Settlement with Respect to Germany\">Treaty on the Final Settlement with Respect to Germany</a> comes into effect, granting full sovereignty to the <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Federal Republic of Germany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Final_Settlement_with_Respect_to_Germany\" title=\"Treaty on the Final Settlement with Respect to Germany\">Treaty on the Final Settlement with Respect to Germany</a> comes into effect, granting full sovereignty to the <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Federal Republic of Germany</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Treaty on the Final Settlement with Respect to Germany", "link": "https://wikipedia.org/wiki/Treaty_on_the_Final_Settlement_with_Respect_to_Germany"}, {"title": "Germany", "link": "https://wikipedia.org/wiki/Germany"}]}, {"year": "2008", "text": "Stockpiles of obsolete ammunition explode at an ex-military ammunition depot in the village of Gërdec, Albania, killing 26 people.", "html": "2008 - Stockpiles of obsolete ammunition <a href=\"https://wikipedia.org/wiki/2008_G%C3%ABrdec_explosions\" title=\"2008 Gërdec explosions\">explode</a> at an ex-military ammunition depot in the village of <a href=\"https://wikipedia.org/wiki/G%C3%ABrdec\" title=\"Gërdec\">Gërdec</a>, Albania, killing 26 people.", "no_year_html": "Stockpiles of obsolete ammunition <a href=\"https://wikipedia.org/wiki/2008_G%C3%ABrdec_explosions\" title=\"2008 Gërdec explosions\">explode</a> at an ex-military ammunition depot in the village of <a href=\"https://wikipedia.org/wiki/G%C3%ABrdec\" title=\"Gërdec\">Gërdec</a>, Albania, killing 26 people.", "links": [{"title": "2008 Gërdec explosions", "link": "https://wikipedia.org/wiki/2008_G%C3%ABrdec_explosions"}, {"title": "G<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%ABrdec"}]}, {"year": "2011", "text": "Beginning of the Syrian Civil War.", "html": "2011 - Beginning of the <a href=\"https://wikipedia.org/wiki/Syrian_Civil_War\" class=\"mw-redirect\" title=\"Syrian Civil War\">Syrian Civil War</a>.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/Syrian_Civil_War\" class=\"mw-redirect\" title=\"Syrian Civil War\">Syrian Civil War</a>.", "links": [{"title": "Syrian Civil War", "link": "https://wikipedia.org/wiki/Syrian_Civil_War"}]}, {"year": "2019", "text": "Fifty-one people are killed in the Christchurch mosque shootings.", "html": "2019 - Fifty-one people are killed in the <a href=\"https://wikipedia.org/wiki/Christchurch_mosque_shootings\" title=\"Christchurch mosque shootings\">Christchurch mosque shootings</a>.", "no_year_html": "Fifty-one people are killed in the <a href=\"https://wikipedia.org/wiki/Christchurch_mosque_shootings\" title=\"Christchurch mosque shootings\">Christchurch mosque shootings</a>.", "links": [{"title": "Christchurch mosque shootings", "link": "https://wikipedia.org/wiki/Christchurch_mosque_shootings"}]}, {"year": "2019", "text": "Beginning of the 2019-20 Hong Kong protests.", "html": "2019 - Beginning of the <a href=\"https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests\" class=\"mw-redirect\" title=\"2019-20 Hong Kong protests\">2019-20 Hong Kong protests</a>.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests\" class=\"mw-redirect\" title=\"2019-20 Hong Kong protests\">2019-20 Hong Kong protests</a>.", "links": [{"title": "2019-20 Hong Kong protests", "link": "https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests"}]}, {"year": "2019", "text": "Approximately 1.4 million young people in 123 countries go on strike to protest climate change.", "html": "2019 - Approximately 1.4 million young people in 123 countries <a href=\"https://wikipedia.org/wiki/School_strike_for_climate\" class=\"mw-redirect\" title=\"School strike for climate\">go on strike</a> to protest climate change.", "no_year_html": "Approximately 1.4 million young people in 123 countries <a href=\"https://wikipedia.org/wiki/School_strike_for_climate\" class=\"mw-redirect\" title=\"School strike for climate\">go on strike</a> to protest climate change.", "links": [{"title": "School strike for climate", "link": "https://wikipedia.org/wiki/School_strike_for_climate"}]}, {"year": "2022", "text": "The 2022 Sri Lankan protests begins amidst Sri Lanka's economic collapse.", "html": "2022 - The <a href=\"https://wikipedia.org/wiki/2022_Sri_Lankan_protests\" title=\"2022 Sri Lankan protests\">2022 Sri Lankan protests</a> begins amidst <a href=\"https://wikipedia.org/wiki/2019%E2%80%93present_Sri_Lankan_economic_crisis\" class=\"mw-redirect\" title=\"2019-present Sri Lankan economic crisis\">Sri Lanka's economic collapse</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2022_Sri_Lankan_protests\" title=\"2022 Sri Lankan protests\">2022 Sri Lankan protests</a> begins amidst <a href=\"https://wikipedia.org/wiki/2019%E2%80%93present_Sri_Lankan_economic_crisis\" class=\"mw-redirect\" title=\"2019-present Sri Lankan economic crisis\">Sri Lanka's economic collapse</a>.", "links": [{"title": "2022 Sri Lankan protests", "link": "https://wikipedia.org/wiki/2022_Sri_Lankan_protests"}, {"title": "2019-present Sri Lankan economic crisis", "link": "https://wikipedia.org/wiki/2019%E2%80%93present_Sri_Lankan_economic_crisis"}]}], "Births": [{"year": "1493", "text": "<PERSON>, French captain and diplomat (d. 1567)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French captain and diplomat (d. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French captain and diplomat (d. 1567)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1516", "text": "<PERSON><PERSON><PERSON>, Safavid prince (d. 1550)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Safavid prince (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Safavid prince (d. 1550)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1582", "text": "<PERSON>, English theologian and controversialist (d. 1645)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and controversialist (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and controversialist (d. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1591", "text": "<PERSON>, French missionary (d. 1660)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1638", "text": "Shu<PERSON>hi Emperor of China (d. 1661)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/Shunzhi_Emperor\" title=\"Shunzhi Emperor\">Shunzhi Emperor</a> of China (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shunzhi_Emperor\" title=\"Shunzhi Emperor\">Shunzhi Emperor</a> of China (d. 1661)", "links": [{"title": "Shunzhi Emperor", "link": "https://wikipedia.org/wiki/Shunz<PERSON>_Emperor"}]}, {"year": "1666", "text": "<PERSON>, German architect, designed the Dresden Frauenkirche (d. 1738)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A4hr\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/Dresden_Frauenkirche\" class=\"mw-redirect\" title=\"Dresden Frauenkirche\">Dresden Frauenkirche</a> (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A4hr\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/Dresden_Frauenkirche\" class=\"mw-redirect\" title=\"Dresden Frauenkirche\">Dresden Frauenkirche</a> (d. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_B%C3%A4hr"}, {"title": "Dresden Frauenkirche", "link": "https://wikipedia.org/wiki/Dresden_Frauenkirche"}]}, {"year": "1754", "text": "<PERSON>, Scottish surgeon and botanist (d. 1842)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and botanist (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and botanist (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON>, American general, judge, and politician, 7th President of the United States (d. 1845)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, judge, and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, judge, and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1779", "text": "<PERSON>, 2nd Viscount <PERSON>, English politician, Prime Minister of the United Kingdom (d. 1848)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON>\" title=\"<PERSON>, 2nd Viscount Melbourne\"><PERSON>, 2nd Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1848)", "links": [{"title": "<PERSON>, 2nd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1790", "text": "<PERSON>, German mathematician and academic (d. 1861)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Ludwig Immanuel Magnus\"><PERSON><PERSON></a>, German mathematician and academic (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ludwig Immanuel Magnus\"><PERSON></a>, German mathematician and academic (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, English author and publisher (d. 1873)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, English author and publisher (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, English author and publisher (d. 1873)", "links": [{"title": "<PERSON> (publisher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)"}]}, {"year": "1809", "text": "<PERSON>, American-Liberian historian and politician, 1st President of Liberia (d. 1876)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Liberian historian and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Liberian historian and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1813", "text": "<PERSON>, English physician and epidemiologist (d. 1858)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and epidemiologist (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and epidemiologist (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, Austrian physicist and chemist (d. 1895)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist and chemist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist and chemist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, Scottish theologian and author (d. 1892)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish theologian and author (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish theologian and author (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, French priest, founded the Missionaries of the Sacred Heart (d. 1907)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Missionaries_of_the_Sacred_Heart\" title=\"Missionaries of the Sacred Heart\">Missionaries of the Sacred Heart</a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Missionaries_of_the_Sacred_Heart\" title=\"Missionaries of the Sacred Heart\">Missionaries of the Sacred Heart</a> (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Missionaries of the Sacred Heart", "link": "https://wikipedia.org/wiki/Missionaries_of_the_Sacred_Heart"}]}, {"year": "1830", "text": "<PERSON>, German author, poet, and playwright, Nobel Prize laureate (d. 1914)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON>, French geographer and anarchist (d. 1905)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/%C3%89lis%C3%A9e_Reclus\" title=\"<PERSON><PERSON><PERSON> Reclus\"><PERSON><PERSON><PERSON></a>, French geographer and anarchist (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lis%C3%A9e_Reclus\" title=\"<PERSON><PERSON><PERSON> Reclus\"><PERSON><PERSON><PERSON></a>, French geographer and anarchist (d. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lis%C3%A9e_Reclus"}]}, {"year": "1831", "text": "Saint <PERSON><PERSON>, Italian missionary and saint (d. 1881)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/Saint_Daniele_Comboni\" class=\"mw-redirect\" title=\"Saint Daniele Comboni\">Saint <PERSON></a>, Italian missionary and saint (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Daniele_Comboni\" class=\"mw-redirect\" title=\"Saint Daniele Comboni\"><PERSON> <PERSON></a>, Italian missionary and saint (d. 1881)", "links": [{"title": "Saint <PERSON>", "link": "https://wikipedia.org/wiki/Saint_Daniel<PERSON>_Comboni"}]}, {"year": "1835", "text": "<PERSON>, Austrian composer and conductor (d. 1916)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Russian cellist, composer, and conductor (d. 1889)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian cellist, composer, and conductor (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian cellist, composer, and conductor (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, American lawyer and politician, Governor of Arkansas (d. 1916)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1845", "text": "<PERSON><PERSON>, African-American educator, writer and activist (d. 1949)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American educator, writer and activist (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American educator, writer and activist (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, Scottish archaeologist and scholar (d. 1939)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish archaeologist and scholar (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish archaeologist and scholar (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, <PERSON>, Anglo-Irish landowner, playwright, and translator (d. 1932)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Augusta,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Lady <PERSON></a>, Anglo-Irish landowner, playwright, and translator (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Lady <PERSON></a>, Anglo-Irish landowner, playwright, and translator (d. 1932)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/Augusta,_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, German physiologist and physician, Nobel Prize laureate (d. 1917)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1857", "text": "<PERSON>, Norwegian businessman and politician, 1st Prime Minister of Norway (d. 1925)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Norway", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Norway"}]}, {"year": "1858", "text": "<PERSON>, American botanist and academic, co-founded the American Society for Horticultural Science (d. 1954)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Liberty Hyde Bailey\"><PERSON></a>, American botanist and academic, co-founded the <a href=\"https://wikipedia.org/wiki/American_Society_for_Horticultural_Science\" title=\"American Society for Horticultural Science\">American Society for Horticultural Science</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hyde_<PERSON>\" title=\"Liberty Hyde Bailey\"><PERSON></a>, American botanist and academic, co-founded the <a href=\"https://wikipedia.org/wiki/American_Society_for_Horticultural_Science\" title=\"American Society for Horticultural Science\">American Society for Horticultural Science</a> (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "American Society for Horticultural Science", "link": "https://wikipedia.org/wiki/American_Society_for_Horticultural_Science"}]}, {"year": "1866", "text": "<PERSON>, Australian miner and politician (d. 1948)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian miner and politician (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian miner and politician (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, English mathematician (d. 1944)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON>, Polish scholar and politician, President of the Republic of Poland (d. 1953)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_W<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish scholar and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Poland\" title=\"List of heads of state of Poland\">President of the Republic of Poland</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_W<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish scholar and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Poland\" title=\"List of heads of state of Poland\">President of the Republic of Poland</a> (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "List of heads of state of Poland", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Poland"}]}, {"year": "1874", "text": "<PERSON>, American journalist and politician, United States Secretary of the Interior (d. 1952)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Iranian Shah (d. 1944)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON> <PERSON> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON> <PERSON> (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American biochemist (d. 1963)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Danish artist (d. 1940)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish artist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish artist (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American runner (d. 1986)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Brazilian sociologist, anthropologist, historian and writer (d. 1987)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian sociologist, anthropologist, historian and writer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian sociologist, anthropologist, historian and writer (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Irish-American actor (d. 1979)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, English-American actor (d. 1985)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27Malley\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English-American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27Malley\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English-American actor (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON>_<PERSON>%27Malley"}]}, {"year": "1905", "text": "<PERSON><PERSON>, German lawyer and judge (d. 1944)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and judge (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, German lawyer and judge (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Swedish actress and singer (d. 1981)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nder\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actress and singer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nder\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actress and singer (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Flemish journalist and author (d. 1979)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish journalist and author (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish journalist and author (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, American blues singer-songwriter and guitarist (d. 1982)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>\" title=\"Light<PERSON>' <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American blues singer-songwriter and guitarist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>nin%27_<PERSON>\" title=\"Light<PERSON>' <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American blues singer-songwriter and guitarist (d. 1982)", "links": [{"title": "Lightnin' <PERSON>", "link": "https://wikipedia.org/wiki/Lightnin%27_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 1994)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English race car driver (d. 2002)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Jr., American actor and pilot (d. 2009)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor and pilot (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor and pilot (d. 2009)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Kosovar commander and politician, President of Kosovo (d. 2001)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kosovar commander and politician, <a href=\"https://wikipedia.org/wiki/President_of_Kosovo\" title=\"President of Kosovo\">President of Kosovo</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kosovar commander and politician, <a href=\"https://wikipedia.org/wiki/President_of_Kosovo\" title=\"President of Kosovo\">President of Kosovo</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Kosovo", "link": "https://wikipedia.org/wiki/President_of_Kosovo"}]}, {"year": "1916", "text": "<PERSON>, American trumpet player, bandleader, and actor (d. 1983)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, bandleader, and actor (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, bandleader, and actor (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American author and critic (d. 1987)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian ice hockey player, coach, and manager (d. 1987)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ch"}]}, {"year": "1919", "text": "<PERSON>, American actor (d. 2002)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American physician and academic, Nobel Prize laureate (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American television writer and producer (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pugh\" title=\"<PERSON><PERSON> Pugh\"><PERSON><PERSON></a>, American television writer and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pugh\" title=\"<PERSON><PERSON> Pugh\"><PERSON><PERSON></a>, American television writer and producer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Pakistani pilot and former Deputy Chief of Air Staff of the PAF (d. 2007)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, Pakistani pilot and former <a href=\"https://wikipedia.org/wiki/Vice_Chief_of_the_Air_Staff_(Pakistan)\" title=\"Vice Chief of the Air Staff (Pakistan)\">Deputy Chief of Air Staff of the PAF</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, Pakistani pilot and former <a href=\"https://wikipedia.org/wiki/Vice_Chief_of_the_Air_Staff_(Pakistan)\" title=\"Vice Chief of the Air Staff (Pakistan)\">Deputy Chief of Air Staff of the PAF</a> (d. 2007)", "links": [{"title": "Khyber Khan", "link": "https://wikipedia.org/wiki/K<PERSON>ber_Khan"}, {"title": "Vice Chief of the Air Staff (Pakistan)", "link": "https://wikipedia.org/wiki/Vice_Chief_of_the_Air_Staff_(Pakistan)"}]}, {"year": "1926", "text": "<PERSON>, American composer and academic  (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and academic (d. 2019)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American football player and coach (d. 1983)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, French actor, director, and screenwriter (d. 2000)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian Mar<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_musician)\" class=\"mw-redirect\" title=\"<PERSON> (country musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_musician)\" class=\"mw-redirect\" title=\"<PERSON> (country musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2010)", "links": [{"title": "<PERSON> (country musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_musician)"}]}, {"year": "1928", "text": "<PERSON>, American clarinetist and saxophonist (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinetist and saxophonist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinetist and saxophonist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ber"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Belarusian-Russian physicist and academic, Nobel Prize laureate (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1932", "text": "<PERSON>, American astronaut and pilot (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut and pilot (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut and pilot (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Turkish-American record producer (d. 2006)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-American record producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-American record producer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American lawyer and judge (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, French actor, director, and screenwriter (d. 2004)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Indian politician (d. 2006)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ram"}]}, {"year": "1935", "text": "<PERSON>, American actor", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American pastor and television host", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ggart"}]}, {"year": "1936", "text": "<PERSON>, American songwriter (d. 1986)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>field"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Russian environmentalist and author (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian environmentalist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian environmentalist and author (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English author, poet, and playwright (d. 2016)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English mountaineer (d. 1986)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English politician, Secretary of State for Health (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Secretary of State for Health", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Health"}]}, {"year": "1940", "text": "<PERSON>, American bassist (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and musician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Love\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, child internee and revolutionary martyr (d. 1949)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, child internee and revolutionary martyr (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Song Z<PERSON>hong\"><PERSON></a>, child internee and revolutionary martyr (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Song_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian actor, director, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, English actress, screenwriter, and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress, screenwriter, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress, screenwriter, and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ynda_La_Plante"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, English bishop (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (d. 2014)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American musician and record producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Stone\"><PERSON><PERSON> <PERSON></a>, American musician and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Stone\"><PERSON><PERSON> <PERSON></a>, American musician and record producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Taiwanese runner", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Taiwanese runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Taiwanese runner", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1944", "text": "<PERSON>, French director and screenwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian director, producer, and screenwriter (d. 1993)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American baseball player and coach (d. 2003)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English-Irish footballer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1946)\" title=\"<PERSON> (footballer, born 1946)\"><PERSON></a>, English-Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1946)\" title=\"<PERSON> (footballer, born 1946)\"><PERSON></a>, English-Irish footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1946)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1946)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oder"}]}, {"year": "1948", "text": "<PERSON>, American author and activist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian diplomat (d. 2003)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian diplomat (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>lo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian diplomat (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9rg<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, <PERSON> of Liverpool, English politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Liverpool\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Liverpool\"><PERSON>, Baron <PERSON> of Liverpool</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Liverpool\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Liverpool\"><PERSON>, Baron <PERSON> of Liverpool</a>, English politician", "links": [{"title": "<PERSON>, <PERSON> of Liverpool", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Liverpool"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Scottish soprano and actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish soprano and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American runner and businessman", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and businessman", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Pakistani_cricketer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Pakistani cricketer)\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Pakistani_cricketer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Pakistani cricketer)\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON> (Pakistani cricketer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Pakistani_cricketer)"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese-American actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Almeida\" title=\"<PERSON><PERSON><PERSON><PERSON> Almeida\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Almeida\" title=\"<PERSON><PERSON><PERSON><PERSON> Almeida\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese-American actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_de_Almeida"}]}, {"year": "1957", "text": "<PERSON>, American actress and activist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Park_Overall\" title=\"Park Overall\"><PERSON> Overall</a>, American actress and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Park_Overall\" title=\"Park Overall\"><PERSON></a>, American actress and activist", "links": [{"title": "Park Overall", "link": "https://wikipedia.org/wiki/Park_Overall"}]}, {"year": "1959", "text": "<PERSON>, American baseball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Finnish director and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish director and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Italian-American model and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American model and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Nigerian poet and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian poet and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American tennis player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American basketball player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Arby\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Arby\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_D%27Arby"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American singer-songwriter, musician, and television personality", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, musician, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, musician, and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American singer-songwriter and musician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1964", "text": "<PERSON>, Belgian politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Indian epidemiologist, author, and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian epidemiologist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian epidemiologist, author, and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Gupta"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Japanese manga artist and creator of Sailor Moon", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist and creator of <a href=\"https://wikipedia.org/wiki/Sailor_Moon\" title=\"Sailor Moon\">Sailor Moon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist and creator of <a href=\"https://wikipedia.org/wiki/<PERSON>_Moon\" title=\"Sailor Moon\">Sailor Moon</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Moon"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Japanese singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Italian singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Gianluca_Festa\" title=\"Gianluca Festa\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gian<PERSON><PERSON>_Festa\" title=\"Gianluca Festa\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Festa"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese jockey", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Take\" title=\"Yu<PERSON>ka Take\"><PERSON><PERSON><PERSON></a>, Japanese jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Take\" title=\"Yu<PERSON><PERSON> Take\"><PERSON><PERSON><PERSON></a>, Japanese jockey", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yu<PERSON><PERSON>_Take"}]}, {"year": "1970", "text": "<PERSON>, American speed skater", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English long jumper", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and musician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, German chef", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German chef", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Longoria"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "will.i.am, American rapper, producer, and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Will.i.am\" title=\"Will.i.am\">will.i.am</a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Will.i.am\" title=\"Will.i.am\">will.i.am</a>, American rapper, producer, and actor", "links": [{"title": "Will.i.am", "link": "https://wikipedia.org/wiki/Will.i.am"}]}, {"year": "1976", "text": "<PERSON>, Canadian actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American musician, DJ, director, and visual artist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, DJ, director, and visual artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, DJ, director, and visual artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Japanese-American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Indian military officer (d. 2008)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian military officer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian military officer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese competitive eater", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese competitive eater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese competitive eater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, New Zealand cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> By<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>num"}]}, {"year": "1981", "text": "<PERSON>, American rapper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Buck\" title=\"<PERSON> Buck\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Buck\" title=\"<PERSON> Buck\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, German-Finnish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Finnish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ssell"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Estonian skier", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jens_Salum%C3%A4e"}]}, {"year": "1982", "text": "<PERSON>, Kenyan runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Umut_Bulut\" title=\"Umut Bulut\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umut_Bulut\" title=\"Umut Bulut\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umut_<PERSON>ulut"}]}, {"year": "1983", "text": "<PERSON>, Australian cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lou"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Salvadoran swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Salvadoran swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Salvadoran swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Irish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON> <PERSON>, Indian rapper, producer, and actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Yo <PERSON>\"><PERSON></a>, Indian rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Yo <PERSON>\"><PERSON></a>, Indian rapper, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, French-Algerian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Algerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian speed skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vasileiadis\" title=\"<PERSON><PERSON><PERSON> Vasileiadis\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>asileiadis\" title=\"<PERSON><PERSON><PERSON> Vasileiadis\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>s_Vasileiadis"}]}, {"year": "1985", "text": "<PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player and coach", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor and model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Danish ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American rapper, comedian, and actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Dick<PERSON>\" title=\"<PERSON> Dicky\"><PERSON></a>, American rapper, comedian, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Dick<PERSON>\" title=\"<PERSON> Dicky\"><PERSON></a>, American rapper, comedian, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/%C3%89ver_Guzm%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89ver_Guzm%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ver_Guzm%C3%A1n"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American sprinter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1989)\" title=\"<PERSON><PERSON> (footballer, born 1989)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1989)\" title=\"<PERSON><PERSON> (footballer, born 1989)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1989)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1989)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wachs\" title=\"<PERSON><PERSON><PERSON> Wachs\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wachs\" title=\"<PERSON><PERSON><PERSON> Wachs\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>achs"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>von <PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Irish professional wrestler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/JD_<PERSON><PERSON>\" title=\"J<PERSON>\"><PERSON><PERSON></a>, Irish professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/JD_<PERSON><PERSON><PERSON>\" title=\"J<PERSON>\">J<PERSON></a>, Irish professional wrestler", "links": [{"title": "JD <PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Freeman\" title=\"Devon<PERSON> Freeman\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Freeman\" title=\"<PERSON><PERSON> Freeman\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Freeman"}]}, {"year": "1993", "text": "<PERSON><PERSON>, British actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Serbian tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksandr<PERSON>_Kruni%C4%87"}]}, {"year": "1993", "text": "<PERSON>, French footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gay\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American comedian", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American professional wrestler", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Scottish sports shooter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>cIntosh\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish sports shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>cIntosh\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish sports shooter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>sh"}]}, {"year": "1996", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Jinjin\" title=\"Jinjin\"><PERSON><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jinjin\" title=\"Jinjin\"><PERSON><PERSON></a>, South Korean singer and actor", "links": [{"title": "Jinjin", "link": "https://wikipedia.org/wiki/Jinjin"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Russian-Bulgarian singer-songwriter", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Bulgarian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Bulgarian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ellie Leach\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American football player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American football player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "44 BC", "text": "<PERSON>, Roman general and statesman (b. 100 BC)", "html": "44 BC - 44 BC - <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON></a>, Roman general and statesman (b. 100 BC)", "no_year_html": "44 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_Caesar\" title=\"Julius Caesar\"><PERSON></a>, Roman general and statesman (b. 100 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "220", "text": "<PERSON>, Chinese general, warlord and statesman", "html": "220 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cao <PERSON>\"><PERSON></a>, Chinese general, warlord and statesman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cao <PERSON>\"><PERSON></a>, Chinese general, warlord and statesman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "493", "text": "<PERSON><PERSON><PERSON><PERSON>, first king of Italy after the fall of the Western Roman Empire (b. 433)", "html": "493 - <a href=\"https://wikipedia.org/wiki/Odoacer\" title=\"Odo<PERSON>r\"><PERSON><PERSON><PERSON><PERSON></a>, first king of Italy after the fall of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a> (b. 433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>r\" title=\"O<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, first king of Italy after the fall of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a> (b. 433)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odoacer"}, {"title": "Western Roman Empire", "link": "https://wikipedia.org/wiki/Western_Roman_Empire"}]}, {"year": "963", "text": "<PERSON><PERSON>, Byzantine emperor", "html": "963 - <a href=\"https://wikipedia.org/wiki/Romanos_II\" title=\"Romanos II\"><PERSON><PERSON> II</a>, Byzantine emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Romanos_II\" title=\"Romanos II\"><PERSON><PERSON> II</a>, Byzantine emperor", "links": [{"title": "Romanos II", "link": "https://wikipedia.org/wiki/Romano<PERSON>_II"}]}, {"year": "990", "text": "<PERSON><PERSON><PERSON> (the Older), German nobleman", "html": "990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Older,_Count_of_Walbeck\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Older, Count of Walbeck\"><PERSON><PERSON><PERSON></a> (the Older), German <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Older,_Count_of_Walbeck\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> I the Older, Count of Walbeck\"><PERSON><PERSON><PERSON></a> (the Older), German <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "links": [{"title": "<PERSON><PERSON><PERSON> the <PERSON>, Count of Walbeck", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Older,_Count_<PERSON>_<PERSON><PERSON>"}, {"title": "Nobility", "link": "https://wikipedia.org/wiki/Nobility"}]}, {"year": "1124", "text": "<PERSON><PERSON><PERSON>, Bishop of Rochester", "html": "1124 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Rochester\" title=\"Bishop of Rochester\">Bishop of Rochester</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Rochester\" title=\"Bishop of Rochester\">Bishop of Rochester</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ernulf"}, {"title": "<PERSON> of Rochester", "link": "https://wikipedia.org/wiki/<PERSON>_of_Rochester"}]}, {"year": "1190", "text": "<PERSON> of Hainault, queen of <PERSON> of France (b. 1170)", "html": "1190 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Isabella of Hainault\"><PERSON> of Hainault</a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1170)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Isabella of Hainault\">Isabella of Hainault</a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1170)", "links": [{"title": "<PERSON> of Hainault", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1311", "text": "<PERSON>, Count of Brienne", "html": "1311 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Brienne\" title=\"<PERSON>, Count of Brienne\"><PERSON>, Count of Brienne</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Brienne\" title=\"<PERSON>, Count of Brienne\"><PERSON>, Count of Brienne</a>", "links": [{"title": "<PERSON>, Count of Brienne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1536", "text": "<PERSON><PERSON><PERSON><PERSON>, Ottoman politician, Grand Vizier of the Ottoman Empire (b. 1493)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/Pargal%C4%B1_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ottoman politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pargal%C4%B1_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ottoman politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1493)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pargal%C4%B1_<PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1575", "text": "<PERSON><PERSON><PERSON>, Italian organist and composer (b. 1527)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian organist and composer (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian organist and composer (b. 1527)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON><PERSON><PERSON>, Italian painter and poet (b. 1615)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and poet (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and poet (b. 1615)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON><PERSON><PERSON>, Italian priest and missionary (b. 1645)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/Eusebio_Kino\" title=\"<PERSON>use<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian priest and missionary (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>usebio_Kino\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian priest and missionary (b. 1645)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eusebio_Kino"}]}, {"year": "1820", "text": "<PERSON>, Austrian priest and saint (b. 1751)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian priest and saint (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian priest and saint (b. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Italian composer and theorist (b. 1760)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and theorist (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and theorist (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Finnish poet, physicist and meteorologist (b. 1805)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet, physicist and meteorologist (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet, physicist and meteorologist (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, English engineer and academic (b. 1819)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English mathematician and academic (b. 1814)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, English engineer and businessman (b. 1813)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Ottoman politician, Grand Vizier of the Ottoman Empire (b. 1874)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1927", "text": "<PERSON>, English-Australian politician, 7th Premier of Western Australia (b. 1858)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1937", "text": "<PERSON><PERSON> <PERSON><PERSON>, American short story writer, editor, and novelist (b. 1890)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_Lovecraft\" title=\"H. P. Lovecraft\"><PERSON><PERSON> <PERSON><PERSON></a>, American short story writer, editor, and novelist (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_Lovecraft\" title=\"H. P. Lovecraft\"><PERSON><PERSON> <PERSON><PERSON></a>, American short story writer, editor, and novelist (b. 1890)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON>._Lovecraft"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Russian-German painter (b. 1864)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-German painter (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-German painter (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American author and poet (b. 1894)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rachel Field\"><PERSON></a>, American author and poet (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rachel_<PERSON>\" title=\"Rachel Field\"><PERSON></a>, American author and poet (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rachel_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, German engineer (b. 1873)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German engineer (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German engineer (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American saxophonist and clarinet player (b. 1909)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1892)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1966", "text": "<PERSON>, American basketball player and coach (b. 1902)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English actor and screenwriter (b. 1888)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>eson\" title=\"<PERSON>eson\"><PERSON></a>, English actor and screenwriter (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eson"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian author and poet (b. 1897)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Tar<PERSON><PERSON>_V<PERSON>\" title=\"<PERSON>r<PERSON><PERSON> V<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian author and poet (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tar<PERSON><PERSON>_<PERSON>\" title=\"Tar<PERSON><PERSON> V<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian author and poet (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tar<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1975", "text": "<PERSON>, Greek-Argentinian businessman (b. 1906)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Argentinian businessman (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Argentinian businessman (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian author and activist (b. 1929)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and activist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and activist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Italian-American wrestler and referee (b. 1921)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American wrestler and referee (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American wrestler and referee (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antonino_Rocca"}]}, {"year": "1981", "text": "<PERSON>, French director and screenwriter (b. 1898)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Clair"}]}, {"year": "1983", "text": "<PERSON>, English author and critic (b. 1892)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> West\"><PERSON></a>, English author and critic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_West\" title=\"<PERSON> West\"><PERSON></a>, English author and critic (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Ukrainian general and spy (b. 1921)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian general and spy (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian general and spy (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Iranian-English journalist (b. 1958)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Farzad_Bazoft\" title=\"Farzad Bazoft\"><PERSON><PERSON></a>, Iranian-English journalist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Farzad_Bazoft\" title=\"Farzad Bazoft\"><PERSON><PERSON></a>, Iranian-English journalist (b. 1958)", "links": [{"title": "<PERSON><PERSON> Bazo<PERSON>", "link": "https://wikipedia.org/wiki/Farzad_Bazoft"}]}, {"year": "1990", "text": "<PERSON>, American football player and sportscaster (b. 1919)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American saxophonist, composer, and bandleader (b. 1906)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actress (b. 1925)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Hungarian-French painter (b. 1906)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-French painter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-French painter (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vasarely"}]}, {"year": "1998", "text": "<PERSON>, American pediatrician and author (b. 1903)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pediatrician and author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pediatrician and author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actress and singer (b. 1909)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "2003", "text": "<PERSON><PERSON>, English actress (b. 1911)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American television producer, created World's Wildest Police Videos (b. 1955)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer, created <i><a href=\"https://wikipedia.org/wiki/World%27s_Wildest_Police_Videos\" title=\"World's Wildest Police Videos\">World's Wildest Police Videos</a></i> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer, created <i><a href=\"https://wikipedia.org/wiki/World%27s_Wildest_Police_Videos\" title=\"World's Wildest Police Videos\">World's Wildest Police Videos</a></i> (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "World's Wildest Police Videos", "link": "https://wikipedia.org/wiki/World%27s_Wildest_Police_Videos"}]}, {"year": "2004", "text": "<PERSON>, French actor (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, New Zealand-American scientist and engineer (b. 1910)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_scientist)\" title=\"<PERSON> (rocket scientist)\"><PERSON></a>, New Zealand-American scientist and engineer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_scientist)\" title=\"<PERSON> (rocket scientist)\"><PERSON></a>, New Zealand-American scientist and engineer (b. 1910)", "links": [{"title": "<PERSON> (rocket scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_scientist)"}]}, {"year": "2004", "text": "<PERSON>, English-American chemist and academic, Nobel Prize laureate (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Georgian basketball player (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Otar_Korkia\" title=\"Otar Korkia\"><PERSON><PERSON></a>, Georgian basketball player (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Otar_Korkia\" title=\"Otar Korkia\"><PERSON><PERSON></a>, Georgian basketball player (b. 1923)", "links": [{"title": "<PERSON>tar <PERSON>", "link": "https://wikipedia.org/wiki/Otar_Korkia"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Greek lieutenant and politician, Prime Minister of Greece (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_<PERSON>llis"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "2006", "text": "<PERSON>, Canadian football player and referee (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Red_Storey\" title=\"Red Storey\"><PERSON></a>, Canadian football player and referee (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Storey\" title=\"Red Storey\"><PERSON></a>, Canadian football player and referee (b. 1918)", "links": [{"title": "Red Storey", "link": "https://wikipedia.org/wiki/Red_Storey"}]}, {"year": "2007", "text": "<PERSON>, American murderer (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American director and producer (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Jamaican singer-songwriter and producer (b. 1954)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican singer-songwriter and producer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican singer-songwriter and producer (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American astronaut and engineer (b. 1956)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American astronaut and engineer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American astronaut and engineer (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, First Indian woman to earn a pilot's license. (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Sa<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, First Indian woman to earn a pilot's license. (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, First Indian woman to earn a pilot's license. (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American actor, director, and producer (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Iraqi poet (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>aw<PERSON>\" title=\"<PERSON><PERSON><PERSON>Samaw<PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi poet (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-Samawi\" title=\"<PERSON><PERSON><PERSON>Samaw<PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi poet (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-<PERSON>aw<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American rapper (b. 1969)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dogg\"><PERSON></a>, American rapper (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dogg\"><PERSON></a>, American rapper (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, English singer and DJ (b. 1963)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Smiley_Culture\" title=\"Smiley Culture\">Smiley Culture</a>, English singer and DJ (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Smiley_Culture\" title=\"Smiley Culture\">Smiley Culture</a>, English singer and DJ (b. 1963)", "links": [{"title": "Smiley Culture", "link": "https://wikipedia.org/wiki/Smiley_Culture"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Welsh rugby player (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON><PERSON><PERSON> (rugby union)\"><PERSON><PERSON><PERSON></a>, Welsh rugby player (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON><PERSON><PERSON> (rugby union)\"><PERSON><PERSON><PERSON></a>, Welsh rugby player (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "2012", "text": "<PERSON>, American baseball player and manager (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American businessman and politician, Governor of Washington (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Washington\" class=\"mw-redirect\" title=\"Governor of Washington\">Governor of Washington</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Washington\" class=\"mw-redirect\" title=\"Governor of Washington\">Governor of Washington</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Washington", "link": "https://wikipedia.org/wiki/Governor_of_Washington"}]}, {"year": "2013", "text": "<PERSON>, English clarinet player (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clarinet player (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clarinet player (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>foot"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Australian actress (b. 1963)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English sociologist (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American drummer (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American comedian, actor, and author (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier and politician, United States Secretary of the Army (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Callaway\" title=\"<PERSON> Callaway\"><PERSON></a>, American soldier and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Army\" title=\"United States Secretary of the Army\">United States Secretary of the Army</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Callaway\" title=\"<PERSON> Callaway\"><PERSON></a>, American soldier and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Army\" title=\"United States Secretary of the Army\">United States Secretary of the Army</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>away"}, {"title": "United States Secretary of the Army", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Army"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, English chef, author, and television personality (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English chef, author, and television personality (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English chef, author, and television personality (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, South African politician (b. 1960)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Collins_Chabane"}]}, {"year": "2015", "text": "<PERSON>, English sculptor and educator (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, English sculptor and educator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, English sculptor and educator (b. 1928)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)"}]}, {"year": "2015", "text": "<PERSON>, American actress and dancer (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American political scientist and author (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, American political scientist and author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Curtis_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American bass player (b. 1955)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English voice actress and television and film producer (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English voice actress and television and film producer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English voice actress and television and film producer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, English historian and academic (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English historian and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English historian and academic (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Fijian rugby player (b. 1978)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian rugby player (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ben<PERSON>\"><PERSON><PERSON></a>, Fijian rugby player (b. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "2019", "text": "<PERSON>, American film and TV series writer (b. 1948)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film and TV series writer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film and TV series writer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Italian architect (b. 1927)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American vocal coach and singer (b. 1935)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American vocal coach and singer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American vocal coach and singer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}