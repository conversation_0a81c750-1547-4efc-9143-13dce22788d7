{"date": "January 18", "url": "https://wikipedia.org/wiki/January_18", "data": {"Events": [{"year": "474", "text": "Seven-year-old <PERSON> succeeds his maternal grandfather <PERSON> as Byzantine emperor. He dies ten months later.", "html": "474 - Seven-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(emperor)\" title=\"<PERSON> II (emperor)\"><PERSON> II</a> succeeds his maternal grandfather <a href=\"https://wikipedia.org/wiki/<PERSON>_I_(emperor)\" title=\"<PERSON> I (emperor)\"><PERSON> I</a> as <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a>. He dies ten months later.", "no_year_html": "Seven-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(emperor)\" title=\"<PERSON> II (emperor)\"><PERSON> II</a> succeeds his maternal grandfather <a href=\"https://wikipedia.org/wiki/<PERSON>_I_(emperor)\" title=\"<PERSON> I (emperor)\"><PERSON> I</a> as <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a>. He dies ten months later.", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)"}, {"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)"}, {"title": "Byzantine emperor", "link": "https://wikipedia.org/wiki/Byzantine_emperor"}]}, {"year": "532", "text": "Nika riots in Constantinople fail.", "html": "532 - <a href=\"https://wikipedia.org/wiki/Nika_riots\" title=\"Nika riots\">Nika riots</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> fail.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nika_riots\" title=\"Nika riots\">Nika riots</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> fail.", "links": [{"title": "Nika riots", "link": "https://wikipedia.org/wiki/Nika_riots"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}]}, {"year": "1126", "text": "Emperor <PERSON><PERSON> abdicates the Chinese throne in favour of his son Emperor <PERSON><PERSON>.", "html": "1126 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON></a> abdicates the Chinese throne in favour of his son <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" class=\"mw-redirect\" title=\"Emperor <PERSON> of Song\">Emperor <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON></a> abdicates the Chinese throne in favour of his son <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" class=\"mw-redirect\" title=\"Emperor <PERSON> of Song\">Emperor <PERSON><PERSON></a>.", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}, {"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1486", "text": "King <PERSON> of England marries <PERSON> of York, daughter of <PERSON>, uniting the House of Lancaster and the House of York.", "html": "1486 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"Henry VII of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/Elizabeth_of_York\" title=\"<PERSON> of York\"><PERSON> of York</a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> IV</a>, uniting the <a href=\"https://wikipedia.org/wiki/House_of_Lancaster\" title=\"House of Lancaster\">House of Lancaster</a> and the <a href=\"https://wikipedia.org/wiki/House_of_York\" title=\"House of York\">House of York</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Henry VII of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/Elizabeth_of_York\" title=\"Elizabeth of York\"><PERSON> of York</a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"Edward IV of England\"><PERSON> IV</a>, uniting the <a href=\"https://wikipedia.org/wiki/House_of_Lancaster\" title=\"House of Lancaster\">House of Lancaster</a> and the <a href=\"https://wikipedia.org/wiki/House_of_York\" title=\"House of York\">House of York</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of York", "link": "https://wikipedia.org/wiki/Elizabeth_of_York"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "House of Lancaster", "link": "https://wikipedia.org/wiki/House_of_Lancaster"}, {"title": "House of York", "link": "https://wikipedia.org/wiki/House_of_York"}]}, {"year": "1562", "text": "Pope <PERSON> reopens the Council of Trent for its third and final session.", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pius_IV\" title=\"Pope Pius IV\">Pope <PERSON> IV</a> reopens the <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a> for its third and final session.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pius_IV\" title=\"Pope Pius IV\">Pope <PERSON> IV</a> reopens the <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a> for its third and final session.", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Council of Trent", "link": "https://wikipedia.org/wiki/Council_of_Trent"}]}, {"year": "1586", "text": "The magnitude 7.9 Tenshō earthquake strikes Honshu, Japan, killing 8,000 people and triggering a tsunami.", "html": "1586 - The magnitude 7.9 <a href=\"https://wikipedia.org/wiki/1586_Tensh%C5%8D_earthquake\" title=\"1586 Tenshō earthquake\">Tenshō earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Honshu\" title=\"Honshu\">Honshu</a>, Japan, killing 8,000 people and triggering a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a>.", "no_year_html": "The magnitude 7.9 <a href=\"https://wikipedia.org/wiki/1586_Tensh%C5%8D_earthquake\" title=\"1586 Tenshō earthquake\">Tenshō earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Honshu\" title=\"Honshu\">Honshu</a>, Japan, killing 8,000 people and triggering a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a>.", "links": [{"title": "1586 Tenshō earthquake", "link": "https://wikipedia.org/wiki/1586_Tensh%C5%8D_earthquake"}, {"title": "Honshu", "link": "https://wikipedia.org/wiki/Honshu"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}]}, {"year": "1670", "text": "<PERSON> captures Panama.", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Panama_expedition\" title=\"<PERSON>'s Panama expedition\">captures</a> <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Panama_expedition\" title=\"<PERSON>'s Panama expedition\">captures</a> <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s Panama expedition", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Panama_expedition"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}]}, {"year": "1701", "text": "<PERSON> crowns himself King in Prussia in Königsberg.", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> I</a> crowns himself King in <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> in <a href=\"https://wikipedia.org/wiki/K%C3%B6nigsberg\" title=\"Königsberg\">Königsberg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> I</a> crowns himself King in <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> in <a href=\"https://wikipedia.org/wiki/K%C3%B6nigsberg\" title=\"Königsberg\">Königsberg</a>.", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>_of_Prussia"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Königsberg", "link": "https://wikipedia.org/wiki/K%C3%B6nigsberg"}]}, {"year": "1778", "text": "<PERSON> is the first known European to discover the Hawaiian Islands, which he names the \"Sandwich Islands\".", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first known European to discover the <a href=\"https://wikipedia.org/wiki/Hawaiian_Islands\" title=\"Hawaiian Islands\">Hawaiian Islands</a>, which he names the \"Sandwich Islands\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first known European to discover the <a href=\"https://wikipedia.org/wiki/Hawaiian_Islands\" title=\"Hawaiian Islands\">Hawaiian Islands</a>, which he names the \"Sandwich Islands\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hawaiian Islands", "link": "https://wikipedia.org/wiki/Hawaiian_Islands"}]}, {"year": "1788", "text": "The first elements of the First Fleet carrying 736 convicts from Great Britain to Australia arrive at Botany Bay.", "html": "1788 - The first elements of the <a href=\"https://wikipedia.org/wiki/First_Fleet\" title=\"First Fleet\">First Fleet</a> carrying 736 convicts from <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a> to Australia arrive at <a href=\"https://wikipedia.org/wiki/Botany_Bay\" title=\"Botany Bay\">Botany Bay</a>.", "no_year_html": "The first elements of the <a href=\"https://wikipedia.org/wiki/First_Fleet\" title=\"First Fleet\">First Fleet</a> carrying 736 convicts from <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a> to Australia arrive at <a href=\"https://wikipedia.org/wiki/Botany_Bay\" title=\"Botany Bay\">Botany Bay</a>.", "links": [{"title": "First Fleet", "link": "https://wikipedia.org/wiki/First_Fleet"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Botany Bay", "link": "https://wikipedia.org/wiki/Botany_Bay"}]}, {"year": "1806", "text": "<PERSON> surrenders the Dutch Cape Colony to the British.", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> surrenders the <a href=\"https://wikipedia.org/wiki/Dutch_Cape_Colony\" title=\"Dutch Cape Colony\">Dutch Cape Colony</a> to the British.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> surrenders the <a href=\"https://wikipedia.org/wiki/Dutch_Cape_Colony\" title=\"Dutch Cape Colony\">Dutch Cape Colony</a> to the British.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dutch Cape Colony", "link": "https://wikipedia.org/wiki/Dutch_Cape_Colony"}]}, {"year": "1866", "text": "Wesley College is established in Melbourne, Australia.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Wesley_College,_Melbourne\" title=\"Wesley College, Melbourne\">Wesley College</a> is established in Melbourne, Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wesley_College,_Melbourne\" title=\"Wesley College, Melbourne\">Wesley College</a> is established in Melbourne, Australia.", "links": [{"title": "Wesley College, Melbourne", "link": "https://wikipedia.org/wiki/Wesley_College,_Melbourne"}]}, {"year": "1871", "text": "<PERSON> of Germany is proclaimed <PERSON> in the Hall of Mirrors of the Palace of Versailles (France) towards the end of the Franco-Prussian War. <PERSON> already had the title of German Emperor since the constitution of 1 January 1871, but he had hesitated to accept the title.", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Wilhelm_I_of_Germany\" class=\"mw-redirect\" title=\"Wilhelm I of Germany\"><PERSON> of Germany</a> is proclaimed <i>Kaiser <PERSON></i> in the <a href=\"https://wikipedia.org/wiki/Hall_of_Mirrors\" title=\"Hall of Mirrors\">Hall of Mirrors</a> of the <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Palace of Versailles</a> (France) towards the end of the <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>. <PERSON> already had the title of <a href=\"https://wikipedia.org/wiki/German_Emperor\" title=\"German Emperor\">German Emperor</a> since the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_German_Confederation_1871\" class=\"mw-redirect\" title=\"Constitution of the German Confederation 1871\">constitution of 1 January 1871</a>, but he had hesitated to accept the title.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelm_I_of_Germany\" class=\"mw-redirect\" title=\"Wilhelm I of Germany\"><PERSON> of Germany</a> is proclaimed <i>Kaiser <PERSON></i> in the <a href=\"https://wikipedia.org/wiki/Hall_of_Mirrors\" title=\"Hall of Mirrors\">Hall of Mirrors</a> of the <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Palace of Versailles</a> (France) towards the end of the <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>. <PERSON> already had the title of <a href=\"https://wikipedia.org/wiki/German_Emperor\" title=\"German Emperor\">German Emperor</a> since the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_German_Confederation_1871\" class=\"mw-redirect\" title=\"Constitution of the German Confederation 1871\">constitution of 1 January 1871</a>, but he had hesitated to accept the title.", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/Wilhelm_I_of_Germany"}, {"title": "Hall of Mirrors", "link": "https://wikipedia.org/wiki/Hall_of_Mirrors"}, {"title": "Palace of Versailles", "link": "https://wikipedia.org/wiki/Palace_of_Versailles"}, {"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "German Emperor", "link": "https://wikipedia.org/wiki/German_Emperor"}, {"title": "Constitution of the German Confederation 1871", "link": "https://wikipedia.org/wiki/Constitution_of_the_German_Confederation_1871"}]}, {"year": "1886", "text": "Modern field hockey is born with the formation of The Hockey Association in England.", "html": "1886 - Modern <a href=\"https://wikipedia.org/wiki/Field_hockey\" title=\"Field hockey\">field hockey</a> is born with the formation of The Hockey Association in England.", "no_year_html": "Modern <a href=\"https://wikipedia.org/wiki/Field_hockey\" title=\"Field hockey\">field hockey</a> is born with the formation of The Hockey Association in England.", "links": [{"title": "Field hockey", "link": "https://wikipedia.org/wiki/Field_hockey"}]}, {"year": "1896", "text": "An X-ray generating machine is exhibited for the first time by <PERSON><PERSON> <PERSON><PERSON>.", "html": "1896 - An <a href=\"https://wikipedia.org/wiki/X-ray\" title=\"X-ray\">X-ray</a> <a href=\"https://wikipedia.org/wiki/X-ray_generator\" class=\"mw-redirect\" title=\"X-ray generator\">generating machine</a> is exhibited for the first time by <PERSON><PERSON> <PERSON><PERSON>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/X-ray\" title=\"X-ray\">X-ray</a> <a href=\"https://wikipedia.org/wiki/X-ray_generator\" class=\"mw-redirect\" title=\"X-ray generator\">generating machine</a> is exhibited for the first time by <PERSON><PERSON> L<PERSON> Smith.", "links": [{"title": "X-ray", "link": "https://wikipedia.org/wiki/X-ray"}, {"title": "X-ray generator", "link": "https://wikipedia.org/wiki/X-ray_generator"}]}, {"year": "1911", "text": "<PERSON> lands on the deck of the USS Pennsylvania anchored in San Francisco Bay, the first time an aircraft landed on a ship.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Eugene_<PERSON>._<PERSON>\" class=\"mw-redirect\" title=\"Eugene B. Ely\"><PERSON></a> lands on the deck of the <a href=\"https://wikipedia.org/wiki/USS_Pennsylvania_(ACR-4)\" title=\"USS Pennsylvania (ACR-4)\">USS <i>Pennsylvania</i></a> anchored in <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a>, the first time an aircraft landed on a ship.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eugene_B._<PERSON>\" class=\"mw-redirect\" title=\"Eugene B. Ely\"><PERSON></a> lands on the deck of the <a href=\"https://wikipedia.org/wiki/USS_Pennsylvania_(ACR-4)\" title=\"USS Pennsylvania (ACR-4)\">USS <i>Pennsylvania</i></a> anchored in <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a>, the first time an aircraft landed on a ship.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_<PERSON>._<PERSON>"}, {"title": "USS Pennsylvania (ACR-4)", "link": "https://wikipedia.org/wiki/USS_Pennsylvania_(ACR-4)"}, {"title": "San Francisco Bay", "link": "https://wikipedia.org/wiki/San_Francisco_Bay"}]}, {"year": "1913", "text": "First Balkan War: A Greek flotilla defeats the Ottoman Navy in the Naval Battle of Lemnos, securing the islands of the Northern Aegean Sea for Greece.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: A <a href=\"https://wikipedia.org/wiki/Hellenic_Navy\" title=\"Hellenic Navy\">Greek</a> flotilla defeats the <a href=\"https://wikipedia.org/wiki/Ottoman_Navy\" title=\"Ottoman Navy\">Ottoman Navy</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Lemnos_(1913)\" title=\"Battle of Lemnos (1913)\">Naval Battle of Lemnos</a>, securing the islands of the Northern <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean Sea</a> for Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: A <a href=\"https://wikipedia.org/wiki/Hellenic_Navy\" title=\"Hellenic Navy\">Greek</a> flotilla defeats the <a href=\"https://wikipedia.org/wiki/Ottoman_Navy\" title=\"Ottoman Navy\">Ottoman Navy</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Lemnos_(1913)\" title=\"Battle of Lemnos (1913)\">Naval Battle of Lemnos</a>, securing the islands of the Northern <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean Sea</a> for Greece.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Hellenic Navy", "link": "https://wikipedia.org/wiki/Hellenic_Navy"}, {"title": "Ottoman Navy", "link": "https://wikipedia.org/wiki/Ottoman_Navy"}, {"title": "Battle of Lemnos (1913)", "link": "https://wikipedia.org/wiki/Battle_of_Lemnos_(1913)"}, {"title": "Aegean Sea", "link": "https://wikipedia.org/wiki/Aegean_Sea"}]}, {"year": "1915", "text": "Japan issues the \"Twenty-One Demands\" to the Republic of China in a bid to increase its power in East Asia.", "html": "1915 - Japan issues the \"<a href=\"https://wikipedia.org/wiki/Twenty-One_Demands\" title=\"Twenty-One Demands\">Twenty-One Demands</a>\" to the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> in a bid to increase its power in East Asia.", "no_year_html": "Japan issues the \"<a href=\"https://wikipedia.org/wiki/Twenty-One_Demands\" title=\"Twenty-One Demands\">Twenty-One Demands</a>\" to the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> in a bid to increase its power in East Asia.", "links": [{"title": "Twenty-One Demands", "link": "https://wikipedia.org/wiki/Twenty-One_Demands"}, {"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}]}, {"year": "1919", "text": "World War I: The Paris Peace Conference opens in Versailles, France.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Paris_Peace_Conference,_1919\" class=\"mw-redirect\" title=\"Paris Peace Conference, 1919\">Paris Peace Conference</a> opens in <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Versailles</a>, France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Paris_Peace_Conference,_1919\" class=\"mw-redirect\" title=\"Paris Peace Conference, 1919\">Paris Peace Conference</a> opens in <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Versailles</a>, France.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Paris Peace Conference, 1919", "link": "https://wikipedia.org/wiki/Paris_Peace_Conference,_1919"}, {"title": "Palace of Versailles", "link": "https://wikipedia.org/wiki/Palace_of_Versailles"}]}, {"year": "1919", "text": "Igna<PERSON> becomes Prime Minister of the newly independent Poland.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Ignacy_<PERSON>_<PERSON>\" title=\"Ignacy <PERSON>\">Igna<PERSON></a> becomes Prime Minister of the newly independent Poland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignacy_<PERSON>_<PERSON>\" title=\"Ignacy <PERSON>\">Igna<PERSON></a> becomes Prime Minister of the newly independent Poland.", "links": [{"title": "<PERSON>gna<PERSON>", "link": "https://wikipedia.org/wiki/Igna<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "Alt Llobregat insurrection breaks out in Central Catalonia, Spain.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Alt_Llobregat_insurrection\" title=\"Alt Llobregat insurrection\">Alt Llobregat insurrection</a> breaks out in <a href=\"https://wikipedia.org/wiki/Central_Catalonia\" class=\"mw-redirect\" title=\"Central Catalonia\">Central Catalonia</a>, <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alt_Llobregat_insurrection\" title=\"Alt Llobregat insurrection\">Alt Llobregat insurrection</a> breaks out in <a href=\"https://wikipedia.org/wiki/Central_Catalonia\" class=\"mw-redirect\" title=\"Central Catalonia\">Central Catalonia</a>, <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a>.", "links": [{"title": "Alt Llobregat insurrection", "link": "https://wikipedia.org/wiki/Alt_Llobregat_insurrection"}, {"title": "Central Catalonia", "link": "https://wikipedia.org/wiki/Central_Catalonia"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}]}, {"year": "1941", "text": "World War II: British troops launch a general counter-offensive against Italian East Africa.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: British troops <a href=\"https://wikipedia.org/wiki/East_African_Campaign_(World_War_II)#Allied_counter-offensive\" class=\"mw-redirect\" title=\"East African Campaign (World War II)\">launch a general counter-offensive</a> against <a href=\"https://wikipedia.org/wiki/Italian_East_Africa\" title=\"Italian East Africa\">Italian East Africa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: British troops <a href=\"https://wikipedia.org/wiki/East_African_Campaign_(World_War_II)#Allied_counter-offensive\" class=\"mw-redirect\" title=\"East African Campaign (World War II)\">launch a general counter-offensive</a> against <a href=\"https://wikipedia.org/wiki/Italian_East_Africa\" title=\"Italian East Africa\">Italian East Africa</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "East African Campaign (World War II)", "link": "https://wikipedia.org/wiki/East_African_Campaign_(World_War_II)#Allied_counter-offensive"}, {"title": "Italian East Africa", "link": "https://wikipedia.org/wiki/Italian_East_Africa"}]}, {"year": "1943", "text": "Warsaw Ghetto Uprising: The first uprising of Jews in the Warsaw Ghetto.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising\" title=\"Warsaw Ghetto Uprising\">Warsaw Ghetto Uprising</a>: The first uprising of Jews in the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto\" title=\"Warsaw Ghetto\">Warsaw Ghetto</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising\" title=\"Warsaw Ghetto Uprising\">Warsaw Ghetto Uprising</a>: The first uprising of Jews in the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto\" title=\"Warsaw Ghetto\">Warsaw Ghetto</a>.", "links": [{"title": "Warsaw Ghetto Uprising", "link": "https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising"}, {"title": "Warsaw Ghetto", "link": "https://wikipedia.org/wiki/Warsaw_Ghetto"}]}, {"year": "1945", "text": "World War II: Liberation of Kraków, Poland by the Red Army.", "html": "1945 - World War II: Liberation of <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, Poland by the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>.", "no_year_html": "World War II: Liberation of <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, Poland by the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>.", "links": [{"title": "Kraków", "link": "https://wikipedia.org/wiki/Krak%C3%B3w"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1958", "text": "<PERSON>, the first Black Canadian National Hockey League player, makes his NHL debut with the Boston Bruins.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ree\" title=\"<PERSON>\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Black_Canadian\" class=\"mw-redirect\" title=\"Black Canadian\">Black Canadian</a> <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> player, makes his NHL debut with the <a href=\"https://wikipedia.org/wiki/Boston_Bruins\" title=\"Boston Bruins\">Boston Bruins</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ree\" title=\"<PERSON>\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Black_Canadian\" class=\"mw-redirect\" title=\"Black Canadian\">Black Canadian</a> <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> player, makes his NHL debut with the <a href=\"https://wikipedia.org/wiki/Boston_Bruins\" title=\"Boston Bruins\">Boston Bruins</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ree"}, {"title": "Black Canadian", "link": "https://wikipedia.org/wiki/Black_Canadian"}, {"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}, {"title": "Boston Bruins", "link": "https://wikipedia.org/wiki/Boston_Bruins"}]}, {"year": "1960", "text": "Capital Airlines Flight 20 crashes into a farm in Charles City County, Virginia, killing all 50 aboard, the third fatal Capital Airlines crash in as many years.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Capital_Airlines_Flight_20\" title=\"Capital Airlines Flight 20\">Capital Airlines Flight 20</a> crashes into a farm in <a href=\"https://wikipedia.org/wiki/Charles_City_County,_Virginia\" title=\"Charles City County, Virginia\">Charles City County, Virginia</a>, killing all 50 aboard, the third fatal <a href=\"https://wikipedia.org/wiki/Capital_Airlines_(United_States)\" title=\"Capital Airlines (United States)\">Capital Airlines</a> crash in as many years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Capital_Airlines_Flight_20\" title=\"Capital Airlines Flight 20\">Capital Airlines Flight 20</a> crashes into a farm in <a href=\"https://wikipedia.org/wiki/Charles_City_County,_Virginia\" title=\"Charles City County, Virginia\">Charles City County, Virginia</a>, killing all 50 aboard, the third fatal <a href=\"https://wikipedia.org/wiki/Capital_Airlines_(United_States)\" title=\"Capital Airlines (United States)\">Capital Airlines</a> crash in as many years.", "links": [{"title": "Capital Airlines Flight 20", "link": "https://wikipedia.org/wiki/Capital_Airlines_Flight_20"}, {"title": "Charles City County, Virginia", "link": "https://wikipedia.org/wiki/Charles_City_County,_Virginia"}, {"title": "Capital Airlines (United States)", "link": "https://wikipedia.org/wiki/Capital_Airlines_(United_States)"}]}, {"year": "1967", "text": "<PERSON>, the \"Boston Strangler\", is convicted of numerous crimes and is sentenced to life imprisonment.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the \"<a href=\"https://wikipedia.org/wiki/Boston_Strangler\" title=\"Boston Strangler\">Boston Strangler</a>\", is convicted of numerous crimes and is sentenced to life imprisonment.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the \"<a href=\"https://wikipedia.org/wiki/Boston_Strangler\" title=\"Boston Strangler\">Boston Strangler</a>\", is convicted of numerous crimes and is sentenced to life imprisonment.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Boston Strangler", "link": "https://wikipedia.org/wiki/Boston_Strangler"}]}, {"year": "1969", "text": "United Airlines Flight 266 crashes into Santa Monica Bay killing all 32 passengers and six crew members.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_266\" class=\"mw-redirect\" title=\"United Airlines Flight 266\">United Airlines Flight 266</a> crashes into <a href=\"https://wikipedia.org/wiki/Santa_Monica_Bay\" title=\"Santa Monica Bay\">Santa Monica Bay</a> killing all 32 passengers and six crew members.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_266\" class=\"mw-redirect\" title=\"United Airlines Flight 266\">United Airlines Flight 266</a> crashes into <a href=\"https://wikipedia.org/wiki/Santa_Monica_Bay\" title=\"Santa Monica Bay\">Santa Monica Bay</a> killing all 32 passengers and six crew members.", "links": [{"title": "United Airlines Flight 266", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_266"}, {"title": "Santa Monica Bay", "link": "https://wikipedia.org/wiki/Santa_Monica_Bay"}]}, {"year": "1972", "text": "Members of the Mukti Bahini lay down their arms to the government of the newly independent Bangladesh, a month after winning the war against the occupying Pakistan Army.", "html": "1972 - Members of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bahini\" title=\"<PERSON><PERSON><PERSON> Bahini\"><PERSON><PERSON><PERSON> Bahini</a> lay down their arms to the government of the newly independent <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, a month after winning the war against the occupying <a href=\"https://wikipedia.org/wiki/Pakistan_Army\" title=\"Pakistan Army\">Pakistan Army</a>.", "no_year_html": "Members of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bahin<PERSON>\" title=\"Mukt<PERSON> Bahini\"><PERSON><PERSON><PERSON> Bahini</a> lay down their arms to the government of the newly independent <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, a month after winning the war against the occupying <a href=\"https://wikipedia.org/wiki/Pakistan_Army\" title=\"Pakistan Army\">Pakistan Army</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mukti_<PERSON><PERSON>i"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "Pakistan Army", "link": "https://wikipedia.org/wiki/Pakistan_Army"}]}, {"year": "1974", "text": "A Disengagement of Forces agreement is signed between the Israeli and Egyptian governments, ending conflict on the Egyptian front of the Yom Kippur War.", "html": "1974 - <a href=\"https://wikipedia.orghttps://en.wikisource.org/wiki/Israel-Egypt_Disengagement_Treaty_of_1974\" class=\"extiw\" title=\"s:Israel-Egypt Disengagement Treaty of 1974\">A Disengagement of Forces agreement</a> is signed between the <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> and <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> governments, ending conflict on the Egyptian front of the <a href=\"https://wikipedia.org/wiki/Yom_Kippur_War\" title=\"Yom Kippur War\">Yom Kippur War</a>.", "no_year_html": "<a href=\"https://wikipedia.orghttps://en.wikisource.org/wiki/Israel-Egypt_Disengagement_Treaty_of_1974\" class=\"extiw\" title=\"s:Israel-Egypt Disengagement Treaty of 1974\">A Disengagement of Forces agreement</a> is signed between the <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> and <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> governments, ending conflict on the Egyptian front of the <a href=\"https://wikipedia.org/wiki/Yom_Kippur_War\" title=\"Yom Kippur War\">Yom Kippur War</a>.", "links": [{"title": "s:Israel-Egypt Disengagement Treaty of 1974", "link": "https://wikipedia.orghttps://en.wikisource.org/wiki/Israel-Egypt_Disengagement_Treaty_of_1974"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Yom Kippur War", "link": "https://wikipedia.org/wiki/Yom_Kippur_War"}]}, {"year": "1976", "text": "Lebanese Christian militias kill at least 1,000 in Karantina, Beirut.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Christianity_in_Lebanon\" title=\"Christianity in Lebanon\">Lebanese Christian</a> <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militias</a> <a href=\"https://wikipedia.org/wiki/Karantina_massacre\" title=\"Karantina massacre\">kill at least 1,000 in Karantina, Beirut</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christianity_in_Lebanon\" title=\"Christianity in Lebanon\">Lebanese Christian</a> <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militias</a> <a href=\"https://wikipedia.org/wiki/Karantina_massacre\" title=\"Karantina massacre\">kill at least 1,000 in Karantina, Beirut</a>.", "links": [{"title": "Christianity in Lebanon", "link": "https://wikipedia.org/wiki/Christianity_in_Lebanon"}, {"title": "Militia", "link": "https://wikipedia.org/wiki/Militia"}, {"title": "Karantina massacre", "link": "https://wikipedia.org/wiki/Karantina_massacre"}]}, {"year": "1977", "text": "Scientists at the Centers for Disease Control and Prevention announce they have identified a previously unknown bacterium as the cause of the mysterious Legionnaires' disease.", "html": "1977 - Scientists at the <a href=\"https://wikipedia.org/wiki/Centers_for_Disease_Control_and_Prevention\" title=\"Centers for Disease Control and Prevention\">Centers for Disease Control and Prevention</a> announce they have identified a previously unknown <a href=\"https://wikipedia.org/wiki/Bacterium\" class=\"mw-redirect\" title=\"Bacterium\">bacterium</a> as the cause of the mysterious <a href=\"https://wikipedia.org/wiki/Legionnaires%27_disease\" title=\"Legionnaires' disease\">Legionnaires' disease</a>.", "no_year_html": "Scientists at the <a href=\"https://wikipedia.org/wiki/Centers_for_Disease_Control_and_Prevention\" title=\"Centers for Disease Control and Prevention\">Centers for Disease Control and Prevention</a> announce they have identified a previously unknown <a href=\"https://wikipedia.org/wiki/Bacterium\" class=\"mw-redirect\" title=\"Bacterium\">bacterium</a> as the cause of the mysterious <a href=\"https://wikipedia.org/wiki/Legionnaires%27_disease\" title=\"Legionnaires' disease\">Legionnaires' disease</a>.", "links": [{"title": "Centers for Disease Control and Prevention", "link": "https://wikipedia.org/wiki/Centers_for_Disease_Control_and_Prevention"}, {"title": "Bacterium", "link": "https://wikipedia.org/wiki/Bacterium"}, {"title": "Legionnaires' disease", "link": "https://wikipedia.org/wiki/Legionnaires%27_disease"}]}, {"year": "1977", "text": "Australia's worst rail disaster occurs at Granville, Sydney, killing 83.", "html": "1977 - Australia's worst <a href=\"https://wikipedia.org/wiki/Granville_rail_disaster\" title=\"Granville rail disaster\">rail disaster</a> occurs at <a href=\"https://wikipedia.org/wiki/Granville,_New_South_Wales\" title=\"Granville, New South Wales\">Granville, Sydney</a>, killing 83.", "no_year_html": "Australia's worst <a href=\"https://wikipedia.org/wiki/Granville_rail_disaster\" title=\"Granville rail disaster\">rail disaster</a> occurs at <a href=\"https://wikipedia.org/wiki/Granville,_New_South_Wales\" title=\"Granville, New South Wales\">Granville, Sydney</a>, killing 83.", "links": [{"title": "Granville rail disaster", "link": "https://wikipedia.org/wiki/Granville_rail_disaster"}, {"title": "Granville, New South Wales", "link": "https://wikipedia.org/wiki/Granville,_New_South_Wales"}]}, {"year": "1977", "text": "SFR Yugoslavia's Prime minister, <PERSON><PERSON><PERSON><PERSON>, his wife and six others are killed in a plane crash in Bosnia and Herzegovina.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">SFR Yugoslavia's</a> Prime minister, <a href=\"https://wikipedia.org/wiki/D%C5%BE<PERSON>l_Bijedi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, his wife and six others are killed in a plane crash in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">SFR Yugoslavia's</a> Prime minister, <a href=\"https://wikipedia.org/wiki/D%C5%BEemal_Bijedi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, his wife and six others are killed in a plane crash in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "links": [{"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C5%BEemal_Bijedi%C4%87"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}]}, {"year": "1978", "text": "The European Court of Human Rights finds the United Kingdom's government guilty of mistreating prisoners in Northern Ireland, but not guilty of torture.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/European_Court_of_Human_Rights\" title=\"European Court of Human Rights\">European Court of Human Rights</a> finds the United Kingdom's government guilty of mistreating prisoners in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, but not guilty of <a href=\"https://wikipedia.org/wiki/Torture\" title=\"Torture\">torture</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Court_of_Human_Rights\" title=\"European Court of Human Rights\">European Court of Human Rights</a> finds the United Kingdom's government guilty of mistreating prisoners in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, but not guilty of <a href=\"https://wikipedia.org/wiki/Torture\" title=\"Torture\">torture</a>.", "links": [{"title": "European Court of Human Rights", "link": "https://wikipedia.org/wiki/European_Court_of_Human_Rights"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Torture", "link": "https://wikipedia.org/wiki/Torture"}]}, {"year": "1981", "text": "<PERSON> and <PERSON> parachute off a Houston skyscraper, becoming the first two people to BASE jump from objects in all four categories: buildings, antennae, spans (bridges), and earth (cliffs).", "html": "1981 - <PERSON> and <PERSON> parachute off a Houston skyscraper, becoming the first two people to <a href=\"https://wikipedia.org/wiki/BASE_jumping\" title=\"BASE jumping\">BASE jump</a> from objects in all four categories: buildings, antennae, spans (bridges), and earth (cliffs).", "no_year_html": "<PERSON> and <PERSON> parachute off a Houston skyscraper, becoming the first two people to <a href=\"https://wikipedia.org/wiki/BASE_jumping\" title=\"BASE jumping\">BASE jump</a> from objects in all four categories: buildings, antennae, spans (bridges), and earth (cliffs).", "links": [{"title": "BASE jumping", "link": "https://wikipedia.org/wiki/BASE_jumping"}]}, {"year": "1983", "text": "The International Olympic Committee restores <PERSON>'s Olympic medals to his family.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a> restores <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s Olympic medals to his family.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a> restores <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s Olympic medals to his family.", "links": [{"title": "International Olympic Committee", "link": "https://wikipedia.org/wiki/International_Olympic_Committee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "An Aerovías Sud Aviation Caravelle crashes on approach to Mundo Maya International Airport in Flores, Petén, Guatemala, killing all 94 people on board.", "html": "1986 - An <a href=\"https://wikipedia.org/wiki/Aerov%C3%ADas\" title=\"Aerovías\">Aerovías</a> <a href=\"https://wikipedia.org/wiki/Sud_Aviation_Caravelle\" title=\"Sud Aviation Caravelle\">Sud Aviation Caravelle</a> <a href=\"https://wikipedia.org/wiki/1986_Aerov%C3%ADas_Guatemala_air_crash\" class=\"mw-redirect\" title=\"1986 Aerovías Guatemala air crash\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Mundo_Maya_International_Airport\" title=\"Mundo Maya International Airport\">Mundo Maya International Airport</a> in <a href=\"https://wikipedia.org/wiki/Flores,_Pet%C3%A9n\" title=\"Flores, Petén\">Flores, Petén</a>, Guatemala, killing all 94 people on board.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Aerov%C3%ADas\" title=\"Aerovías\">Aerovías</a> <a href=\"https://wikipedia.org/wiki/Sud_Aviation_Caravelle\" title=\"Sud Aviation Caravelle\">Sud Aviation Caravelle</a> <a href=\"https://wikipedia.org/wiki/1986_Aerov%C3%ADas_Guatemala_air_crash\" class=\"mw-redirect\" title=\"1986 Aerovías Guatemala air crash\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Mundo_Maya_International_Airport\" title=\"Mundo Maya International Airport\">Mundo Maya International Airport</a> in <a href=\"https://wikipedia.org/wiki/Flores,_Pet%C3%A9n\" title=\"Flores, Petén\">Flores, Petén</a>, Guatemala, killing all 94 people on board.", "links": [{"title": "Aerovías", "link": "https://wikipedia.org/wiki/Aerov%C3%ADas"}, {"title": "Sud Aviation Caravelle", "link": "https://wikipedia.org/wiki/Sud_Aviation_Caravelle"}, {"title": "1986 Aerovías Guatemala air crash", "link": "https://wikipedia.org/wiki/1986_Aerov%C3%ADas_Guatemala_air_crash"}, {"title": "Mundo Maya International Airport", "link": "https://wikipedia.org/wiki/Mundo_Maya_International_Airport"}, {"title": "Flores, Petén", "link": "https://wikipedia.org/wiki/Flores,_Pet%C3%A9n"}]}, {"year": "1988", "text": "China Southwest Airlines Flight 4146 crashes near Chongqing Baishiyi Airport, killing all 98 passengers and 10 crew members.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/China_Southwest_Airlines_Flight_4146\" title=\"China Southwest Airlines Flight 4146\">China Southwest Airlines Flight 4146</a> crashes near <a href=\"https://wikipedia.org/wiki/Chongqing_Baishiyi_Airport\" title=\"Chongqing Baishiyi Airport\">Chongqing Baishiyi Airport</a>, killing all 98 passengers and 10 crew members.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Southwest_Airlines_Flight_4146\" title=\"China Southwest Airlines Flight 4146\">China Southwest Airlines Flight 4146</a> crashes near <a href=\"https://wikipedia.org/wiki/Chongqing_Baishiyi_Airport\" title=\"Chongqing Baishiyi Airport\">Chongqing Baishiyi Airport</a>, killing all 98 passengers and 10 crew members.", "links": [{"title": "China Southwest Airlines Flight 4146", "link": "https://wikipedia.org/wiki/China_Southwest_Airlines_Flight_4146"}, {"title": "Chongqing Baishiyi Airport", "link": "https://wikipedia.org/wiki/Chongqing_Baishiyi_Airport"}]}, {"year": "1990", "text": "Washington, D.C., Mayor <PERSON> is arrested for drug possession in an FBI sting.", "html": "1990 - <PERSON>, D.C., Mayor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#1979-1991:_Mayor_of_the_District_of_Columbia\" title=\"<PERSON>\">arrested for drug possession</a> in an <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> <a href=\"https://wikipedia.org/wiki/Sting_operation\" title=\"Sting operation\">sting</a>.", "no_year_html": "Washington, D.C., Mayor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#1979-1991:_Mayor_of_the_District_of_Columbia\" title=\"<PERSON>\">arrested for drug possession</a> in an <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> <a href=\"https://wikipedia.org/wiki/Sting_operation\" title=\"Sting operation\">sting</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>#1979-1991:_Mayor_of_the_District_of_Columbia"}, {"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}, {"title": "Sting operation", "link": "https://wikipedia.org/wiki/Sting_operation"}]}, {"year": "1993", "text": "<PERSON>, Jr. Day is officially observed for the first time in all 50 US states.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr._<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Jr. Day\"><PERSON>, Jr. Day</a> is officially observed for the first time in all 50 US states.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr._<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Jr. Day\"><PERSON>, Jr. <PERSON></a> is officially observed for the first time in all 50 US states.", "links": [{"title": "<PERSON>, Jr. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr._<PERSON>"}]}, {"year": "2002", "text": "The Sierra Leone Civil War is declared over.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Civil_War\" title=\"Sierra Leone Civil War\">Sierra Leone Civil War</a> is declared over.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Civil_War\" title=\"Sierra Leone Civil War\">Sierra Leone Civil War</a> is declared over.", "links": [{"title": "Sierra Leone Civil War", "link": "https://wikipedia.org/wiki/Sierra_Leone_Civil_War"}]}, {"year": "2003", "text": "A bushfire kills four people and destroys more than 500 homes in Canberra, Australia.", "html": "2003 - A <a href=\"https://wikipedia.org/wiki/2003_Canberra_bushfires\" title=\"2003 Canberra bushfires\">bushfire</a> kills four people and destroys more than 500 homes in <a href=\"https://wikipedia.org/wiki/Canberra\" title=\"Canberra\">Canberra</a>, Australia.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2003_Canberra_bushfires\" title=\"2003 Canberra bushfires\">bushfire</a> kills four people and destroys more than 500 homes in <a href=\"https://wikipedia.org/wiki/Canberra\" title=\"Canberra\">Canberra</a>, Australia.", "links": [{"title": "2003 Canberra bushfires", "link": "https://wikipedia.org/wiki/2003_Canberra_bushfires"}, {"title": "Canberra", "link": "https://wikipedia.org/wiki/Canberra"}]}, {"year": "2005", "text": "The Airbus A380, the world's largest commercial jet, is unveiled at a ceremony in Toulouse, France", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Airbus_A380\" title=\"Airbus A380\">Airbus A380</a>, the world's largest commercial jet, is unveiled at a ceremony in <a href=\"https://wikipedia.org/wiki/Toulouse\" title=\"Toulouse\">Toulouse</a>, France", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Airbus_A380\" title=\"Airbus A380\">Airbus A380</a>, the world's largest commercial jet, is unveiled at a ceremony in <a href=\"https://wikipedia.org/wiki/Toulouse\" title=\"Toulouse\">Toulouse</a>, France", "links": [{"title": "Airbus A380", "link": "https://wikipedia.org/wiki/Airbus_A380"}, {"title": "Toulouse", "link": "https://wikipedia.org/wiki/Toulouse"}]}, {"year": "2007", "text": "The strongest storm in the United Kingdom in 17 years kills 14 people and Germany sees the worst storm since 1999 with 13 deaths. Cyclone <PERSON><PERSON><PERSON> causes at least 44 deaths across 20 countries in Western Europe.", "html": "2007 - The strongest storm in the United Kingdom in 17 years kills 14 people and Germany sees the worst storm since <a href=\"https://wikipedia.org/wiki/1999\" title=\"1999\">1999</a> with 13 deaths. <a href=\"https://wikipedia.org/wiki/Cyclone_Kyrill\" title=\"Cyclone Kyrill\">Cyclone <PERSON></a> causes at least 44 deaths across 20 countries in Western Europe.", "no_year_html": "The strongest storm in the United Kingdom in 17 years kills 14 people and Germany sees the worst storm since <a href=\"https://wikipedia.org/wiki/1999\" title=\"1999\">1999</a> with 13 deaths. <a href=\"https://wikipedia.org/wiki/Cyclone_Kyrill\" title=\"Cyclone Kyrill\">Cyclone <PERSON></a> causes at least 44 deaths across 20 countries in Western Europe.", "links": [{"title": "1999", "link": "https://wikipedia.org/wiki/1999"}, {"title": "Cyclone <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2008", "text": "The Euphronios Krater is unveiled in Rome after being returned to Italy by the Metropolitan Museum of Art.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/Euphronios_Krater\" title=\"Euphronios Krater\">Euphronios Krater</a> is unveiled in <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> after being returned to Italy by the <a href=\"https://wikipedia.org/wiki/Metropolitan_Museum_of_Art\" title=\"Metropolitan Museum of Art\">Metropolitan Museum of Art</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Euphronios_Krater\" title=\"Euphronios Krater\">Euphronios Krater</a> is unveiled in <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> after being returned to Italy by the <a href=\"https://wikipedia.org/wiki/Metropolitan_Museum_of_Art\" title=\"Metropolitan Museum of Art\">Metropolitan Museum of Art</a>.", "links": [{"title": "Eup<PERSON>oni<PERSON>", "link": "https://wikipedia.org/wiki/Euphronios_Krater"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}, {"title": "Metropolitan Museum of Art", "link": "https://wikipedia.org/wiki/Metropolitan_Museum_of_Art"}]}, {"year": "2012", "text": "More than 115,000 websites engage in an online protest against the Stop Online Piracy Act and the Protect IP Act in the US.[citation needed] The websites involved viewed the laws as infringing on the right to free speech and many of them temporarily shut down in protest.", "html": "2012 - More than 115,000 websites engage in an <a href=\"https://wikipedia.org/wiki/Protests_against_SOPA_and_PIPA\" title=\"Protests against SOPA and PIPA\">online protest</a> against the <a href=\"https://wikipedia.org/wiki/Stop_Online_Piracy_Act\" title=\"Stop Online Piracy Act\">Stop Online Piracy Act</a> and the <a href=\"https://wikipedia.org/wiki/PROTECT_IP_Act\" title=\"PROTECT IP Act\">Protect IP Act</a> in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">US</a>. The websites involved viewed the laws as infringing on the right to <a href=\"https://wikipedia.org/wiki/Freedom_of_speech\" title=\"Freedom of speech\">free speech</a> and many of them temporarily shut down in protest.", "no_year_html": "More than 115,000 websites engage in an <a href=\"https://wikipedia.org/wiki/Protests_against_SOPA_and_PIPA\" title=\"Protests against SOPA and PIPA\">online protest</a> against the <a href=\"https://wikipedia.org/wiki/Stop_Online_Piracy_Act\" title=\"Stop Online Piracy Act\">Stop Online Piracy Act</a> and the <a href=\"https://wikipedia.org/wiki/PROTECT_IP_Act\" title=\"PROTECT IP Act\">Protect IP Act</a> in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">US</a>. The websites involved viewed the laws as infringing on the right to <a href=\"https://wikipedia.org/wiki/Freedom_of_speech\" title=\"Freedom of speech\">free speech</a> and many of them temporarily shut down in protest.", "links": [{"title": "Protests against SOPA and PIPA", "link": "https://wikipedia.org/wiki/Protests_against_SOPA_and_PIPA"}, {"title": "Stop Online Piracy Act", "link": "https://wikipedia.org/wiki/Stop_Online_Piracy_Act"}, {"title": "PROTECT IP Act", "link": "https://wikipedia.org/wiki/PROTECT_IP_Act"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Freedom of speech", "link": "https://wikipedia.org/wiki/Freedom_of_speech"}]}, {"year": "2018", "text": "A bus catches fire on the Samara-Shymkent road in Yrgyz District, Aktobe, Kazakhstan. The fire kills 52 passengers, with three passengers and two drivers escaping.", "html": "2018 - A bus catches fire on the <a href=\"https://wikipedia.org/wiki/Samara\" title=\"Samara\">Samara</a>-<a href=\"https://wikipedia.org/wiki/Shymkent\" title=\"Shymkent\">Shymkent</a> road in <a href=\"https://wikipedia.org/wiki/Yrgyz_District\" title=\"Yrgyz District\">Yrgyz District</a>, <a href=\"https://wikipedia.org/wiki/Aktobe_Region\" title=\"Aktobe Region\">Aktobe</a>, Kazakhstan. The fire kills 52 passengers, with three passengers and two drivers escaping.", "no_year_html": "A bus catches fire on the <a href=\"https://wikipedia.org/wiki/Samara\" title=\"Samara\">Samara</a>-<a href=\"https://wikipedia.org/wiki/Shymkent\" title=\"Shymkent\">Shymkent</a> road in <a href=\"https://wikipedia.org/wiki/Yrgyz_District\" title=\"Yrgyz District\">Yrgyz District</a>, <a href=\"https://wikipedia.org/wiki/Aktobe_Region\" title=\"Aktobe Region\">Aktobe</a>, Kazakhstan. The fire kills 52 passengers, with three passengers and two drivers escaping.", "links": [{"title": "Samara", "link": "https://wikipedia.org/wiki/Samara"}, {"title": "Shymkent", "link": "https://wikipedia.org/wiki/Shymkent"}, {"title": "Yrgyz District", "link": "https://wikipedia.org/wiki/Yrgyz_District"}, {"title": "Aktobe Region", "link": "https://wikipedia.org/wiki/Aktobe_Region"}]}, {"year": "2019", "text": "An oil pipeline explosion near Tlahuelilpan, Hidalgo, Mexico, kills 137 people.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Tlahuelilpan_pipeline_explosion\" title=\"Tlahuelilpan pipeline explosion\">An oil pipeline explosion</a> near <a href=\"https://wikipedia.org/wiki/Tlahuelilpan\" title=\"Tlahuelilpan\">Tlahuelilpan</a>, Hidalgo, Mexico, kills 137 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tlahuelilpan_pipeline_explosion\" title=\"Tlahuelilpan pipeline explosion\">An oil pipeline explosion</a> near <a href=\"https://wikipedia.org/wiki/Tlahuelilpan\" title=\"Tlahuelilpan\">Tlahuelilpan</a>, Hidalgo, Mexico, kills 137 people.", "links": [{"title": "Tlahuelilpan pipeline explosion", "link": "https://wikipedia.org/wiki/Tlahuelilpan_pipeline_explosion"}, {"title": "Tlahuelilpan", "link": "https://wikipedia.org/wiki/Tlahuelilpan"}]}, {"year": "2023", "text": "A helicopter crash in Ukraine leaves 14 people dead, including the country's Interior Minister, <PERSON><PERSON>.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/2023_Brovary_helicopter_crash\" title=\"2023 Brovary helicopter crash\">A helicopter crash</a> in <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> leaves 14 people dead, including the country's <a href=\"https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Ukraine)\" title=\"Ministry of Internal Affairs (Ukraine)\">Interior Minister</a>, <a href=\"https://wikipedia.org/wiki/Denys_Monastyrsky\" title=\"<PERSON>ys Monastyrsky\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2023_Brovary_helicopter_crash\" title=\"2023 Brovary helicopter crash\">A helicopter crash</a> in <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> leaves 14 people dead, including the country's <a href=\"https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Ukraine)\" title=\"Ministry of Internal Affairs (Ukraine)\">Interior Minister</a>, <a href=\"https://wikipedia.org/wiki/Denys_Monastyrsky\" title=\"<PERSON><PERSON> Monastyrsky\"><PERSON><PERSON></a>.", "links": [{"title": "2023 <PERSON><PERSON><PERSON> helicopter crash", "link": "https://wikipedia.org/wiki/2023_<PERSON><PERSON><PERSON>_helicopter_crash"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Ministry of Internal Affairs (Ukraine)", "link": "https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Ukraine)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Denys_Monastyrsky"}]}, {"year": "2025", "text": "The popular social media app, TikTok, is banned in the United States, after the passing of PAFACA.", "html": "2025 - The popular social media app, <a href=\"https://wikipedia.org/wiki/TikTok\" title=\"TikTok\">TikTok</a>, is banned in the United States, after the passing of <a href=\"https://wikipedia.org/wiki/Protecting_Americans_from_Foreign_Adversary_Controlled_Applications_Act\" title=\"Protecting Americans from Foreign Adversary Controlled Applications Act\">PAFACA</a>.", "no_year_html": "The popular social media app, <a href=\"https://wikipedia.org/wiki/TikTok\" title=\"TikTok\">TikTok</a>, is banned in the United States, after the passing of <a href=\"https://wikipedia.org/wiki/Protecting_Americans_from_Foreign_Adversary_Controlled_Applications_Act\" title=\"Protecting Americans from Foreign Adversary Controlled Applications Act\">PAFACA</a>.", "links": [{"title": "TikTok", "link": "https://wikipedia.org/wiki/TikTok"}, {"title": "Protecting Americans from Foreign Adversary Controlled Applications Act", "link": "https://wikipedia.org/wiki/Protecting_Americans_from_Foreign_Adversary_Controlled_Applications_Act"}]}], "Births": [{"year": "1404", "text": "Sir <PERSON>, English noble (d. 1463)", "html": "1404 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(died_1463)\" title=\"<PERSON> (died 1463)\">Sir <PERSON></a>, English noble (d. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(died_1463)\" title=\"<PERSON> (died 1463)\">Sir <PERSON></a>, English noble (d. 1463)", "links": [{"title": "<PERSON> (died 1463)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1463)"}]}, {"year": "1457", "text": "<PERSON>, seniore, Roman Catholic cardinal (d. 1508)", "html": "1457 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_seniore\" title=\"<PERSON>, seniore\"><PERSON>, seniore</a>, Roman Catholic cardinal (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_seniore\" title=\"<PERSON>, seniore\"><PERSON>, seniore</a>, Roman Catholic cardinal (d. 1508)", "links": [{"title": "<PERSON>, seniore", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_seniore"}]}, {"year": "1519", "text": "<PERSON>, Queen of Hungary (d. 1559)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen of Hungary (d. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen of Hungary (d. 1559)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1540", "text": "<PERSON>, Duchess of Braganza (d. 1614)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Braganza\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Braganza\"><PERSON>, Duchess of Braganza</a> (d. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Braganza\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Braganza\"><PERSON>, Duchess of Braganza</a> (d. 1614)", "links": [{"title": "<PERSON>, Duchess of Braganza", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Braganza"}]}, {"year": "1641", "text": "<PERSON><PERSON><PERSON>, <PERSON>, French politician, Secretary of State for War (d. 1691)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON>, <PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War_(France)\" title=\"Secretary of State for War (France)\">Secretary of State for War</a> (d. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON>, <PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War_(France)\" title=\"Secretary of State for War (France)\">Secretary of State for War</a> (d. 1691)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Secretary of State for War (France)", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_War_(France)"}]}, {"year": "1659", "text": "<PERSON><PERSON>, English philosopher and theologian (d. 1708)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Masham\" title=\"<PERSON><PERSON> Cudworth Masham\"><PERSON><PERSON></a>, English philosopher and theologian (d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>worth_Masham\" title=\"<PERSON>aris Cudworth Masham\"><PERSON><PERSON><PERSON><PERSON></a>, English philosopher and theologian (d. 1708)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Cudworth_Masham"}]}, {"year": "1672", "text": "<PERSON>, French author (d. 1731)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON>, 1st Duke of Dorset, English politician, Lord Lieutenant of Ireland (d. 1765)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Dorset\" title=\"<PERSON>, 1st Duke of Dorset\"><PERSON>, 1st Duke of Dorset</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Dorset\" title=\"<PERSON>, 1st Duke of Dorset\"><PERSON>, 1st Duke of Dorset</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1765)", "links": [{"title": "<PERSON>, 1st Duke of Dorset", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Dorset"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1689", "text": "<PERSON><PERSON><PERSON><PERSON>, French lawyer and philosopher (d. 1755)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and philosopher (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and philosopher (d. 1755)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1701", "text": "<PERSON>, German jurist (d. 1785)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (d. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1734", "text": "<PERSON><PERSON><PERSON> <PERSON>, German physiologist and embryologist (d. 1794)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German physiologist and embryologist (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German physiologist and embryologist (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1743", "text": "<PERSON>, French mystic and philosopher (d. 1803)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mystic and philosopher (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mystic and philosopher (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, Austrian pianist and composer (d. 1831)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, English architect (d. 1835)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect (d. 1835)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>(architect)"}]}, {"year": "1764", "text": "<PERSON>, English politician (d. 1815)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1764%E2%80%931815)\" title=\"<PERSON> (1764-1815)\"><PERSON></a>, English politician (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1764%E2%80%931815)\" title=\"<PERSON> (1764-1815)\"><PERSON></a>, English politician (d. 1815)", "links": [{"title": "<PERSON> (1764-1815)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1764%E2%80%931815)"}]}, {"year": "1779", "text": "<PERSON>, English physician, lexicographer, and theologian (d. 1869)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, lexicographer, and theologian (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, lexicographer, and theologian (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, American lawyer and politician, 14th United States Secretary of State (d. 1852)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1793", "text": "<PERSON><PERSON><PERSON>, Chhatrapati of the Maratha Empire (d. 1847)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Raja_of_Satara\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Raja of Satara\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ch<PERSON>rapati\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Ch<PERSON>rapati</a> of the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Raja_of_Satara\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Raja of Satara\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>hatrapati\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1847)", "links": [{"title": "<PERSON><PERSON><PERSON>, Raja of Satara", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Raja_of_Satara"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ch<PERSON>rapati"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON> <PERSON>, German theologian and scholar (d. 1874)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_von_Tischendorf\" title=\"<PERSON><PERSON><PERSON> von Tischendorf\">Con<PERSON><PERSON> von <PERSON></a>, German theologian and scholar (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_von_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von Tischendorf\">Con<PERSON><PERSON> <PERSON></a>, German theologian and scholar (d. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, Russian general, composer, and critic (d. 1918)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general, composer, and critic (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general, composer, and critic (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Cui"}]}, {"year": "1840", "text": "<PERSON>, English poet and author (d. 1921)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, French pianist and composer (d. 1894)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1842", "text": "<PERSON><PERSON> <PERSON><PERSON>, American physician and politician, Mayor of Minneapolis (d. 1911)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"A. A<PERSON> Ames\"><PERSON><PERSON> <PERSON><PERSON></a>, American physician and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Minneapolis\" class=\"mw-redirect\" title=\"Mayor of Minneapolis\">Mayor of Minneapolis</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"A. A<PERSON> Ames\"><PERSON><PERSON> <PERSON><PERSON></a>, American physician and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Minneapolis\" class=\"mw-redirect\" title=\"Mayor of Minneapolis\">Mayor of Minneapolis</a> (d. 1911)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Minneapolis", "link": "https://wikipedia.org/wiki/Mayor_of_Minneapolis"}]}, {"year": "1843", "text": "<PERSON><PERSON><PERSON>, South African farmer, soldier, and gun-maker (d. 1900)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African farmer, soldier, and gun-maker (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African farmer, soldier, and gun-maker (d. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON>, Romanian journalist and author (d. 1925)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist and author (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist and author (d. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1849", "text": "<PERSON>, Australian judge and politician, 1st Prime Minister of Australia (d. 1920)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian judge and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian judge and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1850", "text": "<PERSON>, American academic and politician, 92nd Mayor of New York City (d. 1916)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 92nd <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 92nd <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1854", "text": "<PERSON>, American assistant to <PERSON> (d. 1934)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assistant to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assistant to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American surgeon and cardiologist (d. 1931)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and cardiologist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and cardiologist (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, Nicaraguan poet, journalist, and diplomat (d. 1916)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Dar%C3%ADo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nicaraguan poet, journalist, and diplomat (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Dar%C3%ADo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nicaraguan poet, journalist, and diplomat (d. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Dar%C3%ADo"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Japanese admiral and politician, 42nd Prime Minister of Japan (d. 1948)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Kantar%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral and politician, 42nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kantar%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral and politician, 42nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kantar%C5%8D_Suzuki"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1877", "text": "<PERSON>, Russian-American businessman, founded the Cuyamel Fruit Company (d. 1961)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American businessman, founded the <a href=\"https://wikipedia.org/wiki/Cuyamel_Fruit_Company\" title=\"Cuyamel Fruit Company\">Cuyamel Fruit Company</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American businessman, founded the <a href=\"https://wikipedia.org/wiki/Cuyamel_Fruit_Company\" title=\"Cuyamel Fruit Company\">Cuyamel Fruit Company</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cuyamel Fruit Company", "link": "https://wikipedia.org/wiki/Cuyamel_Fruit_Company"}]}, {"year": "1879", "text": "<PERSON>, French general and politician (d. 1949)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Austrian-Dutch physicist and academic (d. 1933)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Dutch physicist and academic (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Dutch physicist and academic (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Italian cardinal (d. 1954)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, French publisher, founded Éditions Gallimard (d. 1975)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French publisher, founded <i><a href=\"https://wikipedia.org/wiki/%C3%89ditions_Gallimard\" title=\"Éditions Gallimard\">Éditions <PERSON>all<PERSON></a></i> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French publisher, founded <i><a href=\"https://wikipedia.org/wiki/%C3%89ditions_Gallimard\" title=\"Éditions Gallimard\">Éditions Gall<PERSON>rd</a></i> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "É<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ditions_<PERSON>allimard"}]}, {"year": "1882", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author, poet, and playwright (d. 1956)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author, poet, and playwright (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author, poet, and playwright (d. 1956)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Swedish-German author and translator (d. 1962)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Nordstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish-German author and translator (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>str%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish-German author and translator (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clara_Nordstr%C3%B6m"}]}, {"year": "1888", "text": "<PERSON>, English ice hockey player, sailor, and pilot (d. 1989)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ice hockey player, sailor, and pilot (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ice hockey player, sailor, and pilot (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American actor and comedian (d. 1957)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American hurdler and coach (d. 1957)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, German surgeon and academic (d. 1956)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon and academic (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon and academic (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Spanish poet, critic, and academic (d. 1984)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Spanish poet, critic, and academic (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Spanish poet, critic, and academic (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American wrestler and promoter (d. 1976)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mondt\"><PERSON><PERSON></a>, American wrestler and promoter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mondt\"><PERSON><PERSON></a>, American wrestler and promoter (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mondt"}]}, {"year": "1896", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON>.</a>, American author (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON><PERSON> Jr.\"><PERSON><PERSON> <PERSON><PERSON>.</a>, American author (d. 1967)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1896", "text": "<PERSON>, Finnish-American runner (d. 1982)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Ville_Ritola\" title=\"Ville Ritola\"><PERSON></a>, Finnish-American runner (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ville_Ritola\" title=\"Ville Ritola\"><PERSON></a>, Finnish-American runner (d. 1982)", "links": [{"title": "Ville Ritola", "link": "https://wikipedia.org/wiki/Ville_Ritola"}]}, {"year": "1898", "text": "<PERSON>, Estonian journalist and author (d. 1978)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Russian mathematician and academic (d. 1973)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, German pianist and composer (d. 1996)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dt\" title=\"<PERSON><PERSON>schmidt\"><PERSON><PERSON></a>, German pianist and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dt\" title=\"<PERSON><PERSON>schmidt\"><PERSON><PERSON></a>, German pianist and composer (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Goldschmidt"}]}, {"year": "1904", "text": "<PERSON>, American accordion player and composer (d. 2006)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player and composer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player and composer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English-American actor (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Italian-American mob boss (d. 2002)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Hungarian conductor (d. 1984)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Ferencsik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian conductor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Ferencsik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian conductor (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Ferencsik"}]}, {"year": "1908", "text": "<PERSON>, Polish-English mathematician, historian, and television host (d. 1974)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English mathematician, historian, and television host (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English mathematician, historian, and television host (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English economist and academic (d. 1993)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Peruvian anthropologist, author, and poet (d. 1969)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Arguedas\" title=\"<PERSON>\"><PERSON></a>, Peruvian anthropologist, author, and poet (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Arguedas\" title=\"<PERSON>\"><PERSON></a>, Peruvian anthropologist, author, and poet (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Arguedas"}]}, {"year": "1911", "text": "<PERSON>, American actor, singer, and dancer (d. 1987)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American artist (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Greek composer (d. 1972)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, German author and translator (d. 1979)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and translator (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and translator (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovene author, poet, and playwright (d. 1987)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Vitomil_Z<PERSON>an\" title=\"Vitomil Z<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene author, poet, and playwright (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vitomi<PERSON>_Z<PERSON>\" title=\"Vitomi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene author, poet, and playwright (d. 1987)", "links": [{"title": "Vitomil <PERSON>", "link": "https://wikipedia.org/wiki/Vitomil_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Canadian pole vaulter, ice hockey player, and politician (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Syl_A<PERSON>\" title=\"<PERSON>yl Apps\"><PERSON><PERSON></a>, Canadian pole vaulter, ice hockey player, and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syl_A<PERSON>\" title=\"<PERSON><PERSON> Apps\"><PERSON><PERSON></a>, Canadian pole vaulter, ice hockey player, and politician (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Syl_Apps"}]}, {"year": "1915", "text": "<PERSON>, Spanish soldier and politician (d. 2012)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Santiago_Carrillo\" title=\"Santiago Carrillo\"><PERSON></a>, Spanish soldier and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Carrillo\" title=\"Santiago Carrillo\"><PERSON></a>, Spanish soldier and politician (d. 2012)", "links": [{"title": "Santiago Carrillo", "link": "https://wikipedia.org/wiki/Santiago_Carrillo"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter and bouzouki player (d. 1984)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American sergeant, Medal of Honor recipient (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1917", "text": "<PERSON>ching, Taiwanese-American businessman (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ching\" title=\"<PERSON>-ching\"><PERSON></a>, Taiwanese-American businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ching\" title=\"<PERSON>-chin<PERSON>\"><PERSON></a>, Taiwanese-American businessman (d. 2008)", "links": [{"title": "<PERSON>ching", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ching"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Canadian-English physician and educator (d. 1996)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-English physician and educator (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-English physician and educator (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, German footballer (d. 1984)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Japanese-American physicist and academic, Nobel Prize laureate (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1923", "text": "<PERSON>, General Officer Commanding (GOC) Wales (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1923)\" title=\"<PERSON> (British Army officer, born 1923)\"><PERSON></a>, General Officer Commanding (GOC) Wales (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1923)\" title=\"<PERSON> (British Army officer, born 1923)\"><PERSON></a>, General Officer Commanding (GOC) Wales (d. 2012)", "links": [{"title": "<PERSON> (British Army officer, born 1923)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_Army_officer,_born_1923)"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Dutch cyclist (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French metaphysician and philosopher (d. 1995)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French metaphysician and philosopher (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French metaphysician and philosopher (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American soldier and politician, 27th Governor of Idaho (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Idaho_governor)\" class=\"mw-redirect\" title=\"<PERSON> (Idaho governor)\"><PERSON></a>, American soldier and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Idaho\" class=\"mw-redirect\" title=\"Governor of Idaho\">Governor of Idaho</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Idaho_governor)\" class=\"mw-redirect\" title=\"<PERSON> (Idaho governor)\"><PERSON></a>, American soldier and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Idaho\" class=\"mw-redirect\" title=\"Governor of Idaho\">Governor of Idaho</a> (d. 2014)", "links": [{"title": "<PERSON> (Idaho governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Idaho_governor)"}, {"title": "Governor of Idaho", "link": "https://wikipedia.org/wiki/Governor_of_Idaho"}]}, {"year": "1925", "text": "<PERSON>, American soldier and author (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American geologist and academic (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Randolph_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Indian actor, singer, and veena player (d. 1990)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>der\" title=\"<PERSON><PERSON> Ba<PERSON>chander\"><PERSON><PERSON></a>, Indian actor, singer, and <a href=\"https://wikipedia.org/wiki/Veena\" title=\"<PERSON><PERSON><PERSON>\">veena</a> player (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>der\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, singer, and <a href=\"https://wikipedia.org/wiki/<PERSON>eena\" title=\"<PERSON><PERSON><PERSON>\">veena</a> player (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veena"}]}, {"year": "1928", "text": "<PERSON>, Soviet and Russian professional basketball coach (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet and Russian professional basketball coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet and Russian professional basketball coach (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American diplomat, UNESCO goodwill ambassador (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/UNESCO_Goodwill_Ambassador\" title=\"UNESCO Goodwill Ambassador\">UNESCO goodwill ambassador</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/UNESCO_Goodwill_Ambassador\" title=\"UNESCO Goodwill Ambassador\">UNESCO goodwill ambassador</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "UNESCO Goodwill Ambassador", "link": "https://wikipedia.org/wiki/UNESCO_Goodwill_Ambassador"}]}, {"year": "1931", "text": "<PERSON>, South Korean general and politician, 5th President of South Korea (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>wan\"><PERSON></a>, South Korean general and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>wan\"><PERSON></a>, South Korean general and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 2021)", "links": [{"title": "<PERSON>wan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1932", "text": "<PERSON>, American psychologist, author, poet, and playwright (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, author, poet, and playwright (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, author, poet, and playwright (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Nigerian politician, 8th Nigerian Minister of Foreign Affairs", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Nigeria)\">Nigerian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Nigeria)\">Nigerian Minister of Foreign Affairs</a>", "links": [{"title": "Emeka Anyaoku", "link": "https://wikipedia.org/wiki/Emeka_Anyaoku"}, {"title": "Minister of Foreign Affairs (Nigeria)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)"}]}, {"year": "1933", "text": "<PERSON>, English botanist, author and academic (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist, author and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David <PERSON>\"><PERSON></a>, English botanist, author and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American engineer and businessman, founded Dolby Laboratories (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Dolby_Laboratories\" class=\"mw-redirect\" title=\"Dolby Laboratories\">Dolby Laboratories</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Dolby_Laboratories\" class=\"mw-redirect\" title=\"Dolby Laboratories\">Dolby Laboratories</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dolby Laboratories", "link": "https://wikipedia.org/wiki/Dolby_Laboratories"}]}, {"year": "1933", "text": "<PERSON>, Baron <PERSON>, English lawyer and politician (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician (d. 2017)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, New Zealand rugby player (d. 2004)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, French ski racer (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ski racer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ski racer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English author and illustrator (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, British illustrator (d. 1999)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, British illustrator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, British illustrator (d. 1999)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1935", "text": "<PERSON>, Canadian actor and director (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and director (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and director (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English poet, critic, and academic (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, critic, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, critic, and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Israeli academic and diplomat, 10th Israel Ambassador to the United Nations (d. 2007)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli academic and diplomat, 10th <a href=\"https://wikipedia.org/wiki/Israel_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"Israel Ambassador to the United Nations\">Israel Ambassador to the United Nations</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli academic and diplomat, 10th <a href=\"https://wikipedia.org/wiki/Israel_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"Israel Ambassador to the United Nations\">Israel Ambassador to the United Nations</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Israel Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/Israel_Ambassador_to_the_United_Nations"}]}, {"year": "1936", "text": "<PERSON>, English actor (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, <PERSON> of Guildford, English journalist and politician, Secretary of State for Transport", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Guildford\" title=\"<PERSON>, Baron <PERSON> of Guildford\"><PERSON>, Baron <PERSON> of Guildford</a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Guildford\" title=\"<PERSON>, Baron <PERSON> of Guildford\"><PERSON>, Baron <PERSON> of Guildford</a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a>", "links": [{"title": "<PERSON>, Baron <PERSON> of Guildford", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Guildford"}, {"title": "Secretary of State for Transport", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Transport"}]}, {"year": "1937", "text": "<PERSON>, Northern Irish educator and politician, Nobel Prize laureate (d. 2020)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish educator and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish educator and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American baseball player and sportscaster (d. 1997)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English sociologist and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, German footballer and manager", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON> \"<PERSON>\" <PERSON>, American musician (d. 2022)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Hargus_%22Pig%22_<PERSON>\" title='<PERSON><PERSON> \"<PERSON>\" <PERSON>'><PERSON><PERSON> \"<PERSON>\" <PERSON></a>, American musician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hargus_%22Pig%22_<PERSON>\" title='<PERSON><PERSON> \"<PERSON>\" <PERSON>'><PERSON><PERSON> \"<PERSON>\" <PERSON></a>, American musician (d. 2022)", "links": [{"title": "Hargus \"Pig\" <PERSON>", "link": "https://wikipedia.org/wiki/Hargus_%22Pig%22_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Mexican race car driver (d. 1971)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Mexican race car driver (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Mexican race car driver (d. 1971)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Pedro_Rodr%C3%<PERSON><PERSON><PERSON>_(racing_driver)"}]}, {"year": "1941", "text": "<PERSON>, Canadian journalist and author (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and author (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ardier"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer (d. 1991)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1943", "text": "<PERSON>, American educator and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English keyboard player and composer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American businessman and politician (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)\" title=\"<PERSON> (Ohio politician)\"><PERSON></a>, American businessman and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)\" title=\"<PERSON> (Ohio politician)\"><PERSON></a>, American businessman and politician (d. 2013)", "links": [{"title": "<PERSON> (Ohio politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)"}]}, {"year": "1944", "text": "<PERSON>, Australian economist and politician, 24th Prime Minister of Australia", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian economist and politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian economist and politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1944", "text": "<PERSON>, American baseball player (d. 1983)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Japanese singer-songwriter and composer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ra"}]}, {"year": "1944", "text": "<PERSON>, President of Austria", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, President of Austria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, President of Austria", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, English businessman and philanthropist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>occo <PERSON>\"><PERSON><PERSON></a>, English businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English businessman and philanthropist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Mexican wrestler (d. 2019)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Perro_Aguayo"}]}, {"year": "1946", "text": "<PERSON>, Swiss economist and politician, 156th President of the Swiss Confederation", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss economist and politician, 156th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss economist and politician, 156th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Bissau-Guinean politician, President of Guinea-Bissau (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bissau-Guinean politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"List of heads of state of Guinea-Bissau\">President of Guinea-Bissau</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bissau-Guinean politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"List of heads of state of Guinea-Bissau\">President of Guinea-Bissau</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Guinea-Bissau", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Guinea-Bissau"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player and journalist (d. 2018)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and journalist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and journalist (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Japanese actor and director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American journalist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French interior designer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French interior designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French interior designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian race car driver", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian race car driver (d. 1982)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Surinamese journalist and activist (d. 1982)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese journalist and activist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese journalist and activist (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English footballer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American biochemist, author, and academic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian neurosurgeon", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Misra\" title=\"<PERSON><PERSON> <PERSON><PERSON> Misra\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian neurosurgeon", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Misra\" title=\"B<PERSON> K. Misra\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian neurosurgeon", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian comedian and actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian and actor", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1954", "text": "<PERSON>, American wrestler", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor, director, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Baron <PERSON>, English banker and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English banker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English banker and politician", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English actor, director, and playwright", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American basketball player and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player, coach, and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor and sculptor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian-American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Canadian lawyer and politician, 7th Minister of Foreign Affairs for Canada", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Minister of Foreign Affairs for Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Minister of Foreign Affairs for Canada</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>e_Bernier"}, {"title": "Minister of Foreign Affairs (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)"}]}, {"year": "1963", "text": "<PERSON>, English footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American soldier, lawyer, and politician, 61st Governor of Maryland", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 61st <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 61st <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_O%27Malley"}, {"title": "Governor of Maryland", "link": "https://wikipedia.org/wiki/Governor_of_Maryland"}]}, {"year": "1964", "text": "<PERSON>, American baseball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Northern Irish jockey and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish jockey and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish jockey and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American boxer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Virgil <PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Virgil <PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virgil_Hill"}]}, {"year": "1964", "text": "<PERSON>, English actress and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Russian chess player and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese singer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Brazilian race car driver (d. 2021)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Brazilian race car driver (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Brazilian race car driver (d. 2021)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON><PERSON><PERSON>_(racing_driver)"}]}, {"year": "1967", "text": "<PERSON>, Australian footballer and coach (d. 2014)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Chilean footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Zamorano\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Zamorano\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Zamorano"}]}, {"year": "1969", "text": "<PERSON>, American wrestler, mixed martial artist, and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, mixed martial artist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, mixed martial artist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor and singer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American guitarist and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(musician)"}]}, {"year": "1970", "text": "DJ <PERSON><PERSON><PERSON>, American rapper and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/DJ_Quik\" title=\"DJ Quik\">DJ <PERSON><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_Quik\" title=\"DJ Quik\">DJ <PERSON><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "DJ <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/DJ_Quik"}]}, {"year": "1970", "text": "<PERSON>, Belgian cyclist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American astronomer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Brazilian race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Spanish footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Pep_Guardiola\" title=\"Pep Guardiola\"><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pep_Guardiola\" title=\"Pep Guardiola\"><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "Pep <PERSON>", "link": "https://wikipedia.org/wiki/Pep_Guardiola"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Kenyan writer (d. 2019)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Binyavanga_Wainaina\" title=\"Binyavanga Wainaina\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan writer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Binyavanga_Wainaina\" title=\"Binyavanga Wainaina\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan writer (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Binyavanga_Wainaina"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Indian cricketer, sportscaster, and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer, sportscaster, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer, sportscaster, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian race walker", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Kjersti_Pl%C3%A4tzer\" title=\"<PERSON><PERSON><PERSON><PERSON>lät<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kjersti_Pl%C3%A4tzer\" title=\"<PERSON>jer<PERSON><PERSON> Plät<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian race walker", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kjersti_Pl%C3%A4tzer"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American actor, director, and producer, co-founded Rooster Teeth Productions", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/Rooster_Teeth_Productions\" class=\"mw-redirect\" title=\"Rooster Teeth Productions\">Rooster Teeth Productions</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/Rooster_Teeth_Productions\" class=\"mw-redirect\" title=\"Rooster Teeth Productions\">Rooster Teeth Productions</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Rooster Teeth Productions", "link": "https://wikipedia.org/wiki/Rooster_Teeth_Productions"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American civic leader and activist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American civic leader and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American civic leader and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball executive", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter, guitarist, and director", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"C<PERSON><PERSON> Mills\"><PERSON><PERSON><PERSON></a>, English singer-songwriter, guitarist, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"C<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter, guitarist, and director", "links": [{"title": "Crispian Mills", "link": "https://wikipedia.org/wiki/Crispian_Mills"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Argentinian footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Belgian tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Argentinian footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Irish-Australian singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1977", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Norwegian cyclist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>d"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Romanian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Bogdan_Lobon%C8%9B\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bogdan_Lobon%C8%9B\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bogdan_Lobon%C8%9B"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Ukrainian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Portuguese footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Paulo_Ferreira\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paulo_Ferreira\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_Ferreira"}]}, {"year": "1979", "text": "<PERSON>, American ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American football player (d. 2018)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Estonian footballer and manager", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Kert_Haavistu\" title=\"Kert Haavistu\"><PERSON><PERSON></a>, Estonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kert_<PERSON>avistu\" title=\"Kert Haavistu\"><PERSON><PERSON></a>, Estonian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kert_Haavistu"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julius Peppers\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julius_Peppers\" title=\"Julius Peppers\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julius_Peppers"}]}, {"year": "1980", "text": "<PERSON>, American actor and screenwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-won\" title=\"<PERSON>-won\"><PERSON>-<PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-won\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>won", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-won"}]}, {"year": "1981", "text": "<PERSON>, Belgian tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Jamaican footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American guitarist and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Kenyan runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>ny"}]}, {"year": "1983", "text": "<PERSON>, Israeli-American comedian, actor, director, and screenwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American comedian, actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American comedian, actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Irish singer-songwriter and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American sprinter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean mass murderer (d. 2007)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean mass murderer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean mass murderer (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Greek swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American biochemist and academic", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American dancer and choreographer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer and choreographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mmer"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Estonian figure skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Viktoria_Shklover\" class=\"mw-redirect\" title=\"Viktoria Shklover\"><PERSON><PERSON></a>, Estonian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viktoria_Shklover\" class=\"mw-redirect\" title=\"Viktoria Shklover\"><PERSON><PERSON></a>, Estonian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viktoria_Shklover"}]}, {"year": "1985", "text": "<PERSON>, Canadian-Australian skier", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Australian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Australian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, South Korean actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>yun_Woo\" title=\"<PERSON>yun Woo\"><PERSON><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Woo\" title=\"<PERSON><PERSON> Woo\"><PERSON><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Woo"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Estonian-American singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ox<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ox<PERSON>\" title=\"<PERSON><PERSON>ox<PERSON>\"><PERSON><PERSON></a>, Estonian-American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oxx"}]}, {"year": "1986", "text": "<PERSON>, American actress, singer, and dancer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese actor and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Korean-American actor, filmmaker, and activist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean-American actor, filmmaker, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean-American actor, filmmaker, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Swiss footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German rugby player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ronnie_Day\" title=\"Ronnie Day\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ronnie_Day\" title=\"Ronnie Day\"><PERSON></a>, American singer-songwriter", "links": [{"title": "Ronnie <PERSON>", "link": "https://wikipedia.org/wiki/Ronnie_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, German tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>stasio<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Anastasio<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anastasios_Kissas"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Murray\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Murray\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "Ashleigh Murray", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Murray"}]}, {"year": "1988", "text": "<PERSON>, Dutch cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Mi%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Mi%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Mi%C3%B1o"}]}, {"year": "1989", "text": "<PERSON>, Dominican baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Senegalese basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Gorg<PERSON>_<PERSON>\" title=\"<PERSON>rg<PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rg<PERSON>_<PERSON>\" title=\"<PERSON>rg<PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gorg<PERSON>_<PERSON>ng"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Ethiopian-Azerbaijani runner", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian-Azerbaijani runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian-Azerbaijani runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1990)\" title=\"<PERSON><PERSON> (footballer, born 1990)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1990)\" title=\"<PERSON><PERSON> (footballer, born 1990)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1990)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1990)"}]}, {"year": "1990", "text": "<PERSON>, South African baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ng<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>pe"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Egyptian-American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, a.k.a. <PERSON>, American streamer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, a.k.a. <PERSON>, American streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, a.k.a. <PERSON>, American streamer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>en"}]}, {"year": "1992", "text": "<PERSON>, Italian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1993", "text": "<PERSON>, Colombian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fried\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Max Fried\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ied"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Belarusian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ilona_Kremen\" title=\"Ilona Kremen\"><PERSON><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ilona_Kremen\" title=\"Ilona Kremen\"><PERSON><PERSON></a>, Belarusian tennis player", "links": [{"title": "Ilona Kremen", "link": "https://wikipedia.org/wiki/Ilona_Kremen"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>u_<PERSON>\" title=\"<PERSON><PERSON> Castillejo\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>u_<PERSON>jo"}]}, {"year": "1997", "text": "<PERSON>, Indonesian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Swiss ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Aitana_Bonmat%C3%AD\" title=\"Aitana Bonmatí\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aitana_Bonmat%C3%AD\" title=\"Aitana Bonmatí\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "Ai<PERSON>", "link": "https://wikipedia.org/wiki/Aitana_Bonmat%C3%AD"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Lisa<PERSON>ro_Mart%C3%ADnez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lisa<PERSON>ro_Mart%C3%ADnez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lisandro_Mart%C3%ADnez"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/%C3%89der_Milit%C3%A3o\" title=\"Éder Militão\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89der_Milit%C3%A3o\" title=\"Éder Militão\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89der_Milit%C3%A3o"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American actor", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ecuadorian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1999", "text": "<PERSON><PERSON>, American actor", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Russian tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, German footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian actress and dancer", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "52 BC", "text": "<PERSON><PERSON>, Roman politician (b. 93 BC)", "html": "52 BC - 52 BC - <a href=\"https://wikipedia.org/wiki/Pub<PERSON>_<PERSON><PERSON>dius_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman politician (b. 93 BC)", "no_year_html": "52 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman politician (b. 93 BC)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "474", "text": "<PERSON>, Byzantine emperor (b. 401)", "html": "474 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a>, Byzantine emperor (b. 401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a>, Byzantine emperor (b. 401)", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)"}]}, {"year": "748", "text": "<PERSON><PERSON><PERSON>, duke of Bavaria", "html": "748 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Bavaria\" title=\"<PERSON><PERSON><PERSON>, Duke of Bavaria\"><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Bavaria\" title=\"<PERSON><PERSON><PERSON>, Duke of Bavaria\"><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavaria</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Bavaria"}, {"title": "Bavaria", "link": "https://wikipedia.org/wiki/Bavaria"}]}, {"year": "896", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON>, ruler of the Tulunids, murdered (b. 864)", "html": "896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON></a>, ruler of the <a href=\"https://wikipedia.org/wiki/Tulunids\" title=\"Tulunids\">Tulunids</a>, murdered (b. 864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_ibn_<PERSON>ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON></a>, ruler of the <a href=\"https://wikipedia.org/wiki/Tulunids\" title=\"Tulunids\">Tulunids</a>, murdered (b. 864)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_ibn_<PERSON>_ibn_<PERSON>"}, {"title": "Tulunids", "link": "https://wikipedia.org/wiki/<PERSON>lunids"}]}, {"year": "1213", "text": "<PERSON><PERSON> of Georgia (b. 1160)", "html": "1213 - <a href=\"https://wikipedia.org/wiki/Tamar_of_Georgia\" title=\"Tamar of Georgia\">Tamar of Georgia</a> (b. 1160)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tamar_of_Georgia\" title=\"Tamar of Georgia\">Tamar of Georgia</a> (b. 1160)", "links": [{"title": "Tamar of Georgia", "link": "https://wikipedia.org/wiki/Tamar_of_Georgia"}]}, {"year": "1253", "text": "King <PERSON> of Cyprus (b. 1217)", "html": "1253 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (b. 1217)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (b. 1217)", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus"}]}, {"year": "1271", "text": "<PERSON> <PERSON> of Hungary (b. 1242)", "html": "1271 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hungary_(saint)\" title=\"<PERSON> of Hungary (saint)\">Saint <PERSON> of Hungary</a> (b. 1242)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hungary_(saint)\" title=\"<PERSON> of Hungary (saint)\">Saint <PERSON> of Hungary</a> (b. 1242)", "links": [{"title": "<PERSON> of Hungary (saint)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hungary_(saint)"}]}, {"year": "1326", "text": "<PERSON>, 1st Baron <PERSON>, English baron (b. 1247)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English baron (b. 1247)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English baron (b. 1247)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1357", "text": "<PERSON> of Portugal, infanta (b. 1313)", "html": "1357 - <a href=\"https://wikipedia.org/wiki/Maria_of_Portugal,_Queen_of_Castile\" title=\"<PERSON> of Portugal, Queen of Castile\"><PERSON> of Portugal</a>, infanta (b. 1313)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Portugal,_Queen_of_Castile\" title=\"<PERSON> of Portugal, Queen of Castile\"><PERSON> of Portugal</a>, infanta (b. 1313)", "links": [{"title": "<PERSON> of Portugal, Queen of Castile", "link": "https://wikipedia.org/wiki/Maria_of_Portugal,_Queen_of_Castile"}]}, {"year": "1367", "text": "<PERSON> of Portugal (b. 1320)", "html": "1367 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1320)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Portugal"}]}, {"year": "1411", "text": "Job<PERSON> of Moravia, ruler of Moravia, King of the Romans", "html": "1411 - <a href=\"https://wikipedia.org/wiki/Jobst_of_Moravia\" title=\"Jobst of Moravia\">Jobst of Moravia</a>, ruler of <a href=\"https://wikipedia.org/wiki/Moravia\" title=\"Moravia\">Moravia</a>, <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jobst_of_Moravia\" title=\"Jobst of Moravia\">Jobst of Moravia</a>, ruler of <a href=\"https://wikipedia.org/wiki/Moravia\" title=\"Moravia\">Moravia</a>, <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a>", "links": [{"title": "Jobst of Moravia", "link": "https://wikipedia.org/wiki/Jobst_of_Moravia"}, {"title": "Moravia", "link": "https://wikipedia.org/wiki/Moravia"}, {"title": "King of the Romans", "link": "https://wikipedia.org/wiki/King_of_the_Romans"}]}, {"year": "1425", "text": "<PERSON>, 5th Earl of March, English politician (b. 1391)", "html": "1425 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_March\" title=\"<PERSON>, 5th Earl of March\"><PERSON>, 5th Earl of March</a>, English politician (b. 1391)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_March\" title=\"<PERSON>, 5th Earl of <PERSON>\"><PERSON>, 5th Earl of March</a>, English politician (b. 1391)", "links": [{"title": "<PERSON>, 5th Earl of March", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_<PERSON>"}]}, {"year": "1451", "text": "<PERSON>, Count of Nassau-Siegen (1442-1451) (b. 1414)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (1442-1451) (b. <a href=\"https://wikipedia.org/wiki/1414\" title=\"1414\">1414</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (1442-1451) (b. <a href=\"https://wikipedia.org/wiki/1414\" title=\"1414\">1414</a>)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}, {"title": "1414", "link": "https://wikipedia.org/wiki/1414"}]}, {"year": "1471", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1419)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Hanazono\" title=\"Emperor Go-Hanazono\">Emperor <PERSON><PERSON>Hanazono</a> of Japan (b. 1419)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Han<PERSON>ono\" title=\"Emperor Go-Hanazono\">Emperor <PERSON><PERSON>Han<PERSON>ono</a> of Japan (b. 1419)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1479", "text": "<PERSON>, Duke of Bavaria (b. 1417)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1417)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1417)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1547", "text": "<PERSON>, Italian cardinal and scholar (b. 1470)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and scholar (b. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and scholar (b. 1470)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1586", "text": "<PERSON> Parma (b. 1522)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Parma\" title=\"<PERSON> of Parma\"><PERSON> of Parma</a> (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Parma\" title=\"<PERSON> of Parma\"><PERSON> of Parma</a> (b. 1522)", "links": [{"title": "<PERSON> of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Parma"}]}, {"year": "1589", "text": "<PERSON>, Faroese naval hero (b. 1545)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Faroese naval hero (b. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Faroese naval hero (b. 1545)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1677", "text": "<PERSON>, Dutch politician, founded Cape Town (b. 1619)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, founded <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a> (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, founded <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a> (b. 1619)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cape Town", "link": "https://wikipedia.org/wiki/Cape_Town"}]}, {"year": "1756", "text": "<PERSON> of Schönborn-Buchheim, Archbishop-Elector of Trier (b. 1682)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sch%C3%B6nborn-Buchheim\" class=\"mw-redirect\" title=\"<PERSON> of Schönborn-Buchheim\"><PERSON> of Schönborn-Buchheim</a>, Archbishop-<PERSON>ector of Trier (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sch%C3%B6nborn-Buchheim\" class=\"mw-redirect\" title=\"<PERSON> of Schönborn-Buchheim\"><PERSON> of Schönborn-Buchheim</a>, Archbishop-<PERSON>ector of Trier (b. 1682)", "links": [{"title": "<PERSON> of Schönborn-Buchheim", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>h%C3%B6nborn-Buchheim"}]}, {"year": "1783", "text": "<PERSON>, French actress and playwright (b. 1699)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and playwright (b. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and playwright (b. 1699)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian poet and academic (b. 1743)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian poet and academic (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian poet and academic (b. 1743)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ipp<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1849", "text": "<PERSON><PERSON><PERSON>, Greek politician (b. 1752)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Panouts<PERSON>_Notaras\" title=\"Panoutsos Notaras\"><PERSON><PERSON><PERSON></a>, Greek politician (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan<PERSON><PERSON>_Notaras\" title=\"Panoutsos Notaras\"><PERSON><PERSON><PERSON></a>, Greek politician (b. 1752)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panoutsos_Notaras"}]}, {"year": "1862", "text": "<PERSON>, American soldier, lawyer, and politician, 10th President of the United States (b. 1790)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON><PERSON>, English author, poet, playwright, and politician, Secretary of State for the Colonies (b. 1803)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (b. 1803)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1878", "text": "<PERSON>, French physicist and academic (b. 1788)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antoine_C%C3%A9<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter (b. 1819)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (b. 1819)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Swiss religious leader, 23rd Superior General of the Society of Jesus (b. 1819)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss religious leader, 23rd <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss religious leader, 23rd <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1896", "text": "<PERSON>, French lawyer and politician, 55th Prime Minister of France (b. 1828)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1923", "text": "<PERSON>, American actor, director, and screenwriter (b. 1891)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Northern Irish political leader of the Nationalist Party (Northern Ireland) (b. 1871)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish political leader of the <a href=\"https://wikipedia.org/wiki/Nationalist_Party_(Northern_Ireland)\" title=\"Nationalist Party (Northern Ireland)\">Nationalist Party (Northern Ireland)</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish political leader of the <a href=\"https://wikipedia.org/wiki/Nationalist_Party_(Northern_Ireland)\" title=\"Nationalist Party (Northern Ireland)\">Nationalist Party (Northern Ireland)</a> (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nationalist Party (Northern Ireland)", "link": "https://wikipedia.org/wiki/Nationalist_Party_(Northern_Ireland)"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Dutch rower (b. 1871)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch rower (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch rower (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, English author and poet, Nobel Prize laureate (b. 1865)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1865)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish author, poet, and playwright (b. 1865)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>rzerwa-Tetmajer\" title=\"<PERSON><PERSON><PERSON><PERSON>rzerwa-Tetmajer\"><PERSON><PERSON><PERSON><PERSON></a>, Polish author, poet, and playwright (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>rz<PERSON>wa-Tetmajer\" title=\"<PERSON><PERSON><PERSON><PERSON>rz<PERSON>wa-Tetmajer\"><PERSON><PERSON><PERSON><PERSON></a>, Polish author, poet, and playwright (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>rz_Przerwa-Tetmajer"}]}, {"year": "1951", "text": "<PERSON>, Irish missionary and humanitarian (b. 1867)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish missionary and humanitarian (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish missionary and humanitarian (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American actor (b. 1903)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1903)", "links": [{"title": "Curly <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English-American actor (b. 1879)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Sydney_Greenstreet\" title=\"Sydney Greenstreet\">Sydney Greenstreet</a>, English-American actor (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Greenstreet\" title=\"Sydney Greenstreet\">Sydney Greenstreet</a>, English-American actor (b. 1879)", "links": [{"title": "Sydney Greenstreet", "link": "https://wikipedia.org/wiki/Sydney_Greenstreet"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Pakistani author and screenwriter (b. 1912)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani author and screenwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani author and screenwriter (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish lawyer and politician (b. 1885)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Makbule_Atadan\" title=\"Makbule Atadan\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Makbule_Atadan\" title=\"Makbule Atadan\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makbule_Atadan"}]}, {"year": "1956", "text": "<PERSON>, Estonian journalist, lawyer, and politician, 1st President of Estonia (b. 1874)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ts\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist, lawyer, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ts\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist, lawyer, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Konstantin_P%C3%A4ts"}, {"title": "President of Estonia", "link": "https://wikipedia.org/wiki/President_of_Estonia"}]}, {"year": "1963", "text": "<PERSON>, English academic and politician, Chancellor of the Exchequer (b. 1906)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1966", "text": "<PERSON>, American journalist and author (b. 1880)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American basketball player and soldier (b. 1921)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tatum\"><PERSON></a>, American basketball player and soldier (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tatum\"><PERSON></a>, American basketball player and soldier (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German sociologist and philosopher (b. 1887)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American religious leader, 9th President of The Church of Jesus Christ of Latter-day Saints (b. 1873)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 9th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 9th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1971", "text": "<PERSON>, American illustrator (b. 1914)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Russian tank commander (b. 1924)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tank commander (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tank commander (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress (b. 1897)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Pakistani philosopher and author (b. 1919)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Pakistani philosopher and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Pakistani philosopher and author (b. 1919)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1980", "text": "<PERSON>, English fashion designer and photographer (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer and photographer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer and photographer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian general and politician (b. 1902)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Panteleim<PERSON>_<PERSON>\" title=\"Panteleim<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian general and politician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Panteleim<PERSON>_<PERSON>\" title=\"Panteleim<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian general and politician (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panteleim<PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter and bouzouki player (b. 1915)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English-French author (b. 1940)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French author (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French author (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English singer (b. 1966)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1947)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Greek historian, academic, and politician (b. 1905)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian, academic, and politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian, academic, and politician (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dionysios_Zakythinos"}]}, {"year": "1995", "text": "<PERSON>, German biochemist and academic, Nobel Prize laureate (b. 1903)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1995", "text": "<PERSON>, American baseball player and umpire (b. 1937)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian actor, director, producer, and politician, 10th Chief Minister of Andhra Pradesh (b. 1923)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian actor, director, producer, and politician, 10th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Andhra_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Andhra Pradesh\">Chief Minister of Andhra Pradesh</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian actor, director, producer, and politician, 10th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Andhra_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Andhra Pradesh\">Chief Minister of Andhra Pradesh</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of Andhra Pradesh", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Andhra_Pradesh"}]}, {"year": "1997", "text": "<PERSON>, American lawyer and politician (b. 1941)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Greek footballer and manager (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer and manager (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Austrian architect (b. 1897)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%BCtte-Lihotzky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian architect (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%BCtte-Lihotzky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian architect (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%BCtte-Lihotzky"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, President of the Democratic Republic of the Congo (b. 1939)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9sir%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_the_Democratic_Republic_of_the_Congo\" title=\"President of the Democratic Republic of the Congo\">President of the Democratic Republic of the Congo</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9sir%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_the_Democratic_Republic_of_the_Congo\" title=\"President of the Democratic Republic of the Congo\">President of the Democratic Republic of the Congo</a> (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laurent-D%C3%A9sir%C3%A9_<PERSON><PERSON>a"}, {"title": "President of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/President_of_the_Democratic_Republic_of_the_Congo"}]}, {"year": "2003", "text": "<PERSON>, American wrestler and trainer (b. 1924)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Indian poet and author (b. 1907)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and author (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Russian-born Soviet test pilot and aerobatics champion (b. 1935)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Korchuganova\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>rch<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-born Soviet test pilot and <a href=\"https://wikipedia.org/wiki/Aerobatics\" title=\"Aerobatics\">aerobatics</a> champion (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Korchuganova\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-born Soviet test pilot and <a href=\"https://wikipedia.org/wiki/Aerobatics\" title=\"Aerobatics\">aerobatics</a> champion (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_Korch<PERSON>nova"}, {"title": "Aerobatics", "link": "https://wikipedia.org/wiki/Aerobatics"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American actor and rapper (b. 1973)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bentley\" title=\"<PERSON><PERSON> Bentley\"><PERSON><PERSON></a>, American actor and rapper (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bentley\" title=\"<PERSON><PERSON> Bentley\"><PERSON><PERSON></a>, American actor and rapper (b. 1973)", "links": [{"title": "Lamont Bentley", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bentley"}]}, {"year": "2006", "text": "<PERSON>, Polish priest and poet (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish priest and poet (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish priest and poet (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American bass player (b. 1963)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Liles"}]}, {"year": "2008", "text": "<PERSON>, American businesswoman and philanthropist (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Georgia_Frontiere\" title=\"Georgia Frontiere\">Georgia Frontiere</a>, American businesswoman and philanthropist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_Frontiere\" title=\"Georgia Frontiere\">Georgia Frontiere</a>, American businesswoman and philanthropist (b. 1927)", "links": [{"title": "Georgia Frontiere", "link": "https://wikipedia.org/wiki/Georgia_Frontiere"}]}, {"year": "2008", "text": "<PERSON>, American composer and theorist (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and theorist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and theorist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actress (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American politician (b. 1929)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English painter and television host (b. 1925)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and television host (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hart\"><PERSON></a>, English painter and television host (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Hungarian-American ballerina (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American ballerina (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American ballerina (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Greek singer-songwriter (b. 1913)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ratigopo<PERSON>\" title=\"<PERSON><PERSON>igopo<PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>po<PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rat<PERSON>u"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Romanian poet and author (b. 1935)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet and author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet and author (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "2010", "text": "<PERSON>, Canadian musician and singer-songwriter (b. 1946)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian musician and singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian musician and singer-songwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American author and academic (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American politician and diplomat, 21st United States Ambassador to France (b. 1915)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>rgent_Shriver\" title=\"Sargent Shriver\"><PERSON><PERSON>ver</a>, American politician and diplomat, 21st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Shriver\" title=\"<PERSON>rgent Shriver\"><PERSON><PERSON> Shriver</a>, American politician and diplomat, 21st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (b. 1915)", "links": [{"title": "<PERSON>rgent <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ver"}, {"title": "United States Ambassador to France", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_France"}]}, {"year": "2012", "text": "<PERSON>, Indian composer and educator (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian composer and educator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian composer and educator (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>alves"}]}, {"year": "2012", "text": "<PERSON>, German captain (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American playwright and producer, founded The National Radio Theater of Chicago (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and producer, founded <a href=\"https://wikipedia.org/wiki/The_National_Radio_Theater_of_Chicago\" class=\"mw-redirect\" title=\"The National Radio Theater of Chicago\">The National Radio Theater of Chicago</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and producer, founded <a href=\"https://wikipedia.org/wiki/The_National_Radio_Theater_of_Chicago\" class=\"mw-redirect\" title=\"The National Radio Theater of Chicago\">The National Radio Theater of Chicago</a> (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "The National Radio Theater of Chicago", "link": "https://wikipedia.org/wiki/The_National_Radio_Theater_of_Chicago"}]}, {"year": "2013", "text": "<PERSON>, Irish footballer and manager (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer and manager (b. 1922)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2013", "text": "<PERSON>, American computer scientist and academic (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian rugby league player (b. 1989)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian skateboarder (b. 1982)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian skateboarder (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian skateboarder (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Israeli lawyer and politician (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli lawyer and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli lawyer and politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American photographer and author (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Nigerian politician, 17th Governor of Plateau State (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Plateau_State\" class=\"mw-redirect\" title=\"List of Governors of Plateau State\">Governor of Plateau State</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Plateau_State\" class=\"mw-redirect\" title=\"List of Governors of Plateau State\">Governor of Plateau State</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Plateau State", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Plateau_State"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English footballer (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actress (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actress)\" title=\"<PERSON> (British actress)\"><PERSON></a>, English actress (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actress)\" title=\"<PERSON> (British actress)\"><PERSON></a>, English actress (b. 1933)", "links": [{"title": "<PERSON> (British actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actress)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Chilean poet and painter (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean poet and painter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean poet and painter (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Argentinian lawyer and prosecutor (b. 1963)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and prosecutor (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and prosecutor (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Romanian cosmetologist and author (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian cosmetologist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian cosmetologist and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Dutch journalist and politician (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist and politician (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American director and producer, invented instant replay (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, invented <a href=\"https://wikipedia.org/wiki/Instant_replay\" title=\"Instant replay\">instant replay</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, invented <a href=\"https://wikipedia.org/wiki/Instant_replay\" title=\"Instant replay\">instant replay</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}, {"title": "Instant replay", "link": "https://wikipedia.org/wiki/Instant_replay"}]}, {"year": "2016", "text": "<PERSON>, American basketball player and coach (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (b. 1948)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON> <PERSON><PERSON>, Judge of the High Court of Singapore (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/T._S._Sinnathuray\" title=\"T. S. Sinnathuray\"><PERSON><PERSON> <PERSON><PERSON></a>, Judge of the <a href=\"https://wikipedia.org/wiki/High_Court_of_Singapore\" title=\"High Court of Singapore\">High Court of Singapore</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._S._Sinnathuray\" title=\"T. S. Sinnathuray\"><PERSON><PERSON> <PERSON><PERSON></a>, Judge of the <a href=\"https://wikipedia.org/wiki/High_Court_of_Singapore\" title=\"High Court of Singapore\">High Court of Singapore</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}, {"title": "High Court of Singapore", "link": "https://wikipedia.org/wiki/High_Court_of_Singapore"}]}, {"year": "2016", "text": "<PERSON>, French journalist and author (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, South African-Jamaican writer (b. 1919)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Jamaican writer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Jamaican writer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Baroness <PERSON>, English cricketer, businesswoman and philanthropist (b. 1939)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Baroness_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON><PERSON>, Baroness <PERSON></a>, English cricketer, businesswoman and philanthropist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Baroness_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON><PERSON>, Baroness <PERSON></a>, English cricketer, businesswoman and philanthropist (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American coloratura soprano (b. 1930)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American coloratura soprano (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American coloratura soprano (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American figure skater (b. 1985)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, American figure skater (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, American figure skater (b. 1985)", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_(figure_skater)"}]}, {"year": "2022", "text": "<PERSON>, Spanish football player (b. 1933)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Francisco_Gento\" class=\"mw-redirect\" title=\"Francisco Gento\"><PERSON></a>, Spanish football player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Gento\" class=\"mw-redirect\" title=\"Francisco Gento\"><PERSON></a>, Spanish football player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1942)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American fashion journalist (b. 1948)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion journalist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion journalist (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American singer-songwriter (b. 1941)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, English director and composer (b. 1953)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and composer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and composer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}