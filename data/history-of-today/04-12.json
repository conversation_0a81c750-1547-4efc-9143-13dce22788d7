{"date": "April 12", "url": "https://wikipedia.org/wiki/April_12", "data": {"Events": [{"year": "240", "text": "<PERSON><PERSON><PERSON> I becomes co-emperor of the Sasanian Empire with his father <PERSON><PERSON><PERSON><PERSON>.", "html": "240 - <a href=\"https://wikipedia.org/wiki/Shapur_I\" title=\"Shapur I\">Shapur I</a> becomes co-emperor of the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a> with his father <a href=\"https://wikipedia.org/wiki/Ardashir_I\" title=\"Ardashir I\">Ardashir I</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shapur_I\" title=\"Shapur I\">Shapur I</a> becomes co-emperor of the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a> with his father <a href=\"https://wikipedia.org/wiki/Ardashir_I\" title=\"Ardashir I\">Ardashir I</a>.", "links": [{"title": "Shapur I", "link": "https://wikipedia.org/wiki/S<PERSON><PERSON>_I"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}, {"title": "Ardashir I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "467", "text": "<PERSON><PERSON><PERSON> is elevated to Emperor of the Western Roman Empire.", "html": "467 - <a href=\"https://wikipedia.org/wiki/<PERSON>them<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elevated to <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor of the Western Roman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elevated to <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor of the Western Roman Empire</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anthemius"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "627", "text": "King <PERSON> of Northumbria is converted to Christianity by <PERSON><PERSON>, bishop of York.", "html": "627 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Northumbria\" title=\"<PERSON> of Northumbria\"><PERSON> of Northumbria</a> is converted to Christianity by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_York\" title=\"<PERSON><PERSON> of York\"><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/York\" title=\"York\">York</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Northumbria\" title=\"<PERSON> of Northumbria\"><PERSON> of Northumbria</a> is converted to Christianity by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_York\" title=\"<PERSON><PERSON> of York\"><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/York\" title=\"York\">York</a>.", "links": [{"title": "<PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Northumbria"}, {"title": "<PERSON><PERSON> of York", "link": "https://wikipedia.org/wiki/<PERSON>inus_of_York"}, {"title": "York", "link": "https://wikipedia.org/wiki/York"}]}, {"year": "1012", "text": "Duke <PERSON><PERSON> of Bohemia deposes and blinds his brother <PERSON><PERSON><PERSON><PERSON><PERSON>, who flees to Poland.[citation needed]", "html": "1012 - <PERSON> <a href=\"https://wikipedia.org/wiki/Old%C5%99ich,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON> of Bohemia</a> deposes and blinds his brother <a href=\"https://wikipedia.org/wiki/<PERSON>aro<PERSON>%C3%<PERSON>r,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, who flees to <a href=\"https://wikipedia.org/wiki/Greater_Poland\" title=\"Greater Poland\">Poland</a>.", "no_year_html": "Duke <a href=\"https://wikipedia.org/wiki/Old%C5%99ich,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON> of Bohemia</a> deposes and blinds his brother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%ADr,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, who flees to <a href=\"https://wikipedia.org/wiki/Greater_Poland\" title=\"Greater Poland\">Poland</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/Old%C5%99ich,_Duke_of_Bohemia"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/Jarom%C3%ADr,_<PERSON>_of_Bohemia"}, {"title": "Greater Poland", "link": "https://wikipedia.org/wiki/Greater_Poland"}]}, {"year": "1204", "text": "The Crusaders of the Fourth Crusade breach the walls of Constantinople and enter the city, which they completely occupy the following day.", "html": "1204 - The <a href=\"https://wikipedia.org/wiki/Crusaders\" class=\"mw-redirect\" title=\"Crusaders\">Crusaders</a> of the <a href=\"https://wikipedia.org/wiki/Fourth_Crusade\" title=\"Fourth Crusade\">Fourth Crusade</a> breach the walls of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> and <a href=\"https://wikipedia.org/wiki/Siege_of_Constantinople_(1204)\" class=\"mw-redirect\" title=\"Siege of Constantinople (1204)\">enter the city</a>, which they completely occupy the following day.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Crusaders\" class=\"mw-redirect\" title=\"Crusaders\">Crusaders</a> of the <a href=\"https://wikipedia.org/wiki/Fourth_Crusade\" title=\"Fourth Crusade\">Fourth Crusade</a> breach the walls of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> and <a href=\"https://wikipedia.org/wiki/Siege_of_Constantinople_(1204)\" class=\"mw-redirect\" title=\"Siege of Constantinople (1204)\">enter the city</a>, which they completely occupy the following day.", "links": [{"title": "Crusaders", "link": "https://wikipedia.org/wiki/Crusaders"}, {"title": "Fourth Crusade", "link": "https://wikipedia.org/wiki/Fourth_Crusade"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Siege of Constantinople (1204)", "link": "https://wikipedia.org/wiki/Siege_of_Constantinople_(1204)"}]}, {"year": "1606", "text": "The Union Flag is adopted as the flag of English and Scottish ships.", "html": "1606 - The <a href=\"https://wikipedia.org/wiki/Union_Flag\" class=\"mw-redirect\" title=\"Union Flag\">Union Flag</a> is adopted as the flag of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scottish</a> ships.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Union_Flag\" class=\"mw-redirect\" title=\"Union Flag\">Union Flag</a> is adopted as the flag of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scottish</a> ships.", "links": [{"title": "Union Flag", "link": "https://wikipedia.org/wiki/Union_Flag"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}]}, {"year": "1776", "text": "American Revolution: With the Halifax Resolves, the North Carolina Provincial Congress authorizes its Congressional delegation to vote for independence from Britain.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: With the <a href=\"https://wikipedia.org/wiki/Halifax_Resolves\" title=\"Halifax Resolves\">Halifax Resolves</a>, the <a href=\"https://wikipedia.org/wiki/North_Carolina_Provincial_Congress\" title=\"North Carolina Provincial Congress\">North Carolina Provincial Congress</a> authorizes its <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Congressional</a> delegation to vote for independence from Britain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: With the <a href=\"https://wikipedia.org/wiki/Halifax_Resolves\" title=\"Halifax Resolves\">Halifax Resolves</a>, the <a href=\"https://wikipedia.org/wiki/North_Carolina_Provincial_Congress\" title=\"North Carolina Provincial Congress\">North Carolina Provincial Congress</a> authorizes its <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Congressional</a> delegation to vote for independence from Britain.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "Halifax Resolves", "link": "https://wikipedia.org/wiki/Halifax_Resolves"}, {"title": "North Carolina Provincial Congress", "link": "https://wikipedia.org/wiki/North_Carolina_Provincial_Congress"}, {"title": "Second Continental Congress", "link": "https://wikipedia.org/wiki/Second_Continental_Congress"}]}, {"year": "1782", "text": "American Revolution: A Royal Navy fleet led by Admiral <PERSON> defeats a French fleet led by the Comte de Grasse at the Battle of the Saintes off Dominica in the Caribbean Sea.", "html": "1782 - American Revolution: A <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> fleet led by Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> defeats a French fleet led by the <a href=\"https://wikipedia.org/wiki/Comte_de_<PERSON>\" class=\"mw-redirect\" title=\"Com<PERSON> de Grasse\"><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Saintes\" title=\"Battle of the Saintes\">Battle of the Saintes</a> off <a href=\"https://wikipedia.org/wiki/Dominica\" title=\"Dominica\">Dominica</a> in the <a href=\"https://wikipedia.org/wiki/Caribbean_Sea\" title=\"Caribbean Sea\">Caribbean Sea</a>.", "no_year_html": "American Revolution: A <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> fleet led by Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> defeats a French fleet led by the <a href=\"https://wikipedia.org/wiki/Comte_de_<PERSON>e\" class=\"mw-redirect\" title=\"Comte de Grasse\"><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Saintes\" title=\"Battle of the Saintes\">Battle of the Saintes</a> off <a href=\"https://wikipedia.org/wiki/Dominica\" title=\"Dominica\">Dominica</a> in the <a href=\"https://wikipedia.org/wiki/Caribbean_Sea\" title=\"Caribbean Sea\">Caribbean Sea</a>.", "links": [{"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of the Saintes", "link": "https://wikipedia.org/wiki/Battle_of_the_Saintes"}, {"title": "Dominica", "link": "https://wikipedia.org/wiki/Dominica"}, {"title": "Caribbean Sea", "link": "https://wikipedia.org/wiki/Caribbean_Sea"}]}, {"year": "1796", "text": "War of the First Coalition: <PERSON> wins his first victory as an army commander at the Battle of Montenotte, splitting the Austrian and Piedmontese armies away from each other, and marking the beginning of the Piedmontese surrender in the war.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> wins his first victory as an army commander at the <a href=\"https://wikipedia.org/wiki/Battle_of_Montenotte\" title=\"Battle of Montenotte\">Battle of Montenotte</a>, splitting the Austrian and Piedmontese armies away from each other, and marking the beginning of the Piedmontese surrender in the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> wins his first victory as an army commander at the <a href=\"https://wikipedia.org/wiki/Battle_of_Montenotte\" title=\"Battle of Montenotte\">Battle of Montenotte</a>, splitting the Austrian and Piedmontese armies away from each other, and marking the beginning of the Piedmontese surrender in the war.", "links": [{"title": "War of the First Coalition", "link": "https://wikipedia.org/wiki/War_of_the_First_Coalition"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Montenotte", "link": "https://wikipedia.org/wiki/Battle_of_Montenotte"}]}, {"year": "1807", "text": "The Froberg mutiny on Malta ends when the remaining mutineers blow up the magazine of Fort Ricasoli.", "html": "1807 - The <a href=\"https://wikipedia.org/wiki/Froberg_mutiny\" title=\"Froberg mutiny\">Froberg mutiny</a> on <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> ends when the remaining mutineers blow up the magazine of <a href=\"https://wikipedia.org/wiki/Fort_Ricasoli\" title=\"Fort Ricasoli\">Fort Ricasoli</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Froberg_mutiny\" title=\"Froberg mutiny\">Froberg mutiny</a> on <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> ends when the remaining mutineers blow up the magazine of <a href=\"https://wikipedia.org/wiki/Fort_Ricasoli\" title=\"Fort Ricasoli\">Fort Ricasoli</a>.", "links": [{"title": "Froberg mutiny", "link": "https://wikipedia.org/wiki/Froberg_mutiny"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "Fort Ricasoli", "link": "https://wikipedia.org/wiki/Fort_Ricasoli"}]}, {"year": "1820", "text": "<PERSON> is declared leader of Filiki Eteria, a secret organization to overthrow Ottoman rule over Greece.[citation needed]", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is declared leader of <a href=\"https://wikipedia.org/wiki/Filiki_Eteria\" title=\"Filiki Eteria\">Filiki Eteria</a>, a secret organization to overthrow <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> rule over Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is declared leader of <a href=\"https://wikipedia.org/wiki/Filiki_Eteria\" title=\"Filiki Eteria\">Filiki Eteria</a>, a secret organization to overthrow <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> rule over Greece.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Filiki Eteria", "link": "https://wikipedia.org/wiki/Filiki_Eteria"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1831", "text": "Soldiers marching on the Broughton Suspension Bridge in Manchester, England, cause it to collapse.", "html": "1831 - Soldiers marching on the <a href=\"https://wikipedia.org/wiki/Broughton_Suspension_Bridge\" title=\"Broughton Suspension Bridge\">Broughton Suspension Bridge</a> in <a href=\"https://wikipedia.org/wiki/Manchester\" title=\"Manchester\">Manchester</a>, England, cause it to collapse.", "no_year_html": "Soldiers marching on the <a href=\"https://wikipedia.org/wiki/Broughton_Suspension_Bridge\" title=\"Broughton Suspension Bridge\">Broughton Suspension Bridge</a> in <a href=\"https://wikipedia.org/wiki/Manchester\" title=\"Manchester\">Manchester</a>, England, cause it to collapse.", "links": [{"title": "Broughton Suspension Bridge", "link": "https://wikipedia.org/wiki/B<PERSON>ton_Suspension_Bridge"}, {"title": "Manchester", "link": "https://wikipedia.org/wiki/Manchester"}]}, {"year": "1861", "text": "American Civil War: Battle of Fort Sumter. The war begins with Confederate forces firing on Fort Sumter, in the harbor of Charleston, South Carolina.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Sumter\" title=\"Battle of Fort Sumter\">Battle of Fort Sumter</a>. The war begins with <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces firing on <a href=\"https://wikipedia.org/wiki/Fort_Sumter\" title=\"Fort Sumter\">Fort Sumter</a>, in the harbor of <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Sumter\" title=\"Battle of Fort Sumter\">Battle of Fort Sumter</a>. The war begins with <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces firing on <a href=\"https://wikipedia.org/wiki/Fort_Sumter\" title=\"Fort Sumter\">Fort Sumter</a>, in the harbor of <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Fort Sumter", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Sumter"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Fort Sumter", "link": "https://wikipedia.org/wiki/Fort_Sumter"}, {"title": "Charleston, South Carolina", "link": "https://wikipedia.org/wiki/Charleston,_South_Carolina"}]}, {"year": "1862", "text": "American Civil War: The Andrews Raid (the Great Locomotive Chase) occurs, starting from Big Shanty, Georgia (now Kennesaw).", "html": "1862 - American Civil War: The Andrews Raid (the <a href=\"https://wikipedia.org/wiki/Great_Locomotive_Chase\" title=\"Great Locomotive Chase\">Great Locomotive Chase</a>) occurs, starting from Big Shanty, Georgia (now <a href=\"https://wikipedia.org/wiki/Kennesaw,_Georgia\" title=\"Kennesaw, Georgia\">Kennesaw</a>).", "no_year_html": "American Civil War: The Andrews Raid (the <a href=\"https://wikipedia.org/wiki/Great_Locomotive_Chase\" title=\"Great Locomotive Chase\">Great Locomotive Chase</a>) occurs, starting from Big Shanty, Georgia (now <a href=\"https://wikipedia.org/wiki/Kennesaw,_Georgia\" title=\"Kennesaw, Georgia\">Kennesaw</a>).", "links": [{"title": "Great Locomotive Chase", "link": "https://wikipedia.org/wiki/Great_Locomotive_Chase"}, {"title": "Kennesaw, Georgia", "link": "https://wikipedia.org/wiki/Kennesaw,_Georgia"}]}, {"year": "1864", "text": "American Civil War: The Battle of Fort Pillow: Confederate forces kill most of the African American soldiers that surrendered at Fort Pillow, Tennessee.", "html": "1864 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Pillow\" title=\"Battle of Fort Pillow\">Battle of Fort Pillow</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_Army\" title=\"Confederate States Army\">Confederate</a> forces kill most of the <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African American</a> soldiers that surrendered at <a href=\"https://wikipedia.org/wiki/Fort_Pillow,_Tennessee\" class=\"mw-redirect\" title=\"Fort Pillow, Tennessee\">Fort Pillow, Tennessee</a>.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Pillow\" title=\"Battle of Fort Pillow\">Battle of Fort Pillow</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_Army\" title=\"Confederate States Army\">Confederate</a> forces kill most of the <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African American</a> soldiers that surrendered at <a href=\"https://wikipedia.org/wiki/Fort_Pillow,_Tennessee\" class=\"mw-redirect\" title=\"Fort Pillow, Tennessee\">Fort Pillow, Tennessee</a>.", "links": [{"title": "Battle of Fort Pillow", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Pillow"}, {"title": "Confederate States Army", "link": "https://wikipedia.org/wiki/Confederate_States_Army"}, {"title": "African Americans", "link": "https://wikipedia.org/wiki/African_Americans"}, {"title": "Fort Pillow, Tennessee", "link": "https://wikipedia.org/wiki/Fort_Pillow,_Tennessee"}]}, {"year": "1865", "text": "American Civil War: Mobile, Alabama, falls to the Union Army.", "html": "1865 - American Civil War: <a href=\"https://wikipedia.org/wiki/Mobile,_Alabama\" title=\"Mobile, Alabama\">Mobile, Alabama</a>, falls to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union Army</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Mobile,_Alabama\" title=\"Mobile, Alabama\">Mobile, Alabama</a>, falls to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union Army</a>.", "links": [{"title": "Mobile, Alabama", "link": "https://wikipedia.org/wiki/Mobile,_Alabama"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1877", "text": "The United Kingdom annexes the Transvaal.", "html": "1877 - The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> annexes the <a href=\"https://wikipedia.org/wiki/South_African_Republic\" title=\"South African Republic\">Transvaal</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> annexes the <a href=\"https://wikipedia.org/wiki/South_African_Republic\" title=\"South African Republic\">Transvaal</a>.", "links": [{"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "South African Republic", "link": "https://wikipedia.org/wiki/South_African_Republic"}]}, {"year": "1900", "text": "One day after its enactment by the Congress, President <PERSON> signs the Foraker Act into law, giving Puerto Rico limited self-rule.", "html": "1900 - One day after its enactment by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\"><PERSON></a>, President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Foraker_Act\" title=\"Foraker Act\">Foraker Act</a> into law, giving <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a> limited <a href=\"https://wikipedia.org/wiki/Self-rule\" class=\"mw-redirect\" title=\"Self-rule\">self-rule</a>.", "no_year_html": "One day after its enactment by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Foraker_Act\" title=\"Foraker Act\">Foraker Act</a> into law, giving <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a> limited <a href=\"https://wikipedia.org/wiki/Self-rule\" class=\"mw-redirect\" title=\"Self-rule\">self-rule</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Foraker Act", "link": "https://wikipedia.org/wiki/Foraker_Act"}, {"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}, {"title": "Self-rule", "link": "https://wikipedia.org/wiki/Self-rule"}]}, {"year": "1910", "text": "SMS Zrínyi, one of the last pre-dreadnought battleships built by the Austro-Hungarian Navy, is launched.[citation needed]", "html": "1910 - <a href=\"https://wikipedia.org/wiki/SMS_Zr%C3%ADnyi\" title=\"SMS Zrínyi\">SMS <i>Zrínyi</i></a>, one of the last <a href=\"https://wikipedia.org/wiki/Pre-dreadnought_battleship\" title=\"Pre-dreadnought battleship\">pre-dreadnought battleships</a> built by the <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian</a> Navy, is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SMS_Zr%C3%ADnyi\" title=\"SMS Zrínyi\">SMS <i>Zrínyi</i></a>, one of the last <a href=\"https://wikipedia.org/wiki/Pre-dreadnought_battleship\" title=\"Pre-dreadnought battleship\">pre-dreadnought battleships</a> built by the <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian</a> Navy, is launched.", "links": [{"title": "SMS Zrínyi", "link": "https://wikipedia.org/wiki/SMS_Zr%C3%ADnyi"}, {"title": "Pre-dreadnought battleship", "link": "https://wikipedia.org/wiki/Pre-dreadnought_battleship"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}]}, {"year": "1917", "text": "World War I: Canadian forces successfully complete the taking of Vimy Ridge from the Germans.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Canadian_Expeditionary_Force\" title=\"Canadian Expeditionary Force\">Canadian</a> forces successfully complete the taking of <a href=\"https://wikipedia.org/wiki/Battle_of_Vimy_Ridge\" title=\"Battle of Vimy Ridge\">Vimy Ridge</a> from the <a href=\"https://wikipedia.org/wiki/Germans\" title=\"Germans\">Germans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Canadian_Expeditionary_Force\" title=\"Canadian Expeditionary Force\">Canadian</a> forces successfully complete the taking of <a href=\"https://wikipedia.org/wiki/Battle_of_Vimy_Ridge\" title=\"Battle of Vimy Ridge\">Vimy Ridge</a> from the <a href=\"https://wikipedia.org/wiki/Germans\" title=\"Germans\">Germans</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Canadian Expeditionary Force", "link": "https://wikipedia.org/wiki/Canadian_Expeditionary_Force"}, {"title": "Battle of Vimy Ridge", "link": "https://wikipedia.org/wiki/Battle_of_Vimy_Ridge"}, {"title": "Germans", "link": "https://wikipedia.org/wiki/Germans"}]}, {"year": "1927", "text": "Shanghai massacre of 1927: <PERSON> orders the Chinese Communist Party members executed in Shanghai, ending the First United Front.[citation needed]", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Shanghai_massacre_of_1927\" class=\"mw-redirect\" title=\"Shanghai massacre of 1927\">Shanghai massacre of 1927</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> members executed in Shanghai, ending the <a href=\"https://wikipedia.org/wiki/First_United_Front\" title=\"First United Front\">First United Front</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shanghai_massacre_of_1927\" class=\"mw-redirect\" title=\"Shanghai massacre of 1927\">Shanghai massacre of 1927</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> members executed in Shanghai, ending the <a href=\"https://wikipedia.org/wiki/First_United_Front\" title=\"First United Front\">First United Front</a>.", "links": [{"title": "Shanghai massacre of 1927", "link": "https://wikipedia.org/wiki/Shanghai_massacre_of_1927"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Chinese Communist Party", "link": "https://wikipedia.org/wiki/Chinese_Communist_Party"}, {"title": "First United Front", "link": "https://wikipedia.org/wiki/First_United_Front"}]}, {"year": "1927", "text": "Rocksprings, Texas is hit by an F5 tornado that destroys 235 of the 247 buildings in the town, kills 72 townspeople, and injures 205; third deadliest tornado in Texas history.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Rocksprings,_Texas\" title=\"Rocksprings, Texas\">Rocksprings, Texas</a> is hit by an F5 tornado that destroys 235 of the 247 buildings in the town, kills 72 townspeople, and injures 205; third deadliest tornado in <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rocksprings,_Texas\" title=\"Rocksprings, Texas\">Rocksprings, Texas</a> is hit by an F5 tornado that destroys 235 of the 247 buildings in the town, kills 72 townspeople, and injures 205; third deadliest tornado in <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> history.", "links": [{"title": "Rocksprings, Texas", "link": "https://wikipedia.org/wiki/Rocksprings,_Texas"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}]}, {"year": "1928", "text": "The Bremen, a German Junkers W 33 type aircraft, takes off for the first successful transatlantic aeroplane flight from east to west.", "html": "1928 - The <i><a href=\"https://wikipedia.org/wiki/Bremen_(aircraft)\" title=\"Bremen (aircraft)\">Bremen</a></i>, a German <a href=\"https://wikipedia.org/wiki/Junkers_W_33\" title=\"Junkers W 33\">Junkers W 33</a> type aircraft, takes off for the first successful transatlantic aeroplane flight from east to west.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Bremen_(aircraft)\" title=\"Bremen (aircraft)\">Bremen</a></i>, a German <a href=\"https://wikipedia.org/wiki/Junkers_W_33\" title=\"Junkers W 33\">Junkers W 33</a> type aircraft, takes off for the first successful transatlantic aeroplane flight from east to west.", "links": [{"title": "Bremen (aircraft)", "link": "https://wikipedia.org/wiki/Bremen_(aircraft)"}, {"title": "Junkers W 33", "link": "https://wikipedia.org/wiki/Junkers_W_33"}]}, {"year": "1934", "text": "The strongest surface wind gust in the world at the time of 231 mph, is measured on the summit of Mount Washington, New Hampshire. It has since been surpassed.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Wind_speed#Highest_speed\" title=\"Wind speed\">strongest surface wind</a> gust in the world at the time of 231 mph, is measured on the summit of <a href=\"https://wikipedia.org/wiki/Mount_Washington_(New_Hampshire)\" class=\"mw-redirect\" title=\"Mount Washington (New Hampshire)\">Mount Washington</a>, <a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a>. It has since been surpassed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wind_speed#Highest_speed\" title=\"Wind speed\">strongest surface wind</a> gust in the world at the time of 231 mph, is measured on the summit of <a href=\"https://wikipedia.org/wiki/Mount_Washington_(New_Hampshire)\" class=\"mw-redirect\" title=\"Mount Washington (New Hampshire)\">Mount Washington</a>, <a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a>. It has since been surpassed.", "links": [{"title": "Wind speed", "link": "https://wikipedia.org/wiki/Wind_speed#Highest_speed"}, {"title": "Mount Washington (New Hampshire)", "link": "https://wikipedia.org/wiki/Mount_Washington_(New_Hampshire)"}, {"title": "New Hampshire", "link": "https://wikipedia.org/wiki/New_Hampshire"}]}, {"year": "1934", "text": "The U.S. Auto-Lite strike begins, culminating in a five-day melee between Ohio National Guard troops and 6,000 strikers and picketers.[citation needed]", "html": "1934 - The U.S. <a href=\"https://wikipedia.org/wiki/Auto-Lite_strike\" class=\"mw-redirect\" title=\"Auto-Lite strike\">Auto-Lite strike</a> begins, culminating in a five-day melee between <a href=\"https://wikipedia.org/wiki/Ohio_National_Guard\" title=\"Ohio National Guard\">Ohio National Guard</a> troops and 6,000 <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strikers and picketers</a>.", "no_year_html": "The U.S. <a href=\"https://wikipedia.org/wiki/Auto-Lite_strike\" class=\"mw-redirect\" title=\"Auto-Lite strike\">Auto-Lite strike</a> begins, culminating in a five-day melee between <a href=\"https://wikipedia.org/wiki/Ohio_National_Guard\" title=\"Ohio National Guard\">Ohio National Guard</a> troops and 6,000 <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strikers and picketers</a>.", "links": [{"title": "Auto-Lite strike", "link": "https://wikipedia.org/wiki/Auto-Lite_strike"}, {"title": "Ohio National Guard", "link": "https://wikipedia.org/wiki/Ohio_National_Guard"}, {"title": "Strike action", "link": "https://wikipedia.org/wiki/Strike_action"}]}, {"year": "1937", "text": "Sir <PERSON> ground-tests the first jet engine designed to power an aircraft, at Rugby, England.", "html": "1937 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> ground-tests the first <a href=\"https://wikipedia.org/wiki/Jet_engine\" title=\"Jet engine\">jet engine</a> designed to power an aircraft, at <a href=\"https://wikipedia.org/wiki/Rugby,_Warwickshire\" title=\"Rugby, Warwickshire\">Rugby, England</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> ground-tests the first <a href=\"https://wikipedia.org/wiki/Jet_engine\" title=\"Jet engine\">jet engine</a> designed to power an aircraft, at <a href=\"https://wikipedia.org/wiki/Rugby,_Warwickshire\" title=\"Rugby, Warwickshire\">Rugby, England</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jet engine", "link": "https://wikipedia.org/wiki/Jet_engine"}, {"title": "Rugby, Warwickshire", "link": "https://wikipedia.org/wiki/Rugby,_Warwickshire"}]}, {"year": "1945", "text": "U.S. President <PERSON> dies in office; Vice President <PERSON> becomes President upon <PERSON>'s death.", "html": "1945 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in office; Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes President upon <PERSON>'s death.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in office; Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes President upon <PERSON>'s death.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "World War II: The U.S. Ninth Army under General <PERSON> crosses the Elbe River astride Magdeburg, and reaches Tangermünde—only 50 miles from Berlin.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Ninth_United_States_Army\" class=\"mw-redirect\" title=\"Ninth United States Army\">U.S. Ninth Army</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> crosses the <a href=\"https://wikipedia.org/wiki/Elbe\" title=\"Elbe\">Elbe River</a> astride <a href=\"https://wikipedia.org/wiki/Magdeburg\" title=\"Magdeburg\">Magdeburg</a>, and reaches <a href=\"https://wikipedia.org/wiki/Tangerm%C3%BCnde\" title=\"Tangermünde\">Tangermünde</a>—only 50 miles from <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Ninth_United_States_Army\" class=\"mw-redirect\" title=\"Ninth United States Army\">U.S. Ninth Army</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> crosses the <a href=\"https://wikipedia.org/wiki/Elbe\" title=\"Elbe\">Elbe River</a> astride <a href=\"https://wikipedia.org/wiki/Magdeburg\" title=\"Magdeburg\">Magdeburg</a>, and reaches <a href=\"https://wikipedia.org/wiki/Tangerm%C3%BCnde\" title=\"Tangermünde\">Tangermünde</a>—only 50 miles from <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Ninth United States Army", "link": "https://wikipedia.org/wiki/Ninth_United_States_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Elbe", "link": "https://wikipedia.org/wiki/Elbe"}, {"title": "Magdeburg", "link": "https://wikipedia.org/wiki/Magdeburg"}, {"title": "Tangermünde", "link": "https://wikipedia.org/wiki/Tangerm%C3%BCnde"}, {"title": "Berlin", "link": "https://wikipedia.org/wiki/Berlin"}]}, {"year": "1955", "text": "The polio vaccine, developed by Dr. <PERSON>, is declared safe and effective.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a>, developed by Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is declared <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#Polio_research\" title=\"<PERSON>\">safe and effective</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a>, developed by Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is declared <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#Polio_research\" title=\"<PERSON>\">safe and effective</a>.", "links": [{"title": "Polio vaccine", "link": "https://wikipedia.org/wiki/Polio_vaccine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>#Polio_research"}]}, {"year": "1961", "text": "Space Race: The Soviet cosmonaut <PERSON> becomes the first human to travel into outer space and perform the first crewed orbital flight, Vostok 1.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Space_Race\" title=\"Space Race\">Space Race</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Cosmonaut\" class=\"mw-redirect\" title=\"Cosmonaut\">cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first human to travel into <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">outer space</a> and perform the first crewed <a href=\"https://wikipedia.org/wiki/Orbit\" title=\"Orbit\">orbital</a> flight, <a href=\"https://wikipedia.org/wiki/Vostok_1\" title=\"Vostok 1\">Vostok 1</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Race\" title=\"Space Race\">Space Race</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Cosmonaut\" class=\"mw-redirect\" title=\"Cosmonaut\">cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first human to travel into <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">outer space</a> and perform the first crewed <a href=\"https://wikipedia.org/wiki/Orbit\" title=\"Orbit\">orbital</a> flight, <a href=\"https://wikipedia.org/wiki/Vostok_1\" title=\"Vostok 1\">Vostok 1</a>.", "links": [{"title": "Space Race", "link": "https://wikipedia.org/wiki/Space_Race"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Cosmonaut", "link": "https://wikipedia.org/wiki/Cosmonaut"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Outer space", "link": "https://wikipedia.org/wiki/Outer_space"}, {"title": "Orbit", "link": "https://wikipedia.org/wiki/Orbit"}, {"title": "Vostok 1", "link": "https://wikipedia.org/wiki/Vostok_1"}]}, {"year": "1963", "text": "The Soviet nuclear-powered submarine K-33 collides with the Finnish merchant vessel M/S Finnclipper in the Danish straits.[citation needed]", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> nuclear-powered submarine <i><a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-33\" title=\"Soviet submarine K-33\">K-33</a></i> collides with the Finnish merchant vessel M/S <i>Finnclipper</i> in the Danish straits.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> nuclear-powered submarine <i><a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-33\" title=\"Soviet submarine K-33\">K-33</a></i> collides with the Finnish merchant vessel M/S <i>Finnclipper</i> in the Danish straits.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Soviet submarine K-33", "link": "https://wikipedia.org/wiki/Soviet_submarine_K-33"}]}, {"year": "1970", "text": "Soviet submarine K-8, carrying four nuclear torpedoes, sinks in the Bay of Biscay four days after a fire on board.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-8\" title=\"Soviet submarine K-8\">Soviet submarine <i>K-8</i></a>, carrying four nuclear torpedoes, sinks in the <a href=\"https://wikipedia.org/wiki/Bay_of_Biscay\" title=\"Bay of Biscay\">Bay of Biscay</a> four days after a fire on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-8\" title=\"Soviet submarine K-8\">Soviet submarine <i>K-8</i></a>, carrying four nuclear torpedoes, sinks in the <a href=\"https://wikipedia.org/wiki/Bay_of_Biscay\" title=\"Bay of Biscay\">Bay of Biscay</a> four days after a fire on board.", "links": [{"title": "Soviet submarine K-8", "link": "https://wikipedia.org/wiki/Soviet_submarine_K-8"}, {"title": "Bay of Biscay", "link": "https://wikipedia.org/wiki/Bay_of_Biscay"}]}, {"year": "1980", "text": "The Americo-Liberian government of Liberia is violently deposed.", "html": "1980 - The Americo-Liberian government of Liberia is <a href=\"https://wikipedia.org/wiki/1980_Liberian_coup_d%27%C3%A9tat\" title=\"1980 Liberian coup d'état\">violently deposed</a>.", "no_year_html": "The Americo-Liberian government of Liberia is <a href=\"https://wikipedia.org/wiki/1980_Liberian_coup_d%27%C3%A9tat\" title=\"1980 Liberian coup d'état\">violently deposed</a>.", "links": [{"title": "1980 Liberian coup d'état", "link": "https://wikipedia.org/wiki/1980_Liberian_coup_d%27%C3%A9tat"}]}, {"year": "1980", "text": "Transbrasil Flight 303, a Boeing 727, crashes on approach to Hercílio <PERSON>z International Airport in Florianópolis, Brazil. Fifty-five out of the 58 people on board are killed.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Transbrasil_Flight_303\" title=\"Transbrasil Flight 303\">Transbrasil Flight 303</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a>, crashes on approach to <a href=\"https://wikipedia.org/wiki/Herc%C3%ADlio_Luz_International_Airport\" title=\"Hercílio Luz International Airport\">Hercílio <PERSON>z International Airport</a> in <a href=\"https://wikipedia.org/wiki/Florian%C3%B3polis\" title=\"Florianópolis\">Florianópolis</a>, Brazil. Fifty-five out of the 58 people on board are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Transbrasil_Flight_303\" title=\"Transbrasil Flight 303\">Transbrasil Flight 303</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a>, crashes on approach to <a href=\"https://wikipedia.org/wiki/Herc%C3%ADlio_Luz_International_Airport\" title=\"Hercílio Luz International Airport\">Hercílio <PERSON>z International Airport</a> in <a href=\"https://wikipedia.org/wiki/Florian%C3%B3polis\" title=\"Florianópolis\">Florianópolis</a>, Brazil. Fifty-five out of the 58 people on board are killed.", "links": [{"title": "Transbrasil Flight 303", "link": "https://wikipedia.org/wiki/Transbrasil_Flight_303"}, {"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "<PERSON><PERSON><PERSON>lio <PERSON> International Airport", "link": "https://wikipedia.org/wiki/Herc%C3%ADlio_Luz_International_Airport"}, {"title": "Florianópolis", "link": "https://wikipedia.org/wiki/Florian%C3%B3polis"}]}, {"year": "1980", "text": "Canadian runner and athlete, <PERSON> begins his Marathon of Hope Run in St. John's, NF", "html": "1980 - Canadian runner and athlete, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his Marathon of Hope Run in <a href=\"https://wikipedia.org/wiki/<PERSON>._John%27s,_Newfoundland_and_Labrador\" title=\"St. John's, Newfoundland and Labrador\">St. John's, NF</a>", "no_year_html": "Canadian runner and athlete, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his Marathon of Hope Run in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_John%27s,_Newfoundland_and_Labrador\" title=\"St. John's, Newfoundland and Labrador\">St. John's, NF</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "St. John's, Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s,_Newfoundland_and_Labrador"}]}, {"year": "1981", "text": "The first launch of a Space Shuttle (Columbia) takes place: The STS-1 mission.", "html": "1981 - The first launch of a <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> (<i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Columbia</a></i>) takes place: The <a href=\"https://wikipedia.org/wiki/STS-1\" title=\"STS-1\">STS-1</a> mission.", "no_year_html": "The first launch of a <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> (<i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Columbia</a></i>) takes place: The <a href=\"https://wikipedia.org/wiki/STS-1\" title=\"STS-1\">STS-1</a> mission.", "links": [{"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-1", "link": "https://wikipedia.org/wiki/STS-1"}]}, {"year": "1983", "text": "<PERSON> is elected as the first black mayor of Chicago.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the first black mayor of <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harold Washington\"><PERSON></a> is elected as the first black mayor of <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}]}, {"year": "1985", "text": "Space Shuttle Discovery launches on STS-51D to deploy two communications satellites.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-51-D\" title=\"STS-51-D\">STS-51D</a> to deploy two <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communications satellites</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-51-D\" title=\"STS-51-D\">STS-51D</a> to deploy two <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communications satellites</a>.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-51-D", "link": "https://wikipedia.org/wiki/STS-51-D"}, {"title": "Communications satellite", "link": "https://wikipedia.org/wiki/Communications_satellite"}]}, {"year": "1990", "text": "<PERSON>'s \"Twentieth Century Dinosaurs\" exhibition opens at the Smithsonian Institution National Museum of Natural History in Washington, D.C. He is the only sculptor ever invited to present a solo exhibition there.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s \"Twentieth Century Dinosaurs\" exhibition opens at the <a href=\"https://wikipedia.org/wiki/Smithsonian_Institution\" title=\"Smithsonian Institution\">Smithsonian Institution</a> <a href=\"https://wikipedia.org/wiki/National_Museum_of_Natural_History\" title=\"National Museum of Natural History\">National Museum of Natural History</a> in Washington, D.C. He is the only sculptor ever invited to present a <a href=\"https://wikipedia.org/wiki/Solo_exhibition\" title=\"Solo exhibition\">solo exhibition</a> there.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s \"Twentieth Century Dinosaurs\" exhibition opens at the <a href=\"https://wikipedia.org/wiki/Smithsonian_Institution\" title=\"Smithsonian Institution\">Smithsonian Institution</a> <a href=\"https://wikipedia.org/wiki/National_Museum_of_Natural_History\" title=\"National Museum of Natural History\">National Museum of Natural History</a> in Washington, D.C. He is the only sculptor ever invited to present a <a href=\"https://wikipedia.org/wiki/Solo_exhibition\" title=\"Solo exhibition\">solo exhibition</a> there.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Smithsonian Institution", "link": "https://wikipedia.org/wiki/Smithsonian_Institution"}, {"title": "National Museum of Natural History", "link": "https://wikipedia.org/wiki/National_Museum_of_Natural_History"}, {"title": "Solo exhibition", "link": "https://wikipedia.org/wiki/Solo_exhibition"}]}, {"year": "1990", "text": "Widerøe Flight 839 crashes after takeoff from Værøy Airport in Norway, killing five people.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Wider%C3%B8e_Flight_839\" title=\"Widerøe Flight 839\">Widerøe Flight 839</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/V%C3%A6r%C3%B8y_Airport\" title=\"Værøy Airport\">Værøy Airport</a> in <a href=\"https://wikipedia.org/wiki/Norway\" title=\"Norway\">Norway</a>, killing five people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wider%C3%B8e_Flight_839\" title=\"Widerøe Flight 839\">Widerøe Flight 839</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/V%C3%A6r%C3%B8y_Airport\" title=\"Værøy Airport\">Værøy Airport</a> in <a href=\"https://wikipedia.org/wiki/Norway\" title=\"Norway\">Norway</a>, killing five people.", "links": [{"title": "Widerøe Flight 839", "link": "https://wikipedia.org/wiki/Wider%C3%B8e_Flight_839"}, {"title": "Værøy Airport", "link": "https://wikipedia.org/wiki/V%C3%A6r%C3%B8y_Airport"}, {"title": "Norway", "link": "https://wikipedia.org/wiki/Norway"}]}, {"year": "1992", "text": "The Euro Disney Resort officially opens with its theme park Euro Disneyland; the resort and its park's name are subsequently changed to Disneyland Resort Paris.", "html": "1992 - The <i>Euro Disney Resort</i> officially opens with its theme park <i><a href=\"https://wikipedia.org/wiki/Disneyland_Park_(Paris)\" title=\"Disneyland Park (Paris)\">Euro Disneyland</a></i>; the resort and its park's name are subsequently changed to <a href=\"https://wikipedia.org/wiki/Disneyland_Resort_Paris\" class=\"mw-redirect\" title=\"Disneyland Resort Paris\">Disneyland Resort Paris</a>.", "no_year_html": "The <i>Euro Disney Resort</i> officially opens with its theme park <i><a href=\"https://wikipedia.org/wiki/Disneyland_Park_(Paris)\" title=\"Disneyland Park (Paris)\">Euro Disneyland</a></i>; the resort and its park's name are subsequently changed to <a href=\"https://wikipedia.org/wiki/Disneyland_Resort_Paris\" class=\"mw-redirect\" title=\"Disneyland Resort Paris\">Disneyland Resort Paris</a>.", "links": [{"title": "Disneyland Park (Paris)", "link": "https://wikipedia.org/wiki/Disneyland_Park_(Paris)"}, {"title": "Disneyland Resort Paris", "link": "https://wikipedia.org/wiki/Disneyland_Resort_Paris"}]}, {"year": "1999", "text": "United States President <PERSON> is cited for contempt of court for giving \"intentionally false statements\" in a civil lawsuit; he is later fined and disbarred.", "html": "1999 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is cited for <a href=\"https://wikipedia.org/wiki/Contempt_of_court\" title=\"Contempt of court\">contempt of court</a> for giving \"intentionally false statements\" in a <a href=\"https://wikipedia.org/wiki/Civil_lawsuit\" class=\"mw-redirect\" title=\"Civil lawsuit\">civil lawsuit</a>; he is later fined and disbarred.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is cited for <a href=\"https://wikipedia.org/wiki/Contempt_of_court\" title=\"Contempt of court\">contempt of court</a> for giving \"intentionally false statements\" in a <a href=\"https://wikipedia.org/wiki/Civil_lawsuit\" class=\"mw-redirect\" title=\"Civil lawsuit\">civil lawsuit</a>; he is later fined and disbarred.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Contempt of court", "link": "https://wikipedia.org/wiki/Contempt_of_court"}, {"title": "Civil lawsuit", "link": "https://wikipedia.org/wiki/Civil_lawsuit"}]}, {"year": "1999", "text": "During the NATO bombing of Yugoslavia, an American McDonnell Douglas F-15E Strike Eagle shoots a passenger train, killing between 20 and 60 people.", "html": "1999 - During the <a href=\"https://wikipedia.org/wiki/NATO_bombing_of_Yugoslavia\" title=\"NATO bombing of Yugoslavia\">NATO bombing of Yugoslavia</a>, an American <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F-15E_Strike_Eagle\" title=\"McDonnell Douglas F-15E Strike Eagle\">McDonnell Douglas F-15E Strike Eagle</a> <a href=\"https://wikipedia.org/wiki/Grdelica_train_bombing\" title=\"Grdelica train bombing\">shoots a passenger train</a>, killing between 20 and 60 people.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/NATO_bombing_of_Yugoslavia\" title=\"NATO bombing of Yugoslavia\">NATO bombing of Yugoslavia</a>, an American <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F-15E_Strike_Eagle\" title=\"McDonnell Douglas F-15E Strike Eagle\">McDonnell Douglas F-15E Strike Eagle</a> <a href=\"https://wikipedia.org/wiki/Grdelica_train_bombing\" title=\"Grdelica train bombing\">shoots a passenger train</a>, killing between 20 and 60 people.", "links": [{"title": "NATO bombing of Yugoslavia", "link": "https://wikipedia.org/wiki/NATO_bombing_of_Yugoslavia"}, {"title": "McDonnell Douglas F-15E Strike Eagle", "link": "https://wikipedia.org/wiki/<PERSON>_Douglas_F-15E_Strike_Eagle"}, {"title": "Grdelica train bombing", "link": "https://wikipedia.org/wiki/Grdelica_train_bombing"}]}, {"year": "2002", "text": "A suicide bomber blows herself up at the entrance to Jerusalem's Mahane <PERSON>da Market, killing seven people and wounding 104.", "html": "2002 - A suicide bomber <a href=\"https://wikipedia.org/wiki/2002_<PERSON><PERSON>_<PERSON>_Market_bombing\" title=\"2002 Mahane <PERSON> Market bombing\">blows herself up</a> at the entrance to Jerusalem's <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Mahane <PERSON> Market\">Mahane <PERSON> Market</a>, killing seven people and wounding 104.", "no_year_html": "A suicide bomber <a href=\"https://wikipedia.org/wiki/2002_<PERSON><PERSON>_<PERSON>_Market_bombing\" title=\"2002 <PERSON>hane <PERSON> bombing\">blows herself up</a> at the entrance to Jerusalem's <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Market\" title=\"Mahane <PERSON> Market\">Mahane <PERSON> Market</a>, killing seven people and wounding 104.", "links": [{"title": "2002 <PERSON>hane <PERSON> bombing", "link": "https://wikipedia.org/wiki/2002_<PERSON><PERSON>_<PERSON>_Market_bombing"}, {"title": "Mahane <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "A suicide bomber penetrates the Green Zone and detonates in a cafeteria within a parliament building, killing Iraqi MP <PERSON> and wounding more than twenty other people.", "html": "2007 - A suicide bomber <a href=\"https://wikipedia.org/wiki/2007_Iraqi_Parliament_bombing\" title=\"2007 Iraqi Parliament bombing\">penetrates the Green Zone and detonates</a> in a cafeteria within a parliament building, killing Iraqi MP <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a> and wounding more than twenty other people.", "no_year_html": "A suicide bomber <a href=\"https://wikipedia.org/wiki/2007_Iraqi_Parliament_bombing\" title=\"2007 Iraqi Parliament bombing\">penetrates the Green Zone and detonates</a> in a cafeteria within a parliament building, killing Iraqi MP <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a> and wounding more than twenty other people.", "links": [{"title": "2007 Iraqi Parliament bombing", "link": "https://wikipedia.org/wiki/2007_Iraqi_Parliament_bombing"}, {"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(politician)"}]}, {"year": "2009", "text": "Zimbabwe officially abandons the Zimbabwean dollar as its official currency.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a> officially abandons the <a href=\"https://wikipedia.org/wiki/Zimbabwean_dollar\" title=\"Zimbabwean dollar\">Zimbabwean dollar</a> as its official currency.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a> officially abandons the <a href=\"https://wikipedia.org/wiki/Zimbabwean_dollar\" title=\"Zimbabwean dollar\">Zimbabwean dollar</a> as its official currency.", "links": [{"title": "Zimbabwe", "link": "https://wikipedia.org/wiki/Zimbabwe"}, {"title": "Zimbabwean dollar", "link": "https://wikipedia.org/wiki/Zimbabwean_dollar"}]}, {"year": "2010", "text": "Merano derailment: A rail accident in South Tyrol kills nine people and injures a further 28.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Merano_derailment\" title=\"Merano derailment\">Merano derailment</a>: A rail accident in <a href=\"https://wikipedia.org/wiki/South_Tyrol\" title=\"South Tyrol\">South Tyrol</a> kills nine people and injures a further 28.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Merano_derailment\" title=\"Merano derailment\">Merano derailment</a>: A rail accident in <a href=\"https://wikipedia.org/wiki/South_Tyrol\" title=\"South Tyrol\">South Tyrol</a> kills nine people and injures a further 28.", "links": [{"title": "Merano derailment", "link": "https://wikipedia.org/wiki/Merano_derailment"}, {"title": "South Tyrol", "link": "https://wikipedia.org/wiki/South_Tyrol"}]}, {"year": "2013", "text": "Two suicide bombers kill three Chadian soldiers and injure dozens of civilians at a market in Kidal, Mali.", "html": "2013 - Two <a href=\"https://wikipedia.org/wiki/2013_<PERSON><PERSON>_suicide_attack\" class=\"mw-redirect\" title=\"2013 Kidal suicide attack\">suicide bombers</a> kill three <a href=\"https://wikipedia.org/wiki/Military_of_Chad\" class=\"mw-redirect\" title=\"Military of Chad\">Chadian soldiers</a> and injure dozens of civilians at a market in <a href=\"https://wikipedia.org/wiki/Kidal\" title=\"Kidal\">Kidal, Mali</a>.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/2013_Kid<PERSON>_suicide_attack\" class=\"mw-redirect\" title=\"2013 Kidal suicide attack\">suicide bombers</a> kill three <a href=\"https://wikipedia.org/wiki/Military_of_Chad\" class=\"mw-redirect\" title=\"Military of Chad\">Chadian soldiers</a> and injure dozens of civilians at a market in <a href=\"https://wikipedia.org/wiki/Kidal\" title=\"Kidal\">Kidal, Mali</a>.", "links": [{"title": "2013 <PERSON><PERSON> suicide attack", "link": "https://wikipedia.org/wiki/2013_<PERSON><PERSON>_suicide_attack"}, {"title": "Military of Chad", "link": "https://wikipedia.org/wiki/Military_of_Chad"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kidal"}]}, {"year": "2014", "text": "The Great Fire of Valparaíso ravages the Chilean city of Valparaíso, killing 16 people, displacing nearly 10,000, and destroying over 2,000 homes.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/Great_Fire_of_Valpara%C3%ADso\" title=\"Great Fire of Valparaíso\">Great Fire of Valparaíso</a> ravages the <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> city of <a href=\"https://wikipedia.org/wiki/Valpara%C3%ADso\" title=\"Valparaíso\">Valparaíso</a>, killing 16 people, displacing nearly 10,000, and destroying over 2,000 homes.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Fire_of_Valpara%C3%ADso\" title=\"Great Fire of Valparaíso\">Great Fire of Valparaíso</a> ravages the <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> city of <a href=\"https://wikipedia.org/wiki/Valpara%C3%ADso\" title=\"Valparaíso\">Valparaíso</a>, killing 16 people, displacing nearly 10,000, and destroying over 2,000 homes.", "links": [{"title": "Great Fire of Valparaíso", "link": "https://wikipedia.org/wiki/Great_Fire_of_Valpara%C3%ADso"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "Valparaíso", "link": "https://wikipedia.org/wiki/Valpara%C3%ADso"}]}], "Births": [{"year": "811", "text": "<PERSON>, the ninth Imam of Shia Islam (d. 835)", "html": "811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the ninth Imam of Shia Islam (d. 835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the ninth Imam of Shia Islam (d. 835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "959", "text": "<PERSON><PERSON><PERSON><PERSON>, emperor of Japan (d. 991)", "html": "959 - <a href=\"https://wikipedia.org/wiki/Emperor_En%27y%C5%AB\" title=\"Emperor <PERSON>'y<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_En%27y%C5%AB\" title=\"Emperor <PERSON>'y<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 991)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_En%27y%C5%AB"}]}, {"year": "1116", "text": "<PERSON><PERSON><PERSON> of Poland, queen of Sweden and Grand Princess of Minsk (d. 1156)", "html": "1116 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Sweden\" title=\"<PERSON><PERSON><PERSON> of Poland, Queen of Sweden\"><PERSON><PERSON><PERSON> of Poland</a>, queen of Sweden and Grand Princess of Minsk (d. 1156)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Sweden\" title=\"<PERSON><PERSON><PERSON> of Poland, Queen of Sweden\"><PERSON><PERSON><PERSON> of Poland</a>, queen of Sweden and Grand Princess of Minsk (d. 1156)", "links": [{"title": "<PERSON><PERSON><PERSON> of Poland, Queen of Sweden", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Sweden"}]}, {"year": "1432", "text": "<PERSON> of Austria, Land<PERSON><PERSON><PERSON> of Thuringia (d. 1462)", "html": "1432 - <a href=\"https://wikipedia.org/wiki/Anne_of_Austria,_Landgravine_of_Thuringia\" title=\"<PERSON> of Austria, Landgravine of Thuringia\"><PERSON> of Austria, Landgra<PERSON>e of Thuringia</a> (d. 1462)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anne_of_Austria,_Landgravine_of_Thuringia\" title=\"<PERSON> of Austria, Landgravine of Thuringia\"><PERSON> of Austria, Landgravine of Thuringia</a> (d. 1462)", "links": [{"title": "<PERSON> of Austria, Landgravine of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Austria,_Landgravine_of_Thuringia"}]}, {"year": "1484", "text": "<PERSON> the Younger, Italian architect, designed the Apostolic Palace and St. Peter's Basilica (d. 1546)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Apostolic_Palace\" title=\"Apostolic Palace\">Apostolic Palace</a> and <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Basilica\" title=\"St. Peter's Basilica\">St. Peter's Basilica</a> (d. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Apostolic_Palace\" title=\"Apostolic Palace\">Apostolic Palace</a> and <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Basilica\" title=\"St. Peter's Basilica\">St. Peter's Basilica</a> (d. 1546)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Younger"}, {"title": "Apostolic Palace", "link": "https://wikipedia.org/wiki/Apostolic_Palace"}, {"title": "St. Peter's Basilica", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s_Basilica"}]}, {"year": "1484", "text": "<PERSON><PERSON><PERSON>, Rana of Mewar (d. 1527)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON>na <PERSON></a>, Rana of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rana Sang<PERSON>\"><PERSON><PERSON><PERSON></a>, Rana of <a href=\"https://wikipedia.org/wiki/Mewar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1527)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mewar"}]}, {"year": "1500", "text": "<PERSON>, German scholar and translator (d. 1574)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and translator (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and translator (d. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1526", "text": "<PERSON><PERSON><PERSON>, French philosopher and author (d. 1585)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author (d. 1585)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Muretus"}]}, {"year": "1550", "text": "<PERSON>, 17th Earl of Oxford, English courtier and politician, Lord Great <PERSON> (d. 1604)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_17th_Earl_of_Oxford\" title=\"<PERSON>, 17th Earl of Oxford\"><PERSON>, 17th Earl of Oxford</a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Great_Chamberlain\" title=\"Lord Great Chamberlain\">Lord Great <PERSON></a> (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_17th_Earl_of_Oxford\" title=\"<PERSON>, 17th Earl of Oxford\"><PERSON>, 17th Earl of Oxford</a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Great_Chamberlain\" title=\"Lord Great Chamberlain\">Lord Great <PERSON></a> (d. 1604)", "links": [{"title": "<PERSON>, 17th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_17th_Earl_of_Oxford"}, {"title": "Lord Great <PERSON>", "link": "https://wikipedia.org/wiki/Lord_Great_<PERSON>"}]}, {"year": "1577", "text": "<PERSON> of Denmark (d. 1648)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/Christian_IV_of_Denmark\" title=\"Christian IV of Denmark\">Christian IV of Denmark</a> (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_IV_of_Denmark\" title=\"Christian IV of Denmark\">Christian IV of Denmark</a> (d. 1648)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_IV_of_Denmark"}]}, {"year": "1612", "text": "<PERSON>, Italian painter and engraver (d. 1648)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and engraver (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and engraver (d. 1648)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ini"}]}, {"year": "1639", "text": "<PERSON>, English naturalist and physician (d. 1712)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English naturalist and physician (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English naturalist and physician (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1656", "text": "<PERSON><PERSON><PERSON><PERSON>, French diplomat and natural historian (d. 1738)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_de_Maillet\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French diplomat and natural historian (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_de_Maillet\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French diplomat and natural historian (d. 1738)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_de_Maillet"}]}, {"year": "1705", "text": "<PERSON>, English minister and pharmacist (d. 1780)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and pharmacist (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and pharmacist (d. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1710", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian actor and singer (d. 1783)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(castrato)\" title=\"<PERSON><PERSON><PERSON><PERSON> (castrato)\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actor and singer (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(castrato)\" title=\"<PERSON><PERSON><PERSON><PERSON> (castrato)\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actor and singer (d. 1783)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (castrato)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(castrato)"}]}, {"year": "1713", "text": "<PERSON>, French historian and author (d. 1796)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_<PERSON>nal\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_<PERSON>nal\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Raynal"}]}, {"year": "1716", "text": "<PERSON><PERSON>, Italian violinist and composer (d. 1796)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian violinist and composer (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian violinist and composer (d. 1796)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ini"}]}, {"year": "1722", "text": "<PERSON>, Italian violinist and composer (d. 1793)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON><PERSON>, American physician, clergyman, and politician, 16th Governor of Georgia (d. 1790)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/Lyman_Hall\" title=\"Lyman Hall\"><PERSON>yman Hall</a>, American physician, clergyman, and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lyman_Hall\" title=\"Lyman Hall\"><PERSON><PERSON> Hall</a>, American physician, clergyman, and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (d. 1790)", "links": [{"title": "Lyman Hall", "link": "https://wikipedia.org/wiki/Lyman_Hall"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "1748", "text": "<PERSON>, French botanist and author (d. 1836)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and author (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and author (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, American lawyer and politician, 9th United States Secretary of State (d. 1852)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1792", "text": "<PERSON>, 1st Earl of Durham, English soldier and politician, Lord Privy Seal (d. 1840)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Durham\" title=\"<PERSON>, 1st Earl of Durham\"><PERSON>, 1st Earl of Durham</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Privy_Seal\" title=\"Lord Privy Seal\">Lord Privy Seal</a> (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Durham\" title=\"<PERSON>, 1st Earl of Durham\"><PERSON>, 1st Earl of Durham</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Privy_Seal\" title=\"Lord Privy Seal\">Lord Privy Seal</a> (d. 1840)", "links": [{"title": "<PERSON>, 1st Earl of Durham", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Durham"}, {"title": "Lord Privy Seal", "link": "https://wikipedia.org/wiki/Lord_Privy_Seal"}]}, {"year": "1794", "text": "Germina<PERSON> <PERSON>, Belgian mathematician and engineer (d. 1847)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Germinal_<PERSON>\" title=\"Germinal <PERSON>\">Germina<PERSON> <PERSON></a>, Belgian mathematician and engineer (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Germina<PERSON>_<PERSON>_<PERSON>\" title=\"Germinal <PERSON>\">Germina<PERSON> <PERSON></a>, Belgian mathematician and engineer (d. 1847)", "links": [{"title": "Germinal <PERSON>", "link": "https://wikipedia.org/wiki/Germina<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, American lawyer and politician, 19th Governor of Massachusetts (d. 1861)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1799", "text": "<PERSON>, Swiss lawyer and politician, 2nd President of the Swiss Confederation (d. 1855)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1801", "text": "<PERSON>, Austrian composer and conductor (d. 1843)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, Irish-Australian politician, 8th Premier of Victoria (d. 1903)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Irish-Australian politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Irish-Australian politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1903)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1823", "text": "<PERSON>, Russian playwright and translator (d. 1886)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian playwright and translator (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian playwright and translator (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON>, Russian geographer and explorer (d. 1888)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian geographer and explorer (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian geographer and explorer (d. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON>, Swedish painter (d. 1933)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Gustaf_Cederstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish painter (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gusta<PERSON>_Cederstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish painter (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gustaf_Cederstr%C3%B6m"}]}, {"year": "1851", "text": "<PERSON>, Puerto Rican soldier and poet (d. 1880)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gautier_Ben%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican soldier and poet (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gautier_Ben%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican soldier and poet (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Gautier_Ben%C3%ADtez"}]}, {"year": "1851", "text": "<PERSON>, English astronomer and author (d. 1928)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and author (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and author (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, German mathematician and academic (d. 1939)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, 1st Baron <PERSON> of Allington, English mountaineer, cartographer, and politician (d. 1937)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Allington\" title=\"<PERSON>, 1st Baron <PERSON> of Allington\"><PERSON>, 1st Baron <PERSON> of Allington</a>, English mountaineer, cartographer, and politician (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Allington\" title=\"<PERSON>, 1st Baron <PERSON> of Allington\"><PERSON>, 1st Baron <PERSON> of Allington</a>, English mountaineer, cartographer, and politician (d. 1937)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Allington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Allington"}]}, {"year": "1863", "text": "<PERSON><PERSON>, Brazilian writer (d. 1895)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pompeia\"><PERSON><PERSON></a>, Brazilian writer (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pompeia\"><PERSON><PERSON></a>, Brazilian writer (d. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mpeia"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Japanese admiral (d. 1918)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Saneyuki\" title=\"<PERSON><PERSON><PERSON> Saneyuki\"><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>uki\" title=\"<PERSON><PERSON>yama Saneyuki\"><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, French serial killer (d. 1922)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9sir%C3%A9_Landru\" title=\"<PERSON>\"><PERSON></a>, French serial killer (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henri_D%C3%A9sir%C3%A9_Landru\" title=\"Henri Désir<PERSON>\"><PERSON></a>, French serial killer (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_D%C3%A9sir%C3%A9_Landru"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Greek general and politician, 130th Prime Minister of Greece (d. 1941)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general and politician, 130th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general and politician, 130th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ax<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1874", "text": "<PERSON>, American lawyer and politician, 47th Speaker of the United States House of Representatives (d. 1940)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 47th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 47th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Bankhead"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1880", "text": "<PERSON><PERSON>, American baseball player and journalist (d. 1911)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and journalist (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and journalist (d. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, American photographer and educator (d. 1976)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Imo<PERSON>\"><PERSON><PERSON><PERSON></a>, American photographer and educator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American photographer and educator (d. 1976)", "links": [{"title": "Imogen Cunningham", "link": "https://wikipedia.org/wiki/Imogen_Cunningham"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Australian rugby player, cricketer, and sailor (d. 1959)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby player, cricketer, and sailor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby player, cricketer, and sailor (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Welsh runner (d. 1932)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh runner (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh runner (d. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, German physician and biochemist, Nobel Prize laureate (d. 1951)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1885", "text": "<PERSON>, French painter (d. 1941)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American actor and director (d. 1918)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Irish-American long jumper and police officer (d. 1942)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American long jumper and police officer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American long jumper and police officer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, English automobile engineer (d. 1945)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English automobile engineer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English automobile engineer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American writer and artist (d. 1973)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and artist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and artist (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Australian-American actress (d. 1983)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American actress (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American actress (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Portuguese field marshal and politician, 13th President of Portugal (d. 1964)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese field marshal and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese field marshal and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON><PERSON>_Lo<PERSON>"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1898", "text": "<PERSON>, French-American soprano and actress (d. 1976)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American soprano and actress (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American soprano and actress (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ns"}]}, {"year": "1901", "text": "<PERSON>, American farmer and politician (d. 1962)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Stockman\" title=\"<PERSON> Stockman\"><PERSON></a>, American farmer and politician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Stockman\" title=\"<PERSON> Stockman\"><PERSON></a>, American farmer and politician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lowell_Stockman"}]}, {"year": "1902", "text": "<PERSON>, Dutch academic and politician, 36th Prime Minister of the Netherlands (d. 1977)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch academic and politician, 36th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch academic and politician, 36th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1903", "text": "<PERSON>, Dutch economist and academic, Nobel Prize laureate (d. 1994)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1907", "text": "<PERSON>, Austrian-American sculptor, designed the Marine Corps War Memorial (d. 2003)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American sculptor, designed the <a href=\"https://wikipedia.org/wiki/Marine_Corps_War_Memorial\" title=\"Marine Corps War Memorial\">Marine Corps War Memorial</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American sculptor, designed the <a href=\"https://wikipedia.org/wiki/Marine_Corps_War_Memorial\" title=\"Marine Corps War Memorial\">Marine Corps War Memorial</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Marine Corps War Memorial", "link": "https://wikipedia.org/wiki/Marine_Corps_War_Memorial"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Burmese poet, author, literary historian, critic, scholar and academic (d. 1990)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON><PERSON></a>, Burmese poet, author, literary historian, critic, scholar and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON><PERSON></a>, Burmese poet, author, literary historian, critic, scholar and academic (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(writer)"}]}, {"year": "1908", "text": "<PERSON>, English author and painter (d. 2013)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Ida_Pollock\" title=\"<PERSON> Pollock\"><PERSON></a>, English author and painter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ida_Pollock\" title=\"Ida Pollock\"><PERSON></a>, English author and painter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_Pollock"}]}, {"year": "1908", "text": "<PERSON>, Jr., American pilot and general (d. 2006)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American pilot and general (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American pilot and general (d. 2006)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1910", "text": "<PERSON><PERSON>, Italian art critic, painter and philosopher (d. 2018)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian art critic, painter and philosopher (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian art critic, painter and philosopher (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, French politician (d. 2018)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French politician (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Egyptian engineer (d. 1976)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian engineer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian engineer (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Canadian businessman (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indonesian politician, 2nd Vice President of Indonesia (d. 1988)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Hamengkubuwono_IX\" title=\"Hamengkubuwono IX\">Hamengkubuwono IX</a>, Indonesian politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Vice_Presidents_of_Indonesia\" class=\"mw-redirect\" title=\"List of Vice Presidents of Indonesia\">Vice President of Indonesia</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamengkubuwono_IX\" title=\"Hamengkubuwono IX\">Hamengkubuwono IX</a>, Indonesian politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Vice_Presidents_of_Indonesia\" class=\"mw-redirect\" title=\"List of Vice Presidents of Indonesia\">Vice President of Indonesia</a> (d. 1988)", "links": [{"title": "Hamengkubuwono IX", "link": "https://wikipedia.org/wiki/Hamengkubuwono_IX"}, {"title": "List of Vice Presidents of Indonesia", "link": "https://wikipedia.org/wiki/List_of_Vice_Presidents_of_Indonesia"}]}, {"year": "1912", "text": "Hound <PERSON>, American singer-songwriter and guitarist (d. 1975)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Hound_<PERSON>_<PERSON>\" title=\"Hound Dog Taylor\">Hound <PERSON></a>, American singer-songwriter and guitarist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hound_<PERSON>_<PERSON>\" title=\"Hound Dog Taylor\">Hound <PERSON></a>, American singer-songwriter and guitarist (d. 1975)", "links": [{"title": "Hound <PERSON>", "link": "https://wikipedia.org/wiki/Hound_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Japanese-American martial artist (d. 2013)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American martial artist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American martial artist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American economist and academic (d. 2013)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Armen_Alchian\" title=\"Armen Alchian\"><PERSON><PERSON></a>, American economist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armen_Alchian\" title=\"Armen Alchian\"><PERSON><PERSON></a>, American economist and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Armen_Alchian"}]}, {"year": "1916", "text": "<PERSON>, American author (d. 2021)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Clear<PERSON>\"><PERSON></a>, American author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Beverly Cleary\"><PERSON></a>, American author (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Beverly_Cleary"}]}, {"year": "1916", "text": "<PERSON>, American-New Zealand composer and conductor (d. 2011)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American-New Zealand composer and conductor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American-New Zealand composer and conductor (d. 2011)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>(composer)"}]}, {"year": "1916", "text": "<PERSON>, American neuropsychologist and academic (d. 2007)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuropsychologist and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuropsychologist and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American singer and actress (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Indian cricketer (d. 1978)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Vinoo_Mankad\" title=\"Vinoo Mankad\"><PERSON><PERSON></a>, Indian cricketer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vinoo_Mankad\" title=\"Vinoo Mankad\"><PERSON><PERSON></a>, Indian cricketer (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vinoo_Mankad"}]}, {"year": "1917", "text": "<PERSON>, French racing driver (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Hungarian-Canadian composer and educator (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Anhalt\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Canadian composer and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Anhalt\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Canadian composer and educator (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Anhalt"}]}, {"year": "1919", "text": "<PERSON>, American musician and bandleader (d. 1991)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and bandleader (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and bandleader (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Canadian lawyer, judge, and politician (d. 1978)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Zambian politician, 2nd Vice President of Zambia (d. 1980)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian politician, 2nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zambia\" class=\"mw-redirect\" title=\"Vice President of Zambia\">Vice President of Zambia</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian politician, 2nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zambia\" class=\"mw-redirect\" title=\"Vice President of Zambia\">Vice President of Zambia</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Zambia", "link": "https://wikipedia.org/wiki/Vice_President_of_Zambia"}]}, {"year": "1923", "text": "<PERSON>, American actress, singer, and dancer (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, French economist and politician, Prime Minister of France (d. 2007)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1924", "text": "<PERSON>, Austrian physician and academic (d. 2003)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American race car driver (d. 1970)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American computer scientist and engineer (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American country music singer and songwriter (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer and songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer and songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English animator, puppeteer, and screenwriter (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English animator, puppeteer, and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Postgate\"><PERSON></a>, English animator, puppeteer, and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_<PERSON>gate"}]}, {"year": "1926", "text": "<PERSON>, American actress (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English baritone (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English baritone (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English baritone (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American screenwriter (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German actor (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%<PERSON>ger\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%BCger\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hardy_Kr%C3%BCger"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, French conductor (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French conductor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French conductor (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Scottish actress (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Elspet_Gray\" title=\"Elsp<PERSON> Gray\"><PERSON><PERSON><PERSON></a>, Scottish actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elspet_Gray\" title=\"Elspet Gray\"><PERSON><PERSON><PERSON></a>, Scottish actress (d. 2013)", "links": [{"title": "Elspet Gray", "link": "https://wikipedia.org/wiki/Elspet_Gray"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Georgian poet and educator (d. 2010)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian poet and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian poet and educator (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1930", "text": "<PERSON>, Australian runner and politician, 26th Governor of Victoria (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Victoria\" title=\"Governor of Victoria\">Governor of Victoria</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Victoria\" title=\"Governor of Victoria\">Governor of Victoria</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Victoria", "link": "https://wikipedia.org/wiki/Governor_of_Victoria"}]}, {"year": "1930", "text": "<PERSON>, English philosopher and politician (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American sculptor and painter (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Greek lyricist and playwright (d. 1979)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>matiou\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>matio<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek lyricist and playwright (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>matiou\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>mat<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek lyricist and playwright (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pythagoras_Papastamatiou"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Polish technician and educator (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Micha%C5%82_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish technician and educator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Micha%C5%82_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish technician and educator (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C5%82_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Russian poet and songwriter (d. 1995)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and songwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and songwriter (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Sri Lankan lawyer and politician, 5th Sri Lankan Minister of Foreign Affairs (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Sri_Lanka)\" title=\"Minister of Foreign Affairs (Sri Lanka)\">Sri Lankan Minister of Foreign Affairs</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Sri_Lanka)\" title=\"Minister of Foreign Affairs (Sri Lanka)\">Sri Lankan Minister of Foreign Affairs</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Sri Lanka)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Sri_Lanka)"}]}, {"year": "1932", "text": "<PERSON>, American singer and ukulele player (d. 1996)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and ukulele player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and ukulele player (d. 1996)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, French actor (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Spanish soprano and actress (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Montserrat_Caball%C3%A9\" title=\"Montserrat Caballé\"><PERSON><PERSON><PERSON></a>, Spanish soprano and actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montserrat_Caball%C3%A9\" title=\"Montserrat Caballé\"><PERSON><PERSON><PERSON></a>, Spanish soprano and actress (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Montserrat_Caball%C3%A9"}]}, {"year": "1934", "text": "<PERSON>, Swiss footballer and manager (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer and manager (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer and manager (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Greek singer (d. 2007)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek singer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American politician, 40th Governor of Wisconsin (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 40th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 40th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Wisconsin", "link": "https://wikipedia.org/wiki/Governor_of_Wisconsin"}]}, {"year": "1936", "text": "<PERSON>, American actor (d. 2011)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2011)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1936", "text": "<PERSON>, Kittitian politician, 4th Prime Minister of Saint Kitts and <PERSON><PERSON><PERSON>", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kittitian politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Saint_Kitts_and_<PERSON>ev<PERSON>\" class=\"mw-redirect\" title=\"List of Prime Ministers of Saint Kitts and Nevis\">Prime Minister of Saint Kitts and Nevis</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kittitian politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Saint_Kitts_and_<PERSON>ev<PERSON>\" class=\"mw-redirect\" title=\"List of Prime Ministers of Saint Kitts and Nevis\">Prime Minister of Saint Kitts and Nevis</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Saint Kitts and Nevis", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_<PERSON>_Kit<PERSON>_and_<PERSON><PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American author and activist (d. 2017)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Ukrainian-Russian colonel, pilot, and astronaut (d. 2017)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian colonel, pilot, and astronaut (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian colonel, pilot, and astronaut (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English director and playwright", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian rugby league player and coach (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Raper\"><PERSON></a>, Australian rugby league player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American baseball player (d. 2011)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American pianist, composer, and bandleader", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and bandleader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and bandleader", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English footballer and manager (d. 1993)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Scottish actor, director, and screenwriter (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and screenwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Argentinian race car driver and politician (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, South African politician, 4th President of South Africa", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of South Africa", "link": "https://wikipedia.org/wiki/President_of_South_Africa"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Indian politician, 16th Speaker of the Lok Sabha", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 16th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 16th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the Lok Sabha", "link": "https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha"}]}, {"year": "1944", "text": "<PERSON>, English historian, author, and academic (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German-Canadian singer-songwriter, guitarist, and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, German-Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, German-Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1945", "text": "<PERSON>, South Korean physician and diplomat (d. 2006)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wook\" title=\"<PERSON>wook\"><PERSON>w<PERSON></a>, South Korean physician and diplomat (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wook\" title=\"<PERSON>wook\"><PERSON>w<PERSON></a>, South Korean physician and diplomat (d. 2006)", "links": [{"title": "<PERSON>wook", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-wook"}]}, {"year": "1946", "text": "<PERSON>, Canadian actor and comedian (d. 2017)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and comedian (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and comedian (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor and comedian", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Ed_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ed_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ed_O%27Neill"}]}, {"year": "1946", "text": "<PERSON>, <PERSON> of Port Ellen, Scottish politician and diplomat, 10th Secretary General of NATO", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Port_Ellen\" title=\"<PERSON>, <PERSON> of Port Ellen\"><PERSON>, <PERSON> of Port Ellen</a>, Scottish politician and diplomat, 10th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Port_Ellen\" title=\"<PERSON>, <PERSON> of Port Ellen\"><PERSON>, <PERSON> of Port Ellen</a>, Scottish politician and diplomat, 10th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a>", "links": [{"title": "<PERSON>, <PERSON> of Port Ellen", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Port_Ellen"}, {"title": "Secretary General of NATO", "link": "https://wikipedia.org/wiki/Secretary_General_of_NATO"}]}, {"year": "1947", "text": "<PERSON>, English epidemiologist, zoologist, and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English epidemiologist, zoologist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English epidemiologist, zoologist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English palaeontologist, biologist, and academic (d. 2014)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English palaeontologist, biologist, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English palaeontologist, biologist, and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American historian and author (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American comedian and talk show host", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Wayne_<PERSON>rop\" title=\"Wayne <PERSON>rop\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wayne_<PERSON>rop\" title=\"Wayne Northrop\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wayne_Northrop"}]}, {"year": "1948", "text": "<PERSON>, English television host and producer (d. 2008)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, German academic and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German academic and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Greek weightlifter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek weightlifter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Italian footballer, manager, and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer, manager, and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lipp<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer, manager, and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lippi"}]}, {"year": "1949", "text": "<PERSON>, American lawyer and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, South African politician (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Malawian politician, 4th president of Malawi", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malawian politician, 4th president of Malawi", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malawian politician, 4th president of Malawi", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Italian businessman", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Flavio_Briatore\" title=\"Flavio Briatore\"><PERSON><PERSON><PERSON></a>, Italian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flavio_Briatore\" title=\"Flavio Briatore\"><PERSON><PERSON><PERSON></a>, Italian businessman", "links": [{"title": "<PERSON>lav<PERSON>", "link": "https://wikipedia.org/wiki/Flavio_Briatore"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2017)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English composer and educator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American football player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, New Zealand rugby player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Leicester_Rutledge\" title=\"Leicester Rutledge\"><PERSON> Rutledge</a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leicester_Rutledge\" title=\"Leicester Rutledge\"><PERSON> Rutledge</a>, New Zealand rugby player", "links": [{"title": "Leicester Rutledge", "link": "https://wikipedia.org/wiki/Leicester_Rutledge"}]}, {"year": "1952", "text": "<PERSON>, American poet, novelist, and memoirist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and memoirist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and memoirist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American journalist (d. 2004)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Italian author and illustrator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Tan<PERSON>_Liberatore\" title=\"Tanino Liberatore\"><PERSON><PERSON></a>, Italian author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tan<PERSON>_Liberatore\" title=\"Tanino Liberatore\"><PERSON><PERSON></a>, Italian author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tanino_Liberatore"}]}, {"year": "1954", "text": "<PERSON>, Australian educator and politician, 52nd Australian Minister for Defence", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 52nd <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 52nd <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1954", "text": "<PERSON>, Belgian businessman and politician (d. 2015)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Travers"}]}, {"year": "1955", "text": "<PERSON>, English graphic designer, engineer, and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English graphic designer, engineer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English graphic designer, engineer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Cuban-American actor, director, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Cuban-American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Cuban-American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German singer-songwriter and actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Herbert_Gr%C3%B6<PERSON><PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian mountaineer and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian mountaineer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian mountaineer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American novelist and short story writer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Will_Sergeant\" title=\"Will Sergeant\"><PERSON></a>, English guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Will_Sergeant\" title=\"Will Sergeant\"><PERSON></a>, English guitarist", "links": [{"title": "Will <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, German javelin thrower", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German javelin thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Bulgarian hurdler", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American basketball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Italian racing driver", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Fabi\" title=\"Co<PERSON><PERSON> Fabi\"><PERSON><PERSON><PERSON></a>, Italian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Fabi\" title=\"Corra<PERSON> Fabi\"><PERSON><PERSON><PERSON></a>, Italian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>abi"}]}, {"year": "1961", "text": "<PERSON>, American football player and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, English-Australian actress, comedian and writer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Australian actress, comedian and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Australian actress, comedian and writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and musician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Alexakis"}]}, {"year": "1962", "text": "<PERSON>, Spanish racing driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish racing driver", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Japanese mixed martial artist and wrestler, founded Hustle", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mixed martial artist and wrestler, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(professional_wrestling)\" title=\"<PERSON><PERSON><PERSON> (professional wrestling)\"><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mixed martial artist and wrestler, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(professional_wrestling)\" title=\"<PERSON><PERSON><PERSON> (professional wrestling)\"><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> (professional wrestling)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(professional_wrestling)"}]}, {"year": "1963", "text": "<PERSON>, Mexican journalist and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lydia_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English footballer and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American folk-rock singer-songwriter, musician, and music producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk-rock singer-songwriter, musician, and music producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk-rock singer-songwriter, musician, and music producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Danish actor and director", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Onwurah\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Onwurah\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chi_Onwurah"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Burundian politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Burundian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Burundian politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>er<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Romanian footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian guitarist and singer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian guitarist and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian guitarist and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English singer-songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German songwriter and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, German songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gad\"><PERSON></a>, German songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ad"}]}, {"year": "1968", "text": "<PERSON>, Canadian ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American football player and politician (d. 2017)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player and politician (d. 2017)", "links": [{"title": "<PERSON> (wide receiver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B6rn_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B6rn_Lenz"}]}, {"year": "1969", "text": "<PERSON>, South African footballer and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian speed skater", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress, director, and producer (d. 2024)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and producer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American author and illustrator", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American trumpet player and composer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Mexican-American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian actress (d. 2006)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1974", "text": "<PERSON>, Czech ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Roman_Hamrl%C3%ADk\" title=\"Roman Hamrlík\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Hamrl%C3%ADk\" title=\"Roman Hamrlík\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Hamrl%C3%ADk"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Russian runner", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Ecuadorian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Giovanny_Espinoza\" title=\"Giovanny Espinoza\"><PERSON><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giovanny_<PERSON>spinoza\" title=\"Giovanny Espinoza\"><PERSON><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giovanny_Espinoza"}]}, {"year": "1977", "text": "<PERSON>, Australian actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Welsh footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian-Scottish cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Scottish cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Scottish cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Scottish bassist (<PERSON><PERSON>)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bassist (<a href=\"https://wikipedia.org/wiki/Coldplay\" title=\"Coldplay\">Coldplay</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bassist (<a href=\"https://wikipedia.org/wiki/Coldplay\" title=\"Coldplay\">Coldplay</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coldplay", "link": "https://wikipedia.org/wiki/Coldplay"}]}, {"year": "1978", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Russian high jumper", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian high jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English businessman and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Russian gymnast", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEman\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEman\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eja_Ke%C5%BEman"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>-young, South Korean singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>-young", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Welsh Paralympic table tennis champion", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Head\"><PERSON></a>, Welsh Paralympic table tennis champion", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Head\"><PERSON></a>, Welsh Paralympic table tennis champion", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Head"}]}, {"year": "1980", "text": "<PERSON>, Irish singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Russian runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Borz<PERSON>ovskiy"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Burdisso\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Burdisso\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Burdisso"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Tulsi_Gabbard\" title=\"Tulsi Gabbard\"><PERSON><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tulsi_Gabbard\" title=\"Tulsi Gabbard\"><PERSON><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tulsi_Gabbard"}]}, {"year": "1981", "text": "<PERSON>, English footballer and professional wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Japanese baseball pitcher", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>kuma\"><PERSON><PERSON></a>, Japanese baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball pitcher", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Serbian-Australian tennis player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-Australian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Kenyan runner", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Russian high jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian high jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball pitcher", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Swiss footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Blerim_D%C5%BEemaili\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Blerim_D%C5%BEemaili\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Blerim_D%C5%BEemaili"}]}, {"year": "1986", "text": "<PERSON>, Spanish tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Burkinabé footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burkinabé footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burkinabé footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Brazilian professional footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian professional footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American model and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Brooklyn_Decker\" title=\"Brooklyn Decker\"><PERSON> Decker</a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brooklyn_Decker\" title=\"Brooklyn Decker\"><PERSON> Decker</a>, American model and actress", "links": [{"title": "Brooklyn Decker", "link": "https://wikipedia.org/wiki/Brooklyn_Decker"}]}, {"year": "1987", "text": "<PERSON>, Canadian football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American singer, songwriter, musician and multi-instrumentalist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>\" title=\"Brendon <PERSON>\"><PERSON><PERSON><PERSON></a>, American singer, songwriter, musician and multi-instrumentalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>\" title=\"Bren<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer, songwriter, musician and multi-instrumentalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Argentinian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Egyptian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Moamen_Zakaria\" title=\"Moamen Zakaria\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moamen_Zakaria\" title=\"Moamen Zakaria\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "Moamen Zakaria", "link": "https://wikipedia.org/wiki/Moamen_Zakaria"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Welsh rugby union player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh rugby union player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/%C3%81d%C3%A1m_Hanga\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81d%C3%A1m_Hanga\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81d%C3%A1m_Hanga"}]}, {"year": "1989", "text": "<PERSON>, American-Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Ponce\" title=\"<PERSON>\"><PERSON></a>, American-Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Ponce"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Canadian-American ice dancer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American ice dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English swimmer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sa<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, French professional footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French professional footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Torey <PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Torey Krug\"><PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English born Northern Irish international footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English born Northern Irish international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English born Northern Irish international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Swedish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4%C3%A4j%C3%A4rvi\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_P%C3%A4%C3%A4j%C3%A4rvi\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Magnus_P%C3%A4%C3%A4j%C3%A4rvi"}]}, {"year": "1991", "text": "<PERSON>, Welsh international footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Richards"}]}, {"year": "1992", "text": "<PERSON>, South African swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Chad_le_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad_le_Clos\" title=\"<PERSON>\"><PERSON></a>, South African swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_le_<PERSON>los"}]}, {"year": "1993", "text": "<PERSON>, American tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1993", "text": "<PERSON>, English-Scottish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(goalkeeper,_born_1993)\" class=\"mw-redirect\" title=\"<PERSON> (goalkeeper, born 1993)\"><PERSON></a>, English-Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(goalkeeper,_born_1993)\" class=\"mw-redirect\" title=\"<PERSON> (goalkeeper, born 1993)\"><PERSON></a>, English-Scottish footballer", "links": [{"title": "<PERSON> (goalkeeper, born 1993)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(goalkeeper,_born_1993)"}]}, {"year": "1993", "text": "<PERSON>-<PERSON>, Canadian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Ivorian professional footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian professional footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Brazilian actress and singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Argentine footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guido_Rodr%C3%ADguez"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American-born Irish actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Saoir<PERSON> Ronan\"><PERSON><PERSON><PERSON></a>, American-born Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ronan\"><PERSON><PERSON><PERSON></a>, American-born Irish actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean musician", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hun_(entertainer)\" class=\"mw-redirect\" title=\"<PERSON>un (entertainer)\"><PERSON></a>, South Korean musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oh_<PERSON>-hun_(entertainer)\" class=\"mw-redirect\" title=\"<PERSON> <PERSON>hun (entertainer)\"><PERSON></a>, South Korean musician", "links": [{"title": "<PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hun_(entertainer)"}]}, {"year": "1995", "text": "<PERSON>, Argentine tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Italian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Russian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "45 BC", "text": "<PERSON><PERSON><PERSON>, Roman general and politician (b. 75 BC)", "html": "45 BC - 45 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(son_of_<PERSON><PERSON><PERSON>_the_Great)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (son of <PERSON><PERSON><PERSON> the Great)\"><PERSON><PERSON><PERSON></a>, Roman general and politician (b. 75 BC)", "no_year_html": "45 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(son_of_<PERSON><PERSON><PERSON>_the_Great)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (son of <PERSON><PERSON><PERSON> the Great)\"><PERSON><PERSON><PERSON></a>, Roman general and politician (b. 75 BC)", "links": [{"title": "<PERSON><PERSON><PERSON> (son of <PERSON><PERSON><PERSON> the <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(son_of_<PERSON><PERSON><PERSON>_the_Great)"}]}, {"year": "352", "text": "<PERSON>, pope of the Catholic Church", "html": "352 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Julius <PERSON>\"><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Julius <PERSON>\"><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "434", "text": "<PERSON><PERSON><PERSON>, archbishop of Constantinople", "html": "434 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON></a>, archbishop of Constantinople", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON></a>, archbishop of Constantinople", "links": [{"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Constantinople"}]}, {"year": "901", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine empress and wife of <PERSON>", "html": "901 - <a href=\"https://wikipedia.org/wiki/Eudokia_Ba%C3%AFana\" title=\"Eudoki<PERSON> Ba<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine empress and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_the_Wise\" title=\"<PERSON> VI the Wise\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eudokia_Ba%C3%AFana\" title=\"Eudokia Baï<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine empress and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_the_Wise\" title=\"<PERSON> VI the Wise\"><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eudokia_Ba%C3%AFana"}, {"title": "<PERSON> the Wise", "link": "https://wikipedia.org/wiki/<PERSON>_VI_the_<PERSON>"}]}, {"year": "1125", "text": "<PERSON><PERSON><PERSON>, Duke of Bohemia (b. 1065)", "html": "1125 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON>, Duke of Bohemia</a> (b. 1065)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON>, Duke of Bohemia</a> (b. 1065)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia"}]}, {"year": "1167", "text": "<PERSON>, king of Sweden (b. c. 1130)", "html": "1167 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_Sweden\" class=\"mw-redirect\" title=\"Charles VII of Sweden\"><PERSON> VII</a>, <a href=\"https://wikipedia.org/wiki/King_of_Sweden\" class=\"mw-redirect\" title=\"King of Sweden\">king of Sweden</a> (b. c. 1130)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_Sweden\" class=\"mw-redirect\" title=\"Charles VII of Sweden\"><PERSON> VII</a>, <a href=\"https://wikipedia.org/wiki/King_of_Sweden\" class=\"mw-redirect\" title=\"King of Sweden\">king of Sweden</a> (b. c. 1130)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_Sweden"}, {"title": "King of Sweden", "link": "https://wikipedia.org/wiki/King_of_Sweden"}]}, {"year": "1256", "text": "<PERSON> of Bourbon, Queen of Navarre, regent of Navarre (b. c. 1217)", "html": "1256 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Navarre\" title=\"<PERSON> of Bourbon, Queen of Navarre\"><PERSON> of Bourbon, Queen of Navarre</a>, regent of Navarre (b. c. 1217)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Navarre\" title=\"<PERSON> of Bourbon, Queen of Navarre\"><PERSON> of Bourbon, Queen of Navarre</a>, regent of Navarre (b. c. 1217)", "links": [{"title": "<PERSON> of Bourbon, Queen of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Navarre"}]}, {"year": "1443", "text": "<PERSON>, English archbishop (b. 1364)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1364)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1364)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1500", "text": "<PERSON><PERSON> of Gorizia, Count of Gorz (b. 1440)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Gorizia\" title=\"<PERSON><PERSON> of Gorizia\"><PERSON><PERSON> of Gorizia</a>, Count of Gorz (b. 1440)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Gorizia\" title=\"<PERSON><PERSON> of Gorizia\"><PERSON><PERSON> of Gorizia</a>, Count of Gorz (b. 1440)", "links": [{"title": "<PERSON><PERSON> of Gorizia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Gorizia"}]}, {"year": "1530", "text": "<PERSON>, Princess of Castile (b. 1462)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Princess of Castile (b. 1462)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Princess of Castile (b. 1462)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1550", "text": "<PERSON>, Duke of Guise (b. 1496)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guise\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a> (b. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guise\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a> (b. 1496)", "links": [{"title": "<PERSON>, Duke of Guise", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_G<PERSON>"}]}, {"year": "1555", "text": "<PERSON>, Queen of Castile and Aragon (b. 1479)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Castile\" title=\"Joanna of Castile\"><PERSON></a>, Queen of Castile and Aragon (b. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Castile\" title=\"Joanna of Castile\">Joanna</a>, Queen of Castile and Aragon (b. 1479)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/Joanna_<PERSON>_Castile"}]}, {"year": "1675", "text": "<PERSON>, English politician, colonial Governor of Virginia (b. 1609)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Governor)\" class=\"mw-redirect\" title=\"<PERSON> (Governor)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">colonial Governor of Virginia</a> (b. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Governor)\" class=\"mw-redirect\" title=\"<PERSON> (Governor)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">colonial Governor of Virginia</a> (b. 1609)", "links": [{"title": "<PERSON> (Governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Governor)"}, {"title": "List of colonial governors of Virginia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia"}]}, {"year": "1684", "text": "<PERSON>, Italian instrument maker (b. 1596)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian instrument maker (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian instrument maker (b. 1596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON>, English-American soldier (b. 1619)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soldier (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soldier (b. 1619)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French bishop and theologian (b. 1627)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French bishop and theologian (b. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French bishop and theologian (b. 1627)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jacques-B%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, English architect, designed Holkham Hall and Chiswick House (b. 1685)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/William_Kent\" title=\"William Kent\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Holkham_Hall\" title=\"Holkham Hall\">Holkham Hall</a> and <a href=\"https://wikipedia.org/wiki/Chiswick_House\" title=\"Chiswick House\">Chiswick House</a> (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Kent\" title=\"William Kent\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Holkham_Hall\" title=\"Holkham Hall\">Holkham Hall</a> and <a href=\"https://wikipedia.org/wiki/Chiswick_House\" title=\"Chiswick House\">Chiswick House</a> (b. 1685)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>"}, {"title": "Holkham Hall", "link": "https://wikipedia.org/wiki/Holkham_Hall"}, {"title": "Chiswick House", "link": "https://wikipedia.org/wiki/Chiswick_House"}]}, {"year": "1782", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-Austrian poet and composer (b. 1698)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/Metastasio\" class=\"mw-redirect\" title=\"Metastasio\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-Austrian poet and composer (b. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Metastasio\" class=\"mw-redirect\" title=\"Metastasi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-Austrian poet and composer (b. 1698)", "links": [{"title": "Metastasio", "link": "https://wikipedia.org/wiki/Metastasio"}]}, {"year": "1788", "text": "<PERSON>, French-Italian composer (b. 1719)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French-Italian composer (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French-Italian composer (b. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, Bavarian general (b. 1710)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_Ros%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Bavarian general (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_Ros%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Bavarian general (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_Ros%C3%A9e"}]}, {"year": "1814", "text": "<PERSON>, English composer and historian (b. 1726)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and historian (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and historian (b. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, French astronomer and academic (b. 1730)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (b. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (b. 1730)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON><PERSON>, American lexicographer and missionary (b. 1788)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lexicographer and missionary (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lexicographer and missionary (b. 1788)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>-<PERSON>, English politician, founded Fleetwood (b. 1801)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1801)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fleetwood", "link": "https://wikipedia.org/wiki/Fleetwood"}]}, {"year": "1872", "text": "<PERSON><PERSON>, Greek composer and theorist (b. 1795)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek composer and theorist (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek composer and theorist (b. 1795)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American lawyer and politician (b. 1823)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>weed\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>weed"}]}, {"year": "1879", "text": "<PERSON>, Confederate general (b. 1826)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Confederate_general)\" title=\"<PERSON> (Confederate general)\"><PERSON></a>, Confederate general (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Confederate_general)\" title=\"<PERSON> (Confederate general)\"><PERSON></a>, Confederate general (b. 1826)", "links": [{"title": "<PERSON> (Confederate general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Confederate_general)"}]}, {"year": "1885", "text": "<PERSON>, Dutch-Australian politician, 14th Premier of Tasmania (b. 1817)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Dutch-Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Dutch-Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1817)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian cardinal (b. 1820)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Elz%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elz%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1820)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elz%C3%A9<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, French physicist and academic (b. 1842)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Indian scholar, academic, and philanthropist (b. 1836)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>charyya\" title=\"<PERSON><PERSON><PERSON> <PERSON> Bhat<PERSON>charyya\"><PERSON><PERSON><PERSON></a>, Indian scholar, academic, and philanthropist (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>charyya\" title=\"<PERSON><PERSON><PERSON> <PERSON>hat<PERSON>chary<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian scholar, academic, and philanthropist (b. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>cha<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American nurse and humanitarian, founded the American Red Cross (b. 1821)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and humanitarian, founded the <a href=\"https://wikipedia.org/wiki/American_Red_Cross\" title=\"American Red Cross\">American Red Cross</a> (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and humanitarian, founded the <a href=\"https://wikipedia.org/wiki/American_Red_Cross\" title=\"American Red Cross\">American Red Cross</a> (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Red Cross", "link": "https://wikipedia.org/wiki/American_Red_Cross"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, American general and politician, 30th Governor of Mississippi (b. 1835)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Mississippi\" title=\"Governor of Mississippi\">Governor of Mississippi</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Mississippi\" title=\"Governor of Mississippi\">Governor of Mississippi</a> (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Mississippi", "link": "https://wikipedia.org/wiki/Governor_of_Mississippi"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish playwright and poet (b. 1852)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Abd%C3%BClhak_H%C3%A2mid_Tarhan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish playwright and poet (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abd%C3%BClhak_H%C3%A2mid_Tarhan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish playwright and poet (b. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abd%C3%BClhak_H%C3%A2mid_Tarhan"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Russian opera singer (b. 1873)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian opera singer (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian opera singer (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Estonian colonel (b. 1889)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian colonel (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian colonel (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American lawyer and politician, 32nd President of the United States (b. 1882)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1953", "text": "<PERSON>, Australian actor and therapist (b. 1880)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and therapist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and therapist (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Scottish racing driver (b. 1923)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Scottish racing driver (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Scottish racing driver (b. 1923)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1966", "text": "<PERSON>, English racing driver and founder of the Allard car company (b. 1910)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Sydney_Allard\" title=\"Sydney Allard\"><PERSON> Allard</a>, English racing driver and founder of the <a href=\"https://wikipedia.org/wiki/Allard_Motor_Company\" title=\"Allard Motor Company\">Allard</a> car company (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Allard\" title=\"Sydney Allard\"><PERSON> Allard</a>, English racing driver and founder of the <a href=\"https://wikipedia.org/wiki/Allard_Motor_Company\" title=\"Allard Motor Company\">Allard</a> car company (b. 1910)", "links": [{"title": "Sydney Allard", "link": "https://wikipedia.org/wiki/Sydney_Allard"}, {"title": "Allard Motor Company", "link": "https://wikipedia.org/wiki/Allard_Motor_Company"}]}, {"year": "1968", "text": "<PERSON>, German engineer (b. 1899)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player and dentist (b. 1886)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and dentist (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and dentist (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American songwriter and producer (b. 1894)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Freed\"><PERSON></a>, American songwriter and producer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Freed\"><PERSON></a>, American songwriter and producer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>d"}]}, {"year": "1975", "text": "<PERSON>, French actress, activist, and humanitarian (b. 1906)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, activist, and humanitarian (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, activist, and humanitarian (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Greek mountain guide (b. 1882)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek mountain guide (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek mountain guide (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American businessman, co-founded Lincoln Park Gun Club (b. 1894)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Lincoln_Park_Gun_Club\" title=\"Lincoln Park Gun Club\">Lincoln Park Gun Club</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Lincoln_Park_Gun_Club\" title=\"Lincoln Park Gun Club\">Lincoln Park Gun Club</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lincoln Park Gun Club", "link": "https://wikipedia.org/wiki/Lincoln_Park_Gun_Club"}]}, {"year": "1980", "text": "<PERSON>, Jr., Liberian politician, 20th President of Liberia (b. 1913)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Liberian politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Liberian politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (b. 1913)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1981", "text": "<PERSON> <PERSON><PERSON><PERSON> of Japan (b. 1887)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a> of Japan (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a> of Japan (b. 1887)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American boxer and wrestler (b. 1914)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and wrestler (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and wrestler (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Norwegian football player and journalist (b. 1906)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian football player and journalist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian football player and journalist (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>ve"}]}, {"year": "1983", "text": "<PERSON>, American baseball player (b. 1944)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American admiral and cryptanalyst (b. 1903)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and <a href=\"https://wikipedia.org/wiki/Cryptanalysis\" title=\"Cryptanalysis\">cryptanalyst</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and <a href=\"https://wikipedia.org/wiki/Cryptanalysis\" title=\"Cryptanalysis\">cryptanalyst</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Cryptanalysis", "link": "https://wikipedia.org/wiki/Cryptanalysis"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Russian author and playwright (b. 1897)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and playwright (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and playwright (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, French singer and actress (b. 1927)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Der%C3%A9al\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer and actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9al\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer and actress (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Colette_Der%C3%A9al"}]}, {"year": "1988", "text": "<PERSON>, South African historian and author (b. 1903)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African historian and author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African historian and author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American activist, co-founded Youth International Party (b. 1936)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist, co-founded <a href=\"https://wikipedia.org/wiki/Youth_International_Party\" title=\"Youth International Party\">Youth International Party</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist, co-founded <a href=\"https://wikipedia.org/wiki/Youth_International_Party\" title=\"Youth International Party\">Youth International Party</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}, {"title": "Youth International Party", "link": "https://wikipedia.org/wiki/Youth_International_Party"}]}, {"year": "1989", "text": "<PERSON>, American boxer (b. 1921)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Sugar Ray Robinson\"><PERSON></a>, American boxer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ray Robinson\"><PERSON></a>, American boxer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Italian racing driver and businessman (b. 1911)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ini\"><PERSON><PERSON></a>, Italian racing driver and businessman (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ini\"><PERSON><PERSON></a>, Italian racing driver and businessman (b. 1911)", "links": [{"title": "Ilario Bandini", "link": "https://wikipedia.org/wiki/Ilario_Bandini"}]}, {"year": "1997", "text": "<PERSON>, American neurologist and academic, Nobel Prize laureate (b. 1906)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1998", "text": "<PERSON>, Canadian poet and diplomat (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" class=\"mw-redirect\" title=\"<PERSON> (poet)\"><PERSON></a>, Canadian poet and diplomat (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" class=\"mw-redirect\" title=\"<PERSON> (poet)\"><PERSON></a>, Canadian poet and diplomat (b. 1915)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American singer-songwriter (b. 1931)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Willie\" title=\"Boxcar Willie\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Willie\" title=\"Boxcar Willie\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter (b. 1931)", "links": [{"title": "Boxcar Willie", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Willie"}]}, {"year": "2001", "text": "<PERSON>, American illustrator, created the smiley (b. 1921)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Ball\"><PERSON></a>, American illustrator, created the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">smiley</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Ball\"><PERSON></a>, American illustrator, created the <a href=\"https://wikipedia.org/wiki/Smiley\" title=\"<PERSON><PERSON>\">smiley</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Smiley"}]}, {"year": "2002", "text": "<PERSON>, Ukrainian-American linguist and philologist (b. 1908)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American linguist and philologist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American linguist and philologist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian physician and academic, invented the venturi mask (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and academic, invented the <a href=\"https://wikipedia.org/wiki/Venturi_mask\" title=\"Venturi mask\">venturi mask</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and academic, invented the <a href=\"https://wikipedia.org/wiki/Venturi_mask\" title=\"Venturi mask\">venturi mask</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Venturi mask", "link": "https://wikipedia.org/wiki/Venturi_mask"}]}, {"year": "2006", "text": "<PERSON>, American minister and activist (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Australian journalist (b. 1936)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English-American figure skater and coach (b. 1920)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American figure skater and coach (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American figure skater and coach (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Irish physician and politician, 6th President of Ireland (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_<PERSON>"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "2008", "text": "<PERSON>, Israeli-American businessman and philanthropist (b. 1949)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Israeli-American businessman and philanthropist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Israeli-American businessman and philanthropist (b. 1949)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "2010", "text": "<PERSON>, Canadian trade union leader (b. 1916)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trade union leader (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trade union leader (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, German director and screenwriter (b. 1945)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Bahraini journalist, co-founded Al-Wasat (b. 1962)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahraini journalist, co-founded <i><a href=\"https://wikipedia.org/wiki/Al-Wasat_(Bahraini_newspaper)\" title=\"Al-Wasat (Bahraini newspaper)\">Al-Wasat</a></i> (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahraini journalist, co-founded <i><a href=\"https://wikipedia.org/wiki/Al-Wasat_(Bahraini_newspaper)\" title=\"Al-Wasat (Bahraini newspaper)\">Al-Wasat</a></i> (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Al-Wasat (Bahraini newspaper)", "link": "https://wikipedia.org/wiki/Al-Wasat_(Bahraini_newspaper)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian poet and playwright (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ttopadhyay\" title=\"<PERSON><PERSON>padhyay\"><PERSON><PERSON></a>, Indian poet and playwright (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>padhyay\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and playwright (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Chattopadhyay"}]}, {"year": "2012", "text": "<PERSON>, American pianist and composer (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Grant\"><PERSON></a>, American pianist and composer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Grant\"><PERSON></a>, American pianist and composer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American chess player and author (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, American chess player and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, American chess player and author (b. 1928)", "links": [{"title": "<PERSON> (chess player)", "link": "https://wikipedia.org/wiki/<PERSON>_(chess_player)"}]}, {"year": "2013", "text": "<PERSON>, South African boxer (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African boxer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African boxer (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American screenwriter (b. 1962)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Michael <PERSON>\"><PERSON></a>, American screenwriter (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Michael_<PERSON>\" title=\"Michael <PERSON>\"><PERSON></a>, American screenwriter (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michael_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American priest and author (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and author (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and author (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian journalist and politician (b. 1961)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Annam%C3%A1ria_Szalai\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian journalist and politician (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Annam%C3%<PERSON><PERSON>_Szalai\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian journalist and politician (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Annam%C3%A1ria_Szalai"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli rabbi and politician (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Ya'<PERSON>kov Yosef\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli rabbi and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya%27<PERSON>ov_<PERSON><PERSON>\" title=\"Ya'<PERSON>kov Yosef\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli rabbi and politician (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya%27ak<PERSON>_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, French author and poet (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, French cyclist (b. 1960)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9our\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9our\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9our"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer (b. 1990)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Maur%C3%ADcio_Alves_Peruchi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maur%C3%ADcio_Alves_Peruchi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maur%C3%ADcio_Alves_Peruchi"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and coach (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(catcher)\" class=\"mw-redirect\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(catcher)\" class=\"mw-redirect\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player and coach (b. 1931)", "links": [{"title": "<PERSON> (catcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(catcher)"}]}, {"year": "2014", "text": "<PERSON>, American race car driver (b. 1953)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Brazilian jurist and politician (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Paulo_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian jurist and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paulo_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian jurist and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_Brossard"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Algerian-French tennis player and trainer (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian-French tennis player and trainer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian-French tennis player and trainer (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German commander (b. 1916)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German commander (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German commander (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Gabonese politician (b. 1957)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gabonese politician (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gabonese politician (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>_<PERSON>me"}]}, {"year": "2016", "text": "<PERSON>, Emirati politician & diplomat (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Emirati politician &amp; diplomat (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Emirati politician &amp; diplomat (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actress (b. 1925)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American actor and comedian (b. 1959)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and comedian (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and comedian (b. 1959)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, American football player (b. 1983)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (b. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor and producer (b. 1955)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American comedian, actor, and singer (b. 1955)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and singer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and singer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Italian fashion designer and inventor (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fashion designer and inventor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fashion designer and inventor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American filmmaker (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian-American journalist and author (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}