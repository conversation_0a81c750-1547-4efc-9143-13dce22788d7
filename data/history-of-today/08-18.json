{"date": "August 18", "url": "https://wikipedia.org/wiki/August_18", "data": {"Events": [{"year": "684", "text": "Battle of Marj Rahit: Umayyad partisans defeat the supporters of <PERSON> and cement Umayyad control of Syria.", "html": "684 - <a href=\"https://wikipedia.org/wiki/Battle_of_Marj_Rahit_(684)\" title=\"Battle of Marj Rahit (684)\">Battle of Marj Rahit</a>: <a href=\"https://wikipedia.org/wiki/Umayyad\" class=\"mw-redirect\" title=\"Umayyad\">Umayyad</a> partisans defeat the supporters of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Z<PERSON>yr\" class=\"mw-redirect\" title=\"Ibn al-Zubayr\"><PERSON>Zubayr</a> and cement Umayyad control of <a href=\"https://wikipedia.org/wiki/Al-Sham\" class=\"mw-redirect\" title=\"Al-Sham\">Syria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Marj_Rahit_(684)\" title=\"Battle of Marj Rahit (684)\">Battle of Marj Rahit</a>: <a href=\"https://wikipedia.org/wiki/Umayyad\" class=\"mw-redirect\" title=\"Umayyad\">Umayyad</a> partisans defeat the supporters of <a href=\"https://wikipedia.org/wiki/Ibn_al-Zubayr\" class=\"mw-redirect\" title=\"Ibn al-Zubayr\"><PERSON> al-Zubayr</a> and cement Umayyad control of <a href=\"https://wikipedia.org/wiki/Al-Sham\" class=\"mw-redirect\" title=\"Al-Sham\">Syria</a>.", "links": [{"title": "Battle of Marj <PERSON>hit (684)", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>_<PERSON><PERSON>_(684)"}, {"title": "Umayyad", "link": "https://wikipedia.org/wiki/Umayyad"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Al-Sham", "link": "https://wikipedia.org/wiki/Al-<PERSON>ham"}]}, {"year": "707", "text": "Princess <PERSON> accedes to the imperial Japanese throne as Empress <PERSON><PERSON>.", "html": "707 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>\" title=\"Empress <PERSON><PERSON>\">Princess <PERSON></a> accedes to the imperial Japanese throne as <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>\" title=\"Empress <PERSON><PERSON>\">Empress <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>\" title=\"Empress <PERSON><PERSON>\">Princess <PERSON></a> accedes to the imperial Japanese throne as <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>\" title=\"Empress <PERSON><PERSON>\">Empress <PERSON><PERSON></a>.", "links": [{"title": "Empress <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>"}, {"title": "Empress <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>"}]}, {"year": "1304", "text": "The Battle of Mons-en-Pévèle is fought to a draw between the French army and the Flemish militias.", "html": "1304 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Mons-en-P%C3%A9v%C3%A8le\" title=\"Battle of Mons-en-Pévèle\">Battle of Mons-en-Pévèle</a> is fought to a draw between the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> army and the <a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flemish</a> militias.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Mons-en-P%C3%A9v%C3%A8le\" title=\"Battle of Mons-en-Pévèle\">Battle of Mons-en-Pévèle</a> is fought to a draw between the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> army and the <a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flemish</a> militias.", "links": [{"title": "Battle of Mons-en-Pévèle", "link": "https://wikipedia.org/wiki/Battle_of_Mons-en-P%C3%A9v%C3%A8le"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "County of Flanders", "link": "https://wikipedia.org/wiki/County_of_Flanders"}]}, {"year": "1487", "text": "The Siege of Málaga ends with the taking of the city by Castilian and Aragonese forces.", "html": "1487 - The <a href=\"https://wikipedia.org/wiki/Siege_of_M%C3%A1laga_(1487)\" title=\"Siege of Málaga (1487)\">Siege of Málaga</a> ends with the taking of the city by <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castilian</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Aragon\" title=\"Kingdom of Aragon\">Aragonese</a> forces.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_M%C3%A1laga_(1487)\" title=\"Siege of Málaga (1487)\">Siege of Málaga</a> ends with the taking of the city by <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castilian</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Aragon\" title=\"Kingdom of Aragon\">Aragonese</a> forces.", "links": [{"title": "Siege of Málaga (1487)", "link": "https://wikipedia.org/wiki/Siege_of_M%C3%A1laga_(1487)"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "Kingdom of Aragon", "link": "https://wikipedia.org/wiki/Kingdom_of_Aragon"}]}, {"year": "1492", "text": "The first grammar of the Spanish language (Gramática de la lengua castellana) is presented to Queen <PERSON>.", "html": "1492 - The first <a href=\"https://wikipedia.org/wiki/Grammar\" title=\"Grammar\">grammar</a> of the <a href=\"https://wikipedia.org/wiki/Spanish_language\" title=\"Spanish language\">Spanish language</a> (<i><a href=\"https://wikipedia.org/wiki/Gram%C3%A1tica_de_la_lengua_castellana\" title=\"Gramática de la lengua castellana\">Gramática de la lengua castellana</a></i>) is presented to <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Castile\" title=\"<PERSON> I of Castile\">Queen <PERSON> I</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Grammar\" title=\"Grammar\">grammar</a> of the <a href=\"https://wikipedia.org/wiki/Spanish_language\" title=\"Spanish language\">Spanish language</a> (<i><a href=\"https://wikipedia.org/wiki/Gram%C3%A1tica_de_la_lengua_castellana\" title=\"Gramática de la lengua castellana\">Gramática de la lengua castellana</a></i>) is presented to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\">Queen <PERSON> I</a>.", "links": [{"title": "Grammar", "link": "https://wikipedia.org/wiki/Grammar"}, {"title": "Spanish language", "link": "https://wikipedia.org/wiki/Spanish_language"}, {"title": "Gramática de la lengua castellana", "link": "https://wikipedia.org/wiki/Gram%C3%A1tica_de_la_lengua_castellana"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}]}, {"year": "1572", "text": "The Huguenot King <PERSON> of Navarre marries the Catholic <PERSON> of Valois, ostensibly to reconcile the feuding Protestants and Catholics of France.", "html": "1572 - The <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenot</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_France\" title=\"<PERSON> IV of France\">King <PERSON> of Navarre</a> marries the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, ostensibly to reconcile the feuding <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestants</a> and <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholics</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenot</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\">King <PERSON> III of Navarre</a> marries the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, ostensibly to reconcile the feuding <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestants</a> and <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholics</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>not"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Protestantism", "link": "https://wikipedia.org/wiki/Protestantism"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}]}, {"year": "1590", "text": "<PERSON>, the governor of the Roanoke Colony, returns from a supply trip to England and finds his settlement deserted.", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist_and_artist)\" title=\"<PERSON> (colonist and artist)\"><PERSON></a>, the governor of the <a href=\"https://wikipedia.org/wiki/Roanoke_Colony\" title=\"Roanoke Colony\">Roanoke Colony</a>, returns from a supply trip to England and finds his settlement deserted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(colonist_and_artist)\" title=\"<PERSON> (colonist and artist)\"><PERSON></a>, the governor of the <a href=\"https://wikipedia.org/wiki/Roanoke_Colony\" title=\"Roanoke Colony\">Roanoke Colony</a>, returns from a supply trip to England and finds his settlement deserted.", "links": [{"title": "<PERSON> (colonist and artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist_and_artist)"}, {"title": "Roanoke Colony", "link": "https://wikipedia.org/wiki/Roanoke_Colony"}]}, {"year": "1612", "text": "The trial of the Pendle witches, one of England's most famous witch trials, begins at Lancaster Assizes.", "html": "1612 - The trial of the <a href=\"https://wikipedia.org/wiki/Pendle_witches\" title=\"Pendle witches\">Pendle witches</a>, one of England's most famous witch trials, begins at <a href=\"https://wikipedia.org/wiki/Lancaster,_Lancashire\" title=\"Lancaster, Lancashire\">Lancaster</a> <a href=\"https://wikipedia.org/wiki/Assizes\" title=\"Assizes\">Assizes</a>.", "no_year_html": "The trial of the <a href=\"https://wikipedia.org/wiki/Pendle_witches\" title=\"Pendle witches\">Pendle witches</a>, one of England's most famous witch trials, begins at <a href=\"https://wikipedia.org/wiki/Lancaster,_Lancashire\" title=\"Lancaster, Lancashire\">Lancaster</a> <a href=\"https://wikipedia.org/wiki/Assizes\" title=\"Assizes\">Assizes</a>.", "links": [{"title": "Pendle witches", "link": "https://wikipedia.org/wiki/Pendle_witches"}, {"title": "Lancaster, Lancashire", "link": "https://wikipedia.org/wiki/Lancaster,_Lancashire"}, {"title": "Assizes", "link": "https://wikipedia.org/wiki/Assizes"}]}, {"year": "1634", "text": "<PERSON><PERSON><PERSON>, accused and convicted of sorcery, is burned alive in Loudun, France.", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ier\" title=\"<PERSON><PERSON><PERSON> Grandier\"><PERSON><PERSON><PERSON></a>, accused and convicted of sorcery, is burned alive in <a href=\"https://wikipedia.org/wiki/Loudun\" title=\"<PERSON>du<PERSON>\">Loudun</a>, France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Grandier\"><PERSON><PERSON><PERSON></a>, accused and convicted of sorcery, is burned alive in <a href=\"https://wikipedia.org/wiki/Loudun\" title=\"Loudu<PERSON>\">Loudun</a>, France.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>ier"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>dun"}]}, {"year": "1721", "text": "The city of Shamakhi in Safavid Shirvan is sacked.", "html": "1721 - The city of <a href=\"https://wikipedia.org/wiki/Shamakhi\" title=\"Shamakhi\">Shamakhi</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Sack_of_Shamakhi\" title=\"Sack of Shamakhi\">is sacked</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Shamakhi\" title=\"Shamakhi\">Shamakhi</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Sack_of_Shamakhi\" title=\"Sack of Shamakhi\">is sacked</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ham<PERSON>hi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Sack of Shamakhi", "link": "https://wikipedia.org/wiki/Sack_of_Shamakhi"}]}, {"year": "1783", "text": "A huge fireball meteor is seen across Great Britain as it passes over the east coast.", "html": "1783 - A <a href=\"https://wikipedia.org/wiki/1783_Great_Meteor\" title=\"1783 Great Meteor\">huge fireball meteor</a> is seen across Great Britain as it passes over the east coast.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1783_Great_Meteor\" title=\"1783 Great Meteor\">huge fireball meteor</a> is seen across Great Britain as it passes over the east coast.", "links": [{"title": "1783 Great Meteor", "link": "https://wikipedia.org/wiki/1783_Great_<PERSON>eor"}]}, {"year": "1809", "text": "The Senate of Finland is established in the Grand Duchy of Finland after the official adoption of the Statute of the Government Council by Tsar <PERSON> of Russia.", "html": "1809 - The <a href=\"https://wikipedia.org/wiki/Senate_of_Finland\" title=\"Senate of Finland\">Senate of Finland</a> is established in the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a> after the official adoption of the Statute of the Government Council by Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Russia\" title=\"<PERSON> I of Russia\"><PERSON> of Russia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Senate_of_Finland\" title=\"Senate of Finland\">Senate of Finland</a> is established in the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a> after the official adoption of the Statute of the Government Council by Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a>.", "links": [{"title": "Senate of Finland", "link": "https://wikipedia.org/wiki/Senate_of_Finland"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1826", "text": "Major <PERSON> becomes the first European to enter Timbuktu.", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Major <PERSON></a> becomes the first European to enter <a href=\"https://wikipedia.org/wiki/Timbuktu\" title=\"Timbuktu\">Timbuktu</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Major <PERSON></a> becomes the first European to enter <a href=\"https://wikipedia.org/wiki/Timbuktu\" title=\"Timbuktu\">Timbuktu</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Timbuktu", "link": "https://wikipedia.org/wiki/Timbuktu"}]}, {"year": "1838", "text": "The Wilkes Expedition, which would explore the Puget Sound and Antarctica, weighs anchor at Hampton Roads.", "html": "1838 - The <a href=\"https://wikipedia.org/wiki/United_States_Exploring_Expedition\" title=\"United States Exploring Expedition\">Wilkes Expedition</a>, which would explore the <a href=\"https://wikipedia.org/wiki/Puget_Sound\" title=\"Puget Sound\">Puget Sound</a> and Antarctica, weighs anchor at <a href=\"https://wikipedia.org/wiki/Hampton_Roads\" title=\"Hampton Roads\">Hampton Roads</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Exploring_Expedition\" title=\"United States Exploring Expedition\">Wilkes Expedition</a>, which would explore the <a href=\"https://wikipedia.org/wiki/Puget_Sound\" title=\"Puget Sound\">Puget Sound</a> and Antarctica, weighs anchor at <a href=\"https://wikipedia.org/wiki/Hampton_Roads\" title=\"Hampton Roads\">Hampton Roads</a>.", "links": [{"title": "United States Exploring Expedition", "link": "https://wikipedia.org/wiki/United_States_Exploring_Expedition"}, {"title": "Puget Sound", "link": "https://wikipedia.org/wiki/Puget_Sound"}, {"title": "Hampton Roads", "link": "https://wikipedia.org/wiki/Hampton_Roads"}]}, {"year": "1848", "text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> are executed on the orders of Argentine dictator <PERSON>.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Camila_O%27Gorman\" title=\"<PERSON><PERSON> O<PERSON>rman\"><PERSON><PERSON></a> and <PERSON><PERSON><PERSON><PERSON> are executed on the orders of Argentine dictator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Camila_O%27Gorman\" title=\"<PERSON><PERSON> O<PERSON>Gorman\"><PERSON><PERSON></a> and <PERSON><PERSON><PERSON><PERSON> are executed on the orders of Argentine dictator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camila_O%27Gorman"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "American Civil War: Battle of Globe Tavern: Union forces try to cut a vital Confederate supply-line into Petersburg, Virginia, by attacking the Wilmington and Weldon Railroad.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Globe_Tavern\" title=\"Battle of Globe Tavern\">Battle of Globe Tavern</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces try to cut a vital <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> supply-line into <a href=\"https://wikipedia.org/wiki/Petersburg,_Virginia\" title=\"Petersburg, Virginia\">Petersburg, Virginia</a>, by attacking the <a href=\"https://wikipedia.org/wiki/Wilmington_and_Weldon_Railroad\" title=\"Wilmington and Weldon Railroad\">Wilmington and Weldon Railroad</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Globe_Tavern\" title=\"Battle of Globe Tavern\">Battle of Globe Tavern</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces try to cut a vital <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> supply-line into <a href=\"https://wikipedia.org/wiki/Petersburg,_Virginia\" title=\"Petersburg, Virginia\">Petersburg, Virginia</a>, by attacking the <a href=\"https://wikipedia.org/wiki/Wilmington_and_Weldon_Railroad\" title=\"Wilmington and Weldon Railroad\">Wilmington and Weldon Railroad</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Globe Tavern", "link": "https://wikipedia.org/wiki/Battle_of_Globe_Tavern"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Petersburg, Virginia", "link": "https://wikipedia.org/wiki/Petersburg,_Virginia"}, {"title": "Wilmington and Weldon Railroad", "link": "https://wikipedia.org/wiki/Wilmington_and_Weldon_Railroad"}]}, {"year": "1868", "text": "French astronomer <PERSON> discovers helium.", "html": "1868 - French <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Timeline_of_chemical_element_discoveries\" class=\"mw-redirect\" title=\"Timeline of chemical element discoveries\">discovers</a> <a href=\"https://wikipedia.org/wiki/Helium\" title=\"Helium\">helium</a>.", "no_year_html": "French <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Timeline_of_chemical_element_discoveries\" class=\"mw-redirect\" title=\"Timeline of chemical element discoveries\">discovers</a> <a href=\"https://wikipedia.org/wiki/Helium\" title=\"Helium\">helium</a>.", "links": [{"title": "Astronomer", "link": "https://wikipedia.org/wiki/Astronomer"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Timeline of chemical element discoveries", "link": "https://wikipedia.org/wiki/Timeline_of_chemical_element_discoveries"}, {"title": "Helium", "link": "https://wikipedia.org/wiki/Helium"}]}, {"year": "1870", "text": "Franco-Prussian War: Battle of Gravelotte is fought.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Gravelotte\" title=\"Battle of Gravelotte\">Battle of Gravelotte</a> is fought.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Gravelotte\" title=\"Battle of Gravelotte\">Battle of Gravelotte</a> is fought.", "links": [{"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "Battle of Gravelotte", "link": "https://wikipedia.org/wiki/Battle_of_Gravelotte"}]}, {"year": "1877", "text": "American astronomer <PERSON><PERSON> discovers Phobos, one of Mars’s moons.", "html": "1877 - American <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a> <a href=\"https://wikipedia.org/wiki/Asaph_Hall\" title=\"Asaph Hall\">Asaph Hall</a> discovers <a href=\"https://wikipedia.org/wiki/Phobos_(moon)\" title=\"Phobos (moon)\"><PERSON><PERSON><PERSON></a>, one of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>’s <a href=\"https://wikipedia.org/wiki/Moons_of_Mars\" title=\"Moons of Mars\">moons</a>.", "no_year_html": "American <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a> <a href=\"https://wikipedia.org/wiki/Asaph_Hall\" title=\"Asaph Hall\">Asaph Hall</a> discovers <a href=\"https://wikipedia.org/wiki/Phobos_(moon)\" title=\"Phobos (moon)\"><PERSON><PERSON><PERSON></a>, one of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>’s <a href=\"https://wikipedia.org/wiki/Moons_of_Mars\" title=\"Moons of Mars\">moons</a>.", "links": [{"title": "Astronomer", "link": "https://wikipedia.org/wiki/Astronomer"}, {"title": "Asaph Hall", "link": "https://wikipedia.org/wiki/Asaph_Hall"}, {"title": "<PERSON><PERSON><PERSON> (moon)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(moon)"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}, {"title": "Moons of Mars", "link": "https://wikipedia.org/wiki/Moons_of_Mars"}]}, {"year": "1891", "text": "A major hurricane strikes Martinique, leaving 700 dead.", "html": "1891 - A <a href=\"https://wikipedia.org/wiki/1891_Martinique_hurricane\" title=\"1891 Martinique hurricane\">major hurricane</a> strikes <a href=\"https://wikipedia.org/wiki/Martin<PERSON>\" title=\"Martinique\">Martin<PERSON></a>, leaving 700 dead.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1891_Martinique_hurricane\" title=\"1891 Martinique hurricane\">major hurricane</a> strikes <a href=\"https://wikipedia.org/wiki/Martin<PERSON>\" title=\"Martinique\"><PERSON><PERSON></a>, leaving 700 dead.", "links": [{"title": "1891 Martinique hurricane", "link": "https://wikipedia.org/wiki/1891_Martinique_hurricane"}, {"title": "Martinique", "link": "https://wikipedia.org/wiki/<PERSON>ique"}]}, {"year": "1903", "text": "German engineer <PERSON> allegedly flies his self-made, motored gliding airplane four months before the first flight of the <PERSON> brothers.", "html": "1903 - German engineer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> allegedly flies his self-made, motored gliding <a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">airplane</a> four months before the first flight of the <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON> brothers</a>.", "no_year_html": "German engineer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> allegedly flies his self-made, motored gliding <a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">airplane</a> four months before the first flight of the <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON> brothers</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Airplane", "link": "https://wikipedia.org/wiki/Airplane"}, {"title": "<PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "A Great Fire in Thessaloniki, Greece, destroys 32% of the city leaving 70,000 individuals homeless.", "html": "1917 - A <a href=\"https://wikipedia.org/wiki/Great_Thessaloniki_Fire_of_1917\" title=\"Great Thessaloniki Fire of 1917\">Great Fire</a> in <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>, Greece, destroys 32% of the city leaving 70,000 individuals homeless.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Great_Thessaloniki_Fire_of_1917\" title=\"Great Thessaloniki Fire of 1917\">Great Fire</a> in <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>, Greece, destroys 32% of the city leaving 70,000 individuals homeless.", "links": [{"title": "Great Thessaloniki Fire of 1917", "link": "https://wikipedia.org/wiki/Great_Thessaloniki_Fire_of_1917"}, {"title": "Thessaloniki", "link": "https://wikipedia.org/wiki/Thessaloniki"}]}, {"year": "1920", "text": "The Nineteenth Amendment to the United States Constitution is ratified, guaranteeing women's suffrage.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution\" title=\"Nineteenth Amendment to the United States Constitution\">Nineteenth Amendment to the United States Constitution</a> is ratified, guaranteeing women's <a href=\"https://wikipedia.org/wiki/Suffrage\" title=\"Suffrage\">suffrage</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution\" title=\"Nineteenth Amendment to the United States Constitution\">Nineteenth Amendment to the United States Constitution</a> is ratified, guaranteeing women's <a href=\"https://wikipedia.org/wiki/Suffrage\" title=\"Suffrage\">suffrage</a>.", "links": [{"title": "Nineteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution"}, {"title": "Suffrage", "link": "https://wikipedia.org/wiki/Suffrage"}]}, {"year": "1923", "text": "The first British Track and Field championships for women are held in London, Great Britain.", "html": "1923 - The <a href=\"https://wikipedia.org/wiki/1923_WAAA_Championships\" title=\"1923 WAAA Championships\">first British Track and Field championships for women</a> are held in London, Great Britain.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1923_WAAA_Championships\" title=\"1923 WAAA Championships\">first British Track and Field championships for women</a> are held in London, Great Britain.", "links": [{"title": "1923 WAAA Championships", "link": "https://wikipedia.org/wiki/1923_WAAA_Championships"}]}, {"year": "1933", "text": "The Volksempfänger is first presented to the German public at a radio exhibition; the presiding Nazi Minister of Propaganda, <PERSON>, delivers an accompanying speech heralding the radio as the ‘eighth great power’.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/Volksempf%C3%A4nger\" title=\"<PERSON>ks<PERSON>pfänger\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is first presented to the German public at a <a href=\"https://wikipedia.org/wiki/IFA_Berlin\" title=\"IFA Berlin\">radio exhibition</a>; the presiding Nazi Minister of Propaganda, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, delivers an accompanying speech heralding the radio as the ‘eighth great power’.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Volksempf%C3%A4nger\" title=\"<PERSON>ks<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is first presented to the German public at a <a href=\"https://wikipedia.org/wiki/IFA_Berlin\" title=\"IFA Berlin\">radio exhibition</a>; the presiding Nazi Minister of Propaganda, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, delivers an accompanying speech heralding the radio as the ‘eighth great power’.", "links": [{"title": "Volksempfänger", "link": "https://wikipedia.org/wiki/Volksempf%C3%A4nger"}, {"title": "IFA Berlin", "link": "https://wikipedia.org/wiki/IFA_Berlin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "A lightning strike starts the Blackwater Fire of 1937 in Shoshone National Forest, killing 15 firefighters within three days and prompting the United States Forest Service to develop their smokejumper program.", "html": "1937 - A lightning strike starts the <a href=\"https://wikipedia.org/wiki/Blackwater_Fire_of_1937\" title=\"Blackwater Fire of 1937\">Blackwater Fire of 1937</a> in <a href=\"https://wikipedia.org/wiki/Shoshone_National_Forest\" title=\"Shoshone National Forest\">Shoshone National Forest</a>, killing 15 firefighters within three days and prompting the <a href=\"https://wikipedia.org/wiki/United_States_Forest_Service\" title=\"United States Forest Service\">United States Forest Service</a> to develop their <a href=\"https://wikipedia.org/wiki/Smokejumper\" title=\"Smokejumper\">smokejumper</a> program.", "no_year_html": "A lightning strike starts the <a href=\"https://wikipedia.org/wiki/Blackwater_Fire_of_1937\" title=\"Blackwater Fire of 1937\">Blackwater Fire of 1937</a> in <a href=\"https://wikipedia.org/wiki/Shoshone_National_Forest\" title=\"Shoshone National Forest\">Shoshone National Forest</a>, killing 15 firefighters within three days and prompting the <a href=\"https://wikipedia.org/wiki/United_States_Forest_Service\" title=\"United States Forest Service\">United States Forest Service</a> to develop their <a href=\"https://wikipedia.org/wiki/Smokejumper\" title=\"Smokejumper\">smokejumper</a> program.", "links": [{"title": "Blackwater Fire of 1937", "link": "https://wikipedia.org/wiki/Blackwater_Fire_of_1937"}, {"title": "Shoshone National Forest", "link": "https://wikipedia.org/wiki/Shoshone_National_Forest"}, {"title": "United States Forest Service", "link": "https://wikipedia.org/wiki/United_States_Forest_Service"}, {"title": "Smokejumper", "link": "https://wikipedia.org/wiki/Smokejumper"}]}, {"year": "1938", "text": "The Thousand Islands Bridge, connecting New York, United States, with Ontario, Canada, over the Saint Lawrence River, is dedicated by U.S. President <PERSON>.", "html": "1938 - The <a href=\"https://wikipedia.org/wiki/Thousand_Islands_Bridge\" title=\"Thousand Islands Bridge\">Thousand Islands Bridge</a>, connecting New York, United States, with <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>, Canada, over the <a href=\"https://wikipedia.org/wiki/Saint_Lawrence_River\" class=\"mw-redirect\" title=\"Saint Lawrence River\">Saint Lawrence River</a>, is dedicated by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Thousand_Islands_Bridge\" title=\"Thousand Islands Bridge\">Thousand Islands Bridge</a>, connecting New York, United States, with <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>, Canada, over the <a href=\"https://wikipedia.org/wiki/Saint_Lawrence_River\" class=\"mw-redirect\" title=\"Saint Lawrence River\">Saint Lawrence River</a>, is dedicated by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Franklin <PERSON> Roosevelt\"><PERSON></a>.", "links": [{"title": "Thousand Islands Bridge", "link": "https://wikipedia.org/wiki/Thousand_Islands_Bridge"}, {"title": "Ontario", "link": "https://wikipedia.org/wiki/Ontario"}, {"title": "Saint Lawrence River", "link": "https://wikipedia.org/wiki/Saint_Lawrence_River"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "World War II: The Hardest Day air battle, part of the Battle of Britain, takes place. At that point, it is the largest aerial engagement in history with heavy losses sustained on both sides.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/The_Hardest_Day\" title=\"The Hardest Day\">The Hardest Day</a> air battle, part of the <a href=\"https://wikipedia.org/wiki/Battle_of_Britain\" title=\"Battle of Britain\">Battle of Britain</a>, takes place. At that point, it is the largest aerial engagement in history with heavy losses sustained on both sides.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/The_Hardest_Day\" title=\"The Hardest Day\">The Hardest Day</a> air battle, part of the <a href=\"https://wikipedia.org/wiki/Battle_of_Britain\" title=\"Battle of Britain\">Battle of Britain</a>, takes place. At that point, it is the largest aerial engagement in history with heavy losses sustained on both sides.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "The Hardest Day", "link": "https://wikipedia.org/wiki/The_Hardest_Day"}, {"title": "Battle of Britain", "link": "https://wikipedia.org/wiki/Battle_of_Britain"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON> takes office as the first president of Indonesia, following the country's declaration of independence the previous day.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\"><PERSON>kar<PERSON></a> takes office as the <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Indonesia\" class=\"mw-redirect\" title=\"List of Presidents of Indonesia\">first president of Indonesia</a>, following the country's declaration of independence the previous day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\"><PERSON>kar<PERSON></a> takes office as the <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Indonesia\" class=\"mw-redirect\" title=\"List of Presidents of Indonesia\">first president of Indonesia</a>, following the country's declaration of independence the previous day.", "links": [{"title": "Sukar<PERSON>", "link": "https://wikipedia.org/wiki/Sukarno"}, {"title": "List of Presidents of Indonesia", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Indonesia"}]}, {"year": "1945", "text": "Soviet-Japanese War: Battle of Shumshu: Soviet forces land at Takeda Beach on Shumshu Island and launch the Battle of Shumshu; the Soviet Union’s Invasion of the Kuril Islands commences.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Soviet-Japanese_War\" class=\"mw-redirect\" title=\"Soviet-Japanese War\">Soviet-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Shumshu\" title=\"Battle of Shumshu\">Battle of Shumshu</a>: Soviet forces land at Takeda Beach on <a href=\"https://wikipedia.org/wiki/Shumshu\" title=\"Shumshu\">Shumshu Island</a> and launch the <a href=\"https://wikipedia.org/wiki/Battle_of_Shumshu\" title=\"Battle of Shumshu\">Battle of Shumshu</a>; the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>’s <a href=\"https://wikipedia.org/wiki/Invasion_of_the_Kuril_Islands\" title=\"Invasion of the Kuril Islands\">Invasion of the Kuril Islands</a> commences.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet-Japanese_War\" class=\"mw-redirect\" title=\"Soviet-Japanese War\">Soviet-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Shumshu\" title=\"Battle of Shumshu\">Battle of Shumshu</a>: Soviet forces land at Takeda Beach on <a href=\"https://wikipedia.org/wiki/Shumshu\" title=\"Shumshu\">Shumshu Island</a> and launch the <a href=\"https://wikipedia.org/wiki/Battle_of_Shumshu\" title=\"Battle of Shumshu\">Battle of Shumshu</a>; the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>’s <a href=\"https://wikipedia.org/wiki/Invasion_of_the_Kuril_Islands\" title=\"Invasion of the Kuril Islands\">Invasion of the Kuril Islands</a> commences.", "links": [{"title": "Soviet-Japanese War", "link": "https://wikipedia.org/wiki/Soviet-Japanese_War"}, {"title": "Battle of Shumshu", "link": "https://wikipedia.org/wiki/Battle_of_Shumshu"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>hu"}, {"title": "Battle of Shumshu", "link": "https://wikipedia.org/wiki/Battle_of_Shumshu"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Invasion of the Kuril Islands", "link": "https://wikipedia.org/wiki/Invasion_of_the_Kuril_Islands"}]}, {"year": "1949", "text": "1949 Kemi strike: Two protesters die in the scuffle between the police and the strikers' protest procession in Kemi, Finland.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/1949_Kemi_strike\" title=\"1949 Kemi strike\">1949 Kemi strike</a>: Two protesters die in the scuffle between the police and the strikers' protest procession in <a href=\"https://wikipedia.org/wiki/Kemi\" title=\"Kemi\">Kemi, Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1949_Kemi_strike\" title=\"1949 Kemi strike\">1949 Kemi strike</a>: Two protesters die in the scuffle between the police and the strikers' protest procession in <a href=\"https://wikipedia.org/wiki/Kemi\" title=\"Kemi\">Kemi, Finland</a>.", "links": [{"title": "1949 Kemi strike", "link": "https://wikipedia.org/wiki/1949_Kemi_strike"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mi"}]}, {"year": "1950", "text": "<PERSON>, the chairman of the Communist Party of Belgium, is assassinated. The Party newspaper blames royalists and Rexists.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the chairman of the Communist Party of Belgium, is assassinated. The Party newspaper blames royalists and <a href=\"https://wikipedia.org/wiki/Rexist_Party\" title=\"Rexist Party\">Rexists</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the chairman of the Communist Party of Belgium, is assassinated. The Party newspaper blames royalists and <a href=\"https://wikipedia.org/wiki/Rexist_Party\" title=\"Rexist Party\">Rexists</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rexist Party", "link": "https://wikipedia.org/wiki/Rexist_Party"}]}, {"year": "1958", "text": "<PERSON>'s controversial novel <PERSON><PERSON><PERSON> is published in the United States.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s controversial novel <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i> is published in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s controversial novel <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i> is published in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lita"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON> from Bangladesh swims across the English Channel in a competition as the first Bengali and the first Asian to do so, placing first among the 39 competitors.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> swims across the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> in a competition as the first <a href=\"https://wikipedia.org/wiki/Bengalis\" title=\"Bengalis\">Bengali</a> and the first Asian to do so, placing first among the 39 competitors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> swims across the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> in a competition as the first <a href=\"https://wikipedia.org/wiki/Bengalis\" title=\"Bengalis\">Bengali</a> and the first Asian to do so, placing first among the 39 competitors.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}, {"title": "Bengalis", "link": "https://wikipedia.org/wiki/Bengalis"}]}, {"year": "1963", "text": "Civil rights movement: <PERSON> becomes the first African American to graduate from the University of Mississippi.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">Civil rights movement</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first African American to graduate from the <a href=\"https://wikipedia.org/wiki/University_of_Mississippi\" title=\"University of Mississippi\">University of Mississippi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">Civil rights movement</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first African American to graduate from the <a href=\"https://wikipedia.org/wiki/University_of_Mississippi\" title=\"University of Mississippi\">University of Mississippi</a>.", "links": [{"title": "Civil rights movement", "link": "https://wikipedia.org/wiki/Civil_rights_movement"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "University of Mississippi", "link": "https://wikipedia.org/wiki/University_of_Mississippi"}]}, {"year": "1965", "text": "Vietnam War: Operation Starlite begins: United States Marines destroy a Viet Cong stronghold on the Van Tuong peninsula in the first major American ground battle of the war.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Starlite\" title=\"Operation Starlite\">Operation Starlite</a> begins: <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marines</a> destroy a <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> stronghold on the Van Tuong peninsula in the first major American ground battle of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Starlite\" title=\"Operation Starlite\">Operation Starlite</a> begins: <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marines</a> destroy a <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> stronghold on the Van Tuong peninsula in the first major American ground battle of the war.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Operation Starlite", "link": "https://wikipedia.org/wiki/Operation_Starlite"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}]}, {"year": "1966", "text": "Vietnam War: The Battle of Long Tan ensues after a patrol from the 6th Battalion, Royal Australian Regiment clashes with a Viet Cong force in Phước Tuy Province.", "html": "1966 - Vietnam War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Long_Tan\" title=\"Battle of Long Tan\">Battle of Long Tan</a> ensues after a patrol from the <a href=\"https://wikipedia.org/wiki/6th_Battalion,_Royal_Australian_Regiment\" title=\"6th Battalion, Royal Australian Regiment\">6th Battalion, Royal Australian Regiment</a> clashes with a <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> force in <a href=\"https://wikipedia.org/wiki/Ph%C6%B0%E1%BB%9Bc_Tuy_Province\" class=\"mw-redirect\" title=\"Phước Tuy Province\">Phước Tuy Province</a>.", "no_year_html": "Vietnam War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Long_Tan\" title=\"Battle of Long Tan\">Battle of Long Tan</a> ensues after a patrol from the <a href=\"https://wikipedia.org/wiki/6th_Battalion,_Royal_Australian_Regiment\" title=\"6th Battalion, Royal Australian Regiment\">6th Battalion, Royal Australian Regiment</a> clashes with a <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> force in <a href=\"https://wikipedia.org/wiki/Ph%C6%B0%E1%BB%9Bc_Tuy_Province\" class=\"mw-redirect\" title=\"Phước Tuy Province\">Phước Tuy Province</a>.", "links": [{"title": "Battle of Long Tan", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>_<PERSON>"}, {"title": "6th Battalion, Royal Australian Regiment", "link": "https://wikipedia.org/wiki/6th_Battalion,_Royal_Australian_Regiment"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "Phước Tuy Province", "link": "https://wikipedia.org/wiki/Ph%C6%B0%E1%BB%9Bc_Tuy_Province"}]}, {"year": "1971", "text": "Vietnam War: Australia and New Zealand decide to withdraw their troops from Vietnam.", "html": "1971 - Vietnam War: Australia and New Zealand decide to withdraw their troops from <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "no_year_html": "Vietnam War: Australia and New Zealand decide to withdraw their troops from <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "links": [{"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1973", "text": "Aeroflot Flight A-13 crashes after takeoff from Baku-Bina International Airport in Azerbaijan, killing 56 people and injuring eight.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_A-13\" title=\"Aeroflot Flight A-13\">Aeroflot Flight A-13</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Heydar_Aliyev_International_Airport\" title=\"Heydar Aliyev International Airport\">Baku-Bina International Airport</a> in Azerbaijan, killing 56 people and injuring eight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_A-13\" title=\"Aeroflot Flight A-13\">Aeroflot Flight A-13</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Heydar_Aliyev_International_Airport\" title=\"Heydar Aliyev International Airport\">Baku-Bina International Airport</a> in Azerbaijan, killing 56 people and injuring eight.", "links": [{"title": "Aeroflot Flight A-13", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_A-13"}, {"title": "Heydar <PERSON>yev International Airport", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Aliyev_International_Airport"}]}, {"year": "1976", "text": "The Korean axe murder incident in Panmunjom results in the deaths of two US Army officers.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Korean_axe_murder_incident\" title=\"Korean axe murder incident\">Korean axe murder incident</a> in Panmunjom results in the deaths of two US Army officers.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Korean_axe_murder_incident\" title=\"Korean axe murder incident\">Korean axe murder incident</a> in Panmunjom results in the deaths of two US Army officers.", "links": [{"title": "Korean axe murder incident", "link": "https://wikipedia.org/wiki/Korean_axe_murder_incident"}]}, {"year": "1976", "text": "The Soviet Union’s robotic probe Luna 24 successfully lands on the Moon.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>’s robotic probe <i><a href=\"https://wikipedia.org/wiki/Luna_24\" title=\"Luna 24\">Luna 24</a></i> successfully lands on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>’s robotic probe <i><a href=\"https://wikipedia.org/wiki/Luna_24\" title=\"Luna 24\">Luna 24</a></i> successfully lands on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Luna 24", "link": "https://wikipedia.org/wiki/Luna_24"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1977", "text": "<PERSON> is arrested at a police roadblock under Terrorism Act No. 83 of 1967 in King William's Town, South Africa. He later dies from injuries sustained during this arrest, bringing attention to South Africa's apartheid policies.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested at a police roadblock under Terrorism Act No. 83 of 1967 in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Town\" class=\"mw-redirect\" title=\"King William's Town\">King <PERSON>'s Town</a>, South Africa. He later dies from injuries sustained during this arrest, bringing attention to South Africa's <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a> policies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested at a police roadblock under Terrorism Act No. 83 of 1967 in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Town\" class=\"mw-redirect\" title=\"King William's Town\">King <PERSON>'s Town</a>, South Africa. He later dies from injuries sustained during this arrest, bringing attention to South Africa's <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a> policies.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "King <PERSON>'s Town", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Town"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}]}, {"year": "1983", "text": "Hurricane <PERSON> hits the Texas coast, killing 21 people and causing over US$1 billion in damage (1983 dollars).", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Hurricane_Alicia\" title=\"Hurricane Alicia\">Hurricane <PERSON></a> hits the <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> coast, killing 21 people and causing over US$1 billion in damage (1983 dollars).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Alicia\" title=\"Hurricane Alicia\">Hurricane <PERSON></a> hits the <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> coast, killing 21 people and causing over US$1 billion in damage (1983 dollars).", "links": [{"title": "Hurricane Alicia", "link": "https://wikipedia.org/wiki/Hurricane_Alicia"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}]}, {"year": "1989", "text": "Leading presidential hopeful <PERSON> is assassinated near Bogotá in Colombia.", "html": "1989 - Leading presidential hopeful <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a> is assassinated near <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a> in <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>.", "no_year_html": "Leading presidential hopeful <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a> is assassinated near <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a> in <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1n"}, {"title": "Bogotá", "link": "https://wikipedia.org/wiki/Bogot%C3%A1"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}]}, {"year": "1993", "text": "American International Airways Flight 808 crashes at Leeward Point Field at Guantanamo Bay Naval Base in Guantánamo Bay, Cuba, injuring the three crew members.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/American_International_Airways_Flight_808\" title=\"American International Airways Flight 808\">American International Airways Flight 808</a> crashes at <a href=\"https://wikipedia.org/wiki/Leeward_Point_Field\" title=\"Leeward Point Field\">Leeward Point Field</a> at <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base\" title=\"Guantanamo Bay Naval Base\">Guantanamo Bay Naval Base</a> in <a href=\"https://wikipedia.org/wiki/Guant%C3%A1namo_Bay\" title=\"Guantánamo Bay\">Guantánamo Bay</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, injuring the three crew members.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_International_Airways_Flight_808\" title=\"American International Airways Flight 808\">American International Airways Flight 808</a> crashes at <a href=\"https://wikipedia.org/wiki/Leeward_Point_Field\" title=\"Leeward Point Field\">Leeward Point Field</a> at <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base\" title=\"Guantanamo Bay Naval Base\">Guantanamo Bay Naval Base</a> in <a href=\"https://wikipedia.org/wiki/Guant%C3%A1namo_Bay\" title=\"Guantánamo Bay\">Guantánamo Bay</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, injuring the three crew members.", "links": [{"title": "American International Airways Flight 808", "link": "https://wikipedia.org/wiki/American_International_Airways_Flight_808"}, {"title": "Leeward Point Field", "link": "https://wikipedia.org/wiki/Leeward_Point_Field"}, {"title": "Guantanamo Bay Naval Base", "link": "https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base"}, {"title": "Guantánamo Bay", "link": "https://wikipedia.org/wiki/Guant%C3%A1namo_Bay"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "2003", "text": "One-year-old <PERSON> is murdered in Newfoundland by his mother, who was awarded custody despite facing trial for the murder of <PERSON>'s father. The case was documented in the film <PERSON> and led to reform of Canada's bail laws.", "html": "2003 - One-year-old <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Murder of <PERSON>\"><PERSON></a> is murdered in <a href=\"https://wikipedia.org/wiki/Newfoundland\" class=\"mw-redirect\" title=\"Newfoundland\">Newfoundland</a> by his mother, who was awarded custody despite facing trial for the murder of <PERSON>'s father. The case was documented in the film <i><a href=\"https://wikipedia.org/wiki/Dear_<PERSON>:_A_Letter_to_a_Son_About_His_Father\" title=\"Dear <PERSON>: A Letter to a Son About His Father\">Dear <PERSON></a></i> and led to reform of Canada's bail laws.", "no_year_html": "One-year-old <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Murder of <PERSON>\"><PERSON></a> is murdered in <a href=\"https://wikipedia.org/wiki/Newfoundland\" class=\"mw-redirect\" title=\"Newfoundland\">Newfoundland</a> by his mother, who was awarded custody despite facing trial for the murder of <PERSON>'s father. The case was documented in the film <i><a href=\"https://wikipedia.org/wiki/Dear_<PERSON>:_A_Letter_to_a_Son_About_His_Father\" title=\"Dear <PERSON>: A Letter to a Son About His Father\">Dear <PERSON></a></i> and led to reform of Canada's bail laws.", "links": [{"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}, {"title": "Newfoundland", "link": "https://wikipedia.org/wiki/Newfoundland"}, {"title": "Dear <PERSON>: A Letter to a Son About His Father", "link": "https://wikipedia.org/wiki/Dear_<PERSON>:_A_Letter_to_a_Son_About_His_Father"}]}, {"year": "2005", "text": "A massive power blackout hits the Indonesian island of Java; affecting almost 100 million people, it is one of the largest and most widespread power outages in history.", "html": "2005 - A <a href=\"https://wikipedia.org/wiki/2005_Java%E2%80%93Bali_blackout\" title=\"2005 Java-Bali blackout\">massive power blackout</a> hits the <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> island of <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a>; affecting almost 100 million people, it is <a href=\"https://wikipedia.org/wiki/List_of_major_power_outages#Largest\" title=\"List of major power outages\">one of the largest and most widespread</a> power outages in history.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2005_Java%E2%80%93Bali_blackout\" title=\"2005 Java-Bali blackout\">massive power blackout</a> hits the <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> island of <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a>; affecting almost 100 million people, it is <a href=\"https://wikipedia.org/wiki/List_of_major_power_outages#Largest\" title=\"List of major power outages\">one of the largest and most widespread</a> power outages in history.", "links": [{"title": "2005 Java-Bali blackout", "link": "https://wikipedia.org/wiki/2005_Java%E2%80%93Bali_blackout"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Java", "link": "https://wikipedia.org/wiki/Java"}, {"title": "List of major power outages", "link": "https://wikipedia.org/wiki/List_of_major_power_outages#Largest"}]}, {"year": "2008", "text": "The President of Pakistan, <PERSON><PERSON>, resigns under threat of impeachment.", "html": "2008 - The President of Pakistan, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Musharra<PERSON>\" title=\"<PERSON><PERSON> Musha<PERSON>f\"><PERSON><PERSON></a>, resigns under <a href=\"https://wikipedia.org/wiki/Movement_to_impeach_<PERSON><PERSON>_<PERSON>rra<PERSON>\" class=\"mw-redirect\" title=\"Movement to impeach <PERSON><PERSON>f\">threat of impeachment</a>.", "no_year_html": "The President of Pakistan, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, resigns under <a href=\"https://wikipedia.org/wiki/Movement_to_impeach_<PERSON><PERSON>_<PERSON>rra<PERSON>\" class=\"mw-redirect\" title=\"Movement to impeach <PERSON><PERSON>\">threat of impeachment</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>f"}, {"title": "Movement to impeach <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Movement_to_impeach_<PERSON><PERSON>_<PERSON>f"}]}, {"year": "2008", "text": "War of Afghanistan: The Uzbin Valley ambush occurs.", "html": "2008 - War of Afghanistan: The <a href=\"https://wikipedia.org/wiki/Uzbin_Valley_ambush\" title=\"Uzbin Valley ambush\">Uzbin Valley ambush</a> occurs.", "no_year_html": "War of Afghanistan: The <a href=\"https://wikipedia.org/wiki/Uzbin_Valley_ambush\" title=\"Uzbin Valley ambush\">Uzbin Valley ambush</a> occurs.", "links": [{"title": "Uzbin Valley ambush", "link": "https://wikipedia.org/wiki/Uzbin_Valley_ambush"}]}, {"year": "2011", "text": "A terrorist attack on Israel's Highway 12 near the Egyptian border kills 16 and injures 40.", "html": "2011 - A <a href=\"https://wikipedia.org/wiki/2011_southern_Israel_cross-border_attacks\" title=\"2011 southern Israel cross-border attacks\">terrorist attack</a> on Israel's <a href=\"https://wikipedia.org/wiki/Highway_12_(Israel)\" title=\"Highway 12 (Israel)\">Highway 12</a> near the Egyptian border kills 16 and injures 40.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2011_southern_Israel_cross-border_attacks\" title=\"2011 southern Israel cross-border attacks\">terrorist attack</a> on Israel's <a href=\"https://wikipedia.org/wiki/Highway_12_(Israel)\" title=\"Highway 12 (Israel)\">Highway 12</a> near the Egyptian border kills 16 and injures 40.", "links": [{"title": "2011 southern Israel cross-border attacks", "link": "https://wikipedia.org/wiki/2011_southern_Israel_cross-border_attacks"}, {"title": "Highway 12 (Israel)", "link": "https://wikipedia.org/wiki/Highway_12_(Israel)"}]}, {"year": "2017", "text": "The first terrorist attack ever sentenced as a crime in Finland kills two and injures eight.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/2017_Turku_attack\" title=\"2017 Turku attack\">first terrorist attack</a> ever sentenced as a crime in Finland kills two and injures eight.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2017_Turku_attack\" title=\"2017 Turku attack\">first terrorist attack</a> ever sentenced as a crime in Finland kills two and injures eight.", "links": [{"title": "2017 Turku attack", "link": "https://wikipedia.org/wiki/2017_Turku_attack"}]}, {"year": "2019", "text": "One hundred activists, officials, and other concerned citizens in Iceland hold a funeral for Okjökull glacier, which has completely melted after having once covered six square miles (15.5 km2).", "html": "2019 - One hundred activists, officials, and other concerned citizens in <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> hold a funeral for <a href=\"https://wikipedia.org/wiki/Okj%C3%B6kull\" title=\"Okjökull\">Okjökull glacier</a>, which has completely melted after having once covered six square miles (15.5 km).", "no_year_html": "One hundred activists, officials, and other concerned citizens in <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> hold a funeral for <a href=\"https://wikipedia.org/wiki/Okj%C3%B6kull\" title=\"Okjökull\">Okjökull glacier</a>, which has completely melted after having once covered six square miles (15.5 km).", "links": [{"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Okj%C3%B6kull"}]}], "Births": [{"year": "1305", "text": "<PERSON><PERSON><PERSON>, Japanese Shōgun (d. 1358)", "html": "1305 - <a href=\"https://wikipedia.org/wiki/Ashikaga_Takauji\" title=\"Ashikaga Takauji\">Ashika<PERSON>kauji</a>, Japanese <a href=\"https://wikipedia.org/wiki/List_of_sh%C5%8Dguns\" class=\"mw-redirect\" title=\"List of shōguns\">Shōgun</a> (d. 1358)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashika<PERSON>_Takauji\" title=\"Ashikaga Takauji\">Ash<PERSON><PERSON>kauji</a>, Japanese <a href=\"https://wikipedia.org/wiki/List_of_sh%C5%8Dguns\" class=\"mw-redirect\" title=\"List of shōguns\">Shōgun</a> (d. 1358)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashika<PERSON>_<PERSON>ji"}, {"title": "List of shōguns", "link": "https://wikipedia.org/wiki/List_of_sh%C5%8Dguns"}]}, {"year": "1450", "text": "<PERSON><PERSON>, Croatian poet and author (d. 1524)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian poet and author (d. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian poet and author (d. 1524)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marko_Maruli%C4%87"}]}, {"year": "1458", "text": "<PERSON>, Catholic cardinal (d. 1531)", "html": "1458 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catholic cardinal (d. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catholic cardinal (d. 1531)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1497", "text": "<PERSON> Milano, Italian composer (d. 1543)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_da_Milano\" title=\"Francesco Canova da Milano\"><PERSON> Milano</a>, Italian composer (d. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_da_Milano\" title=\"Francesco Canova da Milano\"><PERSON></a>, Italian composer (d. 1543)", "links": [{"title": "<PERSON> da Milano", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1542", "text": "<PERSON>, 6th Earl of Westmorland (d. 1601)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Westmorland\" title=\"<PERSON>, 6th Earl of Westmorland\"><PERSON>, 6th Earl of Westmorland</a> (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Westmorland\" title=\"<PERSON>, 6th Earl of Westmorland\"><PERSON>, 6th Earl of Westmorland</a> (d. 1601)", "links": [{"title": "<PERSON>, 6th Earl of Westmorland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Westmorland"}]}, {"year": "1579", "text": "Countess <PERSON> of Nassau (d. 1640)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Nassau\" title=\"Countess <PERSON> of Nassau\">Countess <PERSON> of Nassau</a> (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Nassau\" title=\"Countess <PERSON> of Nassau\">Countess <PERSON> of Nassau</a> (d. 1640)", "links": [{"title": "Countess <PERSON> of Nassau", "link": "https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_<PERSON>_Nassau"}]}, {"year": "1587", "text": "<PERSON>, granddaughter of Governor <PERSON> of the Colony of Roanoke, first child born to English parents in the Americas (date of death unknown)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/Virginia_Dare\" title=\"Virginia Dare\"><PERSON> Dare</a>, granddaughter of Governor <a href=\"https://wikipedia.org/wiki/<PERSON>(colonist_and_artist)\" title=\"<PERSON> (colonist and artist)\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Roanoke_Colony\" title=\"Roanoke Colony\">Colony of Roanoke</a>, first child born to English parents in the Americas (date of death unknown)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Dare\" title=\"Virginia Dare\"><PERSON> Dare</a>, granddaughter of Governor <a href=\"https://wikipedia.org/wiki/<PERSON>_(colonist_and_artist)\" title=\"<PERSON> (colonist and artist)\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Roanoke_Colony\" title=\"Roanoke Colony\">Colony of Roanoke</a>, first child born to English parents in the Americas (date of death unknown)", "links": [{"title": "Virginia Dare", "link": "https://wikipedia.org/wiki/Virginia_Dare"}, {"title": "<PERSON> (colonist and artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist_and_artist)"}, {"title": "Roanoke Colony", "link": "https://wikipedia.org/wiki/Roanoke_Colony"}]}, {"year": "1596", "text": "<PERSON>, Flemish priest and hagiographer (d. 1665)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish priest and hagiographer (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish priest and hagiographer (d. 1665)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1605", "text": "<PERSON>, English churchman and theologian (d. 1660)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English churchman and theologian (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English churchman and theologian (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON> of Spain (d. 1646)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1646)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Spain"}]}, {"year": "1629", "text": "<PERSON><PERSON><PERSON>, Swedish writer (d. 1672)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Agne<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish writer (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Agne<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish writer (d. 1672)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agneta_Horn"}]}, {"year": "1657", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian architect and painter (d. 1743)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Bibiena\" title=\"<PERSON><PERSON>Bibi<PERSON>\"><PERSON><PERSON></a>, Italian architect and painter (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Bibiena\" title=\"<PERSON><PERSON>B<PERSON>\"><PERSON><PERSON></a>, Italian architect and painter (d. 1743)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferdinando_Galli-Bibiena"}]}, {"year": "1685", "text": "<PERSON>, English mathematician and theorist (d. 1731)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Taylor\"><PERSON></a>, English mathematician and theorist (d. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Taylor\"><PERSON></a>, English mathematician and theorist (d. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1692", "text": "<PERSON>, Duke of Bourbon (d. 1740)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (d. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (d. 1740)", "links": [{"title": "<PERSON>, Duke of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon"}]}, {"year": "1700", "text": "<PERSON><PERSON>, first Pesh<PERSON> of Maratha Empire (d. 1740)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> I</a>, first <a href=\"https://wikipedia.org/wiki/Peshwa\" title=\"<PERSON>esh<PERSON>\">Peshwa</a> of <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> I</a>, first <a href=\"https://wikipedia.org/wiki/Peshwa\" title=\"<PERSON>esh<PERSON>\">Peshwa</a> of <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1740)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Peshwa", "link": "https://wikipedia.org/wiki/Peshwa"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}]}, {"year": "1720", "text": "<PERSON>, 4th Earl <PERSON>, English politician (d. 1760)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th Earl <PERSON></a>, English politician (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th <PERSON></a>, English politician (d. 1760)", "links": [{"title": "<PERSON>, 4th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, Italian composer and conductor (d. 1825)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and conductor (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and conductor (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, marquis de <PERSON><PERSON><PERSON>, French general and engineer (d. 1833)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>,_marquis_de_<PERSON><PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, marquis de Chasseloup-Laubat\"><PERSON>, marquis de <PERSON>-<PERSON></a>, French general and engineer (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>,_marquis_de_<PERSON>ou<PERSON>-Laubat\" class=\"mw-redirect\" title=\"<PERSON>, marquis de Chasseloup-Laubat\"><PERSON>, marquis de <PERSON>-<PERSON></a>, French general and engineer (d. 1833)", "links": [{"title": "<PERSON>, marquis de Chasseloup-Laubat", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>,_marquis_<PERSON>_<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1774", "text": "<PERSON><PERSON><PERSON><PERSON>, American soldier, explorer, and politician (d. 1809)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American soldier, explorer, and politician (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American soldier, explorer, and politician (d. 1809)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, 1st <PERSON>, English politician, Prime Minister of the United Kingdom (d. 1878)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1878)", "links": [{"title": "<PERSON>, 1st Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1803", "text": "<PERSON>, American lawyer, jurist, and politician, 19th United States Attorney General (d. 1881)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician, 19th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician, 19th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1807", "text": "<PERSON><PERSON> <PERSON><PERSON>, Australian politician, 1st Premier of South Australia (d. 1893)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1893)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1819", "text": "Grand Duchess <PERSON> of Russia (d. 1876)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1819%E2%80%931876)\" class=\"mw-redirect\" title=\"Grand Duchess <PERSON> of Russia (1819-1876)\">Grand Duchess <PERSON> of Russia</a> (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1819%E2%80%931876)\" class=\"mw-redirect\" title=\"Grand Duchess <PERSON> of Russia (1819-1876)\">Grand Duchess <PERSON> of Russia</a> (d. 1876)", "links": [{"title": "Grand Duchess <PERSON> of Russia (1819-1876)", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1819%E2%80%931876)"}]}, {"year": "1822", "text": "<PERSON>, American general and politician (d. 1862)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON> of Austria (d. 1916)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1916)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1831", "text": "<PERSON>, Scottish businessman and politician (d. 1931)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, American businessman, founded Marshall Field's (d. 1906)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Marshall_Field\" title=\"Marshall Field\">Marshall Field</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Marshall_Field%27s\" title=\"Marshall Field's\">Marshall Field's</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marshall_Field\" title=\"Marshall Field\">Marshall Field</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Marshall_Field%27s\" title=\"Marshall Field's\">Marshall Field's</a> (d. 1906)", "links": [{"title": "Marshall Field", "link": "https://wikipedia.org/wiki/Marshall_Field"}, {"title": "Marshall Field's", "link": "https://wikipedia.org/wiki/Marshall_Field%27s"}]}, {"year": "1841", "text": "<PERSON>, English-American lieutenant, Medal of Honor recipient (d. 1919)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1855", "text": "<PERSON>, English painter and illustrator (d. 1942)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON>, Belgian-American bishop and missionary (d. 1926)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-American bishop and missionary (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-American bishop and missionary (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Bo<PERSON>ms"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON>, 6th Nizam of Hyderabad (d. 1911)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, 6th <a href=\"https://wikipedia.org/wiki/Nizam\" class=\"mw-redirect\" title=\"Nizam\"><PERSON><PERSON></a> of Hyderabad (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, 6th <a href=\"https://wikipedia.org/wiki/Nizam\" class=\"mw-redirect\" title=\"Nizam\"><PERSON><PERSON></a> of Hyderabad (d. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nizam"}]}, {"year": "1869", "text": "<PERSON>, German-American painter and educator (d. 1959)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and educator (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and educator (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON>, Russian general and explorer (d. 1918)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Lav<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general and explorer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lav<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general and explorer (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON><PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Russian general (d. 1970)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Australian poet and critic (d. 1964)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian poet and critic (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian poet and critic (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, English rugby player, mines inspector, engineer and professor of mining (d. 1957)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player, mines inspector, engineer and professor of mining (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player, mines inspector, engineer and professor of mining (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, German economist and politician, Reich Minister of Economics (d. 1960)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German economist and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)\" class=\"mw-redirect\" title=\"Federal Ministry for Economic Affairs and Energy (Germany)\">Reich Minister of Economics</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German economist and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)\" class=\"mw-redirect\" title=\"Federal Ministry for Economic Affairs and Energy (Germany)\">Reich Minister of Economics</a> (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Federal Ministry for Economic Affairs and Energy (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, American baseball player and manager (d. 1985)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (d. 1985)", "links": [{"title": "B<PERSON><PERSON> Grimes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Canadian conductor and composer (d. 1973)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian conductor and composer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian conductor and composer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Canadian-American actor and director (d. 1933)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and director (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and director (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Italian race car driver (d. 1955)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Soviet Communist activist, sentenced to a labor camp during <PERSON>'s Great Purge (d. 1987)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet Communist activist, sentenced to a labor camp during <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Great_Purge\" title=\"Great Purge\">Great Purge</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet Communist activist, sentenced to a labor camp during <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Great_Purge\" title=\"Great Purge\">Great Purge</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Great Purge", "link": "https://wikipedia.org/wiki/Great_Purge"}]}, {"year": "1900", "text": "<PERSON>, American religious leader (d. 1993)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian painter (d. 1968)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Estonian painter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian painter (d. 1968)", "links": [{"title": "Adamson-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-Eric"}]}, {"year": "1902", "text": "<PERSON>, American environmentalist and author (d. 2003)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and author (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, French singer (d. 1983)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Jr., American businessman (d. 1996)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Max_Factor,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_Factor,_Jr.\" class=\"mw-redirect\" title=\"<PERSON> Factor, Jr.\"><PERSON>, Jr.</a>, American businessman (d. 1996)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1905", "text": "<PERSON><PERSON>, American bandleader, violinist, and recording engineer (d. 1978)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Enoch_Light\" title=\"Enoch Light\"><PERSON><PERSON></a>, American bandleader, violinist, and recording engineer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Enoch_Light\" title=\"Enoch Light\"><PERSON><PERSON></a>, American bandleader, violinist, and recording engineer (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enoch_Light"}]}, {"year": "1906", "text": "<PERSON>, French director and screenwriter (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Carn%C3%A9"}]}, {"year": "1906", "text": "<PERSON>, American blues pianist and singer (d. 1971)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American blues pianist and singer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American blues pianist and singer (d. 1971)", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)"}]}, {"year": "1908", "text": "<PERSON>, French historian and politician, 139th Prime Minister of France (d. 1988)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and politician, 139th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and politician, 139th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Norwegian poet and gardener (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet and gardener (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet and gardener (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, New Zealand cricketer and sportscaster (d. 1977)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and sportscaster (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and sportscaster (d. 1977)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Canadian businessman and journalist (d. 2005)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_Filion\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and journalist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rard_Filion\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and journalist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_Filion"}]}, {"year": "1910", "text": "<PERSON>, Polish-American pianist, composer, and conductor (d. 2001)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist, composer, and conductor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist, composer, and conductor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Canadian colonel, engineer, and politician, 26th Canadian Minister of Public Works (d. 1969)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel, engineer, and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)\" title=\"Minister of Public Works (Canada)\">Canadian Minister of Public Works</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel, engineer, and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)\" title=\"Minister of Public Works (Canada)\">Canadian Minister of Public Works</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Public Works (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)"}]}, {"year": "1911", "text": "<PERSON>, American activist (d. 2015)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Hungarian computer scientist and programmer (d. 1963)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian computer scientist and programmer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian computer scientist and programmer (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Indonesian politician and women's rights activist (d. 1988)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian politician and women's rights activist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian politician and women's rights activist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, German general (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Belgian cyclist (d. 1983)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, United States Navy lieutenant commander and psychiatrist (d. 2017)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> <a href=\"https://wikipedia.org/wiki/Lieutenant_commander\" title=\"Lieutenant commander\">lieutenant commander</a> and psychiatrist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> <a href=\"https://wikipedia.org/wiki/Lieutenant_commander\" title=\"Lieutenant commander\">lieutenant commander</a> and psychiatrist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Lieutenant commander", "link": "https://wikipedia.org/wiki/Lieutenant_commander"}]}, {"year": "1915", "text": "<PERSON>, American baseball player and manager (d. 2007)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Romanian historian, journalist, and diplomat (d. 2018)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>eagu_<PERSON>\" title=\"Neagu Djuvar<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian historian, journalist, and diplomat (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eagu <PERSON>ju<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian historian, journalist, and diplomat (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ea<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, English pianist (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lympany\" title=\"<PERSON>ura Lympany\"><PERSON><PERSON></a>, English pianist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lympany\" title=\"<PERSON>ura Lympany\"><PERSON><PERSON></a>, English pianist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moura_Lympany"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, American captain, lawyer, and politician, 15th United States Secretary of Defense (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 1961)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Cisco_Houston\" title=\"Cisco Houston\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cisco_Houston\" title=\"Cisco Houston\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1961)", "links": [{"title": "Cisco Houston", "link": "https://wikipedia.org/wiki/Cisco_Houston"}]}, {"year": "1919", "text": "<PERSON>, American businessman and politician, 2nd Governor of Alaska (d. 2010)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Alaska\" class=\"mw-redirect\" title=\"List of Governors of Alaska\">Governor of Alaska</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Alaska\" class=\"mw-redirect\" title=\"List of Governors of Alaska\">Governor of Alaska</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Alaska", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Alaska"}]}, {"year": "1920", "text": "<PERSON>, English cricketer (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American baseball player and manager (d. 2005)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actress (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Russian lieutenant and pilot (d. 1943)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lieutenant and pilot (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lieutenant and pilot (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish historian and academic (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_(junior)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (junior)\"><PERSON><PERSON><PERSON><PERSON></a>, Polish historian and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_(junior)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (junior)\"><PERSON><PERSON><PERSON><PERSON></a>, Polish historian and academic (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (junior)", "link": "https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON>yguls<PERSON>_(junior)"}]}, {"year": "1922", "text": "<PERSON>-<PERSON>, French director, screenwriter, and novelist (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and novelist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and novelist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actress (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English author and critic (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian surgeon and academic (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian surgeon and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian surgeon and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Egyptian journalist and author (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian journalist and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian journalist and author (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, 41st First Lady of the United States (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, 41st First Lady of the United States (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, 41st First Lady of the United States (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American businesswoman (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American R&B singer (d. 1981)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Til\"><PERSON></a>, American R&amp;B singer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T<PERSON>\" title=\"<PERSON> Til\"><PERSON></a>, American R&amp;B singer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Til"}]}, {"year": "1929", "text": "<PERSON><PERSON>, French singer-songwriter", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Romanian-American engineer and academic (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-American engineer and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-American engineer and academic (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Honduran academic and politician (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran academic and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran academic and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Pineda_Ponce"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Canadian 14th General of The Salvation Army (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Bram<PERSON> Tillsley\"><PERSON><PERSON></a>, Canadian 14th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian 14th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1931", "text": "<PERSON>, Dutch journalist and politician, Deputy Prime Minister of the Netherlands (d. 2010)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands"}]}, {"year": "1931", "text": "<PERSON>, American film, theater and television actor (d. 1985)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American film, theater and television actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American film, theater and television actor (d. 1985)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1932", "text": "<PERSON>, French virologist and academic, Nobel Prize laureate (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1933", "text": "<PERSON>, Moroccan-French footballer and manager (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Just_Fontaine\" title=\"Just Fontaine\"><PERSON></a>, Moroccan-French footballer and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Just_Fontaine\" title=\"Just Fontaine\"><PERSON></a>, Moroccan-French footballer and manager (d. 2023)", "links": [{"title": "Just <PERSON>", "link": "https://wikipedia.org/wiki/Just_Fontaine"}]}, {"year": "1933", "text": "<PERSON>, French-Polish director, producer, screenwriter, and actor", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Roman_Polanski\" title=\"Roman Polanski\"><PERSON></a>, French-Polish director, producer, screenwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Polanski\" title=\"Roman Polanski\"><PERSON></a>, French-Polish director, producer, screenwriter, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Polanski"}]}, {"year": "1933", "text": "<PERSON>, American gangster and hitman (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster and hitman (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster and hitman (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American lawyer and author (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Puerto Rican-American baseball player and soldier (d. 1972)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and soldier (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and soldier (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Indian poet, lyricist and film director", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, lyricist and film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, lyricist and film director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American decathlete and actor (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American decathlete and actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American decathlete and actor (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German-Swiss race car driver and engineer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, German-Swiss race car driver and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, German-Swiss race car driver and engineer", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1935", "text": "<PERSON>, American actress (d. 2000)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Namibian lawyer and politician, 2nd President of Namibia", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Hifikepunye_Pohamba\" title=\"Hifikepunye Pohamba\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Namibian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Namibia\" title=\"President of Namibia\">President of Namibia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hifikepunye_Pohamba\" title=\"Hifikepunye Pohamba\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Namibian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Namibia\" title=\"President of Namibia\">President of Namibia</a>", "links": [{"title": "Hifikepunye <PERSON>", "link": "https://wikipedia.org/wiki/Hifikepunye_Pohamba"}, {"title": "President of Namibia", "link": "https://wikipedia.org/wiki/President_of_Namibia"}]}, {"year": "1936", "text": "<PERSON>, American actor, director, and producer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English physician and author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American soul/R&B singer-songwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soul_singer)\" title=\"<PERSON><PERSON> (soul singer)\"><PERSON><PERSON></a>, American soul/R&amp;B singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soul_singer)\" title=\"<PERSON><PERSON> (soul singer)\"><PERSON><PERSON></a>, American soul/R&amp;B singer-songwriter", "links": [{"title": "<PERSON><PERSON> (soul singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soul_singer)"}]}, {"year": "1939", "text": "<PERSON>, English businessman (d. 2011)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman (d. 2011)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>(businessman)"}]}, {"year": "1939", "text": "<PERSON>, American pop singer (d. 2011)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Polish-Canadian pianist and composer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American journalist (d. 1982)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor and comedian (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Italian footballer and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English singer and actor (d. 2004)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American author (d. 2004)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian sculptor and illustrator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sculptor and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sculptor and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and actress (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dash\"><PERSON></a>, American singer-songwriter and actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Estonian lawyer and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/V%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A4<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Jr., American soldier, lawyer, and author (d. 1994)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American soldier, lawyer, and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American soldier, lawyer, and author (d. 1994)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1948", "text": "<PERSON>, English bishop", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1948", "text": "<PERSON>, English intelligence officer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English intelligence officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English intelligence officer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English bass player, songwriter, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English drummer and sculptor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American actress, director, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor and dancer (d. 2009)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Argentinian footballer and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Ricardo_Villa\" title=\"Ricardo Villa\"><PERSON></a>, Argentinian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ricardo_Villa\" title=\"Ricardo Villa\"><PERSON></a>, Argentinian footballer and coach", "links": [{"title": "Ricardo <PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Villa"}]}, {"year": "1953", "text": "<PERSON>, American captain, lawyer, and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American R&B bass player and songwriter (d. 2010)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B bass player and songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B bass player and songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Italian astrophysicist, astronaut, and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian astrophysicist, astronaut, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian astrophysicist, astronaut, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Egyptian-American cryptographer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-American cryptographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-American cryptographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al"}]}, {"year": "1956", "text": "<PERSON>, American composer and conductor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Indian cricketer and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sandeep Patil\"><PERSON><PERSON></a>, Indian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sandeep Patil\"><PERSON><PERSON></a>, Indian cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>il"}]}, {"year": "1956", "text": "<PERSON>, American drummer and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer and producer", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>(drummer)"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, German cardinal", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German cardinal", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1957", "text": "<PERSON>, French actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Chinese composer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American comedian, actor, producer, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian singer-songwriter, guitarist, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, French race car driver", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Auriol"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1959", "text": "<PERSON>, American wrestler and trainer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American basketball player and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Lever\" title=\"Fat Lever\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lever\" title=\"Fat Lever\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON> Lever", "link": "https://wikipedia.org/wiki/<PERSON>_Lever"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Welsh journalist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(journalist)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (journalist)\"><PERSON><PERSON></a>, Welsh journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(journalist)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (journalist)\"><PERSON><PERSON></a>, Welsh journalist and author", "links": [{"title": "<PERSON><PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(journalist)"}]}, {"year": "1961", "text": "<PERSON>, American banker and politician, 75th United States Secretary of the Treasury", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 75th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 75th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1961", "text": "<PERSON>, American journalist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Mexican lawyer and politician, 56th President of Mexico", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 56th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 56th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Felipe_Calder%C3%B3n"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German singer and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>is"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/I<PERSON>e_%C5%8Ctani\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON>e_%C5%8Ctani\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ikue_%C5%8Ctani"}]}, {"year": "1966", "text": "<PERSON>, Argentinian director and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Indian Punjabi singer, songwriter and record producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Punjabi singer, songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Punjabi singer, songwriter and record producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American author and illustrator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American singer, rapper, and musician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Everlast\" title=\"Everlast\"><PERSON><PERSON></a>, American singer, rapper, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Everlast\" title=\"Everlast\"><PERSON><PERSON></a>, American singer, rapper, and musician", "links": [{"title": "Everlast", "link": "https://wikipedia.org/wiki/Everlast"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American rapper", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1969", "text": "<PERSON>, German rugby player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American economist and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "Malcolm<PERSON><PERSON>, American actor and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and producer", "links": [{"title": "Malcolm<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English musician and record producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Aphex_Twin\" title=\"Aphex Twin\"><PERSON></a>, English musician and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aphex_Twin\" title=\"Aphex Twin\"><PERSON></a>, English musician and record producer", "links": [{"title": "Aphex Twin", "link": "https://wikipedia.org/wiki/Aphex_Twin"}]}, {"year": "1974", "text": "<PERSON>, American novelist and critic", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American actress and comedian", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Para<PERSON><PERSON> Antz<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Para<PERSON><PERSON> Antz<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON> <PERSON><PERSON><PERSON>, Norwegian musician and educator", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON><PERSON>\"><PERSON> <PERSON><PERSON><PERSON></a>, Norwegian musician and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON><PERSON>\"><PERSON> <PERSON><PERSON><PERSON></a>, Norwegian musician and educator", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor and comedian", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>w"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Esteban_Cambiasso\" title=\"Esteban Cambiasso\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Cambiasso\" title=\"Este<PERSON> Cambiasso\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esteban_Cambiasso"}]}, {"year": "1980", "text": "<PERSON>, Australian race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ryan_O%27Hara"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentinian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Greek footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>id<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Lebanese-born English recording artist and singer-songwriter ", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Lebanese-born English recording artist and singer-songwriter ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Lebanese-born English recording artist and singer-songwriter ", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1983", "text": "<PERSON>, Australian cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>jar\" title=\"Sigourney Bandjar\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>jar\" title=\"Sigourney Bandjar\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bandjar"}]}, {"year": "1984", "text": "<PERSON>, German footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Dutch swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/In<PERSON>_<PERSON>\" title=\"Inge Dekker\"><PERSON><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In<PERSON>_<PERSON>\" title=\"In<PERSON> De<PERSON>\"><PERSON><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Costa Rican footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Scottish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Polish mixed martial artist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%99dr<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%99dr<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joanna_J%C4%99dr<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Icelandic footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_J%C3%B3nsson"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, South Korean rapper, singer-songwriter and record producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/G-Dragon\" title=\"G-Dragon\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer-songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G-Dragon\" title=\"G-Dragon\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer-songwriter and record producer", "links": [{"title": "G-Dragon", "link": "https://wikipedia.org/wiki/G-Dragon"}]}, {"year": "1989", "text": "<PERSON>, American actress, comedian, musician, and YouTuber", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, musician, and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, musician, and YouTuber", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Singaporean table tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mengyu\"><PERSON></a>, Singaporean table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mengyu\"><PERSON></a>, Singaporean table tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}]}, {"year": "1991", "text": "<PERSON>, Australian basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Serbian basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Bogdan_Bogdanovi%C4%87_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo<PERSON><PERSON>_Bogdanovi%C4%87_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON> (basketball)", "link": "https://wikipedia.org/wiki/Bogdan_Bogdanovi%C4%87_(basketball)"}]}, {"year": "1992", "text": "<PERSON>, American visual artist and model ", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cobain\" title=\"<PERSON> Cobain\"><PERSON></a>, American visual artist and model ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Coba<PERSON>\" title=\"<PERSON> Cobain\"><PERSON></a>, American visual artist and model ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>in"}]}, {"year": "1993", "text": "<PERSON>, South Korean singer-songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> E<PERSON>-ji\"><PERSON></a>, South Korean singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> Eun-ji\"><PERSON></a>, South Korean singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian actress and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American actress and YouTuber", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and YouTuber", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sanson"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Latvian figure skater", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Al%C4%<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al%C4%<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al%C4%<PERSON><PERSON>_F<PERSON><PERSON>ova"}]}, {"year": "1995", "text": "<PERSON>, American actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Renato Sanches\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Renato Sanches\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Renato_Sanches"}]}, {"year": "1998", "text": "<PERSON>, Australian-Samoan rugby league player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27o\" title=\"<PERSON>\"><PERSON></a>, Australian-Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27o\" title=\"<PERSON>\"><PERSON></a>, Australian-Samoan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27o"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Clairo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clairo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clairo"}]}, {"year": "1998", "text": "<PERSON>, American far-right political commentator", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American far-right political commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American far-right political commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ius Stanley\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ius Stanley\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Stanley"}]}], "Deaths": [{"year": "353", "text": "<PERSON><PERSON><PERSON>, Roman usurper", "html": "353 - <a href=\"https://wikipedia.org/wiki/Decentius\" title=\"<PERSON>ent<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman usurper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Decentius\" title=\"Decent<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman usurper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Decentius"}]}, {"year": "440", "text": "<PERSON> <PERSON><PERSON> III", "html": "440 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sixtus_III\" title=\"Pope Sixtus III\"><PERSON> <PERSON>tus III</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sixtus_III\" title=\"Pope Sixtus III\"><PERSON> <PERSON>tus III</a>", "links": [{"title": "<PERSON> <PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_III"}]}, {"year": "472", "text": "<PERSON><PERSON><PERSON>, Roman general and politician (b. 405)", "html": "472 - <a href=\"https://wikipedia.org/wiki/Ricimer\" title=\"Ricimer\"><PERSON><PERSON><PERSON></a>, Roman general and politician (b. 405)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ricimer\" title=\"Ricimer\"><PERSON><PERSON><PERSON></a>, Roman general and politician (b. 405)", "links": [{"title": "Ricimer", "link": "https://wikipedia.org/wiki/Ricimer"}]}, {"year": "670", "text": "<PERSON><PERSON><PERSON>, Irish hermit", "html": "670 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fiacre\" title=\"<PERSON> Fiacre\"><PERSON><PERSON><PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Recluse\" title=\"<PERSON>clus<PERSON>\">hermit</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Fi<PERSON>re\" title=\"<PERSON> Fiacre\"><PERSON><PERSON><PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Recluse\" title=\"<PERSON><PERSON>lus<PERSON>\">hermit</a>", "links": [{"title": "Saint <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Recluse"}]}, {"year": "673", "text": "<PERSON>, general of Silla (b. 595)", "html": "673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>hin\" class=\"mw-redirect\" title=\"<PERSON>hin\"><PERSON></a>, general of <a href=\"https://wikipedia.org/wiki/Silla\" title=\"Silla\"><PERSON>lla</a> (b. 595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>hin\" class=\"mw-redirect\" title=\"<PERSON>hin\"><PERSON></a>, general of <a href=\"https://wikipedia.org/wiki/Silla\" title=\"Silla\"><PERSON><PERSON></a> (b. 595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-shin"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Silla"}]}, {"year": "849", "text": "<PERSON><PERSON><PERSON><PERSON>, German monk and theologian (b. 808)", "html": "849 - <a href=\"https://wikipedia.org/wiki/Walafrid_Strabo\" title=\"Wala<PERSON><PERSON> Strabo\"><PERSON><PERSON><PERSON><PERSON></a>, German monk and theologian (b. 808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walafrid_Strabo\" title=\"Wala<PERSON>rid Strabo\"><PERSON><PERSON><PERSON><PERSON></a>, German monk and theologian (b. 808)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Walafrid_Strabo"}]}, {"year": "911", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, first Zaydi Imam of Yemen (b. 859)", "html": "911 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Hadi_ila%27l-<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Al-<PERSON>i ila'l-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>l<PERSON><PERSON><PERSON><PERSON></a>, first <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Imam_of_Yemen\" class=\"mw-redirect\" title=\"Imam of Yemen\">Imam of Yemen</a> (b. 859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-Hadi_ila%27l-<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Al-Hadi ila'l-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>l<PERSON><PERSON><PERSON><PERSON></a>, first <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Imam_of_Yemen\" class=\"mw-redirect\" title=\"Imam of Yemen\">Imam of Yemen</a> (b. 859)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Hadi_ila%27l-<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Imam of Yemen", "link": "https://wikipedia.org/wiki/Imam_of_Yemen"}]}, {"year": "1095", "text": "King <PERSON> of Denmark", "html": "1095 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1211", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, king of Burma (b. 1150)", "html": "1211 - <a href=\"https://wikipedia.org/wiki/Narapatisithu\" title=\"Narap<PERSON><PERSON>th<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of Burma (b. 1150)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narapatisithu\" title=\"Narap<PERSON><PERSON>th<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of Burma (b. 1150)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narapatisithu"}]}, {"year": "1258", "text": "<PERSON>, emperor of Nicea (Byzantine emperor in exile)", "html": "1258 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, emperor of Nicea (<a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a> in exile)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, emperor of Nicea (<a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a> in exile)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Byzantine emperor", "link": "https://wikipedia.org/wiki/Byzantine_emperor"}]}, {"year": "1276", "text": "<PERSON> (b. 1220)", "html": "1276 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Adrian <PERSON>\">Pope <PERSON></a> (b. 1220)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Adrian V\"><PERSON> V</a> (b. 1220)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1318", "text": "<PERSON> of Montefalco, Italian nun and saint (b. 1268)", "html": "1318 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Montefalco\" title=\"<PERSON> of Montefalco\"><PERSON> of Montefalco</a>, Italian nun and saint (b. 1268)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Montefalco\" title=\"<PERSON> of Montefalco\"><PERSON> of Montefalco</a>, Italian nun and saint (b. 1268)", "links": [{"title": "Clare of Montefalco", "link": "https://wikipedia.org/wiki/<PERSON>_of_Montefalco"}]}, {"year": "1430", "text": "<PERSON>, 8th Baron <PERSON>, English soldier and politician (b. 1406)", "html": "1430 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 8th Baron <PERSON>\"><PERSON>, 8th Baron <PERSON></a>, English soldier and politician (b. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 8th Baron <PERSON>\"><PERSON>, 8th Baron <PERSON></a>, English soldier and politician (b. 1406)", "links": [{"title": "<PERSON>, 8th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Baron_<PERSON>_<PERSON>"}]}, {"year": "1500", "text": "<PERSON> of Aragon, Spanish prince (b. 1481)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon_(1481%E2%80%931500)\" title=\"<PERSON> of Aragon (1481-1500)\"><PERSON> Aragon</a>, Spanish prince (b. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon_(1481%E2%80%931500)\" title=\"<PERSON> of Aragon (1481-1500)\"><PERSON> of Aragon</a>, Spanish prince (b. 1481)", "links": [{"title": "<PERSON> of Aragon (1481-1500)", "link": "https://wikipedia.org/wiki/Alfonso_of_Aragon_(1481%E2%80%931500)"}]}, {"year": "1502", "text": "<PERSON><PERSON>, Norwegian nobleman and politician (b. 1455)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian nobleman and politician (b. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian nobleman and politician (b. 1455)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1503", "text": "<PERSON> (b. 1431)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VI\" title=\"Pope Alexander VI\">Pope <PERSON> VI</a> (b. 1431)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VI\" title=\"<PERSON> Alexander VI\">Pope <PERSON> VI</a> (b. 1431)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1550", "text": "<PERSON>, Italian architect and military engineer", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and military engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and military engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_<PERSON>"}]}, {"year": "1559", "text": "<PERSON> (b. 1476)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"<PERSON> Paul IV\"><PERSON></a> (b. 1476)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\"><PERSON></a> (b. 1476)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1563", "text": "<PERSON>, French judge and philosopher (b. 1530)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_de_La_Bo%C3%A9tie\" title=\"<PERSON>\"><PERSON></a>, French judge and philosopher (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_de_La_Bo%C3%A9tie\" title=\"<PERSON>\"><PERSON></a>, French judge and philosopher (b. 1530)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_de_La_Bo%C3%A9tie"}]}, {"year": "1600", "text": "<PERSON><PERSON>, Italian businessman (b. 1516)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian businessman (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian businessman (b. 1516)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1613", "text": "<PERSON>, Italian composer and theorist (b. 1540)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and theorist (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and theorist (b. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_Artusi"}]}, {"year": "1620", "text": "<PERSON><PERSON> Emperor of China (b. 1563)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor\" title=\"Wanli Emperor\"><PERSON>li Emperor</a> of China (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor\" title=\"Wanli Emperor\"><PERSON><PERSON> Emperor</a> of China (b. 1563)", "links": [{"title": "<PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}]}, {"year": "1625", "text": "<PERSON>, 11th Baron <PERSON>, English diplomat (b. 1556)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Baron_<PERSON>\" title=\"<PERSON>, 11th Baron <PERSON>\"><PERSON>, 11th Baron <PERSON></a>, English diplomat (b. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Baron_<PERSON>\" title=\"<PERSON>, 11th Baron <PERSON>\"><PERSON>, 11th Baron <PERSON></a>, English diplomat (b. 1556)", "links": [{"title": "<PERSON>, 11th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Baron_<PERSON>"}]}, {"year": "1634", "text": "<PERSON><PERSON><PERSON>, French priest (b. 1590)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ier\" title=\"<PERSON><PERSON><PERSON>ier\"><PERSON><PERSON><PERSON></a>, French priest (b. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ier\" title=\"<PERSON><PERSON><PERSON>ier\"><PERSON><PERSON><PERSON></a>, French priest (b. 1590)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>ier"}]}, {"year": "1642", "text": "<PERSON>, Italian painter and educator (b. 1575)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and educator (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and educator (b. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON> of the Ottoman Empire (b. 1615)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire\" title=\"Ibrahim of the Ottoman Empire\">Ibrahim of the Ottoman Empire</a> (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire\" title=\"Ibrahim of the Ottoman Empire\">Ibrahim of the Ottoman Empire</a> (b. 1615)", "links": [{"title": "<PERSON> of the Ottoman Empire", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire"}]}, {"year": "1683", "text": "<PERSON>, English actor (b. 1625)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(17th-century_actor)\" class=\"mw-redirect\" title=\"<PERSON> (17th-century actor)\"><PERSON></a>, English actor (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(17th-century_actor)\" class=\"mw-redirect\" title=\"<PERSON> (17th-century actor)\"><PERSON></a>, English actor (b. 1625)", "links": [{"title": "<PERSON> (17th-century actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(17th-century_actor)"}]}, {"year": "1707", "text": "<PERSON>, 1st Duke of Devonshire, English soldier and politician, Lord Lieutenant of Derbyshire (b. 1640)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Devonshire\" title=\"<PERSON>, 1st Duke of Devonshire\"><PERSON>, 1st Duke of Devonshire</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Derbyshire\" title=\"Lord Lieutenant of Derbyshire\">Lord Lieutenant of Derbyshire</a> (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Devonshire\" title=\"<PERSON>, 1st Duke of Devonshire\"><PERSON>, 1st Duke of Devonshire</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Derbyshire\" title=\"Lord Lieutenant of Derbyshire\">Lord Lieutenant of Derbyshire</a> (b. 1640)", "links": [{"title": "<PERSON>, 1st Duke of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Devonshire"}, {"title": "Lord Lieutenant of Derbyshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Derbyshire"}]}, {"year": "1712", "text": "<PERSON>, 4th <PERSON>, English general and politician, Lord Lieutenant of Essex (b. 1660)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Earl <PERSON>\"><PERSON>, 4th <PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex\" title=\"Lord Lieutenant of Essex\">Lord Lieutenant of Essex</a> (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Earl <PERSON>\"><PERSON>, 4th <PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex\" title=\"Lord Lieutenant of Essex\">Lord Lieutenant of Essex</a> (b. 1660)", "links": [{"title": "<PERSON>, 4th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Essex", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex"}]}, {"year": "1765", "text": "<PERSON>, Holy Roman Emperor (b. 1708)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1708)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician, 8th Lieutenant Governor of Connecticut (b. 1759)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Lieutenant Governor of Connecticut\">Lieutenant Governor of Connecticut</a> (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Lieutenant Governor of Connecticut\">Lieutenant Governor of Connecticut</a> (b. 1759)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Connecticut", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON>, French balloonist and the inventor of the frameless parachute (b. 1769)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French balloonist and the inventor of the frameless parachute (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French balloonist and the inventor of the frameless parachute (b. 1769)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, French explorer and navigator (b. 1779)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer and navigator (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer and navigator (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON>, French novelist and playwright  (b. 1799)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist and playwright (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist and playwright (b. 1799)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_de_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Scottish Quaker (b. 1772)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, Scottish <a href=\"https://wikipedia.org/wiki/Quaker\" class=\"mw-redirect\" title=\"Quaker\">Quaker</a> (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, Scottish <a href=\"https://wikipedia.org/wiki/Quaker\" class=\"mw-redirect\" title=\"Quaker\">Quaker</a> (b. 1772)", "links": [{"title": "<PERSON> (industrialist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrialist)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Quaker"}]}, {"year": "1886", "text": "<PERSON>, American inventor, invented the <PERSON><PERSON><PERSON> lock (b. 1795)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Mortise_lock\" title=\"Mortise lock\">Mortise lock</a> (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Mortise_lock\" title=\"Mortise lock\">Mortise lock</a> (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mortise lock", "link": "https://wikipedia.org/wiki/Mortise_lock"}]}, {"year": "1919", "text": "<PERSON>, Canadian businessman and politician, founded the Seagram Company (b. 1841)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, founded the <a href=\"https://wikipedia.org/wiki/Seagram\" title=\"Seagram\">Seagram Company</a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, founded the <a href=\"https://wikipedia.org/wiki/Seagram\" title=\"Seagram\">Seagram Company</a> (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gram"}, {"title": "Seagram", "link": "https://wikipedia.org/wiki/Seagram"}]}, {"year": "1940", "text": "<PERSON>, American businessman, founded Chrysler (b. 1875)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Walter_Chrysler\" title=\"Walter Chrysler\">Walter Chrysler</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walter_Chrysler\" title=\"Walter Chrysler\">Walter Chrysler</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> (b. 1875)", "links": [{"title": "Walter <PERSON>", "link": "https://wikipedia.org/wiki/Walter_Chrysler"}, {"title": "Chrysler", "link": "https://wikipedia.org/wiki/Chrysler"}]}, {"year": "1942", "text": "<PERSON>, Austro-Czech composer and pianist (b. 1894)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austro-Czech composer and pianist (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austro-Czech composer and pianist (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Azerbaijani general (b. 1865)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani general (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani general (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German soldier and politician (b. 1886)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A4lmann\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_Th%C3%A4lmann"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Indian activist and politician (b. 1897)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist and politician (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist and politician (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Chinese communist (b. 1894)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Che_Yaoxian\" title=\"Che Yaoxian\"><PERSON><PERSON></a>, Chinese communist (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Che_Yaoxian\" title=\"Che Yaoxian\"><PERSON><PERSON></a>, Chinese communist (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Che_Yao<PERSON>n"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Chinese communist (b. 1904)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese communist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese communist (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American trumpet player and bandleader (b. 1900)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Belgian soldier and politician (b. 1884)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian soldier and politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian soldier and politician (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Chilean priest, lawyer, and saint (b. 1901)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean priest, lawyer, and saint (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean priest, lawyer, and saint (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American lawyer, jurist, and philosopher (b. 1872)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Learned_Hand\" title=\"Learned Hand\">Lea<PERSON> Hand</a>, American lawyer, jurist, and philosopher (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Learned_Hand\" title=\"Learned Hand\">Learned Hand</a>, American lawyer, jurist, and philosopher (b. 1872)", "links": [{"title": "Learned Hand", "link": "https://wikipedia.org/wiki/Learned_Hand"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Berlin Wall victim  (b. 1927)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Trabant\" title=\"<PERSON><PERSON><PERSON> Trabant\"><PERSON><PERSON><PERSON> Trab<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a> <a href=\"https://wikipedia.org/wiki/List_of_deaths_at_the_Berlin_Wall\" title=\"List of deaths at the Berlin Wall\">victim</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Trab<PERSON>\" title=\"<PERSON><PERSON><PERSON> Trab<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a> <a href=\"https://wikipedia.org/wiki/List_of_deaths_at_the_Berlin_Wall\" title=\"List of deaths at the Berlin Wall\">victim</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ant"}, {"title": "Berlin Wall", "link": "https://wikipedia.org/wiki/Berlin_Wall"}, {"title": "List of deaths at the Berlin Wall", "link": "https://wikipedia.org/wiki/List_of_deaths_at_the_Berlin_Wall"}]}, {"year": "1968", "text": "<PERSON>, American pianist and composer (b. 1881)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American pianist and composer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American pianist and composer (b. 1881)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Norwegian Army general and war historian (b. 1897)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian Army general and war historian (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian Army general and war historian (b. 1897)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lindb%C3%A4<PERSON><PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian politician (b. 1913)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Vasa<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vasa<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasa<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vasa<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vasantrao_Naik"}]}, {"year": "1981", "text": "<PERSON>, American author and screenwriter (b. 1889)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, German-English historian and scholar (b. 1902)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English historian and scholar (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English historian and scholar (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Bangladeshi Islamic scholar and educationist (b. 1902)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi Islamic scholar and educationist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi Islamic scholar and educationist (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1990", "text": "<PERSON><PERSON> <PERSON><PERSON>, American psychologist and philosopher, invented the <PERSON> box (b. 1904)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American psychologist and philosopher, invented the <a href=\"https://wikipedia.org/wiki/Skinner_box\" class=\"mw-redirect\" title=\"Skinner box\"><PERSON> box</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American psychologist and philosopher, invented the <a href=\"https://wikipedia.org/wiki/Skinner_box\" class=\"mw-redirect\" title=\"Skinner box\"><PERSON> box</a> (b. 1904)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Skinner box", "link": "https://wikipedia.org/wiki/<PERSON>_box"}]}, {"year": "1994", "text": "<PERSON>, American bishop (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Indian model and actress, Femina Miss India 1965 (b. 1948)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian model and actress, <a href=\"https://wikipedia.org/wiki/Femina_Miss_India\" title=\"Femina Miss India\">Femina Miss India 1965</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian model and actress, <a href=\"https://wikipedia.org/wiki/Femina_Miss_India\" title=\"Femina Miss India\">Femina Miss India 1965</a> (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Per<PERSON>_<PERSON>"}, {"title": "Femina Miss India", "link": "https://wikipedia.org/wiki/Femina_Miss_India"}]}, {"year": "2001", "text": "<PERSON>, English chemist and toxicologist (b. 1931)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and toxicologist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and toxicologist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor and screenwriter (b. 1918)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English singer and bassist (b. 1938)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer and bassist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer and bassist (b. 1938)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2004", "text": "<PERSON>, American composer and conductor (b. 1922)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American soldier and politician (b. 1906)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ong"}]}, {"year": "2005", "text": "Chri$ Ca$h, American wrestler (b. 1982)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Chri$_Ca$h\" title=\"Chri$ Ca$h\">Chri$ Ca$h</a>, American wrestler (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chri$_Ca$h\" title=\"Chri$ Ca$h\">Chri$ Ca$h</a>, American wrestler (b. 1982)", "links": [{"title": "Chri$ Ca$h", "link": "https://wikipedia.org/wiki/Chri$_Ca$h"}]}, {"year": "2006", "text": "<PERSON>, Australian rugby player (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American soldier and politician, White House Deputy Chief of Staff (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, <a href=\"https://wikipedia.org/wiki/White_House_Deputy_Chief_of_Staff\" title=\"White House Deputy Chief of Staff\">White House Deputy Chief of Staff</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, <a href=\"https://wikipedia.org/wiki/White_House_Deputy_Chief_of_Staff\" title=\"White House Deputy Chief of Staff\">White House Deputy Chief of Staff</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Deputy Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Deputy_Chief_of_Staff"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, English author (b. 1947)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Magdalen_Nabb\" title=\"Magdale<PERSON> Nabb\"><PERSON><PERSON><PERSON><PERSON></a>, English author (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magdalen_Nabb\" title=\"Magdalen Nabb\"><PERSON><PERSON><PERSON><PERSON></a>, English author (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Magdale<PERSON>_<PERSON>bb"}]}, {"year": "2009", "text": "<PERSON>, South Korean lieutenant and politician, 15th President of South Korea, Nobel Prize laureate (b. 1925)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean lieutenant and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean lieutenant and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "2009", "text": "<PERSON>, Ukrainian-American economist and author (b. 1910)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American economist and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American economist and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American journalist and author (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American hammer thrower and coach (b. 1931)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hammer thrower and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hammer thrower and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American scholar and jurist (b. 1911)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and jurist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and jurist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American painter (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "2012", "text": "<PERSON>, American football player (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian journalist and author (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian journalist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian journalist and author (b. 1927)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Filipino public servant and politician, 23rd Secretary of the Interior and Local Government (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino public servant and politician, 23rd <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government\" title=\"Secretary of the Interior and Local Government\">Secretary of the Interior and Local Government</a> (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino public servant and politician, 23rd <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government\" title=\"Secretary of the Interior and Local Government\">Secretary of the Interior and Local Government</a> (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of the Interior and Local Government", "link": "https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Angelo\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Angelo\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josephine_D%27Angelo"}]}, {"year": "2013", "text": "<PERSON>, French lawyer and activist (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and activist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and activist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author and critic (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and critic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and critic (b. 1916)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "2014", "text": "<PERSON>, American soldier and politician, 39th Mayor of Hillsboro, Oregon (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 39th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Hillsboro,_Oregon\" title=\"List of mayors of Hillsboro, Oregon\">Mayor of Hillsboro, Oregon</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 39th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Hillsboro,_Oregon\" title=\"List of mayors of Hillsboro, Oregon\">Mayor of Hillsboro, Oregon</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Hillsboro, Oregon", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Hillsboro,_Oregon"}]}, {"year": "2014", "text": "<PERSON>, American captain, lawyer, and politician (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Hungarian chess player (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian chess player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian chess player (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American radio and television announcer (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television announcer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television announcer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Syrian archaeologist and author (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian archaeologist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian archaeologist and author (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English-Australian pianist, composer, and conductor (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian pianist, composer, and conductor (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian pianist, composer, and conductor (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Wife of former Indian president <PERSON><PERSON><PERSON> (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Wife of former Indian president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Wife of former Indian president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American lawyer and politician (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American director, producer, and screenwriter (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> York<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> York<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>in"}]}, {"year": "2016", "text": "<PERSON>, German historian (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, English television presenter and entertainer (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter and entertainer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter and entertainer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Greek actress and beauty pageant winner (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek actress and beauty pageant winner (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek actress and beauty pageant winner (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Nigerian Supreme Court judge (b. 1935)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian Supreme Court judge (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian Supreme Court judge (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Ghanaian diplomat and seventh Secretary-General of the United Nations (b. 1938)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian diplomat and seventh <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian diplomat and seventh <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Annan"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "2020", "text": "<PERSON>, English stage and film actor (b. 1947)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cross\"><PERSON></a>, English stage and film actor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ben Cross\"><PERSON></a>, English stage and film actor (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, the second-oldest orca in captivity (b. ca. 1966)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Lolita_(orca)\" title=\"Lolit<PERSON> (orca)\"><PERSON><PERSON><PERSON></a>, the second-oldest <a href=\"https://wikipedia.org/wiki/Orca\" title=\"Orca\">orca</a> in captivity (b. ca. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(orca)\" title=\"<PERSON><PERSON><PERSON> (orca)\"><PERSON><PERSON><PERSON></a>, the second-oldest <a href=\"https://wikipedia.org/wiki/Orca\" title=\"Orca\">orca</a> in captivity (b. ca. 1966)", "links": [{"title": "Lolita (orca)", "link": "https://wikipedia.org/wiki/Lolita_(orca)"}, {"title": "Orca", "link": "https://wikipedia.org/wiki/Orca"}]}, {"year": "2023", "text": "<PERSON>, American politician, 35th Governor of Minnesota (b. 1923)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}, {"title": "Governor of Minnesota", "link": "https://wikipedia.org/wiki/Governor_of_Minnesota"}]}, {"year": "2024", "text": "<PERSON>, American author and educator, founded ProLiteracy Worldwide (b. 1916)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator, founded <a href=\"https://wikipedia.org/wiki/ProLiteracy_Worldwide\" class=\"mw-redirect\" title=\"ProLiteracy Worldwide\">ProLiteracy Worldwide</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator, founded <a href=\"https://wikipedia.org/wiki/ProLiteracy_Worldwide\" class=\"mw-redirect\" title=\"ProLiteracy Worldwide\">ProLiteracy Worldwide</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "ProLiteracy Worldwide", "link": "https://wikipedia.org/wiki/ProLiteracy_Worldwide"}]}, {"year": "2024", "text": "<PERSON>, French-Swiss actor (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American talk show host and producer (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and producer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and producer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}