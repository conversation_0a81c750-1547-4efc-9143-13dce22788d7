{"date": "April 22", "url": "https://wikipedia.org/wiki/April_22", "data": {"Events": [{"year": "1500", "text": "Portuguese navigator <PERSON> lands in Brazil (discovery of Brazil).", "html": "1500 - Portuguese navigator <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> (<a href=\"https://wikipedia.org/wiki/Discovery_of_Brazil\" title=\"Discovery of Brazil\">discovery of Brazil</a>).", "no_year_html": "Portuguese navigator <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> (<a href=\"https://wikipedia.org/wiki/Discovery_of_Brazil\" title=\"Discovery of Brazil\">discovery of Brazil</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>_<PERSON>"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "Discovery of Brazil", "link": "https://wikipedia.org/wiki/Discovery_of_Brazil"}]}, {"year": "1519", "text": "Spanish conquistador Her<PERSON>n Cortés establishes a settlement at Veracruz, Mexico.", "html": "1519 - Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistador</a> <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s\" title=\"Hernán Cortés\">Herná<PERSON></a> establishes a settlement at <a href=\"https://wikipedia.org/wiki/Veracruz_(city)\" title=\"Veracruz (city)\">Veracruz</a>, Mexico.", "no_year_html": "Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistador</a> <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s\" title=\"Hernán Cortés\">Her<PERSON><PERSON></a> establishes a settlement at <a href=\"https://wikipedia.org/wiki/Veracruz_(city)\" title=\"Veracruz (city)\">Veracruz</a>, Mexico.", "links": [{"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s"}, {"title": "Veracruz (city)", "link": "https://wikipedia.org/wiki/Veracruz_(city)"}]}, {"year": "1529", "text": "Treaty of Zaragoza divides the eastern hemisphere between Spain and Portugal along a line 297.5 leagues (1,250 kilometres (780 mi)) east of the Moluccas.", "html": "1529 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Zaragoza\" title=\"Treaty of Zaragoza\">Treaty of Zaragoza</a> divides the eastern hemisphere between Spain and Portugal along a line 297.5 leagues (1,250 kilometres (780 mi)) east of the <a href=\"https://wikipedia.org/wiki/Moluccas\" class=\"mw-redirect\" title=\"Moluccas\">Moluccas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Zaragoza\" title=\"Treaty of Zaragoza\">Treaty of Zaragoza</a> divides the eastern hemisphere between Spain and Portugal along a line 297.5 leagues (1,250 kilometres (780 mi)) east of the <a href=\"https://wikipedia.org/wiki/Moluccas\" class=\"mw-redirect\" title=\"Moluccas\">Moluccas</a>.", "links": [{"title": "Treaty of Zaragoza", "link": "https://wikipedia.org/wiki/Treaty_of_Zaragoza"}, {"title": "Moluccas", "link": "https://wikipedia.org/wiki/Moluccas"}]}, {"year": "1809", "text": "The second day of the Battle of Eckmühl: The Austrian army is defeated by the First French Empire army led by <PERSON> and driven over the Danube in Regensburg.", "html": "1809 - The second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Eckm%C3%BChl\" title=\"Battle of Eckmühl\">Battle of Eckmühl</a>: The <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> army is defeated by the <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">First French Empire</a> army led by <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a> and driven over the <a href=\"https://wikipedia.org/wiki/Danube\" title=\"Danube\">Danube</a> in <a href=\"https://wikipedia.org/wiki/Regensburg\" title=\"Regensburg\">Regensburg</a>.", "no_year_html": "The second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Eckm%C3%BChl\" title=\"Battle of Eckmühl\">Battle of Eckmühl</a>: The <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> army is defeated by the <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">First French Empire</a> army led by <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> and driven over the <a href=\"https://wikipedia.org/wiki/Danube\" title=\"Danube\">Danube</a> in <a href=\"https://wikipedia.org/wiki/Regensburg\" title=\"Regensburg\">Regensburg</a>.", "links": [{"title": "Battle of Eckmühl", "link": "https://wikipedia.org/wiki/Battle_of_Eckm%C3%BChl"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Danube", "link": "https://wikipedia.org/wiki/Danube"}, {"title": "Regensburg", "link": "https://wikipedia.org/wiki/Regensburg"}]}, {"year": "1836", "text": "Texas Revolution: A day after the Battle of San Jacinto, forces under Texas General <PERSON> identify Mexican General <PERSON> among the captives of the battle when some of his fellow soldiers mistakenly give away his identity.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Texas_Revolution\" title=\"Texas Revolution\">Texas Revolution</a>: A day after the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Jacinto\" title=\"Battle of San Jacinto\">Battle of San Jacinto</a>, forces under <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Texas</a> General <a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a> identify Mexican General <a href=\"https://wikipedia.org/wiki/Antonio_L%C3%B3pez_de_Santa_Anna\" title=\"Antonio López de Santa Anna\"><PERSON></a> among the captives of the battle when some of his fellow soldiers mistakenly give away his identity.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_Revolution\" title=\"Texas Revolution\">Texas Revolution</a>: A day after the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Jacinto\" title=\"Battle of San Jacinto\">Battle of San Jacinto</a>, forces under <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Texas</a> General <a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a> identify Mexican General <a href=\"https://wikipedia.org/wiki/Antonio_L%C3%B3pez_de_Santa_Anna\" title=\"Antonio López de Santa Anna\"><PERSON></a> among the captives of the battle when some of his fellow soldiers mistakenly give away his identity.", "links": [{"title": "Texas Revolution", "link": "https://wikipedia.org/wiki/Texas_Revolution"}, {"title": "Battle of San Jacinto", "link": "https://wikipedia.org/wiki/Battle_of_San_Jacinto"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Houston"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_L%C3%B3<PERSON><PERSON>_<PERSON>_Santa_Anna"}]}, {"year": "1864", "text": "The U.S. Congress passes the Coinage Act of 1864 that permitted the inscription In God We Trust be placed on all coins minted as United States currency.", "html": "1864 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Coinage_Act_of_1864\" title=\"Coinage Act of 1864\">Coinage Act of 1864</a> that permitted the inscription <i>In God We Trust</i> be placed on all coins minted as <a href=\"https://wikipedia.org/wiki/United_States_currency\" class=\"mw-redirect\" title=\"United States currency\">United States currency</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Coinage_Act_of_1864\" title=\"Coinage Act of 1864\">Coinage Act of 1864</a> that permitted the inscription <i>In God We Trust</i> be placed on all coins minted as <a href=\"https://wikipedia.org/wiki/United_States_currency\" class=\"mw-redirect\" title=\"United States currency\">United States currency</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Coinage Act of 1864", "link": "https://wikipedia.org/wiki/Coinage_Act_of_1864"}, {"title": "United States currency", "link": "https://wikipedia.org/wiki/United_States_currency"}]}, {"year": "1876", "text": "The first National League baseball game is played at the Jefferson Street Grounds in Philadelphia.", "html": "1876 - The first <a href=\"https://wikipedia.org/wiki/National_League_(baseball)\" title=\"National League (baseball)\">National League</a> baseball game is played at the <a href=\"https://wikipedia.org/wiki/Jefferson_Street_Grounds\" title=\"Jefferson Street Grounds\">Jefferson Street Grounds</a> in Philadelphia.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/National_League_(baseball)\" title=\"National League (baseball)\">National League</a> baseball game is played at the <a href=\"https://wikipedia.org/wiki/Jefferson_Street_Grounds\" title=\"Jefferson Street Grounds\">Jefferson Street Grounds</a> in Philadelphia.", "links": [{"title": "National League (baseball)", "link": "https://wikipedia.org/wiki/National_League_(baseball)"}, {"title": "Jefferson Street Grounds", "link": "https://wikipedia.org/wiki/Jefferson_Street_Grounds"}]}, {"year": "1889", "text": "At noon, thousands rush to claim land in the Land Rush of 1889. Within hours the cities of Oklahoma City and Guthrie are formed with populations of at least 10,000.", "html": "1889 - At noon, thousands rush to claim land in the <a href=\"https://wikipedia.org/wiki/Land_Rush_of_1889\" title=\"Land Rush of 1889\">Land Rush of 1889</a>. Within hours the cities of <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a> and <a href=\"https://wikipedia.org/wiki/Guthrie,_Oklahoma\" title=\"Guthrie, Oklahoma\">Guthrie</a> are formed with populations of at least 10,000.", "no_year_html": "At noon, thousands rush to claim land in the <a href=\"https://wikipedia.org/wiki/Land_Rush_of_1889\" title=\"Land Rush of 1889\">Land Rush of 1889</a>. Within hours the cities of <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a> and <a href=\"https://wikipedia.org/wiki/Guthrie,_Oklahoma\" title=\"Guthrie, Oklahoma\">Guthrie</a> are formed with populations of at least 10,000.", "links": [{"title": "Land Rush of 1889", "link": "https://wikipedia.org/wiki/Land_Rush_of_1889"}, {"title": "Oklahoma City", "link": "https://wikipedia.org/wiki/Oklahoma_City"}, {"title": "Guthrie, Oklahoma", "link": "https://wikipedia.org/wiki/Guthrie,_Oklahoma"}]}, {"year": "1898", "text": "Spanish-American War: President <PERSON> calls for 125,000 volunteers to join the National Guard and fight in Cuba, while Congress more than doubles regular Army forces to 65,000.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> calls for 125,000 volunteers to join the <a href=\"https://wikipedia.org/wiki/National_Guard_(United_States)\" title=\"National Guard (United States)\">National Guard</a> and fight in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, while <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> more than doubles <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">regular Army</a> forces to 65,000.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> calls for 125,000 volunteers to join the <a href=\"https://wikipedia.org/wiki/National_Guard_(United_States)\" title=\"National Guard (United States)\">National Guard</a> and fight in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, while <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> more than doubles <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">regular Army</a> forces to 65,000.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Guard (United States)", "link": "https://wikipedia.org/wiki/National_Guard_(United_States)"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}]}, {"year": "1906", "text": "The 1906 Intercalated Games open in Athens.", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/1906_Intercalated_Games\" title=\"1906 Intercalated Games\">1906 Intercalated Games</a> open in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1906_Intercalated_Games\" title=\"1906 Intercalated Games\">1906 Intercalated Games</a> open in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>.", "links": [{"title": "1906 Intercalated Games", "link": "https://wikipedia.org/wiki/1906_Intercalated_Games"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}]}, {"year": "1915", "text": "World War I: The use of poison gas in World War I escalates when chlorine gas is released as a chemical weapon in the Second Battle of Ypres.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The use of <a href=\"https://wikipedia.org/wiki/Poison_gas_in_World_War_I\" class=\"mw-redirect\" title=\"Poison gas in World War I\">poison gas in World War I</a> escalates when <a href=\"https://wikipedia.org/wiki/Chlorine\" title=\"Chlorine\">chlorine gas</a> is released as a <a href=\"https://wikipedia.org/wiki/Chemical_warfare\" title=\"Chemical warfare\">chemical weapon</a> in the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Ypres\" title=\"Second Battle of Ypres\">Second Battle of Ypres</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The use of <a href=\"https://wikipedia.org/wiki/Poison_gas_in_World_War_I\" class=\"mw-redirect\" title=\"Poison gas in World War I\">poison gas in World War I</a> escalates when <a href=\"https://wikipedia.org/wiki/Chlorine\" title=\"Chlorine\">chlorine gas</a> is released as a <a href=\"https://wikipedia.org/wiki/Chemical_warfare\" title=\"Chemical warfare\">chemical weapon</a> in the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Ypres\" title=\"Second Battle of Ypres\">Second Battle of Ypres</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Poison gas in World War I", "link": "https://wikipedia.org/wiki/Poison_gas_in_World_War_I"}, {"title": "Chlorine", "link": "https://wikipedia.org/wiki/Chlorine"}, {"title": "Chemical warfare", "link": "https://wikipedia.org/wiki/Chemical_warfare"}, {"title": "Second Battle of Ypres", "link": "https://wikipedia.org/wiki/Second_Battle_of_Ypres"}]}, {"year": "1930", "text": "The United Kingdom, Japan and the United States sign the London Naval Treaty regulating submarine warfare and limiting shipbuilding.", "html": "1930 - The United Kingdom, Japan and the United States sign the <a href=\"https://wikipedia.org/wiki/London_Naval_Treaty\" title=\"London Naval Treaty\">London Naval Treaty</a> regulating <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> warfare and limiting <a href=\"https://wikipedia.org/wiki/Shipbuilding\" title=\"Shipbuilding\">shipbuilding</a>.", "no_year_html": "The United Kingdom, Japan and the United States sign the <a href=\"https://wikipedia.org/wiki/London_Naval_Treaty\" title=\"London Naval Treaty\">London Naval Treaty</a> regulating <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> warfare and limiting <a href=\"https://wikipedia.org/wiki/Shipbuilding\" title=\"Shipbuilding\">shipbuilding</a>.", "links": [{"title": "London Naval Treaty", "link": "https://wikipedia.org/wiki/London_Naval_Treaty"}, {"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}, {"title": "Shipbuilding", "link": "https://wikipedia.org/wiki/Shipbuilding"}]}, {"year": "1944", "text": "The 1st Air Commando Group using Sikorsky R-4 helicopters stage the first use of helicopters in combat with combat search and rescue operations in the China Burma India Theater.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/1st_Air_Commando_Group\" class=\"mw-redirect\" title=\"1st Air Commando Group\">1st Air Commando Group</a> using <a href=\"https://wikipedia.org/wiki/Sikorsky_R-4\" title=\"Sikorsky R-4\">Sikorsky R-4</a> helicopters stage the first use of helicopters in <a href=\"https://wikipedia.org/wiki/Military_helicopter\" title=\"Military helicopter\">combat</a> with <a href=\"https://wikipedia.org/wiki/Combat_search_and_rescue\" title=\"Combat search and rescue\">combat search and rescue</a> operations in the <a href=\"https://wikipedia.org/wiki/China_Burma_India_Theater\" class=\"mw-redirect\" title=\"China Burma India Theater\">China Burma India Theater</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1st_Air_Commando_Group\" class=\"mw-redirect\" title=\"1st Air Commando Group\">1st Air Commando Group</a> using <a href=\"https://wikipedia.org/wiki/Sikorsky_R-4\" title=\"Sikorsky R-4\">Sikorsky R-4</a> helicopters stage the first use of helicopters in <a href=\"https://wikipedia.org/wiki/Military_helicopter\" title=\"Military helicopter\">combat</a> with <a href=\"https://wikipedia.org/wiki/Combat_search_and_rescue\" title=\"Combat search and rescue\">combat search and rescue</a> operations in the <a href=\"https://wikipedia.org/wiki/China_Burma_India_Theater\" class=\"mw-redirect\" title=\"China Burma India Theater\">China Burma India Theater</a>.", "links": [{"title": "1st Air Commando Group", "link": "https://wikipedia.org/wiki/1st_Air_Commando_Group"}, {"title": "Sikorsky R-4", "link": "https://wikipedia.org/wiki/Sikorsky_R-4"}, {"title": "Military helicopter", "link": "https://wikipedia.org/wiki/Military_helicopter"}, {"title": "Combat search and rescue", "link": "https://wikipedia.org/wiki/Combat_search_and_rescue"}, {"title": "China Burma India Theater", "link": "https://wikipedia.org/wiki/China_Burma_India_Theater"}]}, {"year": "1944", "text": "World War II: Operation Persecution is initiated: Allied forces land in the Hollandia (currently known as Jayapura) area of New Guinea.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Persecution\" class=\"mw-redirect\" title=\"Operation Persecution\">Operation Persecution</a> is initiated: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces land in the <a href=\"https://wikipedia.org/wiki/Jayapura\" title=\"Jayapura\">Hollandia</a> (currently known as Jayapura) area of <a href=\"https://wikipedia.org/wiki/New_Guinea\" title=\"New Guinea\">New Guinea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Persecution\" class=\"mw-redirect\" title=\"Operation Persecution\">Operation Persecution</a> is initiated: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces land in the <a href=\"https://wikipedia.org/wiki/Jayapura\" title=\"Jayapura\">Hollandia</a> (currently known as Jayapura) area of <a href=\"https://wikipedia.org/wiki/New_Guinea\" title=\"New Guinea\">New Guinea</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Persecution", "link": "https://wikipedia.org/wiki/Operation_Persecution"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Jayapura", "link": "https://wikipedia.org/wiki/Jayapura"}, {"title": "New Guinea", "link": "https://wikipedia.org/wiki/New_Guinea"}]}, {"year": "1944", "text": "World War II: In Greenland, the Allied Sledge Patrol attack the German Bassgeiger weather station.", "html": "1944 - World War II: In <a href=\"https://wikipedia.org/wiki/Greenland_in_World_War_II\" title=\"Greenland in World War II\">Greenland</a>, the Allied Sledge Patrol attack the German <i>Bassgeiger</i> <a href=\"https://wikipedia.org/wiki/North_Atlantic_weather_war\" title=\"North Atlantic weather war\">weather station</a>.", "no_year_html": "World War II: In <a href=\"https://wikipedia.org/wiki/Greenland_in_World_War_II\" title=\"Greenland in World War II\">Greenland</a>, the Allied Sledge Patrol attack the German <i>Bassgeiger</i> <a href=\"https://wikipedia.org/wiki/North_Atlantic_weather_war\" title=\"North Atlantic weather war\">weather station</a>.", "links": [{"title": "Greenland in World War II", "link": "https://wikipedia.org/wiki/Greenland_in_World_War_II"}, {"title": "North Atlantic weather war", "link": "https://wikipedia.org/wiki/North_Atlantic_weather_war"}]}, {"year": "1945", "text": "World War II: Prisoners at the Jasenovac concentration camp revolt. Five hundred twenty are killed and around eighty escape.", "html": "1945 - World War II: Prisoners at the <a href=\"https://wikipedia.org/wiki/Jasenovac_concentration_camp\" title=\"Jasenovac concentration camp\">Jasenovac concentration camp</a> revolt. Five hundred twenty are killed and around eighty escape.", "no_year_html": "World War II: Prisoners at the <a href=\"https://wikipedia.org/wiki/Jasenovac_concentration_camp\" title=\"Jasenovac concentration camp\">Jasenovac concentration camp</a> revolt. Five hundred twenty are killed and around eighty escape.", "links": [{"title": "Jasenovac concentration camp", "link": "https://wikipedia.org/wiki/Jasenovac_concentration_camp"}]}, {"year": "1945", "text": "World War II: Sachsenhausen concentration camp is liberated by soldiers of the Red Army and Polish First Army.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Sachsenhausen_concentration_camp\" title=\"Sachsenhausen concentration camp\">Sachsenhausen concentration camp</a> is liberated by soldiers of the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> and <a href=\"https://wikipedia.org/wiki/Polish_First_Army\" class=\"mw-redirect\" title=\"Polish First Army\">Polish First Army</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Sachsenhausen_concentration_camp\" title=\"Sachsenhausen concentration camp\">Sachsenhausen concentration camp</a> is liberated by soldiers of the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> and <a href=\"https://wikipedia.org/wiki/Polish_First_Army\" class=\"mw-redirect\" title=\"Polish First Army\">Polish First Army</a>.", "links": [{"title": "Sachsenhausen concentration camp", "link": "https://wikipedia.org/wiki/Sachsenhausen_concentration_camp"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Polish First Army", "link": "https://wikipedia.org/wiki/Polish_First_Army"}]}, {"year": "1948", "text": "Arab-Israeli War: The port city of Haifa is captured by Jewish forces.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">Arab-Israeli War</a>: The port city of <a href=\"https://wikipedia.org/wiki/Haifa\" title=\"Haifa\">Haifa</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_Haifa_(1948)\" title=\"Battle of Haifa (1948)\">captured by Jewish forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">Arab-Israeli War</a>: The port city of <a href=\"https://wikipedia.org/wiki/Haifa\" title=\"Haifa\">Haifa</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_Haifa_(1948)\" title=\"Battle of Haifa (1948)\">captured by Jewish forces</a>.", "links": [{"title": "1948 Arab-Israeli War", "link": "https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War"}, {"title": "Haifa", "link": "https://wikipedia.org/wiki/Haifa"}, {"title": "Battle of Haifa (1948)", "link": "https://wikipedia.org/wiki/Battle_of_Haifa_(1948)"}]}, {"year": "1951", "text": "Korean War: The Chinese People's Volunteer Army begin assaulting positions defended by the Royal Australian Regiment and the Princess <PERSON>'s Canadian Light Infantry at the Battle of Kapyong.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: The Chinese <a href=\"https://wikipedia.org/wiki/People%27s_Volunteer_Army\" title=\"People's Volunteer Army\">People's Volunteer Army</a> begin assaulting positions defended by the <a href=\"https://wikipedia.org/wiki/Royal_Australian_Regiment\" title=\"Royal Australian Regiment\">Royal Australian Regiment</a> and the <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>%27s_Canadian_Light_Infantry\" title=\"Princess <PERSON>'s Canadian Light Infantry\">Princess <PERSON>'s Canadian Light Infantry</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kapyong\" title=\"Battle of Kapyong\">Battle of Kapyong</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: The Chinese <a href=\"https://wikipedia.org/wiki/People%27s_Volunteer_Army\" title=\"People's Volunteer Army\">People's Volunteer Army</a> begin assaulting positions defended by the <a href=\"https://wikipedia.org/wiki/Royal_Australian_Regiment\" title=\"Royal Australian Regiment\">Royal Australian Regiment</a> and the <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>%27s_Canadian_Light_Infantry\" title=\"Princess <PERSON>'s Canadian Light Infantry\">Princess <PERSON>'s Canadian Light Infantry</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kapyong\" title=\"Battle of Kapyong\">Battle of Kapyong</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "People's Volunteer Army", "link": "https://wikipedia.org/wiki/People%27s_Volunteer_Army"}, {"title": "Royal Australian Regiment", "link": "https://wikipedia.org/wiki/Royal_Australian_Regiment"}, {"title": "Princess <PERSON>'s Canadian Light Infantry", "link": "https://wikipedia.org/wiki/Princess_<PERSON>%27s_Canadian_Light_Infantry"}, {"title": "Battle of Kapyong", "link": "https://wikipedia.org/wiki/Battle_of_Kapyong"}]}, {"year": "1954", "text": "<PERSON>: Witnesses begin testifying and live television coverage of the Army-McCarthy hearings begins.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/McCarthyism\" title=\"McCarthyism\"><PERSON></a>: Witnesses begin testifying and live television coverage of the <a href=\"https://wikipedia.org/wiki/Army%E2%80%93McCarthy_hearings\" title=\"Army-McCarthy hearings\">Army-McCarthy hearings</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/McCarthyism\" title=\"McCarthyism\"><PERSON></a>: Witnesses begin testifying and live television coverage of the <a href=\"https://wikipedia.org/wiki/Army%E2%80%93McCarthy_hearings\" title=\"Army-McCarthy hearings\">Army-McCarthy hearings</a> begins.", "links": [{"title": "McCarthyism", "link": "https://wikipedia.org/wiki/McCarthyism"}, {"title": "Army-McCarthy hearings", "link": "https://wikipedia.org/wiki/Army%E2%80%93Mc<PERSON><PERSON><PERSON>_hearings"}]}, {"year": "1966", "text": "American Flyers Airline Flight 280/D crashes on approach to Ardmore Municipal Airport in Ardmore, Oklahoma, killing 83.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/American_Flyers_Airline_Flight_280/D\" title=\"American Flyers Airline Flight 280/D\">American Flyers Airline Flight 280/D</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Ardmore_Municipal_Airport\" title=\"Ardmore Municipal Airport\">Ardmore Municipal Airport</a> in <a href=\"https://wikipedia.org/wiki/Ardmore,_Oklahoma\" title=\"Ardmore, Oklahoma\">Ardmore, Oklahoma</a>, killing 83.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Flyers_Airline_Flight_280/D\" title=\"American Flyers Airline Flight 280/D\">American Flyers Airline Flight 280/D</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Ardmore_Municipal_Airport\" title=\"Ardmore Municipal Airport\">Ardmore Municipal Airport</a> in <a href=\"https://wikipedia.org/wiki/Ardmore,_Oklahoma\" title=\"Ardmore, Oklahoma\">Ardmore, Oklahoma</a>, killing 83.", "links": [{"title": "American Flyers Airline Flight 280/D", "link": "https://wikipedia.org/wiki/American_Flyers_Airline_Flight_280/D"}, {"title": "Ardmore Municipal Airport", "link": "https://wikipedia.org/wiki/Ardmore_Municipal_Airport"}, {"title": "Ardmore, Oklahoma", "link": "https://wikipedia.org/wiki/Ardmore,_Oklahoma"}]}, {"year": "1969", "text": "British yachtsman Sir <PERSON> wins the Sunday Times Golden Globe Race and completes the first solo non-stop circumnavigation of the world.", "html": "1969 - British yachtsman Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/Sunday_Times_Golden_Globe_Race\" title=\"Sunday Times Golden Globe Race\"><i>Sunday Times</i> Golden Globe Race</a> and completes the first solo non-stop circumnavigation of the world.", "no_year_html": "British yachtsman Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/Sunday_Times_Golden_Globe_Race\" title=\"Sunday Times Golden Globe Race\"><i>Sunday Times</i> Golden Globe Race</a> and completes the first solo non-stop circumnavigation of the world.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sunday Times Golden Globe Race", "link": "https://wikipedia.org/wiki/Sunday_Times_Golden_Globe_Race"}]}, {"year": "1969", "text": "The formation of the Communist Party of India (Marxist-Leninist) is announced at a mass rally in Calcutta.", "html": "1969 - The formation of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_India_(Marxist%E2%80%93Leninist)\" title=\"Communist Party of India (Marxist-Leninist)\">Communist Party of India (Marxist-Leninist)</a> is announced at a mass rally in Calcutta.", "no_year_html": "The formation of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_India_(Marxist%E2%80%93Leninist)\" title=\"Communist Party of India (Marxist-Leninist)\">Communist Party of India (Marxist-Leninist)</a> is announced at a mass rally in Calcutta.", "links": [{"title": "Communist Party of India (Marxist-Leninist)", "link": "https://wikipedia.org/wiki/Communist_Party_of_India_(Marxist%E2%80%93Leninist)"}]}, {"year": "1970", "text": "The first Earth Day is celebrated.", "html": "1970 - The first <a href=\"https://wikipedia.org/wiki/Earth_Day\" title=\"Earth Day\">Earth Day</a> is celebrated.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Earth_Day\" title=\"Earth Day\">Earth Day</a> is celebrated.", "links": [{"title": "Earth Day", "link": "https://wikipedia.org/wiki/Earth_Day"}]}, {"year": "1974", "text": "Pan Am Flight 812 crashes on approach to Ngurah Rai International Airport in Denpasar, Bali, Indonesia, killing all 107 people on board.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_812\" title=\"Pan Am Flight 812\">Pan Am Flight 812</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Ngurah_Rai_International_Airport\" title=\"Ngurah Rai International Airport\">Ngurah Rai International Airport</a> in <a href=\"https://wikipedia.org/wiki/Denpasar\" title=\"Denpasar\">Denpasar</a>, <a href=\"https://wikipedia.org/wiki/Bali\" title=\"Bali\">Bali</a>, Indonesia, killing all 107 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_812\" title=\"Pan Am Flight 812\">Pan Am Flight 812</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Ngurah_Rai_International_Airport\" title=\"Ngurah Rai International Airport\">Ngurah Rai International Airport</a> in <a href=\"https://wikipedia.org/wiki/Denpasar\" title=\"Denpasar\">Denpasar</a>, <a href=\"https://wikipedia.org/wiki/Bali\" title=\"Bali\">Bali</a>, Indonesia, killing all 107 people on board.", "links": [{"title": "Pan Am Flight 812", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_812"}, {"title": "Ngurah Rai International Airport", "link": "https://wikipedia.org/wiki/Ngurah_Rai_International_Airport"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Denpasar"}, {"title": "Bali", "link": "https://wikipedia.org/wiki/Bali"}]}, {"year": "1977", "text": "Optical fiber is first used to carry live telephone traffic.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Optical_fiber\" title=\"Optical fiber\">Optical fiber</a> is first used to carry live telephone traffic.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Optical_fiber\" title=\"Optical fiber\">Optical fiber</a> is first used to carry live telephone traffic.", "links": [{"title": "Optical fiber", "link": "https://wikipedia.org/wiki/Optical_fiber"}]}, {"year": "1992", "text": "A series of gas explosions rip through the streets in Guadalajara, Mexico, killing 206.", "html": "1992 - A series of <a href=\"https://wikipedia.org/wiki/Gas_explosion\" title=\"Gas explosion\">gas explosions</a> <a href=\"https://wikipedia.org/wiki/1992_Guadalajara_explosions\" title=\"1992 Guadalajara explosions\">rip through the streets</a> in <a href=\"https://wikipedia.org/wiki/Guadalajara\" title=\"Guadalajara\">Guadalajara</a>, Mexico, killing 206.", "no_year_html": "A series of <a href=\"https://wikipedia.org/wiki/Gas_explosion\" title=\"Gas explosion\">gas explosions</a> <a href=\"https://wikipedia.org/wiki/1992_Guadalajara_explosions\" title=\"1992 Guadalajara explosions\">rip through the streets</a> in <a href=\"https://wikipedia.org/wiki/Guadalajara\" title=\"Guadalajara\">Guadalajara</a>, Mexico, killing 206.", "links": [{"title": "Gas explosion", "link": "https://wikipedia.org/wiki/Gas_explosion"}, {"title": "1992 Guadalajara explosions", "link": "https://wikipedia.org/wiki/1992_Guadalajara_explosions"}, {"title": "Guadalajara", "link": "https://wikipedia.org/wiki/Guadalajara"}]}, {"year": "1993", "text": "Eighteen-year-old <PERSON> is murdered in a racially motivated attack while waiting for a bus in Well Hall, Eltham.", "html": "1993 - Eighteen-year-old <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\"><PERSON></a> is murdered in a racially motivated attack while waiting for a bus in <a href=\"https://wikipedia.org/wiki/Well_Hall\" title=\"Well Hall\">Well Hall</a>, <a href=\"https://wikipedia.org/wiki/Eltham\" title=\"Eltham\">El<PERSON></a>.", "no_year_html": "Eighteen-year-old <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\"><PERSON></a> is murdered in a racially motivated attack while waiting for a bus in <a href=\"https://wikipedia.org/wiki/Well_Hall\" title=\"Well Hall\">Well Hall</a>, <a href=\"https://wikipedia.org/wiki/Eltham\" title=\"Eltham\">Eltham</a>.", "links": [{"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}, {"title": "Well Hall", "link": "https://wikipedia.org/wiki/Well_Hall"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eltham"}]}, {"year": "2005", "text": "Japan's Prime Minister <PERSON><PERSON><PERSON> apologizes for Japan's war record.", "html": "2005 - Japan's Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/List_of_war_apology_statements_issued_by_Japan#2000s\" title=\"List of war apology statements issued by Japan\">apologizes</a> for Japan's war record.", "no_year_html": "Japan's Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/List_of_war_apology_statements_issued_by_Japan#2000s\" title=\"List of war apology statements issued by Japan\">apologizes</a> for Japan's war record.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of war apology statements issued by Japan", "link": "https://wikipedia.org/wiki/List_of_war_apology_statements_issued_by_Japan#2000s"}]}, {"year": "2016", "text": "The Paris Agreement is signed, an agreement to help fight global warming.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/Paris_Agreement\" title=\"Paris Agreement\">Paris Agreement</a> is signed, an agreement to help fight global warming.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Paris_Agreement\" title=\"Paris Agreement\">Paris Agreement</a> is signed, an agreement to help fight global warming.", "links": [{"title": "Paris Agreement", "link": "https://wikipedia.org/wiki/Paris_Agreement"}]}, {"year": "2020", "text": "Four police officers are killed after being struck by a truck on the Eastern Freeway in Melbourne while speaking to a speeding driver, marking the largest loss of police lives in Victoria Police history.", "html": "2020 - Four police officers are <a href=\"https://wikipedia.org/wiki/2020_Eastern_Freeway_truck_crash\" title=\"2020 Eastern Freeway truck crash\">killed after being struck by a truck</a> on the <a href=\"https://wikipedia.org/wiki/Eastern_Freeway_(Melbourne)\" title=\"Eastern Freeway (Melbourne)\">Eastern Freeway</a> in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a> while speaking to a speeding driver, marking the largest loss of police lives in <a href=\"https://wikipedia.org/wiki/Victoria_Police\" title=\"Victoria Police\">Victoria Police</a> history.", "no_year_html": "Four police officers are <a href=\"https://wikipedia.org/wiki/2020_Eastern_Freeway_truck_crash\" title=\"2020 Eastern Freeway truck crash\">killed after being struck by a truck</a> on the <a href=\"https://wikipedia.org/wiki/Eastern_Freeway_(Melbourne)\" title=\"Eastern Freeway (Melbourne)\">Eastern Freeway</a> in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a> while speaking to a speeding driver, marking the largest loss of police lives in <a href=\"https://wikipedia.org/wiki/Victoria_Police\" title=\"Victoria Police\">Victoria Police</a> history.", "links": [{"title": "2020 Eastern Freeway truck crash", "link": "https://wikipedia.org/wiki/2020_Eastern_Freeway_truck_crash"}, {"title": "Eastern Freeway (Melbourne)", "link": "https://wikipedia.org/wiki/Eastern_Freeway_(Melbourne)"}, {"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}, {"title": "Victoria Police", "link": "https://wikipedia.org/wiki/Victoria_Police"}]}], "Births": [{"year": "1412", "text": "<PERSON><PERSON><PERSON>, Count of Hanau (1451-1452) (d. 1452)", "html": "1412 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Hanau\" title=\"<PERSON><PERSON><PERSON> III, Count of Hanau\"><PERSON><PERSON><PERSON>, Count of Hanau</a> (1451-1452) (d. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Hanau\" title=\"<PERSON><PERSON><PERSON> III, Count of Hanau\"><PERSON><PERSON><PERSON> III, Count of Hanau</a> (1451-1452) (d. 1452)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Hanau", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1444", "text": "<PERSON> York, Duchess of Suffolk (d. 1503)", "html": "1444 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_York,_Duchess_of_Suffolk\" title=\"<PERSON> York, Duchess of Suffolk\"><PERSON> York, Duchess of Suffolk</a> (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_York,_Duchess_of_Suffolk\" title=\"<PERSON> York, Duchess of Suffolk\"><PERSON> York, Duchess of Suffolk</a> (d. 1503)", "links": [{"title": "<PERSON>, Duchess of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_York,_Duchess_of_Suffolk"}]}, {"year": "1451", "text": "<PERSON> of Castile (d. 1504)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> (d. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> (d. 1504)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}]}, {"year": "1518", "text": "<PERSON> of Navarre (d. 1562)", "html": "1518 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (d. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (d. 1562)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/Antoine_of_Navarre"}]}, {"year": "1592", "text": "<PERSON>, German astronomer and mathematician (d. 1635)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and mathematician (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and mathematician (d. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON> (d. 1691)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Alexander <PERSON>\">Pope <PERSON> VIII</a> (d. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Alexander <PERSON>\">Pope <PERSON> VIII</a> (d. 1691)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1658", "text": "<PERSON>, Italian violinist and composer (d. 1709)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON>, 2nd Earl <PERSON>, English politician, Lord President of the Council (d. 1763)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1763)", "links": [{"title": "<PERSON>, 2nd Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1707", "text": "<PERSON>, English novelist and playwright (d. 1754)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (d. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON>, Prince <PERSON>, Austrian soldier (d. 1762)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Prince_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>, Prince <PERSON>\"><PERSON>, Prince <PERSON></a>, Austrian soldier (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Prince_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>, Prince <PERSON>\"><PERSON>, Prince <PERSON></a>, Austrian soldier (d. 1762)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>%C3%A1zy"}]}, {"year": "1724", "text": "<PERSON><PERSON><PERSON><PERSON>, German anthropologist, philosopher, and academic (d. 1804)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/Immanu<PERSON>_Kant\" title=\"Immanu<PERSON> Kant\"><PERSON><PERSON><PERSON><PERSON></a>, German anthropologist, philosopher, and academic (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Immanu<PERSON>_Kant\" title=\"Immanu<PERSON> Kant\"><PERSON><PERSON><PERSON><PERSON></a>, German anthropologist, philosopher, and academic (d. 1804)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Immanu<PERSON>_Kant"}]}, {"year": "1732", "text": "<PERSON>, English architect and surveyor (d. 1814)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect,_born_1732)\" title=\"<PERSON> (architect, born 1732)\"><PERSON></a>, English architect and surveyor (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(architect,_born_1732)\" title=\"<PERSON> (architect, born 1732)\"><PERSON></a>, English architect and surveyor (d. 1814)", "links": [{"title": "<PERSON> (architect, born 1732)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect,_born_1732)"}]}, {"year": "1744", "text": "<PERSON>, American lawyer and politician, 7th Governor of Massachusetts (d. 1808)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1808)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1766", "text": "<PERSON><PERSON>, French author and political philosopher (d. 1817)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%ABl\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and political philosopher (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%ABl\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and political philosopher (d. 1817)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>a%C3%ABl"}]}, {"year": "1812", "text": "<PERSON>, Swiss-English orientalist (d. 1894)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Solomon <PERSON>\"><PERSON></a>, Swiss-English orientalist (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Solomon <PERSON>\"><PERSON></a>, Swiss-English orientalist (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON><PERSON><PERSON>, French general (d. 1897)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (d. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, British suffragist and educator, co-founder and an early Mistress of Girton College, Cambridge University (d. 1921)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British suffragist and educator, co-founder and an early Mistress of <a href=\"https://wikipedia.org/wiki/Girton_College,_Cambridge\" title=\"Girton College, Cambridge\">Girton College, Cambridge</a> University (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British suffragist and educator, co-founder and an early Mistress of <a href=\"https://wikipedia.org/wiki/Girton_College,_Cambridge\" title=\"Girton College, Cambridge\">Girton College, Cambridge</a> University (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Girton College, Cambridge", "link": "https://wikipedia.org/wiki/Girton_College,_Cambridge"}]}, {"year": "1832", "text": "<PERSON>, American journalist and politician, 3rd United States Secretary of Agriculture (d. 1902)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a> (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a> (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Morton"}, {"title": "United States Secretary of Agriculture", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture"}]}, {"year": "1844", "text": "<PERSON>, American soldier, attempted assassin of <PERSON> (d. 1865)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a>, American soldier, attempted assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a>, American soldier, attempted assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1865)", "links": [{"title": "<PERSON> (conspirator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Grand Duke of Luxembourg (d. 1912)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Luxembourg\" title=\"<PERSON>, Grand Duke of Luxembourg\"><PERSON>, Grand Duke of Luxembourg</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Luxembourg\" title=\"<PERSON>, Grand Duke of Luxembourg\"><PERSON>, Grand Duke of Luxembourg</a> (d. 1912)", "links": [{"title": "<PERSON>, Grand Duke of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Luxembourg"}]}, {"year": "1854", "text": "<PERSON>, Belgian lawyer and author, Nobel Prize laureate (d. 1943)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian lawyer and author, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian lawyer and author, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1858", "text": "<PERSON>, English composer (d. 1944)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Belgian art collector and art historian (d. 1901)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/1858\" title=\"1858\">1858</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian art collector and art historian (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1858\" title=\"1858\">1858</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian art collector and art historian (d. 1901)", "links": [{"title": "1858", "link": "https://wikipedia.org/wiki/1858"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Russian revolutionary and founder of Soviet Russia (d. 1924)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian revolutionary and founder of <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Soviet Russia</a> (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian revolutionary and founder of <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Soviet Russia</a> (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lenin"}, {"title": "Russian Soviet Federative Socialist Republic", "link": "https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic"}]}, {"year": "1872", "text": "Princess <PERSON> of Prussia (d. 1954)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Prussia\" title=\"Princess <PERSON> of Prussia\">Princess <PERSON> of Prussia</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Prussia\" title=\"Princess <PERSON> of Prussia\">Princess <PERSON> of Prussia</a> (d. 1954)", "links": [{"title": "Princess <PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_Prussia"}]}, {"year": "1873", "text": "<PERSON>, American author (d. 1945)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Ellen_<PERSON>\" title=\"Ellen <PERSON>\"><PERSON></a>, American author (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ellen <PERSON>\"><PERSON></a>, American author (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Chinese warlord, politician, and marshal of the Beiyang Army (d. 1939)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord, politician, and marshal of the <a href=\"https://wikipedia.org/wiki/Beiyang_Army\" title=\"Beiyang Army\">Beiyang Army</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord, politician, and marshal of the <a href=\"https://wikipedia.org/wiki/Beiyang_Army\" title=\"Beiyang Army\">Beiyang Army</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Beiyang Army", "link": "https://wikipedia.org/wiki/Beiyang_Army"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Austrian-Swedish otologist and physician, Nobel Prize laureate (d. 1936)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/R%C3%B3bert_B%C3%A1r%C3%A1ny\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-Swedish otologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%B3bert_B%C3%A1r%C3%A1ny\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-Swedish otologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3bert_B%C3%A1r%C3%A1ny"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1876", "text": "<PERSON>, Estonian wrestler and strongman (d. 1920)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler and strongman (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler and strongman (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Estonian-German chess player (d. 1939)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German chess player (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German chess player (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Austrian-American psychologist and academic (d. 1939)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Otto Rank\"><PERSON></a>, Austrian-American psychologist and academic (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Otto_<PERSON>\" title=\"Otto Rank\"><PERSON></a>, Austrian-American psychologist and academic (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rank"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Slovenian historian, author, and diplomat (d. 1958)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian historian, author, and diplomat (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian historian, author, and diplomat (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Danish mathematician and footballer (d. 1951)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and footballer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and footballer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, German SS officer (d. 1945)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCcks\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCcks\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_Gl%C3%BCcks"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1891", "text": "<PERSON>, American photographer (d. 1979)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Italian engineer (d. 1965)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian engineer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian engineer (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, English mathematician, geophysicist, and astronomer (d. 1989)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, geophysicist, and astronomer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, geophysicist, and astronomer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Italian-American anarchist (d. 1927)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON><PERSON><PERSON>\"><PERSON></a>, Italian-American anarchist (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON><PERSON>\"><PERSON></a>, Italian-American anarchist (d. 1927)", "links": [{"title": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, African-American minister and activist (d. 1965)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American minister and activist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American minister and activist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Russian-born novelist and critic (d. 1977)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born novelist and critic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born novelist and critic (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, British politician, Lord Mayor of Manchester (d. 1988)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Beer\"><PERSON><PERSON></a>, British politician, Lord Mayor of <a href=\"https://wikipedia.org/wiki/Manchester\" title=\"Manchester\">Manchester</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Beer\"><PERSON><PERSON></a>, British politician, Lord Mayor of <a href=\"https://wikipedia.org/wiki/Manchester\" title=\"Manchester\">Manchester</a> (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Manchester", "link": "https://wikipedia.org/wiki/Manchester"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON>, American physicist and academic (d. 1967)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and academic (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and academic (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American-Canadian author, poet, and diplomat (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian author, poet, and diplomat (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian author, poet, and diplomat (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English composer and educator (d. 1997)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON> <PERSON><PERSON><PERSON>, Duke of Västerbotten (d. 1947)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_V%C3%A4<PERSON>botten\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Västerbotten\">Prince <PERSON><PERSON><PERSON>, Duke of Västerbotten</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_V%C3%A4<PERSON>bot<PERSON>\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Västerbotten\">Prince <PERSON><PERSON><PERSON>, Duke of Västerbotten</a> (d. 1947)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>, Duke of Västerbotten", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_V%C3%A<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Italian neurologist and academic, Nobel Prize laureate (d. 2012)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian neurologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian neurologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Italian journalist and historian (d. 2001)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and historian (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and historian (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Greek politician, Prime Minister of Greece (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1910", "text": "<PERSON>, American mathematician and academic (d. 1971)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English operatic singer (d. 1953)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ferrier\"><PERSON></a>, English operatic singer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ferrier\"><PERSON></a>, English operatic singer (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rrier"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Japanese director, producer, and screenwriter (d. 2012)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Kaneto_Shindo\" title=\"Kaneto Shindo\"><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kaneto_Shindo\" title=\"Kaneto Shindo\"><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kaneto_Shindo"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Indian director and producer (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and producer (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Dutch-American author and playwright (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American author and playwright (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American author and playwright (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Peruvian soldier and pilot (d. 1941)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Qui%C3%B1ones_Gonzales\" title=\"<PERSON>\"><PERSON></a>, Peruvian soldier and pilot (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Qui%C3%B1<PERSON>_Gonzales\" title=\"<PERSON>\"><PERSON></a>, Peruvian soldier and pilot (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Qui%C3%B1<PERSON>_Gonzales"}]}, {"year": "1914", "text": "<PERSON>, German SS officer (d. 1944)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1916", "text": "<PERSON><PERSON>, German mathematician and academic (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, American-Swiss violinist and conductor (d. 1999)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Swiss violinist and conductor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Swiss violinist and conductor (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, French ballerina (d. 2016)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French ballerina (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French ballerina (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yvette_Chauvir%C3%A9"}]}, {"year": "1917", "text": "<PERSON>, Australian painter (d. 1992)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American poet and academic (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American baseball player and coach (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 2001)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1919", "text": "<PERSON>, Jr., American businessman and philanthropist (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman and philanthropist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman and philanthropist (d. 2011)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "1922", "text": "<PERSON>, American soldier and painter (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American bassist, composer, and bandleader (d. 1979)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist, composer, and bandleader (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist, composer, and bandleader (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American microbiologist and academic (d. 1973)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> V<PERSON> V<PERSON>\"><PERSON></a>, American microbiologist and academic (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> V<PERSON> V<PERSON>\"><PERSON></a>, American microbiologist and academic (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ish<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American soldier, pilot, and poet (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and poet (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and poet (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American model and actress (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, South Korean politician, 12th Prime Minister of South Korea (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Duck-woo\" title=\"<PERSON> Duck-woo\"><PERSON>woo</a>, South Korean politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Korea\" title=\"Prime Minister of South Korea\">Prime Minister of South Korea</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Duck-woo\" title=\"<PERSON>-woo\"><PERSON>woo</a>, South Korean politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Korea\" title=\"Prime Minister of South Korea\">Prime Minister of South Korea</a> (d. 2013)", "links": [{"title": "<PERSON>-woo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo"}, {"title": "Prime Minister of South Korea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Korea"}]}, {"year": "1926", "text": "<PERSON>, American actress and singer (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Scottish architect, designed the Staatsgalerie Stuttgart and Seeley Historical Library (d. 1992)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, Scottish architect, designed the <a href=\"https://wikipedia.org/wiki/Staatsgalerie_Stuttgart\" title=\"Staatsgalerie Stuttgart\">Staatsgalerie Stuttgart</a> and <a href=\"https://wikipedia.org/wiki/Seeley_Historical_Library\" title=\"Seeley Historical Library\">Seeley Historical Library</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, Scottish architect, designed the <a href=\"https://wikipedia.org/wiki/Staatsgalerie_Stuttgart\" title=\"Staatsgalerie Stuttgart\">Staatsgalerie Stuttgart</a> and <a href=\"https://wikipedia.org/wiki/Seeley_Historical_Library\" title=\"Seeley Historical Library\">Seeley Historical Library</a> (d. 1992)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>(architect)"}, {"title": "Staatsgalerie Stuttgart", "link": "https://wikipedia.org/wiki/Staatsgalerie_Stuttgart"}, {"title": "Seeley Historical Library", "link": "https://wikipedia.org/wiki/Seeley_Historical_Library"}]}, {"year": "1927", "text": "<PERSON>, Cuban-Jamaican singer (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-Jamaican singer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-Jamaican singer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Laurel_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, American actress and comedian (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English-Lebanese mathematician and academic (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Lebanese mathematician and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Lebanese mathematician and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English diplomat, British High Commissioner to India (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_India\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to India\">British High Commissioner to India</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_India\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to India\">British High Commissioner to India</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of High Commissioners of the United Kingdom to India", "link": "https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_India"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Estonian politician, Prime Minister of Estonia in exile (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Estonian_government-in-exile\" title=\"Estonian government-in-exile\">Prime Minister of Estonia in exile</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Estonian_government-in-exile\" title=\"Estonian government-in-exile\">Prime Minister of Estonia in exile</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enno_Penno"}, {"title": "Estonian government-in-exile", "link": "https://wikipedia.org/wiki/Estonian_government-in-exile"}]}, {"year": "1931", "text": "<PERSON>, Canadian lawyer and politician, 20th Premier of Nova Scotia (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 2019)", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)"}, {"title": "Premier of Nova Scotia", "link": "https://wikipedia.org/wiki/Premier_of_Nova_Scotia"}]}, {"year": "1931", "text": "<PERSON>, English dancer and choreographer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dancer and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Welsh-American chemist and astronaut (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American chemist and astronaut (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American chemist and astronaut (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English linguist and academic", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, English linguist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, English linguist and academic", "links": [{"title": "<PERSON> (linguist)", "link": "https://wikipedia.org/wiki/<PERSON>(linguist)"}]}, {"year": "1935", "text": "<PERSON>, African-American bassist and composer (d. 1969)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American bassist and composer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American bassist and composer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Indian-American mathematician and academic", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American mathematician and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>an"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (d. 2017)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Canadian pianist and conductor (d. 1998)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tu\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and conductor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tu\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and conductor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_H%C3%A9tu"}]}, {"year": "1937", "text": "<PERSON>, American actor and producer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter, pianist, and conductor (d. 2000)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and conductor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and conductor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English-Australian businessman (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian businessman (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian businessman (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Nigerian lawyer and activist (d. 2009)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and activist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and activist (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Japanese fashion designer (d. 2022)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese fashion designer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese fashion designer (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English journalist and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adam Raphael\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Raphael\" title=\"Adam Raphael\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer and actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English general and politician, Lieutenant Governor of Guernsey", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_general)\" class=\"mw-redirect\" title=\"<PERSON> (British Army general)\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey\" title=\"Lieutenant Governor of Guernsey\">Lieutenant Governor of Guernsey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_general)\" class=\"mw-redirect\" title=\"<PERSON> (British Army general)\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey\" title=\"Lieutenant Governor of Guernsey\">Lieutenant Governor of Guernsey</a>", "links": [{"title": "<PERSON> (British Army general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_general)"}, {"title": "Lieutenant Governor of Guernsey", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey"}]}, {"year": "1939", "text": "<PERSON>, Canadian journalist and author (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(humorist)\" title=\"<PERSON> (humorist)\"><PERSON></a>, Canadian journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(humorist)\" title=\"<PERSON> (humorist)\"><PERSON></a>, Canadian journalist and author (d. 2013)", "links": [{"title": "<PERSON> (humorist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(humorist)"}]}, {"year": "1939", "text": "<PERSON>, American actor and playwright (d. 2001)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, American actor and playwright (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, American actor and playwright (d. 2001)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)"}]}, {"year": "1939", "text": "<PERSON>, German lawyer and politician, German Federal Minister of Finance", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Finance_(Germany)\" title=\"Federal Ministry of Finance (Germany)\">German Federal Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Finance_(Germany)\" title=\"Federal Ministry of Finance (Germany)\">German Federal Minister of Finance</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Finance (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Finance_(Germany)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Baron <PERSON> of Rising, English politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> Rising\"><PERSON><PERSON><PERSON>, Baron <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON><PERSON>, Baron <PERSON></a>, English politician", "links": [{"title": "<PERSON><PERSON><PERSON>, Baron <PERSON> of Rising", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Rising"}]}, {"year": "1942", "text": "<PERSON>, Italian philosopher and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English politician, Lord Lieutenant of Bristol", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Bristol\" title=\"Lord Lieutenant of Bristol\">Lord Lieutenant of Bristol</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Bristol\" title=\"Lord Lieutenant of Bristol\">Lord Lieutenant of Bristol</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Bristol", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Bristol"}]}, {"year": "1943", "text": "<PERSON>, American businessman and politician (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American poet (d. 2023)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCck\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCck\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gl%C3%BCck"}]}, {"year": "1943", "text": "<PERSON>, <PERSON>, English lawyer and politician, Shadow Secretary of State for Defence (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a> (d. 2012)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Shadow Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence"}]}, {"year": "1943", "text": "<PERSON>, American mathematician and professor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and professor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and professor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American businessman, pilot, and sailor (d. 2007)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, pilot, and sailor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, pilot, and sailor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian ice hockey player (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American conductor and musicologist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and musicologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and musicologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian civil servant and politician, 22nd Governor of West Bengal", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian civil servant and politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor_of_West_Bengal\" class=\"mw-redirect\" title=\"Governor of West Bengal\">Governor of West Bengal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian civil servant and politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor_of_West_Bengal\" class=\"mw-redirect\" title=\"Governor of West Bengal\">Governor of West Bengal</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of West Bengal", "link": "https://wikipedia.org/wiki/Governor_of_West_Bengal"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Greek-Egyptian singer-songwriter (d. 1979)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Demetrio_Stratos\" title=\"Demetrio Stratos\"><PERSON><PERSON><PERSON></a>, Greek-Egyptian singer-songwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Demet<PERSON>_Stratos\" title=\"Demetrio Stratos\"><PERSON><PERSON><PERSON></a>, Greek-Egyptian singer-songwriter (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetrio_Stratos"}]}, {"year": "1946", "text": "<PERSON>, American captain and pilot, Medal of Honor recipient (d. 1972)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1946", "text": "<PERSON>, English physicist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian lawyer and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Baron <PERSON> of Kirkhope, Scottish lawyer and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Kirkhope\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baron <PERSON> of Kirkhope\"><PERSON><PERSON>, Baron <PERSON> of Kirkhope</a>, Scottish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Kirkhope\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baron <PERSON> of Kirkhope\"><PERSON><PERSON>, Baron <PERSON> of Kirkhope</a>, Scottish lawyer and politician", "links": [{"title": "<PERSON><PERSON>, Baron <PERSON> of Kirkhope", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Kirkhope"}]}, {"year": "1946", "text": "<PERSON>, Baron <PERSON> of Brentford, English economist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Brentford\" title=\"<PERSON>, Baron <PERSON> of Brentford\"><PERSON>, Baron <PERSON> of Brentford</a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Brentford\" title=\"<PERSON>, Baron <PERSON> of Brentford\"><PERSON>, Baron <PERSON> of Brentford</a>, English economist and academic", "links": [{"title": "<PERSON>, Baron <PERSON> of Brentford", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Brentford"}]}, {"year": "1946", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English bishop", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1949", "text": "<PERSON>, American basketball player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, English journalist and critic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and critic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Latvian organist, composer, and pianist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kal%C4%93js\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian organist, composer, and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kal%C4%93js\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian organist, composer, and pianist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kal%C4%93js"}]}, {"year": "1951", "text": "<PERSON>, Argentinian author and poet", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Ana_Mar%C3%ADa_Shu<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana_Mar%C3%ADa_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_Mar%C3%<PERSON><PERSON>_Shua"}]}, {"year": "1957", "text": "<PERSON>, Polish journalist and politician, 14th Prime Minister of Poland", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish journalist and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish journalist and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1959", "text": "<PERSON>, American baseball player and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American-Canadian actor and comedian", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Estonian historian and politician, 9th Prime Minister of Estonia", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian historian and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian historian and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart_Laar"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1961", "text": "<PERSON>, American football player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Estonian composer (d. 1996)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, British video game designer and programmer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British video game designer and programmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British video game designer and programmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Dani%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dani%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dani%C3%A8le_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, English sociologist and academic", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sociologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sociologist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English comedian and actor (d. 2021)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English physicist, engineer, and academic (d. 2016)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, engineer, and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, engineer, and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American actress, comedian, and television personality", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, comedian, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, comedian, and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Filipino singer and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Velasquez\" title=\"<PERSON>ine Velasquez\"><PERSON><PERSON></a>, Filipino singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_V<PERSON>z\" title=\"Regine Vela<PERSON>z\"><PERSON><PERSON></a>, Filipino singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Regine_Velasquez"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Kenyan runner and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Hungarian international footballer and manager", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Gera\" title=\"<PERSON><PERSON><PERSON> G<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian international footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Gera\" title=\"<PERSON><PERSON><PERSON> Gera\"><PERSON><PERSON><PERSON></a>, Hungarian international footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Gera"}]}, {"year": "1979", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Kenyan-Australian activist, engineer, and politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Australian activist, engineer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Australian activist, engineer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Quincy_Timberlake"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Kak%C3%A1\" title=\"Kak<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kak%C3%A1\" title=\"Kak<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "Kak<PERSON>", "link": "https://wikipedia.org/wiki/Kak%C3%A1"}]}, {"year": "1983", "text": "<PERSON>, English-American entomologist and palaeontologist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_W._Heads\" title=\"<PERSON>\"><PERSON></a>, English-American entomologist and palaeontologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_W._Heads\" title=\"<PERSON>\"><PERSON></a>, English-American entomologist and palaeontologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._Heads"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Albanian entrepreneur and veganism activist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Shk%C3%ABlzen_Shala\" title=\"Shkëlzen Shala\"><PERSON>hk<PERSON><PERSON> Shala</a>, Albanian entrepreneur and veganism activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shk%C3%ABlzen_Shala\" title=\"Shkëlzen Shala\">Shk<PERSON><PERSON> Shala</a>, Albanian entrepreneur and veganism activist", "links": [{"title": "Shkëlzen Shala", "link": "https://wikipedia.org/wiki/Shk%C3%ABlzen_Shala"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Marshawn Lynch\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>aw<PERSON>_<PERSON>\" title=\"Marshawn Lynch\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Brazilian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON> Gun <PERSON>, American rapper, singer, songwriter, actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(musician)\" title=\"<PERSON> Gun <PERSON> (musician)\"><PERSON> Gun <PERSON></a>, American rapper, singer, songwriter, actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON> Gun <PERSON></a>, American rapper, singer, songwriter, actor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(musician)"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Scottish curler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, English cricketer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American actress", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "296", "text": "<PERSON>", "html": "296 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>us"}]}, {"year": "536", "text": "Pope Agapetus I", "html": "536 - <a href=\"https://wikipedia.org/wiki/Pope_Agapetus_I\" title=\"Pope Agapetus I\">Pope Agapetus I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Agapetus_I\" title=\"Pope Agapetus I\">Pope Agapetus I</a>", "links": [{"title": "Pope Agapetus I", "link": "https://wikipedia.org/wiki/<PERSON>_Agapetus_I"}]}, {"year": "591", "text": "<PERSON> of Raqqa", "html": "591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Raqqa\" class=\"mw-redirect\" title=\"<PERSON> of Raqqa\"><PERSON> of Raqqa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Raqqa\" class=\"mw-redirect\" title=\"<PERSON> III of Raqqa\"><PERSON> of Raqqa</a>", "links": [{"title": "<PERSON> of Raqqa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Ra<PERSON>"}]}, {"year": "613", "text": "<PERSON> of Sykeon", "html": "613 - <a href=\"https://wikipedia.org/wiki/Saint_<PERSON>_of_Sykeon\" class=\"mw-redirect\" title=\"<PERSON> of Sykeon\"><PERSON> of Sykeon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_<PERSON>_of_Sykeon\" class=\"mw-redirect\" title=\"Saint <PERSON> of Sykeon\"><PERSON> of Sykeon</a>", "links": [{"title": "<PERSON> of Sykeon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sykeon"}]}, {"year": "835", "text": "<PERSON><PERSON><PERSON>, Japanese Buddhist monk, founder of Esoteric (Shingon) Buddhism (b. 774)", "html": "835 - <a href=\"https://wikipedia.org/wiki/K%C5%ABkai\" title=\"Kūkai\"><PERSON><PERSON><PERSON></a>, Japanese Buddhist monk, founder of <a href=\"https://wikipedia.org/wiki/Shingon\" class=\"mw-redirect\" title=\"Shingon\">Eso<PERSON>ic (Shingon) Buddhism</a> (b. 774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%ABkai\" title=\"Kūkai\"><PERSON><PERSON><PERSON></a>, Japanese Buddhist monk, founder of <a href=\"https://wikipedia.org/wiki/Shingon\" class=\"mw-redirect\" title=\"Shingon\">Esoteric (Shingon) Buddhism</a> (b. 774)", "links": [{"title": "Kūkai", "link": "https://wikipedia.org/wiki/K%C5%ABkai"}, {"title": "Shingon", "link": "https://wikipedia.org/wiki/Shingon"}]}, {"year": "846", "text": "<PERSON><PERSON>, Chinese emperor (b. 814)", "html": "846 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON><PERSON></a>, Chinese emperor (b. 814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON>zong of Tang\"><PERSON><PERSON></a>, Chinese emperor (b. 814)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}]}, {"year": "1208", "text": "<PERSON> of Poitou, Prince-Bishop of Durham", "html": "1208 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Poitou\" title=\"<PERSON> of Poitou\"><PERSON> of Poitou</a>, Prince-Bishop of Durham", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Poitou\" title=\"<PERSON> of Poitou\"><PERSON> of Poitou</a>, Prince-Bishop of Durham", "links": [{"title": "Philip of Poitou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Poitou"}]}, {"year": "1322", "text": "<PERSON> Fabriano, Italian writer (b. 1251)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_F<PERSON><PERSON>\" title=\"<PERSON> of Fabriano\"><PERSON> of Fabriano</a>, Italian writer (b. 1251)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_F<PERSON>\" title=\"<PERSON> of Fabriano\"><PERSON> of Fabriano</a>, Italian writer (b. 1251)", "links": [{"title": "<PERSON> of Fabriano", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1355", "text": "<PERSON> of Woodstock, countess regent of Guelders, eldest daughter of King <PERSON> of England (b. 1318)", "html": "1355 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock\" title=\"<PERSON> of Woodstock\"><PERSON> of Woodstock</a>, countess regent of Guelders, eldest daughter of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1318)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock\" title=\"<PERSON> of Woodstock\"><PERSON> of Woodstock</a>, countess regent of Guelders, eldest daughter of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1318)", "links": [{"title": "<PERSON> Woodstock", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1585", "text": "<PERSON> of Saxe-Lauenburg, Prince-Archbishop of Bremen, Prince-Bishop of Osnabrück and Paderborn (b. 1550)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"<PERSON> of Saxe-Lauenburg\"><PERSON> of Saxe-Lauenburg</a>, Prince-Archbishop of Bremen, Prince-Bishop of Osnabrück and Paderborn (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"<PERSON> of Saxe-Lauenburg\"><PERSON> of Saxe-Lauenburg</a>, Prince-Archbishop of Bremen, Prince-Bishop of Osnabrück and Paderborn (b. 1550)", "links": [{"title": "<PERSON> of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg"}]}, {"year": "1616", "text": "<PERSON>, Spanish novelist, poet, and playwright (b. 1547)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ce<PERSON>\"><PERSON></a>, Spanish novelist, poet, and playwright (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ce<PERSON>ntes\"><PERSON></a>, Spanish novelist, poet, and playwright (b. 1547)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, Swedish linguist and poet (b. 1598)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish linguist and poet (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish linguist and poet (b. 1598)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nhielm"}]}, {"year": "1699", "text": "<PERSON>, German poet (b. 1646)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/Hans_<PERSON>_A%C3%9Fmann\" title=\"<PERSON>\"><PERSON></a>, German poet (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%9Fmann\" title=\"<PERSON>\"><PERSON></a>, German poet (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hans_Erasmus_A%C3%9Fmann"}]}, {"year": "1758", "text": "<PERSON>, French botanist and physician (b. 1686)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and physician (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and physician (b. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, British inventor (b. 1720)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British inventor (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British inventor (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1806", "text": "<PERSON><PERSON><PERSON>, French admiral (b. 1763)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French admiral (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French admiral (b. 1763)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1821", "text": "<PERSON> of Constantinople, Greek patriarch and saint (b. 1746)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Gregory_V_of_Constantinople\" title=\"Gregory V of Constantinople\"><PERSON> of Constantinople</a>, Greek patriarch and saint (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gregory_V_of_Constantinople\" title=\"<PERSON> V of Constantinople\"><PERSON> of Constantinople</a>, Greek patriarch and saint (b. 1746)", "links": [{"title": "<PERSON> V of Constantinople", "link": "https://wikipedia.org/wiki/Gregory_V_of_Constantinople"}]}, {"year": "1833", "text": "<PERSON>, English engineer and explorer (b. 1771)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and explorer (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and explorer (b. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, Estonian philologist and physician (b. 1798)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian philologist and physician (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian philologist and physician (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Mexican general and politician, 11th President of Mexico (b. 1786)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Bravo\" title=\"Nicolás Bravo\"><PERSON><PERSON><PERSON></a>, Mexican general and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Bravo\" title=\"Nicolás Bravo\"><PERSON><PERSON><PERSON></a>, Mexican general and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1786)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Bravo"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1871", "text": "<PERSON>, Mexican general and president (1855) (b. 1806)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Carrera\" title=\"<PERSON>\"><PERSON></a>, Mexican general and president (1855) (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Carrera\" title=\"<PERSON>\"><PERSON></a>, Mexican general and president (1855) (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Carrera"}]}, {"year": "1877", "text": "<PERSON>, Scottish-American engineer (b. 1807)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American engineer (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American engineer (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, French violinist and composer (b. 1823)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and composer (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and composer (b. 1823)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Lithuanian businessman and author (b. 1825)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian businessman and author (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian businessman and author (b. 1825)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Greek author and poet (b. 1868)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and poet (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and poet (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ko<PERSON>s_Krystallis"}]}, {"year": "1896", "text": "<PERSON>, English engineer, founded Halcrow Group (b. 1812)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, founded <a href=\"https://wikipedia.org/wiki/Halcrow_Group\" title=\"Halcrow Group\">Halcrow Group</a> (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, founded <a href=\"https://wikipedia.org/wiki/Halcrow_Group\" title=\"Halcrow Group\">Halcrow Group</a> (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Halcrow Group", "link": "https://wikipedia.org/wiki/Halcrow_Group"}]}, {"year": "1908", "text": "<PERSON>-<PERSON>, Prime Minister of the United Kingdom (b. 1836)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1836)", "links": [{"title": "<PERSON>-Bannerman", "link": "https://wikipedia.org/wiki/<PERSON>man"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1925", "text": "<PERSON>, French composer and conductor (b. 1878)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Caplet\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Caplet\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Caplet"}]}, {"year": "1929", "text": "<PERSON>, French painter and art collector (b. 1848)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and art collector (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and art collector (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Hungarian-Slovene historian and author (b. 1883)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Slovene historian and author (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Slovene historian and author (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ren<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English engineer and businessman, co-founded Rolls-Royce Limited (b. 1863)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Rolls-Royce_Limited\" title=\"Rolls-Royce Limited\">Rolls-Royce Limited</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Royce\"><PERSON></a>, English engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Rolls-Royce_Limited\" title=\"Rolls-Royce Limited\">Rolls-Royce Limited</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rolls-Royce Limited", "link": "https://wikipedia.org/wiki/Rolls-Royce_Limited"}]}, {"year": "1945", "text": "<PERSON>, German mathematician and academic (b. 1900)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German painter and sculptor (b. 1867)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/K%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and sculptor (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and sculptor (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American lawyer and academic (b. 1895)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English myrmecologist and coleopterist (b. 1870)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English myrmecologist and coleopterist (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English myrmecologist and coleopterist (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor (b. 1902)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress and singer (b. 1907)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German chemist and physicist (b. 1902)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physicist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physicist (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American pianist and bandleader (b. 1903)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and bandleader (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and bandleader (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American photographer and environmentalist (b. 1902)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer and environmentalist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer and environmentalist (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American chemist and academic (b. 1900)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian physician and author (b. 1921)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Romanian historian and author (b. 1907)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Eliade\"><PERSON><PERSON></a>, Romanian historian and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Eli<PERSON>\"><PERSON><PERSON></a>, Romanian historian and author (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Estonian architect (b. 1905)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Erik<PERSON>_N%C3%B5va\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_N%C3%B5va\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erika_N%C3%B5va"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Russian-Estonian astronomer and academic (b. 1917)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Estonian astronomer and academic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Estonian astronomer and academic (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actress (b. 1891)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American actress (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Italian-American physicist and academic, Nobel Prize laureate (b. 1905)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Segr%C3%A8\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Segr%C3%A8\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._Segr%C3%A8"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1928)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, 37th President of the United States (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 37th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 37th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1995", "text": "<PERSON>, American poet and author (b. 1947)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American journalist and author (b. 1927)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American golfer and architect (b. 1908)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Jug_<PERSON>c<PERSON>\" title=\"<PERSON>g <PERSON>c<PERSON>\"><PERSON><PERSON></a>, American golfer and architect (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jug_<PERSON>\" title=\"<PERSON>g <PERSON>\"><PERSON><PERSON></a>, American golfer and architect (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jug_<PERSON>c<PERSON>en"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Pakistani nuclear engineer (b. 1926)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani nuclear engineer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani nuclear engineer (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American songwriter (b. 1925)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, German translator (b. 1906)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German translator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German translator (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American physicist and academic (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Scottish sculptor and artist (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish sculptor and artist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish sculptor and artist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American computer scientist and academic (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and academic (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ram"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Italian actress (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>-<PERSON>, American educator and politician (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, British cinematographer, director and photographer (b. 1914)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Jack_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, British cinematographer, director and photographer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_<PERSON>\" title=\"Jack Cardiff\"><PERSON></a>, British cinematographer, director and photographer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American lawyer and activist (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, American lawyer and activist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, American lawyer and activist (b. 1943)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)"}]}, {"year": "2012", "text": "<PERSON>, American chemist, biologist, and businessman (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, biologist, and businessman (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, biologist, and businessman (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian violinist and composer (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Lalgu<PERSON>_<PERSON>n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian violinist and composer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lalgu<PERSON>_<PERSON>n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian violinist and composer (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "2013", "text": "<PERSON>, American pianist, composer, and conductor (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Venezuelan painter (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan painter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan painter (b. 1926)", "links": [{"title": "Oswald<PERSON>", "link": "https://wikipedia.org/wiki/Oswaldo_Vigas"}]}, {"year": "2015", "text": "<PERSON>, Scottish environmentalist and photographer (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish environmentalist and photographer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish environmentalist and photographer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Australian writer, artist, and activist (b. 1963)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian writer, artist, and activist (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian writer, artist, and activist (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American actress (b. 1936)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American professional baseball player (b. 1943)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional baseball player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional baseball player (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Canadian ice hockey player (b. 1951)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, English ballroom dancer and television personality (b. 1944)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballroom dancer and television personality (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballroom dancer and television personality (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}