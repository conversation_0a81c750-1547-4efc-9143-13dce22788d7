{"date": "September 23", "url": "https://wikipedia.org/wiki/September_23", "data": {"Events": [{"year": "38", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>'s sister who died in June, with whom the emperor is said to have an incestuous relationship, is deified.", "html": "38 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Caligula\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s sister who died in June, with whom the emperor is said to have an incestuous relationship, is deified.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Caligula\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s sister who died in June, with whom the emperor is said to have an incestuous relationship, is deified.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Caligula"}]}, {"year": "1122", "text": "Pope <PERSON><PERSON><PERSON> and Holy Roman Emperor <PERSON> agree to the Concordat of Worms to put an end to the Investiture Controversy.", "html": "1122 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Callixtus_II\" title=\"<PERSON> Callixtus II\">Pope <PERSON><PERSON><PERSON> II</a> and Holy Roman Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON></a> agree to the <a href=\"https://wikipedia.org/wiki/Concordat_of_Worms\" title=\"Concordat of Worms\">Concordat of Worms</a> to put an end to the <a href=\"https://wikipedia.org/wiki/Investiture_Controversy\" title=\"Investiture Controversy\">Investiture Controversy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Callixtus_II\" title=\"Pope Callixtus II\">Pope <PERSON><PERSON><PERSON> II</a> and Holy Roman Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON></a> agree to the <a href=\"https://wikipedia.org/wiki/Concordat_of_Worms\" title=\"Concordat of Worms\">Concordat of Worms</a> to put an end to the <a href=\"https://wikipedia.org/wiki/Investiture_Controversy\" title=\"Investiture Controversy\">Investiture Controversy</a>.", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_Callixtus_II"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Concordat of Worms", "link": "https://wikipedia.org/wiki/Concordat_of_Worms"}, {"title": "Investiture Controversy", "link": "https://wikipedia.org/wiki/Investiture_Controversy"}]}, {"year": "1338", "text": "The Battle of Arnemuiden, in which a French force defeats the English, is the first naval battle of the Hundred Years' War and the first naval battle in which gunpowder artillery is used.", "html": "1338 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Arnemuiden\" title=\"Battle of Arnemuiden\">Battle of Arnemuiden</a>, in which a French force defeats the English, is the first naval battle of the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a> and the first naval battle in which gunpowder artillery is used.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Arnemuiden\" title=\"Battle of Arnemuiden\">Battle of Arnemuiden</a>, in which a French force defeats the English, is the first naval battle of the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a> and the first naval battle in which gunpowder artillery is used.", "links": [{"title": "Battle of Arnemuiden", "link": "https://wikipedia.org/wiki/Battle_of_Arnemuiden"}, {"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}]}, {"year": "1409", "text": "The Battle of Kherlen is the second significant victory over Ming dynasty China by the Mongols since 1368.", "html": "1409 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Kherlen\" title=\"Battle of Kherlen\">Battle of Kherlen</a> is the second significant victory over Ming dynasty China by the Mongols since 1368.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Kherlen\" title=\"Battle of Kherlen\">Battle of Kherlen</a> is the second significant victory over Ming dynasty China by the Mongols since 1368.", "links": [{"title": "Battle of Kherlen", "link": "https://wikipedia.org/wiki/Battle_of_Kherlen"}]}, {"year": "1459", "text": "The Battle of Blore Heath, the first major battle of the English Wars of the Roses, is won by the Yorkists.", "html": "1459 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Blore_Heath\" title=\"Battle of Blore Heath\">Battle of Blore Heath</a>, the first major battle of the English Wars of the Roses, is won by the Yorkists.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Blore_Heath\" title=\"Battle of Blore Heath\">Battle of Blore Heath</a>, the first major battle of the English Wars of the Roses, is won by the Yorkists.", "links": [{"title": "Battle of Blore Heath", "link": "https://wikipedia.org/wiki/Battle_of_Blore_Heath"}]}, {"year": "1561", "text": "King <PERSON> of Spain issues cedula, ordering a halt to colonizing efforts in Florida.", "html": "1561 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> issues <a href=\"https://wikipedia.org/wiki/Cedula_de_identidad\" class=\"mw-redirect\" title=\"Cedula de identidad\">cedula</a>, ordering a halt to colonizing efforts in Florida.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> issues <a href=\"https://wikipedia.org/wiki/Cedula_de_identidad\" class=\"mw-redirect\" title=\"Cedula de identidad\">cedula</a>, ordering a halt to colonizing efforts in Florida.", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}, {"title": "Cedula de identidad", "link": "https://wikipedia.org/wiki/Cedula_de_identidad"}]}, {"year": "1642", "text": "First English Civil War: The Battle of Powick Bridge, the first engagement between the primary field armies of the Royalists and the Parliamentarians, ended in a Royalist victory.", "html": "1642 - <a href=\"https://wikipedia.org/wiki/First_English_Civil_War\" title=\"First English Civil War\">First English Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Powick_Bridge\" title=\"Battle of Powick Bridge\">Battle of Powick Bridge</a>, the first engagement between the primary field armies of the <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalists</a> and the <a href=\"https://wikipedia.org/wiki/Roundheads\" class=\"mw-redirect\" title=\"Roundheads\">Parliamentarians</a>, ended in a Royalist victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_English_Civil_War\" title=\"First English Civil War\">First English Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Powick_Bridge\" title=\"Battle of Powick Bridge\">Battle of Powick Bridge</a>, the first engagement between the primary field armies of the <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalists</a> and the <a href=\"https://wikipedia.org/wiki/Roundheads\" class=\"mw-redirect\" title=\"Roundheads\">Parliamentarians</a>, ended in a Royalist victory.", "links": [{"title": "First English Civil War", "link": "https://wikipedia.org/wiki/First_English_Civil_War"}, {"title": "Battle of Powick Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Powick_Bridge"}, {"title": "Cavalier", "link": "https://wikipedia.org/wiki/Cavalier"}, {"title": "Roundheads", "link": "https://wikipedia.org/wiki/Roundheads"}]}, {"year": "1779", "text": "American Revolution: <PERSON>, naval commander of the United States, on board the USS Bonhomme Richard, wins the Battle of Flamborough Head.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, naval commander of the United States, on board the <a href=\"https://wikipedia.org/wiki/USS_Bonhomme_Richard_(1765)\" title=\"USS Bonhomme Richard (1765)\">USS <i>Bonhom<PERSON> Richard</i></a>, wins the <a href=\"https://wikipedia.org/wiki/Battle_of_Flamborough_Head\" title=\"Battle of Flamborough Head\">Battle of Flamborough Head</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, naval commander of the United States, on board the <a href=\"https://wikipedia.org/wiki/USS_Bonhomme_Richard_(1765)\" title=\"USS Bonhomme Richard (1765)\">USS <i><PERSON>hom<PERSON> Richard</i></a>, wins the <a href=\"https://wikipedia.org/wiki/Battle_of_Flamborough_Head\" title=\"Battle of Flamborough Head\">Battle of Flamborough Head</a>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "USS <PERSON>hom<PERSON> (1765)", "link": "https://wikipedia.org/wiki/USS_<PERSON><PERSON><PERSON>_<PERSON>_(1765)"}, {"title": "Battle of Flamborough Head", "link": "https://wikipedia.org/wiki/Battle_of_Flamborough_Head"}]}, {"year": "1803", "text": "Second Anglo-Maratha War: The Battle of Assaye is fought between the British East India Company and the Maratha Empire in India.", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Second_Anglo-Maratha_War\" title=\"Second Anglo-Maratha War\">Second Anglo-Maratha War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Assaye\" title=\"Battle of Assaye\">Battle of Assaye</a> is fought between the <a href=\"https://wikipedia.org/wiki/British_East_India_Company\" class=\"mw-redirect\" title=\"British East India Company\">British East India Company</a> and the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> in India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Anglo-Maratha_War\" title=\"Second Anglo-Maratha War\">Second Anglo-Maratha War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Assaye\" title=\"Battle of Assaye\">Battle of Assaye</a> is fought between the <a href=\"https://wikipedia.org/wiki/British_East_India_Company\" class=\"mw-redirect\" title=\"British East India Company\">British East India Company</a> and the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> in India.", "links": [{"title": "Second Anglo-Maratha War", "link": "https://wikipedia.org/wiki/Second_Anglo-Maratha_War"}, {"title": "Battle of Assaye", "link": "https://wikipedia.org/wiki/Battle_of_Assaye"}, {"title": "British East India Company", "link": "https://wikipedia.org/wiki/British_East_India_Company"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}]}, {"year": "1821", "text": "Tripolitsa, Greece, is captured by Greek rebels during the Greek War of Independence.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Siege_of_Tripolitsa\" title=\"Siege of Tripolitsa\">Tripolitsa</a>, Greece, is captured by Greek rebels during the Greek War of Independence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Tripolitsa\" title=\"Siege of Tripolitsa\">Tripolitsa</a>, Greece, is captured by Greek rebels during the Greek War of Independence.", "links": [{"title": "Siege of Tripolitsa", "link": "https://wikipedia.org/wiki/Siege_of_Tripolitsa"}]}, {"year": "1846", "text": "Astronomers <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> collaborate on the discovery of Neptune.", "html": "1846 - Astronomers <a href=\"https://wikipedia.org/wiki/Urb<PERSON>_Le_V<PERSON>rier\" title=\"<PERSON>rbain Le Verrier\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> collaborate on the <a href=\"https://wikipedia.org/wiki/Discovery_of_Neptune\" title=\"Discovery of Neptune\">discovery of Neptune</a>.", "no_year_html": "Astronomers <a href=\"https://wikipedia.org/wiki/Urb<PERSON>_Le_V<PERSON>rier\" title=\"<PERSON>rbain Le Verrier\">U<PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> collaborate on the <a href=\"https://wikipedia.org/wiki/Discovery_of_Neptune\" title=\"Discovery of Neptune\">discovery of Neptune</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Discovery of Neptune", "link": "https://wikipedia.org/wiki/Discovery_of_Neptune"}]}, {"year": "1868", "text": "The Grito de Lares occurs in Puerto Rico against Spanish rule.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/Grito_de_Lares\" title=\"Grito de Lares\">Grito de Lares</a> occurs in Puerto Rico against Spanish rule.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grito_de_Lares\" title=\"Grito de Lares\">Grito de Lares</a> occurs in Puerto Rico against Spanish rule.", "links": [{"title": "Grito de Lares", "link": "https://wikipedia.org/wiki/G<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "The Macedo-Romanian Cultural Society is founded.", "html": "1879 - The <a href=\"https://wikipedia.org/wiki/Macedo-Romanian_Cultural_Society\" title=\"Macedo-Romanian Cultural Society\">Macedo-Romanian Cultural Society</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Macedo-Romanian_Cultural_Society\" title=\"Macedo-Romanian Cultural Society\">Macedo-Romanian Cultural Society</a> is founded.", "links": [{"title": "Macedo-Romanian Cultural Society", "link": "https://wikipedia.org/wiki/Macedo-Romanian_Cultural_Society"}]}, {"year": "1884", "text": "On the night of 23-24 September, the steamship Arctique runs aground near Cape Virgenes leading to the discovery of nearby placer gold, beginning the Tierra del Fuego gold rush.", "html": "1884 - On the night of 23-24 September, the steamship <i>Arctique</i> runs aground near <a href=\"https://wikipedia.org/wiki/Cape_Virgenes\" title=\"Cape Virgenes\">Cape Virgenes</a> leading to the discovery of nearby <a href=\"https://wikipedia.org/wiki/Placer_mining\" title=\"Placer mining\">placer</a> gold, beginning the <a href=\"https://wikipedia.org/wiki/Tierra_del_Fuego_gold_rush\" title=\"Tierra del Fuego gold rush\">Tierra del Fuego gold rush</a>.", "no_year_html": "On the night of 23-24 September, the steamship <i>Arctique</i> runs aground near <a href=\"https://wikipedia.org/wiki/Cape_Virgenes\" title=\"Cape Virgenes\">Cape Virgenes</a> leading to the discovery of nearby <a href=\"https://wikipedia.org/wiki/Placer_mining\" title=\"Placer mining\">placer</a> gold, beginning the <a href=\"https://wikipedia.org/wiki/Tierra_del_Fuego_gold_rush\" title=\"Tierra del Fuego gold rush\">Tierra del Fuego gold rush</a>.", "links": [{"title": "Cape Virgenes", "link": "https://wikipedia.org/wiki/Cape_Virgenes"}, {"title": "Placer mining", "link": "https://wikipedia.org/wiki/Placer_mining"}, {"title": "Tierra del Fuego gold rush", "link": "https://wikipedia.org/wiki/Tierra_del_Fuego_gold_rush"}]}, {"year": "1899", "text": "The American Asiatic Squadron destroys a Filipino battery at the Battle of Olongapo.", "html": "1899 - The American Asiatic Squadron destroys a Filipino battery at the <a href=\"https://wikipedia.org/wiki/Battle_of_Olongapo\" title=\"Battle of Olongapo\">Battle of Olongapo</a>.", "no_year_html": "The American Asiatic Squadron destroys a Filipino battery at the <a href=\"https://wikipedia.org/wiki/Battle_of_Olongapo\" title=\"Battle of Olongapo\">Battle of Olongapo</a>.", "links": [{"title": "Battle of Olongapo", "link": "https://wikipedia.org/wiki/Battle_of_Olongapo"}]}, {"year": "1905", "text": "Norway and Sweden sign the Karlstad Treaty, peacefully dissolving the Union between the two countries.", "html": "1905 - Norway and Sweden sign the Karlstad Treaty, peacefully <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_union_between_Norway_and_Sweden\" title=\"Dissolution of the union between Norway and Sweden\">dissolving</a> the Union between the two countries.", "no_year_html": "Norway and Sweden sign the Karlstad Treaty, peacefully <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_union_between_Norway_and_Sweden\" title=\"Dissolution of the union between Norway and Sweden\">dissolving</a> the Union between the two countries.", "links": [{"title": "Dissolution of the union between Norway and Sweden", "link": "https://wikipedia.org/wiki/Dissolution_of_the_union_between_Norway_and_Sweden"}]}, {"year": "1913", "text": "The United Mine Workers of America launch a strike which eventually escalated into the Colorado Coalfield War.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/United_Mine_Workers_of_America\" title=\"United Mine Workers of America\">United Mine Workers of America</a> launch a strike which eventually escalated into the <a href=\"https://wikipedia.org/wiki/Colorado_Coalfield_War\" title=\"Colorado Coalfield War\">Colorado Coalfield War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Mine_Workers_of_America\" title=\"United Mine Workers of America\">United Mine Workers of America</a> launch a strike which eventually escalated into the <a href=\"https://wikipedia.org/wiki/Colorado_Coalfield_War\" title=\"Colorado Coalfield War\">Colorado Coalfield War</a>.", "links": [{"title": "United Mine Workers of America", "link": "https://wikipedia.org/wiki/United_Mine_Workers_of_America"}, {"title": "Colorado Coalfield War", "link": "https://wikipedia.org/wiki/Colorado_Coalfield_War"}]}, {"year": "1918", "text": "World War I: The Battle of Haifa takes place in present-day Israel, part of the Ottoman Empire at that time.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Haifa_(1918)\" title=\"Battle of Haifa (1918)\">The Battle of Haifa</a> takes place in present-day <a href=\"https://wikipedia.org/wiki/Haifa\" title=\"Haifa\">Israel</a>, part of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> at that time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Haifa_(1918)\" title=\"Battle of Haifa (1918)\">The Battle of Haifa</a> takes place in present-day <a href=\"https://wikipedia.org/wiki/Haifa\" title=\"Haifa\">Israel</a>, part of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> at that time.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Haifa (1918)", "link": "https://wikipedia.org/wiki/Battle_of_Haifa_(1918)"}, {"title": "Haifa", "link": "https://wikipedia.org/wiki/Haifa"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1920", "text": "The Louisiana hurricane dissipated over Kansas after forcing around 4,500 people to evacuate and causing $1.45 million in damages.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/1920_Louisiana_hurricane\" title=\"1920 Louisiana hurricane\">Louisiana hurricane</a> dissipated over <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a> after forcing around 4,500 people to evacuate and causing $1.45 million in damages.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1920_Louisiana_hurricane\" title=\"1920 Louisiana hurricane\">Louisiana hurricane</a> dissipated over <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a> after forcing around 4,500 people to evacuate and causing $1.45 million in damages.", "links": [{"title": "1920 Louisiana hurricane", "link": "https://wikipedia.org/wiki/1920_Louisiana_hurricane"}, {"title": "Kansas", "link": "https://wikipedia.org/wiki/Kansas"}]}, {"year": "1932", "text": "Saudi National Day: Crown Prince (later king) <PERSON><PERSON><PERSON> of Saudi Arabia, on behalf of <PERSON>, proclaims the unification of the Kingdom of Saudi Arabia, the current iteration of the Third Saudi State.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Saudi_National_Day\" title=\"Saudi National Day\">Saudi National Day</a>: Crown Prince (later king) <a href=\"https://wikipedia.org/wiki/Faisal_of_Saudi_Arabia\" title=\"<PERSON>aisal of Saudi Arabia\"><PERSON><PERSON><PERSON> of Saudi Arabia</a>, on behalf of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, proclaims the unification of the <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Kingdom of Saudi Arabia</a>, the current iteration of the <a href=\"https://wikipedia.org/wiki/Third_Saudi_State\" title=\"Third Saudi State\">Third Saudi State</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saudi_National_Day\" title=\"Saudi National Day\">Saudi National Day</a>: Crown Prince (later king) <a href=\"https://wikipedia.org/wiki/Faisal_of_Saudi_Arabia\" title=\"<PERSON>aisal of Saudi Arabia\"><PERSON><PERSON><PERSON> of Saudi Arabia</a>, on behalf of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, proclaims the unification of the <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Kingdom of Saudi Arabia</a>, the current iteration of the <a href=\"https://wikipedia.org/wiki/Third_Saudi_State\" title=\"Third Saudi State\">Third Saudi State</a>.", "links": [{"title": "Saudi National Day", "link": "https://wikipedia.org/wiki/Saudi_National_Day"}, {"title": "<PERSON><PERSON><PERSON> of Saudi Arabia", "link": "https://wikipedia.org/wiki/<PERSON>ais<PERSON>_of_Saudi_Arabia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}, {"title": "Third Saudi State", "link": "https://wikipedia.org/wiki/Third_Saudi_State"}]}, {"year": "1942", "text": "World War II: The Matanikau action on Guadalcanal begins: U.S. Marines attack Japanese units along the Matanikau River.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Actions_along_the_Matanikau\" title=\"Actions along the Matanikau\">Matanikau action</a> on <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a> begins: U.S. Marines attack Japanese units along the <a href=\"https://wikipedia.org/wiki/Matanikau_River\" title=\"Matanikau River\">Matanikau River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Actions_along_the_Matanikau\" title=\"Actions along the Matanikau\">Matanikau action</a> on <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a> begins: U.S. Marines attack Japanese units along the <a href=\"https://wikipedia.org/wiki/Matanikau_River\" title=\"Matanikau River\">Matanikau River</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Actions along the Matanikau", "link": "https://wikipedia.org/wiki/Actions_along_the_Matanikau"}, {"title": "Guadalcanal", "link": "https://wikipedia.org/wiki/Guadalcanal"}, {"title": "Matanikau River", "link": "https://wikipedia.org/wiki/Matanikau_River"}]}, {"year": "1947", "text": "A magnitude 6.9 earthquake strikes South Khorasan in Iran, killing over 500 people.", "html": "1947 - A <a href=\"https://wikipedia.org/wiki/1947_Dustabad_earthquake\" title=\"1947 Dustabad earthquake\">magnitude 6.9 earthquake</a> strikes South Khorasan in Iran, killing over 500 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1947_Dustabad_earthquake\" title=\"1947 Dustabad earthquake\">magnitude 6.9 earthquake</a> strikes South Khorasan in Iran, killing over 500 people.", "links": [{"title": "1947 Dustabad earthquake", "link": "https://wikipedia.org/wiki/1947_Dustabad_earthquake"}]}, {"year": "1950", "text": "Korean War: The Battle of Hill 282 is the first US friendly-fire incident on British military personnel since World War II.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Hill_282\" title=\"Battle of Hill 282\">Battle of Hill 282</a> is the first US friendly-fire incident on British military personnel since World War II.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Hill_282\" title=\"Battle of Hill 282\">Battle of Hill 282</a> is the first US friendly-fire incident on British military personnel since World War II.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Battle of Hill 282", "link": "https://wikipedia.org/wiki/Battle_of_Hill_282"}]}, {"year": "1951", "text": "<PERSON>, king of the United Kingdom, has his left lung removed in an operation after a malignant tumour was found.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VI\"><PERSON></a>, king of the United Kingdom, has his <a href=\"https://wikipedia.org/wiki/Pneumonectomy\" title=\"Pneumonectomy\">left lung removed</a> in an operation after a malignant tumour was found.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VI\"><PERSON></a>, king of the United Kingdom, has his <a href=\"https://wikipedia.org/wiki/Pneumonectomy\" title=\"Pneumonectomy\">left lung removed</a> in an operation after a malignant tumour was found.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pneumonectomy", "link": "https://wikipedia.org/wiki/Pneumonectomy"}]}, {"year": "1952", "text": "After being accused of financial improprieties, Senator <PERSON> delivers his \"Checkers speech\" nationwide on television and radio, defending his actions and successfully salvaging his nomination as the Republican candidate for Vice President.", "html": "1952 - After being accused of financial improprieties, Senator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his \"<a href=\"https://wikipedia.org/wiki/Checkers_speech\" title=\"Checkers speech\">Checkers speech</a>\" nationwide on television and radio, defending his actions and successfully salvaging his nomination as the Republican candidate for <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a>.", "no_year_html": "After being accused of financial improprieties, Senator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his \"<a href=\"https://wikipedia.org/wiki/Checkers_speech\" title=\"Checkers speech\">Checkers speech</a>\" nationwide on television and radio, defending his actions and successfully salvaging his nomination as the Republican candidate for <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Checkers speech", "link": "https://wikipedia.org/wiki/Checkers_speech"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1955", "text": "An all-white jury in Mississippi finds <PERSON> and <PERSON><PERSON> <PERSON><PERSON> not guilty in the torture-murder of 14-year-old African American boy <PERSON><PERSON>.", "html": "1955 - An all-white jury in <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> finds <PERSON> and <PERSON><PERSON> <PERSON><PERSON> not guilty in the torture-murder of 14-year-old African American boy <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "An all-white jury in <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> finds <PERSON> and <PERSON><PERSON> <PERSON><PERSON> not guilty in the torture-murder of 14-year-old African American boy <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "A tropical storm originating in the eastern Pacific Ocean passes into the Gulf of Mexico and is upgraded and named Hurricane <PERSON><PERSON><PERSON> just hours before striking the Gulf Coast and causing 15 deaths and an estimated USD$24.8 million in damages.", "html": "1956 - A <a href=\"https://wikipedia.org/wiki/Tropical_cyclone#Classification_and_naming\" title=\"Tropical cyclone\">tropical storm</a> originating in the eastern <a href=\"https://wikipedia.org/wiki/Pacific_Ocean\" title=\"Pacific Ocean\">Pacific Ocean</a> passes into the <a href=\"https://wikipedia.org/wiki/Gulf_of_Mexico\" title=\"Gulf of Mexico\">Gulf of Mexico</a> and is upgraded and named <a href=\"https://wikipedia.org/wiki/Hurricane_Flossy_(1956)\" title=\"Hurricane Flossy (1956)\">Hurricane Flossy</a> just hours before striking the <a href=\"https://wikipedia.org/wiki/Gulf_Coast_of_the_United_States\" title=\"Gulf Coast of the United States\">Gulf Coast</a> and causing 15 deaths and an estimated <a href=\"https://wikipedia.org/wiki/United_States_dollar\" title=\"United States dollar\">USD</a>$24.8 million in damages.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tropical_cyclone#Classification_and_naming\" title=\"Tropical cyclone\">tropical storm</a> originating in the eastern <a href=\"https://wikipedia.org/wiki/Pacific_Ocean\" title=\"Pacific Ocean\">Pacific Ocean</a> passes into the <a href=\"https://wikipedia.org/wiki/Gulf_of_Mexico\" title=\"Gulf of Mexico\">Gulf of Mexico</a> and is upgraded and named <a href=\"https://wikipedia.org/wiki/Hurricane_Flossy_(1956)\" title=\"Hurricane Flossy (1956)\">Hurricane Flossy</a> just hours before striking the <a href=\"https://wikipedia.org/wiki/Gulf_Coast_of_the_United_States\" title=\"Gulf Coast of the United States\">Gulf Coast</a> and causing 15 deaths and an estimated <a href=\"https://wikipedia.org/wiki/United_States_dollar\" title=\"United States dollar\">USD</a>$24.8 million in damages.", "links": [{"title": "Tropical cyclone", "link": "https://wikipedia.org/wiki/Tropical_cyclone#Classification_and_naming"}, {"title": "Pacific Ocean", "link": "https://wikipedia.org/wiki/Pacific_Ocean"}, {"title": "Gulf of Mexico", "link": "https://wikipedia.org/wiki/Gulf_of_Mexico"}, {"title": "Hurricane Flossy (1956)", "link": "https://wikipedia.org/wiki/Hurricane_Flossy_(1956)"}, {"title": "Gulf Coast of the United States", "link": "https://wikipedia.org/wiki/Gulf_Coast_of_the_United_States"}, {"title": "United States dollar", "link": "https://wikipedia.org/wiki/United_States_dollar"}]}, {"year": "1957", "text": "Little Rock schools integration crisis: President <PERSON> sends the 101st Airborne Division to Little Rock, Arkansas, and federalizes the Arkansas National Guard, ordering both to support the integration of Little Rock Central High School.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Little_Rock_Nine\" title=\"Little Rock Nine\">Little Rock schools integration crisis</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends the <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">101st Airborne Division</a> to <a href=\"https://wikipedia.org/wiki/Little_Rock,_Arkansas\" title=\"Little Rock, Arkansas\">Little Rock, Arkansas</a>, and <a href=\"https://wikipedia.org/wiki/National_Guard_(United_States)#Federal_duty\" title=\"National Guard (United States)\">federalizes</a> the <a href=\"https://wikipedia.org/wiki/Arkansas_National_Guard\" title=\"Arkansas National Guard\">Arkansas National Guard</a>, ordering both to support the <a href=\"https://wikipedia.org/wiki/School_integration_in_the_United_States\" title=\"School integration in the United States\">integration</a> of <a href=\"https://wikipedia.org/wiki/Little_Rock_Central_High_School\" title=\"Little Rock Central High School\">Little Rock Central High School</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Little_Rock_Nine\" title=\"Little Rock Nine\">Little Rock schools integration crisis</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends the <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">101st Airborne Division</a> to <a href=\"https://wikipedia.org/wiki/Little_Rock,_Arkansas\" title=\"Little Rock, Arkansas\">Little Rock, Arkansas</a>, and <a href=\"https://wikipedia.org/wiki/National_Guard_(United_States)#Federal_duty\" title=\"National Guard (United States)\">federalizes</a> the <a href=\"https://wikipedia.org/wiki/Arkansas_National_Guard\" title=\"Arkansas National Guard\">Arkansas National Guard</a>, ordering both to support the <a href=\"https://wikipedia.org/wiki/School_integration_in_the_United_States\" title=\"School integration in the United States\">integration</a> of <a href=\"https://wikipedia.org/wiki/Little_Rock_Central_High_School\" title=\"Little Rock Central High School\">Little Rock Central High School</a>.", "links": [{"title": "Little Rock Nine", "link": "https://wikipedia.org/wiki/Little_Rock_Nine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "101st Airborne Division", "link": "https://wikipedia.org/wiki/101st_Airborne_Division"}, {"title": "Little Rock, Arkansas", "link": "https://wikipedia.org/wiki/Little_Rock,_Arkansas"}, {"title": "National Guard (United States)", "link": "https://wikipedia.org/wiki/National_Guard_(United_States)#Federal_duty"}, {"title": "Arkansas National Guard", "link": "https://wikipedia.org/wiki/Arkansas_National_Guard"}, {"title": "School integration in the United States", "link": "https://wikipedia.org/wiki/School_integration_in_the_United_States"}, {"title": "Little Rock Central High School", "link": "https://wikipedia.org/wiki/Little_Rock_Central_High_School"}]}, {"year": "1961", "text": "U.S. President <PERSON> nominates African American civil rights lawyer <PERSON><PERSON><PERSON> to the Court of Appeals for the Second Circuit, although pro-segregation Southern senators manage to delay his confirmation until September 11, 1962.", "html": "1961 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> nominates African American civil rights lawyer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to the <a href=\"https://wikipedia.org/wiki/United_States_Court_of_Appeals_for_the_Second_Circuit\" title=\"United States Court of Appeals for the Second Circuit\">Court of Appeals for the Second Circuit</a>, although pro-<a href=\"https://wikipedia.org/wiki/Racial_segregation_in_the_United_States\" title=\"Racial segregation in the United States\">segregation</a> Southern senators manage to delay his confirmation until September 11, 1962.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> nominates African American civil rights lawyer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to the <a href=\"https://wikipedia.org/wiki/United_States_Court_of_Appeals_for_the_Second_Circuit\" title=\"United States Court of Appeals for the Second Circuit\">Court of Appeals for the Second Circuit</a>, although pro-<a href=\"https://wikipedia.org/wiki/Racial_segregation_in_the_United_States\" title=\"Racial segregation in the United States\">segregation</a> Southern senators manage to delay his confirmation until September 11, 1962.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}, {"title": "United States Court of Appeals for the Second Circuit", "link": "https://wikipedia.org/wiki/United_States_Court_of_Appeals_for_the_Second_Circuit"}, {"title": "Racial segregation in the United States", "link": "https://wikipedia.org/wiki/Racial_segregation_in_the_United_States"}]}, {"year": "1962", "text": "Flying Tiger Line Flight 923, a Lockheed L-1049H Super Constellation registered as N6923C, ditched into the Atlantic Ocean killing 28 out the 76 occupants onboard. The remaining 48 were rescued six hours later.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_923\" title=\"Flying Tiger Line Flight 923\">Flying Tiger Line Flight 923</a>, a <a href=\"https://wikipedia.org/wiki/Lockheed_L-1049H_Super_Constellation\" class=\"mw-redirect\" title=\"Lockheed L-1049H Super Constellation\">Lockheed L-1049H Super Constellation</a> registered as N6923C, <a href=\"https://wikipedia.org/wiki/Ditched\" class=\"mw-redirect\" title=\"Ditched\">ditched</a> into the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a> killing 28 out the 76 occupants onboard. The remaining 48 were rescued six hours later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_923\" title=\"Flying Tiger Line Flight 923\">Flying Tiger Line Flight 923</a>, a <a href=\"https://wikipedia.org/wiki/Lockheed_L-1049H_Super_Constellation\" class=\"mw-redirect\" title=\"Lockheed L-1049H Super Constellation\">Lockheed L-1049H Super Constellation</a> registered as N6923C, <a href=\"https://wikipedia.org/wiki/Ditched\" class=\"mw-redirect\" title=\"Ditched\">ditched</a> into the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a> killing 28 out the 76 occupants onboard. The remaining 48 were rescued six hours later.", "links": [{"title": "Flying Tiger Line Flight 923", "link": "https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_923"}, {"title": "Lockheed L-1049H Super Constellation", "link": "https://wikipedia.org/wiki/Lockheed_L-1049H_Super_Constellation"}, {"title": "Ditched", "link": "https://wikipedia.org/wiki/Ditched"}, {"title": "Atlantic Ocean", "link": "https://wikipedia.org/wiki/Atlantic_Ocean"}]}, {"year": "1964", "text": "Typhoon <PERSON><PERSON>, one of the strongest typhoons to ever strike Japan, makes landfall, causing at least 30 fatalities and sinking at least 64 ships.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Typhoon_Wilda_(1964)\" title=\"Typhoon Wilda (1964)\">Typhoon Wilda</a>, one of the strongest typhoons to ever strike <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, makes landfall, causing at least 30 fatalities and sinking at least 64 ships.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Wilda_(1964)\" title=\"Typhoon Wilda (1964)\">Typhoon Wilda</a>, one of the strongest typhoons to ever strike <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, makes landfall, causing at least 30 fatalities and sinking at least 64 ships.", "links": [{"title": "<PERSON> (1964)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(1964)"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "1967", "text": "Seven people die, 46 people are injured, and more than 150 boats capsize when a squall hit Lake Michigan during Michigan's first coho salmon sport fishing season.", "html": "1967 - Seven people die, 46 people are injured, and more than 150 boats capsize when a <a href=\"https://wikipedia.org/wiki/1967_Coho_Salmon_Fishing_Disaster\" title=\"1967 Coho Salmon Fishing Disaster\">squall hit Lake Michigan</a> during <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a>'s first <a href=\"https://wikipedia.org/wiki/Coho_salmon\" title=\"Coho salmon\">coho salmon</a> sport fishing season.", "no_year_html": "Seven people die, 46 people are injured, and more than 150 boats capsize when a <a href=\"https://wikipedia.org/wiki/1967_Coho_Salmon_Fishing_Disaster\" title=\"1967 Coho Salmon Fishing Disaster\">squall hit Lake Michigan</a> during <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a>'s first <a href=\"https://wikipedia.org/wiki/Coho_salmon\" title=\"Coho salmon\">coho salmon</a> sport fishing season.", "links": [{"title": "1967 Coho Salmon Fishing Disaster", "link": "https://wikipedia.org/wiki/1967_Coho_Salmon_Fishing_Disaster"}, {"title": "Michigan", "link": "https://wikipedia.org/wiki/Michigan"}, {"title": "Coho salmon", "link": "https://wikipedia.org/wiki/Co<PERSON>_salmon"}]}, {"year": "1973", "text": "Argentine general election: <PERSON> returns to power in Argentina.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Argentine_general_election,_September_1973\" class=\"mw-redirect\" title=\"Argentine general election, September 1973\">Argentine general election</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a> returns to power in Argentina.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Argentine_general_election,_September_1973\" class=\"mw-redirect\" title=\"Argentine general election, September 1973\">Argentine general election</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a> returns to power in Argentina.", "links": [{"title": "Argentine general election, September 1973", "link": "https://wikipedia.org/wiki/Argentine_general_election,_September_1973"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Per%C3%B3n"}]}, {"year": "1983", "text": "Gulf Air Flight 771 is destroyed by a bomb, killing all 112 people on board.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Gulf_Air_Flight_771\" title=\"Gulf Air Flight 771\">Gulf Air Flight 771</a> is destroyed by a bomb, killing all 112 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gulf_Air_Flight_771\" title=\"Gulf Air Flight 771\">Gulf Air Flight 771</a> is destroyed by a bomb, killing all 112 people on board.", "links": [{"title": "Gulf Air Flight 771", "link": "https://wikipedia.org/wiki/Gulf_Air_Flight_771"}]}, {"year": "1999", "text": "Qantas Flight 1 overrun a runway in Bangkok during a storm, causing minor injuries to some passengers.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Qantas_Flight_1\" title=\"Qantas Flight 1\">Qantas Flight 1</a> overrun a runway in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok</a> during a storm, causing minor injuries to some passengers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qantas_Flight_1\" title=\"Qantas Flight 1\">Qantas Flight 1</a> overrun a runway in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok</a> during a storm, causing minor injuries to some passengers.", "links": [{"title": "Qantas Flight 1", "link": "https://wikipedia.org/wiki/Qantas_Flight_1"}, {"title": "Bangkok", "link": "https://wikipedia.org/wiki/Bangkok"}]}, {"year": "2004", "text": "Over 3,000 people die in Haiti after Hurricane <PERSON> produces massive flooding and mudslides.", "html": "2004 - Over 3,000 people die in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> after <a href=\"https://wikipedia.org/wiki/Hurricane_Jeanne\" title=\"Hurricane Jeanne\">Hurricane Jeanne</a> produces massive flooding and mudslides.", "no_year_html": "Over 3,000 people die in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> after <a href=\"https://wikipedia.org/wiki/Hurricane_Jeanne\" title=\"Hurricane Jeanne\">Hurricane Jeanne</a> produces massive flooding and mudslides.", "links": [{"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "Hurricane Jeanne", "link": "https://wikipedia.org/wiki/Hurricane_Jeanne"}]}, {"year": "2008", "text": "<PERSON><PERSON> kills ten people at a school in Finland before committing suicide.", "html": "2008 - <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Kauhajoki_school_shooting\" title=\"Kauhajoki school shooting\">kills ten people</a> at a school in Finland before committing suicide.", "no_year_html": "<PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Kauhajoki_school_shooting\" title=\"Kauhajoki school shooting\">kills ten people</a> at a school in Finland before committing suicide.", "links": [{"title": "Kauhajoki school shooting", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_school_shooting"}]}, {"year": "2010", "text": "<PERSON> becomes the first woman to be executed by the U.S. state of Virginia since 1912, and the first woman in the state to be executed by lethal injection.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to be executed by the U.S. state of <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> since 1912, and the first woman in the state to be executed by <a href=\"https://wikipedia.org/wiki/Lethal_injection\" title=\"Lethal injection\">lethal injection</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to be executed by the U.S. state of <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> since 1912, and the first woman in the state to be executed by <a href=\"https://wikipedia.org/wiki/Lethal_injection\" title=\"Lethal injection\">lethal injection</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "Lethal injection", "link": "https://wikipedia.org/wiki/Lethal_injection"}]}, {"year": "2013", "text": "Twenty-five people are killed after Typhoon <PERSON><PERSON> passes Hong Kong and China.", "html": "2013 - Twenty-five people are killed after <a href=\"https://wikipedia.org/wiki/Typhoon_Usagi_(2013)\" title=\"Typhoon Usagi (2013)\">Typhoon Usagi</a> passes Hong Kong and China.", "no_year_html": "Twenty-five people are killed after <a href=\"https://wikipedia.org/wiki/Typhoon_Usagi_(2013)\" title=\"Typhoon Usagi (2013)\">Typhoon Usagi</a> passes Hong Kong and China.", "links": [{"title": "Typhoon Usagi (2013)", "link": "https://wikipedia.org/wiki/Typhoon_Usagi_(2013)"}]}, {"year": "2019", "text": "Twenty people die on the first of two days of rioting in Papua and West Papua over an alleged racist incident.", "html": "2019 - Twenty people die on the first of two days of <a href=\"https://wikipedia.org/wiki/2019_Papua_protests\" title=\"2019 Papua protests\">rioting</a> in <a href=\"https://wikipedia.org/wiki/Papua_(province)\" title=\"Papua (province)\">Papua</a> and <a href=\"https://wikipedia.org/wiki/West_Papua_(province)\" title=\"West Papua (province)\">West Papua</a> over an alleged racist incident.", "no_year_html": "Twenty people die on the first of two days of <a href=\"https://wikipedia.org/wiki/2019_Papua_protests\" title=\"2019 Papua protests\">rioting</a> in <a href=\"https://wikipedia.org/wiki/Papua_(province)\" title=\"Papua (province)\">Papua</a> and <a href=\"https://wikipedia.org/wiki/West_Papua_(province)\" title=\"West Papua (province)\">West Papua</a> over an alleged racist incident.", "links": [{"title": "2019 Papua protests", "link": "https://wikipedia.org/wiki/2019_Papua_protests"}, {"title": "Papua (province)", "link": "https://wikipedia.org/wiki/Papua_(province)"}, {"title": "West Papua (province)", "link": "https://wikipedia.org/wiki/West_Papua_(province)"}]}, {"year": "2020", "text": "A grand jury in Kentucky declines to indict three police officers for the shooting death of <PERSON><PERSON><PERSON><PERSON> in a drug raid gone wrong, leading to nationwide protests in the U.S.", "html": "2020 - A <a href=\"https://wikipedia.org/wiki/Grand_jury\" title=\"Grand jury\">grand jury</a> in <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a> declines to indict three police officers for the shooting death of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in a drug raid gone wrong, leading to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_protests\" title=\"<PERSON><PERSON><PERSON><PERSON> protests\">nationwide protests in the U.S.</a>", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Grand_jury\" title=\"Grand jury\">grand jury</a> in <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a> declines to indict three police officers for the shooting death of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in a drug raid gone wrong, leading to <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_<PERSON>_protests\" title=\"<PERSON><PERSON><PERSON><PERSON> protests\">nationwide protests in the U.S.</a>", "links": [{"title": "Grand jury", "link": "https://wikipedia.org/wiki/Grand_jury"}, {"title": "Kentucky", "link": "https://wikipedia.org/wiki/Kentucky"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> protests", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_protests"}]}, {"year": "2022", "text": "Voting begins in the five-day sham annexation referendums in Russian-occupied Ukraine, leading to Russian annexation of Donetsk, Kherson, Luhansk and Zaporizhzhia oblasts.", "html": "2022 - Voting begins in the five-day <a href=\"https://wikipedia.org/wiki/Election#Shams\" title=\"Election\">sham</a> <a href=\"https://wikipedia.org/wiki/2022_annexation_referendums_in_Russian-occupied_Ukraine\" title=\"2022 annexation referendums in Russian-occupied Ukraine\">annexation referendums in Russian-occupied Ukraine</a>, leading to <a href=\"https://wikipedia.org/wiki/Russian_annexation_of_Donetsk,_Kherson,_Luhansk_and_Zaporizhzhia_oblasts\" title=\"Russian annexation of Donetsk, Kherson, Luhansk and Zaporizhzhia oblasts\">Russian annexation of Donetsk, Kherson, Luhansk and Zaporizhzhia oblasts</a>.", "no_year_html": "Voting begins in the five-day <a href=\"https://wikipedia.org/wiki/Election#Shams\" title=\"Election\">sham</a> <a href=\"https://wikipedia.org/wiki/2022_annexation_referendums_in_Russian-occupied_Ukraine\" title=\"2022 annexation referendums in Russian-occupied Ukraine\">annexation referendums in Russian-occupied Ukraine</a>, leading to <a href=\"https://wikipedia.org/wiki/Russian_annexation_of_Donetsk,_Kherson,_Luhansk_and_Zaporizhzhia_oblasts\" title=\"Russian annexation of Donetsk, Kherson, Luhansk and Zaporizhzhia oblasts\">Russian annexation of Donetsk, Kherson, Luhansk and Zaporizhzhia oblasts</a>.", "links": [{"title": "Election", "link": "https://wikipedia.org/wiki/Election#Shams"}, {"title": "2022 annexation referendums in Russian-occupied Ukraine", "link": "https://wikipedia.org/wiki/2022_annexation_referendums_in_Russian-occupied_Ukraine"}, {"title": "Russian annexation of Donetsk, Kherson, Luhansk and Zaporizhzhia oblasts", "link": "https://wikipedia.org/wiki/Russian_annexation_of_Donetsk,_Kherson,_Luhansk_and_Zaporizhzhia_oblasts"}]}, {"year": "2024", "text": "Israel launches airstrikes against Hezbollah targets in Lebanon, killing more than 490 people.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches <a href=\"https://wikipedia.org/wiki/September_2024_Israeli_attacks_against_Lebanon#23_September_in_Lebanon\" title=\"September 2024 Israeli attacks against Lebanon\">airstrikes</a> against <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a> targets in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, killing more than 490 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches <a href=\"https://wikipedia.org/wiki/September_2024_Israeli_attacks_against_Lebanon#23_September_in_Lebanon\" title=\"September 2024 Israeli attacks against Lebanon\">airstrikes</a> against <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a> targets in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, killing more than 490 people.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "September 2024 Israeli attacks against Lebanon", "link": "https://wikipedia.org/wiki/September_2024_Israeli_attacks_against_Lebanon#23_September_in_Lebanon"}, {"title": "Hezbollah", "link": "https://wikipedia.org/wiki/Hezbollah"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}]}], "Births": [{"year": "63 BC", "text": "<PERSON>, Roman emperor (d. 14 AD)", "html": "63 BC - 63 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Augustus</a>, Roman emperor (d. 14 AD)", "no_year_html": "63 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Augustus</a>, Roman emperor (d. 14 AD)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}]}, {"year": "1158", "text": "<PERSON>, Duke of Brittany (d. 1186)", "html": "1158 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (d. 1186)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (d. 1186)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1215", "text": "<PERSON><PERSON><PERSON>, Mongolian emperor (d. 1294)", "html": "1215 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1294)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1294)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1495", "text": "<PERSON><PERSON><PERSON> <PERSON> of Imereti, King of Imereti (d. 1565)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Imereti\" title=\"<PERSON><PERSON><PERSON> III of Imereti\"><PERSON><PERSON><PERSON> <PERSON> of Imereti</a>, King of Imereti (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Imereti\" title=\"<PERSON><PERSON><PERSON> III of Imereti\"><PERSON><PERSON><PERSON> <PERSON> of Imereti</a>, King of Imereti (d. 1565)", "links": [{"title": "Bagrat III of Imereti", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Imereti"}]}, {"year": "1597", "text": "<PERSON>, Catholic cardinal (d. 1679)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1597%E2%80%931679)\" title=\"<PERSON> (1597-1679)\"><PERSON></a>, Catholic cardinal (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1597%E2%80%931679)\" title=\"<PERSON> (1597-1679)\"><PERSON></a>, Catholic cardinal (d. 1679)", "links": [{"title": "<PERSON> (1597-1679)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1597%E2%80%931679)"}]}, {"year": "1598", "text": "<PERSON><PERSON><PERSON>, Italian wife of <PERSON>, Holy Roman Emperor (d. 1655)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Gonzaga_(1598%E2%80%931655)\" title=\"<PERSON><PERSON><PERSON> (1598-1655)\"><PERSON><PERSON><PERSON></a>, Italian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(1598%E2%80%931655)\" title=\"<PERSON><PERSON><PERSON> (1598-1655)\"><PERSON><PERSON><PERSON></a>, Italian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1655)", "links": [{"title": "<PERSON><PERSON><PERSON> (1598-1655)", "link": "https://wikipedia.org/wiki/Eleonora_Gonzaga_(1598%E2%80%931655)"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1642", "text": "<PERSON>, Italian violinist and composer (d. 1678)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "<PERSON>, English politician, Governor of the Province of Massachusetts Bay (d. 1720)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts\" title=\"List of colonial governors of Massachusetts\">Governor of the Province of Massachusetts Bay</a> (d. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts\" title=\"List of colonial governors of Massachusetts\">Governor of the Province of Massachusetts Bay</a> (d. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Massachusetts", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts"}]}, {"year": "1650", "text": "<PERSON>, English bishop and theologian (d. 1726)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and theologian (d. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and theologian (d. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON> of Spain (d. 1759)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Spain\" class=\"mw-redirect\" title=\"Ferdinand VI of Spain\"><PERSON> VI of Spain</a> (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferdinand_VI_of_Spain\" class=\"mw-redirect\" title=\"Ferdinand VI of Spain\"><PERSON> VI of Spain</a> (d. 1759)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Ferdinand_VI_of_Spain"}]}, {"year": "1740", "text": "Empress <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1813)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>\" title=\"Empress <PERSON>-Sakura<PERSON>chi\">Empress <PERSON></a> of Japan (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>\" title=\"Empress Go-Sakura<PERSON>chi\">Empress <PERSON></a> of Japan (d. 1813)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1756", "text": "<PERSON>, Scottish engineer (d. 1836)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (d. 1840)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dka<PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dka<PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1840)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dkaku"}]}, {"year": "1778", "text": "<PERSON>, Argentinian journalist, lawyer, and politician (d. 1811)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian journalist, lawyer, and politician (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian journalist, lawyer, and politician (d. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "Princess <PERSON><PERSON> of Saxe-Coburg-Saalfeld (d. 1860)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_of_Saxe-Coburg-Saalfeld\" title=\"Princess <PERSON><PERSON> of Saxe-Coburg-Saalfeld\">Princess <PERSON><PERSON> of Saxe-Coburg-Saalfeld</a> (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_of_Saxe-Coburg-Saalfeld\" title=\"Princess <PERSON><PERSON> of Saxe-Coburg-Saalfeld\">Princess <PERSON><PERSON> of Saxe-Coburg-Saalfeld</a> (d. 1860)", "links": [{"title": "Princess <PERSON><PERSON> of Saxe-Coburg-Saalfeld", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_of_Saxe-Coburg-Saalfeld"}]}, {"year": "1791", "text": "<PERSON>, German astronomer and academic (d. 1865)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, German soldier and author (d. 1813)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, German soldier and author (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, German soldier and author (d. 1813)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_K%C3%B6<PERSON>_(author)"}]}, {"year": "1800", "text": "<PERSON>, American author and academic (d. 1873)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON><PERSON><PERSON>, French physicist and academic (d. 1896)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Hippolyte_Fizeau\" title=\"Hippolyte Fizeau\"><PERSON><PERSON><PERSON></a>, French physicist and academic (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hippolyte_Fizeau\" title=\"Hippolyte Fizeau\"><PERSON><PERSON><PERSON></a>, French physicist and academic (d. 1896)", "links": [{"title": "Hippolyte <PERSON>au", "link": "https://wikipedia.org/wiki/Hippolyte_Fizeau"}]}, {"year": "1823", "text": "<PERSON>, English-Australian politician, 13th Premier of South Australia (d. 1902)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1902)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1838", "text": "<PERSON>, American journalist and activist (d. 1927)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Victoria_Woodhull\" title=\"Victoria Woodhull\"><PERSON></a>, American journalist and activist (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Woodhull\" title=\"Victoria Woodhull\"><PERSON></a>, American journalist and activist (d. 1927)", "links": [{"title": "<PERSON>hull", "link": "https://wikipedia.org/wiki/Victoria_Woodhull"}]}, {"year": "1851", "text": "<PERSON>, American mathematician and astronomer (d. 1930)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and astronomer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and astronomer (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, American painter and academic (d. 1917)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, American physician and surgeon (d. 1922)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "Princess <PERSON> of Saxe-Meiningen (d. 1923)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Princess_Marie_<PERSON>_of_Saxe-Meiningen\" title=\"Princess <PERSON> of Saxe-Meiningen\">Princess <PERSON> of Saxe-Meiningen</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Saxe-Meiningen\" title=\"Princess <PERSON> of Saxe-Meiningen\">Princess <PERSON> of Saxe-Meiningen</a> (d. 1923)", "links": [{"title": "Princess <PERSON> of Saxe-Meiningen", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_<PERSON>_Saxe-Meiningen"}]}, {"year": "1861", "text": "<PERSON>, German engineer and businessman, founded Robert Bosch GmbH (d. 1942)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Robert <PERSON> GmbH\">Robert <PERSON> GmbH</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Robert <PERSON> GmbH\">Robert <PERSON> GmbH</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Robert <PERSON> GmbH", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American author and activist (d. 1954)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON>_<PERSON>rrell"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, Finnish painter (d. 1933)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish painter (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish painter (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ek<PERSON>_<PERSON>n"}]}, {"year": "1865", "text": "<PERSON>, Hungarian-English author and playwright (d. 1947)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English author and playwright (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English author and playwright (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, French model and painter (d. 1938)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French model and painter (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French model and painter (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American teacher, musicologist, and folklorist (d. 1948)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher, musicologist, and folklorist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher, musicologist, and folklorist (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American cook and typhoid carrier (d. 1938)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cook and <a href=\"https://wikipedia.org/wiki/Typhoid\" class=\"mw-redirect\" title=\"Typhoid\">typhoid</a> carrier (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cook and <a href=\"https://wikipedia.org/wiki/Typhoid\" class=\"mw-redirect\" title=\"Typhoid\">typhoid</a> carrier (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Typhoid", "link": "https://wikipedia.org/wiki/Typhoid"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Israeli rabbi and scholar (d. 1968)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli rabbi and scholar (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli rabbi and scholar (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1880", "text": "<PERSON>, 1st Baron <PERSON>, Scottish biologist, physician, and politician, Nobel Prize laureate (d. 1971)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish biologist, physician, and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish biologist, physician, and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1971)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>-<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1889", "text": "<PERSON>, American journalist and publisher, co-founded The New Republic (d. 1974)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/The_New_Republic\" title=\"The New Republic\">The New Republic</a></i> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/The_New_Republic\" title=\"The New Republic\">The New Republic</a></i> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The New Republic", "link": "https://wikipedia.org/wiki/The_New_Republic"}]}, {"year": "1890", "text": "<PERSON>, German general (d. 1957)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Russian architect and engineer (d. 1975)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian architect and engineer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian architect and engineer (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American baseball player (d. 1985)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Belgian painter (d. 1994)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Canadian-American actor and singer (d. 1984)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and singer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Australian journalist and politician (d. 1977)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American lawyer and judge, 59th Attorney General of the United States (d. 1977)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge, 59th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_the_United_States\" class=\"mw-redirect\" title=\"Attorney General of the United States\">Attorney General of the United States</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge, 59th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_the_United_States\" class=\"mw-redirect\" title=\"Attorney General of the United States\">Attorney General of the United States</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Attorney General of the United States", "link": "https://wikipedia.org/wiki/Attorney_General_of_the_United_States"}]}, {"year": "1899", "text": "<PERSON>, American sculptor (d. 1988)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English soldier (d. 2009)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_sailor)\" title=\"<PERSON> (Royal Navy sailor)\"><PERSON></a>, English soldier (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_sailor)\" title=\"<PERSON> (Royal Navy sailor)\"><PERSON></a>, English soldier (d. 2009)", "links": [{"title": "<PERSON> (Royal Navy sailor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_sailor)"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Czech poet and journalist, Nobel Prize laureate (d. 1986)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech poet and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech poet and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1902", "text": "<PERSON>, Chinese mathematician and academic (d. 2003)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Su_Buqing\" title=\"Su Buqing\"><PERSON></a>, Chinese mathematician and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Su_Buqing\" title=\"Su Buqing\"><PERSON></a>, Chinese mathematician and academic (d. 2003)", "links": [{"title": "Su Buqing", "link": "https://wikipedia.org/wiki/Su_Buqing"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Australian rugby league player and coach (d. 1957)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player and coach (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player and coach (d. 1957)", "links": [{"title": "Cec <PERSON>field", "link": "https://wikipedia.org/wiki/Cec_Fifield"}]}, {"year": "1904", "text": "<PERSON>, English-Australian rugby league player, coach, and administrator (d. 1966)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian rugby league player, coach, and administrator (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian rugby league player, coach, and administrator (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Canadian diplomat, High Commission of Canada in the United Kingdom (d. 1995)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Canadian diplomat, <a href=\"https://wikipedia.org/wiki/High_Commission_of_Canada_in_the_United_Kingdom\" class=\"mw-redirect\" title=\"High Commission of Canada in the United Kingdom\">High Commission of Canada in the United Kingdom</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Canadian diplomat, <a href=\"https://wikipedia.org/wiki/High_Commission_of_Canada_in_the_United_Kingdom\" class=\"mw-redirect\" title=\"High Commission of Canada in the United Kingdom\">High Commission of Canada in the United Kingdom</a> (d. 1995)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_(diplomat)"}, {"title": "High Commission of Canada in the United Kingdom", "link": "https://wikipedia.org/wiki/High_Commission_of_Canada_in_the_United_Kingdom"}]}, {"year": "1907", "text": "<PERSON>, American singer-songwriter and pianist (d. 1958)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bradshaw\"><PERSON></a>, American singer-songwriter and pianist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bradshaw\"><PERSON></a>, American singer-songwriter and pianist (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, French journalist and author (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Duke of Braganza (d. 1976)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Braganza\" title=\"<PERSON><PERSON>, Duke of Braganza\"><PERSON><PERSON>, Duke of Braganza</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Braganza\" title=\"<PERSON><PERSON>, Duke of Braganza\"><PERSON><PERSON>, Duke of Braganza</a> (d. 1976)", "links": [{"title": "<PERSON><PERSON>, Duke of Braganza", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Braganza"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Indian poet, academic, and politician (d. 1974)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, academic, and politician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, academic, and politician (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Kosovo-Albanian composer and conductor (d. 1991)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kosovo-Albanian composer and conductor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kosovo-Albanian composer and conductor (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>renc_Antoni"}]}, {"year": "1910", "text": "<PERSON>, Swiss anthroposophist and author (d. 2009)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss anthroposophist and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss anthroposophist and author (d. 2009)", "links": [{"title": "Jakob <PERSON>", "link": "https://wikipedia.org/wiki/Jakob_Streit"}]}, {"year": "1911", "text": "<PERSON>, American lawyer and politician (d. 2003)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (d. 2003)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Pakistani linguist, author, and critic (d. 2005)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Pakistani linguist, author, and critic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Pakistani linguist, author, and critic (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American sculptor and educator (d. 1980)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor and educator (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor and educator (d. 1980)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish painter and sculptor (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish painter and sculptor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish painter and sculptor (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American flute player and educator (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American flute player and educator (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American flute player and educator (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Italian academic and politician, 39th Prime Minister of Italy (d. 1978)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian academic and politician, 39th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian academic and politician, 39th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Moro"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1917", "text": "<PERSON>, Mexican <PERSON><PERSON><PERSON>, film actor, and folk icon (d. 1984)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/El_Santo\" title=\"El Santo\">El Santo</a>, Mexican <PERSON><PERSON><PERSON>, film actor, and folk icon (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Santo\" title=\"El Santo\">El Santo</a>, Mexican <PERSON><PERSON><PERSON>, film actor, and folk icon (d. 1984)", "links": [{"title": "El Santo", "link": "https://wikipedia.org/wiki/El_Santo"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Indian chemist (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chatterjee\"><PERSON><PERSON></a>, Indian chemist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chatterjee\"><PERSON><PERSON></a>, Indian chemist (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor, singer, director, and producer (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, director, and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, director, and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Egyptian journalist (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian journalist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian journalist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Estonian-Danish historian and author (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Danish historian and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Danish historian and author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lk"}]}, {"year": "1924", "text": "<PERSON>, Nicaraguan journalist and publisher (d. 1978)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Chamorro_Cardenal\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan journalist and publisher (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Chamorro_Cardenal\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan journalist and publisher (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Chamorro_Cardenal"}]}, {"year": "1925", "text": "<PERSON>, English historian and scholar (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ett\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English historian and scholar (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>witchett\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English historian and scholar (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, French toy maker, created the Etch A Sketch (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French toy maker, created the <a href=\"https://wikipedia.org/wiki/Etch_A_Sketch\" title=\"Etch A Sketch\">Etch A Sketch</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French toy maker, created the <a href=\"https://wikipedia.org/wiki/Etch_A_Sketch\" title=\"Etch A Sketch\">Etch A Sketch</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}, {"title": "Etch A Sketch", "link": "https://wikipedia.org/wiki/Etch_A_Sketch"}]}, {"year": "1926", "text": "<PERSON>, American saxophonist and composer (d. 1967)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American saxophonist and composer (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, American saxophonist and composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, American saxophonist and composer (d. 2011)", "links": [{"title": "<PERSON> (jazz musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)"}]}, {"year": "1928", "text": "<PERSON>, American journalist and actor (d. 1995)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Pakistani poet and songwriter (d. 1996)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani poet and songwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani poet and songwriter (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Northern Irish actor (d. 1987)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish actor (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American singer-songwriter, pianist, and actor (d. 2004)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American businessman, founded CBGB (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/CBGB\" title=\"CBGB\">CBGB</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/CBGB\" title=\"CBGB\">CBGB</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "CBGB", "link": "https://wikipedia.org/wiki/CBGB"}]}, {"year": "1931", "text": "<PERSON>, American author and illustrator (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian educator and politician (d. 2004)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, German footballer and manager", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fler\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fler\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_Ke%C3%9Fler"}]}, {"year": "1933", "text": "<PERSON>, American immunologist and academic (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American immunologist and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American immunologist and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Swedish journalist, author, and playwright (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Swedish journalist, author, and playwright (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, Swedish journalist, author, and playwright (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Indian actor", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>\" title=\"Pre<PERSON>\"><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>\" title=\"Pre<PERSON>\"><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>pra"}]}, {"year": "1935", "text": "<PERSON>, American soul-jazz singer and pianist (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul-jazz singer and pianist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul-jazz singer and pianist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Les_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English-Australian footballer, cricketer, and manager (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian footballer, cricketer, and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian footballer, cricketer, and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English footballer (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Peruvian lawyer and politician, 91st President of Peru (d. 2006)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Valent%C3%ADn_Paniagua\" title=\"Valentín Paniagua\"><PERSON><PERSON><PERSON></a>, Peruvian lawyer and politician, 91st <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valent%C3%ADn_Paniagua\" title=\"Valentín Paniagua\"><PERSON><PERSON><PERSON></a>, Peruvian lawyer and politician, 91st <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON>ín <PERSON>", "link": "https://wikipedia.org/wiki/Valent%C3%ADn_Paniagua"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss skier", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Syl<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>yl<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>yl<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Jordanian physician, general, and politician (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Tareq_Suheimat\" title=\"Tareq Suheimat\"><PERSON><PERSON><PERSON></a>, Jordanian physician, general, and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tareq_Suheimat\" title=\"Tareq Suheimat\"><PERSON><PERSON><PERSON></a>, Jordanian physician, general, and politician (d. 2014)", "links": [{"title": "Tareq <PERSON>", "link": "https://wikipedia.org/wiki/Tareq_Suheimat"}]}, {"year": "1937", "text": "<PERSON>, Canadian author and translator", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American businessman (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman (d. 2024)", "links": [{"title": "Arie L<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, German-French actress (d. 1982)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-French actress (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-French actress (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English cricketer and journalist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1988)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Baroness <PERSON>, English politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "links": [{"title": "<PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American businessman", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Brazilian lawyer and politician, 25th Vice President of Brazil", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Brazil\" title=\"Vice President of Brazil\">Vice President of Brazil</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Brazil\" title=\"Vice President of Brazil\">Vice President of Brazil</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Brazil", "link": "https://wikipedia.org/wiki/Vice_President_of_Brazil"}]}, {"year": "1940", "text": "<PERSON>, Australian rugby player and water polo player (d. 2011)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and water polo player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and water polo player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American activist and author, co-founded the Black Guerrilla Family (d. 1971)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Black_Panther)\" class=\"mw-redirect\" title=\"<PERSON> (Black Panther)\"><PERSON></a>, American activist and author, co-founded the <a href=\"https://wikipedia.org/wiki/Black_Guerrilla_Family\" title=\"Black Guerrilla Family\">Black Guerrilla Family</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Black_Panther)\" class=\"mw-redirect\" title=\"<PERSON> (Black Panther)\"><PERSON></a>, American activist and author, co-founded the <a href=\"https://wikipedia.org/wiki/Black_Guerrilla_Family\" title=\"Black Guerrilla Family\">Black Guerrilla Family</a> (d. 1971)", "links": [{"title": "<PERSON> (Black Panther)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Black_Panther)"}, {"title": "Black Guerrilla Family", "link": "https://wikipedia.org/wiki/Black_Guerrilla_Family"}]}, {"year": "1941", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Puerto Rican-American businesswoman and politician, 12th Secretary of State of Puerto Rico", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Sila_Mar%C3%ADa_Calder%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American businesswoman and politician, 12th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Puerto_Rico\" title=\"Secretary of State of Puerto Rico\">Secretary of State of Puerto Rico</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sila_Mar%C3%ADa_Calder%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American businesswoman and politician, 12th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Puerto_Rico\" title=\"Secretary of State of Puerto Rico\">Secretary of State of Puerto Rico</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sila_Mar%C3%ADa_Calder%C3%B3n"}, {"title": "Secretary of State of Puerto Rico", "link": "https://wikipedia.org/wiki/Secretary_of_State_of_Puerto_Rico"}]}, {"year": "1942", "text": "<PERSON>, Baron <PERSON> of Dalston, Scottish scholar and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Dalston\" title=\"<PERSON>, Baron <PERSON> of Dalston\"><PERSON>, Baron <PERSON> of Dalston</a>, Scottish scholar and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Dalston\" title=\"<PERSON>, Baron <PERSON> of Dalston\"><PERSON>, Baron <PERSON> of Dalston</a>, Scottish scholar and politician", "links": [{"title": "<PERSON>, Baron <PERSON> of Dalston", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Dalston"}]}, {"year": "1942", "text": "<PERSON>, Australian cricketer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Spanish singer-songwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American football player and coach (d. 2021)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Scottish-Australian singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Russian politician and diplomat, Russian Minister of Foreign Affairs", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)\" title=\"Minister of Foreign Affairs (Russia)\">Russian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)\" title=\"Minister of Foreign Affairs (Russia)\">Russian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Russia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)"}]}, {"year": "1945", "text": "<PERSON>, English rugby player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Old\" title=\"<PERSON> Old\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor, singer, author, and activist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, author, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, author, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Austrian politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, French economist and journalist (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and journalist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Baroness <PERSON>, English politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, English politician", "links": [{"title": "<PERSON><PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/Gen<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Bosnian singer-songwriter (d. 2001)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Da<PERSON>in_Popovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian singer-songwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Popovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian singer-songwriter (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Davorin_Popovi%C4%87"}]}, {"year": "1946", "text": "<PERSON>, Canadian director, producer, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian ice hockey player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American drummer and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_(drummer)"}]}, {"year": "1948", "text": "<PERSON>, American guitarist (d. 2013)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Trinidadian-English actress, academic, and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian-English actress, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian-English actress, academic, and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nas"}]}, {"year": "1950", "text": "<PERSON>, American saxophonist and educator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American guitarist and songwriter (d. 2012)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>_<PERSON>d\" title=\"<PERSON><PERSON><PERSON>ae<PERSON>wad\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d\" title=\"<PERSON><PERSON><PERSON> Gaekwad\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d"}]}, {"year": "1952", "text": "<PERSON>, American baseball player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Korean musician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean musician", "links": [{"title": "<PERSON>o", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>o"}]}, {"year": "1952", "text": "<PERSON>, American baseball player and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1953", "text": "<PERSON>, English journalist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor (d. 1996)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor (d. 1996)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1954", "text": "<PERSON><PERSON>, English lawyer and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>erie <PERSON>\"><PERSON><PERSON></a>, English lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>erie <PERSON>\"><PERSON><PERSON></a>, English lawyer and academic", "links": [{"title": "Cherie <PERSON>", "link": "https://wikipedia.org/wiki/Cherie_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American author, actor, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian cricketer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Italian footballer (d. 2020)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Bahraini singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bahraini singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bahraini singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Cuban-American baseball player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American football player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American golfer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor, singer, and voice artist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and voice artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and voice artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>-<PERSON><PERSON>, English author and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Dutch mixed martial artist and wrestler (d. 2014)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mixed martial artist and wrestler (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mixed martial artist and wrestler (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian rugby league player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan_(rugby_league)"}]}, {"year": "1959", "text": "<PERSON>, American actress (d. 2014)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>e%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elizabeth_Pe%C3%B1a"}]}, {"year": "1959", "text": "<PERSON>, English diplomat", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American wrestler", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Spanish race car driver", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American commander, pilot, and astronaut (d. 2003)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>c<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, pilot, and astronaut (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>c<PERSON>ool\" title=\"<PERSON>\"><PERSON></a>, American commander, pilot, and astronaut (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>c<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Scottish journalist (d. 2019)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Canadian actress, director, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Egyptian-Australian director, producer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-Australian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-Australian director, producer, and screenwriter", "links": [{"title": "Alex <PERSON>", "link": "https://wikipedia.org/wiki/Alex_Proyas"}]}, {"year": "1964", "text": "<PERSON>, Welsh footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clayton Blackmore\"><PERSON></a>, Welsh footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German-born Italian kayaker", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Idem\" title=\"Josefa Idem\"><PERSON><PERSON></a>, German-born Italian kayaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dem\" title=\"Josefa Idem\"><PERSON><PERSON></a>, German-born Italian kayaker", "links": [{"title": "Josefa Idem", "link": "https://wikipedia.org/wiki/Josefa_I<PERSON>m"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Inaba"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English director and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English biologist and academic", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American businessman and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American businessman and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American businessman and author", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1965", "text": "<PERSON>, Australian tennis player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress, model, fashion designer, and First Lady of the Turks and Caicos Islands", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress, model, fashion designer, and First Lady of the <a href=\"https://wikipedia.org/wiki/Turks_and_Caicos_Islands\" title=\"Turks and Caicos Islands\">Turks and Caicos Islands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress, model, fashion designer, and First Lady of the <a href=\"https://wikipedia.org/wiki/Turks_and_Caicos_Islands\" title=\"Turks and Caicos Islands\">Turks and Caicos Islands</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Turks and Caicos Islands", "link": "https://wikipedia.org/wiki/Turks_and_Caicos_Islands"}]}, {"year": "1967", "text": "<PERSON>, English footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, English actress and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Welsh politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, Welsh politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian ice hockey player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, French singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Czech footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rek\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rek\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Suchop%C3%A1rek"}]}, {"year": "1970", "text": "<PERSON>, Australian rugby player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and keyboard player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ani_DiFranco"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Pakistani cricketer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player and sportscaster (d. 2023)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American political aide, 30th White House Press Secretary", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political aide, 30th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political aide, 30th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "1972", "text": "<PERSON>, Belgian singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Zimbabwean cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, American rapper and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actor and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Argentinian pianist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Greek footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby league player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American wrestler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American rapper", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bone\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bone\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, South Korean badminton player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean badminton player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English journalist and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Irish rugby player, footballer, and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Irish rugby player, footballer, and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Irish rugby player, footballer, and coach", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_union)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Kazakhstani ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian singer-songwriter and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American actor and model", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ue\" title=\"<PERSON><PERSON>rdue\"><PERSON><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rdue\" title=\"<PERSON><PERSON> Pardue\"><PERSON><PERSON></a>, American actor and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ue"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Ukrainian boxer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian boxer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Italian rugby player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and pianist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American guitarist, drummer, and songwriter (d. 2013)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, drummer, and songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, drummer, and songwriter (d. 2013)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/F%C3%A1bio_Simpl%C3%ADcio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A1bio_Simpl%C3%ADcio\" title=\"F<PERSON>bio <PERSON>mplí<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A1bio_Simpl%C3%ADcio"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Fijian-Australian rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rugby,_born_1979)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rugby, born 1979)\"><PERSON><PERSON></a>, Fijian-Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rugby,_born_1979)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rugby, born 1979)\"><PERSON><PERSON></a>, Fijian-Australian rugby player", "links": [{"title": "<PERSON><PERSON> (rugby, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rugby,_born_1979)"}]}, {"year": "1981", "text": "<PERSON>, Dutch racing driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>-<PERSON>, English field hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field hockey player", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Estonian tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Mait_K%C3%BCnnap\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mai<PERSON>_K%C3%BCnnap\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mait_K%C3%BCnnap"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Canadian pornographic actress (d. 2017)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Shyla_Stylez\" title=\"Shyla Stylez\"><PERSON><PERSON><PERSON></a>, Canadian pornographic actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shyla_Stylez\" title=\"Shyla Stylez\"><PERSON><PERSON><PERSON></a>, Canadian pornographic actress (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shyla_Stylez"}]}, {"year": "1983", "text": "<PERSON>, American mixed martial artist and kick-boxer (d. 2013)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and kick-boxer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and kick-boxer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Dutch-American entertainer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American entertainer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (running back)", "link": "https://wikipedia.org/wiki/<PERSON>_(running_back)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, British actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Cush_Jumbo\" title=\"Cush Jumbo\"><PERSON><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cush_Jumbo\" title=\"Cush Jumbo\"><PERSON><PERSON></a>, British actress", "links": [{"title": "Cush Jumbo", "link": "https://wikipedia.org/wiki/Cush_Jumbo"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Luk%C3%A1%C5%A1_Ka%C5%A1par\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luk%C3%A1%C5%A1_Ka%C5%A1par\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luk%C3%A1%C5%A1_Ka%C5%A1par"}]}, {"year": "1985", "text": "<PERSON>, American comedian, actor, and television host", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American actor and singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Argentinian tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Juan_Mart%C3%ADn_del_<PERSON>tro\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_Mart%C3%ADn_del_Potro\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Mart%C3%ADn_del_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Swiss ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Lasalo\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Lasalo\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Scottish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1991", "text": "<PERSON>, South Korean singer and entertainer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\">Key</a>, South Korean singer and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\">Key</a>, South Korean singer and entertainer", "links": [{"title": "<PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/Key_(entertainer)"}]}, {"year": "1991", "text": "<PERSON>, American tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Mexican wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_G<PERSON>za\" title=\"<PERSON> G<PERSON>za\"><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_G<PERSON>\" title=\"<PERSON> G<PERSON>za\"><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer and entertainer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>oo\" class=\"mw-redirect\" title=\"<PERSON>oo\"><PERSON></a>, South Korean singer and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>oo\" class=\"mw-redirect\" title=\"<PERSON>joo\"><PERSON></a>, South Korean singer and entertainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-joo"}]}, {"year": "1994", "text": "<PERSON>, Chinese Actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Chinese Actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Chinese Actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1999", "text": "<PERSON>, Chinese singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chinese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chinese singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Taiwanese film director", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-lin\" title=\"<PERSON>-lin\"><PERSON>-<PERSON></a>, Taiwanese film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-lin\" title=\"<PERSON>-lin\"><PERSON>-<PERSON></a>, Taiwanese film director", "links": [{"title": "<PERSON>in", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-lin"}]}], "Deaths": [{"year": "788", "text": "<PERSON><PERSON><PERSON>, king of Northumbria", "html": "788 - <a href=\"https://wikipedia.org/wiki/%C3%86<PERSON><PERSON>_I_of_Northumbria\" title=\"<PERSON><PERSON><PERSON> I of Northumbria\"><PERSON><PERSON><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Northumbria\" class=\"mw-redirect\" title=\"Kingdom of Northumbria\">Northumbria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86<PERSON><PERSON>_I_of_Northumbria\" title=\"<PERSON><PERSON><PERSON> I of Northumbria\"><PERSON><PERSON><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Northumbria\" class=\"mw-redirect\" title=\"Kingdom of Northumbria\">Northumbria</a>", "links": [{"title": "<PERSON><PERSON><PERSON> I of Northumbria", "link": "https://wikipedia.org/wiki/%C3%86lf<PERSON>_I_of_Northumbria"}, {"title": "Kingdom of Northumbria", "link": "https://wikipedia.org/wiki/Kingdom_of_Northumbria"}]}, {"year": "965", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Arab poet (b. 915)", "html": "965 - <a href=\"https://wikipedia.org/wiki/Al-Mutanabbi\" title=\"Al-Mutanabbi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Arab poet (b. 915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Mutanabbi\" title=\"Al-Mutanabbi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Arab poet (b. 915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Mu<PERSON>bbi"}]}, {"year": "1193", "text": "<PERSON>, French knight", "html": "1193 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>l%C3%A9\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French knight", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>l%C3%A9\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French knight", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_Sabl%C3%A9"}]}, {"year": "1241", "text": "<PERSON><PERSON><PERSON>, Icelandic historian, poet, and politician (b. 1178)", "html": "1241 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic historian, poet, and politician (b. 1178)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic historian, poet, and politician (b. 1178)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ur<PERSON>on"}]}, {"year": "1253", "text": "<PERSON><PERSON><PERSON> I of Bohemia", "html": "1253 - <a href=\"https://wikipedia.org/wiki/Wenceslaus_I_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> I of Bohemia\"><PERSON><PERSON><PERSON> I of Bohemia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wenceslaus_I_of_Bohemia\" title=\"Wen<PERSON>laus I of Bohemia\"><PERSON><PERSON><PERSON> I of Bohemia</a>", "links": [{"title": "<PERSON><PERSON><PERSON> I of Bohemia", "link": "https://wikipedia.org/wiki/Wen<PERSON>laus_I_of_Bohemia"}]}, {"year": "1267", "text": "<PERSON> of Provence, countess regnant of Provence (b. 1234)", "html": "1267 - <a href=\"https://wikipedia.org/wiki/Beatrice_of_Provence\" title=\"Beatrice of Provence\"><PERSON> of Provence</a>, countess regnant of Provence (b. 1234)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Provence\" title=\"<PERSON> of Provence\"><PERSON> of Provence</a>, countess regnant of Provence (b. 1234)", "links": [{"title": "<PERSON> of Provence", "link": "https://wikipedia.org/wiki/Beatrice_of_Provence"}]}, {"year": "1386", "text": "<PERSON> of Wallachia", "html": "1386 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Wallachia\" title=\"<PERSON> of Wallachia\"><PERSON> of Wallachia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Wallachia\" title=\"<PERSON> of Wallachia\"><PERSON> of Wallachia</a>", "links": [{"title": "<PERSON> of Wallachia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Wallachia"}]}, {"year": "1390", "text": "<PERSON>, Duke of Lorraine (b. 1346)", "html": "1390 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1346)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1346)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Lorraine"}]}, {"year": "1448", "text": "<PERSON><PERSON><PERSON>, Duke of Cleves (b. 1373)", "html": "1448 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Cleves\" title=\"<PERSON><PERSON><PERSON>, Duke of Cleves\"><PERSON><PERSON><PERSON>, Duke of Cleves</a> (b. 1373)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Cleves\" title=\"<PERSON><PERSON><PERSON>, Duke of Cleves\"><PERSON><PERSON><PERSON>, Duke of Cleves</a> (b. 1373)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Cleves", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>_C<PERSON>"}]}, {"year": "1461", "text": "<PERSON>, Prince of Viana, King of Navarre (b. 1421)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Viana\" title=\"<PERSON>, Prince of Viana\"><PERSON>, Prince of Viana</a>, King of Navarre (b. 1421)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Viana\" title=\"<PERSON>, Prince of Viana\"><PERSON>, Prince of Viana</a>, King of Navarre (b. 1421)", "links": [{"title": "<PERSON>, Prince of Viana", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Viana"}]}, {"year": "1508", "text": "<PERSON> of Naples, queen consort of Hungary (b. 1457)", "html": "1508 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Naples\" title=\"Beatrice of Naples\"><PERSON> of Naples</a>, queen consort of Hungary (b. 1457)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Naples\" title=\"Beatrice of Naples\"><PERSON> of Naples</a>, queen consort of Hungary (b. 1457)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_of_Naples"}]}, {"year": "1535", "text": "<PERSON> of Saxe-Lauenburg (b. 1513)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"Catherine of Saxe-Lauenburg\">Catherine of Saxe-Lauenburg</a> (b. 1513)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"Catherine of Saxe-Lauenburg\">Catherine of Saxe-Lauenburg</a> (b. 1513)", "links": [{"title": "<PERSON> of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxe-Lauenburg"}]}, {"year": "1571", "text": "<PERSON>, English bishop (b. 1522)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1522)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1573", "text": "<PERSON><PERSON>, Japanese warlord (b. 1524)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese warlord (b. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese warlord (b. 1524)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON><PERSON>, French priest and poet (b. 1521)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/Pont<PERSON>_de_<PERSON>ard\" title=\"Pont<PERSON> de Tyard\"><PERSON><PERSON></a>, French priest and poet (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pont<PERSON>_de_<PERSON>ard\" title=\"Pontus de Tyard\"><PERSON><PERSON></a>, French priest and poet (b. 1521)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_de_<PERSON>ard"}]}, {"year": "1675", "text": "<PERSON><PERSON>, French author, founded the Académie française (b. 1603)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author, founded the <a href=\"https://wikipedia.org/wiki/Acad%C3%A9mie_fran%C3%A7aise\" class=\"mw-redirect\" title=\"Académie française\">Académie française</a> (b. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author, founded the <a href=\"https://wikipedia.org/wiki/Acad%C3%A9mie_fran%C3%A7aise\" class=\"mw-redirect\" title=\"Académie française\">Académie française</a> (b. 1603)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}, {"title": "Académie française", "link": "https://wikipedia.org/wiki/Acad%C3%A9mie_fran%C3%A7aise"}]}, {"year": "1728", "text": "<PERSON>, German jurist and philosopher (b. 1655)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and philosopher (b. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and philosopher (b. 1655)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, Dutch botanist and physician (b. 1668)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch botanist and physician (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch botanist and physician (b. 1668)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, English poet and playwright (b. 1703)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, Norwegian bishop and botanist (b. 1718)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian bishop and botanist (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian bishop and botanist (b. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, American lawyer and politician (b. 1723)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)\" title=\"<PERSON> (Continental Congress)\"><PERSON></a>, American lawyer and politician (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)\" title=\"<PERSON> (Continental Congress)\"><PERSON></a>, American lawyer and politician (b. 1723)", "links": [{"title": "<PERSON> (Continental Congress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)"}]}, {"year": "1835", "text": "<PERSON>, Italian composer (b. 1801)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON>, Canadian nun, founded the Sisters of Providence (b. 1800)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/%C3%89milie_Gamelin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian nun, founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_Providence_(Montreal)\" title=\"Sisters of Providence (Montreal)\">Sisters of Providence</a> (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mi<PERSON>_<PERSON>lin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian nun, founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_Providence_(Montreal)\" title=\"Sisters of Providence (Montreal)\">Sisters of Providence</a> (b. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89milie_<PERSON>lin"}, {"title": "Sisters of Providence (Montreal)", "link": "https://wikipedia.org/wiki/<PERSON>_of_Providence_(Montreal)"}]}, {"year": "1846", "text": "<PERSON>, English-Australian explorer (b. 1818)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian explorer (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian explorer (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, Uruguayan general and politician (b. 1764)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gervasio_Artigas\" title=\"<PERSON>io <PERSON>iga<PERSON>\"><PERSON></a>, Uruguayan general and politician (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gervasio_Artigas\" title=\"<PERSON>io Artigas\"><PERSON></a>, Uruguayan general and politician (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Gervasio_Artigas"}]}, {"year": "1870", "text": "<PERSON><PERSON>, French archaeologist and historian (b. 1803)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Prosper_M%C3%A9rim%C3%A9e\" title=\"Prosper <PERSON>\"><PERSON><PERSON></a>, French archaeologist and historian (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prosper_M%C3%A9rim%C3%A9e\" title=\"Prosper <PERSON>\"><PERSON><PERSON></a>, French archaeologist and historian (b. 1803)", "links": [{"title": "Pro<PERSON>", "link": "https://wikipedia.org/wiki/Prosper_M%C3%A9rim%C3%A9e"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician (b. 1786)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1786)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, French astronomer (b. 1823)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, French mathematician and astronomer (b. 1811)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Urb<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> V<PERSON>rier\"><PERSON><PERSON><PERSON></a>, French mathematician and astronomer (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Verrier\"><PERSON><PERSON><PERSON></a>, French mathematician and astronomer (b. 1811)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, English novelist, short story writer, and playwright (b. 1824)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English novelist, short story writer, and playwright (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English novelist, short story writer, and playwright (b. 1824)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, French artist (b. 1836)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American businessman, founded Rice University (b. 1816)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Rice\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Rice_University\" title=\"Rice University\">Rice University</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Rice_University\" title=\"Rice University\">Rice University</a> (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Rice University", "link": "https://wikipedia.org/wiki/Rice_University"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Argentinian general (b. 1825)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Donato_%C3%81l<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian general (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Donato_%C3%81l<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian general (b. 1825)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Donato_%C3%81<PERSON><PERSON><PERSON>"}]}, {"year": "1917", "text": "<PERSON>, German lieutenant and pilot (b. 1897)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pilot (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pilot (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Austrian-German chemist, physicist, and academic, Nobel Prize laureate (b. 1865)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German chemist, physicist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German chemist, physicist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Austrian neurologist and psychiatrist (b. 1856)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_Freud\" title=\"Sigmund Freud\"><PERSON><PERSON><PERSON></a>, Austrian neurologist and psychiatrist (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_Freud\" title=\"Sigmund Freud\"><PERSON><PERSON><PERSON></a>, Austrian neurologist and psychiatrist (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Mexican politician and diplomat, interim president, 1911 (b. 1863)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Francisco_Le%C3%B3n_de_la_Barra\" title=\"Francisco León de la Barra\"><PERSON></a>, Mexican politician and diplomat, interim president, 1911 (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Le%C3%B3n_de_la_Barra\" title=\"Francisco León de la Barra\"><PERSON></a>, Mexican politician and diplomat, interim president, 1911 (b. 1863)", "links": [{"title": "<PERSON> Barra", "link": "https://wikipedia.org/wiki/Francisco_Le%C3%B3n_de_la_Barra"}]}, {"year": "1940", "text": "<PERSON>, American businessman (b. 1869)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, English author, screenwriter, and producer (b. 1864)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author, screenwriter, and producer (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author, screenwriter, and producer (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lyn"}]}, {"year": "1944", "text": "<PERSON>, Swiss author and critic (b. 1875)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and critic (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and critic (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American basketball player and coach (b. 1892)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, German engineer (b. 1863)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German engineer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German engineer (b. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian publisher, lawyer, and politician (b. 1876)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian publisher, lawyer, and politician (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian publisher, lawyer, and politician (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Polish wrestler and strongman (b. 1879)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish wrestler and strongman (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish wrestler and strongman (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON> of Pietrelcina, Italian priest and saint (b. 1887)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Pietrelcina\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Pietrelcina\"><PERSON><PERSON> of Pietrelcina</a>, Italian priest and saint (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Pietrelcina\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Pietrelcina\"><PERSON><PERSON> of Pietrelcina</a>, Italian priest and saint (b. 1887)", "links": [{"title": "<PERSON><PERSON> of Pietrelcina", "link": "https://wikipedia.org/wiki/Pi<PERSON>_of_Pietrelcina"}]}, {"year": "1971", "text": "<PERSON>, American mathematician and topologist (b. 1888)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and topologist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, American mathematician and topologist (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Chilean poet and diplomat, Nobel Prize laureate (b. 1904)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean poet and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean poet and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1974", "text": "<PERSON>, American actor and comedian (b. 1905)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Arquette\" title=\"Cliff Arquette\"><PERSON></a>, American actor and comedian (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Arquette\" title=\"Cliff Arquette\"><PERSON></a>, American actor and comedian (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cliff_Arquette"}]}, {"year": "1974", "text": "<PERSON>, Scottish drummer (b. 1950)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, Scottish drummer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, Scottish drummer (b. 1950)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American baseball player (b. 1950)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>stock"}]}, {"year": "1979", "text": "<PERSON>, English actress (b. 1904)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "Chief <PERSON>, Canadian actor, author, and poet (b. 1899)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>\" title=\"Chief <PERSON>\">Chief <PERSON></a>, Canadian actor, author, and poet (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>\" title=\"Chief <PERSON>\">Chief <PERSON></a>, Canadian actor, author, and poet (b. 1899)", "links": [{"title": "Chief <PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor, dancer, choreographer, and director (b. 1927)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, choreographer, and director (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, choreographer, and director (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Hungarian-Serbian explorer and author (b. 1912)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Serbian explorer and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Serbian explorer and author (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tib<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Estonian poet and scholar (b. 1927)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and scholar (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and scholar (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American author and academic (b. 1918)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Glendon_Swarthout\" title=\"Glendon Swarthout\"><PERSON><PERSON></a>, American author and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glendon_Swarthout\" title=\"Glendon Swarthout\"><PERSON><PERSON></a>, American author and academic (b. 1918)", "links": [{"title": "<PERSON>don Swarthout", "link": "https://wikipedia.org/wiki/Glendon_Swarthout"}]}, {"year": "1992", "text": "<PERSON>, American general (b. 1892)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American golfer (b. 1916)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Barber\"><PERSON></a>, American golfer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American author and screenwriter (b. 1917)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French actress (b. 1900)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American author (b. 1906)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English footballer (b. 1909)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress (b. 1943)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Australian-American screenwriter and producer (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American screenwriter and producer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American screenwriter and producer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Mexican baseball player and manager (b. 1947)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Aurelio_Rodr%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican baseball player and manager (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurelio_Rodr%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican baseball player and manager (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurelio_Rodr%C3%ADguez"}]}, {"year": "2000", "text": "<PERSON>, American journalist and author (b. 1925)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American attorney and law professor (b. 1901)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and law professor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and law professor (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Welsh footballer (b. 1928)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)\" title=\"<PERSON> (footballer, born 1928)\"><PERSON></a>, Welsh footballer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)\" title=\"<PERSON> (footballer, born 1928)\"><PERSON></a>, Welsh footballer (b. 1928)", "links": [{"title": "<PERSON> (footballer, born 1928)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)"}]}, {"year": "2003", "text": "<PERSON>, Russian physician and journalist (b. 1937)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and journalist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and journalist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1918)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Puerto Rican activist (b. 1933)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_R%C3%ADos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican activist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_R%C3%ADos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican activist (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fili<PERSON>_<PERSON>_R%C3%ADos"}]}, {"year": "2006", "text": "<PERSON>, English trumpet player and composer (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player and composer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player and composer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American singer and guitarist (b. 1913)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Baker"}]}, {"year": "2008", "text": "<PERSON>, Australian journalist (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Australian journalist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Australian journalist (b. 1942)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American journalist and author (b. 1910)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American sailor and politician, United States Secretary of the Navy (b. 1918)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "2010", "text": "<PERSON>, Australian hunter and television host (b. 1941)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(documentary_maker)\" title=\"<PERSON> (documentary maker)\"><PERSON></a>, Australian hunter and television host (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(documentary_maker)\" title=\"<PERSON> (documentary maker)\"><PERSON></a>, Australian hunter and television host (b. 1941)", "links": [{"title": "<PERSON> (documentary maker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(documentary_maker)"}]}, {"year": "2012", "text": "<PERSON>, Canadian journalist and academic (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and academic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and academic (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian general and politician, 1st Minister of Defence for Russia (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, 1st <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Minister of Defence for Russia</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, 1st <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Minister of Defence for Russia</a> (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Defence (Russia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)"}]}, {"year": "2012", "text": "<PERSON>, Venezuelan baseball player and coach (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Venezuelan baseball player and coach (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Venezuelan baseball player and coach (b. 1941)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Roberto_Rodr%C3%ADguez_(baseball)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, South African boxer (b. 1966)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African boxer (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African boxer (b. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian businessman, founded <PERSON> the Record Man (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Record_Man\" title=\"<PERSON> the Record Man\"><PERSON> the Record Man</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Record_Man\" title=\"<PERSON> the Record Man\"><PERSON> the Record Man</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> the Record Man", "link": "https://wikipedia.org/wiki/<PERSON>_the_Record_Man"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Syrian colonel and politician (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>j\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian colonel and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian colonel and politician (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American captain, lawyer, and politician (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American botanist and immunologist (b. 1907)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and immunologist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and immunologist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player and coach (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach (b. 1943)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American anthropologist and biologist (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Irven_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American anthropologist and biologist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irven_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American anthropologist and biologist (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irven_DeVore"}]}, {"year": "2014", "text": "<PERSON>, American football player and wrestler (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American ice hockey player and referee (b. 1913)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Al_<PERSON>omi\" title=\"Al <PERSON>omi\"><PERSON></a>, American ice hockey player and referee (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_<PERSON>omi\" title=\"Al Suomi\"><PERSON></a>, American ice hockey player and referee (b. 1913)", "links": [{"title": "Al Suomi", "link": "https://wikipedia.org/wiki/<PERSON>_Suomi"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Indian monk and philosopher (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_Vidya)\" title=\"<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> Vidya)\"><PERSON><PERSON><PERSON></a>, Indian monk and philosopher (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_Vidya)\" title=\"<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> Vidya)\"><PERSON><PERSON><PERSON></a>, Indian monk and philosopher (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON> (A<PERSON>ha Vidya)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_Vidya)"}]}, {"year": "2018", "text": "<PERSON>, Hong Kong-American-British electrical engineer and physicist (b. 1933)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-American-British electrical engineer and physicist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-American-British electrical engineer and physicist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American film producer (b. 1940)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American author, journalist, and philanthropist (b.1942)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, journalist, and philanthropist (b.1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, journalist, and philanthropist (b.1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>,  French singer and actress (b. 1927)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Juliette_Gr%C3%A9co\" title=\"<PERSON>\"><PERSON></a>, French singer and actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juliette_Gr%C3%A9co\" title=\"<PERSON>\"><PERSON></a>, French singer and actress (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juliette_Gr%C3%A9co"}]}, {"year": "2021", "text": "<PERSON>, Australian businessman (b. 1941)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Australian businessman (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Australian businessman (b. 1941)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>(businessman)"}]}, {"year": "2021", "text": "<PERSON><PERSON>, Italian race car driver (b. 1933)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}