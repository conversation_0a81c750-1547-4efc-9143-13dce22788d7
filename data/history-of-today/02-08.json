{"date": "February 8", "url": "https://wikipedia.org/wiki/February_8", "data": {"Events": [{"year": "421", "text": "<PERSON><PERSON><PERSON> becomes co-emperor of the Western Roman Empire.", "html": "421 - <a href=\"https://wikipedia.org/wiki/Con<PERSON>ius_III\" title=\"<PERSON><PERSON>ius III\"><PERSON><PERSON><PERSON> III</a> becomes <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">co-emperor</a> of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_III\" title=\"<PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON> III</a> becomes <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">co-emperor</a> of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/Constantius_III"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "Western Roman Empire", "link": "https://wikipedia.org/wiki/Western_Roman_Empire"}]}, {"year": "1238", "text": "The Mongols burn the Russian city of Vladimir.", "html": "1238 - The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> burn the Russian city of <a href=\"https://wikipedia.org/wiki/Vladimir,_Russia\" title=\"Vladimir, Russia\">Vladimir</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> burn the Russian city of <a href=\"https://wikipedia.org/wiki/Vladimir,_Russia\" title=\"Vladimir, Russia\">Vladimir</a>.", "links": [{"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}, {"title": "Vladimir, Russia", "link": "https://wikipedia.org/wiki/Vladimir,_Russia"}]}, {"year": "1250", "text": "Seventh Crusade: Crusaders engage Ayyubid forces in the Battle of Al Mansurah.", "html": "1250 - <a href=\"https://wikipedia.org/wiki/Seventh_Crusade\" title=\"Seventh Crusade\">Seventh Crusade</a>: Crusaders engage <a href=\"https://wikipedia.org/wiki/Ayyubid_dynasty\" title=\"Ayyubid dynasty\">Ayyubid</a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Mansurah_(1250)\" title=\"Battle of Mansurah (1250)\">Battle of Al Mansurah</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seventh_Crusade\" title=\"Seventh Crusade\">Seventh Crusade</a>: Crusaders engage <a href=\"https://wikipedia.org/wiki/Ayyubid_dynasty\" title=\"Ayyubid dynasty\">Ayyubid</a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Mansurah_(1250)\" title=\"Battle of Mansurah (1250)\">Battle of Al Mansurah</a>.", "links": [{"title": "Seventh Crusade", "link": "https://wikipedia.org/wiki/Seventh_Crusade"}, {"title": "Ayyubid dynasty", "link": "https://wikipedia.org/wiki/Ayyubid_dynasty"}, {"title": "Battle of Mansurah (1250)", "link": "https://wikipedia.org/wiki/Battle_of_Mansurah_(1250)"}]}, {"year": "1347", "text": "The Byzantine civil war of 1341-47 ends with a power-sharing agreement between <PERSON> and <PERSON>.", "html": "1347 - The <a href=\"https://wikipedia.org/wiki/Byzantine_civil_war_of_1341%E2%80%9347\" class=\"mw-redirect\" title=\"Byzantine civil war of 1341-47\">Byzantine civil war of 1341-47</a> ends with a power-sharing agreement between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/John_V_Palaiologos\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Byzantine_civil_war_of_1341%E2%80%9347\" class=\"mw-redirect\" title=\"Byzantine civil war of 1341-47\">Byzantine civil war of 1341-47</a> ends with a power-sharing agreement between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/John_<PERSON>_<PERSON>ologos\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Byzantine civil war of 1341-47", "link": "https://wikipedia.org/wiki/Byzantine_civil_war_of_1341%E2%80%9347"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1587", "text": "<PERSON>, Queen of Scots is executed on suspicion of having been involved in the Babington Plot to murder her cousin, Queen <PERSON>.", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> is executed on suspicion of having been involved in the <a href=\"https://wikipedia.org/wiki/Babington_Plot\" title=\"Babington Plot\">Babington Plot</a> to murder her cousin, <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_England\" class=\"mw-redirect\" title=\"Elizabeth I of England\">Queen <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> is executed on suspicion of having been involved in the <a href=\"https://wikipedia.org/wiki/Babington_Plot\" title=\"Babington Plot\">Babington Plot</a> to murder her cousin, <a href=\"https://wikipedia.org/wiki/Elizabeth_I_of_England\" class=\"mw-redirect\" title=\"Elizabeth I of England\">Queen <PERSON></a>.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "Babington Plot", "link": "https://wikipedia.org/wiki/Babington_Plot"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1601", "text": "<PERSON>, 2nd Earl of Essex, unsuccessfully rebels against Queen <PERSON>.", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex\" title=\"<PERSON>, 2nd Earl of Essex\"><PERSON>, 2nd Earl of Essex</a>, <a href=\"https://wikipedia.org/wiki/Essex%27s_Rebellion\" title=\"Essex's Rebellion\">unsuccessfully rebels</a> against Queen <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex\" title=\"<PERSON>, 2nd Earl of Essex\"><PERSON>, 2nd Earl of Essex</a>, <a href=\"https://wikipedia.org/wiki/Essex%27s_Rebellion\" title=\"Essex's Rebellion\">unsuccessfully rebels</a> against Queen <PERSON>.", "links": [{"title": "<PERSON>, 2nd Earl of Essex", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex"}, {"title": "Essex's Rebellion", "link": "https://wikipedia.org/wiki/Essex%27s_Rebellion"}]}, {"year": "1693", "text": "The College of William & Mary in Williamsburg, Virginia, the second-oldest institution of higher education in the Thirteen Colonies, is granted a charter by King <PERSON> and Queen <PERSON>.", "html": "1693 - The <a href=\"https://wikipedia.org/wiki/College_of_William_%26_Mary\" title=\"College of William &amp; Mary\">College of William &amp; Mary</a> in <a href=\"https://wikipedia.org/wiki/Williamsburg,_Virginia\" title=\"Williamsburg, Virginia\">Williamsburg, Virginia</a>, the <a href=\"https://wikipedia.org/wiki/Colonial_colleges\" title=\"Colonial colleges\">second-oldest institution</a> of <a href=\"https://wikipedia.org/wiki/Higher_education\" class=\"mw-redirect\" title=\"Higher education\">higher education</a> in the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a>, is granted a charter by <a href=\"https://wikipedia.org/wiki/William_III_of_England\" title=\"William III of England\">King <PERSON> III</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\">Queen Mary II</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/College_of_<PERSON>_%26_Mary\" title=\"College of William &amp; Mary\">College of William &amp; Mary</a> in <a href=\"https://wikipedia.org/wiki/Williamsburg,_Virginia\" title=\"Williamsburg, Virginia\">Williamsburg, Virginia</a>, the <a href=\"https://wikipedia.org/wiki/Colonial_colleges\" title=\"Colonial colleges\">second-oldest institution</a> of <a href=\"https://wikipedia.org/wiki/Higher_education\" class=\"mw-redirect\" title=\"Higher education\">higher education</a> in the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a>, is granted a charter by <a href=\"https://wikipedia.org/wiki/William_III_of_England\" title=\"William III of England\">King <PERSON> III</a> and <a href=\"https://wikipedia.org/wiki/Mary_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\">Queen Mary II</a>.", "links": [{"title": "College of William & Mary", "link": "https://wikipedia.org/wiki/<PERSON>_of_<PERSON>_%26_Mary"}, {"title": "Williamsburg, Virginia", "link": "https://wikipedia.org/wiki/Williamsburg,_Virginia"}, {"title": "Colonial colleges", "link": "https://wikipedia.org/wiki/Colonial_colleges"}, {"title": "Higher education", "link": "https://wikipedia.org/wiki/Higher_education"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Mary_<PERSON>_of_England"}]}, {"year": "1807", "text": "<PERSON> defeats the coalition forces of Russian General <PERSON><PERSON><PERSON><PERSON> and Prussian General <PERSON> at the Battle of Eylau.", "html": "1807 - <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">coalition forces</a> of <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian General</a> <a href=\"https://wikipedia.org/wiki/Levin_August,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussian General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Estocq\" title=\"<PERSON>\">L'Estocq</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Eylau\" title=\"Battle of Eylau\">Battle of Eylau</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">coalition forces</a> of <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian General</a> <a href=\"https://wikipedia.org/wiki/Levin_August,_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussian General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Estocq\" title=\"<PERSON>\">L'Estocq</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Eylau\" title=\"Battle of Eylau\">Battle of Eylau</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "War of the Fourth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fourth_Coalition"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "<PERSON> August, <PERSON> von <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August,_Count_<PERSON>_<PERSON>"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27Estocq"}, {"title": "Battle of Eylau", "link": "https://wikipedia.org/wiki/Battle_of_Eylau"}]}, {"year": "1817", "text": "An army led by Grand Marshal <PERSON> crosses the Andes to join San Martín in the liberation of Chile from Spain.", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Army_of_the_Andes\" title=\"Army of the Andes\">An army</a> led by <a href=\"https://wikipedia.org/wiki/Grand_marshal\" title=\"Grand marshal\">Grand Marshal</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>as\" title=\"Juan <PERSON>\"><PERSON> Heras</a> <a href=\"https://wikipedia.org/wiki/Crossing_of_the_Andes\" title=\"Crossing of the Andes\">crosses the Andes</a> to join <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"<PERSON> Martín\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Chilean_War_of_Independence\" title=\"Chilean War of Independence\">liberation of Chile</a> from <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Army_of_the_Andes\" title=\"Army of the Andes\">An army</a> led by <a href=\"https://wikipedia.org/wiki/Grand_marshal\" title=\"Grand marshal\">Grand Marshal</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON> Heras</a> <a href=\"https://wikipedia.org/wiki/Crossing_of_the_Andes\" title=\"Crossing of the Andes\">crosses the Andes</a> to join <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Chilean_War_of_Independence\" title=\"Chilean War of Independence\">liberation of Chile</a> from <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spain</a>.", "links": [{"title": "Army of the Andes", "link": "https://wikipedia.org/wiki/Army_of_the_Andes"}, {"title": "Grand marshal", "link": "https://wikipedia.org/wiki/Grand_marshal"}, {"title": "<PERSON>as", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Crossing of the Andes", "link": "https://wikipedia.org/wiki/Crossing_of_the_Andes"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn"}, {"title": "Chilean War of Independence", "link": "https://wikipedia.org/wiki/Chilean_War_of_Independence"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}]}, {"year": "1837", "text": "<PERSON> becomes the first and only Vice President of the United States chosen by the Senate.", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first and only <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> chosen by the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first and only <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> chosen by the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senate</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1865", "text": "Delaware refuses to ratify the Thirteenth Amendment to the U.S. Constitution, delaying the criminalization of slavery until the amendment's national adoption on December 6, 1865. The amendment is ultimately ratified by Delaware on February 12, 1901, the 92nd anniversary of <PERSON>'s birth.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a> refuses to ratify the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_U.S._Constitution\" class=\"mw-redirect\" title=\"Thirteenth Amendment to the U.S. Constitution\">Thirteenth Amendment to the U.S. Constitution</a>, delaying the <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">criminalization of slavery</a> until the amendment's national adoption on December 6, 1865. The amendment is ultimately ratified by Delaware on February 12, 1901, the 92nd anniversary of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> birth.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a> refuses to ratify the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_U.S._Constitution\" class=\"mw-redirect\" title=\"Thirteenth Amendment to the U.S. Constitution\">Thirteenth Amendment to the U.S. Constitution</a>, delaying the <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">criminalization of slavery</a> until the amendment's national adoption on December 6, 1865. The amendment is ultimately ratified by Delaware on February 12, 1901, the 92nd anniversary of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> birth.", "links": [{"title": "Delaware", "link": "https://wikipedia.org/wiki/Delaware"}, {"title": "Thirteenth Amendment to the U.S. Constitution", "link": "https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_U.S._Constitution"}, {"title": "Slavery in the United States", "link": "https://wikipedia.org/wiki/Slavery_in_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON> first proposes the adoption of Universal Standard Time at a meeting of the Royal Canadian Institute.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fleming\"><PERSON><PERSON></a> first proposes the adoption of <a href=\"https://wikipedia.org/wiki/Universal_Standard_Time\" class=\"mw-redirect\" title=\"Universal Standard Time\">Universal Standard Time</a> at a meeting of the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Institute\" title=\"Royal Canadian Institute\">Royal Canadian Institute</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fleming\"><PERSON><PERSON></a> first proposes the adoption of <a href=\"https://wikipedia.org/wiki/Universal_Standard_Time\" class=\"mw-redirect\" title=\"Universal Standard Time\">Universal Standard Time</a> at a meeting of the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Institute\" title=\"Royal Canadian Institute\">Royal Canadian Institute</a>.", "links": [{"title": "Sandford Fleming", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fleming"}, {"title": "Universal Standard Time", "link": "https://wikipedia.org/wiki/Universal_Standard_Time"}, {"title": "Royal Canadian Institute", "link": "https://wikipedia.org/wiki/Royal_Canadian_Institute"}]}, {"year": "1879", "text": "England's cricket team, led by <PERSON>, is attacked in a riot during a match in Sydney.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/England_cricket_team\" title=\"England cricket team\">England's cricket team</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\">Lord <PERSON></a>, is <a href=\"https://wikipedia.org/wiki/Sydney_Riot_of_1879\" title=\"Sydney Riot of 1879\">attacked in a riot during a match</a> in <a href=\"https://wikipedia.org/wiki/Sydney\" title=\"Sydney\">Sydney</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/England_cricket_team\" title=\"England cricket team\">England's cricket team</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\">Lord <PERSON></a>, is <a href=\"https://wikipedia.org/wiki/Sydney_Riot_of_1879\" title=\"Sydney Riot of 1879\">attacked in a riot during a match</a> in <a href=\"https://wikipedia.org/wiki/Sydney\" title=\"Sydney\">Sydney</a>.", "links": [{"title": "England cricket team", "link": "https://wikipedia.org/wiki/England_cricket_team"}, {"title": "<PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_<PERSON>_<PERSON>"}, {"title": "Sydney Riot of 1879", "link": "https://wikipedia.org/wiki/Sydney_Riot_of_1879"}, {"title": "Sydney", "link": "https://wikipedia.org/wiki/Sydney"}]}, {"year": "1885", "text": "The first Japanese immigrants arrive in Hawaii.", "html": "1885 - The first <a href=\"https://wikipedia.org/wiki/Japanese_in_Hawaii\" title=\"Japanese in Hawaii\">Japanese immigrants</a> arrive in <a href=\"https://wikipedia.org/wiki/Hawaiian_Kingdom\" title=\"Hawaiian Kingdom\">Hawaii</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Japanese_in_Hawaii\" title=\"Japanese in Hawaii\">Japanese immigrants</a> arrive in <a href=\"https://wikipedia.org/wiki/Hawaiian_Kingdom\" title=\"Hawaiian Kingdom\">Hawaii</a>.", "links": [{"title": "Japanese in Hawaii", "link": "https://wikipedia.org/wiki/Japanese_in_Hawaii"}, {"title": "Hawaiian Kingdom", "link": "https://wikipedia.org/wiki/Hawaiian_Kingdom"}]}, {"year": "1887", "text": "The Dawes Act is enacted, authorizing the U.S. President to divide Native American tribal land into individual allotments.", "html": "1887 - The <a href=\"https://wikipedia.org/wiki/Dawes_Act\" title=\"Dawes Act\">Dawes Act</a> is enacted, authorizing the <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> to divide <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> tribal land into <a href=\"https://wikipedia.org/wiki/List_of_Indian_reservations_in_the_United_States\" title=\"List of Indian reservations in the United States\">individual allotments</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dawes_Act\" title=\"Dawes Act\">Dawes Act</a> is enacted, authorizing the <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> to divide <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> tribal land into <a href=\"https://wikipedia.org/wiki/List_of_Indian_reservations_in_the_United_States\" title=\"List of Indian reservations in the United States\">individual allotments</a>.", "links": [{"title": "Dawes Act", "link": "https://wikipedia.org/wiki/Dawes_Act"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}, {"title": "List of Indian reservations in the United States", "link": "https://wikipedia.org/wiki/List_of_Indian_reservations_in_the_United_States"}]}, {"year": "1904", "text": "Japanese forces launch a surprise attack against Russian-controlled Port Arthur, marking the start of the Russo-Japanese war.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese forces</a> launch a <a href=\"https://wikipedia.org/wiki/Battle_of_Port_Arthur\" title=\"Battle of Port Arthur\">surprise attack</a> against <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian-controlled</a> <a href=\"https://wikipedia.org/wiki/Lushun\" class=\"mw-redirect\" title=\"Lushun\">Port Arthur</a>, marking the start of the <a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese war</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese forces</a> launch a <a href=\"https://wikipedia.org/wiki/Battle_of_Port_Arthur\" title=\"Battle of Port Arthur\">surprise attack</a> against <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian-controlled</a> <a href=\"https://wikipedia.org/wiki/Lushun\" class=\"mw-redirect\" title=\"Lushun\">Port Arthur</a>, marking the start of the <a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese war</a>.", "links": [{"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Battle of Port Arthur", "link": "https://wikipedia.org/wiki/Battle_of_Port_Arthur"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Lushun", "link": "https://wikipedia.org/wiki/Lushun"}, {"title": "Russo-Japanese War", "link": "https://wikipedia.org/wiki/Russo-Japanese_War"}]}, {"year": "1904", "text": "The Dutch Colonial Army's Marechaussee regiment led by General <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> launch a military campaign in the Dutch East Indies' Northern Sumatra region, leading to the deaths of thousands of civilians.", "html": "1904 - The <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army\" title=\"Royal Netherlands East Indies Army\">Dutch Colonial Army</a>'s <a href=\"https://wikipedia.org/wiki/Korps_Marechaussee_te_voet\" title=\"Korps Marechaussee te voet\">Marechaussee</a> regiment led by <a href=\"https://wikipedia.org/wiki/General_officer\" title=\"General officer\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> launch a <a href=\"https://wikipedia.org/wiki/Aceh_War#Pacification\" title=\"Aceh War\">military campaign</a> in the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>' <a href=\"https://wikipedia.org/wiki/North_Sumatra\" title=\"North Sumatra\">Northern Sumatra</a> region, leading to the deaths of thousands of civilians.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army\" title=\"Royal Netherlands East Indies Army\">Dutch Colonial Army</a>'s <a href=\"https://wikipedia.org/wiki/Korps_Marechaussee_te_voet\" title=\"Korps Marechaussee te voet\">Marechaussee</a> regiment led by <a href=\"https://wikipedia.org/wiki/General_officer\" title=\"General officer\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Gotfried <PERSON>enraad <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> launch a <a href=\"https://wikipedia.org/wiki/Aceh_War#Pacification\" title=\"Aceh War\">military campaign</a> in the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>' <a href=\"https://wikipedia.org/wiki/North_Sumatra\" title=\"North Sumatra\">Northern Sumatra</a> region, leading to the deaths of thousands of civilians.", "links": [{"title": "Royal Netherlands East Indies Army", "link": "https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army"}, {"title": "Korps Marechaussee te voet", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Marechaussee_te_voet"}, {"title": "General officer", "link": "https://wikipedia.org/wiki/General_officer"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Aceh War", "link": "https://wikipedia.org/wiki/Aceh_War#Pacification"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}, {"title": "North Sumatra", "link": "https://wikipedia.org/wiki/North_Sumatra"}]}, {"year": "1910", "text": "The Boy Scouts of America is incorporated by <PERSON>.", "html": "1910 - The <a href=\"https://wikipedia.org/wiki/Boy_Scouts_of_America\" class=\"mw-redirect\" title=\"Boy Scouts of America\">Boy Scouts of America</a> is incorporated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Boy_Scouts_of_America\" class=\"mw-redirect\" title=\"Boy Scouts of America\">Boy Scouts of America</a> is incorporated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Boy Scouts of America", "link": "https://wikipedia.org/wiki/Boy_Scouts_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON> <PERSON><PERSON>'s controversial landmark film The Birth of a Nation premieres in Los Angeles.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>'s controversial landmark film <i><a href=\"https://wikipedia.org/wiki/The_Birth_of_a_Nation\" title=\"The Birth of a Nation\">The Birth of a Nation</a></i> premieres in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>'s controversial landmark film <i><a href=\"https://wikipedia.org/wiki/The_Birth_of_a_Nation\" title=\"The Birth of a Nation\">The Birth of a Nation</a></i> premieres in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "The Birth of a Nation", "link": "https://wikipedia.org/wiki/The_Birth_of_a_Nation"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1924", "text": "The first state execution in the United States by gas chamber takes place in Nevada.", "html": "1924 - The first state <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_States\" title=\"Capital punishment in the United States\">execution in the United States</a> by <a href=\"https://wikipedia.org/wiki/Gas_chamber\" title=\"Gas chamber\">gas chamber</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jon\">takes place</a> in <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>.", "no_year_html": "The first state <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_States\" title=\"Capital punishment in the United States\">execution in the United States</a> by <a href=\"https://wikipedia.org/wiki/Gas_chamber\" title=\"Gas chamber\">gas chamber</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jon\">takes place</a> in <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>.", "links": [{"title": "Capital punishment in the United States", "link": "https://wikipedia.org/wiki/Capital_punishment_in_the_United_States"}, {"title": "Gas chamber", "link": "https://wikipedia.org/wiki/Gas_chamber"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nevada", "link": "https://wikipedia.org/wiki/Nevada"}]}, {"year": "1937", "text": "Spanish Civil War: Republican forces establish the Interprovincial Council of Santander, Palencia and Burgos in Cantabria.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Republican_faction_(Spanish_Civil_War)\" title=\"Republican faction (Spanish Civil War)\">Republican forces</a> establish the <a href=\"https://wikipedia.org/wiki/Interprovincial_Council_of_Santander,_Palencia_and_Burgos\" title=\"Interprovincial Council of Santander, Palencia and Burgos\">Interprovincial Council of Santander, Palencia and Burgos</a> in <a href=\"https://wikipedia.org/wiki/Cantabria\" title=\"Cantabria\">Cantabria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Republican_faction_(Spanish_Civil_War)\" title=\"Republican faction (Spanish Civil War)\">Republican forces</a> establish the <a href=\"https://wikipedia.org/wiki/Interprovincial_Council_of_Santander,_Palencia_and_Burgos\" title=\"Interprovincial Council of Santander, Palencia and Burgos\">Interprovincial Council of Santander, Palencia and Burgos</a> in <a href=\"https://wikipedia.org/wiki/Cantabria\" title=\"Cantabria\">Cantabria</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Republican faction (Spanish Civil War)", "link": "https://wikipedia.org/wiki/Republican_faction_(Spanish_Civil_War)"}, {"title": "Interprovincial Council of Santander, Palencia and Burgos", "link": "https://wikipedia.org/wiki/Interprovincial_Council_of_Santander,_Pa<PERSON>cia_and_Burgos"}, {"title": "Cantabria", "link": "https://wikipedia.org/wiki/Cantabria"}]}, {"year": "1942", "text": "World War II: Japan invades Singapore.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Singapore\" class=\"mw-redirect\" title=\"Battle of Singapore\">Japan invades Singapore</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Singapore\" class=\"mw-redirect\" title=\"Battle of Singapore\">Japan invades Singapore</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Singapore", "link": "https://wikipedia.org/wiki/Battle_of_Singapore"}]}, {"year": "1945", "text": "World War II: British and Canadian forces commence Operation Veritable to occupy land between the Maas and Rhine rivers.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/British_Armed_Forces\" title=\"British Armed Forces\">British</a> and <a href=\"https://wikipedia.org/wiki/Canadian_Armed_Forces\" title=\"Canadian Armed Forces\">Canadian forces</a> commence <a href=\"https://wikipedia.org/wiki/Operation_Veritable\" title=\"Operation Veritable\">Operation Veritable</a> to occupy <a href=\"https://wikipedia.org/wiki/Rhineland\" title=\"Rhineland\">land between</a> the <a href=\"https://wikipedia.org/wiki/Meuse\" title=\"Meuse\">Maas</a> and <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine rivers</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/British_Armed_Forces\" title=\"British Armed Forces\">British</a> and <a href=\"https://wikipedia.org/wiki/Canadian_Armed_Forces\" title=\"Canadian Armed Forces\">Canadian forces</a> commence <a href=\"https://wikipedia.org/wiki/Operation_Veritable\" title=\"Operation Veritable\">Operation Veritable</a> to occupy <a href=\"https://wikipedia.org/wiki/Rhineland\" title=\"Rhineland\">land between</a> the <a href=\"https://wikipedia.org/wiki/Meuse\" title=\"Meuse\">Maas</a> and <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine rivers</a>.", "links": [{"title": "British Armed Forces", "link": "https://wikipedia.org/wiki/British_Armed_Forces"}, {"title": "Canadian Armed Forces", "link": "https://wikipedia.org/wiki/Canadian_Armed_Forces"}, {"title": "Operation Veritable", "link": "https://wikipedia.org/wiki/Operation_Veritable"}, {"title": "Rhineland", "link": "https://wikipedia.org/wiki/Rhineland"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Meuse"}, {"title": "Rhine", "link": "https://wikipedia.org/wiki/Rhine"}]}, {"year": "1945", "text": "World War II: <PERSON> escapes with nine other Soviet POWs from a Nazi concentration camp in Peenemünde, Usedom.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes with nine other <a href=\"https://wikipedia.org/wiki/Soviet_POWs_in_Nazi_Germany\" class=\"mw-redirect\" title=\"Soviet POWs in Nazi Germany\">Soviet POWs</a> from a <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">Nazi concentration camp</a> in <a href=\"https://wikipedia.org/wiki/Peenem%C3%BCnde\" title=\"Peenemünde\">Peenemünde</a>, <a href=\"https://wikipedia.org/wiki/Usedom\" title=\"Usedom\">Usedom</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes with nine other <a href=\"https://wikipedia.org/wiki/Soviet_POWs_in_Nazi_Germany\" class=\"mw-redirect\" title=\"Soviet POWs in Nazi Germany\">Soviet POWs</a> from a <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">Nazi concentration camp</a> in <a href=\"https://wikipedia.org/wiki/Peenem%C3%BCnde\" title=\"Peenemünde\">Peenemünde</a>, <a href=\"https://wikipedia.org/wiki/Usedom\" title=\"Usedom\">Usedom</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Soviet POWs in Nazi Germany", "link": "https://wikipedia.org/wiki/Soviet_POWs_in_Nazi_Germany"}, {"title": "Nazi concentration camps", "link": "https://wikipedia.org/wiki/Nazi_concentration_camps"}, {"title": "Peenemünde", "link": "https://wikipedia.org/wiki/Peenem%C3%BCnde"}, {"title": "Usedom", "link": "https://wikipedia.org/wiki/Usedom"}]}, {"year": "1946", "text": "The People's Republic of Korea is dissolved in the North and replaced by the communist-controlled Provisional People's Committee of North Korea.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Korea\" title=\"People's Republic of Korea\">People's Republic of Korea</a> is dissolved in the <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North</a> and replaced by the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Korea\" title=\"Communist Party of Korea\">communist</a>-controlled <a href=\"https://wikipedia.org/wiki/Provisional_People%27s_Committee_of_North_Korea\" title=\"Provisional People's Committee of North Korea\">Provisional People's Committee of North Korea</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Korea\" title=\"People's Republic of Korea\">People's Republic of Korea</a> is dissolved in the <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North</a> and replaced by the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Korea\" title=\"Communist Party of Korea\">communist</a>-controlled <a href=\"https://wikipedia.org/wiki/Provisional_People%27s_Committee_of_North_Korea\" title=\"Provisional People's Committee of North Korea\">Provisional People's Committee of North Korea</a>.", "links": [{"title": "People's Republic of Korea", "link": "https://wikipedia.org/wiki/People%27s_Republic_of_Korea"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Communist Party of Korea", "link": "https://wikipedia.org/wiki/Communist_Party_of_Korea"}, {"title": "Provisional People's Committee of North Korea", "link": "https://wikipedia.org/wiki/Provisional_People%27s_Committee_of_North_Korea"}]}, {"year": "1950", "text": "The Stasi, the secret police of East Germany, is established.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Stasi\" title=\"Stasi\">Stasi</a>, the secret police of <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>, is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Stasi\" title=\"Stasi\">Stasi</a>, the secret police of <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>, is established.", "links": [{"title": "Stasi", "link": "https://wikipedia.org/wiki/Stasi"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}]}, {"year": "1960", "text": "Queen <PERSON> issues an Order-in-Council, proclaiming the House of Windsor and declaring that her descendants will take the name Mountbatten-Windsor.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\">Queen <PERSON> II</a> issues an <a href=\"https://wikipedia.org/wiki/Order-in-Council\" class=\"mw-redirect\" title=\"Order-in-Council\">Order-in-Council</a>, proclaiming the <a href=\"https://wikipedia.org/wiki/House_of_Windsor\" title=\"House of Windsor\">House of Windsor</a> and declaring that her descendants will take the name <a href=\"https://wikipedia.org/wiki/Mountbatten-Windsor\" title=\"Mountbatten-Windsor\">Mountbatten-Windsor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\">Queen <PERSON> II</a> issues an <a href=\"https://wikipedia.org/wiki/Order-in-Council\" class=\"mw-redirect\" title=\"Order-in-Council\">Order-in-Council</a>, proclaiming the <a href=\"https://wikipedia.org/wiki/House_of_Windsor\" title=\"House of Windsor\">House of Windsor</a> and declaring that her descendants will take the name <a href=\"https://wikipedia.org/wiki/Mountbatten-Windsor\" title=\"Mountbatten-Windsor\">Mountbatten-Windsor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Order-in-Council", "link": "https://wikipedia.org/wiki/Order-in-Council"}, {"title": "House of Windsor", "link": "https://wikipedia.org/wiki/House_of_Windsor"}, {"title": "Mountbatten-Windsor", "link": "https://wikipedia.org/wiki/Mountbatten-Windsor"}]}, {"year": "1960", "text": "The Hollywood Walk of Fame is founded.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Hollywood_Walk_of_Fame\" title=\"Hollywood Walk of Fame\">Hollywood Walk of Fame</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hollywood_Walk_of_Fame\" title=\"Hollywood Walk of Fame\">Hollywood Walk of Fame</a> is founded.", "links": [{"title": "Hollywood Walk of Fame", "link": "https://wikipedia.org/wiki/Hollywood_Walk_of_Fame"}]}, {"year": "1962", "text": "Nine protestors are killed at Charonne station, Paris, by French police under the command of ex-<PERSON><PERSON> official and Parisian Prefect of Police <PERSON>.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Charonne_subway_massacre\" title=\"Charonne subway massacre\">Nine protestors are killed</a> at <a href=\"https://wikipedia.org/wiki/Charonne_station\" title=\"Charonne station\">Charonne station</a>, Paris, by <a href=\"https://wikipedia.org/wiki/Law_enforcement_in_France\" title=\"Law enforcement in France\">French police</a> under the command of ex-<a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy official</a> and <a href=\"https://wikipedia.org/wiki/Paris_Police_Prefecture\" title=\"Paris Police Prefecture\">Parisian Prefect of Police</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charonne_subway_massacre\" title=\"Charonne subway massacre\">Nine protestors are killed</a> at <a href=\"https://wikipedia.org/wiki/Charonne_station\" title=\"Charonne station\">Charonne station</a>, Paris, by <a href=\"https://wikipedia.org/wiki/Law_enforcement_in_France\" title=\"Law enforcement in France\">French police</a> under the command of ex-<a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy official</a> and <a href=\"https://wikipedia.org/wiki/Paris_Police_Prefecture\" title=\"Paris Police Prefecture\">Parisian Prefect of Police</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Charonne subway massacre", "link": "https://wikipedia.org/wiki/Charonne_subway_massacre"}, {"title": "Charonne station", "link": "https://wikipedia.org/wiki/Charonne_station"}, {"title": "Law enforcement in France", "link": "https://wikipedia.org/wiki/Law_enforcement_in_France"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}, {"title": "Paris Police Prefecture", "link": "https://wikipedia.org/wiki/Paris_Police_Prefecture"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "The regime of Prime Minister of Iraq <PERSON> is overthrown by the Ba'ath Party.", "html": "1963 - The regime of <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iraq\" title=\"Prime Minister of Iraq\">Prime Minister of Iraq</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Ramadan_Revolution\" title=\"Ramadan Revolution\">overthrown</a> by the <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a>.", "no_year_html": "The regime of <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iraq\" title=\"Prime Minister of Iraq\">Prime Minister of Iraq</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Ramadan_Revolution\" title=\"Ramadan Revolution\">overthrown</a> by the <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a>.", "links": [{"title": "Prime Minister of Iraq", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iraq"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ramadan Revolution", "link": "https://wikipedia.org/wiki/Ramadan_Revolution"}, {"title": "Ba'ath Party", "link": "https://wikipedia.org/wiki/Ba%27ath_Party"}]}, {"year": "1965", "text": "Eastern Air Lines Flight 663 crashes into the Atlantic Ocean and explodes, killing all 84 people onboard.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_663\" title=\"Eastern Air Lines Flight 663\">Eastern Air Lines Flight 663</a> crashes into the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a> and explodes, killing all 84 people onboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_663\" title=\"Eastern Air Lines Flight 663\">Eastern Air Lines Flight 663</a> crashes into the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a> and explodes, killing all 84 people onboard.", "links": [{"title": "Eastern Air Lines Flight 663", "link": "https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_663"}, {"title": "Atlantic Ocean", "link": "https://wikipedia.org/wiki/Atlantic_Ocean"}]}, {"year": "1968", "text": "American civil rights movement: An attack on Black students from South Carolina State University who are protesting racial segregation leaves three dead and 28 injured in Orangeburg, South Carolina.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/American_civil_rights_movement\" class=\"mw-redirect\" title=\"American civil rights movement\">American civil rights movement</a>: An <a href=\"https://wikipedia.org/wiki/Orangeburg_Massacre\" title=\"Orangeburg Massacre\">attack on Black students</a> from <a href=\"https://wikipedia.org/wiki/South_Carolina_State_University\" title=\"South Carolina State University\">South Carolina State University</a> who are protesting <a href=\"https://wikipedia.org/wiki/Racial_segregation\" title=\"Racial segregation\">racial segregation</a> leaves three dead and 28 injured in <a href=\"https://wikipedia.org/wiki/Orangeburg,_South_Carolina\" title=\"Orangeburg, South Carolina\">Orangeburg, South Carolina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_civil_rights_movement\" class=\"mw-redirect\" title=\"American civil rights movement\">American civil rights movement</a>: An <a href=\"https://wikipedia.org/wiki/Orangeburg_Massacre\" title=\"Orangeburg Massacre\">attack on Black students</a> from <a href=\"https://wikipedia.org/wiki/South_Carolina_State_University\" title=\"South Carolina State University\">South Carolina State University</a> who are protesting <a href=\"https://wikipedia.org/wiki/Racial_segregation\" title=\"Racial segregation\">racial segregation</a> leaves three dead and 28 injured in <a href=\"https://wikipedia.org/wiki/Orangeburg,_South_Carolina\" title=\"Orangeburg, South Carolina\">Orangeburg, South Carolina</a>.", "links": [{"title": "American civil rights movement", "link": "https://wikipedia.org/wiki/American_civil_rights_movement"}, {"title": "Orangeburg Massacre", "link": "https://wikipedia.org/wiki/Orangeburg_Massacre"}, {"title": "South Carolina State University", "link": "https://wikipedia.org/wiki/South_Carolina_State_University"}, {"title": "Racial segregation", "link": "https://wikipedia.org/wiki/Racial_segregation"}, {"title": "Orangeburg, South Carolina", "link": "https://wikipedia.org/wiki/Orangeburg,_South_Carolina"}]}, {"year": "1971", "text": "The NASDAQ stock market index opens for the first time.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/NASDAQ\" class=\"mw-redirect\" title=\"NASDAQ\">NASDAQ</a> <a href=\"https://wikipedia.org/wiki/Stock_market_index\" title=\"Stock market index\">stock market index</a> opens for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/NASDAQ\" class=\"mw-redirect\" title=\"NASDAQ\">NASDAQ</a> <a href=\"https://wikipedia.org/wiki/Stock_market_index\" title=\"Stock market index\">stock market index</a> opens for the first time.", "links": [{"title": "NASDAQ", "link": "https://wikipedia.org/wiki/NASDAQ"}, {"title": "Stock market index", "link": "https://wikipedia.org/wiki/Stock_market_index"}]}, {"year": "1971", "text": "South Vietnamese ground troops launch an incursion into Laos to try to cut off the Ho Chi Minh trail and stop communist infiltration into the country.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese ground troops</a> <a href=\"https://wikipedia.org/wiki/Operation_Lam_Son_719\" title=\"Operation Lam Son 719\">launch an incursion</a> into <a href=\"https://wikipedia.org/wiki/Laos\" title=\"Laos\">Laos</a> to try to cut off the <a href=\"https://wikipedia.org/wiki/Ho_Chi_Minh_trail\" title=\"Ho Chi Minh trail\">Ho Chi Minh trail</a> and stop <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">communist infiltration</a> into the country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese ground troops</a> <a href=\"https://wikipedia.org/wiki/Operation_Lam_Son_719\" title=\"Operation Lam Son 719\">launch an incursion</a> into <a href=\"https://wikipedia.org/wiki/Laos\" title=\"Laos\">Laos</a> to try to cut off the <a href=\"https://wikipedia.org/wiki/Ho_Chi_Minh_trail\" title=\"Ho Chi Minh trail\">Ho Chi Minh trail</a> and stop <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">communist infiltration</a> into the country.", "links": [{"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "Operation Lam Son 719", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_719"}, {"title": "Laos", "link": "https://wikipedia.org/wiki/Laos"}, {"title": "Ho Chi Minh trail", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_trail"}, {"title": "People's Army of Vietnam", "link": "https://wikipedia.org/wiki/People%27s_Army_of_Vietnam"}]}, {"year": "1974", "text": "The crew of Skylab 4, the last mission to visit the American space station Skylab, returns to Earth after 84 days in space.", "html": "1974 - The crew of <a href=\"https://wikipedia.org/wiki/Skylab_4\" title=\"Skylab 4\">Skylab 4</a>, the last mission to visit the American <a href=\"https://wikipedia.org/wiki/Space_station\" title=\"Space station\">space station</a> <a href=\"https://wikipedia.org/wiki/Skylab\" title=\"Skylab\"><PERSON><PERSON></a>, returns to Earth after 84 days in <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a>.", "no_year_html": "The crew of <a href=\"https://wikipedia.org/wiki/Skylab_4\" title=\"Skylab 4\">Skylab 4</a>, the last mission to visit the American <a href=\"https://wikipedia.org/wiki/Space_station\" title=\"Space station\">space station</a> <a href=\"https://wikipedia.org/wiki/Skylab\" title=\"Skylab\"><PERSON><PERSON></a>, returns to Earth after 84 days in <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a>.", "links": [{"title": "Skylab 4", "link": "https://wikipedia.org/wiki/Skylab_4"}, {"title": "Space station", "link": "https://wikipedia.org/wiki/Space_station"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Skylab"}, {"title": "Outer space", "link": "https://wikipedia.org/wiki/Outer_space"}]}, {"year": "1983", "text": "A dust storm hits Melbourne, resulting in the worst drought on record and severe weather conditions in the city.", "html": "1983 - A <a href=\"https://wikipedia.org/wiki/1983_Melbourne_dust_storm\" title=\"1983 Melbourne dust storm\">dust storm hits Melbourne</a>, resulting in the worst drought on record and severe weather conditions in the city.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1983_Melbourne_dust_storm\" title=\"1983 Melbourne dust storm\">dust storm hits Melbourne</a>, resulting in the worst drought on record and severe weather conditions in the city.", "links": [{"title": "1983 Melbourne dust storm", "link": "https://wikipedia.org/wiki/1983_Melbourne_dust_storm"}]}, {"year": "1983", "text": "Irish race horse <PERSON><PERSON><PERSON> is stolen and allegedly killed by gunmen in a ransom attempt by the PIRA.", "html": "1983 - Irish race horse <a href=\"https://wikipedia.org/wiki/Sherga<PERSON>\" title=\"Sherga<PERSON>\"><PERSON><PERSON><PERSON></a> is stolen and allegedly killed by gunmen in a ransom attempt by the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">PIRA</a>.", "no_year_html": "Irish race horse <a href=\"https://wikipedia.org/wiki/She<PERSON><PERSON>\" title=\"Sherga<PERSON>\"><PERSON><PERSON><PERSON></a> is stolen and allegedly killed by gunmen in a ransom attempt by the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">PIRA</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}]}, {"year": "1986", "text": "Twenty-three people are killed when a VIA Rail passenger train collides with a Canadian National freight train near the town of Hinton, Alberta, making it one of the worst rail accidents in Canada.", "html": "1986 - Twenty-three people are killed when a <a href=\"https://wikipedia.org/wiki/Via_Rail\" title=\"Via Rail\">VIA Rail</a> passenger train <a href=\"https://wikipedia.org/wiki/Hinton_train_collision\" title=\"Hinton train collision\">collides</a> with a <a href=\"https://wikipedia.org/wiki/Canadian_National_Railway\" title=\"Canadian National Railway\">Canadian National</a> freight train near the town of <a href=\"https://wikipedia.org/wiki/Hinton,_Alberta\" title=\"Hinton, Alberta\">Hinton, Alberta</a>, making it one of the worst rail accidents in Canada.", "no_year_html": "Twenty-three people are killed when a <a href=\"https://wikipedia.org/wiki/Via_Rail\" title=\"Via Rail\">VIA Rail</a> passenger train <a href=\"https://wikipedia.org/wiki/Hinton_train_collision\" title=\"Hinton train collision\">collides</a> with a <a href=\"https://wikipedia.org/wiki/Canadian_National_Railway\" title=\"Canadian National Railway\">Canadian National</a> freight train near the town of <a href=\"https://wikipedia.org/wiki/Hinton,_Alberta\" title=\"Hinton, Alberta\">Hinton, Alberta</a>, making it one of the worst rail accidents in Canada.", "links": [{"title": "Via Rail", "link": "https://wikipedia.org/wiki/Via_Rail"}, {"title": "Hinton train collision", "link": "https://wikipedia.org/wiki/Hi<PERSON>_train_collision"}, {"title": "Canadian National Railway", "link": "https://wikipedia.org/wiki/Canadian_National_Railway"}, {"title": "Hinton, Alberta", "link": "https://wikipedia.org/wiki/Hinton,_Alberta"}]}, {"year": "1989", "text": "Independent Air Flight 1851 strikes Pico Alto mountain while on approach to Santa Maria Airport in the Azores, killing all 144 passengers on board.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Independent_Air_Flight_1851\" title=\"Independent Air Flight 1851\">Independent Air Flight 1851</a> strikes <a href=\"https://wikipedia.org/wiki/Pico_Alto_(Santa_Maria)\" title=\"Pico Alto (Santa Maria)\">Pico Alto</a> mountain while on approach to <a href=\"https://wikipedia.org/wiki/Santa_Maria_Airport_(Azores)\" title=\"Santa Maria Airport (Azores)\">Santa Maria Airport</a> in the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>, killing all 144 passengers on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Independent_Air_Flight_1851\" title=\"Independent Air Flight 1851\">Independent Air Flight 1851</a> strikes <a href=\"https://wikipedia.org/wiki/Pico_Alto_(Santa_Maria)\" title=\"Pico Alto (Santa Maria)\">Pico Alto</a> mountain while on approach to <a href=\"https://wikipedia.org/wiki/Santa_Maria_Airport_(Azores)\" title=\"Santa Maria Airport (Azores)\">Santa Maria Airport</a> in the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>, killing all 144 passengers on board.", "links": [{"title": "Independent Air Flight 1851", "link": "https://wikipedia.org/wiki/Independent_Air_Flight_1851"}, {"title": "Pico Alto (Santa Maria)", "link": "https://wikipedia.org/wiki/Pico_Alto_(Santa_Maria)"}, {"title": "Santa Maria Airport (Azores)", "link": "https://wikipedia.org/wiki/Santa_Maria_Airport_(Azores)"}, {"title": "Azores", "link": "https://wikipedia.org/wiki/Azores"}]}, {"year": "1993", "text": "An Iran Air Tours Tupolev Tu-154 and an Iranian Air Force Sukhoi Su-24 collide mid-air near Qods, Iran, killing all 133 people onboard both aircraft.", "html": "1993 - An <a href=\"https://wikipedia.org/wiki/Iran_Airtour\" title=\"Iran Airtour\">Iran Air Tours</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a> and an <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force\" title=\"Islamic Republic of Iran Air Force\">Iranian Air Force</a> <a href=\"https://wikipedia.org/wiki/Sukhoi_Su-24\" title=\"Sukhoi Su-24\">Sukhoi Su-24</a> <a href=\"https://wikipedia.org/wiki/1993_Tehran_mid-air_collision\" title=\"1993 Tehran mid-air collision\">collide mid-air</a> near <a href=\"https://wikipedia.org/wiki/Qods,_Iran\" title=\"Qods, Iran\">Qods, Iran</a>, killing all 133 people onboard both aircraft.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Iran_Airtour\" title=\"Iran Airtour\">Iran Air Tours</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a> and an <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force\" title=\"Islamic Republic of Iran Air Force\">Iranian Air Force</a> <a href=\"https://wikipedia.org/wiki/Sukhoi_Su-24\" title=\"Sukhoi Su-24\">Sukhoi Su-24</a> <a href=\"https://wikipedia.org/wiki/1993_Tehran_mid-air_collision\" title=\"1993 Tehran mid-air collision\">collide mid-air</a> near <a href=\"https://wikipedia.org/wiki/Qods,_Iran\" title=\"Qods, Iran\">Qods, Iran</a>, killing all 133 people onboard both aircraft.", "links": [{"title": "Iran Airtour", "link": "https://wikipedia.org/wiki/Iran_Airtour"}, {"title": "Tupolev Tu-154", "link": "https://wikipedia.org/wiki/Tupolev_Tu-154"}, {"title": "Islamic Republic of Iran Air Force", "link": "https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force"}, {"title": "Sukhoi Su-24", "link": "https://wikipedia.org/wiki/Sukhoi_Su-24"}, {"title": "1993 Tehran mid-air collision", "link": "https://wikipedia.org/wiki/1993_Tehran_mid-air_collision"}, {"title": "Qods, Iran", "link": "https://wikipedia.org/wiki/Qods,_Iran"}]}, {"year": "2010", "text": "Over 2 miles (3.2 km) of road are buried after a storm in the Hindu Kush mountains of Afghanistan triggers a series of avalanches, killing at least 172 people and trapping over 2,000 others.", "html": "2010 - Over 2 miles (3.2 km) of road are buried after a storm in the <a href=\"https://wikipedia.org/wiki/Hindu_Kush\" title=\"Hindu Kush\">Hindu Kush</a> mountains of <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> triggers a <a href=\"https://wikipedia.org/wiki/2010_Salang_avalanches\" title=\"2010 Salang avalanches\">series of avalanches</a>, killing at least 172 people and trapping over 2,000 others.", "no_year_html": "Over 2 miles (3.2 km) of road are buried after a storm in the <a href=\"https://wikipedia.org/wiki/Hindu_Kush\" title=\"Hindu Kush\">Hindu Kush</a> mountains of <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> triggers a <a href=\"https://wikipedia.org/wiki/2010_Salang_avalanches\" title=\"2010 Salang avalanches\">series of avalanches</a>, killing at least 172 people and trapping over 2,000 others.", "links": [{"title": "Hindu Kush", "link": "https://wikipedia.org/wiki/Hindu_<PERSON><PERSON>"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "2010 Salang avalanches", "link": "https://wikipedia.org/wiki/2010_Salang_avalanches"}]}, {"year": "2013", "text": "A blizzard kills at least 18 and leaves hundreds of thousands of people without electricity in the northeastern United States and parts of Canada.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/February_2013_North_American_blizzard\" title=\"February 2013 North American blizzard\">A blizzard</a> kills at least 18 and leaves hundreds of thousands of people without electricity in the <a href=\"https://wikipedia.org/wiki/Northeastern_United_States\" title=\"Northeastern United States\">northeastern United States</a> and parts of Canada.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/February_2013_North_American_blizzard\" title=\"February 2013 North American blizzard\">A blizzard</a> kills at least 18 and leaves hundreds of thousands of people without electricity in the <a href=\"https://wikipedia.org/wiki/Northeastern_United_States\" title=\"Northeastern United States\">northeastern United States</a> and parts of Canada.", "links": [{"title": "February 2013 North American blizzard", "link": "https://wikipedia.org/wiki/February_2013_North_American_blizzard"}, {"title": "Northeastern United States", "link": "https://wikipedia.org/wiki/Northeastern_United_States"}]}, {"year": "2014", "text": "A hotel fire in Medina, Saudi Arabia, kills 15 Egyptian pilgrims with 130 others injured.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/2014_Medina_hotel_fire\" title=\"2014 Medina hotel fire\">hotel fire</a> in <a href=\"https://wikipedia.org/wiki/Medina,_Saudi_Arabia\" class=\"mw-redirect\" title=\"Medina, Saudi Arabia\">Medina, Saudi Arabia</a>, kills 15 <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> pilgrims with 130 others injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2014_Medina_hotel_fire\" title=\"2014 Medina hotel fire\">hotel fire</a> in <a href=\"https://wikipedia.org/wiki/Medina,_Saudi_Arabia\" class=\"mw-redirect\" title=\"Medina, Saudi Arabia\">Medina, Saudi Arabia</a>, kills 15 <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> pilgrims with 130 others injured.", "links": [{"title": "2014 Medina hotel fire", "link": "https://wikipedia.org/wiki/2014_Medina_hotel_fire"}, {"title": "Medina, Saudi Arabia", "link": "https://wikipedia.org/wiki/Medina,_Saudi_Arabia"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "2020", "text": "A soldier opens fire in a military camp and a shopping center in Nakhon Ratchasima, Thailand, killing 29 people and injuring 58 others before being shot dead by police the next day. It is considered the deadliest mass shooting in the country's history.", "html": "2020 - A soldier <a href=\"https://wikipedia.org/wiki/Nakhon_Ratchasima_shootings\" title=\"Nakhon Ratchasima shootings\">opens fire</a> in a <a href=\"https://wikipedia.org/wiki/Military_camp\" title=\"Military camp\">military camp</a> and a shopping center in <a href=\"https://wikipedia.org/wiki/Nakhon_Ratchasima\" title=\"Nakhon Ratchasima\">Nakh<PERSON> Ratchasima</a>, <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>, killing 29 people and injuring 58 others before being shot dead by <a href=\"https://wikipedia.org/wiki/Royal_Thai_Police\" title=\"Royal Thai Police\">police</a> the next day. It is considered the deadliest mass shooting in the country's history.", "no_year_html": "A soldier <a href=\"https://wikipedia.org/wiki/Nakhon_Ratchasima_shootings\" title=\"Nakhon Ratchasima shootings\">opens fire</a> in a <a href=\"https://wikipedia.org/wiki/Military_camp\" title=\"Military camp\">military camp</a> and a shopping center in <a href=\"https://wikipedia.org/wiki/Nakhon_Ratchasima\" title=\"Nakhon Ratchasima\">Nakhon Ratchasima</a>, <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>, killing 29 people and injuring 58 others before being shot dead by <a href=\"https://wikipedia.org/wiki/Royal_Thai_Police\" title=\"Royal Thai Police\">police</a> the next day. It is considered the deadliest mass shooting in the country's history.", "links": [{"title": "<PERSON><PERSON><PERSON> shootings", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_shootings"}, {"title": "Military camp", "link": "https://wikipedia.org/wiki/Military_camp"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rat<PERSON>sima"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "Royal Thai Police", "link": "https://wikipedia.org/wiki/Royal_Thai_Police"}]}, {"year": "2023", "text": "Two children are killed and six others are injured when a bus crashes into a daycare centre in Laval, Quebec, Canada. The driver is arrested and charged with homicide and dangerous driving.", "html": "2023 - Two children are killed and six others are injured when a <a href=\"https://wikipedia.org/wiki/La<PERSON>_daycare_bus_crash\" title=\"Laval daycare bus crash\">bus crashes</a> into a daycare centre in <a href=\"https://wikipedia.org/wiki/Laval,_Quebec\" title=\"Laval, Quebec\">Laval, Quebec</a>, <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>. The driver is arrested and <a href=\"https://wikipedia.org/wiki/Criminal_charge\" title=\"Criminal charge\">charged</a> with <a href=\"https://wikipedia.org/wiki/Homicide\" title=\"Homicide\">homicide</a> and <a href=\"https://wikipedia.org/wiki/Dangerous_driving\" title=\"Dangerous driving\">dangerous driving</a>.", "no_year_html": "Two children are killed and six others are injured when a <a href=\"https://wikipedia.org/wiki/La<PERSON>_daycare_bus_crash\" title=\"Laval daycare bus crash\">bus crashes</a> into a daycare centre in <a href=\"https://wikipedia.org/wiki/Laval,_Quebec\" title=\"Laval, Quebec\">Laval, Quebec</a>, <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>. The driver is arrested and <a href=\"https://wikipedia.org/wiki/Criminal_charge\" title=\"Criminal charge\">charged</a> with <a href=\"https://wikipedia.org/wiki/Homicide\" title=\"Homicide\">homicide</a> and <a href=\"https://wikipedia.org/wiki/Dangerous_driving\" title=\"Dangerous driving\">dangerous driving</a>.", "links": [{"title": "La<PERSON> daycare bus crash", "link": "https://wikipedia.org/wiki/La<PERSON>_daycare_bus_crash"}, {"title": "Laval, Quebec", "link": "https://wikipedia.org/wiki/Laval,_Quebec"}, {"title": "Canada", "link": "https://wikipedia.org/wiki/Canada"}, {"title": "Criminal charge", "link": "https://wikipedia.org/wiki/Criminal_charge"}, {"title": "Homicide", "link": "https://wikipedia.org/wiki/Homicide"}, {"title": "Dangerous driving", "link": "https://wikipedia.org/wiki/Dangerous_driving"}]}], "Births": [{"year": "120", "text": "<PERSON><PERSON><PERSON>, Greek astronomer, mathematician, and astrologer (d. ~175)", "html": "120 - <a href=\"https://wikipedia.org/wiki/Vettius_<PERSON>\" title=\"Vettius Vale<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek astronomer, mathematician, and astrologer (d. ~175)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vetti<PERSON>_<PERSON>\" title=\"Vettius <PERSON>\"><PERSON><PERSON><PERSON></a>, Greek astronomer, mathematician, and astrologer (d. ~175)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>ns"}]}, {"year": "412", "text": "<PERSON><PERSON><PERSON>, Greek mathematician and philosopher (d. ~485)", "html": "412 - <a href=\"https://wikipedia.org/wiki/Proclus\" title=\"Pro<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek mathematician and philosopher (d. ~485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Proclus\" title=\"Proclus\"><PERSON><PERSON><PERSON></a>, Greek mathematician and philosopher (d. ~485)", "links": [{"title": "Proclus", "link": "https://wikipedia.org/wiki/Proclus"}]}, {"year": "882", "text": "<PERSON> ibn <PERSON><PERSON>, Egyptian commander and politician, Abbasid Governor of Egypt (d. 946)", "html": "882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_ibn_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Egyptian commander and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt\" class=\"mw-redirect\" title=\"List of governors of Islamic Egypt\">Abbasid Governor of Egypt</a> (d. 946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_ibn_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Egyptian commander and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt\" class=\"mw-redirect\" title=\"List of governors of Islamic Egypt\">Abbasid Governor of Egypt</a> (d. 946)", "links": [{"title": "<PERSON> ibn <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "List of governors of Islamic Egypt", "link": "https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt"}]}, {"year": "1191", "text": "<PERSON><PERSON><PERSON> of Vladimir (d. 1246)", "html": "1191 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Vladimir\" title=\"<PERSON><PERSON><PERSON> II of Vladimir\"><PERSON><PERSON><PERSON> II of Vladimir</a> (d. 1246)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Vladimir\" title=\"<PERSON><PERSON><PERSON> II of Vladimir\"><PERSON><PERSON><PERSON> of Vladimir</a> (d. 1246)", "links": [{"title": "<PERSON><PERSON><PERSON> II of Vladimir", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Vladimir"}]}, {"year": "1291", "text": "<PERSON><PERSON><PERSON> of Portugal, Portuguese king (d. 1357)", "html": "1291 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Portugal\" title=\"<PERSON><PERSON><PERSON> IV of Portugal\"><PERSON><PERSON><PERSON> IV of Portugal</a>, Portuguese king (d. 1357)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Portugal\" title=\"<PERSON><PERSON><PERSON> IV of Portugal\"><PERSON><PERSON><PERSON> IV of Portugal</a>, Portuguese king (d. 1357)", "links": [{"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Afonso_IV_of_Portugal"}]}, {"year": "1405", "text": "<PERSON>, Byzantine emperor (d. 1453)", "html": "1405 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XI_Palaiologos\" title=\"<PERSON> XI Palaiologos\"><PERSON></a>, Byzantine emperor (d. 1453)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XI_Palaiologos\" title=\"<PERSON> XI Palaiologos\"><PERSON></a>, Byzantine emperor (d. 1453)", "links": [{"title": "Constantine XI Palaiologos", "link": "https://wikipedia.org/wiki/Constantine_XI_Palaiologos"}]}, {"year": "1487", "text": "<PERSON>, Duke of Württemberg, German duke (d. 1550)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a>, German duke (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a>, German duke (d. 1550)", "links": [{"title": "<PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_W%C3%BCrttem<PERSON>"}]}, {"year": "1514", "text": "<PERSON><PERSON>, Venetian churchman, diplomat and scholar (d. 1570)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venetian churchman, diplomat and scholar (d. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venetian churchman, diplomat and scholar (d. 1570)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1552", "text": "<PERSON><PERSON><PERSON><PERSON>, French poet and soldier (d. 1630)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/Agrippa_d%27Aubign%C3%A9\" title=\"Agrippa d'Aubigné\"><PERSON><PERSON><PERSON><PERSON> d'Aubigné</a>, French poet and soldier (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agrippa_d%27Aubign%C3%A9\" title=\"Agrippa d'Aubigné\"><PERSON><PERSON><PERSON><PERSON> d'Aubigné</a>, French poet and soldier (d. 1630)", "links": [{"title": "A<PERSON>rip<PERSON>", "link": "https://wikipedia.org/wiki/Agrippa_d%27Aubign%C3%A9"}]}, {"year": "1577", "text": "<PERSON>, English priest, physician, and scholar (d. 1640)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)\" class=\"mw-redirect\" title=\"<PERSON> (scholar)\"><PERSON></a>, English priest, physician, and scholar (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(scholar)\" class=\"mw-redirect\" title=\"<PERSON> (scholar)\"><PERSON></a>, English priest, physician, and scholar (d. 1640)", "links": [{"title": "<PERSON> (scholar)", "link": "https://wikipedia.org/wiki/<PERSON>_(scholar)"}]}, {"year": "1591", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter (d. 1666)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/Guercino\" title=\"Guer<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guer<PERSON>o\" title=\"G<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (d. 1666)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Guercino"}]}, {"year": "1685", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French historian and author (d. 1770)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A7ois_H%C3%A9nault\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French historian and author (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A7ois_H%C3%A9nault\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French historian and author (d. 1770)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7ois_H%C3%A9nault"}]}, {"year": "1700", "text": "<PERSON>, Dutch-Swiss mathematician and physicist (d. 1782)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Swiss mathematician and physicist (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Swiss mathematician and physicist (d. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1720", "text": "Emperor <PERSON><PERSON><PERSON>, Japanese emperor (d. 1750)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1750)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1741", "text": "<PERSON>, Belgian-French organist and composer (d. 1813)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gr%C3%A9try\" title=\"<PERSON>\"><PERSON></a>, Belgian-French organist and composer (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gr%C3%A9try\" title=\"<PERSON>\"><PERSON></a>, Belgian-French organist and composer (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Gr%C3%A9try"}]}, {"year": "1762", "text": "<PERSON><PERSON>, Vietnamese emperor (d. 1820)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Long\" title=\"<PERSON><PERSON> Long\"><PERSON><PERSON></a>, Vietnamese emperor (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Long\" title=\"<PERSON><PERSON> Long\"><PERSON><PERSON></a>, Vietnamese emperor (d. 1820)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_Long"}]}, {"year": "1764", "text": "<PERSON>, Austrian composer and conductor (d. 1846)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON> of Bavaria, German princess (d. 1873)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Augusta_of_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, German princess (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caroline_Augusta_of_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, German princess (d. 1873)", "links": [{"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Caroline_Augusta_of_Bavaria"}]}, {"year": "1798", "text": "Grand Duke <PERSON> of Russia, Russian grand duke (d. 1849)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a>, Russian grand duke (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a>, Russian grand duke (d. 1849)", "links": [{"title": "Grand Duke <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1807", "text": "<PERSON>, English sculptor and zoologist (d. 1889)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and zoologist (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and zoologist (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hawkins"}]}, {"year": "1817", "text": "<PERSON>, American general (d. 1872)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, English author, critic, and academic (d. 1900)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, critic, and academic (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, critic, and academic (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, American general (d. 1891)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON><PERSON>, French photographer and journalist (d. 1894)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French photographer and journalist (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Maxim<PERSON>\"><PERSON><PERSON></a>, French photographer and journalist (d. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, English geographer, biologist, and explorer (d. 1892)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geographer, biologist, and explorer (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geographer, biologist, and explorer (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, French author, poet, and playwright (d. 1905)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON><PERSON>, French-Canadian bishop and missionary (d. 1902)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, French-Canadian bishop and missionary (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, French-Canadian bishop and missionary (d. 1902)", "links": [{"title": "Vital-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON><PERSON><PERSON>, Russian chemist and academic (d. 1907)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian chemist and academic (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian chemist and academic (d. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American author (d. 1904)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON>, American politician and suffragist (d. 1937)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and suffragist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and suffragist (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Ukrainian-American chemist and academic (d. 1947)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American chemist and academic (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American chemist and academic (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>-<PERSON>, German painter (d. 1907)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1907)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Austrian-Israeli philosopher and academic (d. 1965)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Israeli philosopher and academic (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Israeli philosopher and academic (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, German soldier and painter (d. 1916)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and painter (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and painter (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, German actor and director (d. 1931)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and director (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and director (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American lieutenant and pilot (d. 1908)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Finnish politician (d. 1955)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isak_<PERSON>la"}]}, {"year": "1883", "text": "<PERSON>, Czech-American economist and political scientist (d. 1950)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American economist and political scientist (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American economist and political scientist (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Australian boxer, rugby player, and actor (d. 1953)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian boxer, rugby player, and actor (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian boxer, rugby player, and actor (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American actor (d. 1970)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rug<PERSON>\"><PERSON></a>, American actor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ruggles\"><PERSON></a>, American actor (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, English actress (d. 1976)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Filipino lawyer, jurist, and politician (d. 1960)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Clar<PERSON>_<PERSON>._Recto\" title=\"C<PERSON>o M<PERSON> Re<PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Filipino lawyer, jurist, and politician (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clar<PERSON>_<PERSON>._Recto\" title=\"Claro M. Recto\"><PERSON><PERSON><PERSON> <PERSON></a>, Filipino lawyer, jurist, and politician (d. 1960)", "links": [{"title": "Claro M<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lar<PERSON>_<PERSON><PERSON>_Recto"}]}, {"year": "1893", "text": "<PERSON>, Burmese lawyer and politician, Prime Minister of Burma (d. 1977)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (d. 1977)", "links": [{"title": "Ba <PERSON>", "link": "https://wikipedia.org/wiki/Ba_Maw"}, {"title": "Prime Minister of Burma", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Burma"}]}, {"year": "1894", "text": "<PERSON>, American director, producer, and screenwriter (d. 1982)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/King_<PERSON>\" title=\"King <PERSON>\">King <PERSON></a>, American director, producer, and screenwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_<PERSON>\" title=\"King <PERSON>\">King <PERSON></a>, American director, producer, and screenwriter (d. 1982)", "links": [{"title": "King <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>r"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Indian academic and politician, 3rd president of India (d. 1969)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">president of India</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">president of India</a> (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist (d. 1970)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mongol prince and politician, head of state of Mengjiang (d. 1966)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Demchugdongrub\" title=\"Demchugdongrub\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongol prince and politician, head of state of <a href=\"https://wikipedia.org/wiki/Mengjiang\" title=\"Mengjiang\">Mengjiang</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Demchugdongrub\" title=\"Demchugdongrub\"><PERSON><PERSON>chu<PERSON><PERSON><PERSON><PERSON></a>, Mongol prince and politician, head of state of <a href=\"https://wikipedia.org/wiki/Mengjiang\" title=\"Mengjiang\">Mengjiang</a> (d. 1966)", "links": [{"title": "<PERSON><PERSON>chugdongrub", "link": "https://wikipedia.org/wiki/Demchugdongrub"}, {"title": "Mengjiang", "link": "https://wikipedia.org/wiki/Mengjiang"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Austrian-American singer and actress (d. 1977)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American singer and actress (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American singer and actress (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, 1st Prime Minister of Malaysia (d. 1990)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 1st Prime Minister of Malaysia (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 1st Prime Minister of Malaysia (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American physicist and lawyer, invented Xerography (d. 1968)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and lawyer, invented <a href=\"https://wikipedia.org/wiki/Xerography\" title=\"Xerography\">Xerography</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and lawyer, invented <a href=\"https://wikipedia.org/wiki/Xerography\" title=\"Xerography\">Xerography</a> (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Xerography", "link": "https://wikipedia.org/wiki/Xerography"}]}, {"year": "1909", "text": "<PERSON>, Australian philanthropist (d. 2012)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, Australian philanthropist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, Australian philanthropist (d. 2012)", "links": [{"title": "<PERSON> (philanthropist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)"}]}, {"year": "1911", "text": "<PERSON>, American poet and author (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actress (d. 1973)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Field\" title=\"Betty Field\"><PERSON></a>, American actress (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Field\" title=\"Betty Field\"><PERSON></a>, American actress (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Greek singer-songwriter (d. 2009)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ratigopo<PERSON>\" title=\"<PERSON><PERSON>igopo<PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rat<PERSON>u"}]}, {"year": "1914", "text": "<PERSON>, American author and screenwriter, co-created <PERSON> (d. 1974)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter, co-created <a href=\"https://wikipedia.org/wiki/Batman\" title=\"Batman\"><PERSON></a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter, co-created <a href=\"https://wikipedia.org/wiki/Batman\" title=\"Batman\"><PERSON></a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Egyptian-French singer, dancer, and actor (d. 1997)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tary\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer, dancer, and actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tary\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer, dancer, and actor (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_Gu%C3%A9tary"}]}, {"year": "1918", "text": "<PERSON>, American wrestler and manager (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American wrestler and manager (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Canadian colonel and politician, 21st Canadian Minister of National Defence (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and politician, 21st <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and politician, 21st <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of National Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Albanian politician (d. 2020)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Nexhmi<PERSON>_<PERSON>\" title=\"Nexhm<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Albanian politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nexhmi<PERSON>_<PERSON>\" title=\"Nexhm<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Albanian politician (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nexhmije_<PERSON>xha"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Guyanese politician, 1st Minister of Home Affairs (d. 2022)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guyanese politician, 1st Minister of Home Affairs (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guyanese politician, 1st Minister of Home Affairs (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress (d. 1995)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress and banker (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and banker (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and banker (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor (d. 2001)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American author and poet (d. 1968)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish film actress (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish film actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ir<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish film actress (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birgit<PERSON>_<PERSON>imer"}]}, {"year": "1930", "text": "<PERSON>, Argentinian-American actor and director (d. 1987)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American actor and director (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alejandro Rey\"><PERSON></a>, Argentinian-American actor and director (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor (d. 1955)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Egyptian actress and singer (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Shadia\" title=\"Shad<PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian actress and singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shadia\" title=\"Shad<PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian actress and singer (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shadia"}]}, {"year": "1932", "text": "<PERSON>, English racing driver and businessman (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and businessman (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Allison\"><PERSON></a>, English racing driver and businessman (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American pianist, composer, and conductor", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Dutch soprano", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>g"}]}, {"year": "1937", "text": "<PERSON>, American pianist and composer (d. 1989)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Chinese human rights activist (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese human rights activist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese human rights activist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Filipino activist and theorist (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino activist and theorist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino activist and theorist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Congolese politician (d. 1999)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese politician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese politician (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English-American journalist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actor and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rush\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tom Rush\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Indian singer-songwriter (d. 2011)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American comedian, actor, and singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and producer (d. 2004)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor and musician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American scientist and inventor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and inventor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and inventor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English actor (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian photographer and journalist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian photographer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian photographer and journalist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sebasti%C3%A3o_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2009)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Dan_<PERSON>s\" title=\"Dan Seals\"><PERSON></a>, American singer-songwriter and guitarist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dan_Seals\" title=\"Dan Seals\"><PERSON></a>, American singer-songwriter and guitarist (d. 2009)", "links": [{"title": "Dan <PERSON>s", "link": "https://wikipedia.org/wiki/Dan_Seals"}]}, {"year": "1949", "text": "<PERSON>, American actress, producer, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress, producer, and screenwriter", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1949", "text": "<PERSON><PERSON>, French actor, director, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>up"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Brazilian footballer and coach (d. 2014)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and coach (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marin<PERSON>_Chagas"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American wrestler (d. 2018)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American basketball player and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, French historian of mathematics and sinologist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French historian of mathematics and sinologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French historian of mathematics and sinologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American wrestler and manager (d. 2007)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and manager (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Brazilian environmentalist and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian environmentalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian environmentalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Silva"}]}, {"year": "1959", "text": "<PERSON>, Canadian actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Swiss tennis player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian equestrian rider", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian equestrian rider", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian equestrian rider", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Argentinian businessman and politician, President of Argentina", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Filipino politician, 15th President of the Philippines (d. 2021)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Indian cricketer and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Belizean-American mathematical physicist and academic", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belizean-American mathematical physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belizean-American mathematical physicist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Indian director, cinematographer, producer, and actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, cinematographer, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, cinematographer, producer, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Santosh_Sivan"}]}, {"year": "1964", "text": "<PERSON><PERSON>, English fashion designer and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English fashion designer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English fashion designer and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Bulgarian footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Brazilian priest and balloonist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Ad<PERSON>r_Ant%C3%B4<PERSON>_de_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian priest and balloonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t%C3%B4<PERSON>_de_Carli\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian priest and balloonist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adelir_Ant%C3%B4<PERSON>_<PERSON>_Carli"}]}, {"year": "1967", "text": "<PERSON>, American basketball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor (d. 2010)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, New Zealand-Australian singer-songwriter and guitarist (d. 2010)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Australian singer-songwriter and guitarist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Australian singer-songwriter and guitarist (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American puppeteer and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress and comedian", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian footballer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American basketball player and executive", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and executive", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, English footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>royd"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Swedish-Finnish drummer and songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Finnish drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Finnish drummer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON> <PERSON>, American wrestler and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Big_Show\" title=\"Big Show\">Big Show</a>, American wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Show\" title=\"Big Show\">Big Show</a>, American wrestler and actor", "links": [{"title": "Big Show", "link": "https://wikipedia.org/wiki/Big_Show"}]}, {"year": "1973", "text": "<PERSON>, Australian basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "Guy<PERSON><PERSON>, French musician, singer, composer, and record producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French musician, singer, composer, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French musician, singer, composer, and record producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor, voice artist, comedian, producer, writer, and director", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, comedian, producer, writer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, comedian, producer, writer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Bahamian-American mixed martial artist (d. 2016)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Slice\" title=\"Kimbo Slice\"><PERSON><PERSON></a>, Bahamian-American mixed martial artist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Slice\" title=\"Kimbo Slice\"><PERSON><PERSON></a>, Bahamian-American mixed martial artist (d. 2016)", "links": [{"title": "Kimbo Slice", "link": "https://wikipedia.org/wiki/Kimbo_Slice"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Bangladeshi cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French rally driver and mountain biker", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rally driver and mountain biker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rally driver and mountain biker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American musician and songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Russian ice dancer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian chef, author, restauranteur, and television personality", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Ran<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Ran<PERSON><PERSON> Brar\"><PERSON><PERSON><PERSON><PERSON></a>, Indian chef, author, restauranteur, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>n<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Ran<PERSON><PERSON> Brar\"><PERSON><PERSON><PERSON><PERSON></a>, Indian chef, author, restauranteur, and television personality", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ran<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Ivorian footballer (d. 2015)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Mexican singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>mayor_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Canadian basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)"}]}, {"year": "1983", "text": "<PERSON>, New Zealand rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Finnish ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Czech tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Petra_Cetkovsk%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petra_Cetkovsk%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Petra_Cetkovsk%C3%A1"}]}, {"year": "1985", "text": "<PERSON>, American bass player and songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dominican baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Pie\" title=\"<PERSON> Pie\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Pie\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Pie"}]}, {"year": "1985", "text": "<PERSON>, American basketball player and coach", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON> <PERSON>, American singer, songwriter, rapper, and record producer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_.<PERSON>ak\" title=\"Anderson .Paak\"><PERSON> <PERSON></a>, American singer, songwriter, rapper, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_.<PERSON>\" title=\"Anderson .Paak\"><PERSON> .<PERSON></a>, American singer, songwriter, rapper, and record producer", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_.Paak"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_Garc%C3%ADa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arc%C3%ADa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Javi_Garc%C3%ADa"}]}, {"year": "1987", "text": "<PERSON>, Italian figure skater", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Carolina_Kostner\" title=\"Carolina Kostner\"><PERSON></a>, Italian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carolina_Kostner\" title=\"<PERSON> Kostner\"><PERSON></a>, Italian figure skater", "links": [{"title": "<PERSON> Kostner", "link": "https://wikipedia.org/wiki/Carolina_Kostner"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Zimbabwean cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, New Zealand rugby player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player", "links": [{"title": "Zac <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1989", "text": "<PERSON>, American-Hungarian basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Hungarian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Hungarian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American surfer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, South Korean singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oo-hyun\" title=\"<PERSON> Woo-hyun\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oo-hyun\" title=\"<PERSON> Woo-hyun\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyun"}]}, {"year": "1992", "text": "<PERSON>, Portuguese-Dutch footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Hakan_%C3%87alhano%C4%9Flu\" title=\"<PERSON><PERSON> Çalhanoğlu\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hakan_%C3%87alhano%C4%9Flu\" title=\"<PERSON><PERSON> Çalhanoğlu\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hakan_%C3%87alhano%C4%9Flu"}]}, {"year": "1994", "text": "<PERSON>, Canadian singer-songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Argentine basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gabriel Deck\"><PERSON></a>, Argentine basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, German footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Vander <PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Vander Esch\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Japanese basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, South Korean singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/I.N\" title=\"I.N\"><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I.N\" title=\"I.N\"><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "I.N", "link": "https://wikipedia.org/wiki/I.N"}]}], "Deaths": [{"year": "538", "text": "<PERSON><PERSON><PERSON> of Antioch, patriarch of Antioch (b. 465)", "html": "538 - <a href=\"https://wikipedia.org/wiki/Se<PERSON><PERSON>_of_Antioch\" title=\"<PERSON><PERSON><PERSON> of Antioch\"><PERSON><PERSON><PERSON> of Antioch</a>, <a href=\"https://wikipedia.org/wiki/Patriarch_of_Antioch\" title=\"Patriarch of Antioch\">patriarch of Antioch</a> (b. 465)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se<PERSON><PERSON>_of_Antioch\" title=\"<PERSON><PERSON><PERSON> of Antioch\"><PERSON><PERSON><PERSON> of Antioch</a>, <a href=\"https://wikipedia.org/wiki/Patriarch_of_Antioch\" title=\"Patriarch of Antioch\">patriarch of Antioch</a> (b. 465)", "links": [{"title": "<PERSON><PERSON><PERSON> of Antioch", "link": "https://wikipedia.org/wiki/Severus_of_Antioch"}, {"title": "Patriarch of Antioch", "link": "https://wikipedia.org/wiki/Patriarch_of_Antioch"}]}, {"year": "1204", "text": "<PERSON><PERSON>, Byzantine emperor (b. 1182)", "html": "1204 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV_Angelo<PERSON>\" title=\"<PERSON><PERSON> IV <PERSON>\"><PERSON><PERSON></a>, Byzantine emperor (b. 1182)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV_<PERSON>\" title=\"<PERSON><PERSON> IV <PERSON>\"><PERSON><PERSON></a>, Byzantine emperor (b. 1182)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alexios_IV_Angelos"}]}, {"year": "1229", "text": "<PERSON>, sixth <PERSON><PERSON><PERSON><PERSON> of Tayyibi Isma'ilism", "html": "1229 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sixth <i><a href=\"https://wikipedia.org/wiki/Da%27i_al-Mutlaq\" title=\"<PERSON><PERSON><PERSON> al-Mutlaq\"><PERSON><PERSON><PERSON><PERSON></a></i> of <a href=\"https://wikipedia.org/wiki/Tayyibi_Isma%27ilism\" title=\"Tayyibi Isma'ilism\"><PERSON>yyibi <PERSON>'il<PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sixth <i><a href=\"https://wikipedia.org/wiki/Da%27i_al-Mutlaq\" title=\"<PERSON><PERSON><PERSON> al-<PERSON>tlaq\"><PERSON><PERSON><PERSON><PERSON></a></i> of <a href=\"https://wikipedia.org/wiki/Tayyibi_Isma%27ilism\" title=\"Tayyibi Isma'ilism\">Tayyibi Isma'ilism</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> al-<PERSON>", "link": "https://wikipedia.org/wiki/Da%27i_<PERSON>-<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tayyibi_Isma%27ilism"}]}, {"year": "1250", "text": "<PERSON>, Count of Artois (b. 1216)", "html": "1250 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Artois\" title=\"<PERSON>, Count of Artois\"><PERSON>, Count of Artois</a> (b. 1216)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Artois\" title=\"<PERSON>, Count of Artois\"><PERSON>, Count of Artois</a> (b. 1216)", "links": [{"title": "<PERSON>, Count of Artois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1250", "text": "<PERSON>, Earl of Salisbury, English martyr (b. 1212)", "html": "1250 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9e\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON>, Earl of Salisbury</a>, English martyr (b. 1212)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9e\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON>, Earl of Salisbury</a>, English martyr (b. 1212)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_II_Longesp%C3%A9e"}]}, {"year": "1265", "text": "<PERSON><PERSON><PERSON>, Mongol ruler (b. 1217)", "html": "1265 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongol ruler (b. 1217)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongol ruler (b. 1217)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1285", "text": "<PERSON><PERSON> of Landsberg (b. 1242)", "html": "1285 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Landsberg\" title=\"<PERSON><PERSON> of Landsberg\"><PERSON><PERSON> of Landsberg</a> (b. 1242)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Landsberg\" title=\"<PERSON><PERSON> of Landsberg\"><PERSON><PERSON> of Landsberg</a> (b. 1242)", "links": [{"title": "<PERSON><PERSON> of Landsberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Landsberg"}]}, {"year": "1296", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> II of Poland (b. 1257)", "html": "1296 - <a href=\"https://wikipedia.org/wiki/Przemys%C5%82_II\" title=\"Przemysł II\">Przemysł II</a> of Poland (b. 1257)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Przemys%C5%82_II\" title=\"Przemysł II\">Przemysł II</a> of Poland (b. 1257)", "links": [{"title": "Przemysł II", "link": "https://wikipedia.org/wiki/Przemys%C5%82_II"}]}, {"year": "1314", "text": "<PERSON> Anjou, queen of Serbia (b. 1236)", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" class=\"mw-redirect\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a>, queen of Serbia (b. 1236)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a>, queen of Serbia (b. 1236)", "links": [{"title": "<PERSON> Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1382", "text": "<PERSON> of France, Duchess of Orléans (b. 1328)", "html": "1382 - <a href=\"https://wikipedia.org/wiki/Blanche_of_France,_Duchess_of_Orl%C3%A9ans\" title=\"<PERSON> of France, Duchess of Orléans\"><PERSON> of France, Duchess of Orléans</a> (b. 1328)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Blanche_of_France,_Duchess_of_Orl%C3%A9ans\" title=\"<PERSON> of France, Duchess of Orléans\"><PERSON> of France, Duchess of Orléans</a> (b. 1328)", "links": [{"title": "<PERSON> of France, Duchess of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>_of_France,_Duchess_<PERSON>_Orl%C3%A9ans"}]}, {"year": "1537", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, Italian humanitarian (b. 1481)", "html": "1537 - <PERSON> <a href=\"https://wikipedia.org/wiki/G<PERSON>lam<PERSON>_<PERSON>ni\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian humanitarian (b. 1481)", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/G<PERSON>lam<PERSON>_<PERSON>ni\" title=\"<PERSON><PERSON>lam<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian humanitarian (b. 1481)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>lamo_<PERSON>ni"}]}, {"year": "1599", "text": "<PERSON>, Scottish theologian and academic (b. 1555)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish theologian and academic (b. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish theologian and academic (b. 1555)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1676", "text": "<PERSON> of Russia (b. 1629)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/Alexis_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alexis_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1629)", "links": [{"title": "Alexis of Russia", "link": "https://wikipedia.org/wiki/Alexis_of_Russia"}]}, {"year": "1696", "text": "<PERSON> of Russia (b. 1666)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/Ivan_V_of_Russia\" title=\"Ivan V of Russia\"><PERSON> of Russia</a> (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ivan_V_of_Russia\" title=\"Ivan V of Russia\"><PERSON> of Russia</a> (b. 1666)", "links": [{"title": "Ivan V of Russia", "link": "https://wikipedia.org/wiki/Ivan_V_of_Russia"}]}, {"year": "1709", "text": "<PERSON>, Italian violinist and composer (b. 1658)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1658)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON>, Russian emperor (b. 1672)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, Russian emperor (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, Russian emperor (b. 1672)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}]}, {"year": "1749", "text": "<PERSON>, Dutch painter (b. 1682)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, English playwright and poet (b. 1685)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English playwright and poet (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English playwright and poet (b. 1685)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1772", "text": "Princess <PERSON> of Saxe-Gotha (b. 1719)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Saxe-Gotha\" title=\"Princess <PERSON> of Saxe-Gotha\">Princess <PERSON> of Saxe-Gotha</a> (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Augusta_of_Saxe-Gotha\" title=\"Princess <PERSON> of Saxe-Gotha\">Princess <PERSON> of Saxe-Gotha</a> (b. 1719)", "links": [{"title": "Princess <PERSON> of Saxe-Gotha", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Saxe-Gotha"}]}, {"year": "1849", "text": "<PERSON>, French violinist and conductor (b. 1781)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and conductor (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and conductor (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Slovenian poet and lawyer (b. 1800)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/France_Pre%C5%A1eren\" title=\"France Prešeren\">France Prešeren</a>, Slovenian poet and lawyer (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_Pre%C5%A1eren\" title=\"France Prešeren\">France Preš<PERSON></a>, Slovenian poet and lawyer (b. 1800)", "links": [{"title": "France Prešeren", "link": "https://wikipedia.org/wiki/France_Pre%C5%A1eren"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, Italian entomologist and academic (b. 1773)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bassi\"><PERSON><PERSON><PERSON></a>, Italian entomologist and academic (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bassi\"><PERSON><PERSON><PERSON></a>, Italian entomologist and academic (b. 1773)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bassi"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Dutch chemist and academic (b. 1854)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch chemist and academic (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>boo<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">He<PERSON><PERSON></a>, Dutch chemist and academic (b. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Norwegian philosopher and activist (b. 1854)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Hans_<PERSON>%C3%A6ger\" title=\"<PERSON>\"><PERSON></a>, Norwegian philosopher and activist (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hans_<PERSON>%C3%A6ger\" title=\"<PERSON>\"><PERSON></a>, Norwegian philosopher and activist (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hans_J%C3%A6ger"}]}, {"year": "1915", "text": "<PERSON>, Canadian journalist, lawyer, and politician, 10th Lieutenant Governor of Quebec (b. 1838)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, lawyer, and politician, 10th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, lawyer, and politician, 10th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1921", "text": "<PERSON>, English actor and singer (b. 1876)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Russian zoologist, geographer, and philologist (b. 1842)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian zoologist, geographer, and philologist (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian zoologist, geographer, and philologist (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German chemist (b. 1857)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Bulgarian architect, designed the Sveti Sedmochislenitsi Church (b. 1867)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian architect, designed the <a href=\"https://wikipedia.org/wiki/Sveti_Sedmochislenitsi_Church\" class=\"mw-redirect\" title=\"Sveti Sedmochislenitsi Church\">Sveti Sedmochislenitsi Church</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian architect, designed the <a href=\"https://wikipedia.org/wiki/Sveti_Sedmochislenitsi_Church\" class=\"mw-redirect\" title=\"Sveti Sedmochislenitsi Church\">Sveti Sedmochislenitsi Church</a> (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Sveti Sedmochislenitsi Church", "link": "https://wikipedia.org/wiki/Sveti_Sedmochislenitsi_Church"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Finnish linguist and politician, Minister for Foreign Affairs (b. 1864)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/E<PERSON>il_Nestor_Set%C3%A4l%C3%A4\" title=\"<PERSON><PERSON>il Nestor Setälä\"><PERSON><PERSON><PERSON>estor <PERSON></a>, Finnish linguist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Finland)\" title=\"Minister for Foreign Affairs (Finland)\">Minister for Foreign Affairs</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_Nestor_Set%C3%A4l%C3%A4\" title=\"<PERSON>emil Nestor Setälä\"><PERSON><PERSON><PERSON> Nestor <PERSON></a>, Finnish linguist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Finland)\" title=\"Minister for Foreign Affairs (Finland)\">Minister for Foreign Affairs</a> (b. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eemil_Nestor_Set%C3%A4l%C3%A4"}, {"title": "Minister for Foreign Affairs (Finland)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Finland)"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and politician, 31st Vice President of the United States (b. 1860)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1938", "text": "<PERSON>, Ukrainian Jewish anarchist (b. 1876)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian Jewish anarchist (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian Jewish anarchist (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Italian fencer and coach (b. 1866)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Italo <PERSON>\"><PERSON><PERSON></a>, Italian fencer and coach (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Italo <PERSON>\"><PERSON><PERSON></a>, Italian fencer and coach (b. 1866)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>elli"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and manager (b. 1862)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, German physicist and academic, Nobel Prize laureate (b. 1891)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1957", "text": "<PERSON>, Hungarian-American mathematician and physicist (b. 1903)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and physicist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and physicist (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American head of the Office of Strategic Services (OSS) (b. 1883)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American head of the <a href=\"https://wikipedia.org/wiki/Office_of_Strategic_Services\" title=\"Office of Strategic Services\">Office of Strategic Services (OSS)</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American head of the <a href=\"https://wikipedia.org/wiki/Office_of_Strategic_Services\" title=\"Office of Strategic Services\">Office of Strategic Services (OSS)</a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Office of Strategic Services", "link": "https://wikipedia.org/wiki/Office_of_Strategic_Services"}]}, {"year": "1960", "text": "<PERSON><PERSON> <PERSON><PERSON>, English philosopher and academic (b. 1911)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. L. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. L. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and academic (b. 1911)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English architect and engineer, designed the Red telephone box and Liverpool Cathedral (b. 1880)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Red_telephone_box\" title=\"Red telephone box\">Red telephone box</a> and <a href=\"https://wikipedia.org/wiki/Liverpool_Cathedral\" title=\"Liverpool Cathedral\">Liverpool Cathedral</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Red_telephone_box\" title=\"Red telephone box\">Red telephone box</a> and <a href=\"https://wikipedia.org/wiki/Liverpool_Cathedral\" title=\"Liverpool Cathedral\">Liverpool Cathedral</a> (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Red telephone box", "link": "https://wikipedia.org/wiki/Red_telephone_box"}, {"title": "Liverpool Cathedral", "link": "https://wikipedia.org/wiki/Liverpool_Cathedral"}]}, {"year": "1963", "text": "<PERSON>, Italian-American actor (b. 1908)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German psychiatrist and author (b. 1888)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and author (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and author (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Northern Irish republican and anti partition politician (b. 1877)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Northern <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish republican</a> and anti partition politician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Northern <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish republican</a> and anti partition politician (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian independence movement activist, politician, writer and educationist (b. 1887)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Kanaiyal<PERSON>_<PERSON>_<PERSON>hi\" title=\"<PERSON><PERSON><PERSON><PERSON> Maneklal Munshi\"><PERSON><PERSON><PERSON><PERSON></a>, Indian independence movement activist, politician, writer and educationist (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON><PERSON> Maneklal Munshi\"><PERSON><PERSON><PERSON><PERSON></a>, Indian independence movement activist, politician, writer and educationist (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hi"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Greek singer-songwriter and bouzouki player (b. 1905)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (b.1886)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organic_chemist)\" class=\"mw-redirect\" title=\"<PERSON> (organic chemist)\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b.1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(organic_chemist)\" class=\"mw-redirect\" title=\"<PERSON> (organic chemist)\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b.1886)", "links": [{"title": "<PERSON> (organic chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organic_chemist)"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Norwegian composer and theorist (b. 1901)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian composer and theorist (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian composer and theorist (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eivind_<PERSON>n"}]}, {"year": "1979", "text": "<PERSON>, Hungarian-English physicist and engineer, Nobel Prize laureate (b. 1900)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Greek singer-songwriter (b. 1936)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American financier and diplomat, United States Ambassador to the United Kingdom (b. 1904)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "1985", "text": "<PERSON>, English businessman, co-founded Swallow Sidecar Company (b. 1901)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Swallow_Sidecar_Company\" title=\"Swallow Sidecar Company\">Swallow Sidecar Company</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Swallow_Sidecar_Company\" title=\"Swallow Sidecar Company\">Swallow Sidecar Company</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Swallow Sidecar Company", "link": "https://wikipedia.org/wiki/Swallow_Sidecar_Company"}]}, {"year": "1987", "text": "<PERSON>, American actress (b. 1905)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1934)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Del Shannon\"><PERSON></a>, American singer-songwriter and guitarist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Del Shannon\"><PERSON></a>, American singer-songwriter and guitarist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, British Australian nuclear physicist (b. 1916)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Australian nuclear physicist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Australian nuclear physicist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American sergeant (b. 1918)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Dunham\"><PERSON></a>, American sergeant (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>nham\"><PERSON></a>, American sergeant (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American pianist and composer (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American baseball player (b. 1925)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American motorcycle stunt rider (b. 1968)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle stunt rider (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle stunt rider (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic author, poet, and playwright, Nobel Prize laureate (b. 1902)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Halld%C3%B3r_Laxness\" title=\"Halld<PERSON><PERSON>ness\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halld%C3%B3r_Laxness\" title=\"Hall<PERSON><PERSON><PERSON>ness\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Halld%C3%B3r_Laxness"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1998", "text": "<PERSON><PERSON>, English soldier and politician, Secretary of State for Health (b. 1912)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of State for Health", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Health"}]}, {"year": "1998", "text": "<PERSON>, American economist and author (b. 1932)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Irish-born British novelist and philosopher (b. 1919)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born British novelist and philosopher (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born British novelist and philosopher (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Murdoch"}]}, {"year": "2000", "text": "<PERSON>, Canadian-American ice hockey player, coach, and sportscaster (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player, coach, and sportscaster (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player, coach, and sportscaster (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American football player (b. 1967)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Norwegian director and screenwriter (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian director and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian director and screenwriter (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivo_Caprino"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Singaporean architect and politician, 5th President of Singapore (b. 1936)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Ong_<PERSON>g_<PERSON>eong\" title=\"Ong Teng Cheong\"><PERSON><PERSON></a>, Singaporean architect and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ong_<PERSON>_<PERSON>eon<PERSON>\" title=\"Ong <PERSON>eong\"><PERSON><PERSON></a>, Singaporean architect and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ong_Teng_<PERSON>eong"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "2004", "text": "<PERSON>, American journalist and author (b. 1915)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Sri Lankan sailor and politician (b. 1944)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan sailor and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan sailor and politician (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English saxophonist, songwriter, and producer (b. 1945)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dean\"><PERSON></a>, English saxophonist, songwriter, and producer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dean\"><PERSON></a>, English saxophonist, songwriter, and producer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, French actor (b. 1953)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Japanese composer (b. 1914)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese composer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese composer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American model and actress (b. 1967)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian-American psychiatrist and academic (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychiatrist and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychiatrist and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American educator and cultural historian (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American educator and cultural historian (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Garrard Woodson\"><PERSON></a>, American educator and cultural historian (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American colonel and politician (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American baseball player and soldier (b. 1909)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and soldier (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and soldier (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Brazilian singer-songwriter (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Brazilian singer-songwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Brazilian singer-songwriter (b. 1945)", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "2012", "text": "<PERSON>, Argentinian singer-songwriter (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian singer-songwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian singer-songwriter (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Italian cardinal (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American conductor and educator (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist and author (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American scientist (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American scientist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American scientist (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Dutch physician and politician, Deputy Prime Minister of the Netherlands (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rst"}, {"title": "Deputy Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Brazilian footballer (b. 1988)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_May_1988)\" title=\"<PERSON><PERSON> (footballer, born May 1988)\"><PERSON><PERSON></a>, Brazilian footballer (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_May_1988)\" title=\"<PERSON><PERSON> (footballer, born May 1988)\"><PERSON><PERSON></a>, Brazilian footballer (b. 1988)", "links": [{"title": "<PERSON><PERSON> (footballer, born May 1988)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_May_1988)"}]}, {"year": "2014", "text": "<PERSON>, American sculptor and painter (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>, Finnish physician and parapsychologist (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish physician and parapsychologist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish physician and parapsychologist (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Argentine actress (b. 1914)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine actress (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine actress (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Indian poet and songwriter (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and songwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and songwriter (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English historian, author, and critic (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and critic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and critic (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, French ballerina (b. 1933)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Verd<PERSON>\"><PERSON><PERSON></a>, French ballerina (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Verd<PERSON>\"><PERSON><PERSON></a>, French ballerina (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Violette_Verdy"}]}, {"year": "2017", "text": "<PERSON>, English physicist, Nobel laureate (b. 1933)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel laureate</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel laureate</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Japanese idol singer (b. 1998)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese idol singer (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese idol singer (b. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, English scriptwriter (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scriptwriter)\" title=\"<PERSON> (scriptwriter)\"><PERSON></a>, English scriptwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(scriptwriter)\" title=\"<PERSON> (scriptwriter)\"><PERSON></a>, English scriptwriter (b. 1929)", "links": [{"title": "<PERSON> (scriptwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(scriptwriter)"}]}, {"year": "2020", "text": "<PERSON>, American actor (b. 1935)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American football player and coach (b. 1943)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American singer (b. 1944)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (b. 1944)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Finnish professional hockey player (b. 1963)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Arto_He<PERSON>n\" title=\"Art<PERSON> He<PERSON>n\"><PERSON><PERSON></a>, Finnish professional hockey player (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arto_<PERSON>\" title=\"Art<PERSON>\"><PERSON><PERSON></a>, Finnish professional hockey player (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arto_Heisk<PERSON>n"}]}, {"year": "2025", "text": "<PERSON>, American football player and coach (b. 1950)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, Namibian politician, 1st President of Namibia (b. 1929)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Namibia\" title=\"President of Namibia\">President of Namibia</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Namibia\" title=\"President of Namibia\">President of Namibia</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Namibia", "link": "https://wikipedia.org/wiki/President_of_Namibia"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON>, Brother of the 14th Dal<PERSON> (b. 1928)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/Gyalo_Thondup\" title=\"Gyalo Thondup\"><PERSON><PERSON><PERSON>du<PERSON></a>, Brother of the 14th Dal<PERSON> Lama (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gyalo_Thondup\" title=\"Gyalo Thondup\"><PERSON><PERSON><PERSON>hon<PERSON></a>, Brother of the 14th Dal<PERSON> Lama (b. 1928)", "links": [{"title": "Gyalo Thondup", "link": "https://wikipedia.org/wiki/Gyalo_Thondup"}]}]}}