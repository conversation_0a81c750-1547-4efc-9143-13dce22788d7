{"date": "December 28", "url": "https://wikipedia.org/wiki/December_28", "data": {"Events": [{"year": "418", "text": "A papal election begins, resulting in the election of <PERSON> <PERSON><PERSON><PERSON>.", "html": "418 - A papal election begins, resulting in the election of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_I\" title=\"Pope Bonif<PERSON> I\">Pope <PERSON><PERSON><PERSON> I</a>.", "no_year_html": "A papal election begins, resulting in the election of <a href=\"https://wikipedia.org/wiki/Pope_<PERSON><PERSON><PERSON>_I\" title=\"Pope Bonif<PERSON> I\">Pope <PERSON><PERSON><PERSON> I</a>.", "links": [{"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>iface_I"}]}, {"year": "457", "text": "<PERSON><PERSON> is acclaimed as Western Roman emperor.", "html": "457 - <a href=\"https://wikipedia.org/wiki/Majorian\" title=\"Majorian\">Major<PERSON></a> is acclaimed as <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Majorian\" title=\"Majorian\">Major<PERSON></a> is acclaimed as <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman emperor</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "Western Roman Empire", "link": "https://wikipedia.org/wiki/Western_Roman_Empire"}]}, {"year": "484", "text": "<PERSON><PERSON><PERSON> II succeeds his father <PERSON><PERSON><PERSON> and becomes king of the Visigoths. He establishes his capital at Aire-sur-l'Adour (Southern Gaul).", "html": "484 - <a href=\"https://wikipedia.org/wiki/Alaric_II\" title=\"Alaric II\"><PERSON><PERSON><PERSON> II</a> succeeds his father <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and becomes king of the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a>. He establishes his capital at <a href=\"https://wikipedia.org/wiki/Aire-sur-l%27Adour\" title=\"Aire-sur-l'Adour\">Aire-sur-l'Adour</a> (Southern <a href=\"https://wikipedia.org/wiki/Gaul\" title=\"Gaul\">Gaul</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alari<PERSON>_II\" title=\"Alaric II\"><PERSON><PERSON><PERSON> II</a> succeeds his father <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and becomes king of the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a>. He establishes his capital at <a href=\"https://wikipedia.org/wiki/Aire-sur-l%27Adour\" title=\"Aire-sur-l'Adour\">Aire-sur-l'Adour</a> (Southern <a href=\"https://wikipedia.org/wiki/Gaul\" title=\"Gaul\">Gaul</a>).", "links": [{"title": "Alaric II", "link": "https://wikipedia.org/wiki/Alaric_II"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Euric"}, {"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}, {"title": "Aire-sur-l'Adour", "link": "https://wikipedia.org/wiki/Aire-sur-l%27Adour"}, {"title": "Gaul", "link": "https://wikipedia.org/wiki/Gaul"}]}, {"year": "893", "text": "An earthquake destroys the city of Dvin, Armenia.", "html": "893 - An earthquake <a href=\"https://wikipedia.org/wiki/893_Dvin_earthquake\" title=\"893 Dvin earthquake\">destroys</a> the city of <a href=\"https://wikipedia.org/wiki/Dvin_(ancient_city)\" title=\"Dvin (ancient city)\">Dvin</a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>.", "no_year_html": "An earthquake <a href=\"https://wikipedia.org/wiki/893_Dvin_earthquake\" title=\"893 Dvin earthquake\">destroys</a> the city of <a href=\"https://wikipedia.org/wiki/Dvin_(ancient_city)\" title=\"Dvin (ancient city)\">Dvin</a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>.", "links": [{"title": "893 Dvin earthquake", "link": "https://wikipedia.org/wiki/893_Dvin_earthquake"}, {"title": "Dvin (ancient city)", "link": "https://wikipedia.org/wiki/Dvin_(ancient_city)"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}]}, {"year": "1065", "text": "<PERSON> the Confessor's Romanesque monastic church at Westminster Abbey is consecrated.", "html": "1065 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a>'s <a href=\"https://wikipedia.org/wiki/Romanesque_architecture\" title=\"Romanesque architecture\">Romanesque</a> monastic church at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> is <a href=\"https://wikipedia.org/wiki/Consecration\" class=\"mw-redirect\" title=\"Consecration\">consecrated</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a>'s <a href=\"https://wikipedia.org/wiki/Romanesque_architecture\" title=\"Romanesque architecture\">Romanesque</a> monastic church at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> is <a href=\"https://wikipedia.org/wiki/Consecration\" class=\"mw-redirect\" title=\"Consecration\">consecrated</a>.", "links": [{"title": "<PERSON> the Confessor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Confessor"}, {"title": "Romanesque architecture", "link": "https://wikipedia.org/wiki/Romanesque_architecture"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}, {"title": "Consecration", "link": "https://wikipedia.org/wiki/Consecration"}]}, {"year": "1308", "text": "The reign of Emperor <PERSON><PERSON><PERSON> of Japan begins.", "html": "1308 - The reign of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor Han<PERSON><PERSON>\">Emperor Han<PERSON><PERSON></a> of Japan begins.", "no_year_html": "The reign of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan begins.", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}]}, {"year": "1659", "text": "The Marathas defeat the Adilshahi forces in the Battle of Kolhapur.", "html": "1659 - The <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\"><PERSON><PERSON><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Adil_Shahi_dynasty\" class=\"mw-redirect\" title=\"Adil Shahi dynasty\"><PERSON><PERSON><PERSON><PERSON></a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kolhapur\" class=\"mw-redirect\" title=\"Battle of Kolhapur\">Battle of Kolhapur</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\"><PERSON><PERSON><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Adil_Shahi_dynasty\" class=\"mw-redirect\" title=\"Adil Shahi dynasty\"><PERSON><PERSON><PERSON><PERSON></a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kolhapur\" class=\"mw-redirect\" title=\"Battle of Kolhapur\">Battle of Kolhapur</a>.", "links": [{"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}, {"title": "<PERSON>il <PERSON> dynasty", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Shahi_dynasty"}, {"title": "Battle of Kolhapur", "link": "https://wikipedia.org/wiki/Battle_of_Kolhapur"}]}, {"year": "1768", "text": "King <PERSON><PERSON><PERSON>'s coronation achieved through conquest as a king of Thailand and established Thonburi as a capital.", "html": "1768 - King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s coronation achieved through conquest as a king of <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> and established <a href=\"https://wikipedia.org/wiki/Thonburi\" title=\"Thonburi\">Thonburi</a> as a capital.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s coronation achieved through conquest as a king of <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> and established <a href=\"https://wikipedia.org/wiki/Thonburi\" title=\"Thonburi\">Thonburi</a> as a capital.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taksin"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "Thonburi", "link": "https://wikipedia.org/wiki/Thonburi"}]}, {"year": "1795", "text": "Construction of Yonge Street, formerly recognized as the longest street in the world, begins in York, Upper Canada (present-day Toronto).", "html": "1795 - Construction of <a href=\"https://wikipedia.org/wiki/Yonge_Street\" title=\"Yonge Street\">Yonge Street</a>, formerly recognized as the longest street in the world, begins in <a href=\"https://wikipedia.org/wiki/York,_Upper_Canada\" title=\"York, Upper Canada\">York, Upper Canada</a> (present-day <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>).", "no_year_html": "Construction of <a href=\"https://wikipedia.org/wiki/Yonge_Street\" title=\"Yonge Street\">Yonge Street</a>, formerly recognized as the longest street in the world, begins in <a href=\"https://wikipedia.org/wiki/York,_Upper_Canada\" title=\"York, Upper Canada\">York, Upper Canada</a> (present-day <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>).", "links": [{"title": "Yonge Street", "link": "https://wikipedia.org/wiki/Yonge_<PERSON>"}, {"title": "York, Upper Canada", "link": "https://wikipedia.org/wiki/York,_Upper_Canada"}, {"title": "Toronto", "link": "https://wikipedia.org/wiki/Toronto"}]}, {"year": "1832", "text": "<PERSON> becomes the first Vice President of the United States to resign. He resigned after being elected Senator from South Carolina.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> to resign. He resigned after being elected Senator from South Carolina.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> to resign. He resigned after being elected Senator from South Carolina.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON><PERSON> leads his Seminole warriors in Florida into the Second Seminole War against the United States Army.", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Osceola\" title=\"Osceola\"><PERSON><PERSON><PERSON><PERSON></a> leads his <a href=\"https://wikipedia.org/wiki/Seminole\" title=\"Seminole\">Seminole</a> warriors in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> into the <a href=\"https://wikipedia.org/wiki/Second_Seminole_War\" title=\"Second Seminole War\">Second Seminole War</a> against the United States Army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Osceola\" title=\"Osceola\"><PERSON><PERSON><PERSON><PERSON></a> leads his <a href=\"https://wikipedia.org/wiki/Seminole\" title=\"Seminole\">Seminole</a> warriors in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> into the <a href=\"https://wikipedia.org/wiki/Second_Seminole_War\" title=\"Second Seminole War\">Second Seminole War</a> against the United States Army.", "links": [{"title": "Osceola", "link": "https://wikipedia.org/wiki/Osceola"}, {"title": "Seminole", "link": "https://wikipedia.org/wiki/Seminole"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Second Seminole War", "link": "https://wikipedia.org/wiki/Second_Seminole_War"}]}, {"year": "1836", "text": "South Australia and Adelaide are founded.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/South_Australia\" title=\"South Australia\">South Australia</a> and <a href=\"https://wikipedia.org/wiki/Adelaide\" title=\"Adelaide\">Adelaide</a> are founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Australia\" title=\"South Australia\">South Australia</a> and <a href=\"https://wikipedia.org/wiki/Adelaide\" title=\"Adelaide\">Adelaide</a> are founded.", "links": [{"title": "South Australia", "link": "https://wikipedia.org/wiki/South_Australia"}, {"title": "Adelaide", "link": "https://wikipedia.org/wiki/Adelaide"}]}, {"year": "1836", "text": "Spain recognizes the independence of Mexico with the signing of the Santa María-Calatrava Treaty.", "html": "1836 - Spain recognizes the independence of Mexico with the signing of the <a href=\"https://wikipedia.org/wiki/Spanish_attempts_to_reconquer_Mexico\" title=\"Spanish attempts to reconquer Mexico\">Santa María-Calatrava Treaty</a>.", "no_year_html": "Spain recognizes the independence of Mexico with the signing of the <a href=\"https://wikipedia.org/wiki/Spanish_attempts_to_reconquer_Mexico\" title=\"Spanish attempts to reconquer Mexico\">Santa María-Calatrava Treaty</a>.", "links": [{"title": "Spanish attempts to reconquer Mexico", "link": "https://wikipedia.org/wiki/Spanish_attempts_to_reconquer_Mexico"}]}, {"year": "1846", "text": "Iowa is admitted as the 29th U.S. state.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Iowa\" title=\"Iowa\">Iowa</a> is admitted as the 29th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iowa\" title=\"Iowa\">Iowa</a> is admitted as the 29th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Iowa", "link": "https://wikipedia.org/wiki/Iowa"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1879", "text": "Tay Bridge disaster: The central part of the Tay Rail Bridge in Dundee, Scotland, United Kingdom collapses as a train passes over it, killing 75.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Tay_Bridge_disaster\" title=\"Tay Bridge disaster\">Tay Bridge disaster</a>: The central part of the <a href=\"https://wikipedia.org/wiki/Tay_Rail_Bridge\" class=\"mw-redirect\" title=\"Tay Rail Bridge\">Tay Rail Bridge</a> in <a href=\"https://wikipedia.org/wiki/Dundee\" title=\"Dundee\">Dundee</a>, Scotland, United Kingdom collapses as a train passes over it, killing 75.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tay_Bridge_disaster\" title=\"Tay Bridge disaster\">Tay Bridge disaster</a>: The central part of the <a href=\"https://wikipedia.org/wiki/Tay_Rail_Bridge\" class=\"mw-redirect\" title=\"Tay Rail Bridge\">Tay Rail Bridge</a> in <a href=\"https://wikipedia.org/wiki/Dundee\" title=\"Dundee\">Dundee</a>, Scotland, United Kingdom collapses as a train passes over it, killing 75.", "links": [{"title": "Tay Bridge disaster", "link": "https://wikipedia.org/wiki/Tay_Bridge_disaster"}, {"title": "Tay Rail Bridge", "link": "https://wikipedia.org/wiki/Tay_Rail_Bridge"}, {"title": "Dundee", "link": "https://wikipedia.org/wiki/Dundee"}]}, {"year": "1885", "text": "Indian National Congress, a political party of India, is founded in Bombay Presidency, British India.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a>, a political party of India, is founded in <a href=\"https://wikipedia.org/wiki/Bombay_Presidency\" title=\"Bombay Presidency\">Bombay Presidency</a>, British India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a>, a political party of India, is founded in <a href=\"https://wikipedia.org/wiki/Bombay_Presidency\" title=\"Bombay Presidency\">Bombay Presidency</a>, British India.", "links": [{"title": "Indian National Congress", "link": "https://wikipedia.org/wiki/Indian_National_Congress"}, {"title": "Bombay Presidency", "link": "https://wikipedia.org/wiki/Bombay_Presidency"}]}, {"year": "1895", "text": "The <PERSON><PERSON><PERSON> brothers perform for their first paying audience at the Grand Cafe in Boulevard des Capucines.", "html": "1895 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> and <PERSON>\"><PERSON><PERSON><PERSON> brothers</a> perform for their first paying audience at the Grand Cafe in <i>Boulevard des Capucines</i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> and <PERSON>\"><PERSON><PERSON><PERSON> brothers</a> perform for their first paying audience at the Grand Cafe in <i>Boulevard des Capucines</i>.", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1895", "text": "<PERSON> publishes a paper detailing his discovery of a new type of radiation, which later will be known as x-rays.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Wilhelm_R%C3%B6ntgen\" title=\"<PERSON>\"><PERSON></a> publishes a paper detailing his discovery of a new type of <a href=\"https://wikipedia.org/wiki/Radiation\" title=\"Radiation\">radiation</a>, which later will be known as <a href=\"https://wikipedia.org/wiki/X-ray\" title=\"X-ray\">x-rays</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelm_<PERSON>%C3%B6ntgen\" title=\"<PERSON>\"><PERSON></a> publishes a paper detailing his discovery of a new type of <a href=\"https://wikipedia.org/wiki/Radiation\" title=\"Radiation\">radiation</a>, which later will be known as <a href=\"https://wikipedia.org/wiki/X-ray\" title=\"X-ray\">x-rays</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_R%C3%B6ntgen"}, {"title": "Radiation", "link": "https://wikipedia.org/wiki/Radiation"}, {"title": "X-ray", "link": "https://wikipedia.org/wiki/X-ray"}]}, {"year": "1902", "text": "The Syracuse Athletic Club defeat the New York Philadelphians, 5-0, in the first indoor professional football game, which was held at Madison Square Garden.", "html": "1902 - The <a href=\"https://wikipedia.org/wiki/Syracuse_Pros\" title=\"Syracuse Pros\">Syracuse Athletic Club</a> <a href=\"https://wikipedia.org/wiki/World_Series_of_Football_(1902%E2%80%9303)\" title=\"World Series of Football (1902-03)\">defeat</a> the <a href=\"https://wikipedia.org/wiki/New_York_(World_Series_of_Football)\" title=\"New York (World Series of Football)\">New York Philadelphians</a>, 5-0, in the <a href=\"https://wikipedia.org/wiki/Indoor_American_football\" class=\"mw-redirect\" title=\"Indoor American football\">first indoor professional football</a> game, which was held at <a href=\"https://wikipedia.org/wiki/Madison_Square_Garden_(1890)\" title=\"Madison Square Garden (1890)\">Madison Square Garden</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Syracuse_Pros\" title=\"Syracuse Pros\">Syracuse Athletic Club</a> <a href=\"https://wikipedia.org/wiki/World_Series_of_Football_(1902%E2%80%9303)\" title=\"World Series of Football (1902-03)\">defeat</a> the <a href=\"https://wikipedia.org/wiki/New_York_(World_Series_of_Football)\" title=\"New York (World Series of Football)\">New York Philadelphians</a>, 5-0, in the <a href=\"https://wikipedia.org/wiki/Indoor_American_football\" class=\"mw-redirect\" title=\"Indoor American football\">first indoor professional football</a> game, which was held at <a href=\"https://wikipedia.org/wiki/Madison_Square_Garden_(1890)\" title=\"Madison Square Garden (1890)\">Madison Square Garden</a>.", "links": [{"title": "Syracuse Pros", "link": "https://wikipedia.org/wiki/Syracuse_Pros"}, {"title": "World Series of Football (1902-03)", "link": "https://wikipedia.org/wiki/World_Series_of_Football_(1902%E2%80%9303)"}, {"title": "New York (World Series of Football)", "link": "https://wikipedia.org/wiki/New_York_(World_Series_of_Football)"}, {"title": "Indoor American football", "link": "https://wikipedia.org/wiki/Indoor_American_football"}, {"title": "Madison Square Garden (1890)", "link": "https://wikipedia.org/wiki/Madison_Square_Garden_(1890)"}]}, {"year": "1908", "text": "The 7.1 Mw  Messina earthquake shakes Southern Italy with a maximum Mercalli intensity of XI (Extreme), killing between about 80,000.", "html": "1908 - The 7.1 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1908_Messina_earthquake\" title=\"1908 Messina earthquake\">Messina earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Southern_Italy\" title=\"Southern Italy\">Southern Italy</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), killing between about 80,000.", "no_year_html": "The 7.1 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1908_Messina_earthquake\" title=\"1908 Messina earthquake\">Messina earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Southern_Italy\" title=\"Southern Italy\">Southern Italy</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), killing between about 80,000.", "links": [{"title": "1908 Messina earthquake", "link": "https://wikipedia.org/wiki/1908_Messina_earthquake"}, {"title": "Southern Italy", "link": "https://wikipedia.org/wiki/Southern_Italy"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1912", "text": "The first municipally owned streetcars take to the streets in San Francisco.", "html": "1912 - The first <a href=\"https://wikipedia.org/wiki/San_Francisco_Municipal_Railway\" title=\"San Francisco Municipal Railway\">municipally owned</a> <a href=\"https://wikipedia.org/wiki/Tram\" title=\"Tram\">streetcars</a> take to the streets in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/San_Francisco_Municipal_Railway\" title=\"San Francisco Municipal Railway\">municipally owned</a> <a href=\"https://wikipedia.org/wiki/Tram\" title=\"Tram\">streetcars</a> take to the streets in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "links": [{"title": "San Francisco Municipal Railway", "link": "https://wikipedia.org/wiki/San_Francisco_Municipal_Railway"}, {"title": "Tram", "link": "https://wikipedia.org/wiki/Tram"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1918", "text": "<PERSON>, while detained in Holloway prison, becomes the first woman to be elected Member of Parliament (MP) to the British House of Commons.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, while detained in Holloway prison, becomes the first woman to be elected <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP) to the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">British House of Commons</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, while detained in Holloway prison, becomes the first woman to be elected <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP) to the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">British House of Commons</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Member of Parliament (United Kingdom)", "link": "https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)"}, {"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}]}, {"year": "1941", "text": "World War II: Operation Anthropoid, the plot to assassinate high-ranking Nazi officer <PERSON><PERSON><PERSON>, commences.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Anthropoid\" class=\"mw-redirect\" title=\"Operation Anthropoid\">Operation Anthropoid</a>, the plot to assassinate high-ranking <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> officer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, commences.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Anthropoid\" class=\"mw-redirect\" title=\"Operation Anthropoid\">Operation Anthropoid</a>, the plot to assassinate high-ranking <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> officer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, commences.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Anthropoid", "link": "https://wikipedia.org/wiki/Operation_Anthropoid"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "Soviet authorities launch Operation Ulussy, beginning the deportation of the Kalmyk nation to Siberia and Central Asia.", "html": "1943 - Soviet authorities launch Operation Ulussy, beginning the <a href=\"https://wikipedia.org/wiki/Kalmyk_deportations_of_1943\" class=\"mw-redirect\" title=\"Kalmyk deportations of 1943\">deportation of the Kalmyk nation</a> to Siberia and Central Asia.", "no_year_html": "Soviet authorities launch Operation Ulussy, beginning the <a href=\"https://wikipedia.org/wiki/Kalmyk_deportations_of_1943\" class=\"mw-redirect\" title=\"Kalmyk deportations of 1943\">deportation of the Kalmyk nation</a> to Siberia and Central Asia.", "links": [{"title": "Kalmyk deportations of 1943", "link": "https://wikipedia.org/wiki/Kalmyk_deportations_of_1943"}]}, {"year": "1943", "text": "World War II: After eight days of brutal house-to-house fighting, the Battle of Ortona concludes with the victory of the 1st Canadian Infantry Division over the German 1st Parachute Division and the capture of the Italian town of Ortona.", "html": "1943 - World War II: After eight days of brutal <a href=\"https://wikipedia.org/wiki/Urban_warfare\" title=\"Urban warfare\">house-to-house fighting</a>, the <a href=\"https://wikipedia.org/wiki/Battle_of_Ortona\" title=\"Battle of Ortona\">Battle of Ortona</a> concludes with the victory of the <a href=\"https://wikipedia.org/wiki/1st_Canadian_Infantry_Division\" class=\"mw-redirect\" title=\"1st Canadian Infantry Division\">1st Canadian Infantry Division</a> over the <a href=\"https://wikipedia.org/wiki/1st_Parachute_Division_(Germany)\" title=\"1st Parachute Division (Germany)\">German 1st Parachute Division</a> and the capture of the Italian town of <a href=\"https://wikipedia.org/wiki/Ortona\" title=\"Ortona\">Ortona</a>.", "no_year_html": "World War II: After eight days of brutal <a href=\"https://wikipedia.org/wiki/Urban_warfare\" title=\"Urban warfare\">house-to-house fighting</a>, the <a href=\"https://wikipedia.org/wiki/Battle_of_Ortona\" title=\"Battle of Ortona\">Battle of Ortona</a> concludes with the victory of the <a href=\"https://wikipedia.org/wiki/1st_Canadian_Infantry_Division\" class=\"mw-redirect\" title=\"1st Canadian Infantry Division\">1st Canadian Infantry Division</a> over the <a href=\"https://wikipedia.org/wiki/1st_Parachute_Division_(Germany)\" title=\"1st Parachute Division (Germany)\">German 1st Parachute Division</a> and the capture of the Italian town of <a href=\"https://wikipedia.org/wiki/Ortona\" title=\"Ortona\">Ortona</a>.", "links": [{"title": "Urban warfare", "link": "https://wikipedia.org/wiki/Urban_warfare"}, {"title": "Battle of Ortona", "link": "https://wikipedia.org/wiki/Battle_of_Ortona"}, {"title": "1st Canadian Infantry Division", "link": "https://wikipedia.org/wiki/1st_Canadian_Infantry_Division"}, {"title": "1st Parachute Division (Germany)", "link": "https://wikipedia.org/wiki/1st_Parachute_Division_(Germany)"}, {"title": "Ortona", "link": "https://wikipedia.org/wiki/Ortona"}]}, {"year": "1944", "text": "<PERSON> becomes the first player to score eight points in one game of NHL ice hockey.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first player to score eight points in one game of <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">NHL</a> <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">ice hockey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first player to score eight points in one game of <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">NHL</a> <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">ice hockey</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}, {"title": "Ice hockey", "link": "https://wikipedia.org/wiki/Ice_hockey"}]}, {"year": "1948", "text": "The DC-3 airliner NC16002 disappears 80 kilometres (50 mi) south of Miami.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">DC-3</a> airliner <i><a href=\"https://wikipedia.org/wiki/1948_Airborne_Transport_DC-3_(DST)_disappearance\" class=\"mw-redirect\" title=\"1948 Airborne Transport DC-3 (DST) disappearance\">NC16002</a></i> disappears 80 kilometres (50 mi) south of <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">DC-3</a> airliner <i><a href=\"https://wikipedia.org/wiki/1948_Airborne_Transport_DC-3_(DST)_disappearance\" class=\"mw-redirect\" title=\"1948 Airborne Transport DC-3 (DST) disappearance\">NC16002</a></i> disappears 80 kilometres (50 mi) south of <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>.", "links": [{"title": "Douglas DC-3", "link": "https://wikipedia.org/wiki/Douglas_DC-3"}, {"title": "1948 Airborne Transport DC-3 (DST) disappearance", "link": "https://wikipedia.org/wiki/1948_Airborne_Transport_DC-3_(DST)_disappearance"}, {"title": "Miami", "link": "https://wikipedia.org/wiki/Miami"}]}, {"year": "1956", "text": "<PERSON>, <PERSON> and <PERSON><PERSON><PERSON> meet in Baling, Malaya to try and resolve the Malayan Emergency situation.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>(Singaporean_politician)\" title=\"<PERSON> (Singaporean politician)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> meet in <a href=\"https://wikipedia.org/wiki/Baling\" class=\"mw-redirect\" title=\"Baling\">Baling</a>, <a href=\"https://wikipedia.org/wiki/Federation_of_Malaya\" title=\"Federation of Malaya\">Malaya</a> to <a href=\"https://wikipedia.org/wiki/Baling_Talks\" title=\"Baling Talks\">try and resolve</a> the <a href=\"https://wikipedia.org/wiki/Malayan_Emergency\" title=\"Malayan Emergency\">Malayan Emergency</a> situation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>(Singaporean_politician)\" title=\"<PERSON> (Singaporean politician)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> meet in <a href=\"https://wikipedia.org/wiki/Baling\" class=\"mw-redirect\" title=\"Baling\">Baling</a>, <a href=\"https://wikipedia.org/wiki/Federation_of_Malaya\" title=\"Federation of Malaya\">Malaya</a> to <a href=\"https://wikipedia.org/wiki/Baling_Talks\" title=\"Baling Talks\">try and resolve</a> the <a href=\"https://wikipedia.org/wiki/Malayan_Emergency\" title=\"Malayan Emergency\">Malayan Emergency</a> situation.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}, {"title": "<PERSON> (Singaporean politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Singaporean_politician)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Baling"}, {"title": "Federation of Malaya", "link": "https://wikipedia.org/wiki/Federation_of_Malaya"}, {"title": "Baling Talks", "link": "https://wikipedia.org/wiki/Baling_Talks"}, {"title": "Malayan Emergency", "link": "https://wikipedia.org/wiki/Malayan_Emergency"}]}, {"year": "1958", "text": "\"Greatest Game Ever Played\": The Baltimore Colts defeat the New York Giants in the first ever National Football League sudden death overtime game at New York's Yankee Stadium to win the NFL Championship.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/1958_NFL_Championship_Game\" title=\"1958 NFL Championship Game\">\"Greatest Game Ever Played\"</a>: The <a href=\"https://wikipedia.org/wiki/History_of_the_Indianapolis_Colts\" title=\"History of the Indianapolis Colts\">Baltimore Colts</a> defeat the <a href=\"https://wikipedia.org/wiki/New_York_Giants\" title=\"New York Giants\">New York Giants</a> in the first ever <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> <a href=\"https://wikipedia.org/wiki/Sudden_death_(sport)\" title=\"Sudden death (sport)\">sudden death</a> <a href=\"https://wikipedia.org/wiki/Overtime_(sports)\" title=\"Overtime (sports)\">overtime</a> game at New York's <a href=\"https://wikipedia.org/wiki/Yankee_Stadium_(1923)\" title=\"Yankee Stadium (1923)\">Yankee Stadium</a> to win the NFL Championship.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1958_NFL_Championship_Game\" title=\"1958 NFL Championship Game\">\"Greatest Game Ever Played\"</a>: The <a href=\"https://wikipedia.org/wiki/History_of_the_Indianapolis_Colts\" title=\"History of the Indianapolis Colts\">Baltimore Colts</a> defeat the <a href=\"https://wikipedia.org/wiki/New_York_Giants\" title=\"New York Giants\">New York Giants</a> in the first ever <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> <a href=\"https://wikipedia.org/wiki/Sudden_death_(sport)\" title=\"Sudden death (sport)\">sudden death</a> <a href=\"https://wikipedia.org/wiki/Overtime_(sports)\" title=\"Overtime (sports)\">overtime</a> game at New York's <a href=\"https://wikipedia.org/wiki/Yankee_Stadium_(1923)\" title=\"Yankee Stadium (1923)\">Yankee Stadium</a> to win the NFL Championship.", "links": [{"title": "1958 NFL Championship Game", "link": "https://wikipedia.org/wiki/1958_NFL_Championship_Game"}, {"title": "History of the Indianapolis Colts", "link": "https://wikipedia.org/wiki/History_of_the_Indianapolis_Colts"}, {"title": "New York Giants", "link": "https://wikipedia.org/wiki/New_York_Giants"}, {"title": "National Football League", "link": "https://wikipedia.org/wiki/National_Football_League"}, {"title": "Sudden death (sport)", "link": "https://wikipedia.org/wiki/Sudden_death_(sport)"}, {"title": "Overtime (sports)", "link": "https://wikipedia.org/wiki/Overtime_(sports)"}, {"title": "Yankee Stadium (1923)", "link": "https://wikipedia.org/wiki/Yankee_Stadium_(1923)"}]}, {"year": "1967", "text": "American businesswoman <PERSON><PERSON> becomes the first woman to own a seat on the New York Stock Exchange.", "html": "1967 - American businesswoman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first woman to own a seat on the <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a>.", "no_year_html": "American businesswoman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first woman to own a seat on the <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "New York Stock Exchange", "link": "https://wikipedia.org/wiki/New_York_Stock_Exchange"}]}, {"year": "1972", "text": "The last scheduled day for induction into the military by the Selective Service System. Due to the fact that President <PERSON> declared this day a national day of mourning due to former President <PERSON>'s death, approximately 300 men were not able to report due to most Federal offices being closed. Since the draft was not resumed in 1973, they were never drafted.", "html": "1972 - The last scheduled day for induction into the military by the <a href=\"https://wikipedia.org/wiki/Selective_Service_System\" title=\"Selective Service System\">Selective Service System</a>. Due to the fact that President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declared this day a national day of mourning due to former President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s death, approximately 300 men were not able to report due to most Federal offices being closed. Since the draft was not resumed in 1973, they were never drafted.", "no_year_html": "The last scheduled day for induction into the military by the <a href=\"https://wikipedia.org/wiki/Selective_Service_System\" title=\"Selective Service System\">Selective Service System</a>. Due to the fact that President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declared this day a national day of mourning due to former President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s death, approximately 300 men were not able to report due to most Federal offices being closed. Since the draft was not resumed in 1973, they were never drafted.", "links": [{"title": "Selective Service System", "link": "https://wikipedia.org/wiki/Selective_Service_System"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "The United States Endangered Species Act is signed into law by President <PERSON>.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> <a href=\"https://wikipedia.org/wiki/Endangered_Species_Act\" class=\"mw-redirect\" title=\"Endangered Species Act\">Endangered Species Act</a> is signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> <a href=\"https://wikipedia.org/wiki/Endangered_Species_Act\" class=\"mw-redirect\" title=\"Endangered Species Act\">Endangered Species Act</a> is signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Endangered Species Act", "link": "https://wikipedia.org/wiki/Endangered_Species_Act"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "United Airlines Flight 173 crashes in a residential neighborhood near Portland International Airport, killing 10 people.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_173\" title=\"United Airlines Flight 173\">United Airlines Flight 173</a> crashes in a residential neighborhood near <a href=\"https://wikipedia.org/wiki/Portland_International_Airport\" title=\"Portland International Airport\">Portland International Airport</a>, killing 10 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_173\" title=\"United Airlines Flight 173\">United Airlines Flight 173</a> crashes in a residential neighborhood near <a href=\"https://wikipedia.org/wiki/Portland_International_Airport\" title=\"Portland International Airport\">Portland International Airport</a>, killing 10 people.", "links": [{"title": "United Airlines Flight 173", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_173"}, {"title": "Portland International Airport", "link": "https://wikipedia.org/wiki/Portland_International_Airport"}]}, {"year": "1989", "text": "A magnitude 5.6 earthquake hits Newcastle, New South Wales, Australia, killing 13 people.", "html": "1989 - A <a href=\"https://wikipedia.org/wiki/1989_Newcastle_earthquake\" title=\"1989 Newcastle earthquake\">magnitude 5.6 earthquake</a> hits <a href=\"https://wikipedia.org/wiki/Newcastle,_New_South_Wales\" title=\"Newcastle, New South Wales\">Newcastle, New South Wales</a>, Australia, killing 13 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1989_Newcastle_earthquake\" title=\"1989 Newcastle earthquake\">magnitude 5.6 earthquake</a> hits <a href=\"https://wikipedia.org/wiki/Newcastle,_New_South_Wales\" title=\"Newcastle, New South Wales\">Newcastle, New South Wales</a>, Australia, killing 13 people.", "links": [{"title": "1989 Newcastle earthquake", "link": "https://wikipedia.org/wiki/1989_Newcastle_earthquake"}, {"title": "Newcastle, New South Wales", "link": "https://wikipedia.org/wiki/Newcastle,_New_South_Wales"}]}, {"year": "2006", "text": "War in Somalia: The militaries of Somalia's Transitional Federal Government and Ethiopian troops capture Mogadishu unopposed.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/War_in_Somalia_(2006%E2%80%9309)\" class=\"mw-redirect\" title=\"War in Somalia (2006-09)\">War in Somalia</a>: The militaries of <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>'s <a href=\"https://wikipedia.org/wiki/Transitional_Federal_Government_of_Somalia\" title=\"Transitional Federal Government of Somalia\">Transitional Federal Government</a> and <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> troops <a href=\"https://wikipedia.org/wiki/Fall_of_Mogadishu\" title=\"Fall of Mogadishu\">capture Mogadishu</a> unopposed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Somalia_(2006%E2%80%9309)\" class=\"mw-redirect\" title=\"War in Somalia (2006-09)\">War in Somalia</a>: The militaries of <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>'s <a href=\"https://wikipedia.org/wiki/Transitional_Federal_Government_of_Somalia\" title=\"Transitional Federal Government of Somalia\">Transitional Federal Government</a> and <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> troops <a href=\"https://wikipedia.org/wiki/Fall_of_Mogadishu\" title=\"Fall of Mogadishu\">capture Mogadishu</a> unopposed.", "links": [{"title": "War in Somalia (2006-09)", "link": "https://wikipedia.org/wiki/War_in_Somalia_(2006%E2%80%9309)"}, {"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}, {"title": "Transitional Federal Government of Somalia", "link": "https://wikipedia.org/wiki/Transitional_Federal_Government_of_Somalia"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Fall of Mogadishu", "link": "https://wikipedia.org/wiki/Fall_of_Mogadishu"}]}, {"year": "2009", "text": "Forty-three people die in a suicide bombing in Karachi, Pakistan, where Shia Muslims are observing the Day of Ashura.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/2009_Karachi_bombing\" title=\"2009 Karachi bombing\">Forty-three people die in a suicide bombing</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, Pakistan, where <a href=\"https://wikipedia.org/wiki/Shia_Islam\" title=\"Shia Islam\">Shia</a> Muslims are observing the <a href=\"https://wikipedia.org/wiki/Day_of_Ashura\" class=\"mw-redirect\" title=\"Day of Ashura\">Day of Ashura</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2009_Karachi_bombing\" title=\"2009 Karachi bombing\">Forty-three people die in a suicide bombing</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, Pakistan, where <a href=\"https://wikipedia.org/wiki/Shia_Islam\" title=\"Shia Islam\">Shia</a> Muslims are observing the <a href=\"https://wikipedia.org/wiki/Day_of_Ashura\" class=\"mw-redirect\" title=\"Day of Ashura\">Day of Ashura</a>.", "links": [{"title": "2009 Karachi bombing", "link": "https://wikipedia.org/wiki/2009_Karachi_bombing"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}, {"title": "Shia Islam", "link": "https://wikipedia.org/wiki/Shia_Islam"}, {"title": "Day of Ashura", "link": "https://wikipedia.org/wiki/Day_of_Ashura"}]}, {"year": "2014", "text": "Indonesia AirAsia Flight 8501 crashes into the Karimata Strait en route from Surabaya to Singapore, killing all 162 people aboard.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Indonesia_AirAsia_Flight_8501\" title=\"Indonesia AirAsia Flight 8501\">Indonesia AirAsia Flight 8501</a> crashes into the <a href=\"https://wikipedia.org/wiki/Karimata_Strait\" title=\"Karimata Strait\">Karimata Strait</a> en route from <a href=\"https://wikipedia.org/wiki/Juanda_International_Airport\" title=\"Juanda International Airport\">Surabaya</a> to <a href=\"https://wikipedia.org/wiki/Singapore_Changi_Airport\" class=\"mw-redirect\" title=\"Singapore Changi Airport\">Singapore</a>, killing all 162 people aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indonesia_AirAsia_Flight_8501\" title=\"Indonesia AirAsia Flight 8501\">Indonesia AirAsia Flight 8501</a> crashes into the <a href=\"https://wikipedia.org/wiki/Karimata_Strait\" title=\"Karimata Strait\">Karimata Strait</a> en route from <a href=\"https://wikipedia.org/wiki/Juanda_International_Airport\" title=\"Juanda International Airport\">Surabaya</a> to <a href=\"https://wikipedia.org/wiki/Singapore_Changi_Airport\" class=\"mw-redirect\" title=\"Singapore Changi Airport\">Singapore</a>, killing all 162 people aboard.", "links": [{"title": "Indonesia AirAsia Flight 8501", "link": "https://wikipedia.org/wiki/Indonesia_AirAsia_Flight_8501"}, {"title": "Karimata Strait", "link": "https://wikipedia.org/wiki/Karimata_Strait"}, {"title": "Juanda International Airport", "link": "https://wikipedia.org/wiki/Juanda_International_Airport"}, {"title": "Singapore Changi Airport", "link": "https://wikipedia.org/wiki/Singapore_Changi_Airport"}]}, {"year": "2014", "text": "Nine people die and another 19 are reported missing, when the MS Norman Atlantic catches fire in the Strait of Otranto, in the Adriatic Sea, in Italian waters.", "html": "2014 - Nine people die and another 19 are reported missing, when the <a href=\"https://wikipedia.org/wiki/MS_Norman_Atlantic\" title=\"MS Norman Atlantic\">MS <i>Norman Atlantic</i></a> catches fire in the <a href=\"https://wikipedia.org/wiki/Strait_of_Otranto\" title=\"Strait of Otranto\">Strait of Otranto</a>, in the <a href=\"https://wikipedia.org/wiki/Adriatic_Sea\" title=\"Adriatic Sea\">Adriatic Sea</a>, in Italian waters.", "no_year_html": "Nine people die and another 19 are reported missing, when the <a href=\"https://wikipedia.org/wiki/MS_Norman_Atlantic\" title=\"MS Norman Atlantic\">MS <i>Norman Atlantic</i></a> catches fire in the <a href=\"https://wikipedia.org/wiki/Strait_of_Otranto\" title=\"Strait of Otranto\">Strait of Otranto</a>, in the <a href=\"https://wikipedia.org/wiki/Adriatic_Sea\" title=\"Adriatic Sea\">Adriatic Sea</a>, in Italian waters.", "links": [{"title": "MS Norman Atlantic", "link": "https://wikipedia.org/wiki/MS_Norman_Atlantic"}, {"title": "Strait of Otranto", "link": "https://wikipedia.org/wiki/Strait_of_Otranto"}, {"title": "Adriatic Sea", "link": "https://wikipedia.org/wiki/Adriatic_Sea"}]}], "Births": [{"year": "1461", "text": "<PERSON> of Savoy, French nun (d. 1503)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy_(nun)\" title=\"<PERSON> of Savoy (nun)\"><PERSON> of Savoy</a>, French nun (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy_(nun)\" title=\"<PERSON> of Savoy (nun)\"><PERSON> of Savoy</a>, French nun (d. 1503)", "links": [{"title": "<PERSON> Savoy (nun)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy_(nun)"}]}, {"year": "1510", "text": "<PERSON>, English politician (d. 1579)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Keeper)\" title=\"<PERSON> (Lord Keeper)\"><PERSON></a>, English politician (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Keeper)\" title=\"<PERSON> (Lord Keeper)\"><PERSON></a>, English politician (d. 1579)", "links": [{"title": "<PERSON> (Lord Keeper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Keeper)"}]}, {"year": "1535", "text": "<PERSON>, German theologian (d. 1578)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1578)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON>, French author and scholar (d. 1688)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French author and scholar (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French author and scholar (d. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antoine_Fureti%C3%A8re"}]}, {"year": "1635", "text": "<PERSON>, second daughter of King <PERSON> of England (d. 1650)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(daughter_of_<PERSON>_<PERSON>)\" title=\"<PERSON> (daughter of <PERSON>)\"><PERSON></a>, second daughter of King <PERSON> of England (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(daughter_of_<PERSON>_<PERSON>)\" title=\"<PERSON> (daughter of <PERSON>)\"><PERSON></a>, second daughter of King <PERSON> of England (d. 1650)", "links": [{"title": "<PERSON> (daughter of <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(daughter_of_<PERSON>_<PERSON>)"}]}, {"year": "1651", "text": "<PERSON>, German organist and composer (d. 1735)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1655", "text": "<PERSON>, 3rd Baron <PERSON>, English politician, Lord Lieutenant of Suffolk (d. 1698)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Suffolk\" title=\"Lord Lieutenant of Suffolk\">Lord Lieutenant of Suffolk</a> (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Suffolk\" title=\"Lord Lieutenant of Suffolk\">Lord Lieutenant of Suffolk</a> (d. 1698)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>"}, {"title": "Lord Lieutenant of Suffolk", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Suffolk"}]}, {"year": "1665", "text": "<PERSON>, 1st Duke of Northumberland, English general and politician, Lord Lieutenant of Berkshire (d. 1716)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Northumberland\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Northumberland\"><PERSON>, 1st Duke of Northumberland</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Berkshire\" title=\"Lord Lieutenant of Berkshire\">Lord Lieutenant of Berkshire</a> (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Northumberland\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Northumberland\"><PERSON>, 1st Duke of Northumberland</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Berkshire\" title=\"Lord Lieutenant of Berkshire\">Lord Lieutenant of Berkshire</a> (d. 1716)", "links": [{"title": "<PERSON>, 1st Duke of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Northumberland"}, {"title": "Lord Lieutenant of Berkshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Berkshire"}]}, {"year": "1722", "text": "<PERSON>, Caribbean-American agriculturalist (d. 1793)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean-American agriculturalist (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean-American agriculturalist (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, Prince-Bishop of Bamberg (d. 1805)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prince-Bishop of Bamberg (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prince-Bishop of Bamberg (d. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, English-Canadian brewer, founded the Molson Brewery (d. 1836)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian brewer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>lson_Brewery\" title=\"Molson Brewery\">Molson Brewery</a> (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian brewer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>lson_Brewery\" title=\"Molson Brewery\">Molson Brewery</a> (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Molson Brewery", "link": "https://wikipedia.org/wiki/Molson_Brewery"}]}, {"year": "1775", "text": "<PERSON><PERSON><PERSON>, Swiss banker and photographer (d. 1863)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss banker and photographer (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss banker and photographer (d. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1789", "text": "<PERSON><PERSON>, American novelist of \"domestic fiction\" (d. 1867)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>wick\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist of \"domestic fiction\" (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>dgwick\"><PERSON><PERSON></a>, American novelist of \"domestic fiction\" (d. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>wick"}]}, {"year": "1798", "text": "<PERSON>, Scottish astronomer and mathematician (d. 1844)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, Scottish astronomer and mathematician (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, Scottish astronomer and mathematician (d. 1844)", "links": [{"title": "<PERSON> (astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)"}]}, {"year": "1818", "text": "<PERSON>, German chemist and academic (d. 1897)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON>, Canadian-American lieutenant and composer (d. 1891)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/Calixa_Lavall%C3%A9e\" title=\"Calixa Lavallée\"><PERSON><PERSON><PERSON></a>, Canadian-American lieutenant and composer (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Calixa_Lavall%C3%A9e\" title=\"Calixa Lavallée\"><PERSON><PERSON><PERSON></a>, Canadian-American lieutenant and composer (d. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Calixa_Lavall%C3%A9e"}]}, {"year": "1856", "text": "<PERSON>, American historian and politician, 28th President of the United States, Nobel Prize laureate (d. 1924)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician, 28th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician, 28th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1865", "text": "<PERSON>, Swiss/French painter (d. 1925)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON>ton\" title=\"<PERSON>\"><PERSON></a>, Swiss/French painter (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON>ton\" title=\"<PERSON>\"><PERSON></a>, Swiss/French painter (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Vallotton"}]}, {"year": "1870", "text": "<PERSON>, English runner (d. 1949)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English runner (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English runner (d. 1949)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1882", "text": "<PERSON>, English astronomer, physicist, and mathematician (d. 1944)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer, physicist, and mathematician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer, physicist, and mathematician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dington"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Danish model and painter (d. 1931)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish model and painter (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish model and painter (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German physicist and academic (d. 1946)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON>er\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%B6rster"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, German-American director, producer, and screenwriter (d. 1931)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/F<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, German-American director, producer, and screenwriter (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F._<PERSON><PERSON>_<PERSON>\" title=\"F<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, German-American director, producer, and screenwriter (d. 1931)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>rna<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American political scientist, historian, and academic (d. 1970)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, historian, and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wright\"><PERSON></a>, American political scientist, historian, and academic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wright"}]}, {"year": "1895", "text": "<PERSON>, American author and playwright (d. 1981)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>k"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish-American meteorologist and academic (d. 1957)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish-American meteorologist and academic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish-American meteorologist and academic (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese admiral (d. 1947)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American baseball player (d. 1986)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American philosopher and author (d. 2001)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Chinese author and educator (d. 1988)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and educator (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and educator (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American pianist and bandleader (d. 1983)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and bandleader (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and bandleader (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Hungarian-American mathematician and physicist (d. 1957)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and physicist (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and physicist (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ukrainian-Israeli linguist and academic (d. 2013)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Ze%27ev_<PERSON>\" title=\"Ze'ev <PERSON>\"><PERSON><PERSON>'ev <PERSON></a>, Ukrainian-Israeli linguist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ze%27<PERSON>_<PERSON>\" title=\"Ze'ev <PERSON>\"><PERSON><PERSON>'ev <PERSON></a>, Ukrainian-Israeli linguist and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ze%27ev_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American actor (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lew_<PERSON>yres"}]}, {"year": "1910", "text": "<PERSON>, American singer (d. 1972)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 1972)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Dutch sprinter and journalist (d. 2003)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sprinter and journalist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sprinter and journalist (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian-American actor (d. 2009)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Russian author and educator (d. 1974)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Bidia_Dandaron\" title=\"B<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and educator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bidia_Dan<PERSON>\" title=\"Bid<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and educator (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bidia_Dandaron"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Pops_Staples\" title=\"Pops Staples\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pops_Staples\" title=\"Pops Staples\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2000)", "links": [{"title": "Pops Staples", "link": "https://wikipedia.org/wiki/Pops_Staples"}]}, {"year": "1917", "text": "<PERSON>, Trinidadian politician, 1st President of Trinidad and Tobago (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Trinidad_and_Tobago#President_of_Trinidad_and_Tobago\" title=\"List of heads of state of Trinidad and Tobago\">President of Trinidad and Tobago</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Trinidad_and_Tobago#President_of_Trinidad_and_Tobago\" title=\"List of heads of state of Trinidad and Tobago\">President of Trinidad and Tobago</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of heads of state of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Trinidad_and_Tobago#President_of_Trinidad_and_Tobago"}]}, {"year": "1919", "text": "<PERSON>, American author (d. 1997)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, South African cricketer (d. 1952)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (d. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American architect, designed the Knoxville City-County Building (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Knoxville_City-County_Building\" title=\"Knoxville City-County Building\">Knoxville City-County Building</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Knoxville_City-County_Building\" title=\"Knoxville City-County Building\">Knoxville City-County Building</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Knoxville City-County Building", "link": "https://wikipedia.org/wiki/Knoxville_City-County_Building"}]}, {"year": "1920", "text": "<PERSON>, Honduran-American football player (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON>-American football player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON>-American football player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American football player and coach (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Al_W<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Wistert\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2016)", "links": [{"title": "Al Wistert", "link": "https://wikipedia.org/wiki/Al_Wistert"}]}, {"year": "1921", "text": "<PERSON>, American singer-songwriter and producer (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Otis\"><PERSON></a>, American singer-songwriter and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Australian politician, 6th Deputy Prime Minister of Australia (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia"}]}, {"year": "1922", "text": "<PERSON>, American publisher, producer, and actor (d. 2018)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, producer, and actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, producer, and actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Ethiopian politician; President of Ethiopia (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>old<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian politician; <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Ethiopia\" class=\"mw-redirect\" title=\"List of Presidents of Ethiopia\">President of Ethiopia</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian politician; <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Ethiopia\" class=\"mw-redirect\" title=\"List of Presidents of Ethiopia\">President of Ethiopia</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gir<PERSON>_<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"title": "List of Presidents of Ethiopia", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Ethiopia"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, German actress and singer (d. 2002)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nef\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actress and singer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nef\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actress and singer (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nef"}]}, {"year": "1925", "text": "<PERSON>, Ugandan engineer and politician, 2nd President of Uganda (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan engineer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Uganda\" title=\"President of Uganda\">President of Uganda</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan engineer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Uganda\" title=\"President of Uganda\">President of Uganda</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Uganda", "link": "https://wikipedia.org/wiki/President_of_Uganda"}]}, {"year": "1926", "text": "<PERSON>, German-English cricketer and referee  (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English cricketer and referee (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English cricketer and referee (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian flute player, saxophonist, and composer (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian flute player, saxophonist, and composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian flute player, saxophonist, and composer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian politician (d. 2025)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English journalist and author (d. 1994)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian-American ice hockey player (d. 1970)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Dutch astronomer (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch astronomer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch astronomer (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Egyptian illustrator and academic (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Maria<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Egyptian illustrator and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Egyptian illustrator and academic (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, French theorist and author (d. 1994)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theorist and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theorist and author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian businessman, founded Reliance Industries (d. 2002)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Dhirubhai_Ambani\" title=\"Dhirubhai Ambani\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman, founded <a href=\"https://wikipedia.org/wiki/Reliance_Industries\" title=\"Reliance Industries\">Reliance Industries</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dhirub<PERSON>_Ambani\" title=\"Dhirub<PERSON> Ambani\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman, founded <a href=\"https://wikipedia.org/wiki/Reliance_Industries\" title=\"Reliance Industries\">Reliance Industries</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>bani"}, {"title": "Reliance Industries", "link": "https://wikipedia.org/wiki/Reliance_Industries"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American singer-songwriter (d. 1979)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Dorsey_Burnette\" title=\"Dorsey Burnette\"><PERSON><PERSON></a>, American singer-songwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dorsey_Burnette\" title=\"Dorsey Burnette\"><PERSON><PERSON></a>, American singer-songwriter (d. 1979)", "links": [{"title": "Dorsey Burnette", "link": "https://wikipedia.org/wiki/Dorsey_Burnette"}]}, {"year": "1932", "text": "<PERSON>, English journalist and politician, Shadow Home Secretary", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Home_Secretary\" title=\"Shadow Home Secretary\">Shadow Home Secretary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Home_Secretary\" title=\"Shadow Home Secretary\">Shadow Home Secretary</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Shadow Home Secretary", "link": "https://wikipedia.org/wiki/Shadow_Home_Secretary"}]}, {"year": "1932", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2019)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, American actress (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Argentine author and playwright (d. 1990)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine author and playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine author and playwright (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American soldier, lawyer, and politician, 55th Governor of Kentucky (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American soldier, lawyer, and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American soldier, lawyer, and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (d. 2022)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Governor of Kentucky", "link": "https://wikipedia.org/wiki/Governor_of_Kentucky"}]}, {"year": "1934", "text": "<PERSON><PERSON>, German footballer and manager (d. 2000)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Rudi_Fa%C3%9Fnacht\" title=\"Rudi Faßnacht\"><PERSON><PERSON></a>, German footballer and manager (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rudi_Fa%C3%9Fnacht\" title=\"Rudi Faßnacht\"><PERSON><PERSON></a>, German footballer and manager (d. 2000)", "links": [{"title": "R<PERSON>", "link": "https://wikipedia.org/wiki/Rudi_Fa%C3%9Fnacht"}]}, {"year": "1934", "text": "<PERSON>, English actress (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English-Australian director, producer, and screenwriter (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian director, producer, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian director, producer, and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American journalist, director, and producer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Indian businessman and philanthropist (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and philanthropist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and philanthropist (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American trumpet player, scholar, and critic (d. 2008)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, scholar, and critic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, scholar, and critic (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American businessman, founded Anschutz Entertainment Group", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Anschutz_Entertainment_Group\" title=\"Anschutz Entertainment Group\">Anschutz Entertainment Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Anschutz_Entertainment_Group\" title=\"Anschutz Entertainment Group\">Anschutz Entertainment Group</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Anschutz Entertainment Group", "link": "https://wikipedia.org/wiki/Anschutz_Entertainment_Group"}]}, {"year": "1939", "text": "<PERSON>, Scottish footballer and manager", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American journalist and illustrator (d. 2006)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and illustrator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and illustrator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, Indian Minister of Defence", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a>", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Defence (India)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(India)"}]}, {"year": "1940", "text": "<PERSON>, Chilean-American journalist and talk show host", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_host)\" title=\"<PERSON> (television host)\"><PERSON></a>, Chilean-American journalist and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_host)\" title=\"<PERSON> (television host)\"><PERSON></a>, Chilean-American journalist and talk show host", "links": [{"title": "<PERSON> (television host)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_host)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani cricketer and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Intikhab_Alam\" title=\"Intikhab Alam\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Intikhab_Alam\" title=\"Intikhab Alam\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Intikhab_Alam"}]}, {"year": "1942", "text": "<PERSON>, Belgian cyclist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Peruvian cardinal", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian lawyer and politician, 20th Premier of Ontario", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Ontario", "link": "https://wikipedia.org/wiki/Premier_of_Ontario"}]}, {"year": "1943", "text": "<PERSON>, Welsh politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American astronomer and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American sergeant and politician (d. 2021)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American biochemist and academic, Nobel Prize laureate (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1944", "text": "<PERSON>, English footballer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, King of Nepal (d. 2001)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON></a>, King of Nepal (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON></a>, King of Nepal (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON> of Nepal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ndra_of_Nepal"}]}, {"year": "1945", "text": "<PERSON>, English journalist, historian, and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, historian, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, historian, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American lawyer and politician, 45th Governor of Arkansas", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1946", "text": "<PERSON>, Canadian director, screenwriter, and activist (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, screenwriter, and activist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, screenwriter, and activist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American golfer (d. 2018)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Green\"><PERSON></a>, American golfer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American lawyer and politician (d. 2024)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Dakota_politician)\" title=\"<PERSON> (South Dakota politician)\"><PERSON></a>, American lawyer and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Dakota_politician)\" title=\"<PERSON> (South Dakota politician)\"><PERSON></a>, American lawyer and politician (d. 2024)", "links": [{"title": "<PERSON> (South Dakota politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_Dakota_politician)"}]}, {"year": "1946", "text": "<PERSON>, Lady Judge, American-English lawyer and businesswoman (d. 2020)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Lady_Judge\" class=\"mw-redirect\" title=\"<PERSON>, Lady Judge\"><PERSON>, Lady Judge</a>, American-English lawyer and businesswoman (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lady_Judge\" class=\"mw-redirect\" title=\"<PERSON>, Lady Judge\"><PERSON>, Lady Judge</a>, American-English lawyer and businesswoman (d. 2020)", "links": [{"title": "<PERSON>, Lady Judge", "link": "https://wikipedia.org/wiki/<PERSON>,_Lady_Judge"}]}, {"year": "1946", "text": "<PERSON>, American baseball player and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left-handed_pitcher)\" title=\"<PERSON> (left-handed pitcher)\"><PERSON></a>, American baseball player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left-handed_pitcher)\" title=\"<PERSON> (left-handed pitcher)\"><PERSON></a>, American baseball player and author", "links": [{"title": "<PERSON> (left-handed pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(left-handed_pitcher)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Panamanian jockey", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Laffit_<PERSON><PERSON>_Jr.\" title=\"Laffit <PERSON><PERSON> Jr.\">Laffit <PERSON><PERSON> Jr.</a>, Panamanian jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Laffit_<PERSON><PERSON>_Jr.\" title=\"Laffit Pincay Jr.\">Laffit <PERSON>nc<PERSON> Jr.</a>, Panamanian jockey", "links": [{"title": "La<PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/Laffit_<PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter, keyboard player, and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Dutch-Australian rock bass player (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Australian rock bass player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Australian rock bass player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Mexican baseball player, coach, and manager (d. 2000)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Aurelio_Rodr%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican baseball player, coach, and manager (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurelio_Rodr%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican baseball player, coach, and manager (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurelio_Rodr%C3%ADguez"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Ziggy_Modeliste\" title=\"Ziggy Modeliste\"><PERSON><PERSON><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ziggy_Modeliste\" title=\"Ziggy Modeliste\"><PERSON><PERSON><PERSON></a>, American drummer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ziggy_Modeliste"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2010)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English mathematician and cryptographer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and cryptographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and cryptographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, German-American painter and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American painter and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American painter and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Indian lawyer and politician, 9th Indian Minister of Law and Justice (d. 2019)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Ministry_of_Law_and_Justice_(India)\" title=\"Ministry of Law and Justice (India)\">Indian Minister of Law and Justice</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Ministry_of_Law_and_Justice_(India)\" title=\"Ministry of Law and Justice (India)\">Indian Minister of Law and Justice</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Law and Justice (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Law_and_Justice_(India)"}]}, {"year": "1952", "text": "<PERSON>, Scottish educator and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, French pianist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler and promoter, founded Dradition wrestling promotion", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler and promoter, founded <a href=\"https://wikipedia.org/wiki/Dradition\" title=\"Dradi<PERSON>\">Dradition wrestling promotion</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler and promoter, founded <a href=\"https://wikipedia.org/wiki/Dradition\" title=\"Dradition\">Dradition wrestling promotion</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "Dradition", "link": "https://wikipedia.org/wiki/Dradition"}]}, {"year": "1953", "text": "<PERSON>, American journalist and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American serial killer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American television journalist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> King\"><PERSON><PERSON></a>, American television journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> King\"><PERSON><PERSON></a>, American television journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American actor, director, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Denzel_Washington\" title=\"Denzel Washington\"><PERSON><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denzel_Washington\" title=\"Den<PERSON> Washington\"><PERSON><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>zel_Washington"}]}, {"year": "1955", "text": "<PERSON>, English comedian, actor, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Chinese author, academic, and activist, Nobel Prize laureate (d. 2017)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Xiaobo\"><PERSON></a>, Chinese author, academic, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Liu Xiaobo\"><PERSON></a>, Chinese author, academic, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Liu_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1956", "text": "<PERSON>, English violinist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American golfer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Serbian volleyball trainer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>oran_Gaji%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian volleyball trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aji%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian volleyball trainer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zoran_Gaji%C4%87"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON>, German runner and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Hansj%C3%B6rg_<PERSON>nz<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German runner and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hansj%C3%B6rg_<PERSON>nz<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German runner and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hansj%C3%B6rg_<PERSON><PERSON>e"}]}, {"year": "1959", "text": "<PERSON>, American composer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_L%C3%A9o_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Spanish singer-songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tor<PERSON>ja\"><PERSON></a>, Spanish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ja"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_Bo<PERSON>que"}]}, {"year": "1960", "text": "<PERSON>, Australian tennis player, coach, and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player, coach, and sportscaster", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1960", "text": "<PERSON>, American actor and race car driver (d. 2024)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and race car driver (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and race car driver (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American basketball player (d. 2010)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Danish footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Kent_Nielsen\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kent_Nielsen\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kent_Nielsen"}]}, {"year": "1962", "text": "<PERSON>, French jazz pianist (d. 1999)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French jazz pianist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French jazz pianist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Perkins\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Perkins\" title=\"<PERSON> Perkins\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON> Perkins", "link": "https://wikipedia.org/wiki/<PERSON>_Perkins"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Spanish runner", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Z%C3%BA%C3%B1iga\" title=\"<PERSON><PERSON> Zúñiga\"><PERSON><PERSON></a>, Spanish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Z%C3%BA%C3%B1iga\" title=\"<PERSON>te Zúñiga\"><PERSON><PERSON></a>, Spanish runner", "links": [{"title": "Maite <PERSON>", "link": "https://wikipedia.org/wiki/Maite_Z%C3%BA%C3%B1iga"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Estonian skier", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Allar_Levandi\" title=\"Allar Levandi\"><PERSON><PERSON></a>, Estonian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Allar_Levandi\" title=\"Allar Levandi\"><PERSON><PERSON></a>, Estonian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Allar_Levandi"}]}, {"year": "1967", "text": "<PERSON>, American illustrator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Japanese engineer and astronaut", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Finnish-American computer programmer, developed Linux kernel", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-American computer programmer, developed <a href=\"https://wikipedia.org/wiki/Linux_kernel\" title=\"Linux kernel\">Linux kernel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ds\"><PERSON><PERSON></a>, Finnish-American computer programmer, developed <a href=\"https://wikipedia.org/wiki/Linux_kernel\" title=\"Linux kernel\">Linux kernel</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Linux kernel", "link": "https://wikipedia.org/wiki/Linux_kernel"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American sprinter and football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Dutch tennis player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Spanish footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Ser<PERSON>_<PERSON>ju%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ser<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sergi_Barju%C3%A1n"}]}, {"year": "1971", "text": "<PERSON>, Dutch singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1972", "text": "<PERSON>, Peruvian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian-Bermudian tennis player and model", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian-Bermudian tennis player and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian-Bermudian tennis player and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, German sprinter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German sprinter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor, producer, screenwriter, and talk show host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, screenwriter, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, screenwriter, and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Dutch speed skater", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Ids_Postma\" title=\"Ids Postma\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ids_Postma\" title=\"Ids Postma\"><PERSON><PERSON> Post<PERSON></a>, Dutch speed skater", "links": [{"title": "Ids Postma", "link": "https://wikipedia.org/wiki/Ids_Postma"}]}, {"year": "1974", "text": "<PERSON>, American singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ni<PERSON>rmayer"}]}, {"year": "1974", "text": "<PERSON>, German footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Norwegian race walker", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Trond_Nymark\" title=\"Trond Nymark\">Tron<PERSON> Nymark</a>, Norwegian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trond_Nymark\" title=\"Trond Nymark\">Tron<PERSON> Nymark</a>, Norwegian race walker", "links": [{"title": "Trond Nymark", "link": "https://wikipedia.org/wiki/Trond_Nymark"}]}, {"year": "1976", "text": "<PERSON>, Australian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Croatian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDikovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDikovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Igor_%C5%BDikovi%C4%87"}]}, {"year": "1977", "text": "<PERSON>, American sprinter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby league player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Nigerian sprinter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter, pianist, and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/John_Legend\" title=\"John Legend\"><PERSON></a>, American singer-songwriter, pianist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Legend\" title=\"John Legend\"><PERSON></a>, American singer-songwriter, pianist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_Legend"}]}, {"year": "1979", "text": "<PERSON>, American tennis player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, German singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(utility_player)\" title=\"<PERSON> (utility player)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(utility_player)\" title=\"<PERSON> (utility player)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (utility player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(utility_player)"}]}, {"year": "1979", "text": "<PERSON>, American musician and artist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hill\"><PERSON></a>, American musician and artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Holland\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Holland\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Holland"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Swedish actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ace"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Congolese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_LuaLua\" title=\"Lomana LuaLua\"><PERSON><PERSON> Lua<PERSON>ua</a>, Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_LuaLua\" title=\"Lomana LuaLua\"><PERSON><PERSON> LuaLua</a>, Congolese footballer", "links": [{"title": "Lomana LuaLua", "link": "https://wikipedia.org/wiki/<PERSON>mana_LuaLua"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Belarusian race walker", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian race walker", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yt<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Dutch footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American journalist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American-British actress and fashion designer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British actress and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British actress and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(ice_hockey)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, South Korean singer and dancer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Narsha\" title=\"Narsha\"><PERSON><PERSON><PERSON></a>, South Korean singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narsha\" title=\"Narsha\"><PERSON><PERSON><PERSON></a>, South Korean singer and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narsha"}]}, {"year": "1981", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Finnish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Mi<PERSON>_V%C3%A4yrynen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mi<PERSON>_V%C3%A4yrynen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mika_V%C3%A4yrynen"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American football player (d. 2019)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress and model", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Belgian decathlete", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Gourmet\" title=\"<PERSON>\"><PERSON></a>, Belgian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Gourmet\" title=\"<PERSON>\"><PERSON></a>, Belgian decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Gourmet"}]}, {"year": "1982", "text": "<PERSON>, Canadian hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Curtis Glencross\"><PERSON></a>, Canadian hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German golfer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor and musician", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Barnes"}]}, {"year": "1989", "text": "<PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Ethiopian runner", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Ayele_Abshero\" title=\"Ayele Abshero\"><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aye<PERSON>_Abshero\" title=\"Ayele Abshero\"><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Abshero"}]}, {"year": "1990", "text": "<PERSON>, Spanish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1990)\" title=\"<PERSON> (footballer, born 1990)\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1990)\" title=\"<PERSON> (footballer, born 1990)\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON> (footballer, born 1990)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1990)"}]}, {"year": "1990", "text": "<PERSON>, American singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch swimmer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Jur%C4%8Do\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Jur%C4%8Do\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Jur%C4%8Do"}]}, {"year": "1994", "text": "<PERSON>, English swimmer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Nahitan_N%C3%A1ndez\" title=\"Nahitan Nández\">Nahitan <PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nahitan_N%C3%A1ndez\" title=\"Nahitan Nández\">Nahitan <PERSON></a>, Uruguayan footballer", "links": [{"title": "Nahitan <PERSON>", "link": "https://wikipedia.org/wiki/Nahitan_N%C3%A1ndez"}]}, {"year": "1996", "text": "<PERSON><PERSON>, French footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Tanguy_Ndombele\" title=\"Tanguy Ndombele\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tang<PERSON>_Ndombele\" title=\"Tanguy Ndombele\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tanguy_Ndombele"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Canadian actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, British-Irish footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, British-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, British-Irish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}], "Deaths": [{"year": "925", "text": "<PERSON>, general of the Chinese state of Former Shu", "html": "925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">Chinese</a> state of <a href=\"https://wikipedia.org/wiki/Former_Shu\" title=\"Former Shu\">Former Shu</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">Chinese</a> state of <a href=\"https://wikipedia.org/wiki/Former_Shu\" title=\"Former Shu\">Former <PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "History of China", "link": "https://wikipedia.org/wiki/History_of_China"}, {"title": "Former <PERSON>", "link": "https://wikipedia.org/wiki/Former_Shu"}]}, {"year": "1218", "text": "<PERSON>, Count of Dreux (b. 1154)", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Dreux\" title=\"<PERSON>, Count of Dreux\"><PERSON>, Count of Dreux</a> (b. 1154)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Dreux\" title=\"<PERSON>, Count of Dreux\"><PERSON>, Count of Dreux</a> (b. 1154)", "links": [{"title": "<PERSON>, Count of Dreux", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1297", "text": "<PERSON>, French cardinal (b. 1230)", "html": "1297 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (b. 1230)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (b. 1230)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1326", "text": "Sir <PERSON>, Earl of Atholl, Constable of Scotland, and Chief Warden of Northumberland", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Atholl\" title=\"<PERSON>, Earl of Atholl\">Sir <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Earl_<PERSON>_Atholl\" title=\"Earl of Atholl\">Earl of Atholl</a>, <a href=\"https://wikipedia.org/wiki/Constable_of_Scotland\" class=\"mw-redirect\" title=\"Constable of Scotland\">Constable of Scotland</a>, and Chief Warden of <a href=\"https://wikipedia.org/wiki/Northumberland\" title=\"Northumberland\">Northumberland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_<PERSON>_Atholl\" title=\"<PERSON>, Earl of Atholl\">Sir <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Earl_<PERSON>_Athol<PERSON>\" title=\"Earl of Atholl\">Earl of Atholl</a>, <a href=\"https://wikipedia.org/wiki/Constable_of_Scotland\" class=\"mw-redirect\" title=\"Constable of Scotland\">Constable of Scotland</a>, and Chief Warden of <a href=\"https://wikipedia.org/wiki/Northumberland\" title=\"Northumberland\">Northumberland</a>", "links": [{"title": "<PERSON>, Earl of Atholl", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Earl_of_Atholl"}, {"title": "Earl of Atholl", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Constable of Scotland", "link": "https://wikipedia.org/wiki/Constable_of_Scotland"}, {"title": "Northumberland", "link": "https://wikipedia.org/wiki/Northumberland"}]}, {"year": "1367", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1330)", "html": "1367 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ash<PERSON><PERSON> Yoshiakira\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1330)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Yoshiakira\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1330)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>ra"}]}, {"year": "1394", "text": "<PERSON>, queen of Epirus (b. 1350)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>ologina\" title=\"<PERSON>olo<PERSON>\"><PERSON></a>, queen of Epirus (b. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>ologin<PERSON>\" title=\"<PERSON>olo<PERSON>\"><PERSON></a>, queen of Epirus (b. 1350)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "1446", "text": "<PERSON><PERSON><PERSON> (b. 1369)", "html": "1446 - <a href=\"https://wikipedia.org/wiki/Antipope_Clement_VIII\" title=\"Antipope Clement VIII\">Antipope Clement VIII</a> (b. 1369)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_Clement_VIII\" title=\"Antipope Clement VIII\">Antipope Clement VIII</a> (b. 1369)", "links": [{"title": "Antipop<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1491", "text": "<PERSON><PERSON><PERSON>, Italian sculptor (b. c. 1435)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian sculptor (b. c. 1435)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian sculptor (b. c. 1435)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1503", "text": "<PERSON><PERSON> the Unfortunate, Italian ruler (b. 1471)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/Pier<PERSON>_the_Unfortunate\" title=\"<PERSON><PERSON> the Unfortunate\"><PERSON><PERSON> the Unfortunate</a>, Italian ruler (b. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pier<PERSON>_the_Unfortunate\" title=\"<PERSON><PERSON> the Unfortunate\"><PERSON><PERSON> the Unfortunate</a>, Italian ruler (b. 1471)", "links": [{"title": "<PERSON><PERSON> the Unfortunate", "link": "https://wikipedia.org/wiki/Piero_the_Unfortunate"}]}, {"year": "1538", "text": "<PERSON>, <PERSON><PERSON> of Venice (b. 1455)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> of Venice (b. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> of Venice (b. 1455)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1547", "text": "<PERSON>, German humanist and antiquarian (b. 1465)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and antiquarian (b. 1465)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and antiquarian (b. 1465)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1558", "text": "<PERSON>, German organist and composer (b. 1527)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1527)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1622", "text": "<PERSON>, French bishop and saint (b. 1567)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop and saint (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop and saint (b. 1567)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1663", "text": "<PERSON>, Italian mathematician and physicist (b. 1618)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and physicist (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and physicist (b. 1618)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1671", "text": "<PERSON>, German scholar and critic (b. 1611)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and critic (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and critic (b. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON> of England (b. 1662)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\"><PERSON> of England</a> (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\"><PERSON> of England</a> (b. 1662)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Mary_<PERSON>_of_England"}]}, {"year": "1706", "text": "<PERSON>, French philosopher and author (b. 1647)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1647)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1708", "text": "<PERSON>, French botanist and mycologist (b. 1656)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and mycologist (b. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and mycologist (b. 1656)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, Scottish minister and academic (b. 1649)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and academic (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and academic (b. 1649)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1734", "text": "<PERSON>, Scottish outlaw (b. 1671)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish outlaw (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish outlaw (b. 1671)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON>, Italian composer (b. 1670)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, Polish-Estonian physician and journalist (b. 1732)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Estonian physician and journalist (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Estonian physician and journalist (b. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON><PERSON><PERSON>, Ecuadorian physician and lawyer (b. 1747)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian physician and lawyer (b. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian physician and lawyer (b. 1747)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, 1st Baron <PERSON>, English historian and politician, Secretary at War (b. 1800)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Secretary_at_War\" title=\"Secretary at War\">Secretary at War</a> (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Secretary_at_War\" title=\"Secretary at War\">Secretary at War</a> (b. 1800)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Secretary at War", "link": "https://wikipedia.org/wiki/Secretary_at_War"}]}, {"year": "1872", "text": "<PERSON>, American lawyer and politician, 7th Mayor of San Francisco (b. 1808)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of San Francisco", "link": "https://wikipedia.org/wiki/Mayor_of_San_Francisco"}]}, {"year": "1890", "text": "<PERSON>, American painter (b. 1861)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>unker\" title=\"<PERSON> Bunker\"><PERSON></a>, American painter (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>unker\" title=\"<PERSON> Bunker\"><PERSON></a>, American painter (b. 1861)", "links": [{"title": "<PERSON> Bunker", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>unker"}]}, {"year": "1897", "text": "<PERSON>, American priest and academic (b. 1833)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and academic (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and academic (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Portuguese soldier and explorer (b. 1846)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese soldier and explorer (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese soldier and explorer (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Swedish playwright (b. 1812)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish playwright (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish playwright (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Turkish journalist and translator (b. 1844)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Ah<PERSON>_<PERSON>_<PERSON>fendi\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Mithat Efendi\"><PERSON><PERSON></a>, Turkish journalist and translator (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ah<PERSON>_<PERSON>_<PERSON>di\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Mithat Efendi\"><PERSON><PERSON></a>, Turkish journalist and translator (b. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Austrian violinist and composer (b. 1835)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Canadian captain and pilot (b. 1892)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and pilot (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and pilot (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Brazilian poet and journalist (b. 1865)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Olavo_Bilac\" title=\"Olavo Bilac\"><PERSON><PERSON><PERSON></a>, Brazilian poet and journalist (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olavo_Bilac\" title=\"Olavo Bilac\"><PERSON><PERSON><PERSON></a>, Brazilian poet and journalist (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Olavo_Bilac"}]}, {"year": "1919", "text": "<PERSON>, Swedish physicist and academic (b. 1854)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and academic (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and academic (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Russian painter and costume designer (b. 1866)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and costume designer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and costume designer (b. 1866)", "links": [{"title": "Léon Bakst", "link": "https://wikipedia.org/wiki/L%C3%A9on_Bakst"}]}, {"year": "1932", "text": "<PERSON>, Australian cricketer (b. 1854)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American author and illustrator (b. 1874)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clarence Day\"><PERSON></a>, American author and illustrator (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Day\" title=\"Clarence Day\"><PERSON></a>, American author and illustrator (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, French pianist and composer (b. 1875)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian actress (b. 1886)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Lawrence"}]}, {"year": "1942", "text": "<PERSON>, German gymnast (b. 1869)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American baseball player (b. 1885)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1885)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1945", "text": "<PERSON>, American novelist and journalist (b. 1871)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and journalist (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and journalist (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Polish-American sculptor (b. 1882)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American sculptor (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American sculptor (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON> of Italy (b. 1869)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> (b. 1869)", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy"}]}, {"year": "1949", "text": "<PERSON>, New Zealand runner and soldier (b. 1910)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and soldier (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and soldier (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Croatian fascist dictator during World War II (b. 1889)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian fascist dictator during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian fascist dictator during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a> (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ante_Paveli%C4%87"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1960", "text": "<PERSON>, Canadian physician, academic, and diplomat (b. 1895)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician, academic, and diplomat (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician, academic, and diplomat (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, German violist, composer, and conductor (b. 1895)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violist, composer, and conductor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violist, composer, and conductor (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American biologist and philanthropist (b. 1875)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist and philanthropist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist and philanthropist (b. 1875)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, 12th Earl of Airlie, Scottish peer, soldier and courtier (b. 1893)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Airlie\" title=\"<PERSON>, 12th Earl of Airlie\"><PERSON>, 12th Earl of Airlie</a>, Scottish peer, soldier and courtier (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Airlie\" title=\"<PERSON>, 12th Earl of Airlie\"><PERSON>, 12th Earl of Airlie</a>, Scottish peer, soldier and courtier (b. 1893)", "links": [{"title": "<PERSON>, 12th Earl of Airlie", "link": "https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Air<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Austrian-American pianist, composer, and conductor (b. 1888)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American pianist, composer, and conductor (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American pianist, composer, and conductor (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American politician (b. 1903)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian-American director, producer, and screenwriter (b. 1885)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director, producer, and screenwriter (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director, producer, and screenwriter (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American drummer, songwriter, and producer (b. 1944)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American director, producer, and screenwriter (b. 1925)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, <PERSON> of Alvechurch, British politician and educator (b. 1903)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Alvechurch\" title=\"<PERSON>, Baroness <PERSON> of Alvechurch\"><PERSON>, Baroness <PERSON> of Alvechurch</a>, British politician and educator (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Alvechurch\" title=\"<PERSON>, Baroness <PERSON> of Alvechurch\"><PERSON>, Baroness <PERSON> of Alvechurch</a>, British politician and educator (b. 1903)", "links": [{"title": "<PERSON>, <PERSON> of Alvechurch", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Alvechurch"}]}, {"year": "1986", "text": "<PERSON>, American colonel and author (b. 1916)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Dutch painter (b. 1922)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Romanian-German physicist and engineer (b. 1894)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-German physicist and engineer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-German physicist and engineer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American screenwriter and producer (b. 1946)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American baseball player and coach (b. 1917)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sal_<PERSON>e"}]}, {"year": "1993", "text": "<PERSON>, American journalist and historian (b. 1904)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Canadian businessman and philanthropist (b. 1911)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9vesque\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businessman and philanthropist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9vesque\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businessman and philanthropist (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9vesque"}]}, {"year": "1999", "text": "<PERSON>, American actor (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American lieutenant, biologist, and engineer (b. 1919)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, biologist, and engineer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, biologist, and engineer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American priest and author (b. 1928)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American admiral (b. 1935)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>cker\" title=\"<PERSON> Hacker\"><PERSON></a>, American admiral (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>cker\" title=\"<PERSON> Hacker\"><PERSON></a>, American admiral (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>cker"}]}, {"year": "2004", "text": "<PERSON>, American actor and singer (b. 1935)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American novelist, essayist, critic, and playwright (b. 1933)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, critic, and playwright (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, critic, and playwright (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Iranian politician, Iranian Minister of Justice (b. 1956)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(Iran)\" title=\"Ministry of Justice (Iran)\">Iranian Minister of Justice</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(Iran)\" title=\"Ministry of Justice (Iran)\">Iranian Minister of Justice</a> (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Justice (Iran)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice_(Iran)"}]}, {"year": "2008", "text": "<PERSON>, Polish-American painter and illustrator (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter and illustrator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter and illustrator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American musician, composer and songwriter. Known by his stage name <PERSON> Rev (b. 1981)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/The_Rev\" title=\"The Rev\"><PERSON></a>, American musician, composer and songwriter. Known by his stage name <a href=\"https://wikipedia.org/wiki/The_Rev\" title=\"The Rev\">The Rev</a> (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Rev\" title=\"The Rev\"><PERSON></a>, American musician, composer and songwriter. Known by his stage name <a href=\"https://wikipedia.org/wiki/The_Rev\" title=\"The Rev\">The Rev</a> (b. 1981)", "links": [{"title": "The Rev", "link": "https://wikipedia.org/wiki/The_Rev"}, {"title": "The Rev", "link": "https://wikipedia.org/wiki/The_Rev"}]}, {"year": "2010", "text": "<PERSON>, American pianist and composer (b. 1921)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American serial killer (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Greek-English seismologist and engineer (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-English seismologist and engineer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-English seismologist and engineer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American computer scientist and academic, designed the IMAP (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic, designed the <a href=\"https://wikipedia.org/wiki/IMAP\" class=\"mw-redirect\" title=\"IMAP\">IMAP</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic, designed the <a href=\"https://wikipedia.org/wiki/IMAP\" class=\"mw-redirect\" title=\"IMAP\">IMAP</a> (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "IMAP", "link": "https://wikipedia.org/wiki/IMAP"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech footballer (b. 1980)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Drobn%C3%BD\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech footballer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Drobn%C3%BD\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech footballer (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A1clav_Drobn%C3%BD"}]}, {"year": "2012", "text": "<PERSON>, Irish hurler and manager (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurler and manager (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurler and manager (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American-German astronomer and critic (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Halton_Arp\" title=\"Halton Arp\"><PERSON><PERSON></a>, American-German astronomer and critic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halton_Arp\" title=\"Halton Arp\"><PERSON><PERSON></a>, American-German astronomer and critic (b. 1927)", "links": [{"title": "Halton Arp", "link": "https://wikipedia.org/wiki/Halton_Arp"}]}, {"year": "2013", "text": "<PERSON>, Cuban soprano and actress (b. 1913)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban soprano and actress (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban soprano and actress (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Jr., American soldier, lawyer, and politician (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American soldier, lawyer, and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American soldier, lawyer, and politician (b. 1932)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "2013", "text": "<PERSON>, American businessman, founded <PERSON><PERSON> (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1919)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s"}]}, {"year": "2013", "text": "<PERSON>, American actor and producer (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Ukrainian-Russian footballer and manager (b. 1969)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>mbalar\" title=\"<PERSON><PERSON> Tsymbalar\"><PERSON><PERSON></a>, Ukrainian-Russian footballer and manager (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>mba<PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian footballer and manager (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilya_Tsymbalar"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American transgender teenager (b. 1997)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Death of Leelah Alcorn\"><PERSON><PERSON></a>, American transgender teenager (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Death of Leelah Alcorn\"><PERSON><PERSON></a>, American transgender teenager (b. 1997)", "links": [{"title": "Death of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Armenian politician (b. 1956)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian politician (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian politician (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1938)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "2015", "text": "<PERSON>, English drummer and songwriter (b. 1953)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English drummer and songwriter (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English drummer and songwriter (b. 1953)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Mariana Islander businessman and politician, 8th Governor of the Northern Mariana Islands (b. 1949)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mariana Islander businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_the_Northern_Mariana_Islands\" class=\"mw-redirect\" title=\"List of Governors of the Northern Mariana Islands\">Governor of the Northern Mariana Islands</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mariana Islander businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_the_Northern_Mariana_Islands\" class=\"mw-redirect\" title=\"List of Governors of the Northern Mariana Islands\">Governor of the Northern Mariana Islands</a> (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>os"}, {"title": "List of Governors of the Northern Mariana Islands", "link": "https://wikipedia.org/wiki/List_of_Governors_of_the_Northern_Mariana_Islands"}]}, {"year": "2015", "text": "<PERSON><PERSON>, English musician, singer, and songwriter (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Lemmy\" title=\"Lemmy\"><PERSON><PERSON></a>, English musician, singer, and songwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician, singer, and songwriter (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actress, singer and dancer (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer and dancer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer and dancer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, French political scientist (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French political scientist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French political scientist (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American actress and comedienne (b. 1923)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedienne (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedienne (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, French television presenter and scientific essayist (b. 1949)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Bogdanoff\"><PERSON><PERSON><PERSON></a>, French television presenter and scientific essayist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>gdanoff\"><PERSON><PERSON><PERSON></a>, French television presenter and scientific essayist (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American football Hall of Fame coach and commentator (b. 1936)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football <a href=\"https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame\" title=\"Pro Football Hall of Fame\">Hall of Fame</a> coach and commentator (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football <a href=\"https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame\" title=\"Pro Football Hall of Fame\">Hall of Fame</a> coach and commentator (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pro Football Hall of Fame", "link": "https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame"}]}, {"year": "2021", "text": "<PERSON>, American lawyer, politician, and former Senate majority leader (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and former <a href=\"https://wikipedia.org/wiki/Senate_majority_leader\" class=\"mw-redirect\" title=\"Senate majority leader\">Senate majority leader</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and former <a href=\"https://wikipedia.org/wiki/Senate_majority_leader\" class=\"mw-redirect\" title=\"Senate majority leader\">Senate majority leader</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Senate majority leader", "link": "https://wikipedia.org/wiki/Senate_majority_leader"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, German Romani author (b. 1922)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Romani author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Romani author (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actor and politician (b. 1952)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Vijayakanth\" title=\"<PERSON>aka<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vijayakanth\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor and politician (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vijayakanth"}]}, {"year": "2024", "text": "<PERSON>, American businessman, founded Cablevision and HBO (b. 1926)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Cablevision\" title=\"Cablevision\">Cablevision</a> and <a href=\"https://wikipedia.org/wiki/HBO\" title=\"HBO\">HBO</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Cablevision\" title=\"Cablevision\">Cablevision</a> and <a href=\"https://wikipedia.org/wiki/HBO\" title=\"HBO\">HBO</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cablevision", "link": "https://wikipedia.org/wiki/Cablevision"}, {"title": "HBO", "link": "https://wikipedia.org/wiki/HBO"}]}]}}