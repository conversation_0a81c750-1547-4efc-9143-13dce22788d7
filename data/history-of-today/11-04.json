{"date": "November 4", "url": "https://wikipedia.org/wiki/November_4", "data": {"Events": [{"year": "1354", "text": "War of the Straits: The Genoese fleet under <PERSON><PERSON><PERSON> defeats and captures the entire Venetian fleet under <PERSON><PERSON><PERSON><PERSON> at the Battle of Sapienza.", "html": "1354 - <a href=\"https://wikipedia.org/wiki/War_of_the_Straits\" title=\"War of the Straits\">War of the Straits</a>: The <a href=\"https://wikipedia.org/wiki/Republic_of_Genoa\" title=\"Republic of Genoa\">Genoese</a> fleet under <a href=\"https://wikipedia.org/wiki/Paganino_Doria\" title=\"Paganino Doria\">Paganino Doria</a> defeats and captures the entire <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> fleet under <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Pisani\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Sapienza\" title=\"Battle of Sapienza\">Battle of Sapienza</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Straits\" title=\"War of the Straits\">War of the Straits</a>: The <a href=\"https://wikipedia.org/wiki/Republic_of_Genoa\" title=\"Republic of Genoa\">Genoese</a> fleet under <a href=\"https://wikipedia.org/wiki/Paganino_Doria\" title=\"Paganino Doria\">Pagan<PERSON> Do<PERSON></a> defeats and captures the entire <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> fleet under <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Pisani\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Sapienza\" title=\"Battle of Sapienza\">Battle of Sapienza</a>.", "links": [{"title": "War of the Straits", "link": "https://wikipedia.org/wiki/War_of_the_Straits"}, {"title": "Republic of Genoa", "link": "https://wikipedia.org/wiki/Republic_of_Genoa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Paganino_Doria"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON>i"}, {"title": "Battle of Sapienza", "link": "https://wikipedia.org/wiki/Battle_of_Sapienza"}]}, {"year": "1429", "text": "Armagnac-Burgundian Civil War: Joan of Arc liberates Saint-Pierre-le-Moûtier.", "html": "1429 - <a href=\"https://wikipedia.org/wiki/Armagnac%E2%80%93Burgundian_Civil_War\" title=\"Armagnac-Burgundian Civil War\">Armagnac-Burgundian Civil War</a>: <a href=\"https://wikipedia.org/wiki/Joan_of_Arc\" title=\"Joan of Arc\">Joan of Arc</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Saint-Pierre-le-Mo%C3%BBtier\" title=\"Siege of Saint-Pierre-le-Moûtier\">liberates Saint-Pierre-le-Moûtier</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armagnac%E2%80%93Burgundian_Civil_War\" title=\"Armagnac-Burgundian Civil War\">Armagnac-Burgundian Civil War</a>: <a href=\"https://wikipedia.org/wiki/Joan_of_Arc\" title=\"Joan of Arc\">Joan of Arc</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Saint-Pierre-le-Mo%C3%BBtier\" title=\"Siege of Saint-Pierre-le-Moûtier\">liberates Saint-Pierre-le-Moûtier</a>.", "links": [{"title": "Armagnac-Burgundian Civil War", "link": "https://wikipedia.org/wiki/Armagnac%E2%80%93Burgundian_Civil_War"}, {"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Siege of Saint-Pierre-le-Moûtier", "link": "https://wikipedia.org/wiki/Siege_of_Saint-Pierre-le-Mo%C3%BBtier"}]}, {"year": "1493", "text": "<PERSON> reaches the Leeward Islands.", "html": "1493 - <a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>\" title=\"Christopher Columbus\"><PERSON></a> reaches the <a href=\"https://wikipedia.org/wiki/Leeward_Islands\" title=\"Leeward Islands\">Leeward Islands</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christopher_Columbus\" title=\"Christopher Columbus\"><PERSON></a> reaches the <a href=\"https://wikipedia.org/wiki/Leeward_Islands\" title=\"Leeward Islands\">Leeward Islands</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Leeward Islands", "link": "https://wikipedia.org/wiki/Leeward_Islands"}]}, {"year": "1501", "text": "<PERSON> of Aragon (later <PERSON>'s first wife) meets <PERSON>, <PERSON>'s older brother - they would later marry.", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON></a> (later <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON></a>'s first wife) meets <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <PERSON>'s older brother - they would later marry.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (later <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON></a>'s first wife) meets <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <PERSON>'s older brother - they would later marry.", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1576", "text": "Eighty Years' War: In Flanders, Spain captures Antwerp (which is nearly destroyed after three days).", "html": "1576 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: In <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flanders</a>, Spain captures <a href=\"https://wikipedia.org/wiki/Antwerp\" title=\"Antwerp\">Antwerp</a> (which is <a href=\"https://wikipedia.org/wiki/Sack_of_Antwerp\" title=\"Sack of Antwerp\">nearly destroyed</a> after three days).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: In <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flanders</a>, Spain captures <a href=\"https://wikipedia.org/wiki/Antwerp\" title=\"Antwerp\">Antwerp</a> (which is <a href=\"https://wikipedia.org/wiki/Sack_of_Antwerp\" title=\"Sack of Antwerp\">nearly destroyed</a> after three days).", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Flanders", "link": "https://wikipedia.org/wiki/Flanders"}, {"title": "Antwerp", "link": "https://wikipedia.org/wiki/Antwerp"}, {"title": "Sack of Antwerp", "link": "https://wikipedia.org/wiki/Sack_of_Antwerp"}]}, {"year": "1677", "text": "The future <PERSON> of England marries <PERSON>, Prince of Orange; they later jointly reign as <PERSON> and <PERSON>.", "html": "1677 - The future <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"<PERSON> II of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"<PERSON> of England\"><PERSON>, Prince of Orange</a>; they later jointly reign as <PERSON> and <PERSON>.", "no_year_html": "The future <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> II of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON>, Prince of Orange</a>; they later jointly reign as <PERSON> and <PERSON>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Mary_<PERSON>_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1737", "text": "The Teatro di San Carlo, the oldest working opera house in Europe, is inaugurated in Naples, Italy.", "html": "1737 - The <a href=\"https://wikipedia.org/wiki/Teatro_di_San_Carlo\" title=\"Teatro di San Carlo\">Teatro di San Carlo</a>, the oldest working opera house in Europe, is inaugurated in Naples, Italy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Teatro_di_San_Carlo\" title=\"Teatro di San Carlo\">Teatro di San Carlo</a>, the oldest working opera house in Europe, is inaugurated in Naples, Italy.", "links": [{"title": "Teatro di San Carlo", "link": "https://wikipedia.org/wiki/Teatro_di_San_Carlo"}]}, {"year": "1780", "text": "The Rebellion of Túpac Amaru II against Spanish rule in the Viceroyalty of Peru begins.", "html": "1780 - The <a href=\"https://wikipedia.org/wiki/Rebellion_of_T%C3%BApac_Amaru_II\" title=\"Rebellion of Túpac Amaru II\">Rebellion of Túpac Amaru II</a> against Spanish rule in the <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_Peru\" title=\"Viceroyalty of Peru\">Viceroyalty of Peru</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rebellion_of_T%C3%BApac_Amaru_II\" title=\"Rebellion of Túpac Amaru II\">Rebellion of Túpac Amaru II</a> against Spanish rule in the <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_Peru\" title=\"Viceroyalty of Peru\">Viceroyalty of Peru</a> begins.", "links": [{"title": "Rebellion of Túpac Amaru II", "link": "https://wikipedia.org/wiki/Rebellion_of_T%C3%BApac_Amaru_II"}, {"title": "Viceroyalty of Peru", "link": "https://wikipedia.org/wiki/Viceroyalty_of_Peru"}]}, {"year": "1783", "text": "<PERSON>'s Symphony No. 36 is performed for the first time in Linz, Austria.", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ade<PERSON>_Mozart\" title=\"<PERSON> Amadeus Mozart\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._36_(Mozart)\" title=\"Symphony No. 36 (<PERSON>)\">Symphony No. 36</a> is performed for the first time in <a href=\"https://wikipedia.org/wiki/Linz\" title=\"Linz\">Linz</a>, Austria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ade<PERSON>_<PERSON>\" title=\"<PERSON> Amadeus Mozart\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._36_(Mozart)\" title=\"Symphony No. 36 (Mozart)\">Symphony No. 36</a> is performed for the first time in <a href=\"https://wikipedia.org/wiki/Linz\" title=\"Linz\">Linz</a>, Austria.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Symphony No. 36 (<PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._36_(<PERSON>)"}, {"title": "Linz", "link": "https://wikipedia.org/wiki/Linz"}]}, {"year": "1791", "text": "Northwest Indian War: The Western Confederacy of American Indians wins a major victory over the United States in the Battle of the Wabash.", "html": "1791 - <a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Western_Confederacy\" class=\"mw-redirect\" title=\"Western Confederacy\">Western Confederacy</a> of <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">American Indians</a> wins a major victory over the United States in the <a href=\"https://wikipedia.org/wiki/St._Clair%27s_Defeat\" class=\"mw-redirect\" title=\"<PERSON>. Clair's Defeat\">Battle of the Wabash</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Western_Confederacy\" class=\"mw-redirect\" title=\"Western Confederacy\">Western Confederacy</a> of <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">American Indians</a> wins a major victory over the United States in the <a href=\"https://wikipedia.org/wiki/St._Clair%27s_Defeat\" class=\"mw-redirect\" title=\"St. Clair's Defeat\">Battle of the Wabash</a>.", "links": [{"title": "Northwest Indian War", "link": "https://wikipedia.org/wiki/Northwest_Indian_War"}, {"title": "Western Confederacy", "link": "https://wikipedia.org/wiki/Western_Confederacy"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}, {"title": "St<PERSON> Clair's Defeat", "link": "https://wikipedia.org/wiki/St._Clair%27s_Defeat"}]}, {"year": "1798", "text": "The Russo-Ottoman siege of Corfu begins.", "html": "1798 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Corfu_(1798%E2%80%9399)\" class=\"mw-redirect\" title=\"Siege of Corfu (1798-99)\">Russo-Ottoman siege</a> of <a href=\"https://wikipedia.org/wiki/Corfu\" title=\"Corfu\">Corfu</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Corfu_(1798%E2%80%9399)\" class=\"mw-redirect\" title=\"Siege of Corfu (1798-99)\">Russo-Ottoman siege</a> of <a href=\"https://wikipedia.org/wiki/Corfu\" title=\"Corfu\">Corfu</a> begins.", "links": [{"title": "Siege of Corfu (1798-99)", "link": "https://wikipedia.org/wiki/Siege_of_Corfu_(1798%E2%80%9399)"}, {"title": "Corfu", "link": "https://wikipedia.org/wiki/Corfu"}]}, {"year": "1839", "text": "Newport Rising: The last large-scale armed rebellion against authority in mainland Britain.", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Newport_Rising\" title=\"Newport Rising\">Newport Rising</a>: The last large-scale armed rebellion against authority in mainland Britain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Newport_Rising\" title=\"Newport Rising\">Newport Rising</a>: The last large-scale armed rebellion against authority in mainland Britain.", "links": [{"title": "Newport Rising", "link": "https://wikipedia.org/wiki/Newport_Rising"}]}, {"year": "1847", "text": "Sir <PERSON>, a Scottish physician, discovers the anaesthetic properties of chloroform.", "html": "1847 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Scottish physician, discovers the <a href=\"https://wikipedia.org/wiki/Anaesthetic\" class=\"mw-redirect\" title=\"Anaesthetic\">anaesthetic</a> properties of <a href=\"https://wikipedia.org/wiki/Chloroform\" title=\"Chloroform\">chloroform</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Scottish physician, discovers the <a href=\"https://wikipedia.org/wiki/Anaesthetic\" class=\"mw-redirect\" title=\"Anaesthetic\">anaesthetic</a> properties of <a href=\"https://wikipedia.org/wiki/Chloroform\" title=\"Chloroform\">chloroform</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Anaesthetic", "link": "https://wikipedia.org/wiki/Anaesthetic"}, {"title": "Chloroform", "link": "https://wikipedia.org/wiki/Chloroform"}]}, {"year": "1852", "text": "<PERSON><PERSON>, Count of Cavour, becomes the prime minister of Piedmont-Sardinia, which soon expands to become Italy.", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Cavour\" title=\"<PERSON><PERSON>, Count of Cavour\"><PERSON><PERSON>, Count of Cavour</a>, becomes the <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">prime minister</a> of <a href=\"https://wikipedia.org/wiki/Piedmont\" title=\"Piedmont\">Piedmont</a>-<a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia\" title=\"Kingdom of Sardinia\">Sardinia</a>, which soon expands to become Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Cavour\" title=\"<PERSON><PERSON>, Count of Cavour\"><PERSON><PERSON>, Count of Cavour</a>, becomes the <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">prime minister</a> of <a href=\"https://wikipedia.org/wiki/Piedmont\" title=\"Piedmont\">Piedmont</a>-<a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia\" title=\"Kingdom of Sardinia\">Sardinia</a>, which soon expands to become Italy.", "links": [{"title": "<PERSON><PERSON>, Count of Cavour", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_<PERSON><PERSON><PERSON>"}, {"title": "Prime minister", "link": "https://wikipedia.org/wiki/Prime_minister"}, {"title": "Piedmont", "link": "https://wikipedia.org/wiki/Piedmont"}, {"title": "Kingdom of Sardinia", "link": "https://wikipedia.org/wiki/Kingdom_of_Sardinia"}]}, {"year": "1864", "text": "American Civil War: Confederate troops bombard a Union supply base and destroy millions of dollars in materiel at the Battle of Johnsonville.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops bombard a <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> supply base and destroy millions of dollars in materiel at the <a href=\"https://wikipedia.org/wiki/Battle_of_Johnsonville\" title=\"Battle of Johnsonville\">Battle of Johnsonville</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops bombard a <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> supply base and destroy millions of dollars in materiel at the <a href=\"https://wikipedia.org/wiki/Battle_of_Johnsonville\" title=\"Battle of Johnsonville\">Battle of Johnsonville</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "Battle of Johnsonville", "link": "https://wikipedia.org/wiki/Battle_of_Johnsonville"}]}, {"year": "1868", "text": "Camagüey, Cuba, revolts against Spain during the Ten Years' War.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Camag%C3%BCey\" title=\"Camagüey\">Camagüey</a>, Cuba, revolts against Spain during the <a href=\"https://wikipedia.org/wiki/Ten_Years%27_War\" title=\"Ten Years' War\">Ten Years' War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Camag%C3%BCey\" title=\"Camagüey\">Camagüey</a>, Cuba, revolts against Spain during the <a href=\"https://wikipedia.org/wiki/Ten_Years%27_War\" title=\"Ten Years' War\">Ten Years' War</a>.", "links": [{"title": "Camagüey", "link": "https://wikipedia.org/wiki/Camag%C3%BCey"}, {"title": "Ten Years' War", "link": "https://wikipedia.org/wiki/Ten_Years%27_War"}]}, {"year": "1890", "text": "City and South London Railway: London's first deep-level tube railway opens between King William Street and Stockwell.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/City_and_South_London_Railway\" title=\"City and South London Railway\">City and South London Railway</a>: London's first deep-level <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">tube</a> railway opens between <a href=\"https://wikipedia.org/wiki/King_William_Street,_London\" title=\"King William Street, London\">King William Street</a> and <a href=\"https://wikipedia.org/wiki/Stockwell_tube_station\" title=\"Stockwell tube station\">Stockwell</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/City_and_South_London_Railway\" title=\"City and South London Railway\">City and South London Railway</a>: London's first deep-level <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">tube</a> railway opens between <a href=\"https://wikipedia.org/wiki/King_William_Street,_London\" title=\"King William Street, London\">King William Street</a> and <a href=\"https://wikipedia.org/wiki/Stockwell_tube_station\" title=\"Stockwell tube station\">Stockwell</a>.", "links": [{"title": "City and South London Railway", "link": "https://wikipedia.org/wiki/City_and_South_London_Railway"}, {"title": "London Underground", "link": "https://wikipedia.org/wiki/London_Underground"}, {"title": "King William Street, London", "link": "https://wikipedia.org/wiki/King_William_<PERSON>,_London"}, {"title": "Stockwell tube station", "link": "https://wikipedia.org/wiki/Stockwell_tube_station"}]}, {"year": "1918", "text": "World War I: The Armistice of Villa Giusti between Italy and Austria-Hungary is implemented.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Armistice_of_Villa_Giusti\" title=\"Armistice of Villa Giusti\">Armistice of Villa Giusti</a> between Italy and <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> is implemented.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Armistice_of_Villa_Giusti\" title=\"Armistice of Villa Giusti\">Armistice of Villa Giusti</a> between Italy and <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> is implemented.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Armistice of Villa Giusti", "link": "https://wikipedia.org/wiki/Armistice_of_Villa_Giusti"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}]}, {"year": "1921", "text": "The Saalschutz Abteilung (hall defense detachment) of the Nazi Party is renamed the Sturmabteilung (storm detachment) after a large riot in Munich.", "html": "1921 - The <i>Saalschutz Abteilung</i> (hall defense detachment) of the <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a> is renamed the <i><a href=\"https://wikipedia.org/wiki/Sturmabteilung\" title=\"Sturmabteilung\">Sturmabteilung</a></i> (storm detachment) after a large riot in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>.", "no_year_html": "The <i>Saalschutz Abteilung</i> (hall defense detachment) of the <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a> is renamed the <i><a href=\"https://wikipedia.org/wiki/Sturmabteilung\" title=\"Sturmabteilung\">Sturmabteilung</a></i> (storm detachment) after a large riot in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>.", "links": [{"title": "Nazi Party", "link": "https://wikipedia.org/wiki/Nazi_Party"}, {"title": "Sturmabteilung", "link": "https://wikipedia.org/wiki/Sturmabteilung"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}]}, {"year": "1921", "text": "Japanese Prime Minister <PERSON> is assassinated in Tokyo.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Japanese Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated in Tokyo.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Japanese Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated in Tokyo.", "links": [{"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hi"}]}, {"year": "1922", "text": "In Egypt, British archaeologist <PERSON> and his men find the entrance to <PERSON><PERSON><PERSON><PERSON><PERSON>'s tomb in the Valley of the Kings.", "html": "1922 - In Egypt, British archaeologist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his men find the entrance to <a href=\"https://wikipedia.org/wiki/Tutankhamun\" title=\"Tutankhamun\">Tutankhamun</a>'s tomb in the <a href=\"https://wikipedia.org/wiki/Valley_of_the_Kings\" title=\"Valley of the Kings\">Valley of the Kings</a>.", "no_year_html": "In Egypt, British archaeologist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his men find the entrance to <a href=\"https://wikipedia.org/wiki/Tutankhamun\" title=\"Tutankhamun\">Tutankhamun</a>'s tomb in the <a href=\"https://wikipedia.org/wiki/Valley_of_the_Kings\" title=\"Valley of the Kings\">Valley of the Kings</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tutankhamun", "link": "https://wikipedia.org/wiki/Tutankhamun"}, {"title": "Valley of the Kings", "link": "https://wikipedia.org/wiki/Valley_of_the_Kings"}]}, {"year": "1924", "text": "<PERSON><PERSON> of Wyoming becomes the first female elected as governor in the United States.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Wyoming\" title=\"Wyoming\">Wyoming</a> becomes the first female elected as governor in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Wyoming\" title=\"Wyoming\">Wyoming</a> becomes the first female elected as governor in the United States.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Wyoming", "link": "https://wikipedia.org/wiki/Wyoming"}]}, {"year": "1936", "text": "Spanish Civil War: <PERSON><PERSON> reshuffles his war cabinet, persuading the anarcho-syndicalist CNT to join the government.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Largo_Caballero\" class=\"mw-redirect\" title=\"Largo Caballero\">Largo Caballero</a> <a href=\"https://wikipedia.org/wiki/Cabinet_reshuffle\" title=\"Cabinet reshuffle\">reshuffles</a> his <a href=\"https://wikipedia.org/wiki/War_cabinet\" title=\"War cabinet\">war cabinet</a>, persuading the <a href=\"https://wikipedia.org/wiki/Anarcho-syndicalist\" class=\"mw-redirect\" title=\"Anarcho-syndicalist\">anarcho-syndicalist</a> <a href=\"https://wikipedia.org/wiki/Confederaci%C3%B3n_Nacional_del_Trabajo\" title=\"Confederación Nacional del Trabajo\">CNT</a> to join the government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Largo_Caballero\" class=\"mw-redirect\" title=\"Largo Caballero\">Largo Caballero</a> <a href=\"https://wikipedia.org/wiki/Cabinet_reshuffle\" title=\"Cabinet reshuffle\">reshuffles</a> his <a href=\"https://wikipedia.org/wiki/War_cabinet\" title=\"War cabinet\">war cabinet</a>, persuading the <a href=\"https://wikipedia.org/wiki/Anarcho-syndicalist\" class=\"mw-redirect\" title=\"Anarcho-syndicalist\">anarcho-syndicalist</a> <a href=\"https://wikipedia.org/wiki/Confederaci%C3%B3n_Nacional_del_Trabajo\" title=\"Confederación Nacional del Trabajo\">CNT</a> to join the government.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Largo <PERSON>", "link": "https://wikipedia.org/wiki/Largo_Caballero"}, {"title": "Cabinet reshuffle", "link": "https://wikipedia.org/wiki/Cabinet_reshuffle"}, {"title": "War cabinet", "link": "https://wikipedia.org/wiki/War_cabinet"}, {"title": "Anarcho-syndicalist", "link": "https://wikipedia.org/wiki/Anarcho-syndicalist"}, {"title": "Confederación Nacional del Trabajo", "link": "https://wikipedia.org/wiki/Confederaci%C3%B3n_Nacional_del_Trabajo"}]}, {"year": "1939", "text": "World War II: U.S. President <PERSON> orders the United States Customs Service to implement the Neutrality Act of 1939, allowing cash-and-carry purchases of weapons by belligerents.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/United_States_Customs_Service\" title=\"United States Customs Service\">United States Customs Service</a> to implement the <a href=\"https://wikipedia.org/wiki/Neutrality_Acts_of_the_1930s#Neutrality_Act_of_1939\" title=\"Neutrality Acts of the 1930s\">Neutrality Act of 1939</a>, allowing cash-and-carry purchases of weapons by belligerents.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/United_States_Customs_Service\" title=\"United States Customs Service\">United States Customs Service</a> to implement the <a href=\"https://wikipedia.org/wiki/Neutrality_Acts_of_the_1930s#Neutrality_Act_of_1939\" title=\"Neutrality Acts of the 1930s\">Neutrality Act of 1939</a>, allowing cash-and-carry purchases of weapons by belligerents.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Customs Service", "link": "https://wikipedia.org/wiki/United_States_Customs_Service"}, {"title": "Neutrality Acts of the 1930s", "link": "https://wikipedia.org/wiki/Neutrality_Acts_of_the_1930s#Neutrality_Act_of_1939"}]}, {"year": "1942", "text": "World War II: Disobeying a direct order by <PERSON>, General Field Marshal <PERSON> begins a retreat of his forces after a costly defeat during the Second Battle of El Alamein. The retreat would ultimately last five months.", "html": "1942 - World War II: Disobeying a direct order by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a>, General Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins a retreat of his forces after a costly defeat during the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_El_Alamein\" title=\"Second Battle of El Alamein\">Second Battle of El Alamein</a>. The retreat would ultimately last five months.", "no_year_html": "World War II: Disobeying a direct order by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hitler\"><PERSON></a>, General Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins a retreat of his forces after a costly defeat during the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_El_Alamein\" title=\"Second Battle of El Alamein\">Second Battle of El Alamein</a>. The retreat would ultimately last five months.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second Battle of El Alamein", "link": "https://wikipedia.org/wiki/Second_Battle_of_El_Alamein"}]}, {"year": "1944", "text": "World War II: The 7th Macedonian Liberation Brigade liberates Bitola for the Allies.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Macedonian_Partisans\" title=\"Macedonian Partisans\">7th Macedonian Liberation Brigade</a> liberates <a href=\"https://wikipedia.org/wiki/Bitola\" title=\"Bitola\">Bitola</a> for the Allies.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Macedonian_Partisans\" title=\"Macedonian Partisans\">7th Macedonian Liberation Brigade</a> liberates <a href=\"https://wikipedia.org/wiki/Bitola\" title=\"Bitola\">Bitola</a> for the Allies.", "links": [{"title": "Macedonian Partisans", "link": "https://wikipedia.org/wiki/Macedonian_Partisans"}, {"title": "Bitola", "link": "https://wikipedia.org/wiki/Bitola"}]}, {"year": "1944", "text": "World War II: Operation Pheasant, an Allied offensive to liberate North Brabant in the Netherlands, ends successfully.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Pheasant\" title=\"Operation Pheasant\">Operation Pheasant</a>, an Allied offensive to liberate <a href=\"https://wikipedia.org/wiki/North_Brabant\" title=\"North Brabant\">North Brabant</a> in the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>, ends successfully.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Pheasant\" title=\"Operation Pheasant\">Operation Pheasant</a>, an Allied offensive to liberate <a href=\"https://wikipedia.org/wiki/North_Brabant\" title=\"North Brabant\">North Brabant</a> in the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>, ends successfully.", "links": [{"title": "Operation Pheasant", "link": "https://wikipedia.org/wiki/Operation_Pheasant"}, {"title": "North Brabant", "link": "https://wikipedia.org/wiki/North_Brabant"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}]}, {"year": "1952", "text": "The United States government establishes the National Security Agency, or NSA.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">United States government</a> establishes the <a href=\"https://wikipedia.org/wiki/National_Security_Agency\" title=\"National Security Agency\">National Security Agency</a>, or NSA.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">United States government</a> establishes the <a href=\"https://wikipedia.org/wiki/National_Security_Agency\" title=\"National Security Agency\">National Security Agency</a>, or NSA.", "links": [{"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}, {"title": "National Security Agency", "link": "https://wikipedia.org/wiki/National_Security_Agency"}]}, {"year": "1956", "text": "Soviet troops enter Hungary to end the Hungarian revolution against the Soviet Union that started on October 23. Thousands are killed, more are wounded, and nearly a quarter million leave the country.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> troops enter <a href=\"https://wikipedia.org/wiki/Hungarian_People%27s_Republic\" title=\"Hungarian People's Republic\">Hungary</a> to end the <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian revolution</a> against the Soviet Union that started on <a href=\"https://wikipedia.org/wiki/October_23\" title=\"October 23\">October 23</a>. Thousands are killed, more are wounded, and nearly a quarter million leave the country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> troops enter <a href=\"https://wikipedia.org/wiki/Hungarian_People%27s_Republic\" title=\"Hungarian People's Republic\">Hungary</a> to end the <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian revolution</a> against the Soviet Union that started on <a href=\"https://wikipedia.org/wiki/October_23\" title=\"October 23\">October 23</a>. Thousands are killed, more are wounded, and nearly a quarter million leave the country.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Hungarian People's Republic", "link": "https://wikipedia.org/wiki/Hungarian_People%27s_Republic"}, {"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}, {"title": "October 23", "link": "https://wikipedia.org/wiki/October_23"}]}, {"year": "1960", "text": "At the Kasakela Chimpanzee Community in Tanzania, Dr. <PERSON> observes chimpanzees creating tools, the first-ever observation in non-human animals.", "html": "1960 - At the <a href=\"https://wikipedia.org/wiki/Kasakela_chimpanzee_community\" title=\"Kasakela chimpanzee community\">Kasakela Chimpanzee Community</a> in Tanzania, Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> observes <a href=\"https://wikipedia.org/wiki/Eastern_chimpanzee\" title=\"Eastern chimpanzee\">chimpanzees</a> creating tools, the first-ever observation in non-human animals.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Kasakela_chimpanzee_community\" title=\"Kasakela chimpanzee community\">Kasakela Chimpanzee Community</a> in Tanzania, Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> observes <a href=\"https://wikipedia.org/wiki/Eastern_chimpanzee\" title=\"Eastern chimpanzee\">chimpanzees</a> creating tools, the first-ever observation in non-human animals.", "links": [{"title": "Kasakela chimpanzee community", "link": "https://wikipedia.org/wiki/Kasakela_chimpanzee_community"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Eastern chimpanzee", "link": "https://wikipedia.org/wiki/Eastern_chimpanzee"}]}, {"year": "1962", "text": "The United States concludes Operation Fishbowl, its final above-ground nuclear weapons testing series, in anticipation of the 1963 Partial Nuclear Test Ban Treaty.", "html": "1962 - The United States concludes <a href=\"https://wikipedia.org/wiki/Operation_Fishbowl\" title=\"Operation Fishbowl\">Operation Fishbowl</a>, its final above-ground <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">nuclear weapons testing</a> series, in anticipation of the 1963 <a href=\"https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty\" title=\"Partial Nuclear Test Ban Treaty\">Partial Nuclear Test Ban Treaty</a>.", "no_year_html": "The United States concludes <a href=\"https://wikipedia.org/wiki/Operation_Fishbowl\" title=\"Operation Fishbowl\">Operation Fishbowl</a>, its final above-ground <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">nuclear weapons testing</a> series, in anticipation of the 1963 <a href=\"https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty\" title=\"Partial Nuclear Test Ban Treaty\">Partial Nuclear Test Ban Treaty</a>.", "links": [{"title": "Operation Fishbowl", "link": "https://wikipedia.org/wiki/Operation_Fishbowl"}, {"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "Partial Nuclear Test Ban Treaty", "link": "https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty"}]}, {"year": "1966", "text": "The Arno River floods Florence, Italy, to a maximum depth of 6.7 m (22 ft), leaving thousands homeless and destroying millions of masterpieces of art and rare books. Venice is also submerged on the same day at its record all-time acqua alta of 194 cm (76 in).", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Arno\" title=\"Arno\">Arno River</a> <a href=\"https://wikipedia.org/wiki/1966_flood_of_the_Arno\" title=\"1966 flood of the Arno\">floods</a> <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>, Italy, to a maximum depth of 6.7 m (22 ft), leaving thousands homeless and destroying millions of masterpieces of art and rare books. <a href=\"https://wikipedia.org/wiki/1966_Venice_flood\" title=\"1966 Venice flood\">Venice is also submerged</a> on the same day at its record all-time <a href=\"https://wikipedia.org/wiki/Acqua_alta\" title=\"Acqua alta\">acqua alta</a> of 194 cm (76 in).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arno\" title=\"Arno\">Arno River</a> <a href=\"https://wikipedia.org/wiki/1966_flood_of_the_Arno\" title=\"1966 flood of the Arno\">floods</a> <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>, Italy, to a maximum depth of 6.7 m (22 ft), leaving thousands homeless and destroying millions of masterpieces of art and rare books. <a href=\"https://wikipedia.org/wiki/1966_Venice_flood\" title=\"1966 Venice flood\">Venice is also submerged</a> on the same day at its record all-time <a href=\"https://wikipedia.org/wiki/Acqua_alta\" title=\"Acqua alta\">acqua alta</a> of 194 cm (76 in).", "links": [{"title": "Arno", "link": "https://wikipedia.org/wiki/Arno"}, {"title": "1966 flood of the Arno", "link": "https://wikipedia.org/wiki/1966_flood_of_the_Arno"}, {"title": "Florence", "link": "https://wikipedia.org/wiki/Florence"}, {"title": "1966 Venice flood", "link": "https://wikipedia.org/wiki/1966_Venice_flood"}, {"title": "Acqua alta", "link": "https://wikipedia.org/wiki/Acqua_alta"}]}, {"year": "1967", "text": "Iberia Flight 062 crashes in Blackdown, West Sussex, killing all 37 people on board including British actress <PERSON>.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Iberia_Flight_062\" title=\"Iberia Flight 062\">Iberia Flight 062</a> crashes in <a href=\"https://wikipedia.org/wiki/Blackdown,_West_Sussex\" title=\"Blackdown, West Sussex\">Blackdown, West Sussex</a>, killing all 37 people on board including British actress <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON><PERSON>\">June <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iberia_Flight_062\" title=\"Iberia Flight 062\">Iberia Flight 062</a> crashes in <a href=\"https://wikipedia.org/wiki/Blackdown,_West_Sussex\" title=\"Blackdown, West Sussex\">Blackdown, West Sussex</a>, killing all 37 people on board including British actress <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON><PERSON>\">June <PERSON></a>.", "links": [{"title": "Iberia Flight 062", "link": "https://wikipedia.org/wiki/Iberia_Flight_062"}, {"title": "Blackdown, West Sussex", "link": "https://wikipedia.org/wiki/Blackdown,_West_Sussex"}, {"title": "June <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON><PERSON>"}]}, {"year": "1970", "text": "Vietnam War: The United States turns over control of the air base at Bình Thủy in the Mekong Delta to South Vietnam.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The United States turns over control of the <a href=\"https://wikipedia.org/wiki/Binh_Thuy_Air_Base\" title=\"Binh Thuy Air Base\">air base at Bình Thủy in the Mekong Delta</a> to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The United States turns over control of the <a href=\"https://wikipedia.org/wiki/Binh_Thuy_Air_Base\" title=\"Binh Thuy Air Base\">air base at Bình Thủy in the Mekong Delta</a> to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Binh Thuy Air Base", "link": "https://wikipedia.org/wiki/Binh_Thuy_Air_Base"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1970", "text": "<PERSON> takes office as President of Chile, the first Marxist to become president of a Latin American country through open elections.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"Salvador Allen<PERSON>\"><PERSON></a> takes office as <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a>, the first <a href=\"https://wikipedia.org/wiki/Marxism\" title=\"Marxism\">Marxist</a> to become president of a <a href=\"https://wikipedia.org/wiki/Latin_America\" title=\"Latin America\">Latin American</a> country through open elections.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"Salvador Allende\"><PERSON></a> takes office as <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a>, the first <a href=\"https://wikipedia.org/wiki/Marxism\" title=\"Marxism\">Marxist</a> to become president of a <a href=\"https://wikipedia.org/wiki/Latin_America\" title=\"Latin America\">Latin American</a> country through open elections.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvador_Allende"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}, {"title": "Marxism", "link": "https://wikipedia.org/wiki/Marxism"}, {"title": "Latin America", "link": "https://wikipedia.org/wiki/Latin_America"}]}, {"year": "1973", "text": "The Netherlands experiences the first car-free Sunday caused by the 1973 oil crisis. Highways are used only by cyclists and roller skaters.", "html": "1973 - The Netherlands experiences the first car-free Sunday caused by the <a href=\"https://wikipedia.org/wiki/1973_oil_crisis\" title=\"1973 oil crisis\">1973 oil crisis</a>. Highways are used only by <a href=\"https://wikipedia.org/wiki/Cyclist\" class=\"mw-redirect\" title=\"Cyclist\">cyclists</a> and <a href=\"https://wikipedia.org/wiki/Roller_skaters\" class=\"mw-redirect\" title=\"Roller skaters\">roller skaters</a>.", "no_year_html": "The Netherlands experiences the first car-free Sunday caused by the <a href=\"https://wikipedia.org/wiki/1973_oil_crisis\" title=\"1973 oil crisis\">1973 oil crisis</a>. Highways are used only by <a href=\"https://wikipedia.org/wiki/Cyclist\" class=\"mw-redirect\" title=\"Cyclist\">cyclists</a> and <a href=\"https://wikipedia.org/wiki/Roller_skaters\" class=\"mw-redirect\" title=\"Roller skaters\">roller skaters</a>.", "links": [{"title": "1973 oil crisis", "link": "https://wikipedia.org/wiki/1973_oil_crisis"}, {"title": "Cyclist", "link": "https://wikipedia.org/wiki/Cyclist"}, {"title": "Roller skaters", "link": "https://wikipedia.org/wiki/Roller_skaters"}]}, {"year": "1979", "text": "Iran hostage crisis: A group of Iranian college students overruns the U.S. embassy in Tehran and takes 90 hostages.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: A group of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> college students overruns the U.S. embassy in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a> and takes 90 hostages.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: A group of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> college students overruns the U.S. embassy in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a> and takes 90 hostages.", "links": [{"title": "Iran hostage crisis", "link": "https://wikipedia.org/wiki/Iran_hostage_crisis"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}]}, {"year": "1980", "text": "<PERSON> is elected as the 40th President of the United States, defeating incumbent <PERSON>.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1980_United_States_presidential_election\" title=\"1980 United States presidential election\">elected</a> as the 40th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, defeating incumbent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1980_United_States_presidential_election\" title=\"1980 United States presidential election\">elected</a> as the 40th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, defeating incumbent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1980 United States presidential election", "link": "https://wikipedia.org/wiki/1980_United_States_presidential_election"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "China Airlines Flight 605, a brand-new 747-400, overruns the runway at Hong Kong Kai Tak Airport.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_605\" title=\"China Airlines Flight 605\">China Airlines Flight 605</a>, a brand-new <a href=\"https://wikipedia.org/wiki/747-400\" class=\"mw-redirect\" title=\"747-400\">747-400</a>, overruns the runway at <a href=\"https://wikipedia.org/wiki/Hong_Kong\" title=\"Hong Kong\">Hong Kong</a> <a href=\"https://wikipedia.org/wiki/Kai_Tak_Airport\" title=\"Kai Tak Airport\">Kai Tak Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_605\" title=\"China Airlines Flight 605\">China Airlines Flight 605</a>, a brand-new <a href=\"https://wikipedia.org/wiki/747-400\" class=\"mw-redirect\" title=\"747-400\">747-400</a>, overruns the runway at <a href=\"https://wikipedia.org/wiki/Hong_Kong\" title=\"Hong Kong\">Hong Kong</a> <a href=\"https://wikipedia.org/wiki/Kai_Tak_Airport\" title=\"Kai Tak Airport\">Kai Tak Airport</a>.", "links": [{"title": "China Airlines Flight 605", "link": "https://wikipedia.org/wiki/China_Airlines_Flight_605"}, {"title": "747-400", "link": "https://wikipedia.org/wiki/747-400"}, {"title": "Hong Kong", "link": "https://wikipedia.org/wiki/Hong_Kong"}, {"title": "Kai Tak Airport", "link": "https://wikipedia.org/wiki/Kai_Tak_Airport"}]}, {"year": "1995", "text": "Israel-Palestinian conflict: Israeli prime minister <PERSON><PERSON><PERSON> is assassinated by an extremist Israeli.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israel-Palestinian conflict</a>: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> prime minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON>\">assassinated</a> by an extremist Israeli.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israel-Palestinian conflict</a>: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> prime minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Yitzhak <PERSON>bin\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON>\">assassinated</a> by an extremist Israeli.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Assassination of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2002", "text": "Chinese authorities arrest cyber-dissident <PERSON> for signing a pro-democracy letter to the 16th Communist Party Congress.", "html": "2002 - Chinese authorities arrest cyber-dissident <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> for signing a pro-democracy letter to the 16th <a href=\"https://wikipedia.org/wiki/National_Congress_of_the_Communist_Party_of_China\" class=\"mw-redirect\" title=\"National Congress of the Communist Party of China\">Communist Party Congress</a>.", "no_year_html": "Chinese authorities arrest cyber-dissident <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> for signing a pro-democracy letter to the 16th <a href=\"https://wikipedia.org/wiki/National_Congress_of_the_Communist_Party_of_China\" class=\"mw-redirect\" title=\"National Congress of the Communist Party of China\">Communist Party Congress</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "National Congress of the Communist Party of China", "link": "https://wikipedia.org/wiki/National_Congress_of_the_Communist_Party_of_China"}]}, {"year": "2008", "text": "<PERSON> becomes the first person of biracial or African-American descent to be elected as President of the United States.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> becomes the first person of biracial or <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African-American</a> descent to be <a href=\"https://wikipedia.org/wiki/2008_United_States_presidential_election\" title=\"2008 United States presidential election\">elected</a> as <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> becomes the first person of biracial or <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African-American</a> descent to be <a href=\"https://wikipedia.org/wiki/2008_United_States_presidential_election\" title=\"2008 United States presidential election\">elected</a> as <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}, {"title": "African Americans", "link": "https://wikipedia.org/wiki/African_Americans"}, {"title": "2008 United States presidential election", "link": "https://wikipedia.org/wiki/2008_United_States_presidential_election"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "2010", "text": "Aero Caribbean Flight 883 crashes into Guasimal, Sancti Spíritus; all 68 passengers and crew are killed.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Aero_Caribbean_Flight_883\" title=\"Aero Caribbean Flight 883\">Aero Caribbean Flight 883</a> crashes into <a href=\"https://wikipedia.org/wiki/Guasimal\" title=\"Guasimal\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON>; all 68 passengers and crew are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aero_Caribbean_Flight_883\" title=\"Aero Caribbean Flight 883\">Aero Caribbean Flight 883</a> crashes into <a href=\"https://wikipedia.org/wiki/Guasimal\" title=\"Guasimal\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON>; all 68 passengers and crew are killed.", "links": [{"title": "Aero Caribbean Flight 883", "link": "https://wikipedia.org/wiki/Aero_Caribbean_Flight_883"}, {"title": "Guasimal", "link": "https://wikipedia.org/wiki/Guasimal"}]}, {"year": "2010", "text": "Qantas Flight 32, an Airbus A380, suffers an uncontained engine failure over Indonesia shortly after taking off from Singapore, crippling the jet. The crew manage to safely return to Singapore, saving all 469 passengers and crew.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Qantas_Flight_32\" title=\"Qantas Flight 32\">Qantas Flight 32</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A380\" title=\"Airbus A380\">Airbus A380</a>, suffers an uncontained engine failure over Indonesia shortly after taking off from <a href=\"https://wikipedia.org/wiki/Changi_Airport\" title=\"Changi Airport\">Singapore</a>, crippling the jet. The crew manage to safely return to Singapore, saving all 469 passengers and crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qantas_Flight_32\" title=\"Qantas Flight 32\">Qantas Flight 32</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A380\" title=\"Airbus A380\">Airbus A380</a>, suffers an uncontained engine failure over Indonesia shortly after taking off from <a href=\"https://wikipedia.org/wiki/Changi_Airport\" title=\"Changi Airport\">Singapore</a>, crippling the jet. The crew manage to safely return to Singapore, saving all 469 passengers and crew.", "links": [{"title": "Qantas Flight 32", "link": "https://wikipedia.org/wiki/Qantas_Flight_32"}, {"title": "Airbus A380", "link": "https://wikipedia.org/wiki/Airbus_A380"}, {"title": "Changi Airport", "link": "https://wikipedia.org/wiki/Changi_Airport"}]}, {"year": "2015", "text": "A cargo plane crashes shortly after takeoff from Juba International Airport in Juba, South Sudan, killing at least 37 people.", "html": "2015 - A cargo plane <a href=\"https://wikipedia.org/wiki/2015_Juba_An-12_crash\" title=\"2015 Juba An-12 crash\">crashes</a> shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Juba_International_Airport\" title=\"Juba International Airport\">Juba International Airport</a> in <a href=\"https://wikipedia.org/wiki/Juba\" title=\"Juba\">Juba</a>, South Sudan, killing at least 37 people.", "no_year_html": "A cargo plane <a href=\"https://wikipedia.org/wiki/2015_Juba_An-12_crash\" title=\"2015 Juba An-12 crash\">crashes</a> shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Juba_International_Airport\" title=\"Juba International Airport\">Juba International Airport</a> in <a href=\"https://wikipedia.org/wiki/Juba\" title=\"Juba\">Juba</a>, South Sudan, killing at least 37 people.", "links": [{"title": "2015 Juba An-12 crash", "link": "https://wikipedia.org/wiki/2015_Juba_An-12_crash"}, {"title": "Juba International Airport", "link": "https://wikipedia.org/wiki/Juba_International_Airport"}, {"title": "Juba", "link": "https://wikipedia.org/wiki/Juba"}]}, {"year": "2015", "text": "A building collapses in the Pakistani city of Lahore resulting in at least 45 deaths and at least 100 injuries.", "html": "2015 - A building <a href=\"https://wikipedia.org/wiki/2015_Lahore_factory_disaster\" title=\"2015 Lahore factory disaster\">collapses</a> in the Pakistani city of <a href=\"https://wikipedia.org/wiki/Lahore\" title=\"Lahore\">Lahore</a> resulting in at least 45 deaths and at least 100 injuries.", "no_year_html": "A building <a href=\"https://wikipedia.org/wiki/2015_Lahore_factory_disaster\" title=\"2015 Lahore factory disaster\">collapses</a> in the Pakistani city of <a href=\"https://wikipedia.org/wiki/Lahore\" title=\"Lahore\">Lahore</a> resulting in at least 45 deaths and at least 100 injuries.", "links": [{"title": "2015 Lahore factory disaster", "link": "https://wikipedia.org/wiki/2015_Lahore_factory_disaster"}, {"title": "Lahore", "link": "https://wikipedia.org/wiki/Lahore"}]}, {"year": "2020", "text": "The Tigray War begins with Tigrayan rebels launching attacks on Ethiopian command centers.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/Tigray_War\" class=\"mw-redirect\" title=\"Tigray War\">Tigray War</a> begins with Tigrayan rebels <a href=\"https://wikipedia.org/wiki/Northern_Command_attacks_(Ethiopia)\" title=\"Northern Command attacks (Ethiopia)\">launching attacks on Ethiopian command centers</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tigray_War\" class=\"mw-redirect\" title=\"Tigray War\">Tigray War</a> begins with Tigrayan rebels <a href=\"https://wikipedia.org/wiki/Northern_Command_attacks_(Ethiopia)\" title=\"Northern Command attacks (Ethiopia)\">launching attacks on Ethiopian command centers</a>.", "links": [{"title": "Tigray War", "link": "https://wikipedia.org/wiki/Tigray_War"}, {"title": "Northern Command attacks (Ethiopia)", "link": "https://wikipedia.org/wiki/Northern_Command_attacks_(Ethiopia)"}]}], "Births": [{"year": "1448", "text": "<PERSON> of Naples (d. 1495)", "html": "1448 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (d. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (d. 1495)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/Alfonso_II_of_Naples"}]}, {"year": "1512", "text": "<PERSON>, Chinese general (d. 1565)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/Hu_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hu_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (d. 1565)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hu_<PERSON>n"}]}, {"year": "1553", "text": "<PERSON>, Solicitor-General for Ireland (d. 1616)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solicitor-General for Ireland (d. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solicitor-General for Ireland (d. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1575", "text": "<PERSON>, Italian painter and illustrator (d. 1642)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and illustrator (d. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and illustrator (d. 1642)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1592", "text": "<PERSON>, Dutch painter (d. 1656)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1656)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1631", "text": "<PERSON>, Princess <PERSON> and Princess of Orange (d. 1660)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Princess_of_Orange\" title=\"<PERSON>, Princess <PERSON> and Princess of Orange\"><PERSON>, Princess <PERSON> and Princess of Orange</a> (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Princess_of_Orange\" title=\"<PERSON>, Princess <PERSON> and Princess of Orange\"><PERSON>, Princess <PERSON> and Princess of Orange</a> (d. 1660)", "links": [{"title": "<PERSON>, Princess <PERSON> and Princess of Orange", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Princess_of_Orange"}]}, {"year": "1640", "text": "<PERSON>, Italian violinist and composer (d. 1697)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1697)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON>, Deputy Governor of colonial Pennsylvania (d. 1714)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Deputy Governor of colonial Pennsylvania (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Deputy Governor of colonial Pennsylvania (d. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, Elector <PERSON>, German son of <PERSON><PERSON><PERSON><PERSON> of Hesse-Darmstadt (d. 1742)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_Palatine\" class=\"mw-redirect\" title=\"<PERSON>, Elector Palatine\"><PERSON>, Elector <PERSON></a>, German son of <a href=\"https://wikipedia.org/wiki/Land<PERSON><PERSON>e_<PERSON>_<PERSON>_of_Hesse-Darmstadt\" title=\"<PERSON><PERSON><PERSON>e <PERSON> of Hesse-Darmstadt\"><PERSON><PERSON><PERSON><PERSON> of Hesse-Darmstadt</a> (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>ector_Pa<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Elector Palatine\"><PERSON>, Elector <PERSON></a>, German son of <a href=\"https://wikipedia.org/wiki/Land<PERSON><PERSON>e_<PERSON>_<PERSON>_of_Hesse-Darmstadt\" title=\"<PERSON><PERSON><PERSON><PERSON> of Hesse-Darmstadt\"><PERSON><PERSON><PERSON><PERSON> of Hesse-Darmstadt</a> (d. 1742)", "links": [{"title": "<PERSON>, Elector <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}, {"title": "Landgravine Elisabeth Amalie of Hesse-Darmstadt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_of_Hesse-Darmstadt"}]}, {"year": "1740", "text": "<PERSON>, English cleric and hymn writer (d. 1778)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Toplady\" title=\"<PERSON> Toplady\"><PERSON></a>, English cleric and hymn writer (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Toplady\" title=\"Augustus Toplady\"><PERSON></a>, English cleric and hymn writer (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus_Toplady"}]}, {"year": "1765", "text": "<PERSON><PERSON><PERSON>, French mathematician and engineer (d. 1836)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and engineer (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and engineer (d. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, British Shakespearean stage actor (d. 1833)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Shakespearean stage actor (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Shakespearean stage actor (d. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, American lawyer and jurist (d. 1874)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, American lawyer and jurist 5th Chief Justice of California (d. 1899)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist 5th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_California\" class=\"mw-redirect\" title=\"Chief Justice of California\">Chief Justice of California</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist 5th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_California\" class=\"mw-redirect\" title=\"Chief Justice of California\">Chief Justice of California</a> (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of California", "link": "https://wikipedia.org/wiki/Chief_Justice_of_California"}]}, {"year": "1821", "text": "<PERSON>, Canadian engineer and businessman (d. 1915)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and businessman (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and businessman (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, American businessman (d. 1912)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, Australian politician, 13th Premier of Tasmania (d. 1887)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1853", "text": "<PERSON>, Czech physician (d. 1924)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bayerov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech physician (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bayerov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech physician (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_Bayerov%C3%A1"}]}, {"year": "1861", "text": "<PERSON>, American journalist (d. 1929)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Go<PERSON>\"><PERSON></a>, American journalist (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alice_Gossage"}]}, {"year": "1862", "text": "<PERSON><PERSON>, Norwegian actor and director (d. 1932)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, Norwegian actor and director (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, Norwegian actor and director (d. 1932)", "links": [{"title": "<PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "1868", "text": "<PERSON>, Spanish actress, singer, and dancer (d. 1965)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"La Belle Otero\"><PERSON></a>, Spanish actress, singer, and dancer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"La Belle Otero\"><PERSON></a>, Spanish actress, singer, and dancer (d. 1965)", "links": [{"title": "La Belle Otero", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Japanese author, poet, and playwright (d. 1939)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Ky%C5%8Dka_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, poet, and playwright (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ky%C5%8Dka_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, poet, and playwright (d. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ky%C5%8Dka_<PERSON><PERSON>mi"}]}, {"year": "1874", "text": "<PERSON>, French sculptor (d. 1946)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American actor and screenwriter (d. 1935)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Greek general and politician 135th Prime Minister of Greece (d. 1953)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician 135th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician 135th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>last<PERSON>"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1884", "text": "<PERSON>, Irish engineer, invented the tractor (d. 1960)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish engineer, invented the <a href=\"https://wikipedia.org/wiki/Tractor\" title=\"Tractor\">tractor</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish engineer, invented the <a href=\"https://wikipedia.org/wiki/Tractor\" title=\"Tractor\">tractor</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tractor", "link": "https://wikipedia.org/wiki/Tractor"}]}, {"year": "1887", "text": "<PERSON>, American physicist and philanthropist (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and philanthropist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and philanthropist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, American composer and bandleader (d. 1987)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer and bandleader (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer and bandleader (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alton_Adams"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON><PERSON>, German author and poet (d. 1928)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Klabund\" title=\"Klabund\"><PERSON><PERSON><PERSON><PERSON></a>, German author and poet (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Klabund\" title=\"Klabund\"><PERSON><PERSON><PERSON><PERSON></a>, German author and poet (d. 1928)", "links": [{"title": "Klabund", "link": "https://wikipedia.org/wiki/Klabund"}]}, {"year": "1896", "text": "<PERSON>, Filipino lawyer and politician, 8th President of the Philippines (d. 1971)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1897", "text": "<PERSON>, American baseball player and umpire (d. 1968)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)\" title=\"<PERSON> (umpire)\"><PERSON></a>, American baseball player and umpire (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)\" title=\"<PERSON> (umpire)\"><PERSON></a>, American baseball player and umpire (d. 1968)", "links": [{"title": "<PERSON> (umpire)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian sociologist and activist (d. 1954)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Lucre%C8%9Biu_P%C4%83tr%C4%83%C8%99canu\" title=\"<PERSON>rețiu Pătrășcanu\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian sociologist and activist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucre%C8%9Biu_P%C4%83tr%C4%83%C8%99canu\" title=\"<PERSON>re<PERSON>iu Pătrășcanu\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian sociologist and activist (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucre%C8%9Biu_P%C4%83tr%C4%83%C8%99canu"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Greek archaeologist, author, and academic (d. 1974)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Spyrid<PERSON>_Marinatos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek archaeologist, author, and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rid<PERSON>_Marinatos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek archaeologist, author, and academic (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Spyridon_Marinatos"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Polish engineer, technician, and academic (d. 1967)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Tadeusz_%C5%BByli%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish engineer, technician, and academic (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tad<PERSON><PERSON>_%C5%BByli%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish engineer, technician, and academic (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_%C5%BByli%C5%84ski"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Croatian poet and translator (d. 2007)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian poet and translator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian poet and translator (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tad<PERSON>novi%C4%87"}]}, {"year": "1906", "text": "<PERSON>, American author and critic (d. 1974)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Sterling_North\" title=\"Sterling North\">Sterling North</a>, American author and critic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sterling_North\" title=\"Sterling North\">Sterling North</a>, American author and critic (d. 1974)", "links": [{"title": "Sterling North", "link": "https://wikipedia.org/wiki/Sterling_North"}]}, {"year": "1908", "text": "<PERSON>, American cinematographer and photographer (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer and photographer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer and photographer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Polish-English physicist and academic, Nobel Prize laureate (d. 2005)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1909", "text": "<PERSON>, American colonel and pilot (d. 2012)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American soccer player (d. 1974)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, American baseball player and manager (d. 1986)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American actress and singer (d. 1952)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Lee\" title=\"<PERSON> Lee\"><PERSON></a>, American actress and singer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lee\" title=\"<PERSON> Lee\"><PERSON></a>, American actress and singer (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Filipino painter (d. 1969)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Botong_Francisco\" title=\"Botong Francisco\"><PERSON><PERSON><PERSON> Francisco</a>, Filipino painter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Botong_Francisco\" title=\"Botong Francisco\"><PERSON><PERSON>g Francisco</a>, Filipino painter (d. 1969)", "links": [{"title": "Botong Francisco", "link": "https://wikipedia.org/wiki/Botong_Francisco"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Russian pianist and composer (d. 1978)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, New Zealand cricketer (d. 1983)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American actor (d. 1978)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Young\"><PERSON><PERSON></a>, American actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Young\"><PERSON><PERSON></a>, American actor (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Authoritarian ruler of Guatemala (d. 1957)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Authoritarian ruler of Guatemala (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Authoritarian ruler of Guatemala (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English economist and author (d. 2015)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Malaysian politician (d. 1973)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian politician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian politician (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American sergeant, Medal of Honor recipient (d. 1945)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1916", "text": "<PERSON>, American journalist, voice actor, and producer (d. 2009)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, voice actor, and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, voice actor, and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_<PERSON>ronkite"}]}, {"year": "1916", "text": "<PERSON>, American businesswoman, created <PERSON> (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, created <a href=\"https://wikipedia.org/wiki/Barbie\" title=\"Barbie\">Barbie</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, created <a href=\"https://wikipedia.org/wiki/Barbie\" title=\"Barbie\">Barbie</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Barbie", "link": "https://wikipedia.org/wiki/Barbie"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Art_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Carney\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Carney"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 1994)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1919", "text": "<PERSON>, American actor, director, and screenwriter (d. 1996)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English race car driver and businessman (d. 2015)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver and businessman (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver and businessman (d. 2015)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "1921", "text": "<PERSON>, American scientist and engineer (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and engineer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and engineer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Swiss-German actor, director, and screenwriter (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-German actor, director, and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-German actor, director, and screenwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Dutch businessman (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Canadian ice hockey player, coach, and politician (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach, and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach, and politician (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American soldier, author, and academic (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, author, and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, author, and academic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ledge"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Sri Lankan economist and diplomat (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Gamani_Corea\" title=\"Gamani Corea\"><PERSON><PERSON><PERSON></a>, Sri Lankan economist and diplomat (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gamani_Corea\" title=\"Gamani Corea\"><PERSON><PERSON><PERSON></a>, Sri Lankan economist and diplomat (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gamani_Corea"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>\" <PERSON>, Cuban-American conga player and composer (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Patato%22_<PERSON><PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, Cuban-American <a href=\"https://wikipedia.org/wiki/Conga\" title=\"Conga\">conga</a> player and composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Patato%22_<PERSON><PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, Cuban-American <a href=\"https://wikipedia.org/wiki/Conga\" title=\"Conga\">conga</a> player and composer (d. 2007)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Patato%22_Valdes"}, {"title": "Conga", "link": "https://wikipedia.org/wiki/Conga"}]}, {"year": "1928", "text": "<PERSON>, American drummer and vibraphone player (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and <a href=\"https://wikipedia.org/wiki/Vibraphone\" title=\"Vibraphone\">vibraphone</a> player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and <a href=\"https://wikipedia.org/wiki/Vibraphone\" title=\"Vibraphone\">vibraphone</a> player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}, {"title": "Vibraphone", "link": "https://wikipedia.org/wiki/Vibraphone"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Filipino businessman and chairman of the ABS-CBN Broadcasting Corporation (d. 1999)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>.</a>, Filipino businessman and chairman of the <a href=\"https://wikipedia.org/wiki/ABS-CBN_Broadcasting_Corporation\" class=\"mw-redirect\" title=\"ABS-CBN Broadcasting Corporation\">ABS-CBN Broadcasting Corporation</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>.</a>, Filipino businessman and chairman of the <a href=\"https://wikipedia.org/wiki/ABS-CBN_Broadcasting_Corporation\" class=\"mw-redirect\" title=\"ABS-CBN Broadcasting Corporation\">ABS-CBN Broadcasting Corporation</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "ABS-CBN Broadcasting Corporation", "link": "https://wikipedia.org/wiki/ABS-CBN_Broadcasting_Corporation"}]}, {"year": "1928", "text": "<PERSON>, American poet and author (d. 1997)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON> of Albania, Greek-Albanian archbishop (d. 2025)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_of_Albania\" title=\"<PERSON><PERSON><PERSON><PERSON> of Albania\"><PERSON><PERSON><PERSON><PERSON> of Albania</a>, Greek-Albanian archbishop (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_of_Albania\" title=\"<PERSON><PERSON><PERSON><PERSON> of Albania\"><PERSON><PERSON><PERSON><PERSON> of Albania</a>, Greek-Albanian archbishop (d. 2025)", "links": [{"title": "Anastasios of Albania", "link": "https://wikipedia.org/wiki/Anastasios_of_Albania"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian mathematician and astrologer (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian mathematician and astrologer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian mathematician and astrologer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Devi"}]}, {"year": "1930", "text": "<PERSON>, American painter (d. 1967)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Indian pharmacologist and academic (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian pharmacologist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian pharmacologist and academic (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American baseball player and sportscaster (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American journalist and author (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Mexican-American cardinal (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American cardinal (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American cardinal (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Austrian politician and diplomat, 10th President of Austria (d. 2004)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician and diplomat, 10th <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician and diplomat, 10th <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Austria", "link": "https://wikipedia.org/wiki/President_of_Austria"}]}, {"year": "1932", "text": "<PERSON>, Irish singer-songwriter (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Makem\"><PERSON></a>, Irish singer-songwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tommy Makem\"><PERSON></a>, Irish singer-songwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American baseball player (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Chinese physicist and engineer, Nobel Prize laureate (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Nigerian colonel and politician, President of Biafra (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Ojukwu\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_Biafra\" class=\"mw-redirect\" title=\"President of Biafra\">President of Biafra</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_O<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_Biafra\" class=\"mw-redirect\" title=\"President of Biafra\">President of Biafra</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON> Ojuk<PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON>dumegwu_O<PERSON>wu"}, {"title": "President of Biafra", "link": "https://wikipedia.org/wiki/President_of_Biafra"}]}, {"year": "1935", "text": "<PERSON>, Australian singer, actor, and television host", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer, actor, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer, actor, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, English conductor and composer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English conductor and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English conductor and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, American poet, critic, and translator (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet, critic, and translator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet, critic, and translator (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American actress and singer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON>wit\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON> Swit\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Loretta_Swit"}]}, {"year": "1937", "text": "<PERSON>, Canadian academic and politician, 31st Canadian Minister of Finance (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian academic and politician, 31st <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Canada)\" title=\"Minister of Finance (Canada)\">Canadian Minister of Finance</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian academic and politician, 31st <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Canada)\" title=\"Minister of Finance (Canada)\">Canadian Minister of Finance</a> (d. 2019)", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Canadian_politician)"}, {"title": "Minister of Finance (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Canada)"}]}, {"year": "1939", "text": "<PERSON>, American author and illustrator", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English academic and politician, Secretary of State for the Environment, Transport and the Regions (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment,_Transport_and_the_Regions\" title=\"Secretary of State for the Environment, Transport and the Regions\">Secretary of State for the Environment, Transport and the Regions</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment,_Transport_and_the_Regions\" title=\"Secretary of State for the Environment, Transport and the Regions\">Secretary of State for the Environment, Transport and the Regions</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Secretary of State for the Environment, Transport and the Regions", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment,_Transport_and_the_Regions"}]}, {"year": "1940", "text": "<PERSON>, Scottish social sciences professor (d. 2003)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish social sciences professor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish social sciences professor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, French actress, singer, and author", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Marl%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress, singer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marl%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress, singer, and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marl%C3%A8ne_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American conservationist, environmentalist, historic preservationist, author, artist, community leader", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American conservationist, environmentalist, historic preservationist, author, artist, community leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American conservationist, environmentalist, historic preservationist, author, artist, community leader", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, South African-English author and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South African-English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South African-English author and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American ophthalmologist and academic (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Patricia_<PERSON>\" title=\"Patricia Bath\"><PERSON></a>, American ophthalmologist and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patricia_Bath\" title=\"Patricia Bath\"><PERSON></a>, American ophthalmologist and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patricia_Bath"}]}, {"year": "1943", "text": "<PERSON>, American tennis player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French race car driver and skier (d. 2001)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver and skier (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver and skier (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American educator and librarian, 45th First Lady of the United States", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and librarian, 45th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and librarian, 45th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1946", "text": "<PERSON>, American cinematographer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American photographer (d. 1989)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Puerto Rican actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1947", "text": "<PERSON>, American actor, director, and production manager (d. 2003)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and production manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and production manager (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian cricketer and coach (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rod_Marsh"}]}, {"year": "1947", "text": "<PERSON>, Turkish director, producer, and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96zgent%C3%BCrk\" title=\"<PERSON>\"><PERSON></a>, Turkish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96zgent%C3%BCrk\" title=\"<PERSON>\"><PERSON></a>, Turkish director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ali_%C3%96zgent%C3%BCrk"}]}, {"year": "1947", "text": "<PERSON>, Russian figure skater", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian figure skater and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian figure skater and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, New Zealand-English painter and photographer (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English painter and photographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunter\"><PERSON></a>, New Zealand-English painter and photographer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Malian soldier and politician, President of Mali (d. 2020)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Amadou_Toumani_Tour%C3%A9\" title=\"Amadou Toumani Touré\"><PERSON><PERSON><PERSON></a>, Malian soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Mali\" class=\"mw-redirect\" title=\"President of Mali\">President of Mali</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amadou_Toumani_Tour%C3%A9\" title=\"Amadou Toumani Touré\"><PERSON><PERSON><PERSON></a>, Malian soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Mali\" class=\"mw-redirect\" title=\"President of Mali\">President of Mali</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amadou_Toumani_Tour%C3%A9"}, {"title": "President of Mali", "link": "https://wikipedia.org/wiki/President_of_Mali"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Japanese photographer and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Garo Aida\"><PERSON><PERSON> <PERSON><PERSON></a>, Japanese photographer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Garo Aida\"><PERSON><PERSON> <PERSON><PERSON></a>, Japanese photographer and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Garo_Aida"}]}, {"year": "1950", "text": "<PERSON>, American novelist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American actress (d. 2021)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ie_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English businessman, co-founded Virgin Group (d. 2019)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Virgin_Group\" title=\"Virgin Group\">Virgin Group</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Virgin_Group\" title=\"Virgin Group\">Virgin Group</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Virgin Group", "link": "https://wikipedia.org/wiki/Virgin_Group"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Romanian captain and politician, 4th President of Romania", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Traian_B%C4%83<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian captain and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trai<PERSON>_<PERSON>%C4%83<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian captain and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Traian_B%C4%83sescu"}, {"title": "President of Romania", "link": "https://wikipedia.org/wiki/President_of_Romania"}]}, {"year": "1952", "text": "<PERSON> of Alexandria", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_II_of_Alexandria\" title=\"<PERSON> <PERSON><PERSON><PERSON> of Alexandria\"><PERSON> of Alexandria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_II_of_Alexandria\" title=\"<PERSON> <PERSON><PERSON><PERSON> of Alexandria\"><PERSON> of Alexandria</a>", "links": [{"title": "<PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>wa<PERSON><PERSON>_II_of_Alexandria"}]}, {"year": "1953", "text": "<PERSON>, English footballer (d. 2013)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer (d. 2013)", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)"}]}, {"year": "1953", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player and manager (d. 2012)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player and manager (d. 2012)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Cuban-American businessman and politician, 35th United States Secretary of Commerce", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1953", "text": "<PERSON>, English animator, director, and producer, co-founded Aardman Animations", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lord\"><PERSON></a>, English animator, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/Aardman_Animations\" title=\"Aardman Animations\">Aardman Animations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lord\" title=\"<PERSON> Lord\"><PERSON></a>, English animator, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/Aardman_Animations\" title=\"Aardman Animations\">Aardman Animations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Aardman Animations", "link": "https://wikipedia.org/wiki/Aardman_Animations"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2001)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian race car driver", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver,_born_1953)\" title=\"<PERSON> (racing driver, born 1953)\"><PERSON></a>, Canadian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver,_born_1953)\" title=\"<PERSON> (racing driver, born 1953)\"><PERSON></a>, Canadian race car driver", "links": [{"title": "<PERSON> (racing driver, born 1953)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver,_born_1953)"}]}, {"year": "1954", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Indian author, poet, and scholar", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author, poet, and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author, poet, and scholar", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Finnish journalist and politician, 40th Prime Minister of Finland", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist and politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist and politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "1956", "text": "<PERSON>, Swedish singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>-<PERSON>, English guitarist and songwriter (d. 1982)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English guitarist and songwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English guitarist and songwriter (d. 1982)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American keyboard player and songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_<PERSON>s"}]}, {"year": "1957", "text": "<PERSON>, English-Australian scholar and politician, 28th Prime Minister of Australia", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian scholar and politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian scholar and politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1957", "text": "<PERSON>, English businessman and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1957", "text": "<PERSON>, Russian gymnast and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gymnast)\" title=\"<PERSON> (gymnast)\"><PERSON></a>, Russian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gymnast)\" title=\"<PERSON> (gymnast)\"><PERSON></a>, Russian gymnast and coach", "links": [{"title": "<PERSON> (gymnast)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gymnast)"}]}, {"year": "1958", "text": "<PERSON>, English activist and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American businesswoman", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian actor and stuntman", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and stuntman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and stuntman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American painter and poet (d. 2012)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American comedian and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Slovak ice hockey player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American pianist, composer, and conductor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American composer and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and academic", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American television host and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "1961", "text": "<PERSON>, English politician, Lord Mayor of Liverpool", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Liverpool\" title=\"Lord Mayor of Liverpool\">Lord Mayor of Liverpool</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Liverpool\" title=\"Lord Mayor of Liverpool\">Lord Mayor of Liverpool</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Mayor of Liverpool", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_Liverpool"}]}, {"year": "1961", "text": "<PERSON>, Northern Irish footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Estonian conductor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Arvo_<PERSON>\" title=\"Arvo <PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arvo_<PERSON>\" title=\"Arvo <PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian conductor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arvo_<PERSON>mer"}]}, {"year": "1963", "text": "<PERSON>, Canadian singer and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marc_D%C3%A9ry"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Scottish singer and television host (d. 1999)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer and television host (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer and television host (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer (d. 2016)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2014)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Static\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Static\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "links": [{"title": "Wayne <PERSON>", "link": "https://wikipedia.org/wiki/Wayne_Static"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Japanese songwriter and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish actor, director, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Y%C4%B1lmaz_Erdo%C4%9Fan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C4%B1lmaz_Erdo%C4%9Fan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C4%B1lmaz_<PERSON>rdo%C4%9Fan"}]}, {"year": "1967", "text": "<PERSON>, Austrian politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American author, critic, and educator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author, critic, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author, critic, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Puerto Rican baseball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, New Zealand cricketer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American rapper, producer, and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1970", "text": "<PERSON>, American triathlete", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Swedish soprano", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2012)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American jazz singer-songwriter and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Indian actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)\" title=\"<PERSON><PERSON> (actress)\"><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)\" title=\"<PERSON><PERSON> (actress)\"><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Portuguese footballer and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Figo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Figo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%ADs_Figo"}]}, {"year": "1973", "text": "<PERSON>, Canadian actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/%C3%89ric_<PERSON>ud\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89ric_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ric_<PERSON><PERSON>ud"}]}, {"year": "1975", "text": "<PERSON>, Russian handball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian handball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Orlando_Pace\" title=\"Orlando Pace\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Pace\" title=\"Orlando Pace\"><PERSON></a>, American football player", "links": [{"title": "Orlando Pace", "link": "https://wikipedia.org/wiki/Orlando_Pace"}]}, {"year": "1975", "text": "<PERSON>, American actress and director", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American basketball player (d. 2010)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, German banker and politician, German Federal Minister of Health", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German banker and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Germany)\" title=\"Federal Ministry of Health (Germany)\">German Federal Minister of Health</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German banker and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Germany)\" title=\"Federal Ministry of Health (Germany)\">German Federal Minister of Health</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Health (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Germany)"}]}, {"year": "1976", "text": "<PERSON>, Brazilian race car driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Dutch footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Japanese mixed martial artist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American serial killer (d. 2016)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Japanese motorcycle racer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Belgian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>bie\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Samoan-New Zealand rugby player (d. 2015)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-New Zealand rugby player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-New Zealand rugby player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player and coach", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Romanian career diplomat, political scientist, journalist, and essayist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian career diplomat, political scientist, journalist, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian career diplomat, political scientist, journalist, and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English motorcycle racer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wilfork"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Polish hammer thrower (d. 2009)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish hammer thrower (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish hammer thrower (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Russian astrophysicist and journalist (d. 2014)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astrophysicist and journalist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astrophysicist and journalist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>uf\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>uf\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>uf"}]}, {"year": "1985", "text": "<PERSON><PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Indian businessman", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Canadian actress and singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON>_Paw%C5%82owski_(footballer_born_1986)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer born 1986)\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON>_Paw%C5%82owski_(footballer_born_1986)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer born 1986)\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer born 1986)", "link": "https://wikipedia.org/wiki/Szymon_Paw%C5%82owski_(footballer_born_1986)"}]}, {"year": "1986", "text": "<PERSON>, South African race car driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Dutch footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian netball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian netball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Polish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Artur_J%C4%99dr<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>ur <PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artur_J%C4%99dr<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>ur <PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artur_J%C4%99dr<PERSON>j<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Papua New Guinean rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Papua New Guinean rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Papua New Guinean rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1988", "text": "<PERSON>, Australian rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Canadian actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Israeli race car driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Alon_Day\" title=\"Alon Day\">Alon Day</a>, Israeli race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alon_Day\" title=\"Alon Day\">Alon Day</a>, Israeli race car driver", "links": [{"title": "Alon Day", "link": "https://wikipedia.org/wiki/Alon_Day"}]}, {"year": "1991", "text": "<PERSON>, Dutch tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Belarusian ice dancer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>v\"><PERSON><PERSON></a>, Belarusian ice dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>v"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>da"}]}, {"year": "1992", "text": "<PERSON>, German footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ie%C3%9Fmeier\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ie%C3%9Fmeier\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ie%C3%9Fmeier"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, American middle-distance runner", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Ce%27A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American middle-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ce%27A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American middle-distance runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ce%27A<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, German gymnast", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian cricketer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American ice dancer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American ice dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Filipino figure skater", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rugby_league)"}]}, {"year": "1996", "text": "<PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Filipina television actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipina television actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipina television actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ene"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Chinese table tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Sun_Yingsha\" title=\"Sun Yingsha\"><PERSON></a>, Chinese table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sun_Yingsha\" title=\"Sun Yingsha\"><PERSON></a>, Chinese table tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sun_Yingsha"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Russian-German rhythmic gymnast", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arfolomeev\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-German rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arfolomee<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-German rhythmic gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dar<PERSON>_<PERSON>arfolomeev"}]}], "Deaths": [{"year": "604", "text": "<PERSON><PERSON>, Mayan queen", "html": "604 - <a href=\"https://wikipedia.org/wiki/Yohl_Ik%27nal\" class=\"mw-redirect\" title=\"Yohl Ik'nal\"><PERSON><PERSON>'nal</a>, Mayan queen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yohl_Ik%27nal\" class=\"mw-redirect\" title=\"Yohl Ik'nal\"><PERSON><PERSON>'nal</a>, Mayan queen", "links": [{"title": "<PERSON><PERSON>l", "link": "https://wikipedia.org/wiki/Yohl_Ik%27nal"}]}, {"year": "915", "text": "<PERSON>, Chinese empress (b. 892)", "html": "915 - <a href=\"https://wikipedia.org/wiki/Consort_<PERSON>_(<PERSON>)\" title=\"Consort <PERSON> (<PERSON>)\"><PERSON></a>, Chinese empress (b. 892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Consort_<PERSON>_(<PERSON>)\" title=\"Consort <PERSON> (<PERSON>)\"><PERSON></a>, Chinese empress (b. 892)", "links": [{"title": "<PERSON> (<PERSON>)", "link": "https://wikipedia.org/wiki/Consort_<PERSON>_(<PERSON>)"}]}, {"year": "1038", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, duke of Bohemia (b. 970)", "html": "1038 - <a href=\"https://wikipedia.org/wiki/<PERSON>arom%C3%<PERSON>r,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, duke of Bohemia (b. 970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>arom%C3%ADr,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, duke of Bohemia (b. 970)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/Jarom%C3%ADr,_<PERSON>_of_Bohemia"}]}, {"year": "1203", "text": "<PERSON>, Count of Holland", "html": "1203 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON>, Count of Holland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON>, Count of Holland</a>", "links": [{"title": "<PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland"}]}, {"year": "1212", "text": "<PERSON> of Valois, French saint (b. 1127)", "html": "1212 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, French saint (b. 1127)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, French saint (b. 1127)", "links": [{"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1360", "text": "<PERSON>, English noblewoman (b. 1295)", "html": "1360 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1295)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1295)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1411", "text": "<PERSON><PERSON><PERSON> of Timurid (b. 1384)", "html": "1411 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sultan\"><PERSON><PERSON><PERSON></a> of Timurid (b. 1384)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of Timurid (b. 1384)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1428", "text": "<PERSON> of Bavaria, queen of Bohemia (b. 1376)", "html": "1428 - <a href=\"https://wikipedia.org/wiki/Sophia_of_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, queen of Bohemia (b. 1376)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sophia_of_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, queen of Bohemia (b. 1376)", "links": [{"title": "Sophia of Bavaria", "link": "https://wikipedia.org/wiki/Sophia_of_Bavaria"}]}, {"year": "1485", "text": "<PERSON><PERSON>, duchess of Brittany (b. 1427)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_d%27Amboise\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, duchess of Brittany (b. 1427)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_d%27Amboise\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, duchess of Brittany (b. 1427)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise_d%27Amboise"}]}, {"year": "1485", "text": "<PERSON>, <PERSON><PERSON> of Venice (b. 1408)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> of Venice (b. 1408)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> of Venice (b. 1408)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1576", "text": "<PERSON>, 2nd Marquess of Winchester (b. c. 1510)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Winchester\" title=\"<PERSON>, 2nd Marquess of Winchester\"><PERSON>, 2nd Marquess of Winchester</a> (b. c. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Winchester\" title=\"<PERSON>, 2nd Marquess of Winchester\"><PERSON>, 2nd Marquess of Winchester</a> (b. c. 1510)", "links": [{"title": "<PERSON>, 2nd Marquess of Winchester", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Winchester"}]}, {"year": "1581", "text": "<PERSON><PERSON><PERSON>, rival Grandmaster of the Knights Hospitaller (b. c.1525)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/Romegas\" class=\"mw-redirect\" title=\"Romegas\"><PERSON><PERSON><PERSON></a>, rival Grandmaster of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a> (b. c.1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Romegas\" class=\"mw-redirect\" title=\"Romegas\"><PERSON><PERSON><PERSON></a>, rival Grandmaster of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a> (b. c.1525)", "links": [{"title": "Romegas", "link": "https://wikipedia.org/wiki/Romegas"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}]}, {"year": "1652", "text": "<PERSON><PERSON><PERSON>, Flemish priest and mathematician (b. 1597)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish priest and mathematician (b. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish priest and mathematician (b. 1597)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1658", "text": "<PERSON>, French lawyer and author (b. 1608)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and author (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and author (b. 1608)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1669", "text": "<PERSON>, Dutch theologian and academic (b. 1603)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian and academic (b. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian and academic (b. 1603)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1698", "text": "<PERSON><PERSON>, Danish physician and mathematician (b. 1625)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish physician and mathematician (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish physician and mathematician (b. 1625)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON>, English admiral (b. 1653)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (b. 1653)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, German orientalist and scholar (b. 1654)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German orientalist and scholar (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German orientalist and scholar (b. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andreas_<PERSON>lut<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, German poet and songwriter (b. 1721)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6tz\" title=\"<PERSON>\"><PERSON></a>, German poet and songwriter (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6tz\" title=\"<PERSON>\"><PERSON></a>, German poet and songwriter (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6tz"}]}, {"year": "1801", "text": "<PERSON>, American physician and anatomist (b. 1712)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and anatomist (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and anatomist (b. 1712)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1847", "text": "<PERSON>, German pianist, composer, and conductor (b. 1809)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese emperor (b. 1807)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Thi%E1%BB%87u_Tr%E1%BB%8B\" title=\"Thiệu Trị\"><PERSON><PERSON><PERSON><PERSON>rị</a>, Vietnamese emperor (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thi%E1%BB%87u_Tr%E1%BB%8B\" title=\"Thiệu Trị\"><PERSON><PERSON><PERSON><PERSON>rị</a>, Vietnamese emperor (b. 1807)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thi%E1%BB%87u_Tr%E1%BB%8B"}]}, {"year": "1856", "text": "<PERSON>, French painter and educator (b. 1797)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and educator (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and educator (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Irish-Australian politician, 6th Premier of New South Wales (b. 1820)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(premier)\" title=\"<PERSON> (premier)\"><PERSON></a>, Irish-Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(premier)\" title=\"<PERSON> (premier)\"><PERSON></a>, Irish-Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1820)", "links": [{"title": "<PERSON> (premier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(premier)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1893", "text": "<PERSON>, Swiss-French engineer and politician, 54th Prime Minister of France (b. 1827)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French engineer and politician, 54th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French engineer and politician, 54th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1895", "text": "<PERSON>, American journalist, author, and poet (b. 1850)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Eugene Field\"><PERSON></a>, American journalist, author, and poet (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eugene_Field\" title=\"Eugene Field\"><PERSON></a>, American journalist, author, and poet (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_Field"}]}, {"year": "1906", "text": "<PERSON>, American general and politician (b. 1832)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, English lieutenant and poet (b. 1893)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, English lieutenant and poet (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a>, English lieutenant and poet (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Japanese politician, 10th Prime Minister of Japan (b. 1856)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hi"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1924", "text": "<PERSON>, American sergeant, Medal of Honor recipient (b. 1843)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1924", "text": "<PERSON>, French pianist, composer, and educator (b. 1845)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and educator (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and educator (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>au<PERSON>%C3%A9"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 1859)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>furu\" title=\"<PERSON><PERSON><PERSON> Yoshifuru\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>fu<PERSON>\" title=\"<PERSON><PERSON><PERSON> Yoshifuru\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yoshifuru"}]}, {"year": "1931", "text": "<PERSON>, American cornet player and bandleader (b. 1877)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cornet player and bandleader (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cornet player and bandleader (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Italian theorist and activist (b. 1861)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian theorist and activist (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian theorist and activist (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English captain (b. 1869)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, German general (b. 1865)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German general (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German general (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, 1st Baron <PERSON>, English businessman and politician, Secretary of State for Business, Innovation and Skills (b. 1874)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a> (b. 1874)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Secretary of State for Business, Innovation and Skills", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills"}]}, {"year": "1950", "text": "Grove<PERSON>, American baseball player and coach (b. 1887)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Grove<PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Grover <PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (b. 1887)", "links": [{"title": "Grover <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>r_Cleveland_Alexander"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Swedish journalist and writer (b. 1923)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist and writer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ger<PERSON>\"><PERSON><PERSON></a>, Swedish journalist and writer (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American playwright and screenwriter (b. 1896)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and manager (b. 1867)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Guardian of the Baháʼí Faith (b. 1897)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Shoghi_Effendi\" title=\"Shoghi Effendi\">Shoghi Effendi</a>, <a href=\"https://wikipedia.org/wiki/Guardian_(Bah%C3%A1%CA%BC%C3%AD_Faith)\" class=\"mw-redirect\" title=\"Guardian (Baháʼí Faith)\">Guardian</a> of the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shoghi_Effendi\" title=\"Shoghi Effendi\">Shoghi Effendi</a>, <a href=\"https://wikipedia.org/wiki/Guardian_(Bah%C3%A1%CA%BC%C3%AD_Faith)\" class=\"mw-redirect\" title=\"Guardian (Baháʼí Faith)\">Guardian</a> of the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a> (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shog<PERSON>_Effendi"}, {"title": "Guardian (Baháʼí Faith)", "link": "https://wikipedia.org/wiki/Guardian_(Bah%C3%A1%CA%BC%C3%AD_Faith)"}, {"title": "Baháʼí Faith", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith"}]}, {"year": "1956", "text": "<PERSON>, English motorcycle racer and race car driver (b. 1892)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer and race car driver (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer and race car driver (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Austrian mathematician, physicist, and philosopher from the Vienna Circle (b. 1896)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician, physicist, and philosopher from the Vienna Circle (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician, physicist, and philosopher from the Vienna Circle (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English race car driver (b. 1918)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Belarusian-French painter and soldier (b. 1892)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-French painter and soldier (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-French painter and soldier (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Brazilian author and activist (b. 1911)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian author and activist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian author and activist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American soccer player (b. 1909)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Czech priest and academic (b. 1893)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech priest and academic (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech priest and academic (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Syrian journalist, historian, and academic (b. 1914)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian journalist, historian, and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian journalist, historian, and academic (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1976", "text": "<PERSON>, German race car driver and motorcycle racer (b. 1906)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver and motorcycle racer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver and motorcycle racer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American author and illustrator (b. 1935)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian-American engineer and author (b. 1905)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer and author (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer and journalist (b. 1889)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and journalist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and journalist (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American journalist (b. 1940)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Turkish poet and author (b. 1926)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Ya%C5%9Far_O%C4%9Fuzcan\" title=\"Ümit Yaşar <PERSON>uz<PERSON>\"><PERSON><PERSON></a>, Turkish poet and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Ya%C5%9Far_O%C4%9Fuzcan\" title=\"Ümit Yaşar <PERSON>\"><PERSON><PERSON></a>, Turkish poet and author (b. 1926)", "links": [{"title": "Ümit <PERSON>", "link": "https://wikipedia.org/wiki/%C3%9Cmit_Ya%C5%9Far_O%C4%9Fuzcan"}]}, {"year": "1986", "text": "<PERSON>, German-English mathematician and academic (b. 1906)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English mathematician and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English mathematician and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer and manager (b. 1916)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer and manager (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer and manager (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_Vikelidis"}]}, {"year": "1992", "text": "<PERSON>, Canadian engineer, invented the motorized wheelchair (b. 1904)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, Canadian engineer, invented the <a href=\"https://wikipedia.org/wiki/Motorized_wheelchair\" title=\"Motorized wheelchair\">motorized wheelchair</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, Canadian engineer, invented the <a href=\"https://wikipedia.org/wiki/Motorized_wheelchair\" title=\"Motorized wheelchair\">motorized wheelchair</a> (b. 1904)", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>(inventor)"}, {"title": "Motorized wheelchair", "link": "https://wikipedia.org/wiki/Motorized_wheelchair"}]}, {"year": "1994", "text": "<PERSON>, American soldier and painter (b. 1923)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, French philosopher and scholar (b. 1925)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and scholar (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and scholar (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English actor (b. 1927)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Israeli general and politician, 5th Prime Minister of Israel, Nobel Peace Prize laureate (b. 1922)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli general and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a> laureate (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli general and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a> laureate (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American sociologist, author, and academic (b. 1916)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sociologist, author, and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sociologist, author, and academic (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American novelist (b. 1924)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist (b. 1924)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1999", "text": "<PERSON>, Barbadian cricketer and coach (b. 1958)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Cornish author and poet (b. 1917)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cornish author and poet (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cornish author and poet (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English philosopher, author, and academic (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, author, and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, author, and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Afghan journalist and poet (b. 1980)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan journalist and poet (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan journalist and poet (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American actress and dancer (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Sheree_North\" title=\"Sheree North\"><PERSON><PERSON></a>, American actress and dancer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheree_North\" title=\"Sheree North\"><PERSON><PERSON></a>, American actress and dancer (b. 1932)", "links": [{"title": "Sheree North", "link": "https://wikipedia.org/wiki/Sheree_North"}]}, {"year": "2005", "text": "<PERSON>, South African-born English actor and singer (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born English actor and singer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born English actor and singer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Japanese singer-songwriter and guitarist (b. 1964)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Canadian lawyer and politician (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American author (b. 1908)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Estonian physicist and academic (b. 1926)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, German-American author and screenwriter (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author and screenwriter (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American physician, author, director, producer, and screenwriter (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, author, director, producer, and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, author, director, producer, and screenwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American ballerina (b. 1920)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>tower\"><PERSON><PERSON></a>, American ballerina (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>tower\"><PERSON><PERSON></a>, American ballerina (b. 1920)", "links": [{"title": "<PERSON><PERSON> Hightower", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hightower"}]}, {"year": "2008", "text": "<PERSON>, French-Mexican economist and politician, Mexican Secretary of the Interior (b. 1971)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, French-Mexican economist and politician, <a href=\"https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)\" class=\"mw-redirect\" title=\"Secretariat of the Interior (Mexico)\">Mexican Secretary of the Interior</a> (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, French-Mexican economist and politician, <a href=\"https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)\" class=\"mw-redirect\" title=\"Secretariat of the Interior (Mexico)\">Mexican Secretary of the Interior</a> (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%C3%B1o"}, {"title": "Secretariat of the Interior (Mexico)", "link": "https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)"}]}, {"year": "2009", "text": "<PERSON><PERSON>, German bishop (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German bishop (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German bishop (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1934)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Latvian-Estonian soldier and politician (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Latvian-Estonian soldier and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Latvian-Estonian soldier and politician (b. 1920)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "2011", "text": "<PERSON>, American author, critic, journalist, and television personality (b. 1919)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, critic, journalist, and television personality (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, critic, journalist, and television personality (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Brazilian-Israeli architect, designed <PERSON><PERSON> (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Israeli architect, designed <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">Ya<PERSON></a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Israeli architect, designed <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">Ya<PERSON></a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yad_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American sergeant, Medal of Honor recipient (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Russian-Estonian philosopher and academic (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Estonian philosopher and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Estonian philosopher and academic (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian-American football player and coach (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player and coach (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player and coach (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Argentinian lawyer and politician, 2nd Chief of Government of the City of Buenos Aires (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_of_Government_of_the_City_of_Buenos_Aires\" class=\"mw-redirect\" title=\"Chief of Government of the City of Buenos Aires\">Chief of Government of the City of Buenos Aires</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_of_Government_of_the_City_of_Buenos_Aires\" class=\"mw-redirect\" title=\"Chief of Government of the City of Buenos Aires\">Chief of Government of the City of Buenos Aires</a> (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief of Government of the City of Buenos Aires", "link": "https://wikipedia.org/wiki/Chief_of_Government_of_the_City_of_Buenos_Aires"}]}, {"year": "2014", "text": "<PERSON>, American author and academic (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American physicist and chemist, invented <PERSON><PERSON><PERSON><PERSON><PERSON> (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and chemist, invented <a href=\"https://wikipedia.org/wiki/CorningWare\" title=\"CorningWare\">CorningWare</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American physicist and chemist, invented <a href=\"https://wikipedia.org/wiki/CorningWare\" title=\"CorningWare\">CorningWare</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "CorningWare", "link": "https://wikipedia.org/wiki/CorningWare"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Polish-American historian and journalist (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American historian and journalist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American historian and journalist (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>otr_Domaradzki"}]}, {"year": "2015", "text": "<PERSON>, French-American historian, philosopher, and critic (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_G<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American historian, philosopher, and critic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American historian, philosopher, and critic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>rd"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian-Hungarian cellist, flute player, and composer (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Horv%C3%A1th\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian-Hungarian cellist, flute player, and composer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Horv%C3%A1th\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian-Hungarian cellist, flute player, and composer (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A1roly_Horv%C3%A1th"}]}, {"year": "2015", "text": "<PERSON>, American lawyer and politician (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (b. 1943)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "2016", "text": "<PERSON>, first female Papua New Guinean judge (b. 1960)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first female <a href=\"https://wikipedia.org/wiki/Papua_New_Guinea\" title=\"Papua New Guinea\">Papua New Guinean</a> judge (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first female <a href=\"https://wikipedia.org/wiki/Papua_New_Guinea\" title=\"Papua New Guinea\">Papua New Guinean</a> judge (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Papua New Guinea", "link": "https://wikipedia.org/wiki/Papua_New_Guinea"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Iranian football player and coach (b. 1946)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pourheidari\" title=\"<PERSON>our Pourheidari\"><PERSON><PERSON></a>, Iranian football player and coach (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pourheidari\" title=\"<PERSON>our Pourheidari\"><PERSON><PERSON></a>, Iranian football player and coach (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>our_Pourheidari"}]}, {"year": "2017", "text": "<PERSON>, Filipino-Spanish actress and singer (b. 1976)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Isabel_Granada\" title=\"Isabel Granada\"><PERSON></a>, Filipino-Spanish actress and singer (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabel_Granada\" title=\"Isabel Granada\"><PERSON></a>, Filipino-Spanish actress and singer (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Isabel_Granada"}]}, {"year": "2017", "text": "<PERSON>, American actor and opera singer (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and opera singer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and opera singer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Irish broadcaster (b. 1934)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish broadcaster (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish broadcaster (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English rock singer-songwriter and musician (b. 1945)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock singer-songwriter and musician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock singer-songwriter and musician (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Iranian vocalist (b. 1934)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian vocalist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian vocalist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "2024", "text": "<PERSON>, American billionaire businessman and philanthropist (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American billionaire businessman and philanthropist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American billionaire businessman and philanthropist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian lawyer and politician (b. 1951)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}