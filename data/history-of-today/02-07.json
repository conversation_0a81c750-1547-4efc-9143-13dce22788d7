{"date": "February 7", "url": "https://wikipedia.org/wiki/February_7", "data": {"Events": [{"year": "457", "text": "<PERSON> becomes the Eastern Roman emperor.", "html": "457 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> becomes the Eastern Roman emperor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON> I</a> becomes the Eastern Roman emperor.", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)"}]}, {"year": "987", "text": "<PERSON><PERSON> the Younger and <PERSON><PERSON>, Byzantine generals of the military elite, begin a wide-scale rebellion against Emperor <PERSON>.", "html": "987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON><PERSON> the Younger</a> and <a href=\"https://wikipedia.org/wiki/Bardas_Skleros\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Byzantine generals of the military elite, begin a <a href=\"https://wikipedia.org/wiki/Rebellion_of_<PERSON><PERSON>_<PERSON><PERSON>_the_Younger\" title=\"Rebellion of <PERSON><PERSON>okas the Younger\">wide-scale rebellion</a> against Emperor <a href=\"https://wikipedia.org/wiki/Basil_II\" title=\"Basil II\">Basil II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON><PERSON> the Younger</a> and <a href=\"https://wikipedia.org/wiki/Bar<PERSON>_Skleros\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Byzantine generals of the military elite, begin a <a href=\"https://wikipedia.org/wiki/Rebellion_of_<PERSON><PERSON>_<PERSON><PERSON>_the_Younger\" title=\"Rebellion of <PERSON><PERSON> the Younger\">wide-scale rebellion</a> against Emperor <a href=\"https://wikipedia.org/wiki/Basil_II\" title=\"Basil II\"><PERSON> II</a>.", "links": [{"title": "<PERSON><PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_the_Younger"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bardas_Skleros"}, {"title": "Rebellion of <PERSON><PERSON> Ph<PERSON>s the Younger", "link": "https://wikipedia.org/wiki/Rebellion_of_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_the_Younger"}, {"title": "Basil II", "link": "https://wikipedia.org/wiki/Basil_II"}]}, {"year": "1301", "text": "<PERSON> of Caernarvon (later King <PERSON> of England) becomes the first English Prince of Wales.", "html": "1301 - <PERSON> of <a href=\"https://wikipedia.org/wiki/Caernarfon\" title=\"Caernarfon\">Caernar<PERSON></a> (later King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>) becomes the first English <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a>.", "no_year_html": "<PERSON> of <a href=\"https://wikipedia.org/wiki/Caernarfon\" title=\"Caernarfon\">C<PERSON><PERSON><PERSON><PERSON></a> (later King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>) becomes the first English <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a>.", "links": [{"title": "C<PERSON>rna<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Caernarfon"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Prince of Wales", "link": "https://wikipedia.org/wiki/Prince_of_Wales"}]}, {"year": "1313", "text": "King <PERSON><PERSON><PERSON><PERSON> founds the Pinya Kingdom as the de jure successor state of the Pagan Kingdom.", "html": "1313 - King <a href=\"https://wikipedia.org/wiki/Thihathu\" title=\"Thihat<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/Pinya_Kingdom\" title=\"Pinya Kingdom\">Pinya Kingdom</a> as the de jure successor state of the <a href=\"https://wikipedia.org/wiki/Pagan_Kingdom\" class=\"mw-redirect\" title=\"Pagan Kingdom\">Pagan Kingdom</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Thihathu\" title=\"<PERSON>hihat<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/Pinya_Kingdom\" title=\"Pinya Kingdom\">Pinya Kingdom</a> as the de jure successor state of the <a href=\"https://wikipedia.org/wiki/Pagan_Kingdom\" class=\"mw-redirect\" title=\"Pagan Kingdom\">Pagan Kingdom</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thihathu"}, {"title": "Pinya Kingdom", "link": "https://wikipedia.org/wiki/Pinya_Kingdom"}, {"title": "Pagan Kingdom", "link": "https://wikipedia.org/wiki/Pagan_Kingdom"}]}, {"year": "1365", "text": "<PERSON> of Mecklenburg (King <PERSON> of Sweden) grants city rights to Ulvila (Swedish: Ulvsby).", "html": "1365 - <PERSON> III of <a href=\"https://wikipedia.org/wiki/House_of_Mecklenburg\" title=\"House of Mecklenburg\">Mecklenburg</a> (King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Sweden\" title=\"<PERSON>, King of Sweden\"><PERSON> of Sweden</a>) grants city rights to <a href=\"https://wikipedia.org/wiki/Ulvila\" title=\"Ulvila\">Ulvila</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\">Ulvsby</i>).", "no_year_html": "<PERSON> of <a href=\"https://wikipedia.org/wiki/House_of_Mecklenburg\" title=\"House of Mecklenburg\">Mecklenburg</a> (King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Sweden\" title=\"<PERSON>, King of Sweden\"><PERSON> of Sweden</a>) grants city rights to <a href=\"https://wikipedia.org/wiki/Ulvila\" title=\"Ulvila\">Ulvila</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\">Ulvsby</i>).", "links": [{"title": "House of Mecklenburg", "link": "https://wikipedia.org/wiki/House_of_Mecklenburg"}, {"title": "<PERSON>, King of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Sweden"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Swedish language", "link": "https://wikipedia.org/wiki/Swedish_language"}]}, {"year": "1497", "text": "In Florence, Italy, supporters of <PERSON><PERSON><PERSON> burn cosmetics, art, and books, in a \"Bonfire of the vanities\".", "html": "1497 - In <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>, Italy, supporters of <a href=\"https://wikipedia.org/wiki/Girolamo_Savonarola\" title=\"Girolamo Savonarola\"><PERSON><PERSON><PERSON></a> burn <a href=\"https://wikipedia.org/wiki/Cosmetics\" title=\"Cosmetics\">cosmetics</a>, art, and books, in a \"<a href=\"https://wikipedia.org/wiki/Bonfire_of_the_vanities\" title=\"Bonfire of the vanities\">Bonfire of the vanities</a>\".", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>, Italy, supporters of <a href=\"https://wikipedia.org/wiki/Girolamo_Savonarola\" title=\"Girolamo Savonarola\"><PERSON><PERSON><PERSON></a> burn <a href=\"https://wikipedia.org/wiki/Cosmetics\" title=\"Cosmetics\">cosmetics</a>, art, and books, in a \"<a href=\"https://wikipedia.org/wiki/Bonfire_of_the_vanities\" title=\"Bonfire of the vanities\">Bonfire of the vanities</a>\".", "links": [{"title": "Florence", "link": "https://wikipedia.org/wiki/Florence"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Savonarola"}, {"title": "Cosmetics", "link": "https://wikipedia.org/wiki/Cosmetics"}, {"title": "Bonfire of the vanities", "link": "https://wikipedia.org/wiki/Bonfire_of_the_vanities"}]}, {"year": "1756", "text": "Guaraní War: The leader of the Guaraní rebels, <PERSON><PERSON>, is killed in a skirmish with Spanish and Portuguese troops.", "html": "1756 - <a href=\"https://wikipedia.org/wiki/Guaran%C3%AD_War\" title=\"Guaraní War\">Guaraní War</a>: The leader of the <a href=\"https://wikipedia.org/wiki/Guaran%C3%AD_people\" title=\"Guaraní people\">Guaraní</a> rebels, <a href=\"https://wikipedia.org/wiki/Sep%C3%A9_Tiaraju\" title=\"Sepé T<PERSON>ju\"><PERSON><PERSON></a>, is killed in a skirmish with Spanish and Portuguese troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guaran%C3%AD_War\" title=\"Guaraní War\">Guaraní War</a>: The leader of the <a href=\"https://wikipedia.org/wiki/Guaran%C3%AD_people\" title=\"Guaraní people\">Guaraní</a> rebels, <a href=\"https://wikipedia.org/wiki/Sep%C3%A9_Tiaraju\" title=\"Sepé <PERSON>ju\"><PERSON><PERSON></a>, is killed in a skirmish with Spanish and Portuguese troops.", "links": [{"title": "Guaraní War", "link": "https://wikipedia.org/wiki/Guaran%C3%AD_War"}, {"title": "Guaraní people", "link": "https://wikipedia.org/wiki/Guaran%C3%AD_people"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sep%C3%A9_Tiaraju"}]}, {"year": "1783", "text": "American Revolutionary War: French and Spanish forces lift the Great Siege of Gibraltar.", "html": "1783 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: French and Spanish forces lift the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Gibraltar\" title=\"Great Siege of Gibraltar\">Great Siege of Gibraltar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: French and Spanish forces lift the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Gibraltar\" title=\"Great Siege of Gibraltar\">Great Siege of Gibraltar</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Great Siege of Gibraltar", "link": "https://wikipedia.org/wiki/Great_Siege_of_Gibraltar"}]}, {"year": "1795", "text": "The 11th Amendment to the United States Constitution is ratified.", "html": "1795 - The <a href=\"https://wikipedia.org/wiki/Eleventh_Amendment_to_the_United_States_Constitution\" title=\"Eleventh Amendment to the United States Constitution\">11th Amendment</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> is <a href=\"https://wikipedia.org/wiki/Ratification\" title=\"Ratification\">ratified</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Eleventh_Amendment_to_the_United_States_Constitution\" title=\"Eleventh Amendment to the United States Constitution\">11th Amendment</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> is <a href=\"https://wikipedia.org/wiki/Ratification\" title=\"Ratification\">ratified</a>.", "links": [{"title": "Eleventh Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Eleventh_Amendment_to_the_United_States_Constitution"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}, {"title": "Ratification", "link": "https://wikipedia.org/wiki/Ratification"}]}, {"year": "1807", "text": "Napoleonic Wars: <PERSON> finds <PERSON><PERSON><PERSON><PERSON>'s Russian forces taking a stand at Eylau. After bitter fighting, the French take the town, but the Russians resume the battle the next day.", "html": "1807 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <PERSON> finds <a href=\"https://wikipedia.org/wiki/Levin_August,_Count_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> August, <PERSON>\"><PERSON><PERSON><PERSON><PERSON>'s</a> Russian forces taking a stand at <a href=\"https://wikipedia.org/wiki/Battle_of_Eylau\" title=\"Battle of Eylau\">Eylau</a>. After bitter fighting, the French take the town, but the Russians resume the battle the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <PERSON> finds <a href=\"https://wikipedia.org/wiki/Levin_August,_Count_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> August, Count <PERSON>\"><PERSON><PERSON><PERSON><PERSON>'s</a> Russian forces taking a stand at <a href=\"https://wikipedia.org/wiki/Battle_of_Eylau\" title=\"Battle of Eylau\">Eylau</a>. After bitter fighting, the French take the town, but the Russians resume the battle the next day.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "<PERSON> August, <PERSON> von <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August,_Count_<PERSON>_<PERSON>"}, {"title": "Battle of Eylau", "link": "https://wikipedia.org/wiki/Battle_of_Eylau"}]}, {"year": "1812", "text": "The strongest in a series of earthquakes strikes New Madrid, Missouri.", "html": "1812 - The strongest in <a href=\"https://wikipedia.org/wiki/1811%E2%80%9312_New_Madrid_earthquakes\" class=\"mw-redirect\" title=\"1811-12 New Madrid earthquakes\">a series of earthquakes</a> strikes <a href=\"https://wikipedia.org/wiki/New_Madrid,_Missouri\" title=\"New Madrid, Missouri\">New Madrid, Missouri</a>.", "no_year_html": "The strongest in <a href=\"https://wikipedia.org/wiki/1811%E2%80%9312_New_Madrid_earthquakes\" class=\"mw-redirect\" title=\"1811-12 New Madrid earthquakes\">a series of earthquakes</a> strikes <a href=\"https://wikipedia.org/wiki/New_Madrid,_Missouri\" title=\"New Madrid, Missouri\">New Madrid, Missouri</a>.", "links": [{"title": "1811-12 New Madrid earthquakes", "link": "https://wikipedia.org/wiki/1811%E2%80%9312_New_Madrid_earthquakes"}, {"title": "New Madrid, Missouri", "link": "https://wikipedia.org/wiki/New_Madrid,_Missouri"}]}, {"year": "1813", "text": "In the action of 7 February 1813 near the Îles de Los, the frigates <PERSON><PERSON><PERSON><PERSON> and <PERSON> batter each other, but neither can gain the upper hand.", "html": "1813 - In the <a href=\"https://wikipedia.org/wiki/Action_of_7_February_1813\" title=\"Action of 7 February 1813\">action of 7 February 1813</a> near the <a href=\"https://wikipedia.org/wiki/%C3%8Eles_de_Los\" title=\"Îles de Los\">Îles de Los</a>, the <a href=\"https://wikipedia.org/wiki/Frigate\" title=\"Frigate\">frigates</a> <i><a href=\"https://wikipedia.org/wiki/French_frigate_Ar%C3%A9thuse_(1812)\" title=\"French frigate Aréthuse (1812)\"><PERSON><PERSON><PERSON><PERSON></a></i> and <a href=\"https://wikipedia.org/wiki/HMS_Amelia_(1796)\" title=\"HMS Amelia (1796)\"><i>Amelia</i></a> batter each other, but neither can gain the upper hand.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Action_of_7_February_1813\" title=\"Action of 7 February 1813\">action of 7 February 1813</a> near the <a href=\"https://wikipedia.org/wiki/%C3%8Eles_de_Los\" title=\"Îles de Los\">Îles de Los</a>, the <a href=\"https://wikipedia.org/wiki/Frigate\" title=\"Frigate\">frigates</a> <i><a href=\"https://wikipedia.org/wiki/French_frigate_Ar%C3%A9thuse_(1812)\" title=\"French frigate Aréthuse (1812)\"><PERSON><PERSON>thuse</a></i> and <a href=\"https://wikipedia.org/wiki/HMS_Amelia_(1796)\" title=\"HMS Amelia (1796)\"><i>Amelia</i></a> batter each other, but neither can gain the upper hand.", "links": [{"title": "Action of 7 February 1813", "link": "https://wikipedia.org/wiki/Action_of_7_February_1813"}, {"title": "Îles de Los", "link": "https://wikipedia.org/wiki/%C3%8Eles_de_Los"}, {"title": "Frigate", "link": "https://wikipedia.org/wiki/Frigate"}, {"title": "French frigate Aréthuse (1812)", "link": "https://wikipedia.org/wiki/French_frigate_Ar%C3%A9thuse_(1812)"}, {"title": "HMS Amelia (1796)", "link": "https://wikipedia.org/wiki/HMS_Amelia_(1796)"}]}, {"year": "1819", "text": "Sir <PERSON> leaves Singapore after just taking it over, leaving it in the hands of <PERSON>.", "html": "1819 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Raffles\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> after just taking it over, leaving it in the hands of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Raffles\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> after just taking it over, leaving it in the hands of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ffles"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1842", "text": "Battle of Debre Tabor: <PERSON><PERSON> <PERSON>, Regent of the Emperor of Ethiopia defeats warlord <PERSON><PERSON> of Semien.", "html": "1842 - <a href=\"https://wikipedia.org/wiki/Battle_of_Debre_Tabor\" title=\"Battle of Debre Tabor\">Battle of Debre Tabor</a>: <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Yejju\" title=\"<PERSON> II of Yejju\"><PERSON></a>, Regent of the <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a> defeats warlord <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Haile_<PERSON>\" title=\"<PERSON><PERSON> Haile Mary<PERSON>\"><PERSON><PERSON></a> of Semien.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Debre_Tabor\" title=\"Battle of Debre Tabor\">Battle of Debre Tabor</a>: <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Yejju\" title=\"<PERSON> II of Yejju\"><PERSON></a>, Regent of the <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a> defeats warlord <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>le_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of Semien.", "links": [{"title": "Battle of Debre Tabor", "link": "https://wikipedia.org/wiki/Battle_of_Debre_Tabor"}, {"title": "<PERSON> of Yejju", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yejju"}, {"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "A law is approved to found the Swiss Federal Institute of Technology. Lectures started October 16, 1855.", "html": "1854 - A law is approved to found the <a href=\"https://wikipedia.org/wiki/ETH_Zurich\" title=\"ETH Zurich\">Swiss Federal Institute of Technology</a>. Lectures started October 16, 1855.", "no_year_html": "A law is approved to found the <a href=\"https://wikipedia.org/wiki/ETH_Zurich\" title=\"ETH Zurich\">Swiss Federal Institute of Technology</a>. Lectures started October 16, 1855.", "links": [{"title": "ETH Zurich", "link": "https://wikipedia.org/wiki/ETH_Zurich"}]}, {"year": "1863", "text": "HMS Orpheus sinks off the coast of Auckland, New Zealand, killing 189.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/HMS_Orpheus_(1860)\" title=\"HMS Orpheus (1860)\">HMS <i>Orpheus</i></a> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Auckland\" title=\"Auckland\">Auckland</a>, New Zealand, killing 189.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Orpheus_(1860)\" title=\"HMS Orpheus (1860)\">HMS <i>Orpheus</i></a> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Auckland\" title=\"Auckland\">Auckland</a>, New Zealand, killing 189.", "links": [{"title": "HMS Orpheus (1860)", "link": "https://wikipedia.org/wiki/<PERSON>_Or<PERSON>us_(1860)"}, {"title": "Auckland", "link": "https://wikipedia.org/wiki/Auckland"}]}, {"year": "1894", "text": "The Cripple Creek miner's strike, led by the Western Federation of Miners, begins in Cripple Creek, Colorado, United States.", "html": "1894 - The <a href=\"https://wikipedia.org/wiki/Cripple_Creek_miners%27_strike_of_1894\" title=\"Cripple Creek miners' strike of 1894\">Cripple Creek miner's strike</a>, led by the <a href=\"https://wikipedia.org/wiki/Western_Federation_of_Miners\" title=\"Western Federation of Miners\">Western Federation of Miners</a>, begins in <a href=\"https://wikipedia.org/wiki/Cripple_Creek,_Colorado\" title=\"Cripple Creek, Colorado\">Cripple Creek, Colorado</a>, United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cripple_Creek_miners%27_strike_of_1894\" title=\"Cripple Creek miners' strike of 1894\">Cripple Creek miner's strike</a>, led by the <a href=\"https://wikipedia.org/wiki/Western_Federation_of_Miners\" title=\"Western Federation of Miners\">Western Federation of Miners</a>, begins in <a href=\"https://wikipedia.org/wiki/Cripple_Creek,_Colorado\" title=\"Cripple Creek, Colorado\">Cripple Creek, Colorado</a>, United States.", "links": [{"title": "Cripple Creek miners' strike of 1894", "link": "https://wikipedia.org/wiki/Cripple_Creek_miners%27_strike_of_1894"}, {"title": "Western Federation of Miners", "link": "https://wikipedia.org/wiki/Western_Federation_of_Miners"}, {"title": "Cripple Creek, Colorado", "link": "https://wikipedia.org/wiki/Cripple_Creek,_Colorado"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON> affair: <PERSON><PERSON> is brought to trial for libel for publishing J'Accuse...!", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair\" title=\"<PERSON><PERSON><PERSON><PERSON> affair\"><PERSON><PERSON><PERSON>s affair</a>: <a href=\"https://wikipedia.org/wiki/%C3%89mile_Zola\" title=\"É<PERSON>\"><PERSON><PERSON></a> is brought to trial for <a href=\"https://wikipedia.org/wiki/Libel\" class=\"mw-redirect\" title=\"Libel\">libel</a> for publishing <i><a href=\"https://wikipedia.org/wiki/J%27Accuse...!\" title=\"J'Accuse...!\">J'Accuse...!</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair\" title=\"<PERSON><PERSON><PERSON><PERSON> affair\"><PERSON><PERSON><PERSON><PERSON> affair</a>: <a href=\"https://wikipedia.org/wiki/%C3%89mile_Zola\" title=\"É<PERSON>\"><PERSON><PERSON></a> is brought to trial for <a href=\"https://wikipedia.org/wiki/Libel\" class=\"mw-redirect\" title=\"Libel\">libel</a> for publishing <i><a href=\"https://wikipedia.org/wiki/J%27Accuse...!\" title=\"J'Accuse...!\">J'Accuse...!</a></i>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> affair", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Zola"}, {"title": "Libel", "link": "https://wikipedia.org/wiki/Libel"}, {"title": "J'Accuse...!", "link": "https://wikipedia.org/wiki/J%27Accuse...!"}]}, {"year": "1900", "text": "Second Boer War: British troops fail in their third attempt to lift the Siege of Ladysmith.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> troops fail in their <a href=\"https://wikipedia.org/wiki/Battle_of_Vaal_Krantz\" title=\"Battle of Vaal Krantz\">third attempt</a> to lift the <a href=\"https://wikipedia.org/wiki/Siege_of_Ladysmith\" title=\"Siege of Ladysmith\">Siege of Ladysmith</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> troops fail in their <a href=\"https://wikipedia.org/wiki/Battle_of_Vaal_Krantz\" title=\"Battle of Vaal Krantz\">third attempt</a> to lift the <a href=\"https://wikipedia.org/wiki/Siege_of_Ladysmith\" title=\"Siege of Ladysmith\">Siege of Ladysmith</a>.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "Battle of Vaal Krantz", "link": "https://wikipedia.org/wiki/Battle_of_Vaal_Krantz"}, {"title": "Siege of Ladysmith", "link": "https://wikipedia.org/wiki/Siege_of_Ladysmith"}]}, {"year": "1900", "text": "A Chinese immigrant in San Francisco falls ill to bubonic plague in the first plague epidemic in the continental United States.", "html": "1900 - A Chinese immigrant in San Francisco falls ill to <a href=\"https://wikipedia.org/wiki/Bubonic_plague\" title=\"Bubonic plague\">bubonic plague</a> in the <a href=\"https://wikipedia.org/wiki/San_Francisco_plague_of_1900%E2%80%931904\" class=\"mw-redirect\" title=\"San Francisco plague of 1900-1904\">first plague epidemic in the continental United States</a>.", "no_year_html": "A Chinese immigrant in San Francisco falls ill to <a href=\"https://wikipedia.org/wiki/Bubonic_plague\" title=\"Bubonic plague\">bubonic plague</a> in the <a href=\"https://wikipedia.org/wiki/San_Francisco_plague_of_1900%E2%80%931904\" class=\"mw-redirect\" title=\"San Francisco plague of 1900-1904\">first plague epidemic in the continental United States</a>.", "links": [{"title": "Bubonic plague", "link": "https://wikipedia.org/wiki/Bubonic_plague"}, {"title": "San Francisco plague of 1900-1904", "link": "https://wikipedia.org/wiki/San_Francisco_plague_of_1900%E2%80%931904"}]}, {"year": "1904", "text": "The Great Baltimore Fire begins in Baltimore, Maryland; it destroys over 1,500 buildings in 30 hours.", "html": "1904 - The <a href=\"https://wikipedia.org/wiki/Great_Baltimore_Fire\" title=\"Great Baltimore Fire\">Great Baltimore Fire</a> begins in <a href=\"https://wikipedia.org/wiki/Baltimore,_Maryland\" class=\"mw-redirect\" title=\"Baltimore, Maryland\">Baltimore, Maryland</a>; it destroys over 1,500 buildings in 30 hours.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Baltimore_Fire\" title=\"Great Baltimore Fire\">Great Baltimore Fire</a> begins in <a href=\"https://wikipedia.org/wiki/Baltimore,_Maryland\" class=\"mw-redirect\" title=\"Baltimore, Maryland\">Baltimore, Maryland</a>; it destroys over 1,500 buildings in 30 hours.", "links": [{"title": "Great Baltimore Fire", "link": "https://wikipedia.org/wiki/Great_Baltimore_Fire"}, {"title": "Baltimore, Maryland", "link": "https://wikipedia.org/wiki/Baltimore,_Maryland"}]}, {"year": "1940", "text": "The second full-length animated Walt Disney film, <PERSON><PERSON><PERSON><PERSON>, premieres.", "html": "1940 - The second full-length animated <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\">Walt Disney</a> film, <i><a href=\"https://wikipedia.org/wiki/Pi<PERSON><PERSON><PERSON>_(1940_film)\" title=\"Pinocchio (1940 film)\">Pinocchio</a></i>, premieres.", "no_year_html": "The second full-length animated <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\">Walt Disney</a> film, <i><a href=\"https://wikipedia.org/wiki/Pi<PERSON><PERSON><PERSON>_(1940_film)\" title=\"Pinocchio (1940 film)\">Pi<PERSON><PERSON>o</a></i>, premieres.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pinocchio (1940 film)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(1940_film)"}]}, {"year": "1943", "text": "World War II: Imperial Japanese Navy forces complete the evacuation of Imperial Japanese Army troops from Guadalcanal during Operation Ke, ending Japanese attempts to retake the island from Allied forces in the Guadalcanal Campaign.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> forces complete the evacuation of <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> troops from <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a> during <a href=\"https://wikipedia.org/wiki/Operation_Ke\" title=\"Operation Ke\">Operation Ke</a>, ending Japanese attempts to retake the island from <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces in the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> forces complete the evacuation of <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> troops from <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a> during <a href=\"https://wikipedia.org/wiki/Operation_Ke\" title=\"Operation Ke\">Operation Ke</a>, ending Japanese attempts to retake the island from <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces in the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "Guadalcanal", "link": "https://wikipedia.org/wiki/Guadalcanal"}, {"title": "Operation Ke", "link": "https://wikipedia.org/wiki/Operation_Ke"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Guadalcanal Campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_Campaign"}]}, {"year": "1944", "text": "World War II: In Anzio, Italy, German forces launch a counteroffensive during the Allied Operation Shingle.", "html": "1944 - World War II: In <a href=\"https://wikipedia.org/wiki/Anzio\" title=\"Anzio\">Anzio</a>, Italy, German forces launch a counteroffensive during the Allied <a href=\"https://wikipedia.org/wiki/Operation_Shingle\" class=\"mw-redirect\" title=\"Operation Shingle\">Operation Shingle</a>.", "no_year_html": "World War II: In <a href=\"https://wikipedia.org/wiki/Anzio\" title=\"Anzio\">Anzio</a>, Italy, German forces launch a counteroffensive during the Allied <a href=\"https://wikipedia.org/wiki/Operation_Shingle\" class=\"mw-redirect\" title=\"Operation Shingle\">Operation Shingle</a>.", "links": [{"title": "Anzio", "link": "https://wikipedia.org/wiki/Anzio"}, {"title": "Operation Shingle", "link": "https://wikipedia.org/wiki/Operation_Shingle"}]}, {"year": "1951", "text": "Korean War: More than 700 suspected communist sympathizers are massacred by South Korean forces.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: More than 700 suspected communist sympathizers <a href=\"https://wikipedia.org/wiki/Sancheong-Hamyang_massacre\" class=\"mw-redirect\" title=\"Sancheong-Hamyang massacre\">are massacred</a> by <a href=\"https://wikipedia.org/wiki/Republic_of_Korea_Army\" title=\"Republic of Korea Army\">South Korean forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: More than 700 suspected communist sympathizers <a href=\"https://wikipedia.org/wiki/Sancheong-Hamyang_massacre\" class=\"mw-redirect\" title=\"Sancheong-Hamyang massacre\">are massacred</a> by <a href=\"https://wikipedia.org/wiki/Republic_of_Korea_Army\" title=\"Republic of Korea Army\">South Korean forces</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Sancheong-Hamyang massacre", "link": "https://wikipedia.org/wiki/Sancheong-Hamyang_massacre"}, {"title": "Republic of Korea Army", "link": "https://wikipedia.org/wiki/Republic_of_Korea_Army"}]}, {"year": "1962", "text": "The United States bans all Cuban imports and exports.", "html": "1962 - The United States <a href=\"https://wikipedia.org/wiki/United_States_embargo_against_Cuba\" title=\"United States embargo against Cuba\">bans</a> all <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuban</a> imports and exports.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/United_States_embargo_against_Cuba\" title=\"United States embargo against Cuba\">bans</a> all <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuban</a> imports and exports.", "links": [{"title": "United States embargo against Cuba", "link": "https://wikipedia.org/wiki/United_States_embargo_against_Cuba"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1964", "text": "The Beatles land in the United States for the first time, at the newly renamed John F. Kennedy International Airport.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> land in the United States for the first time, at the newly renamed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> land in the United States for the first time, at the newly renamed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a>.", "links": [{"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "<PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Kennedy_International_Airport"}]}, {"year": "1966", "text": "The Great Fire of Iloilo breaks out in a lumber yard in Iznart Street and burns for almost half a day destroying nearly three-quarters of the City Proper area and Php 50 million pesos in total properties' damage.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/1966_Iloilo_City_fire\" title=\"1966 Iloilo City fire\">Great Fire of Iloilo</a> breaks out in a lumber yard in <a href=\"https://wikipedia.org/wiki/Iznart_Street\" title=\"Iznart Street\">Iznart Street</a> and burns for almost half a day destroying nearly three-quarters of the <a href=\"https://wikipedia.org/wiki/Iloilo_City_Proper\" title=\"Iloilo City Proper\">City Proper</a> area and Php 50 million pesos in total properties' damage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1966_Iloilo_City_fire\" title=\"1966 Iloilo City fire\">Great Fire of Iloilo</a> breaks out in a lumber yard in <a href=\"https://wikipedia.org/wiki/Iznart_Street\" title=\"Iznart Street\">Iznart Street</a> and burns for almost half a day destroying nearly three-quarters of the <a href=\"https://wikipedia.org/wiki/Iloilo_City_Proper\" title=\"Iloilo City Proper\">City Proper</a> area and Php 50 million pesos in total properties' damage.", "links": [{"title": "1966 Iloilo City fire", "link": "https://wikipedia.org/wiki/1966_Iloilo_City_fire"}, {"title": "Iznart Street", "link": "https://wikipedia.org/wiki/Iznart_Street"}, {"title": "Iloilo City Proper", "link": "https://wikipedia.org/wiki/Iloilo_City_Proper"}]}, {"year": "1974", "text": "Grenada gains independence from the United Kingdom.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Grenada\" title=\"Grenada\">Grenada</a> gains independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grenada\" title=\"Grenada\">Grenada</a> gains independence from the United Kingdom.", "links": [{"title": "Grenada", "link": "https://wikipedia.org/wiki/Grenada"}]}, {"year": "1979", "text": "Pluto moves inside Neptune's orbit for the first time since either was discovered.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> moves inside <a href=\"https://wikipedia.org/wiki/Neptune\" title=\"Neptune\">Neptune</a>'s orbit for the first time since either was discovered.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> moves inside <a href=\"https://wikipedia.org/wiki/Neptune\" title=\"Neptune\">Neptune</a>'s orbit for the first time since either was discovered.", "links": [{"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}, {"title": "Neptune", "link": "https://wikipedia.org/wiki/Neptune"}]}, {"year": "1981", "text": "A plane crash at Pushkin Airport kills 50 people, including 16 members of the Pacific Fleet.", "html": "1981 - A <a href=\"https://wikipedia.org/wiki/1981_Pushkin_Tu-104_crash\" title=\"1981 Pushkin Tu-104 crash\">plane crash</a> at <a href=\"https://wikipedia.org/wiki/Pushkin_Airport\" title=\"Pushkin Airport\">Pushkin Airport</a> kills 50 people, including 16 members of the <a href=\"https://wikipedia.org/wiki/Pacific_Fleet_(Russia)\" title=\"Pacific Fleet (Russia)\">Pacific Fleet</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1981_Pushkin_Tu-104_crash\" title=\"1981 Pushkin Tu-104 crash\">plane crash</a> at <a href=\"https://wikipedia.org/wiki/Pushkin_Airport\" title=\"Pushkin Airport\">Pushkin Airport</a> kills 50 people, including 16 members of the <a href=\"https://wikipedia.org/wiki/Pacific_Fleet_(Russia)\" title=\"Pacific Fleet (Russia)\">Pacific Fleet</a>.", "links": [{"title": "1981 Pushkin Tu-104 crash", "link": "https://wikipedia.org/wiki/1981_Pushkin_Tu-104_crash"}, {"title": "Pushkin Airport", "link": "https://wikipedia.org/wiki/Pushkin_Airport"}, {"title": "Pacific Fleet (Russia)", "link": "https://wikipedia.org/wiki/Pacific_Fleet_(Russia)"}]}, {"year": "1984", "text": "Space Shuttle program: STS-41-B Mission: astronauts <PERSON> and <PERSON> make the first untethered space walk using the Manned Maneuvering Unit (MMU).", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-41-B\" title=\"STS-41-B\">STS-41-B</a> Mission: astronauts <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>andless_II\" title=\"Bruce McCandless II\"><PERSON> II</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> make the first untethered <a href=\"https://wikipedia.org/wiki/Space_walk\" class=\"mw-redirect\" title=\"Space walk\">space walk</a> using the <a href=\"https://wikipedia.org/wiki/Manned_Maneuvering_Unit\" title=\"Manned Maneuvering Unit\">Manned Maneuvering Unit</a> (MMU).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-41-B\" title=\"STS-41-B\">STS-41-B</a> Mission: astronauts <a href=\"https://wikipedia.org/wiki/Bruce_McCandless_II\" title=\"Bruce McCandless II\"><PERSON> II</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> make the first untethered <a href=\"https://wikipedia.org/wiki/Space_walk\" class=\"mw-redirect\" title=\"Space walk\">space walk</a> using the <a href=\"https://wikipedia.org/wiki/Manned_Maneuvering_Unit\" title=\"Manned Maneuvering Unit\">Manned Maneuvering Unit</a> (MMU).", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-41-B", "link": "https://wikipedia.org/wiki/STS-41-B"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Space walk", "link": "https://wikipedia.org/wiki/Space_walk"}, {"title": "Manned Maneuvering Unit", "link": "https://wikipedia.org/wiki/Manned_Maneuvering_Unit"}]}, {"year": "1986", "text": "Twenty-eight years of one-family rule end in Haiti, when President <PERSON><PERSON><PERSON> flees the Caribbean nation.", "html": "1986 - Twenty-eight years of one-family rule end in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>, when President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> flees the <a href=\"https://wikipedia.org/wiki/Caribbean\" title=\"Caribbean\">Caribbean</a> nation.", "no_year_html": "Twenty-eight years of one-family rule end in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>, when President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> flees the <a href=\"https://wikipedia.org/wiki/Caribbean\" title=\"Caribbean\">Caribbean</a> nation.", "links": [{"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Caribbean", "link": "https://wikipedia.org/wiki/Caribbean"}]}, {"year": "1990", "text": "Dissolution of the Soviet Union: The Central Committee of the Soviet Communist Party agrees to give up its monopoly on power.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union\" title=\"Dissolution of the Soviet Union\">Dissolution of the Soviet Union</a>: The <a href=\"https://wikipedia.org/wiki/Central_Committee\" class=\"mw-redirect\" title=\"Central Committee\">Central Committee</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Communist_Party\" class=\"mw-redirect\" title=\"Soviet Communist Party\">Soviet Communist Party</a> agrees to give up its monopoly on power.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union\" title=\"Dissolution of the Soviet Union\">Dissolution of the Soviet Union</a>: The <a href=\"https://wikipedia.org/wiki/Central_Committee\" class=\"mw-redirect\" title=\"Central Committee\">Central Committee</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Communist_Party\" class=\"mw-redirect\" title=\"Soviet Communist Party\">Soviet Communist Party</a> agrees to give up its monopoly on power.", "links": [{"title": "Dissolution of the Soviet Union", "link": "https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union"}, {"title": "Central Committee", "link": "https://wikipedia.org/wiki/Central_Committee"}, {"title": "Soviet Communist Party", "link": "https://wikipedia.org/wiki/Soviet_Communist_Party"}]}, {"year": "1991", "text": "Haiti's first democratically elected president, <PERSON><PERSON><PERSON>, is sworn in.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>'s first democratically elected president, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is sworn in.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>'s first democratically elected president, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is sworn in.", "links": [{"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "The Troubles: The Provisional IRA launches a mortar attack on 10 Downing Street in London, the headquarters of the British government.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> launches <a href=\"https://wikipedia.org/wiki/Downing_Street_mortar_attack\" title=\"Downing Street mortar attack\">a mortar attack</a> on <a href=\"https://wikipedia.org/wiki/10_Downing_Street\" title=\"10 Downing Street\">10 Downing Street</a> in London, the headquarters of the British government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> launches <a href=\"https://wikipedia.org/wiki/Downing_Street_mortar_attack\" title=\"Downing Street mortar attack\">a mortar attack</a> on <a href=\"https://wikipedia.org/wiki/10_Downing_Street\" title=\"10 Downing Street\">10 Downing Street</a> in London, the headquarters of the British government.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Downing Street mortar attack", "link": "https://wikipedia.org/wiki/Downing_Street_mortar_attack"}, {"title": "10 Downing Street", "link": "https://wikipedia.org/wiki/10_Downing_Street"}]}, {"year": "1992", "text": "The Maastricht Treaty is signed, leading to the creation of the European Union.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Maastricht_Treaty\" title=\"Maastricht Treaty\">Maastricht Treaty</a> is signed, leading to the creation of the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Maastricht_Treaty\" title=\"Maastricht Treaty\">Maastricht Treaty</a> is signed, leading to the creation of the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "Maastricht Treaty", "link": "https://wikipedia.org/wiki/Maastricht_Treaty"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "1995", "text": "<PERSON><PERSON>, the mastermind of the 1993 World Trade Center bombing, is arrested in Islamabad, Pakistan.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the mastermind of the <a href=\"https://wikipedia.org/wiki/1993_World_Trade_Center_bombing\" title=\"1993 World Trade Center bombing\">1993 World Trade Center bombing</a>, is arrested in <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad, Pakistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the mastermind of the <a href=\"https://wikipedia.org/wiki/1993_World_Trade_Center_bombing\" title=\"1993 World Trade Center bombing\">1993 World Trade Center bombing</a>, is arrested in <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad, Pakistan</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "1993 World Trade Center bombing", "link": "https://wikipedia.org/wiki/1993_World_Trade_Center_bombing"}, {"title": "Islamabad", "link": "https://wikipedia.org/wiki/Islamabad"}]}, {"year": "1999", "text": "Crown Prince <PERSON> becomes the King of Jordan on the death of his father, King <PERSON>.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Jordan\" title=\"<PERSON> of Jordan\">Crown Prince <PERSON></a> becomes the King of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a> on <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_Hussein_of_Jordan\" title=\"Death and state funeral of <PERSON> Jordan\">the death</a> of his father, King <a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"Hussein of Jordan\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Jordan\" title=\"<PERSON> Jordan\">Crown Prince <PERSON></a> becomes the King of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a> on <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>_Jordan\" title=\"Death and state funeral of <PERSON> Jordan\">the death</a> of his father, King <a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"Hussein of Jordan\"><PERSON></a>.", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Jordan"}, {"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "Death and state funeral of <PERSON>", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_of_Jordan"}, {"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/Hussein_of_Jordan"}]}, {"year": "2001", "text": "Space Shuttle program: Space Shuttle Atlantis is launched on mission STS-98, carrying the Destiny laboratory module to the International Space Station.", "html": "2001 - Space Shuttle program: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-98\" title=\"STS-98\">STS-98</a>, carrying the <i><a href=\"https://wikipedia.org/wiki/Destiny_(ISS_module)\" title=\"Destiny (ISS module)\">Destiny</a></i> laboratory module to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "Space Shuttle program: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-98\" title=\"STS-98\">STS-98</a>, carrying the <i><a href=\"https://wikipedia.org/wiki/Destiny_(ISS_module)\" title=\"Destiny (ISS module)\">Destiny</a></i> laboratory module to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-98", "link": "https://wikipedia.org/wiki/STS-98"}, {"title": "Destiny (ISS module)", "link": "https://wikipedia.org/wiki/Destiny_(ISS_module)"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2009", "text": "Bushfires in Victoria leave 173 dead in the worst natural disaster in Australia's history.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Black_Saturday_bushfires\" title=\"Black Saturday bushfires\">Bushfires in Victoria</a> leave 173 dead in the worst natural disaster in Australia's history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Saturday_bushfires\" title=\"Black Saturday bushfires\">Bushfires in Victoria</a> leave 173 dead in the worst natural disaster in Australia's history.", "links": [{"title": "Black Saturday bushfires", "link": "https://wikipedia.org/wiki/Black_Saturday_bushfires"}]}, {"year": "2012", "text": "President <PERSON> of the Republic of Maldives resigns, after 23 days of anti-governmental protests calling for the release of the Chief Judge unlawfully arrested by the military.", "html": "2012 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Maldives\" title=\"Maldives\">Republic of Maldives</a> resigns, after 23 days of anti-governmental protests calling for the release of the Chief Judge unlawfully arrested by the military.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Maldives\" title=\"Maldives\">Republic of Maldives</a> resigns, after 23 days of anti-governmental protests calling for the release of the Chief Judge unlawfully arrested by the military.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Maldives", "link": "https://wikipedia.org/wiki/Maldives"}]}, {"year": "2013", "text": "The U.S. state of Mississippi officially certifies the Thirteenth Amendment, becoming the last state to approve the abolition of slavery. The Thirteenth Amendment was formally ratified by Mississippi in 1995.", "html": "2013 - The U.S. state of <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> officially certifies the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment</a>, becoming the last state to approve the abolition of <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>. The Thirteenth Amendment was formally ratified by Mississippi in 1995.", "no_year_html": "The U.S. state of <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> officially certifies the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment</a>, becoming the last state to approve the abolition of <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>. The Thirteenth Amendment was formally ratified by Mississippi in 1995.", "links": [{"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Thirteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}]}, {"year": "2014", "text": "Scientists announce that the Happisburgh footprints in Norfolk, England, date back to more than 800,000 years ago, making them the oldest known hominid footprints outside Africa.", "html": "2014 - Scientists announce that the <a href=\"https://wikipedia.org/wiki/Happisburgh_footprints\" title=\"Happisburgh footprints\">Happisburgh footprints</a> in <a href=\"https://wikipedia.org/wiki/Norfolk\" title=\"Norfolk\">Norfolk</a>, England, date back to more than 800,000 years ago, making them the oldest known hominid footprints outside Africa.", "no_year_html": "Scientists announce that the <a href=\"https://wikipedia.org/wiki/Happisburgh_footprints\" title=\"Happisburgh footprints\">Happisburgh footprints</a> in <a href=\"https://wikipedia.org/wiki/Norfolk\" title=\"Norfolk\">Norfolk</a>, England, date back to more than 800,000 years ago, making them the oldest known hominid footprints outside Africa.", "links": [{"title": "Happisburgh footprints", "link": "https://wikipedia.org/wiki/Happisburgh_footprints"}, {"title": "Norfolk", "link": "https://wikipedia.org/wiki/Norfolk"}]}, {"year": "2016", "text": "North Korea launches Kwangmyŏngsŏng-4 into outer space violating multiple UN treaties and prompting condemnation from around the world.", "html": "2016 - North Korea launches <a href=\"https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-4\" title=\"Kwangmyŏngsŏng-4\">Kwangmyŏngsŏng-4</a> into outer space violating multiple UN treaties and prompting condemnation from around the world.", "no_year_html": "North Korea launches <a href=\"https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-4\" title=\"Kwangmyŏngsŏng-4\">Kwangmyŏngsŏng-4</a> into outer space violating multiple UN treaties and prompting condemnation from around the world.", "links": [{"title": "Kwangmyŏngsŏng-4", "link": "https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-4"}]}, {"year": "2021", "text": "The 2021 Uttarakhand flood begins.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/2021_Uttarakhand_flood\" title=\"2021 Uttarakhand flood\">2021 Uttarakhand flood</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2021_Uttarakhand_flood\" title=\"2021 Uttarakhand flood\">2021 Uttarakhand flood</a> begins.", "links": [{"title": "2021 Uttarakhand flood", "link": "https://wikipedia.org/wiki/2021_Uttarakhand_flood"}]}, {"year": "2024", "text": "Pakistan election offices are hit by twin bombings, killing at least 24 people a day before general elections.", "html": "2024 - Pakistan election offices are hit by <a href=\"https://wikipedia.org/wiki/2024_Balochistan_bombings\" title=\"2024 Balochistan bombings\">twin bombings</a>, killing at least 24 people a day before <a href=\"https://wikipedia.org/wiki/2024_Pakistani_general_election\" title=\"2024 Pakistani general election\">general elections.</a>", "no_year_html": "Pakistan election offices are hit by <a href=\"https://wikipedia.org/wiki/2024_Balochistan_bombings\" title=\"2024 Balochistan bombings\">twin bombings</a>, killing at least 24 people a day before <a href=\"https://wikipedia.org/wiki/2024_Pakistani_general_election\" title=\"2024 Pakistani general election\">general elections.</a>", "links": [{"title": "2024 Balochistan bombings", "link": "https://wikipedia.org/wiki/2024_Balochistan_bombings"}, {"title": "2024 Pakistani general election", "link": "https://wikipedia.org/wiki/2024_Pakistani_general_election"}]}], "Births": [{"year": "574", "text": "<PERSON> of Japan (d. 622)", "html": "574 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>h%C5%8Dtoku\" title=\"Prince <PERSON>\">Prince <PERSON></a> of Japan (d. 622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>h%C5%8Dtoku\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON></a> of Japan (d. 622)", "links": [{"title": "Prince <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prince_Sh%C5%8Dtoku"}]}, {"year": "1102", "text": "Empress <PERSON>, Holy Roman Empress and claimant to the English throne (probable; d. 1167)", "html": "1102 - <a href=\"https://wikipedia.org/wiki/Empress_Matilda\" title=\"Empress Matilda\">Empress <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empress\" class=\"mw-redirect\" title=\"Holy Roman Empress\">Holy Roman Empress</a> and claimant to the <a href=\"https://wikipedia.org/wiki/English_throne\" class=\"mw-redirect\" title=\"English throne\">English throne</a> (probable; d. 1167)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_Matilda\" title=\"Empress Matilda\">Empress <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empress\" class=\"mw-redirect\" title=\"Holy Roman Empress\">Holy Roman Empress</a> and claimant to the <a href=\"https://wikipedia.org/wiki/English_throne\" class=\"mw-redirect\" title=\"English throne\">English throne</a> (probable; d. 1167)", "links": [{"title": "Empress <PERSON>", "link": "https://wikipedia.org/wiki/Empress_Matilda"}, {"title": "Holy Roman Empress", "link": "https://wikipedia.org/wiki/Holy_Roman_Empress"}, {"title": "English throne", "link": "https://wikipedia.org/wiki/English_throne"}]}, {"year": "1449", "text": "<PERSON><PERSON> of Nassau-Siegen, German countess (d. 1477)", "html": "1449 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Nassau-Siegen\" title=\"<PERSON><PERSON> of Nassau-Siegen\"><PERSON><PERSON> of Nassau-Siegen</a>, German countess (d. 1477)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Nassau-Siegen\" title=\"<PERSON><PERSON> of Nassau-Siegen\"><PERSON><PERSON> of Nassau-Siegen</a>, German countess (d. 1477)", "links": [{"title": "<PERSON><PERSON> of Nassau-Siegen", "link": "https://wikipedia.org/wiki/Adrian<PERSON>_of_Nassau-Siegen"}]}, {"year": "1478", "text": "<PERSON>, English lawyer and politician, Lord Chancellor of England (d. 1535)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of England</a> (d. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of England</a> (d. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1487", "text": "Queen <PERSON><PERSON><PERSON><PERSON>, Korean royal consort (d. 1557)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON><PERSON><PERSON><PERSON></a>, Korean royal consort (d. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON><PERSON><PERSON><PERSON></a>, Korean royal consort (d. 1557)", "links": [{"title": "Queen <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1500", "text": "<PERSON>, viceroy of Portuguese India (d. 1548)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, viceroy of Portuguese India (d. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, viceroy of Portuguese India (d. 1548)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1612", "text": "<PERSON>, English playwright and manager (d. 1683)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and manager (d. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and manager (d. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON><PERSON><PERSON>, Italian noble (d. 1694)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/Vitt<PERSON>_della_Rovere\" title=\"<PERSON>itt<PERSON> della Rovere\"><PERSON><PERSON><PERSON>e</a>, Italian noble (d. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>itt<PERSON> della Rovere\"><PERSON><PERSON><PERSON>e</a>, Italian noble (d. 1694)", "links": [{"title": "Vittoria della Rover<PERSON>", "link": "https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1693", "text": "<PERSON> <PERSON> of Russia (d. 1740)", "html": "1693 - Empress <a href=\"https://wikipedia.org/wiki/Anna_of_Russia\" title=\"Anna of Russia\"><PERSON> of Russia</a> (d. 1740)", "no_year_html": "Empress <a href=\"https://wikipedia.org/wiki/Anna_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1740)", "links": [{"title": "Anna of Russia", "link": "https://wikipedia.org/wiki/Anna_of_Russia"}]}, {"year": "1722", "text": "<PERSON><PERSON>, Iranian anthologist and poet (d. 1781)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian anthologist and poet (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian anthologist and poet (d. 1781)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1726", "text": "<PERSON>-<PERSON><PERSON><PERSON>, English painter (d. 1766)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1766)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1741", "text": "<PERSON>, Swiss-English painter and academic (d. 1825)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-English painter and academic (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-English painter and academic (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech tenor and composer (d. 1826)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>ed<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tenor and composer (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>edik<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tenor and composer (d. 1826)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benedikt_Schack"}]}, {"year": "1796", "text": "<PERSON>, English-Australian lawyer and politician, 2nd Premier of Tasmania (baptism date; d. 1874)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (baptism date; d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (baptism date; d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1802", "text": "<PERSON>, American poet, essayist, and literary critic (d. 1892)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, and literary critic (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, and literary critic (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, American blacksmith and businessman, founded Deere & Company (d. 1886)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American blacksmith and businessman, founded <a href=\"https://wikipedia.org/wiki/Deere_%26_Company\" class=\"mw-redirect\" title=\"Deere &amp; Company\">Deere &amp; Company</a> (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American blacksmith and businessman, founded <a href=\"https://wikipedia.org/wiki/Deere_%26_Company\" class=\"mw-redirect\" title=\"Deere &amp; Company\">Deere &amp; Company</a> (d. 1886)", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)"}, {"title": "Deere & Company", "link": "https://wikipedia.org/wiki/Deere_%26_Company"}]}, {"year": "1812", "text": "<PERSON>, English novelist and critic (d. 1870)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and critic (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and critic (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, German zoologist and ecologist (d. 1908)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6bius\" title=\"<PERSON>\"><PERSON></a>, German zoologist and ecologist (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6bius\" title=\"<PERSON>\"><PERSON></a>, German zoologist and ecologist (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_M%C3%B6bius"}]}, {"year": "1834", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French architect (d. 1895)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French architect (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French architect (d. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, Scottish lexicographer and philologist (d. 1915)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)\" title=\"<PERSON> (lexicographer)\"><PERSON></a>, Scottish lexicographer and philologist (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)\" title=\"<PERSON> (lexicographer)\"><PERSON></a>, Scottish lexicographer and philologist (d. 1915)", "links": [{"title": "<PERSON> (lexicographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)"}]}, {"year": "1864", "text": "<PERSON>, American baritone singer (d. 1933)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American baritone singer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American baritone singer (d. 1933)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1867", "text": "<PERSON>, American author (d. 1957)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_In<PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Austrian-Scottish psychologist and therapist (d. 1937)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Scottish psychologist and therapist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Scottish psychologist and therapist (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Swedish pianist, composer, and conductor (d. 1927)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish pianist, composer, and conductor (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish pianist, composer, and conductor (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_Stenhammar"}]}, {"year": "1873", "text": "<PERSON>, Irish shipbuilder and businessman, designed the RMS Titanic (d. 1912)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(shipbuilder)\" class=\"mw-redirect\" title=\"<PERSON> (shipbuilder)\"><PERSON></a>, Irish shipbuilder and businessman, designed the <a href=\"https://wikipedia.org/wiki/RMS_Titanic\" class=\"mw-redirect\" title=\"RMS Titanic\">RMS <i>Titanic</i></a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(shipbuilder)\" class=\"mw-redirect\" title=\"<PERSON> (shipbuilder)\"><PERSON></a>, Irish shipbuilder and businessman, designed the <a href=\"https://wikipedia.org/wiki/RMS_Titanic\" class=\"mw-redirect\" title=\"RMS Titanic\">RMS <i>Titanic</i></a> (d. 1912)", "links": [{"title": "<PERSON> (shipbuilder)", "link": "https://wikipedia.org/wiki/<PERSON>_(shipbuilder)"}, {"title": "RMS Titanic", "link": "https://wikipedia.org/wiki/RMS_Titanic"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Finnish composer (d. 1937)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish composer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish composer (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>in"}]}, {"year": "1877", "text": "<PERSON><PERSON> <PERSON><PERSON>, English mathematician and geneticist (d. 1947)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English mathematician and geneticist (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English mathematician and geneticist (d. 1947)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Russian-American pianist and conductor (d. 1936)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Ossip_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American pianist and conductor (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ossi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American pianist and conductor (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ossip_<PERSON><PERSON><PERSON>ch"}]}, {"year": "1885", "text": "<PERSON>, American novelist, short-story writer, and playwright, Nobel Prize laureate (d. 1951)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short-story writer, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short-story writer, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1885", "text": "<PERSON>, German field marshal (d. 1953)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, American pianist and composer (d. 1983)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pianist and composer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pianist and composer (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Swedish-American engineer and theorist (d. 1976)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer and theorist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer and theorist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Canadian astrophysicist and astronomer (d. 1988)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian astrophysicist and astronomer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian astrophysicist and astronomer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Filipino pianist, composer and teacher (d. 1934)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino pianist, composer and teacher (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino pianist, composer and teacher (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American actress (d. 1961)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, New Zealand minister and politician, 30th New Zealand Minister of Finance (d. 1989)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand minister and politician, 30th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(New_Zealand)\" title=\"Minister of Finance (New Zealand)\">New Zealand Minister of Finance</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand minister and politician, 30th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(New_Zealand)\" title=\"Minister of Finance (New Zealand)\">New Zealand Minister of Finance</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Finance (New Zealand)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(New_Zealand)"}]}, {"year": "1904", "text": "<PERSON>, American politician (d. 2002)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French philosopher and author (d. 1940)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Swedish physiologist and academic, Nobel Prize laureate (d. 1983)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Swedish physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Soviet engineer, founded the Antonov Design Bureau (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(aircraft_designer)\" title=\"<PERSON><PERSON> (aircraft designer)\"><PERSON><PERSON></a>, Soviet engineer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON>ov Design Bureau</a> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(aircraft_designer)\" title=\"<PERSON><PERSON> (aircraft designer)\"><PERSON><PERSON></a>, Soviet engineer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON>ov Design Bureau</a> (d. 1984)", "links": [{"title": "<PERSON><PERSON> (aircraft designer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(aircraft_designer)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Chinese emperor (d. 1967)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>uy<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese emperor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uy<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese emperor (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Puyi"}]}, {"year": "1908", "text": "<PERSON>, American swimmer and actor (d. 1983)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actor (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actor (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Indian journalist and author (d. 2000)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Brazilian archbishop (d. 1999)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/H%C3%A9lder_C%C3%A2mara\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian archbishop (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9lder_C%C3%A2mara\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian archbishop (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9lder_C%C3%A2mara"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Italian soldier (d. 2010)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian soldier (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian soldier (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English-Australian painter (d. 1981)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian painter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian painter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>sdale"}]}, {"year": "1912", "text": "<PERSON>, American socialite and oil heiress (d. 2020)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite and oil heiress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite and oil heiress (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Romanian patriarch (d. 2007)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Teoctist_Ar%C4%83pa%C8%99u\" title=\"Teoctist <PERSON><PERSON><PERSON>\"><PERSON>oc<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian patriarch (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Teoctist_Ar%C4%83pa%C8%99u\" title=\"Teoctist <PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON>oc<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian patriarch (d. 2007)", "links": [{"title": "Te<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teoctist_Ar%C4%83pa%C8%99u"}]}, {"year": "1915", "text": "<PERSON>, American actor and singer (d. 2002)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster (d. 2007)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American army corporal and combat medic, Medal of Honor recipient (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American army corporal and combat medic, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American army corporal and combat medic, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American actor and stuntman (d. 1989)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and stuntman (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and stuntman (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian-American singer-songwriter and author (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Oscar_Brand\" title=\"Oscar Brand\"><PERSON></a>, Canadian-American singer-songwriter and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_Brand\" title=\"Oscar Brand\"><PERSON></a>, Canadian-American singer-songwriter and author (d. 2016)", "links": [{"title": "Oscar Brand", "link": "https://wikipedia.org/wiki/Oscar_Brand"}]}, {"year": "1920", "text": "<PERSON>, Chinese-American engineer and businessman, founded Wang Laboratories (d. 1990)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Wang_Laboratories\" title=\"Wang Laboratories\">Wang Laboratories</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Wang_Laboratories\" title=\"Wang Laboratories\">Wang Laboratories</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wang Laboratories", "link": "https://wikipedia.org/wiki/Wang_Laboratories"}]}, {"year": "1921", "text": "<PERSON><PERSON>, South African cricketer (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (d. 1998)", "links": [{"title": "Athol Rowan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Rowan"}]}, {"year": "1922", "text": "<PERSON><PERSON>, English actress (d. 1980)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actress and restaurateur (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and restaurateur (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and restaurateur (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian wrestler (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler (d. 2012)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1926", "text": "<PERSON>, Russian engineer and astronaut (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, French singer and actress (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Juliette_Gr%C3%A9co\" title=\"<PERSON>\"><PERSON></a>, French singer and actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juliette_Gr%C3%A9co\" title=\"<PERSON>\"><PERSON></a>, French singer and actress (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juliette_Gr%C3%A9co"}]}, {"year": "1927", "text": "<PERSON>, Ukrainian-Russian runner and coach (d. 1975)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian runner and coach (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian runner and coach (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Mexican actor (d. 1973)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Lalo_R%C3%ADos\" title=\"Lalo Ríos\"><PERSON><PERSON></a>, Mexican actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lalo_R%C3%ADos\" title=\"Lalo Ríos\"><PERSON><PERSON></a>, Mexican actor (d. 1973)", "links": [{"title": "Lalo Ríos", "link": "https://wikipedia.org/wiki/Lalo_R%C3%ADos"}]}, {"year": "1928", "text": "<PERSON>, American general (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Lincoln_D._<PERSON>aurer\" title=\"<PERSON> Faurer\"><PERSON></a>, American general (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lincoln_D._Faurer\" title=\"Lincoln D. Faurer\"><PERSON></a>, American general (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lincoln_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English international footballer and manager (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and manager (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American journalist and memoirist", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Gay_Talese\" title=\"Gay Talese\"><PERSON></a>, American journalist and memoirist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gay_Talese\" title=\"Gay Talese\"><PERSON></a>, American journalist and memoirist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gay_Talese"}]}, {"year": "1932", "text": "<PERSON>, American colonel, pilot, and astronaut (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician, Sri Lankan Minister of Finance (d. 2015)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"K. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Sri_Lanka)\" title=\"Minister of Finance (Sri Lanka)\">Sri Lankan Minister of Finance</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"K. N<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Sri_Lanka)\" title=\"Minister of Finance (Sri Lanka)\">Sri Lankan Minister of Finance</a> (d. 2015)", "links": [{"title": "K. N. Cho<PERSON>y", "link": "https://wikipedia.org/wiki/K._<PERSON>._<PERSON>y"}, {"title": "Minister of Finance (Sri Lanka)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Sri_Lanka)"}]}, {"year": "1934", "text": "<PERSON>, Maltese lawyer and politician, 7th President of Malta", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Malta\" title=\"President of Malta\">President of Malta</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Malta\" title=\"President of Malta\">President of Malta</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>i"}, {"title": "President of Malta", "link": "https://wikipedia.org/wiki/President_of_Malta"}]}, {"year": "1934", "text": "<PERSON>, American saxophonist and producer (d. 1971)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and producer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and producer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 2003)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2003)", "links": [{"title": "Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Welsh international footballer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_footballer)\" title=\"<PERSON> (Welsh footballer)\"><PERSON></a>, Welsh international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_footballer)\" title=\"<PERSON> (Welsh footballer)\"><PERSON></a>, Welsh international footballer", "links": [{"title": "<PERSON> (Welsh footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_footballer)"}]}, {"year": "1935", "text": "<PERSON>, American businessman and politician (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Swiss actor and author (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Swiss actor and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Swiss actor and author (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON>_(actor)"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Italian journalist and politician", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English economist, journalist, and diplomat, British Ambassador to the United States (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English economist, journalist, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English economist, journalist, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a> (d. 2024)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)"}, {"title": "List of Ambassadors of the United Kingdom to the United States", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States"}]}, {"year": "1937", "text": "<PERSON>, Puerto Rican baseball player (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player (d. 2021)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1940", "text": "<PERSON>, Singaporean academic and politician, 7th President of Singapore", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean academic and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean academic and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "1942", "text": "<PERSON>, English actor (d. 2007)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American historian, author, and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Welsh rugby player and journalist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Argentinian-Brazilian director, producer, and screenwriter (d. 2016)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian-Brazilian director, producer, and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian-Brazilian director, producer, and screenwriter (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Babenco"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Haitian priest and activist (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian priest and activist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian priest and activist (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actor (d. 2011)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian police officer and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian police officer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian police officer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English-Australian bass player singer and songwriter <PERSON> (d. 2022)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian bass player singer and songwriter <PERSON> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian bass player singer and songwriter <PERSON> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American football player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, German singer-songwriter and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American football player and game show host", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and game show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor and director (d. 2017)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Danish racing driver", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Danish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Danish racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "1956", "text": "<PERSON>, American guitarist (d. 2007)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Mark_St._John\" title=\"Mark St. John\"><PERSON></a>, American guitarist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mark_St._John\" title=\"Mark St. John\"><PERSON> John</a>, American guitarist (d. 2007)", "links": [{"title": "Mark St. John", "link": "https://wikipedia.org/wiki/Mark_St._John"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican baseball player and footballer (d. 2020)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/D%C3%A1maso_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player and footballer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A1maso_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player and footballer (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A1maso_Garc%C3%ADa"}]}, {"year": "1958", "text": "<PERSON>, Italian footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English boxer and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, English boxer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, English boxer and politician", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)"}]}, {"year": "1958", "text": "<PERSON>, English journalist, author, and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English footballer, manager, and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Spader\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American keyboard player and songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English comedian, actor, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>, American Naval officer and astronaut", "html": "1963 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">He<PERSON><PERSON><PERSON>-<PERSON></a>, American Naval officer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">He<PERSON><PERSON><PERSON></a>, American Naval officer and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Heidemarie_<PERSON><PERSON><PERSON><PERSON>-Piper"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Indian journalist, author, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Banker\" title=\"<PERSON><PERSON> Banker\"><PERSON><PERSON></a>, Indian journalist, author, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Banker\" title=\"<PERSON><PERSON> Banker\"><PERSON><PERSON>er</a>, Indian journalist, author, and screenwriter", "links": [{"title": "<PERSON><PERSON> Banker", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Banker"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chris Rock\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chris_Rock\" title=\"Chris Rock\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, German swimmer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Ukrainian-Slovak ice hockey player and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Slovak ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Slovak ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Austrian politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Christian_Drobits\" title=\"Christian Drobits\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Drobits\" title=\"Christian Drobits\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Drobits"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American singer-songwriter and musician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian swimmer and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Austrian politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Maltese painter and musician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese painter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese painter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Atkins"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ly\" title=\"<PERSON><PERSON> Lively\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ly\" title=\"<PERSON><PERSON> Lively\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lively"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American rapper and producer (d. 2006)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American rapper and producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, American rapper and producer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, South African-Canadian basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese record producer, DJ, composer and arranger (d. 2010)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Nujabes\" title=\"Nujabes\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese record producer, DJ, composer and arranger (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nujabes\" title=\"Nujabes\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese record producer, DJ, composer and arranger (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nujabes"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian journalist, television presenter and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, television presenter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, television presenter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, French comedian and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French comedian and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9mi_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Filipino singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Miranda\" title=\"Chi<PERSON> Miranda\"><PERSON><PERSON></a>, Filipino singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Miranda\" title=\"<PERSON><PERSON> Miranda\"><PERSON><PERSON></a>, Filipino singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chi<PERSON>_Miranda"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Tsuney<PERSON><PERSON>_<PERSON>\" title=\"Tsuney<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsuney<PERSON><PERSON>_<PERSON>\" title=\"Tsuney<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsun<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Swiss ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Venezuelan baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Endy_Ch%C3%A1vez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/End<PERSON>_Ch%C3%A1vez\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Endy_Ch%C3%A1vez"}]}, {"year": "1978", "text": "<PERSON>, American model, actor, producer, and entrepreneur", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actor, producer, and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actor, producer, and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American-Belizean basketball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Palacio\"><PERSON><PERSON></a>, American-Belizean basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Palacio\"><PERSON><PERSON></a>, American-Belizean basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_<PERSON>cio"}]}, {"year": "1978", "text": "<PERSON>, Belgian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German footballer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Yemeni journalist and activist, Nobel Prize laureate", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tawakko<PERSON> Karman\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Yemeni journalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tawak<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Yemeni journalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "Tawak<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wak<PERSON><PERSON>_<PERSON>rman"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1979", "text": "<PERSON>, American author", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Croatian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>or_<PERSON>gari%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gari%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dalibor_Bagari%C4%87"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1981)\" title=\"<PERSON><PERSON> (footballer, born 1981)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1981)\" title=\"<PERSON><PERSON> (footballer, born 1981)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1981)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1981)"}]}, {"year": "1981", "text": "<PERSON>, South Korean boxer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Iranian serial killer (d. 2006)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian serial killer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian serial killer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Japanese actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, French basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Micka%C3%ABl_Pi%C3%A9trus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Micka%C3%ABl_Pi%C3%A9trus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micka%C3%ABl_Pi%C3%A9trus"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S<PERSON>_Kamogawa"}]}, {"year": "1983", "text": "<PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American decathlete", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>s American model and actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> American model and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Japanese singer and actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ai_Kago"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American-Greek basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Italian cyclist", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish YouTuber", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Jacksepticeye\" title=\"Jacksepticeye\">Jacksepticeye</a>, Irish <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jacksepticeye\" title=\"Jacksepticeye\">Jacksepticeye</a>, Irish <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a>", "links": [{"title": "Jacksepticeye", "link": "https://wikipedia.org/wiki/Jacksepticeye"}, {"title": "YouTuber", "link": "https://wikipedia.org/wiki/YouTuber"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American hurdler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American Internet personality and singer-songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American Internet personality and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American Internet personality and singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly_(ice_hockey)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly_(ice_hockey)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly_(ice_hockey)"}]}, {"year": "1991", "text": "<PERSON>, Slovak ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_P%C3%A1nik\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nik\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_P%C3%A1nik"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Russian figure skater", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Ksen<PERSON>_Stolbova\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ksen<PERSON>_<PERSON>va\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ksenia_Stolbova"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English diver", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diver)\" title=\"<PERSON> (diver)\"><PERSON></a>, English diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(diver)\" title=\"<PERSON> (diver)\"><PERSON></a>, English diver", "links": [{"title": "<PERSON> (diver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diver)"}]}, {"year": "1994", "text": "<PERSON>, American ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Barber\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Welsh-Australian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>-<PERSON><PERSON>, English actor and musician", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, English actor and musician", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Mexican baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, French racing driver", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Ukrainian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Egyptian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Italian footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "199", "text": "<PERSON><PERSON>, Chinese warlord", "html": "199 - <a href=\"https://wikipedia.org/wiki/L%C3%BC_Bu\" title=\"<PERSON>ü Bu\"><PERSON><PERSON></a>, Chinese warlord", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%BC_Bu\" title=\"<PERSON>ü Bu\"><PERSON><PERSON></a>, Chinese warlord", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%BC_Bu"}]}, {"year": "318", "text": "<PERSON>, emperor of the Jin Dynasty (b. 300)", "html": "318 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_<PERSON>\" title=\"Emperor <PERSON> of Jin\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Jin_Dynasty_(265-420)\" class=\"mw-redirect\" title=\"Jin Dynasty (265-420)\">Jin Dynasty</a> (b. 300)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_<PERSON>\" title=\"Emperor <PERSON> of Jin\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Jin_Dynasty_(265-420)\" class=\"mw-redirect\" title=\"Jin Dynasty (265-420)\">Jin Dynasty</a> (b. 300)", "links": [{"title": "Emperor <PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Jin Dynasty (265-420)", "link": "https://wikipedia.org/wiki/Jin_Dynasty_(265-420)"}]}, {"year": "999", "text": "<PERSON><PERSON><PERSON> the Pious, Duke of Bohemia (b. 932)", "html": "999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_the_Pious\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> II the Pious\"><PERSON><PERSON><PERSON> the Pious</a>, Duke of Bohemia (b. 932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_the_Pious\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> II the Pious\"><PERSON><PERSON><PERSON> <PERSON> the Pious</a>, Duke of Bohemia (b. 932)", "links": [{"title": "<PERSON><PERSON><PERSON> the Pious", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_the_Pious"}]}, {"year": "1045", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1009)", "html": "1045 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Suzaku\" title=\"Emperor <PERSON>-Suzaku\">Emperor <PERSON><PERSON>Su<PERSON>u</a> of Japan (b. 1009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Suzaku\" title=\"Emperor Go-Suzaku\">Emperor <PERSON><PERSON>Su<PERSON>u</a> of Japan (b. 1009)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>"}]}, {"year": "1065", "text": "<PERSON><PERSON><PERSON>, Count of Sponheim (b. c. 1010)", "html": "1065 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Sponheim\" title=\"<PERSON><PERSON><PERSON>, Count of Sponheim\"><PERSON><PERSON><PERSON>, Count of Sponheim</a> (b. c. 1010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Sponheim\" title=\"<PERSON><PERSON><PERSON>, Count of Sponheim\"><PERSON><PERSON><PERSON>, Count of Sponheim</a> (b. c. 1010)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Sponheim", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Sponheim"}]}, {"year": "1127", "text": "<PERSON>, German poet (b. 1060)", "html": "1127 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, German poet (b. 1060)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, German poet (b. 1060)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1165", "text": "Marshal <PERSON> of Armenia", "html": "1165 - Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Armenia\" title=\"<PERSON> of Armenia\"><PERSON> of Armenia</a>", "no_year_html": "Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Armenia\" title=\"<PERSON> of Armenia\"><PERSON> of Armenia</a>", "links": [{"title": "Stephen of Armenia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Armenia"}]}, {"year": "1259", "text": "<PERSON>, Count of Flanders", "html": "1259 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON>, Count of Flanders</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON>, Count of Flanders</a>", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Flanders"}]}, {"year": "1317", "text": "<PERSON>, Count of Clermont (b. 1256)", "html": "1317 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Clermont\" title=\"<PERSON>, Count of Clermont\"><PERSON>, Count of Clermont</a> (b. 1256)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Clermont\" title=\"<PERSON>, Count of Clermont\"><PERSON>, Count of Clermont</a> (b. 1256)", "links": [{"title": "<PERSON>, Count of Clermont", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_Cler<PERSON>"}]}, {"year": "1320", "text": "<PERSON>, Bishop of Kraków (b. 1250)", "html": "1320 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Kraków (b. 1250)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Kraków (b. 1250)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1333", "text": "<PERSON><PERSON>, Japanese priest, founder of Ni<PERSON>ren <PERSON>hoshu Buddhism (b. 1246)", "html": "1333 - <a href=\"https://wikipedia.org/wiki/Nikk%C5%8D_(priest)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (priest)\"><PERSON><PERSON></a>, Japanese priest, founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hu\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> Buddhism (b. 1246)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nikk%C5%8D_(priest)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (priest)\"><PERSON><PERSON></a>, Japanese priest, founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> Buddhism (b. 1246)", "links": [{"title": "<PERSON><PERSON><PERSON> (priest)", "link": "https://wikipedia.org/wiki/Nikk%C5%8D_(priest)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hu"}]}, {"year": "1520", "text": "<PERSON><PERSON><PERSON><PERSON>, Regent of Florence (b. 1472)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/Alfonsina_Orsini\" title=\"Alfonsina Orsini\"><PERSON><PERSON><PERSON><PERSON></a>, Regent of Florence (b. 1472)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonsina_Orsini\" title=\"Alfonsina Orsini\"><PERSON><PERSON><PERSON><PERSON></a>, Regent of Florence (b. 1472)", "links": [{"title": "Alfo<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alfonsina_Orsini"}]}, {"year": "1560", "text": "<PERSON><PERSON><PERSON><PERSON>, Florentine sculptor (b. 1493)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Florentine sculptor (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Florentine sculptor (b. 1493)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German politician (b. 1520)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/Bartholom%C3%A4us_Sastrow\" title=\"Bartholomäus Sastrow\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German politician (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartholom%C3%A4us_Sastrow\" title=\"Bartholomäus Sastrow\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German politician (b. 1520)", "links": [{"title": "Bartholomäus <PERSON>w", "link": "https://wikipedia.org/wiki/Bartholom%C3%A4us_Sastrow"}]}, {"year": "1623", "text": "<PERSON>, 1st Earl of Exeter, English soldier and politician, Lord Lieutenant of Northamptonshire (b. 1546)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Exeter\" title=\"<PERSON>, 1st Earl of Exeter\"><PERSON>, 1st Earl of Exeter</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Northamptonshire\" title=\"Lord Lieutenant of Northamptonshire\">Lord Lieutenant of Northamptonshire</a> (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Exeter\" title=\"<PERSON>, 1st Earl of Exeter\"><PERSON>, 1st Earl of Exeter</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Northamptonshire\" title=\"Lord Lieutenant of Northamptonshire\">Lord Lieutenant of Northamptonshire</a> (b. 1546)", "links": [{"title": "<PERSON>, 1st Earl of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Exeter"}, {"title": "Lord Lieutenant of Northamptonshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Northamptonshire"}]}, {"year": "1626", "text": "<PERSON>, Duke of Bavaria (b. 1548)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1548)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1642", "text": "<PERSON>, English bishop and academic (b. 1571)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and academic (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and academic (b. 1571)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1693", "text": "<PERSON>, French lawyer and author (b. 1624)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and author (b. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and author (b. 1624)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1736", "text": "<PERSON>, English astronomer and physicist (b. 1666)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English astronomer and physicist (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English astronomer and physicist (b. 1666)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>(scientist)"}]}, {"year": "1779", "text": "<PERSON>, English organist and composer (b. 1711)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (b. 1711)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON> Emperor of China (b. 1711)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Qi<PERSON><PERSON>_Emperor\" title=\"Qianlong Emperor\">Qianlong Emperor</a> of China (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qianlong_Emperor\" title=\"Qianlong Emperor\">Qianlong Emperor</a> of China (b. 1711)", "links": [{"title": "<PERSON><PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1801", "text": "<PERSON>, Polish-German painter and academic (b. 1726)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German painter and academic (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German painter and academic (b. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, German-Estonian linguist and author (b. 1737)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German-Estonian linguist and author (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German-Estonian linguist and author (b. 1737)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, English author (b. 1764)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON> of Sweden (b. 1778)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Gustav_IV_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"Gustav IV Adolf of Sweden\"><PERSON> IV Adolf of Sweden</a> (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustav_IV_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"Gustav IV Adolf of Sweden\"><PERSON> IV Adolf of Sweden</a> (b. 1778)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1849", "text": "<PERSON>, Mexican general and 16th president (1845-1846) (b. 1797)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(President_of_Mexico)\" class=\"mw-redirect\" title=\"<PERSON> (President of Mexico)\"><PERSON></a>, Mexican general and 16th president (1845-1846) (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(President_of_Mexico)\" class=\"mw-redirect\" title=\"<PERSON> (President of Mexico)\"><PERSON></a>, Mexican general and 16th president (1845-1846) (b. 1797)", "links": [{"title": "<PERSON> (President of Mexico)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(President_of_Mexico)"}]}, {"year": "1862", "text": "<PERSON>, Spanish playwright and politician, Prime Minister of Spain (b. 1787)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Francisco_de_Paula_Mart%C3%ADnez_de_la_Rosa_y_Berdejo\" class=\"mw-redirect\" title=\"<PERSON> y Berdejo\"><PERSON> y <PERSON></a>, Spanish playwright and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_Paula_Mart%C3%ADnez_de_la_Rosa_y_Berdejo\" class=\"mw-redirect\" title=\"<PERSON> y Berdejo\"><PERSON> y <PERSON></a>, Spanish playwright and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (b. 1787)", "links": [{"title": "<PERSON> de la Rosa y Berdejo", "link": "https://wikipedia.org/wiki/Francisco_de_Paula_Mart%C3%ADnez_<PERSON>_<PERSON>_<PERSON>_y_Be<PERSON>jo"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1864", "text": "<PERSON><PERSON>, Serbian philologist and linguist (b. 1787)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Vuk_Karad%C5%BEi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian philologist and linguist (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vuk_Karad%C5%BEi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian philologist and linguist (b. 1787)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vuk_Karad%C5%BEi%C4%87"}]}, {"year": "1871", "text": "<PERSON>, German-American businessman, founded Steinway & Sons (b. 1797)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>way_%26_Sons\" title=\"Steinway &amp; Sons\">Steinway &amp; Sons</a> (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>way_%26_Sons\" title=\"Steinway &amp; Sons\">Steinway &amp; Sons</a> (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Steinway & Sons", "link": "https://wikipedia.org/wiki/<PERSON>way_%26_Sons"}]}, {"year": "1873", "text": "<PERSON>, Irish author (b. 1814)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON> (b. 1792)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_IX\" title=\"Pope Pius IX\"><PERSON> <PERSON></a> (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\"><PERSON> <PERSON></a> (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American story writer and journalist  (b. 1849)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American story writer and journalist (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American story writer and journalist (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Italian physicist and engineer (b. 1847)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Galileo_Ferraris\" title=\"Galileo Ferraris\">Galileo Ferraris</a>, Italian physicist and engineer (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Galileo_Ferraris\" title=\"Galileo Ferraris\">Galileo Ferraris</a>, Italian physicist and engineer (b. 1847)", "links": [{"title": "Galileo Ferraris", "link": "https://wikipedia.org/wiki/Galileo_Ferraris"}]}, {"year": "1919", "text": "<PERSON>, English-American lieutenant, Medal of Honor recipient (b. 1841)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1920", "text": "<PERSON>, Russian admiral and explorer (b. 1874)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian admiral and explorer (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian admiral and explorer (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian journalist, judge, and politician (b. 1850)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, judge, and politician (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, judge, and politician (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American politician (b. 1845)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American lawyer and politician, 38th United States Secretary of State, Nobel Prize laureate (b. 1845)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Root\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1845)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Root"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1938", "text": "<PERSON>, American businessman, founded the Firestone Tire and Rubber Company (b. 1868)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>stone\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Firestone_Tire_and_Rubber_Company\" title=\"Firestone Tire and Rubber Company\">Firestone Tire and Rubber Company</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>stone\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Firestone_Tire_and_Rubber_Company\" title=\"Firestone Tire and Rubber Company\">Firestone Tire and Rubber Company</a> (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>stone"}, {"title": "Firestone Tire and Rubber Company", "link": "https://wikipedia.org/wiki/Firestone_Tire_and_Rubber_Company"}]}, {"year": "1939", "text": "<PERSON>, Russian painter and illustrator (b. 1886)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and illustrator (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and illustrator (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON> (Irish republican), Executed Irish Republican (b. 1910)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> (b. 1910)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}]}, {"year": "1940", "text": "<PERSON> (Irish republican), Executed Irish Republican (b. 1907)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> (b. 1907)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}]}, {"year": "1942", "text": "<PERSON>, Russian illustrator and stage designer (b. 1876)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian illustrator and stage designer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian illustrator and stage designer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Italian soprano and actress (b. 1874)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1874)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Nap_<PERSON><PERSON><PERSON>\" title=\"Nap <PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nap_<PERSON><PERSON><PERSON>\" title=\"Nap <PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nap_<PERSON><PERSON>ie"}]}, {"year": "1959", "text": "<PERSON>, South African minister and politician, 5th Prime Minister of South Africa (b. 1874)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African minister and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African minister and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}]}, {"year": "1959", "text": "<PERSON> <PERSON>, American singer and guitarist (b. 1926)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Guitar_Slim\" title=\"Guitar Slim\">Guitar Slim</a>, American singer and guitarist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guitar_Slim\" title=\"Guitar Slim\">Guitar Slim</a>, American singer and guitarist (b. 1926)", "links": [{"title": "Guitar Slim", "link": "https://wikipedia.org/wiki/Guitar_Slim"}]}, {"year": "1960", "text": "<PERSON>, Russian physicist and academic (b. 1903)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Italian cyclist and manager (b. 1902)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Learco_G<PERSON>\" title=\"Learco G<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist and manager (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Learco_G<PERSON>\" title=\"Learco G<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist and manager (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Learco_Guerra"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek captain and politician, 133rd Prime Minister of Greece (b. 1894)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Sofok<PERSON>_Venizelos\" title=\"Sofok<PERSON> Venizel<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek captain and politician, 133rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sofok<PERSON>_Venizelos\" title=\"Sofok<PERSON> Venizelos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek captain and politician, 133rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1894)", "links": [{"title": "Sofok<PERSON>", "link": "https://wikipedia.org/wiki/Sofoklis_Venizelos"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1968", "text": "<PERSON>, American actor and screenwriter (b. 1931)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1931)\" title=\"<PERSON> (actor, born 1931)\"><PERSON></a>, American actor and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1931)\" title=\"<PERSON> (actor, born 1931)\"><PERSON></a>, American actor and screenwriter (b. 1931)", "links": [{"title": "<PERSON> (actor, born 1931)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1931)"}]}, {"year": "1972", "text": "<PERSON>, American director and screenwriter (b. 1896)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German SS officer and physician (b. 1911)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and physician (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and physician (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Senegalese historian, anthropologist, and physicist (b. 1923)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chei<PERSON>_<PERSON>_<PERSON>\" title=\"Chei<PERSON>\">Ch<PERSON><PERSON> <PERSON></a>, Senegalese historian, anthropologist, and physicist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ei<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ei<PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Senegalese historian, anthropologist, and physicist (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ei<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American computer scientist and academic (b. 1922)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Filipino general (b. 1905)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Filipino general (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Filipino general (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Israeli colonel (b. 1920)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli colonel (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli colonel (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Polish composer and conductor (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Witold_Lutos%C5%82awski\" title=\"Wito<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish composer and conductor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Witold_Lutos%C5%82awski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish composer and conductor (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Witold_Lutos%C5%82awski"}]}, {"year": "1999", "text": "<PERSON> <PERSON> Jordan (b. 1935)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> of Jordan\">King <PERSON> of Jordan</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> of Jordan\">King <PERSON> of Jordan</a> (b. 1935)", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/Hussein_of_Jordan"}]}, {"year": "1999", "text": "<PERSON>, American actor, pianist, and composer (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bobby Troup\"><PERSON></a>, American actor, pianist, and composer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bobby Troup\"><PERSON></a>, American actor, pianist, and composer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>roup"}]}, {"year": "2000", "text": "<PERSON>, Canadian magician and politician (b. 1947)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian magician and politician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian magician and politician (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American author and pilot (b. 1906)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and pilot (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and pilot (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Guatemalan author (b. 1921)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guatemalan author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guatemalan author (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Faroese engineer and politician, 5th Prime Minister of the Faroe Islands (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Atli_Dam\" title=\"Atli Dam\">Atli Dam</a>, Faroese engineer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atli_Dam\" title=\"Atli Dam\">Atli Dam</a>, Faroese engineer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1932)", "links": [{"title": "Atli Dam", "link": "https://wikipedia.org/wiki/Atli_Dam"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "2006", "text": "Princess <PERSON><PERSON><PERSON> of the Ottoman Empire (b. 1914)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Princess <PERSON><PERSON><PERSON>\">Princess <PERSON><PERSON><PERSON></a> of the Ottoman Empire (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Princess <PERSON><PERSON><PERSON>\">Princess <PERSON><PERSON><PERSON></a> of the Ottoman Empire (b. 1914)", "links": [{"title": "Princess <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American singer and pianist (b. 1924)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Blossom Dear<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer and pianist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Blossom Dear<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer and pianist (b. 1924)", "links": [{"title": "Blossom <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON>_<PERSON><PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Italian cyclist and coach (b. 1964)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist and coach (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist and coach (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Ballerini"}]}, {"year": "2012", "text": "<PERSON>, American soccer player and coach (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Croatian director and screenwriter (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian director and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian director and screenwriter (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krsto_Papi%C4%87"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American ice hockey player (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American golfer (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American psychologist and author (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American banker and politician, 9th United States Deputy Secretary of State (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State\" title=\"United States Deputy Secretary of State\">United States Deputy Secretary of State</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State\" title=\"United States Deputy Secretary of State\">United States Deputy Secretary of State</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Deputy Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State"}]}, {"year": "2017", "text": "<PERSON>, American actor (b. 1945)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1945)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2017", "text": "<PERSON>, Swedish academic (b. 1948)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish academic (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish academic (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON><PERSON>, Bulgarian philosopher (b. 1939)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian philosopher (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian philosopher (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American politician (b. 1926)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, English actor (b. 1936)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Polish politician, 3rd Prime Minister (b. 1930)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "2019", "text": "<PERSON>, American baseball player, coach, and manager (b. 1935)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Chinese ophthalmologist who initially warned about COVID-19 (b. 1986)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wenliang\"><PERSON></a>, Chinese ophthalmologist who initially warned about COVID-19 (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wenliang\"><PERSON></a>, Chinese ophthalmologist who initially warned about COVID-19 (b. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Welsh academic and politician (b. 1946)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh academic and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh academic and politician (b. 1946)", "links": [{"title": "Dafydd Elis-Thomas", "link": "https://wikipedia.org/wiki/<PERSON>fydd_Elis-Thomas"}]}, {"year": "2025", "text": "<PERSON>, American actor and singer (b. 1939)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (b. 1939)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}]}}