{"date": "May 29", "url": "https://wikipedia.org/wiki/May_29", "data": {"Events": [{"year": "363", "text": "The Roman emperor <PERSON> defeats the Sasanian army in the Battle of Ctesiphon, under the walls of the Sasanian capital, but is unable to take the city.", "html": "363 - The <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Military_of_the_Sasanian_Empire\" title=\"Military of the Sasanian Empire\">Sasanian army</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ctesiphon_(363)\" title=\"Battle of Ctesiphon (363)\">Battle of Ctesiphon</a>, under the walls of the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian</a> capital, but is unable to take the city.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Military_of_the_Sasanian_Empire\" title=\"Military of the Sasanian Empire\">Sasanian army</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ctesiphon_(363)\" title=\"Battle of Ctesiphon (363)\">Battle of Ctesiphon</a>, under the walls of the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian</a> capital, but is unable to take the city.", "links": [{"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}, {"title": "Military of the Sasanian Empire", "link": "https://wikipedia.org/wiki/Military_of_the_Sasanian_Empire"}, {"title": "Battle of Ctesiphon (363)", "link": "https://wikipedia.org/wiki/Battle_of_Ctesiphon_(363)"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}]}, {"year": "1108", "text": "Battle of Uclés: Almoravid troops under the command of <PERSON><PERSON> defeat a Castile and León alliance under the command of Prince <PERSON><PERSON>.", "html": "1108 - <a href=\"https://wikipedia.org/wiki/Battle_of_Ucl%C3%A9s_(1108)\" title=\"Battle of Uclés (1108)\">Battle of Uclés</a>: <a href=\"https://wikipedia.org/wiki/Almoravid_dynasty\" title=\"Almoravid dynasty\">Almoravid</a> troops under the command of <PERSON><PERSON> ibn <PERSON> defeat a <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> alliance under the command of Prince <a href=\"https://wikipedia.org/wiki/Sancho_Alf%C3%B3nsez\" title=\"Sancho Alfónsez\">Sancho Alfónsez</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Ucl%C3%A9s_(1108)\" title=\"Battle of Uclés (1108)\">Battle of Uclés</a>: <a href=\"https://wikipedia.org/wiki/Almoravid_dynasty\" title=\"Almoravid dynasty\">Almoravid</a> troops under the command of <PERSON><PERSON> ibn <PERSON> defeat a <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> alliance under the command of Prince <a href=\"https://wikipedia.org/wiki/Sancho_Alf%C3%B3nsez\" title=\"Sancho Alfónsez\">Sancho Alfónsez</a>.", "links": [{"title": "Battle of Uclés (1108)", "link": "https://wikipedia.org/wiki/Battle_of_Ucl%C3%A9s_(1108)"}, {"title": "Almoravid dynasty", "link": "https://wikipedia.org/wiki/Almoravid_dynasty"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sancho_Alf%C3%B3nsez"}]}, {"year": "1167", "text": "Battle of Monte Porzio: A Roman army supporting Pope <PERSON> is defeated by <PERSON> of Buch and <PERSON><PERSON> of Dassel.", "html": "1167 - <a href=\"https://wikipedia.org/wiki/Battle_of_Monte_Porzio\" title=\"Battle of Monte Porzio\">Battle of Monte Porzio</a>: A <a href=\"https://wikipedia.org/wiki/Commune_of_Rome\" title=\"Commune of Rome\">Roman</a> army supporting <a href=\"https://wikipedia.org/wiki/<PERSON>_Alexander_III\" title=\"Pope Alexander III\">Pope Alexander III</a> is defeated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Mainz)\" title=\"<PERSON> (archbishop of Mainz)\"><PERSON> of Buch</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_<PERSON>sel\" title=\"<PERSON><PERSON> of Dassel\"><PERSON><PERSON> of Dassel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Monte_Porzio\" title=\"Battle of Monte Porzio\">Battle of Monte Porzio</a>: A <a href=\"https://wikipedia.org/wiki/Commune_of_Rome\" title=\"Commune of Rome\">Roman</a> army supporting <a href=\"https://wikipedia.org/wiki/<PERSON>_Alexander_III\" title=\"Pope Alexander III\">Pope Alexander III</a> is defeated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Mainz)\" title=\"<PERSON> (archbishop of Mainz)\"><PERSON> of Buch</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Dassel\" title=\"<PERSON><PERSON> of Dassel\"><PERSON><PERSON> of Dassel</a>.", "links": [{"title": "Battle of Monte Porzio", "link": "https://wikipedia.org/wiki/Battle_of_Monte_Porzio"}, {"title": "Commune of Rome", "link": "https://wikipedia.org/wiki/Commune_of_Rome"}, {"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> (archbishop of Mainz)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Mainz)"}, {"title": "<PERSON><PERSON> of Dassel", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Das<PERSON>"}]}, {"year": "1176", "text": "Battle of Legnano: The Lombard League defeats Emperor <PERSON>.", "html": "1176 - <a href=\"https://wikipedia.org/wiki/Battle_of_Legnano\" title=\"Battle of Legnano\">Battle of Legnano</a>: The <a href=\"https://wikipedia.org/wiki/Lombard_League\" title=\"Lombard League\">Lombard League</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"Frederick I, Holy Roman Emperor\">Emperor <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Legnano\" title=\"Battle of Legnano\">Battle of Legnano</a>: The <a href=\"https://wikipedia.org/wiki/Lombard_League\" title=\"Lombard League\">Lombard League</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"Frederick I, Holy Roman Emperor\">Emperor <PERSON> I</a>.", "links": [{"title": "Battle of Legnano", "link": "https://wikipedia.org/wiki/Battle_of_Legnano"}, {"title": "Lombard League", "link": "https://wikipedia.org/wiki/Lombard_League"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1233", "text": "Mongol-Jin war: The Mongols entered Kaifeng after a successful siege and began looting in the fallen capital of the Jin dynasty.", "html": "1233 - <a href=\"https://wikipedia.org/wiki/Mongol_conquest_of_the_Jin_dynasty\" title=\"Mongol conquest of the Jin dynasty\">Mongol-Jin war</a>: The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> entered <a href=\"https://wikipedia.org/wiki/Kaifeng\" title=\"Kaifeng\">Kaifeng</a> after a <a href=\"https://wikipedia.org/wiki/Mongol_siege_of_Kaifeng\" class=\"mw-redirect\" title=\"Mongol siege of Kaifeng\">successful siege</a> and began looting in the fallen capital of the <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)\" title=\"Jin dynasty (1115-1234)\">Jin dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongol_conquest_of_the_Jin_dynasty\" title=\"Mongol conquest of the Jin dynasty\">Mongol-Jin war</a>: The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> entered <a href=\"https://wikipedia.org/wiki/Kaifeng\" title=\"Kaifeng\">Kaifeng</a> after a <a href=\"https://wikipedia.org/wiki/Mongol_siege_of_Kaifeng\" class=\"mw-redirect\" title=\"Mongol siege of Kaifeng\">successful siege</a> and began looting in the fallen capital of the <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)\" title=\"Jin dynasty (1115-1234)\">Jin dynasty</a>.", "links": [{"title": "Mongol conquest of the Jin dynasty", "link": "https://wikipedia.org/wiki/Mongol_conquest_of_the_Jin_dynasty"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}, {"title": "Kaifeng", "link": "https://wikipedia.org/wiki/Kai<PERSON>"}, {"title": "Mongol siege of Kaifeng", "link": "https://wikipedia.org/wiki/Mongol_siege_of_Kaifeng"}, {"title": "Jin dynasty (1115-1234)", "link": "https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)"}]}, {"year": "1328", "text": "<PERSON> is crowned King of France.", "html": "1328 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VI of France\"><PERSON> VI</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_French_monarchs\" title=\"List of French monarchs\">King of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> VI</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_French_monarchs\" title=\"List of French monarchs\">King of France</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Philip_<PERSON>_of_France"}, {"title": "List of French monarchs", "link": "https://wikipedia.org/wiki/List_of_French_monarchs"}]}, {"year": "1416", "text": "Battle of Gallipoli: The Venetians under <PERSON> defeat a much larger Ottoman fleet off Gallipoli.", "html": "1416 - <a href=\"https://wikipedia.org/wiki/Battle_of_Gallipoli_(1416)\" title=\"Battle of Gallipoli (1416)\">Battle of Gallipoli</a>: The <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetians</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(admiral)\" title=\"<PERSON> (admiral)\"><PERSON></a> defeat a much larger <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> fleet off <a href=\"https://wikipedia.org/wiki/Gelibolu\" title=\"Gelibolu\">Gallipoli</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Gallipoli_(1416)\" title=\"Battle of Gallipoli (1416)\">Battle of Gallipoli</a>: The <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetians</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(admiral)\" title=\"<PERSON> (admiral)\"><PERSON></a> defeat a much larger <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> fleet off <a href=\"https://wikipedia.org/wiki/Gelibolu\" title=\"Gelibolu\">Gallipoli</a>.", "links": [{"title": "Battle of Gallipoli (1416)", "link": "https://wikipedia.org/wiki/Battle_of_Gallipoli_(1416)"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "<PERSON> (admiral)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(admiral)"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Gelibolu", "link": "https://wikipedia.org/wiki/Gelibolu"}]}, {"year": "1453", "text": "Fall of Constantinople: Ottoman armies under <PERSON> <PERSON><PERSON><PERSON> capture Constantinople after a 53-day siege, ending the Roman Empire after over 2,000 years.", "html": "1453 - <a href=\"https://wikipedia.org/wiki/Fall_of_Constantinople\" title=\"Fall of Constantinople\">Fall of Constantinople</a>: <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> armies under <a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">Sultan</a> <a href=\"https://wikipedia.org/wiki/Mehm<PERSON>_the_Conqueror\" class=\"mw-redirect\" title=\"Mehm<PERSON> the Conqueror\">Mehm<PERSON> II</a> capture <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> after a 53-day <a href=\"https://wikipedia.org/wiki/Siege\" title=\"Siege\">siege</a>, ending the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a> after over 2,000 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fall_of_Constantinople\" title=\"Fall of Constantinople\">Fall of Constantinople</a>: <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> armies under <a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">Sultan</a> <a href=\"https://wikipedia.org/wiki/Mehmed_the_Conqueror\" class=\"mw-redirect\" title=\"Mehm<PERSON> the Conqueror\">Mehmed II</a> capture <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> after a 53-day <a href=\"https://wikipedia.org/wiki/Siege\" title=\"Siege\">siege</a>, ending the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a> after over 2,000 years.", "links": [{"title": "Fall of Constantinople", "link": "https://wikipedia.org/wiki/Fall_of_Constantinople"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sultan"}, {"title": "<PERSON><PERSON><PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Conqueror"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Siege", "link": "https://wikipedia.org/wiki/Siege"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}]}, {"year": "1658", "text": "Battle of Samugarh: decisive battle in the struggle for the throne during the Mughal war of succession (1658-1659).", "html": "1658 - <a href=\"https://wikipedia.org/wiki/Battle_of_Samugarh\" title=\"Battle of Samugarh\">Battle of Samugarh</a>: decisive battle in the struggle for the throne during the <a href=\"https://wikipedia.org/wiki/Mughal_war_of_succession_(1658%E2%80%931659)\" title=\"Mughal war of succession (1658-1659)\">Mughal war of succession (1658-1659)</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Samugarh\" title=\"Battle of Samugarh\">Battle of Samugarh</a>: decisive battle in the struggle for the throne during the <a href=\"https://wikipedia.org/wiki/Mughal_war_of_succession_(1658%E2%80%931659)\" title=\"Mughal war of succession (1658-1659)\">Mughal war of succession (1658-1659)</a>.", "links": [{"title": "Battle of Samugarh", "link": "https://wikipedia.org/wiki/Battle_of_Samugarh"}, {"title": "Mughal war of succession (1658-1659)", "link": "https://wikipedia.org/wiki/Mughal_war_of_succession_(1658%E2%80%931659)"}]}, {"year": "1660", "text": "English Restoration: <PERSON> is restored to the throne of England, Scotland and Ireland.", "html": "1660 - <a href=\"https://wikipedia.org/wiki/Restoration_(England)\" class=\"mw-redirect\" title=\"Restoration (England)\">English Restoration</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"Charles II of England\"><PERSON> II</a> is restored to the throne of <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>, <a href=\"https://wikipedia.org/wiki/Scotland\" title=\"Scotland\">Scotland</a> and <a href=\"https://wikipedia.org/wiki/Ireland\" title=\"Ireland\">Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Restoration_(England)\" class=\"mw-redirect\" title=\"Restoration (England)\">English Restoration</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles II of England\"><PERSON> II</a> is restored to the throne of <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>, <a href=\"https://wikipedia.org/wiki/Scotland\" title=\"Scotland\">Scotland</a> and <a href=\"https://wikipedia.org/wiki/Ireland\" title=\"Ireland\">Ireland</a>.", "links": [{"title": "Restoration (England)", "link": "https://wikipedia.org/wiki/Restoration_(England)"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}, {"title": "Scotland", "link": "https://wikipedia.org/wiki/Scotland"}, {"title": "Ireland", "link": "https://wikipedia.org/wiki/Ireland"}]}, {"year": "1733", "text": "The right of settlers in New France to enslave natives is upheld at Quebec City.", "html": "1733 - The right of settlers in <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> to <a href=\"https://wikipedia.org/wiki/Slavery_in_Canada_(New_France)\" class=\"mw-redirect\" title=\"Slavery in Canada (New France)\">enslave natives</a> is upheld at <a href=\"https://wikipedia.org/wiki/Quebec_City\" title=\"Quebec City\">Quebec City</a>.", "no_year_html": "The right of settlers in <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> to <a href=\"https://wikipedia.org/wiki/Slavery_in_Canada_(New_France)\" class=\"mw-redirect\" title=\"Slavery in Canada (New France)\">enslave natives</a> is upheld at <a href=\"https://wikipedia.org/wiki/Quebec_City\" title=\"Quebec City\">Quebec City</a>.", "links": [{"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}, {"title": "Slavery in Canada (New France)", "link": "https://wikipedia.org/wiki/Slavery_in_Canada_(New_France)"}, {"title": "Quebec City", "link": "https://wikipedia.org/wiki/Quebec_City"}]}, {"year": "1780", "text": "American Revolutionary War: At the Waxhaws Massacre, the British continue attacking after the Continentals lay down their arms, killing 113 and critically wounding all but 53 that remained.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: At the <a href=\"https://wikipedia.org/wiki/Waxhaws_Massacre\" class=\"mw-redirect\" title=\"Waxhaws Massacre\">Waxhaws Massacre</a>, the British continue attacking after the Continentals lay down their arms, killing 113 and critically wounding all but 53 that remained.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: At the <a href=\"https://wikipedia.org/wiki/Waxhaws_Massacre\" class=\"mw-redirect\" title=\"Waxhaws Massacre\">Waxhaws Massacre</a>, the British continue attacking after the Continentals lay down their arms, killing 113 and critically wounding all but 53 that remained.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Waxhaws Massacre", "link": "https://wikipedia.org/wiki/Waxhaws_Massacre"}]}, {"year": "1790", "text": "Rhode Island becomes the last of North America's original Thirteen Colonies to ratify the Constitution and become one of the United States.", "html": "1790 - <a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island</a> becomes the last of North America's original <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">Constitution</a> and become one of the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island</a> becomes the last of North America's original <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">Constitution</a> and become one of the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "links": [{"title": "Rhode Island", "link": "https://wikipedia.org/wiki/Rhode_Island"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1798", "text": "United Irishmen Rebellion: Between 300 and 500 United Irishmen are executed as rebels by the British Army in County Kildare, Ireland.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">United Irishmen Rebellion</a>: Between 300 and 500 <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irishmen</a> <a href=\"https://wikipedia.org/wiki/Gibbet_Rath_massacre\" class=\"mw-redirect\" title=\"Gibbet Rath massacre\">are executed as rebels</a> by the British Army in <a href=\"https://wikipedia.org/wiki/County_Kildare\" title=\"County Kildare\">County Kildare</a>, Ireland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">United Irishmen Rebellion</a>: Between 300 and 500 <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irishmen</a> <a href=\"https://wikipedia.org/wiki/Gibbet_Rath_massacre\" class=\"mw-redirect\" title=\"Gibbet Rath massacre\">are executed as rebels</a> by the British Army in <a href=\"https://wikipedia.org/wiki/County_Kildare\" title=\"County Kildare\">County Kildare</a>, Ireland.", "links": [{"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}, {"title": "Society of United Irishmen", "link": "https://wikipedia.org/wiki/Society_of_United_Irishmen"}, {"title": "Gibbet Rath massacre", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>_massacre"}, {"title": "County Kildare", "link": "https://wikipedia.org/wiki/County_Kildare"}]}, {"year": "1807", "text": "<PERSON> became Sultan of the Ottoman Empire and <PERSON><PERSON><PERSON> of Islam.", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" title=\"Mustafa IV\"><PERSON> IV</a> became <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> and <a href=\"https://wikipedia.org/wiki/Caliph_of_Islam\" class=\"mw-redirect\" title=\"Caliph of Islam\"><PERSON><PERSON><PERSON> of Islam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" title=\"Mustafa IV\"><PERSON> IV</a> became <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> and <a href=\"https://wikipedia.org/wiki/Caliph_of_Islam\" class=\"mw-redirect\" title=\"Caliph of Islam\"><PERSON><PERSON><PERSON> of Islam</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_IV"}, {"title": "Sultan of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire"}, {"title": "Cal<PERSON>h of Islam", "link": "https://wikipedia.org/wiki/Caliph_of_Islam"}]}, {"year": "1825", "text": "The Coronation of <PERSON> of France takes place in Reims Cathedral, the last ever coronation of a French monarch.", "html": "1825 - The <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Coronation of <PERSON> X\">Coronation of <PERSON> X</a> of France takes place in <a href=\"https://wikipedia.org/wiki/Reims_Cathedral\" title=\"Reims Cathedral\">Reims Cathedral</a>, the last ever coronation of a French monarch.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Coronation of Charles X\">Coronation of <PERSON> X</a> of France takes place in <a href=\"https://wikipedia.org/wiki/Reims_Cathedral\" title=\"Reims Cathedral\">Reims Cathedral</a>, the last ever coronation of a French monarch.", "links": [{"title": "Coronation of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Reims Cathedral", "link": "https://wikipedia.org/wiki/Reims_Cathedral"}]}, {"year": "1851", "text": "Sojourner Truth delivers her famous Ain't I a Woman? speech at the Woman’s Rights Convention in Akron, Ohio.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Sojourner_Truth\" title=\"Sojourner Truth\">Sojourner Truth</a> delivers her famous <i><a href=\"https://wikipedia.org/wiki/Ain%27t_I_a_Woman%3F\" title=\"Ain't I a Woman?\">Ain't I a Woman?</a></i> speech at the Woman’s Rights Convention in Akron, Ohio.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sojourner_Truth\" title=\"Sojourner Truth\">Sojourner Truth</a> delivers her famous <i><a href=\"https://wikipedia.org/wiki/Ain%27t_I_a_Woman%3F\" title=\"Ain't I a Woman?\">Ain't I a Woman?</a></i> speech at the Woman’s Rights Convention in Akron, Ohio.", "links": [{"title": "Sojourner Truth", "link": "https://wikipedia.org/wiki/Sojourner_Truth"}, {"title": "Ain't I a Woman?", "link": "https://wikipedia.org/wiki/Ain%27t_I_a_Woman%3F"}]}, {"year": "1852", "text": "<PERSON> leaves New York after her two-year American tour.", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York</a> after her <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_tour_of_America,_1850%E2%80%9352\" class=\"mw-redirect\" title=\"<PERSON> tour of America, 1850-52\">two-year American tour</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York</a> after her <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_tour_of_America,_1850%E2%80%9352\" class=\"mw-redirect\" title=\"<PERSON> tour of America, 1850-52\">two-year American tour</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "<PERSON> tour of America, 1850-52", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_tour_of_America,_1850%E2%80%9352"}]}, {"year": "1861", "text": "The Hong Kong General Chamber of Commerce is founded, in Hong Kong.", "html": "1861 - The <a href=\"https://wikipedia.org/wiki/Hong_Kong_General_Chamber_of_Commerce\" title=\"Hong Kong General Chamber of Commerce\">Hong Kong General Chamber of Commerce</a> is founded, in Hong Kong.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hong_Kong_General_Chamber_of_Commerce\" title=\"Hong Kong General Chamber of Commerce\">Hong Kong General Chamber of Commerce</a> is founded, in Hong Kong.", "links": [{"title": "Hong Kong General Chamber of Commerce", "link": "https://wikipedia.org/wiki/Hong_Kong_General_Chamber_of_Commerce"}]}, {"year": "1864", "text": "Emperor <PERSON> of Mexico arrives in Mexico for the first time.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Emperor_of_Mexico\" title=\"Emperor of Mexico\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Maximilian_I_of_Mexico\" title=\"Maximilian I of Mexico\"><PERSON> of Mexico</a> arrives in Mexico for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_of_Mexico\" title=\"Emperor of Mexico\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Maximilian_I_of_Mexico\" title=\"Maximilian I of Mexico\"><PERSON> of Mexico</a> arrives in Mexico for the first time.", "links": [{"title": "Emperor of Mexico", "link": "https://wikipedia.org/wiki/Emperor_of_Mexico"}, {"title": "<PERSON> I of Mexico", "link": "https://wikipedia.org/wiki/Maximilian_I_of_Mexico"}]}, {"year": "1867", "text": "The Austro-Hungarian Compromise of 1867 (\"the Compromise\") is born through Act 12, which establishes the Austro-Hungarian Empire.", "html": "1867 - The <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Compromise_of_1867\" title=\"Austro-Hungarian Compromise of 1867\">Austro-Hungarian Compromise of 1867</a> (\"the Compromise\") is born through Act 12, which establishes the <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian Empire</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Compromise_of_1867\" title=\"Austro-Hungarian Compromise of 1867\">Austro-Hungarian Compromise of 1867</a> (\"the Compromise\") is born through Act 12, which establishes the <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian Empire</a>.", "links": [{"title": "Austro-Hungarian Compromise of 1867", "link": "https://wikipedia.org/wiki/Austro-Hungarian_Compromise_of_1867"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}]}, {"year": "1886", "text": "The pharmacist <PERSON> places his first advertisement for Coca-Cola, which appeared in The Atlanta Journal.", "html": "1886 - The <a href=\"https://wikipedia.org/wiki/Pharmacist\" title=\"Pharmacist\">pharmacist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> places his first <a href=\"https://wikipedia.org/wiki/Advertising\" title=\"Advertising\">advertisement</a> for <a href=\"https://wikipedia.org/wiki/Coca-Cola\" title=\"Coca-Cola\">Coca-Cola</a>, which appeared in <i><a href=\"https://wikipedia.org/wiki/The_Atlanta_Journal\" class=\"mw-redirect\" title=\"The Atlanta Journal\">The Atlanta Journal</a></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pharmacist\" title=\"Pharmacist\">pharmacist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> places his first <a href=\"https://wikipedia.org/wiki/Advertising\" title=\"Advertising\">advertisement</a> for <a href=\"https://wikipedia.org/wiki/Coca-Cola\" title=\"Coca-Cola\">Coca-Cola</a>, which appeared in <i><a href=\"https://wikipedia.org/wiki/The_Atlanta_Journal\" class=\"mw-redirect\" title=\"The Atlanta Journal\">The Atlanta Journal</a></i>.", "links": [{"title": "Pharmacist", "link": "https://wikipedia.org/wiki/Pharmacist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Advertising", "link": "https://wikipedia.org/wiki/Advertising"}, {"title": "Coca-Cola", "link": "https://wikipedia.org/wiki/Coca-Cola"}, {"title": "The Atlanta Journal", "link": "https://wikipedia.org/wiki/The_Atlanta_Journal"}]}, {"year": "1900", "text": "N'Djamena is founded as Fort-Lamy by the French commander <PERSON><PERSON>.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/N%27Djamena\" title=\"N'Djamena\">N'Djamena</a> is founded as Fort-Lamy by the French commander <a href=\"https://wikipedia.org/wiki/%C3%89mile_Gentil\" title=\"Émile <PERSON>til\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%27Djamena\" title=\"N'Djamena\">N'Djamena</a> is founded as Fort-Lamy by the French commander <a href=\"https://wikipedia.org/wiki/%C3%89mile_Gentil\" title=\"Émile <PERSON>til\"><PERSON><PERSON></a>.", "links": [{"title": "N'Djamena", "link": "https://wikipedia.org/wiki/N%27Djamena"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Gentil"}]}, {"year": "1903", "text": "In the May Coup, <PERSON>, King of Serbia, and Queen <PERSON><PERSON>, are assassinated in Belgrade by the Black Hand (Crna Ruka) organization.", "html": "1903 - In the <a href=\"https://wikipedia.org/wiki/May_<PERSON>_(Serbia)\" title=\"<PERSON> (Serbia)\">May <PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia\" title=\"<PERSON> of Serbia\"><PERSON></a>, King of Serbia, and <a href=\"https://wikipedia.org/wiki/Draga_Ma%C5%A1in\" title=\"Draga Mašin\">Queen <PERSON><PERSON></a>, are <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinated</a> in Belgrade by the <a href=\"https://wikipedia.org/wiki/Black_Hand_(Serbia)\" title=\"Black Hand (Serbia)\">Black Hand</a> (<i>Crna Ruka</i>) organization.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/May_<PERSON>_(Serbia)\" title=\"<PERSON> (Serbia)\">May <PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia\" title=\"<PERSON> of Serbia\"><PERSON></a>, King of Serbia, and <a href=\"https://wikipedia.org/wiki/Draga_Ma%C5%A1in\" title=\"Draga Mašin\">Queen <PERSON><PERSON></a>, are <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinated</a> in Belgrade by the <a href=\"https://wikipedia.org/wiki/Black_Hand_(Serbia)\" title=\"Black Hand (Serbia)\">Black Hand</a> (<i>Crna Ruka</i>) organization.", "links": [{"title": "<PERSON> (Serbia)", "link": "https://wikipedia.org/wiki/May_<PERSON><PERSON>_(Serbia)"}, {"title": "<PERSON> of Serbia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Draga_Ma%C5%A1in"}, {"title": "Assassination", "link": "https://wikipedia.org/wiki/Assassination"}, {"title": "Black Hand (Serbia)", "link": "https://wikipedia.org/wiki/Black_Hand_(Serbia)"}]}, {"year": "1913", "text": "<PERSON>'s ballet score The Rite of Spring receives its premiere performance in Paris, France, provoking a riot.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Ballet\" title=\"Ballet\">ballet</a> score <i><a href=\"https://wikipedia.org/wiki/The_Rite_of_Spring\" title=\"The Rite of Spring\">The Rite of Spring</a></i> receives its premiere performance in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris, France</a>, provoking a riot.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Ballet\" title=\"Ballet\">ballet</a> score <i><a href=\"https://wikipedia.org/wiki/The_Rite_of_Spring\" title=\"The Rite of Spring\">The Rite of Spring</a></i> receives its premiere performance in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris, France</a>, provoking a riot.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ballet", "link": "https://wikipedia.org/wiki/Ballet"}, {"title": "The Rite of Spring", "link": "https://wikipedia.org/wiki/The_Rite_of_Spring"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1914", "text": "The Ocean liner RMS Empress of Ireland sinks in the Gulf of Saint Lawrence with the loss of 1,012 lives.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">Ocean liner</a> <a href=\"https://wikipedia.org/wiki/RMS_Empress_of_Ireland\" title=\"RMS Empress of Ireland\">R<PERSON> <i>Empress of Ireland</i></a> sinks in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Saint_Lawrence\" class=\"mw-redirect\" title=\"Gulf of Saint Lawrence\">Gulf of Saint Lawrence</a> with the loss of 1,012 lives.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">Ocean liner</a> <a href=\"https://wikipedia.org/wiki/R<PERSON>_Empress_of_Ireland\" title=\"RMS Empress of Ireland\">R<PERSON> <i>Empress of Ireland</i></a> sinks in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Saint_Lawrence\" class=\"mw-redirect\" title=\"Gulf of Saint Lawrence\">Gulf of Saint Lawrence</a> with the loss of 1,012 lives.", "links": [{"title": "Ocean liner", "link": "https://wikipedia.org/wiki/Ocean_liner"}, {"title": "RMS Empress of Ireland", "link": "https://wikipedia.org/wiki/R<PERSON>_Empress_of_Ireland"}, {"title": "Gulf of Saint Lawrence", "link": "https://wikipedia.org/wiki/Gulf_of_Saint_Lawrence"}]}, {"year": "1918", "text": "Armenia defeats the Ottoman Army in the Battle of Sardarabad.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a> defeats the <a href=\"https://wikipedia.org/wiki/Military_of_the_Ottoman_Empire\" title=\"Military of the Ottoman Empire\">Ottoman Army</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sardarabad\" title=\"Battle of Sardarabad\">Battle of Sardarabad</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a> defeats the <a href=\"https://wikipedia.org/wiki/Military_of_the_Ottoman_Empire\" title=\"Military of the Ottoman Empire\">Ottoman Army</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sardarabad\" title=\"Battle of Sardarabad\">Battle of Sardarabad</a>.", "links": [{"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "Military of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Military_of_the_Ottoman_Empire"}, {"title": "Battle of Sardarabad", "link": "https://wikipedia.org/wiki/Battle_of_Sardarabad"}]}, {"year": "1919", "text": "<PERSON>'s theory of general relativity is tested (later confirmed) by <PERSON> and <PERSON>.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert Einstein\"><PERSON></a>'s theory of <a href=\"https://wikipedia.org/wiki/General_relativity\" title=\"General relativity\">general relativity</a> <a href=\"https://wikipedia.org/wiki/Eddington_experiment\" title=\"Eddington experiment\">is tested</a> (later confirmed) by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Albert Einstein\"><PERSON></a>'s theory of <a href=\"https://wikipedia.org/wiki/General_relativity\" title=\"General relativity\">general relativity</a> <a href=\"https://wikipedia.org/wiki/Eddington_experiment\" title=\"Eddington experiment\">is tested</a> (later confirmed) by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "General relativity", "link": "https://wikipedia.org/wiki/General_relativity"}, {"title": "<PERSON><PERSON> experiment", "link": "https://wikipedia.org/wiki/Eddington_experiment"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dington"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "The Louth flood of 1920 was a severe flash flooding in the Lincolnshire market town of Louth, resulting in 23 fatalities in 20 minutes. It has been described as one of the most significant flood disasters in the United Kingdom during the 20th century.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Louth_flood_of_1920\" class=\"mw-redirect\" title=\"Louth flood of 1920\">Louth flood of 1920</a> was a severe flash flooding in the <a href=\"https://wikipedia.org/wiki/Lincolnshire\" title=\"Lincolnshire\">Lincolnshire</a> market town of <a href=\"https://wikipedia.org/wiki/Louth,_Lincolnshire\" title=\"Louth, Lincolnshire\">Louth</a>, resulting in 23 fatalities in 20 minutes. It has been described as one of the most significant flood disasters in the United Kingdom during the 20th century.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Louth_flood_of_1920\" class=\"mw-redirect\" title=\"Louth flood of 1920\">Louth flood of 1920</a> was a severe flash flooding in the <a href=\"https://wikipedia.org/wiki/Lincolnshire\" title=\"Lincolnshire\">Lincolnshire</a> market town of <a href=\"https://wikipedia.org/wiki/Louth,_Lincolnshire\" title=\"Louth, Lincolnshire\">Louth</a>, resulting in 23 fatalities in 20 minutes. It has been described as one of the most significant flood disasters in the United Kingdom during the 20th century.", "links": [{"title": "Louth flood of 1920", "link": "https://wikipedia.org/wiki/Louth_flood_of_1920"}, {"title": "Lincolnshire", "link": "https://wikipedia.org/wiki/Lincolnshire"}, {"title": "Louth, Lincolnshire", "link": "https://wikipedia.org/wiki/Louth,_Lincolnshire"}]}, {"year": "1931", "text": "<PERSON>, a citizen of the United States, is executed by a Royal Italian Army firing squad for intent to kill <PERSON>.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Citizenship_of_the_United_States\" title=\"Citizenship of the United States\">citizen of the United States</a>, is executed by a <a href=\"https://wikipedia.org/wiki/Royal_Italian_Army\" title=\"Royal Italian Army\">Royal Italian Army</a> firing squad for intent to kill <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Citizenship_of_the_United_States\" title=\"Citizenship of the United States\">citizen of the United States</a>, is executed by a <a href=\"https://wikipedia.org/wiki/Royal_Italian_Army\" title=\"Royal Italian Army\">Royal Italian Army</a> firing squad for intent to kill <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Citizenship of the United States", "link": "https://wikipedia.org/wiki/Citizenship_of_the_United_States"}, {"title": "Royal Italian Army", "link": "https://wikipedia.org/wiki/Royal_Italian_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "World War I veterans begin to assemble in Washington, D.C., in the Bonus Army to request cash bonuses promised to them to be paid in 1945.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> <a href=\"https://wikipedia.org/wiki/Veteran\" title=\"Veteran\">veterans</a> begin to assemble in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, in the <a href=\"https://wikipedia.org/wiki/Bonus_Army\" title=\"Bonus Army\">Bonus Army</a> to request cash bonuses promised to them to be paid in 1945.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> <a href=\"https://wikipedia.org/wiki/Veteran\" title=\"Veteran\">veterans</a> begin to assemble in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, in the <a href=\"https://wikipedia.org/wiki/Bonus_Army\" title=\"Bonus Army\">Bonus Army</a> to request cash bonuses promised to them to be paid in 1945.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Veteran", "link": "https://wikipedia.org/wiki/Veteran"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}, {"title": "Bonus Army", "link": "https://wikipedia.org/wiki/Bonus_Army"}]}, {"year": "1935", "text": "First flight of the Messerschmitt Bf 109 fighter aeroplane.", "html": "1935 - First flight of the <a href=\"https://wikipedia.org/wiki/Messerschmitt_Bf_109\" title=\"Messerschmitt Bf 109\">Messerschmitt Bf 109</a> fighter aeroplane.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Messerschmitt_Bf_109\" title=\"Messerschmitt Bf 109\">Messerschmitt Bf 109</a> fighter aeroplane.", "links": [{"title": "Messerschmitt Bf 109", "link": "https://wikipedia.org/wiki/Messerschmitt_Bf_109"}]}, {"year": "1945", "text": "First combat mission of the Consolidated B-32 Dominator heavy bomber.", "html": "1945 - First <a href=\"https://wikipedia.org/wiki/Combat\" title=\"Combat\">combat</a> mission of the <a href=\"https://wikipedia.org/wiki/Consolidated_B-32_Dominator\" title=\"Consolidated B-32 Dominator\">Consolidated B-32 Dominator</a> <a href=\"https://wikipedia.org/wiki/Heavy_bomber\" title=\"Heavy bomber\">heavy bomber</a>.", "no_year_html": "First <a href=\"https://wikipedia.org/wiki/Combat\" title=\"Combat\">combat</a> mission of the <a href=\"https://wikipedia.org/wiki/Consolidated_B-32_Dominator\" title=\"Consolidated B-32 Dominator\">Consolidated B-32 Dominator</a> <a href=\"https://wikipedia.org/wiki/Heavy_bomber\" title=\"Heavy bomber\">heavy bomber</a>.", "links": [{"title": "Combat", "link": "https://wikipedia.org/wiki/Combat"}, {"title": "Consolidated B-32 Dominator", "link": "https://wikipedia.org/wiki/Consolidated_B-32_Dominator"}, {"title": "Heavy bomber", "link": "https://wikipedia.org/wiki/Heavy_bomber"}]}, {"year": "1947", "text": "United Airlines Flight 521 crashes at LaGuardia Airport, killing 43.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_521\" class=\"mw-redirect\" title=\"United Airlines Flight 521\">United Airlines Flight 521</a> crashes at <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a>, killing 43.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_521\" class=\"mw-redirect\" title=\"United Airlines Flight 521\">United Airlines Flight 521</a> crashes at <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a>, killing 43.", "links": [{"title": "United Airlines Flight 521", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_521"}, {"title": "LaGuardia Airport", "link": "https://wikipedia.org/wiki/LaGuardia_Airport"}]}, {"year": "1948", "text": "United Nations Truce Supervision Organization is founded.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/United_Nations_Truce_Supervision_Organization\" title=\"United Nations Truce Supervision Organization\">United Nations Truce Supervision Organization</a> is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Truce_Supervision_Organization\" title=\"United Nations Truce Supervision Organization\">United Nations Truce Supervision Organization</a> is founded.", "links": [{"title": "United Nations Truce Supervision Organization", "link": "https://wikipedia.org/wiki/United_Nations_Truce_Supervision_Organization"}]}, {"year": "1950", "text": "The St. Roch, the first ship to circumnavigate North America, arrives in Halifax, Nova Scotia, Canada.", "html": "1950 - The <i><a href=\"https://wikipedia.org/wiki/St._R<PERSON>_(ship)\" title=\"St. Roch (ship)\">St. Roch</a></i>, the first ship to <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigate</a> North America, arrives in <a href=\"https://wikipedia.org/wiki/Halifax,_Nova_Scotia\" title=\"Halifax, Nova Scotia\">Halifax, Nova Scotia</a>, Canada.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/St._Roch_(ship)\" title=\"St. Roch (ship)\">St. Roch</a></i>, the first ship to <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigate</a> North America, arrives in <a href=\"https://wikipedia.org/wiki/Halifax,_Nova_Scotia\" title=\"Halifax, Nova Scotia\">Halifax, Nova Scotia</a>, Canada.", "links": [{"title": "<PERSON><PERSON> (ship)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(ship)"}, {"title": "Circumnavigation", "link": "https://wikipedia.org/wiki/Circumnavigation"}, {"title": "Halifax, Nova Scotia", "link": "https://wikipedia.org/wiki/Halifax,_Nova_Scotia"}]}, {"year": "1953", "text": "<PERSON> and <PERSON><PERSON><PERSON> become the first people to reach the summit of Mount Everest, on <PERSON><PERSON> Norgay's (adopted) 39th birthday.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Sherpa_people\" title=\"Sherpa people\">Sherpa</a> <a href=\"https://wikipedia.org/wiki/Tenzing_Norgay\" title=\"Tenzing Norgay\">Tenzing Norgay</a> become the <a href=\"https://wikipedia.org/wiki/1953_British_Mount_Everest_expedition\" title=\"1953 British Mount Everest expedition\">first people to reach the summit</a> of <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>, on Tenzing Norgay's (adopted) 39th birthday.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Sherpa_people\" title=\"Sherpa people\"><PERSON>rpa</a> <a href=\"https://wikipedia.org/wiki/Tenzing_Norgay\" title=\"Tenzing Norgay\">Tenzing Norgay</a> become the <a href=\"https://wikipedia.org/wiki/1953_British_Mount_Everest_expedition\" title=\"1953 British Mount Everest expedition\">first people to reach the summit</a> of <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>, on Tenzing Norgay's (adopted) 39th birthday.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sherpa people", "link": "https://wikipedia.org/wiki/Sherpa_people"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tenzing_Norgay"}, {"title": "1953 British Mount Everest expedition", "link": "https://wikipedia.org/wiki/1953_British_Mount_Everest_expedition"}, {"title": "Mount Everest", "link": "https://wikipedia.org/wiki/Mount_Everest"}]}, {"year": "1964", "text": "The Arab League meets in East Jerusalem to discuss the Palestinian question, leading to the formation of the Palestine Liberation Organization.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Arab_League\" title=\"Arab League\">Arab League</a> meets in <a href=\"https://wikipedia.org/wiki/East_Jerusalem\" title=\"East Jerusalem\">East Jerusalem</a> to discuss the Palestinian question, leading to the formation of the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arab_League\" title=\"Arab League\">Arab League</a> meets in <a href=\"https://wikipedia.org/wiki/East_Jerusalem\" title=\"East Jerusalem\">East Jerusalem</a> to discuss the Palestinian question, leading to the formation of the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a>.", "links": [{"title": "Arab League", "link": "https://wikipedia.org/wiki/Arab_League"}, {"title": "East Jerusalem", "link": "https://wikipedia.org/wiki/East_Jerusalem"}, {"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}]}, {"year": "1964", "text": "Having deposed them in a January coup South Vietnamese leader <PERSON><PERSON><PERSON><PERSON> had rival Generals <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> convicted of \"lax morality\".", "html": "1964 - Having <a href=\"https://wikipedia.org/wiki/January_1964_South_Vietnamese_coup\" title=\"January 1964 South Vietnamese coup\">deposed them in a January coup</a> South Vietnamese leader <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> had rival Generals <a href=\"https://wikipedia.org/wiki/Tran_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Tran Van Don\">Tr<PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a> convicted of \"lax morality\".", "no_year_html": "Having <a href=\"https://wikipedia.org/wiki/January_1964_South_Vietnamese_coup\" title=\"January 1964 South Vietnamese coup\">deposed them in a January coup</a> South Vietnamese leader <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> had rival Generals <a href=\"https://wikipedia.org/wiki/Tran_Van_<PERSON>\" class=\"mw-redirect\" title=\"Tran Van Don\">T<PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a> convicted of \"lax morality\".", "links": [{"title": "January 1964 South Vietnamese coup", "link": "https://wikipedia.org/wiki/January_1964_South_Vietnamese_coup"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON> is elected the first black mayor of Los Angeles, California.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" class=\"mw-redirect\" title=\"<PERSON> (American politician)\"><PERSON></a> is elected the first black <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">mayor</a> of <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_politician)\" class=\"mw-redirect\" title=\"<PERSON> (American politician)\"><PERSON></a> is elected the first black <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">mayor</a> of <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "links": [{"title": "<PERSON> (American politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)"}, {"title": "Mayor of Los Angeles", "link": "https://wikipedia.org/wiki/Mayor_of_Los_Angeles"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1974", "text": "SETA, a Finnish LGBT rights organisation, is founded in Helsinki.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Seta_(organization)\" title=\"Seta (organization)\">SETA</a>, a Finnish <a href=\"https://wikipedia.org/wiki/LGBT\" class=\"mw-redirect\" title=\"LGBT\">LGBT</a> rights organisation, is founded in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seta_(organization)\" title=\"Seta (organization)\">SETA</a>, a Finnish <a href=\"https://wikipedia.org/wiki/LGBT\" class=\"mw-redirect\" title=\"LGBT\">LGBT</a> rights organisation, is founded in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "links": [{"title": "Seta (organization)", "link": "https://wikipedia.org/wiki/Seta_(organization)"}, {"title": "LGBT", "link": "https://wikipedia.org/wiki/LGBT"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1982", "text": "<PERSON> <PERSON> becomes the first pontiff to visit Canterbury Cathedral.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> becomes the first pontiff to visit <a href=\"https://wikipedia.org/wiki/Canterbury_Cathedral\" title=\"Canterbury Cathedral\">Canterbury Cathedral</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> becomes the first pontiff to visit <a href=\"https://wikipedia.org/wiki/Canterbury_Cathedral\" title=\"Canterbury Cathedral\">Canterbury Cathedral</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Canterbury Cathedral", "link": "https://wikipedia.org/wiki/Canterbury_Cathedral"}]}, {"year": "1982", "text": "Falklands War: the British Army defeats the Argentine Army at the Battle of Goose Green.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> defeats the <a href=\"https://wikipedia.org/wiki/Argentine_Army\" title=\"Argentine Army\">Argentine Army</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Goose_Green\" title=\"Battle of Goose Green\">Battle of Goose Green</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> defeats the <a href=\"https://wikipedia.org/wiki/Argentine_Army\" title=\"Argentine Army\">Argentine Army</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Goose_Green\" title=\"Battle of Goose Green\">Battle of Goose Green</a>.", "links": [{"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "Argentine Army", "link": "https://wikipedia.org/wiki/Argentine_Army"}, {"title": "Battle of Goose Green", "link": "https://wikipedia.org/wiki/Battle_of_Goose_Green"}]}, {"year": "1985", "text": "Heysel Stadium disaster: Thirty-nine association football fans die and hundreds are injured when a dilapidated retaining wall collapses.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Heysel_Stadium_disaster\" title=\"Heysel Stadium disaster\">Heysel Stadium disaster</a>: Thirty-nine <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">association football</a> fans die and hundreds are injured when a dilapidated retaining wall collapses.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heysel_Stadium_disaster\" title=\"Heysel Stadium disaster\">Heysel Stadium disaster</a>: Thirty-nine <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">association football</a> fans die and hundreds are injured when a dilapidated retaining wall collapses.", "links": [{"title": "Heysel Stadium disaster", "link": "https://wikipedia.org/wiki/Heysel_Stadium_disaster"}, {"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}]}, {"year": "1985", "text": "Amputee <PERSON> completes cross-Canada marathon at Victoria, British Columbia, after 14 months.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Amputation\" title=\"Amputation\">Amputee</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> completes <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">cross-Canada</a> <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> at <a href=\"https://wikipedia.org/wiki/Victoria,_British_Columbia\" title=\"Victoria, British Columbia\">Victoria, British Columbia</a>, after 14 months.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amputation\" title=\"Amputation\">Amputee</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> completes <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">cross-Canada</a> <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> at <a href=\"https://wikipedia.org/wiki/Victoria,_British_Columbia\" title=\"Victoria, British Columbia\">Victoria, British Columbia</a>, after 14 months.", "links": [{"title": "Amputation", "link": "https://wikipedia.org/wiki/Amputation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canada", "link": "https://wikipedia.org/wiki/Canada"}, {"title": "Marathon", "link": "https://wikipedia.org/wiki/Marathon"}, {"title": "Victoria, British Columbia", "link": "https://wikipedia.org/wiki/Victoria,_British_Columbia"}]}, {"year": "1988", "text": "U.S. President <PERSON> begins his first visit to the Soviet Union when he arrives in Moscow for a superpower summit with the Soviet leader <PERSON>.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his first visit to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> when he arrives in Moscow for a superpower summit with the Soviet leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his first visit to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> when he arrives in Moscow for a superpower summit with the Soviet leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "Signing of an agreement between Egypt and the United States, allowing the manufacture of parts of the F-16 jet fighter plane in Egypt.", "html": "1989 - Signing of an agreement between <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> and the United States, allowing the manufacture of parts of the <a href=\"https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon\" title=\"General Dynamics F-16 Fighting Falcon\">F-16</a> jet fighter plane in Egypt.", "no_year_html": "Signing of an agreement between <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> and the United States, allowing the manufacture of parts of the <a href=\"https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon\" title=\"General Dynamics F-16 Fighting Falcon\">F-16</a> jet fighter plane in Egypt.", "links": [{"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "General Dynamics F-16 Fighting Falcon", "link": "https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon"}]}, {"year": "1990", "text": "The Congress of People's Deputies of Russia elects <PERSON> as President of the Russian Soviet Federative Socialist Republic.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Congress_of_People%27s_Deputies_of_Russia\" title=\"Congress of People's Deputies of Russia\">Congress of People's Deputies</a> of <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Russia</a> elects <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/President_of_the_RSFSR\" class=\"mw-redirect\" title=\"President of the RSFSR\">President of the Russian Soviet Federative Socialist Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congress_of_People%27s_Deputies_of_Russia\" title=\"Congress of People's Deputies of Russia\">Congress of People's Deputies</a> of <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Russia</a> elects <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/President_of_the_RSFSR\" class=\"mw-redirect\" title=\"President of the RSFSR\">President of the Russian Soviet Federative Socialist Republic</a>.", "links": [{"title": "Congress of People's Deputies of Russia", "link": "https://wikipedia.org/wiki/Congress_of_People%27s_Deputies_of_Russia"}, {"title": "Russian Soviet Federative Socialist Republic", "link": "https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the RSFSR", "link": "https://wikipedia.org/wiki/President_of_the_RSFSR"}]}, {"year": "1993", "text": "The Miss Sarajevo beauty pageant is held in war-torn Sarajevo drawing global attention to the plight of its citizens.", "html": "1993 - The Miss Sarajevo beauty pageant is held in war-torn <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a> drawing global attention to the plight of its citizens.", "no_year_html": "The Miss Sarajevo beauty pageant is held in war-torn <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a> drawing global attention to the plight of its citizens.", "links": [{"title": "Sarajevo", "link": "https://wikipedia.org/wiki/Sarajevo"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON> takes office as President of Nigeria, the first elected and civilian head of state in Nigeria after 16 years of military rule.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Olusegun_Obasanjo\" title=\"<PERSON>luseg<PERSON> Obasanjo\"><PERSON><PERSON><PERSON><PERSON></a> takes office as <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a>, the first elected and civilian <a href=\"https://wikipedia.org/wiki/Head_of_state\" title=\"Head of state\">head of state</a> in <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> after 16 years of military rule.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oluseg<PERSON>_Obasanjo\" title=\"<PERSON>luseg<PERSON> Obasanjo\"><PERSON><PERSON><PERSON><PERSON></a> takes office as <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a>, the first elected and civilian <a href=\"https://wikipedia.org/wiki/Head_of_state\" title=\"Head of state\">head of state</a> in <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> after 16 years of military rule.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>eg<PERSON>_O<PERSON>anjo"}, {"title": "President of Nigeria", "link": "https://wikipedia.org/wiki/President_of_Nigeria"}, {"title": "Head of state", "link": "https://wikipedia.org/wiki/Head_of_state"}, {"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}]}, {"year": "1999", "text": "Space Shuttle Discovery completes the first docking with the International Space Station.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> completes the <a href=\"https://wikipedia.org/wiki/STS-96\" title=\"STS-96\">first docking</a> with the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> completes the <a href=\"https://wikipedia.org/wiki/STS-96\" title=\"STS-96\">first docking</a> with the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-96", "link": "https://wikipedia.org/wiki/STS-96"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2001", "text": "The U.S. Supreme Court rules that the disabled golfer <PERSON> can use a cart to ride in tournaments.", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that the disabled golfer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> can use a cart to ride in tournaments.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that the disabled golfer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> can use a cart to ride in tournaments.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "The National World War II Memorial is dedicated in Washington, D.C.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/National_World_War_II_Memorial\" class=\"mw-redirect\" title=\"National World War II Memorial\">National World War II Memorial</a> is dedicated in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_World_War_II_Memorial\" class=\"mw-redirect\" title=\"National World War II Memorial\">National World War II Memorial</a> is dedicated in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "links": [{"title": "National World War II Memorial", "link": "https://wikipedia.org/wiki/National_World_War_II_Memorial"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "2005", "text": "France rejects the Constitution of the European Union in a national referendum.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> rejects the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_European_Union\" class=\"mw-redirect\" title=\"Constitution of the European Union\">Constitution of the European Union</a> in a <a href=\"https://wikipedia.org/wiki/2005_French_European_Constitution_referendum\" title=\"2005 French European Constitution referendum\">national referendum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> rejects the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_European_Union\" class=\"mw-redirect\" title=\"Constitution of the European Union\">Constitution of the European Union</a> in a <a href=\"https://wikipedia.org/wiki/2005_French_European_Constitution_referendum\" title=\"2005 French European Constitution referendum\">national referendum</a>.", "links": [{"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Constitution of the European Union", "link": "https://wikipedia.org/wiki/Constitution_of_the_European_Union"}, {"title": "2005 French European Constitution referendum", "link": "https://wikipedia.org/wiki/2005_French_European_Constitution_referendum"}]}, {"year": "2008", "text": "A doublet earthquake, of combined magnitude 6.1, strikes Iceland near the town of Selfoss, injuring 30 people.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/2008_Iceland_earthquake\" title=\"2008 Iceland earthquake\">doublet earthquake</a>, of combined <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">magnitude</a> 6.1, strikes <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> near the town of <a href=\"https://wikipedia.org/wiki/Selfoss_(town)\" title=\"Selfoss (town)\">Selfoss</a>, injuring 30 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2008_Iceland_earthquake\" title=\"2008 Iceland earthquake\">doublet earthquake</a>, of combined <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">magnitude</a> 6.1, strikes <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> near the town of <a href=\"https://wikipedia.org/wiki/Selfoss_(town)\" title=\"Selfoss (town)\">Selfoss</a>, injuring 30 people.", "links": [{"title": "2008 Iceland earthquake", "link": "https://wikipedia.org/wiki/2008_Iceland_earthquake"}, {"title": "Moment magnitude scale", "link": "https://wikipedia.org/wiki/Moment_magnitude_scale"}, {"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "Selfoss (town)", "link": "https://wikipedia.org/wiki/Selfoss_(town)"}]}, {"year": "2012", "text": "A 5.8-magnitude earthquake hits northern Italy near Bologna, killing at least 24 people.", "html": "2012 - A 5.8-magnitude <a href=\"https://wikipedia.org/wiki/2012_Northern_Italy_earthquakes#29_May_earthquake\" title=\"2012 Northern Italy earthquakes\">earthquake</a> hits northern Italy near <a href=\"https://wikipedia.org/wiki/Bologna\" title=\"Bologna\">Bologna</a>, killing at least 24 people.", "no_year_html": "A 5.8-magnitude <a href=\"https://wikipedia.org/wiki/2012_Northern_Italy_earthquakes#29_May_earthquake\" title=\"2012 Northern Italy earthquakes\">earthquake</a> hits northern Italy near <a href=\"https://wikipedia.org/wiki/Bologna\" title=\"Bologna\">Bologna</a>, killing at least 24 people.", "links": [{"title": "2012 Northern Italy earthquakes", "link": "https://wikipedia.org/wiki/2012_Northern_Italy_earthquakes#29_May_earthquake"}, {"title": "Bologna", "link": "https://wikipedia.org/wiki/Bologna"}]}, {"year": "2015", "text": "One World Observatory at One World Trade Center opens.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/One_World_Observatory\" class=\"mw-redirect\" title=\"One World Observatory\">One World Observatory</a> at <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">One World Trade Center</a> opens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/One_World_Observatory\" class=\"mw-redirect\" title=\"One World Observatory\">One World Observatory</a> at <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">One World Trade Center</a> opens.", "links": [{"title": "One World Observatory", "link": "https://wikipedia.org/wiki/One_World_Observatory"}, {"title": "One World Trade Center", "link": "https://wikipedia.org/wiki/One_World_Trade_Center"}]}, {"year": "2021", "text": "A Cessna Citation I/SP crashes into Percy Priest Lake in Tennessee, killing all six people on board, including actor <PERSON> and his wife <PERSON>.", "html": "2021 - A <a href=\"https://wikipedia.org/wiki/Cessna_Citation_I\" title=\"Cessna Citation I\">Cessna Citation I/SP</a> <a href=\"https://wikipedia.org/wiki/2021_<PERSON>_<PERSON>_<PERSON>_Cessna_Citation_crash\" title=\"2021 Percy <PERSON> Lake Cessna Citation crash\">crashes</a> into <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>, killing all six people on board, including actor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his wife <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>hamblin_<PERSON>\" title=\"<PERSON> Shamblin Lara\"><PERSON>ham<PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Cessna_Citation_I\" title=\"Cessna Citation I\">Cessna Citation I/SP</a> <a href=\"https://wikipedia.org/wiki/2021_<PERSON>_<PERSON>_<PERSON>_Cessna_Citation_crash\" title=\"2021 Percy <PERSON> Lake Cessna Citation crash\">crashes</a> into <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>, killing all six people on board, including actor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his wife <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>blin_<PERSON>\" title=\"<PERSON>hamblin <PERSON>\"><PERSON></a>.", "links": [{"title": "Cessna Citation I", "link": "https://wikipedia.org/wiki/<PERSON>ssna_Citation_I"}, {"title": "2021 <PERSON> Cessna Citation crash", "link": "https://wikipedia.org/wiki/2021_<PERSON>_<PERSON>_<PERSON>_Cessna_Citation_crash"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hamblin_<PERSON>"}]}], "Births": [{"year": "1421", "text": "<PERSON>, Prince of Viana (d. 1461)", "html": "1421 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Viana\" title=\"<PERSON>, Prince of Viana\"><PERSON>, Prince of Viana</a> (d. 1461)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Viana\" title=\"<PERSON>, Prince of Viana\"><PERSON>, Prince of Viana</a> (d. 1461)", "links": [{"title": "<PERSON>, Prince of Viana", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Viana"}]}, {"year": "1443", "text": "<PERSON>, Duke of Münsterberg, <PERSON><PERSON><PERSON><PERSON>, Duke of Münsterberg and Opava, Count of Glatz (d. 1500)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_M%C3%BCnsterberg\" title=\"<PERSON>, Duke of Münsterberg\"><PERSON>, Duke of Münsterberg</a>, <PERSON><PERSON><PERSON><PERSON>, Duke of Münsterberg and Opava, Count of Glatz (d. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_M%C3%BCnsterberg\" title=\"<PERSON>, Duke of Münsterberg\"><PERSON>, Duke of Münsterberg</a>, <PERSON><PERSON><PERSON><PERSON>, Duke of Münsterberg and Opava, Count of Glatz (d. 1500)", "links": [{"title": "<PERSON>, Duke of Münsterberg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_M%C3%BCnsterberg"}]}, {"year": "1504", "text": "<PERSON><PERSON>, Croatian archbishop (d. 1573)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/Antun_Vran%C4%8Di%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian archbishop (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antun_Vran%C4%8Di%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian archbishop (d. 1573)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antun_Vran%C4%8Di%C4%87"}]}, {"year": "1555", "text": "<PERSON>, 1st Earl of Totnes, English general and administrator (d. 1629)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Totnes\" title=\"<PERSON>, 1st Earl of Totnes\"><PERSON>, 1st Earl of Totnes</a>, English general and administrator (d. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Totnes\" title=\"<PERSON>, 1st Earl of Totnes\"><PERSON>, 1st Earl of Totnes</a>, English general and administrator (d. 1629)", "links": [{"title": "<PERSON>, 1st Earl of Totnes", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Totnes"}]}, {"year": "1568", "text": "<PERSON>, Italian princess (d. 1615)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/Virginia_de%27_<PERSON>\" title=\"<PERSON> de<PERSON>\"><PERSON></a>, Italian princess (d. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_de%27_<PERSON>\" title=\"Virginia de<PERSON>\"><PERSON></a>, Italian princess (d. 1615)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_de%27_Medici"}]}, {"year": "1594", "text": "<PERSON><PERSON><PERSON> zu Pappenheim, Bavarian field marshal (d. 1632)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_Pappenheim\" title=\"<PERSON><PERSON><PERSON> zu Pappenheim\"><PERSON><PERSON><PERSON> zu Pappenheim</a>, Bavarian field marshal (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_Pappenheim\" title=\"<PERSON><PERSON><PERSON> zu Pappenheim\"><PERSON><PERSON><PERSON> zu Pappenheim</a>, Bavarian field marshal (d. 1632)", "links": [{"title": "<PERSON><PERSON><PERSON> Pappenheim", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1627", "text": "<PERSON>, Duchess of Montpensier, French princess (d. 1693)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Montpensier\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Montpensier\"><PERSON>, Duchess of Montpensier</a>, French princess (d. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Montpensier\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Montpensier\"><PERSON>, Duchess of Montpensier</a>, French princess (d. 1693)", "links": [{"title": "<PERSON>, Duchess of Montpensier", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_<PERSON>_Mont<PERSON>"}]}, {"year": "1630", "text": "<PERSON> of England (d. 1685)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1685)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1675", "text": "<PERSON><PERSON><PERSON><PERSON>, English mathematician and philosopher (d. 1715)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English mathematician and philosopher (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English mathematician and philosopher (d. 1715)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1716", "text": "Louis<PERSON><PERSON><PERSON><PERSON>, French zoologist and mineralogist (d. 1800)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French zoologist and mineralogist (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French zoologist and mineralogist (d. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1722", "text": "<PERSON>, 1st Duke of Leinster, Irish soldier and politician (d. 1773)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Leinster\" title=\"<PERSON>, 1st Duke of Leinster\"><PERSON>, 1st Duke of Leinster</a>, Irish soldier and politician (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Leinster\" title=\"<PERSON>, 1st Duke of Leinster\"><PERSON>, 1st Duke of Leinster</a>, Irish soldier and politician (d. 1773)", "links": [{"title": "<PERSON>, 1st Duke of Leinster", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Leinster"}]}, {"year": "1730", "text": "<PERSON> of Exeter, English organist and composer (d. 1803)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Exeter\" title=\"<PERSON> of Exeter\"><PERSON> of Exeter</a>, English organist and composer (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Exeter\" title=\"<PERSON> of Exeter\"><PERSON> of Exeter</a>, English organist and composer (d. 1803)", "links": [{"title": "<PERSON> of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>_of_Exeter"}]}, {"year": "1736", "text": "<PERSON>, American lawyer and politician, 1st Governor of Virginia (d. 1799)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a> (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a> (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Virginia", "link": "https://wikipedia.org/wiki/Governor_of_Virginia"}]}, {"year": "1780", "text": "<PERSON>, French chemist and pharmacist (d. 1855)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and pharmacist (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and pharmacist (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>not"}]}, {"year": "1794", "text": "<PERSON>, German astronomer and selenographer (d. 1874)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4dler\" title=\"<PERSON>\"><PERSON></a>, German astronomer and selenographer (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4dler\" title=\"<PERSON>\"><PERSON></a>, German astronomer and selenographer (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4dler"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter (d. 1836)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9one_Dr%C3%B6lling\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9one_Dr%C3%B6lling\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (d. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-Ad%C3%A9one_Dr%C3%B6lling"}]}, {"year": "1823", "text": "<PERSON>, American carpenter and inventor (d. 1895)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American carpenter and inventor (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American carpenter and inventor (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Spanish pianist and composer (d. 1909)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9niz\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist and composer (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9niz\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist and composer (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%A9niz"}]}, {"year": "1871", "text": "<PERSON>, American painter (d. 1933)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Estonian organist and composer (d. 1918)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian organist and composer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian organist and composer (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON> <PERSON><PERSON>, English essayist, poet, and playwright (d. 1936)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English essayist, poet, and playwright (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"G. K. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English essayist, poet, and playwright (d. 1936)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, German historian and philosopher (d. 1936)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and philosopher (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and philosopher (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss-Argentinian poet and author (d. 1938)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Alfonsina_Storni\" title=\"Alfonsina Storni\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss-Argentinian poet and author (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonsina_Storni\" title=\"Alfonsina Storni\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss-Argentinian poet and author (d. 1938)", "links": [{"title": "Alfons<PERSON>", "link": "https://wikipedia.org/wiki/Alfonsina_Storni"}]}, {"year": "1893", "text": "<PERSON>, American journalist and author (d. 1944)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Max_Brand\" title=\"Max Brand\"><PERSON></a>, American journalist and author (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_Brand\" title=\"Max Brand\"><PERSON></a>, American journalist and author (d. 1944)", "links": [{"title": "Max <PERSON>", "link": "https://wikipedia.org/wiki/Max_Brand"}]}, {"year": "1894", "text": "<PERSON>, Canadian-English actress, singer and writer (d. 1989)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English actress, singer and writer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English actress, singer and writer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Austrian-American director, producer, and screenwriter (d. 1969)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American director, producer, and screenwriter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American director, producer, and screenwriter (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Czech-American pianist, composer, and conductor (d. 1957)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American pianist, composer, and conductor (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American pianist, composer, and conductor (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Canadian lawyer and politician, 10th Canadian Minister of Defence (d. 1987)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of Defence</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of Defence</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of National Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)"}]}, {"year": "1902", "text": "<PERSON>, Australian rugby league player and coach (d. 1999)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English-American actor, singer, and producer (d. 2003)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, singer, and producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, singer, and producer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Australian cyclist and politician (d. 1996)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist and politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist and politician (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English actor, director, and playwright (d. 1994)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, and playwright (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, and playwright (d. 1994)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1906", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian-English author (d. 1964)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian-English author (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian-English author (d. 1964)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Canadian captain and politician (d. 2002)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian captain and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian captain and politician (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hart<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Welsh-English playwright and screenwriter (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, Welsh-English playwright and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, Welsh-English playwright and screenwriter (d. 1996)", "links": [{"title": "<PERSON> (screenwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)"}]}, {"year": "1910", "text": "<PERSON>, American sprinter and politician (d. 1978)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and politician (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and politician (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American boxer (d. 1997)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 2003)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1914", "text": "<PERSON><PERSON>, Nepalese-Indian mountaineer (d. 1986)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Tenzing_Norgay\" title=\"Tenzing Norgay\"><PERSON><PERSON> Norga<PERSON></a>, Nepalese-Indian mountaineer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tenzing_Norgay\" title=\"Tenzing Norgay\"><PERSON><PERSON> Norga<PERSON></a>, Nepalese-Indian mountaineer (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tenzing_Norgay"}]}, {"year": "1915", "text": "<PERSON>, German conductor and composer (d. 1990)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and composer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and composer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1917", "text": "<PERSON>, 35th President of the United States (d. 1963)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 35th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 35th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1917", "text": "<PERSON>, Canadian historian, author, and academic (d. 2011)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian, author, and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian, author, and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Canadian physician and academic (d. 2018)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Hungarian-American economist and academic, Nobel Prize laureate (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 2017)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Australian cartoonist and puppeteer (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist and puppeteer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist and puppeteer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Norwegian dancer and choreographer (d. 2023)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian dancer and choreographer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian dancer and choreographer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American race car driver (d. 1964)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Greek-French composer, engineer, and theorist (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-French composer, engineer, and theorist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-French composer, engineer, and theorist (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French author (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, 6th Earl of Morley, English colonel and politician, Lord Lieutenant of Devon (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th Earl of Morley\"><PERSON>, 6th Earl of Morley</a>, English colonel and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Devon\" title=\"Lord Lieutenant of Devon\">Lord Lieutenant of Devon</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th Earl of Morley\"><PERSON>, 6th Earl of Morley</a>, English colonel and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Devon\" title=\"Lord Lieutenant of Devon\">Lord Lieutenant of Devon</a> (d. 2015)", "links": [{"title": "<PERSON>, 6th Earl of Morley", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_<PERSON>"}, {"title": "Lord Lieutenant of Devon", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Devon"}]}, {"year": "1923", "text": "<PERSON>, American jazz bassist (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz bassist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz bassist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Danish author and illustrator (d. 1999)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and illustrator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bo\"><PERSON></a>, Danish author and illustrator (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Czech basketball player and coach (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_K%C5%99%C3%AD%C5%BE\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech basketball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%99%C3%AD%C5%BE\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech basketball player and coach (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miloslav_K%C5%99%C3%AD%C5%BE"}]}, {"year": "1924", "text": "<PERSON>, American baseball player (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Pepper_Paire\" title=\"Pepper Paire\"><PERSON></a>, American baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pepper_Paire\" title=\"Pepper Paire\"><PERSON></a>, American baseball player (d. 2013)", "links": [{"title": "Pepper Paire", "link": "https://wikipedia.org/wiki/Pepper_Paire"}]}, {"year": "1926", "text": "<PERSON>, Italian-English actress and television host (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English actress and television host (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English actress and television host (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Queen Consort of Tonga (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Halaevalu_Mata%CA%BBaho_%CA%BBAhome%CA%BBe\" title=\"Halaevalu Mataʻaho ʻAhomeʻe\"><PERSON><PERSON><PERSON><PERSON> Mataʻaho ʻAhomeʻe</a>, Queen Consort of Tonga (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halaevalu_Mata%CA%BBaho_%CA%BBAhome%CA%BBe\" title=\"Halaevalu Mataʻaho ʻAhomeʻe\"><PERSON><PERSON><PERSON><PERSON> Mataʻaho ʻAhomeʻe</a>, Queen Consort of Tonga (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Mataʻaho ʻAhomeʻe", "link": "https://wikipedia.org/wiki/Halaevalu_Mata%CA%BBaho_%CA%BBAhome%CA%BBe"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Senegalese academic and politician, 3rd President of Senegal", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Senegalese academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Senegal\" class=\"mw-redirect\" title=\"List of Presidents of Senegal\">President of Senegal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Senegalese academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Senegal\" class=\"mw-redirect\" title=\"List of Presidents of Senegal\">President of Senegal</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Presidents of Senegal", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Senegal"}]}, {"year": "1927", "text": "<PERSON>, Canadian pharmacist and businessman, founded the Jean Co<PERSON>u Group", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>(pharmacist)\" title=\"<PERSON> (pharmacist)\"><PERSON></a>, Canadian pharmacist and businessman, founded the <a href=\"https://wikipedia.org/wiki/Jean_<PERSON>_<PERSON>\" title=\"Jean Coutu Group\">Jean Coutu Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pharmacist)\" title=\"<PERSON> (pharmacist)\"><PERSON></a>, Canadian pharmacist and businessman, founded the <a href=\"https://wikipedia.org/wiki/Jean_<PERSON>_<PERSON>\" title=\"Jean Coutu Group\">Jean Coutu Group</a>", "links": [{"title": "<PERSON> (pharmacist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pharmacist)"}, {"title": "Jean <PERSON>", "link": "https://wikipedia.org/wiki/Jean_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American jazz pianist and composer (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz pianist and composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz pianist and composer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>d"}]}, {"year": "1929", "text": "<PERSON>, American philosopher and academic (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Harry_<PERSON>\" title=\"Harry <PERSON>\"><PERSON></a>, American philosopher and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harry_<PERSON>\" title=\"Harry <PERSON>\"><PERSON></a>, American philosopher and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English-Scottish physicist and academic, Nobel Prize laureate (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1929", "text": "<PERSON>, Puerto Rican-American baseball player, coach, and manager (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player, coach, and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player, coach, and manager (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American biologist and author", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American basketball player and coach", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, German conductor and educator", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German conductor and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German conductor and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian motorcycle racer (d. 2005)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Tarquinio_Provini\" title=\"Tarquin<PERSON> Provini\"><PERSON><PERSON><PERSON><PERSON></a>, Italian motorcycle racer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarquin<PERSON>_Provini\" title=\"Tarquinio Provini\"><PERSON><PERSON><PERSON><PERSON></a>, Italian motorcycle racer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tarquinio_Provini"}]}, {"year": "1934", "text": "<PERSON>, Dutch-Canadian businessman and politician, 28th Premier of British Columbia", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian businessman and politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian businessman and politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "1935", "text": "<PERSON>, South African author and playwright (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Brink\" title=\"<PERSON>\"><PERSON></a>, South African author and playwright (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Brink\" title=\"<PERSON>\"><PERSON></a>, South African author and playwright (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Brink"}]}, {"year": "1935", "text": "<PERSON>, American singer and producer (d. 2011)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Robinson\"><PERSON></a>, American singer and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American lawyer and judge", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, German keyboard player and composer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German keyboard player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German keyboard player and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, German show-jumper", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6hle\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German show-jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6hle\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German show-jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ckem%C3%B6hle"}]}, {"year": "1937", "text": "<PERSON>, American basketball player and coach", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English businessman and politician (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and businessman, 8th Commissioner of Baseball (d. 2025)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman, 8th <a href=\"https://wikipedia.org/wiki/Commissioner_of_Baseball\" title=\"Commissioner of Baseball\">Commissioner of Baseball</a> (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman, 8th <a href=\"https://wikipedia.org/wiki/Commissioner_of_Baseball\" title=\"Commissioner of Baseball\">Commissioner of Baseball</a> (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Commissioner of Baseball", "link": "https://wikipedia.org/wiki/Commissioner_of_Baseball"}]}, {"year": "1939", "text": "<PERSON>, Australian radio and television announcer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(announcer)\" title=\"<PERSON> (announcer)\"><PERSON></a>, Australian radio and television announcer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(announcer)\" title=\"<PERSON> (announcer)\"><PERSON></a>, Australian radio and television announcer", "links": [{"title": "<PERSON> (announcer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(announcer)"}]}, {"year": "1939", "text": "<PERSON>, American race car driver (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2021)", "links": [{"title": "Al Unser", "link": "https://wikipedia.org/wiki/Al_Unser"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 48th <PERSON><PERSON><PERSON><PERSON> (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Taih%C5%8D_K%C5%8Dki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 48th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taih%C5%8D_K%C5%8Dki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 48th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taih%C5%8D_K%C5%8Dki"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Pakistani politician, 8th President of Pakistan (d. 2010)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1941", "text": "<PERSON>, English mountaineer and author (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American journalist (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian businessman and politician, 40th Mayor of Montreal", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian businessman and politician, 40th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian businessman and politician, 40th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1942", "text": "<PERSON>, American actor and director (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 2020)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1943", "text": "<PERSON>, American educator and politician (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American businessman (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English soldier and politician, Shadow Secretary of State for Northern Ireland", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland\" title=\"Shadow Secretary of State for Northern Ireland\">Shadow Secretary of State for Northern Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland\" title=\"Shadow Secretary of State for Northern Ireland\">Shadow Secretary of State for Northern Ireland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Shadow Secretary of State for Northern Ireland", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and pianist (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and pianist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and pianist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, <PERSON> of Carmyllie, Scottish lawyer and politician, Solicitor General for Scotland (d. 2013)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Carmyllie\" title=\"<PERSON>, Baron <PERSON> of Carmyllie\"><PERSON>, Baron <PERSON> of Carmyllie</a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_Scotland\" title=\"Solicitor General for Scotland\">Solicitor General for Scotland</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Carmyllie\" title=\"<PERSON>, Baron <PERSON> of Carmyllie\"><PERSON>, Baron <PERSON> of Carmyllie</a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_Scotland\" title=\"Solicitor General for Scotland\">Solicitor General for Scotland</a> (d. 2013)", "links": [{"title": "<PERSON>, <PERSON> of Carmyllie", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Carmyllie"}, {"title": "Solicitor General for Scotland", "link": "https://wikipedia.org/wiki/Solicitor_General_for_Scotland"}]}, {"year": "1945", "text": "<PERSON>, English economist and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English jockey and trainer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jockey and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jockey and trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American photographer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Belgian scholar and author (d. 2018)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian scholar and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian scholar and author (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Spanish politician (d. 2000)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish politician (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English composer and radio host", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English microbiologist and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English microbiologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English microbiologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor and screenwriter (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 2019)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1949", "text": "<PERSON>, English footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, American singer and actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American film composer, singer-songwriter, producer, and actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film composer, singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film composer, singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American composer and educator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American lawyer and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, German runner (d. 2010)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American attempted assassin of <PERSON>", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American attempted assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American attempted assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American animator, producer, and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Scottish historian and curator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and curator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and curator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American race car driver and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English diplomat, British Ambassador to the United Nations", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/British_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"British Ambassador to the United Nations\">British Ambassador to the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/British_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"British Ambassador to the United Nations\">British Ambassador to the United Nations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "British Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/British_Ambassador_to_the_United_Nations"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter and actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Toya <PERSON>\"><PERSON> <PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Toy<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON> Toya <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English bishop and theologian", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and theologian", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American lawyer and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_He<PERSON><PERSON><PERSON>\" title=\"Je<PERSON> He<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_He<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> He<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Iranian film director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian film director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Israeli actor, director, and activist (d. 2011)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actor, director, and activist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actor, director, and activist (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, German footballer and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Uwe_Rapolder\" title=\"Uwe Rapolder\"><PERSON><PERSON></a>, German footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uwe_Rapolder\" title=\"Uwe Rapolder\"><PERSON><PERSON></a>, German footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uwe_Rapolder"}]}, {"year": "1958", "text": "<PERSON>, American baseball player and sportscaster", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English actor and novelist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English drummer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Irish-English bass player and songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Irish-English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Irish-English bass player and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1960", "text": "<PERSON>, Swiss economist and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter, guitarist, and activist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American drummer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Singaporean footballer, coach, and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1962", "text": "<PERSON>, Scottish journalist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Canadian actress and singer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Chlo%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chlo%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chlo%C3%A9_<PERSON>-Marie"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bayley"}]}, {"year": "1963", "text": "<PERSON>, Chinese high jumper", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Zhu_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>ky<PERSON>_Kat<PERSON>ma\" title=\"Uky<PERSON> Katayama\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uky<PERSON>_Kat<PERSON>ma\" title=\"Uky<PERSON> Katayama\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uky<PERSON>_Kat<PERSON>ma"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American academic and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_III"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Brazilian race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, Brazilian race car driver", "links": [{"title": "<PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1966", "text": "<PERSON>, French journalist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8de\" title=\"<PERSON>\"><PERSON></a>, French journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8de\" title=\"<PERSON>\"><PERSON></a>, French journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A8de"}]}, {"year": "1967", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American economist, author, and academic", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, 13th Duke of Argyll, Scottish politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_13th_Duke_of_Argyll\" title=\"<PERSON><PERSON><PERSON><PERSON>, 13th Duke of Argyll\"><PERSON><PERSON><PERSON><PERSON>, 13th Duke of Argyll</a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_13th_Duke_of_Argyll\" title=\"<PERSON><PERSON><PERSON><PERSON>, 13th Duke of Argyll\"><PERSON><PERSON><PERSON><PERSON>, 13th Duke of Argyll</a>, Scottish politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, 13th Duke of Argyll", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_13th_Duke_of_Argyll"}]}, {"year": "1968", "text": "<PERSON>, American basketball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> George\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> George\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American activist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Hida_Viloria\" title=\"Hida Viloria\"><PERSON><PERSON></a>, American activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hida_Viloria\" title=\"Hida Viloria\"><PERSON><PERSON></a>, American activist", "links": [{"title": "<PERSON>da Viloria", "link": "https://wikipedia.org/wiki/Hida_Viloria"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Australian journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ing"}]}, {"year": "1970", "text": "<PERSON>, Italian footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Canadian boxer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, German race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Bernd_Mayl%C3%A4nder\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mayl%C3%A4nder\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bernd_Mayl%C3%A4nder"}]}, {"year": "1971", "text": "<PERSON>, Australian television host and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English shot putter and discus thrower", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English shot putter and discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English shot putter and discus thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ck"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, American actress and LGBT advocate", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and LGBT advocate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and LGBT advocate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American basketball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English singer and bass player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer and bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rugby_league)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Japanese voice actress, singer, and radio personality", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress, singer, and radio personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress, singer, and radio personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American guitarist and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Alpay_%C3%96zalan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alpay_%C3%96zalan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alpay_%C3%96zalan"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian radio and television host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hurst\" title=\"<PERSON><PERSON>hurst\"><PERSON><PERSON></a>, Australian radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hurst\" title=\"<PERSON><PERSON>hurst\"><PERSON><PERSON></a>, Australian radio and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Myf_Warhurst"}]}, {"year": "1974", "text": "<PERSON>, American martial artist and retired actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and retired actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and retired actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American author and cartoonist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English singer-songwriter, dancer, and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B\"><PERSON></a>, English singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B\"><PERSON></a>, English singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_B"}]}, {"year": "1975", "text": "<PERSON>, German footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English comedian", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English golfer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wall\"><PERSON></a>, English golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American comedian, television host, actor, writer, and executive producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, television host, actor, writer, and executive producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, television host, actor, writer, and executive producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A1udio_Ca%C3%A7apa\" title=\"Cláudio Caçapa\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A1udio_Ca%C3%A7apa\" title=\"Cláudio Caçapa\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "Cláudio <PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A1udio_Ca%C3%A7apa"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1976", "text": "<PERSON><PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Russian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mass<PERSON>_<PERSON>i"}]}, {"year": "1977", "text": "<PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Angolan footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Lebo_Lebo\" title=\"António Lebo Lebo\"><PERSON><PERSON><PERSON><PERSON></a>, Angolan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Lebo_Lebo\" title=\"António Lebo Lebo\"><PERSON><PERSON><PERSON><PERSON></a>, Angolan footballer", "links": [{"title": "Ant<PERSON><PERSON> Le<PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_Lebo_Lebo"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Swedish singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, French tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, German footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player (d. 2017)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Argentinian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADas\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADas\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernesto_Far%C3%ADas"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Russian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Ukrainian heptathlete", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Do<PERSON>\" title=\"<PERSON><PERSON> Dobrynska\"><PERSON><PERSON></a>, Ukrainian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dobrynska\"><PERSON><PERSON></a>, Ukrainian heptathlete", "links": [{"title": "Nataliya Dobrynska", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, South Korean baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1982)\" title=\"<PERSON> (baseball, born 1982)\"><PERSON></a>, South Korean baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1982)\" title=\"<PERSON> (baseball, born 1982)\"><PERSON></a>, South Korean baseball player", "links": [{"title": "<PERSON> (baseball, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(baseball,_born_1982)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Australian-American professional wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-American professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-American professional wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American long jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American long jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American entrepreneur, film producer and YouTuber", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American entrepreneur, film producer and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American entrepreneur, film producer and YouTuber", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ffer\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ffer\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andreas_Sch%C3%A4ffer"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Norwegian singer and songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Ina_Wroldsen\" title=\"Ina Wroldsen\"><PERSON><PERSON></a>, Norwegian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ina_Wroldsen\" title=\"In<PERSON> Wroldsen\"><PERSON><PERSON></a>, Norwegian singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_W<PERSON><PERSON>en"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Lithuanian long jumper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian long jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C4%97"}]}, {"year": "1987", "text": "<PERSON>, Australian cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Dutch footballer (d. 2019)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian actor, producer, and screenwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>io"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Jordanian captain and pilot (d. 2015)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>ath_<PERSON>\" class=\"mw-redirect\" title=\"Muath <PERSON>\"><PERSON><PERSON></a>, Jordanian captain and pilot (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ath <PERSON>\"><PERSON><PERSON></a>, Jordanian captain and pilot (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Chinese gymnast", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fe<PERSON>\"><PERSON></a>, Chinese gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fe<PERSON>\"><PERSON></a>, Chinese gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American soccer player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Ghanaian-American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> An<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> An<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian-American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>h"}]}, {"year": "1989", "text": "<PERSON>, Argentinian footballer (d. 2015)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American model and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball pitcher", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1990", "text": "<PERSON>, American civil rights activist (d. 2017)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Cuban discus thrower", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban discus thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Women's World Chess Champion, 2017-2018", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Women%27s_World_Chess_Champion\" class=\"mw-redirect\" title=\"Women's World Chess Champion\">Women's World Chess Champion</a>, 2017-2018", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Women%27s_World_Chess_Champion\" class=\"mw-redirect\" title=\"Women's World Chess Champion\">Women's World Chess Champion</a>, 2017-2018", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Women's World Chess Champion", "link": "https://wikipedia.org/wiki/Women%27s_World_Chess_Champion"}]}, {"year": "1992", "text": "<PERSON>, Swiss tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Slovak tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Jana_%C4%8Cepelov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jana_%C4%8Cepelov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jana_%C4%8Cepelov%C3%A1"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American actress and kiteboarder", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and kiteboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and kiteboarder", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Estonian heptathlete", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Grete_%C5%A0adeiko\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grete_%C5%A0adeiko\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian heptathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grete_%C5%A0adeiko"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Reaves\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Re<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_<PERSON>aves"}]}, {"year": "1999", "text": "<PERSON>, South Korean actor and singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hoon\" title=\"<PERSON>ho<PERSON>\"><PERSON></a>, South Korean actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>on\" title=\"<PERSON>\"><PERSON></a>, South Korean actor and singer", "links": [{"title": "<PERSON>on", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hoon"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American soccer player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Gen<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">soccer</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">soccer</a> player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>cua\" title=\"Puka Nacua\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>cua\" title=\"P<PERSON> Nacua\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Puka_Nacua"}]}, {"year": "2001", "text": "<PERSON>, American figure skater", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American baseball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Youngest contender to compete for the title of World Chess Champion", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Gukesh_D\" class=\"mw-redirect\" title=\"Gukesh D\"><PERSON><PERSON><PERSON></a>, Youngest contender to compete for the title of <a href=\"https://wikipedia.org/wiki/World_Chess_Championship_2024\" title=\"World Chess Championship 2024\">World Chess Champion</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>sh_D\" class=\"mw-redirect\" title=\"Gukesh D\"><PERSON><PERSON><PERSON></a>, Youngest contender to compete for the title of <a href=\"https://wikipedia.org/wiki/World_Chess_Championship_2024\" title=\"World Chess Championship 2024\">World Chess Champion</a>", "links": [{"title": "G<PERSON>sh D", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>sh_D"}, {"title": "World Chess Championship 2024", "link": "https://wikipedia.org/wiki/World_Chess_Championship_2024"}]}], "Deaths": [{"year": "931", "text": "<PERSON><PERSON> of Pamplona", "html": "931 - <a href=\"https://wikipedia.org/wiki/Jimeno_Garc%C3%A9s_of_Pamplona\" class=\"mw-redirect\" title=\"<PERSON><PERSON>arcés of Pamplona\"><PERSON><PERSON> of Pamplona</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jimeno_Garc%C3%A9s_of_Pamplona\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Pamplona\"><PERSON><PERSON> of Pamplona</a>", "links": [{"title": "<PERSON><PERSON> of Pamplona", "link": "https://wikipedia.org/wiki/Jimeno_Garc%C3%A9s_of_Pamplona"}]}, {"year": "1040", "text": "<PERSON><PERSON><PERSON>, Count of Nevers", "html": "1040 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Nevers\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Nevers\"><PERSON><PERSON><PERSON>, Count of Nevers</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Nevers\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Nevers\"><PERSON><PERSON><PERSON> <PERSON>, Count of Nevers</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Nevers", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_<PERSON><PERSON>"}]}, {"year": "1259", "text": "<PERSON> of Denmark (b. 1219)", "html": "1259 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1219)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1219)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1311", "text": "<PERSON> of Majorca (b. 1243)", "html": "1311 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Majorca\" title=\"<PERSON> II of Majorca\"><PERSON> of Majorca</a> (b. 1243)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Majorca\" title=\"<PERSON> II of Majorca\"><PERSON> of Majorca</a> (b. 1243)", "links": [{"title": "<PERSON> of Majorca", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Majorca"}]}, {"year": "1320", "text": "<PERSON> <PERSON> of Alexandria, Coptic pope", "html": "1320 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>_of_Alexandria\" title=\"Pope John <PERSON> of Alexandria\">Pope <PERSON> of Alexandria</a>, Coptic pope", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>_of_Alexandria\" title=\"<PERSON> of Alexandria\">Pope <PERSON> of Alexandria</a>, Coptic pope", "links": [{"title": "<PERSON> <PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Alexandria"}]}, {"year": "1327", "text": "<PERSON><PERSON>, Danish archbishop (b. c. 1260)", "html": "1327 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Grand\" title=\"Jen<PERSON> Grand\"><PERSON><PERSON></a>, Danish archbishop (b. c. 1260)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Jen<PERSON> Grand\"><PERSON><PERSON></a>, Danish archbishop (b. c. 1260)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1379", "text": "<PERSON> of Castile (b. 1334)", "html": "1379 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Castile\" title=\"<PERSON> II of Castile\"><PERSON> of Castile</a> (b. 1334)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> II of Castile\"><PERSON> of Castile</a> (b. 1334)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Castile"}]}, {"year": "1405", "text": "<PERSON>, French soldier and author (b. 1327)", "html": "1405 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9zi%C3%A8res\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1327)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9zi%C3%A8res\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1327)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9zi%C3%A8res"}]}, {"year": "1425", "text": "Hongxi Emperor of China (b. 1378)", "html": "1425 - <a href=\"https://wikipedia.org/wiki/Hongxi_Emperor\" title=\"Hongxi Emperor\">Hongxi Emperor</a> of China (b. 1378)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hongxi_Emperor\" title=\"Hongxi Emperor\">Hongxi Emperor</a> of China (b. 1378)", "links": [{"title": "Hongxi Emperor", "link": "https://wikipedia.org/wiki/Hongxi_Emperor"}]}, {"year": "1453", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ottoman commander (b. 1428)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/Ulubatl%C4%B1_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman commander (b. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ulubatl%C4%B1_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman commander (b. 1428)", "links": [{"title": "Ulub<PERSON><PERSON><PERSON> Hasan", "link": "https://wikipedia.org/wiki/Ulubatl%C4%B1_<PERSON>"}]}, {"year": "1453", "text": "<PERSON>, Byzantine emperor (b. 1404)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XI_Palaiologos\" title=\"<PERSON> XI Palaiologos\"><PERSON></a>, Byzantine emperor (b. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XI_Palaiologos\" title=\"<PERSON> XI Palaiologos\"><PERSON></a>, Byzantine emperor (b. 1404)", "links": [{"title": "Constantine XI Palaiologos", "link": "https://wikipedia.org/wiki/Constantine_XI_Palaiologos"}]}, {"year": "1500", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese explorer and navigator (b. 1451)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/Bartolomeu_Dias\" title=\"Bartolomeu Dias\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese explorer and navigator (b. 1451)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bart<PERSON><PERSON>u_Dias\" title=\"Bartolomeu Dias\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese explorer and navigator (b. 1451)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>u_Dias"}]}, {"year": "1500", "text": "<PERSON>, English cleric and minister (b. 1423)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric and minister (b. 1423)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric and minister (b. 1423)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1546", "text": "<PERSON>, Scottish cardinal and politician, Lord Chancellor of Scotland (b. 1494)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cardinal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland\" title=\"Lord Chancellor of Scotland\">Lord Chancellor of Scotland</a> (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cardinal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland\" title=\"Lord Chancellor of Scotland\">Lord Chancellor of Scotland</a> (b. 1494)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lord Chancellor of Scotland", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland"}]}, {"year": "1593", "text": "<PERSON>, Welsh martyr (b. 1559)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh martyr (b. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh martyr (b. 1559)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1660", "text": "<PERSON><PERSON>, Dutch mathematician and academic (b. 1615)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and academic (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and academic (b. 1615)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON><PERSON><PERSON>, Dutch admiral (b. 1629)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tromp\" title=\"Corne<PERSON> Tromp\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tromp\" title=\"Cornelis Tromp\"><PERSON><PERSON><PERSON>rom<PERSON></a>, Dutch admiral (b. 1629)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tromp"}]}, {"year": "1790", "text": "<PERSON>, American general (b. 1718)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/Israel_Putnam\" title=\"Israel Putnam\"><PERSON></a>, American general (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Putnam\" title=\"Israel Putnam\"><PERSON></a>, American general (b. 1718)", "links": [{"title": "Israel Putnam", "link": "https://wikipedia.org/wiki/Israel_Putnam"}]}, {"year": "1796", "text": "<PERSON>, Swedish general and politician (b. 1720)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish general and politician (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish general and politician (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON>, French empress, first wife of <PERSON> (b. 1763)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French empress, first wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French empress, first wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (b. 1763)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9<PERSON>_<PERSON>_<PERSON>s"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON><PERSON>, English-Swiss chemist and academic (b. 1778)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, English-Swiss chemist and academic (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, English-Swiss chemist and academic (b. 1778)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, <PERSON>, French general (b. 1766)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, French general (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, French general (b. 1766)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, Polish composer, music conductor, and music teacher (b. 1791)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish composer, music conductor, and music teacher (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish composer, music conductor, and music teacher (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American general, lawyer, and politician (b. 1786)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON> of Hesse and by Rhine (b. 1870)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Hesse_and_by_Rhine\" title=\"Prince <PERSON> of Hesse and by Rhine\">Prince <PERSON> of Hesse and by Rhine</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Hesse_and_by_Rhine\" title=\"Prince <PERSON> of Hesse and by Rhine\">Prince <PERSON> of Hesse and by Rhine</a> (b. 1870)", "links": [{"title": "<PERSON> of Hesse and by Rhine", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_of_Hesse_and_by_Rhine"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Persian religious leader, founded the Baháʼí Faith (b. 1817)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%27u%27ll%C3%A1h\" class=\"mw-redirect\" title=\"Bahá'u'll<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a> (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bah%C3%A1%27u%27ll%C3%A1h\" class=\"mw-redirect\" title=\"Bahá'u'll<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Bahá<PERSON>í <PERSON></a> (b. 1817)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bah%C3%A1%27u%27ll%C3%A1h"}, {"title": "Baháʼí Faith", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith"}]}, {"year": "1896", "text": "<PERSON>, French geologist and academic (b. 1814)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9e"}]}, {"year": "1903", "text": "<PERSON>, American architect, designed the Château Frontenac and American Surety Building (b. 1845)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Ch%C3%A2teau_Frontenac\" title=\"Château Frontenac\">Château Frontenac</a> and <a href=\"https://wikipedia.org/wiki/American_Surety_Building\" title=\"American Surety Building\">American Surety Building</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Ch%C3%A2teau_Frontenac\" title=\"Château Frontenac\">Château Frontenac</a> and <a href=\"https://wikipedia.org/wiki/American_Surety_Building\" title=\"American Surety Building\">American Surety Building</a> (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Château Frontenac", "link": "https://wikipedia.org/wiki/Ch%C3%A2teau_Frontenac"}, {"title": "American Surety Building", "link": "https://wikipedia.org/wiki/American_Surety_Building"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Russian pianist, composer, and conductor (b. 1837)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist, composer, and conductor (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist, composer, and conductor (b. 1837)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON> <PERSON><PERSON>, English playwright and poet (b. 1836)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English playwright and poet (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English playwright and poet (b. 1836)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, English author and playwright (b. 1871)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bb_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ribb <PERSON>\"><PERSON></a>, English author and playwright (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bb_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ribb <PERSON>\"><PERSON></a>, English author and playwright (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brodribb_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English explorer, hunter, and author (b. 1853)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer, hunter, and author (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer, hunter, and author (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American poet and educator (b. 1831)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and educator (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and educator (b. 1831)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1919", "text": "<PERSON>, American colonel and politician, 39th United States Secretary of State (b. 1860)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 39th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 39th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1920", "text": "<PERSON>, French rower (b. 1864)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rower (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rower (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American painter and educator (b. 1849)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Czech violinist and composer (b. 1874)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Czech violinist and composer (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Czech violinist and composer (b. 1874)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1939", "text": "<PERSON>, Austrian-Polish nun and saint, founded the Congregation of the Ursulines of the Agonizing Heart of Jesus (b. 1865)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3chowska\" title=\"<PERSON>\"><PERSON></a>, Austrian-Polish nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_the_Ursulines_of_the_Agonizing_Heart_of_Jesus\" title=\"Congregation of the Ursulines of the Agonizing Heart of Jesus\">Congregation of the Ursulines of the Agonizing Heart of Jesus</a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3chowska\" title=\"<PERSON>\"><PERSON></a>, Austrian-Polish nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_the_Ursulines_of_the_Agonizing_Heart_of_Jesus\" title=\"Congregation of the Ursulines of the Agonizing Heart of Jesus\">Congregation of the Ursulines of the Agonizing Heart of Jesus</a> (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Led%C3%B3chowska"}, {"title": "Congregation of the Ursulines of the Agonizing Heart of Jesus", "link": "https://wikipedia.org/wiki/Congregation_of_the_Ursulines_of_the_Agonizing_Heart_of_Jesus"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian pianist, composer, and educator (b. 1892)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/L%C3%A9o-<PERSON>_<PERSON>\" title=\"<PERSON>éo<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian pianist, composer, and educator (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9o-<PERSON>_<PERSON>\" title=\"<PERSON>éo<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian pianist, composer, and educator (b. 1892)", "links": [{"title": "Léo-Pol <PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o-<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor (b. 1882)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, German SS officer (b. 1905)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1948", "text": "<PERSON>, English actress (b. 1865)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/May_Whit<PERSON>\" title=\"May Whit<PERSON>\">May <PERSON><PERSON><PERSON></a>, English actress (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_Whit<PERSON>\" title=\"May Whit<PERSON>\">May <PERSON><PERSON><PERSON></a>, English actress (b. 1865)", "links": [{"title": "May <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/May_Whitty"}]}, {"year": "1951", "text": "<PERSON>, American singer and comedian (b. 1891)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>\" title=\"<PERSON> Brice\"><PERSON></a>, American singer and comedian (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Brice\" title=\"<PERSON> Brice\"><PERSON></a>, American singer and comedian (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Greek-French soldier and composer (b. 1885)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-French soldier and composer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-French soldier and composer (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American painter and educator (b. 1886)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English director (b. 1889)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Spanish poet and academic, Nobel Prize laureate (b. 1881)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Jim%C3%A9nez\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Jim%C3%A9nez\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Jim%C3%A9nez"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1963", "text": "<PERSON><PERSON>, English author (b. 1887)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON> Muskett\"><PERSON><PERSON></a>, English author (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Muskett\" title=\"<PERSON><PERSON> Muskett\"><PERSON><PERSON></a>, English author (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ta_<PERSON>skett"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Estonian-French priest and psychologist (b. 1909)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-French priest and psychologist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-French priest and psychologist (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Estonian lawyer and politician, Estonian Minister of Education (b. 1896)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Education\" class=\"mw-redirect\" title=\"Estonian Minister of Education\">Estonian Minister of Education</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Education\" class=\"mw-redirect\" title=\"Estonian Minister of Education\">Estonian Minister of Education</a> (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Estonian Minister of Education", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_Education"}]}, {"year": "1970", "text": "<PERSON>, American journalist and author (b. 1901)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American artist (b. 1936)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player, coach, and spy (b. 1902)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and spy (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and spy (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Ukrainian-American engineer and academic (b. 1878)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American engineer and academic (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American engineer and academic (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English businessman (b. 1908)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Burmese politician, Prime Minister of Burma (b. 1893)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (b. 1893)", "links": [{"title": "Ba <PERSON>", "link": "https://wikipedia.org/wiki/Ba_Maw"}, {"title": "Prime Minister of Burma", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Burma"}]}, {"year": "1979", "text": "<PERSON>, Canadian-American actress, producer, and screenwriter, co-founder of United Artists (b. 1892)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress, producer, and screenwriter, co-founder of <a href=\"https://wikipedia.org/wiki/United_Artists\" title=\"United Artists\">United Artists</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress, producer, and screenwriter, co-founder of <a href=\"https://wikipedia.org/wiki/United_Artists\" title=\"United Artists\">United Artists</a> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Artists", "link": "https://wikipedia.org/wiki/United_Artists"}]}, {"year": "1979", "text": "<PERSON>, American lawyer and judge (b. 1916)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and judge (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and judge (b. 1916)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1982", "text": "<PERSON><PERSON>, German-French actress (b. 1938)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-French actress (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-French actress (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian-Russian historian and politician (b. 1899)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Arv%C4%ABds_Pel%C5%A1e\" title=\"Arvī<PERSON> Pelše\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian-Russian historian and politician (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arv%C4%ABds_Pel%C5%A1e\" title=\"Arvī<PERSON> Pelše\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian-Russian historian and politician (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arv%C4%ABds_Pel%C5%A1e"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Indian politician, 5th Prime Minister of India (b. 1902)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1988", "text": "<PERSON>, Saudi Arabian businessman (b. 1946)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Salem_bin_Laden\" title=\"Salem bin Laden\"><PERSON> bin <PERSON></a>, Saudi Arabian businessman (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salem_bin_Laden\" title=\"Salem bin Laden\"><PERSON> bin <PERSON>den</a>, Saudi Arabian businessman (b. 1946)", "links": [{"title": "Salem bin Laden", "link": "https://wikipedia.org/wiki/Salem_bin_Laden"}]}, {"year": "1989", "text": "<PERSON>, American sociologist and academic (b. 1910)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON> (choreographer), Australian choreographer and teacher of dance-drama (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON> (choreographer)</a>, Australian choreographer and teacher of dance-drama (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON> (choreographer)</a>, Australian choreographer and teacher of dance-drama (b. 1904)", "links": [{"title": "<PERSON> (choreographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(choreographer)"}]}, {"year": "1993", "text": "<PERSON>, American boxer (b. 1917)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German lawyer and politician (b. 1912)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "Lady <PERSON>, member of the British Royal Family (b. 1906)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady May <PERSON>\">Lady <PERSON></a>, member of the British Royal Family (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, member of the British Royal Family (b. 1906)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American ballerina and actress (b. 1919)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballerina and actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballerina and actress (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1966)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American general, activist, and politician (b. 1909)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, activist, and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, activist, and politician (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English motorcycle racer (b. 1972)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American lawyer and politician, 31st United States Solicitor General (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/United_States_Solicitor_General\" class=\"mw-redirect\" title=\"United States Solicitor General\">United States Solicitor General</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/United_States_Solicitor_General\" class=\"mw-redirect\" title=\"United States Solicitor General\">United States Solicitor General</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Solicitor General", "link": "https://wikipedia.org/wiki/United_States_Solicitor_General"}]}, {"year": "2004", "text": "<PERSON>, American academic and politician (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player and referee (b. 1937)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27A<PERSON><PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and referee (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27A<PERSON><PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and referee (b. 1937)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/John_<PERSON>%27Amico_(ice_hockey)"}]}, {"year": "2005", "text": "<PERSON>, South African surgeon (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Naki\"><PERSON></a>, South African surgeon (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Naki\"><PERSON></a>, South African surgeon (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Naki"}]}, {"year": "2005", "text": "<PERSON>, American soldier and composer (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and composer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and composer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Canadian businessman (b. 1930)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovak actress (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Katar%C3%ADna_Koln%C3%ADkov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Katar%C3%ADna_Koln%C3%ADkov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak actress (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Katar%C3%ADna_Koln%C3%ADkov%C3%A1"}]}, {"year": "2007", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Bermudian lawyer and politician (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bermudian lawyer and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bermudian lawyer and politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American writer (b. 1939)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Canadian ice hockey player (b. 1987)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor and comedian (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actor, director, and screenwriter (b. 1936)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Abkhazian politician, 2nd President of Abkhazia (b. 1949)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Abkhazian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Abkhazia\" title=\"President of Abkhazia\">President of Abkhazia</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Abkhazian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Abkhazia\" title=\"President of Abkhazia\">President of Abkhazia</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Abkhazia", "link": "https://wikipedia.org/wiki/President_of_Abkhazia"}]}, {"year": "2011", "text": "<PERSON>, American soldier and politician, 42nd Governor of Texas (b. 1917)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Texas", "link": "https://wikipedia.org/wiki/Governor_of_Texas"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Hungarian academic and politician, 14th President of Hungary (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%A1dl\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian academic and politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary\" title=\"List of heads of state of Hungary\">President of Hungary</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%A1dl\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian academic and politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary\" title=\"List of heads of state of Hungary\">President of Hungary</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_M%C3%A1dl"}, {"title": "List of heads of state of Hungary", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary"}]}, {"year": "2012", "text": "<PERSON>, Russian composer (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Japanese director, producer, and screenwriter (b. 1912)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Kaneto_Shindo\" title=\"Kaneto Shindo\"><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kaneto_Shindo\" title=\"Kaneto Shindo\"><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kaneto_Shindo"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American-English journalist and author (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English journalist and author (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English journalist and author (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, French actress (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American priest, sociologist, and author (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, sociologist, and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, sociologist, and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, American pianist and composer (b. 1955)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American pianist and composer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American pianist and composer (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Polish-Canadian physician and activist (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian physician and activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian physician and activist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>,  Italian actress and playwright (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress and playwright (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress and playwright (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franca_Rame"}]}, {"year": "2013", "text": "<PERSON>, German physician and academic (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and academic (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and academic (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Pakistani commander (b. 1970)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Pakistani commander (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Pakistani commander (b. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian singer-songwriter (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Swiss biologist and academic (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss biologist and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss biologist and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Czech-American scientist and engineer (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American scientist and engineer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American scientist and engineer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian composer and conductor (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian composer and conductor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian composer and conductor (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businessman (b. 1916)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American football player and sprinter (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sprinter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sprinter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American tennis player (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actress (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Panamanian general and politician, Military Leader of Panama (b. 1934)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama\" title=\"List of heads of state of Panama\">Military Leader of Panama</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama\" title=\"List of heads of state of Panama\">Military Leader of Panama</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of state of Panama", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli Lieutenant General and minister (b. 1924)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Mordechai_Tzipori\" title=\"Mordechai Tzipori\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli Lieutenant General and minister (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mordechai_Tzipori\" title=\"Mordechai Tzipori\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli Lieutenant General and minister (b. 1924)", "links": [{"title": "Mordechai Tzipori", "link": "https://wikipedia.org/wiki/Mordechai_Tzipori"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Greek politician and prime minister (b. 1918)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician and prime minister (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician and prime minister (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Nigerian engineer, former chief of state oil firm. (b. 1959)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian engineer, former chief of state oil firm. (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian engineer, former chief of state oil firm. (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "2021", "text": "<PERSON>, American actor, Christian activist, and author (b. 1931)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, Christian activist, and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, Christian activist, and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American basketball player and sportscaster (b. 1957)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer (b. 1942)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer (b. 1942)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Bruneian cardinal (b. 1951)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bruneian cardinal (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bruneian cardinal (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American rockabilly singer-songwriter and guitarist. (b. 1935)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rockabilly singer-songwriter and guitarist. (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rockabilly singer-songwriter and guitarist. (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Indian singer, rapper, actor and politician. (b. 1993)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer, rapper, actor and politician. (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer, rapper, actor and politician. (b. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wala"}]}, {"year": "2024", "text": "<PERSON>, Australian radio and television host (b. 1926)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)\" title=\"<PERSON> (DJ)\"><PERSON></a>, Australian radio and television host (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)\" title=\"<PERSON> (DJ)\"><PERSON></a>, Australian radio and television host (b. 1926)", "links": [{"title": "<PERSON> (DJ)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)"}]}]}}