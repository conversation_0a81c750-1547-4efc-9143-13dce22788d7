{"date": "December 5", "url": "https://wikipedia.org/wiki/December_5", "data": {"Events": [{"year": "63 BC", "text": "<PERSON> gives the fourth and final of the Catiline Orations.", "html": "63 BC - 63 BC - <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\"><PERSON></a> gives the fourth and final of the <a href=\"https://wikipedia.org/wiki/Catiline_Orations\" class=\"mw-redirect\" title=\"Catiline Orations\">Catiline Orations</a>.", "no_year_html": "63 BC - <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\"><PERSON></a> gives the fourth and final of the <a href=\"https://wikipedia.org/wiki/Catiline_Orations\" class=\"mw-redirect\" title=\"Catiline Orations\">Catiline Orations</a>.", "links": [{"title": "Cicero", "link": "https://wikipedia.org/wiki/Cicero"}, {"title": "Catiline Orations", "link": "https://wikipedia.org/wiki/Catiline_Orations"}]}, {"year": "633", "text": "Fourth Council of Toledo opens, presided over by <PERSON><PERSON><PERSON> of Seville.", "html": "633 - <a href=\"https://wikipedia.org/wiki/Fourth_Council_of_Toledo\" title=\"Fourth Council of Toledo\">Fourth Council of Toledo</a> opens, presided over by <a href=\"https://wikipedia.org/wiki/Is<PERSON>re_of_Seville\" title=\"<PERSON><PERSON><PERSON> of Seville\"><PERSON><PERSON><PERSON> of Seville</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fourth_Council_of_Toledo\" title=\"Fourth Council of Toledo\">Fourth Council of Toledo</a> opens, presided over by <a href=\"https://wikipedia.org/wiki/Is<PERSON>re_of_Seville\" title=\"<PERSON><PERSON><PERSON> of Seville\"><PERSON><PERSON><PERSON> of Seville</a>.", "links": [{"title": "Fourth Council of Toledo", "link": "https://wikipedia.org/wiki/Fourth_Council_of_Toledo"}, {"title": "<PERSON><PERSON><PERSON> of Seville", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Seville"}]}, {"year": "1033", "text": "The Jordan Rift Valley earthquake destroys multiple cities across the Levant, triggers a tsunami and kills many.", "html": "1033 - The <a href=\"https://wikipedia.org/wiki/1033_Jordan_Rift_Valley_earthquake\" class=\"mw-redirect\" title=\"1033 Jordan Rift Valley earthquake\">Jordan Rift Valley earthquake</a> destroys <a href=\"https://wikipedia.org/wiki/List_of_largest_cities_in_the_Levant_region_by_population\" title=\"List of largest cities in the Levant region by population\">multiple cities</a> across the <a href=\"https://wikipedia.org/wiki/Levant\" title=\"Levant\">Levant</a>, triggers a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> and kills many.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1033_Jordan_Rift_Valley_earthquake\" class=\"mw-redirect\" title=\"1033 Jordan Rift Valley earthquake\">Jordan Rift Valley earthquake</a> destroys <a href=\"https://wikipedia.org/wiki/List_of_largest_cities_in_the_Levant_region_by_population\" title=\"List of largest cities in the Levant region by population\">multiple cities</a> across the <a href=\"https://wikipedia.org/wiki/Levant\" title=\"Levant\">Levant</a>, triggers a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> and kills many.", "links": [{"title": "1033 Jordan Rift Valley earthquake", "link": "https://wikipedia.org/wiki/1033_Jordan_Rift_Valley_earthquake"}, {"title": "List of largest cities in the Levant region by population", "link": "https://wikipedia.org/wiki/List_of_largest_cities_in_the_Levant_region_by_population"}, {"title": "Levant", "link": "https://wikipedia.org/wiki/Levant"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}]}, {"year": "1082", "text": "<PERSON>, Count of Barcelona is assassinated, most likely by his brother, <PERSON><PERSON><PERSON><PERSON>.", "html": "1082 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Barcelona\" title=\"<PERSON>, Count of Barcelona\"><PERSON>, Count of Barcelona</a> is assassinated, most likely by his brother, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Barcelona\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Count of Barcelona\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Barcelona\" title=\"<PERSON>, Count of Barcelona\"><PERSON>, Count of Barcelona</a> is assassinated, most likely by his brother, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Barcelona\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Count of Barcelona\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>, Count of Barcelona", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_of_Barcelona"}, {"title": "<PERSON><PERSON><PERSON><PERSON>, Count of Barcelona", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Barcelona"}]}, {"year": "1408", "text": "Seeking to resubjugate Muscovy, <PERSON><PERSON> of the Golden Horde reaches Moscow, burning areas around the city but failing to take the city itself.", "html": "1408 - Seeking to resubjugate <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Moscow\" class=\"mw-redirect\" title=\"Grand Duchy of Moscow\">Muscovy</a>, Emir <a href=\"https://wikipedia.org/wiki/Edigu\" title=\"Edigu\">Edigu</a> of the <a href=\"https://wikipedia.org/wiki/Golden_Horde\" title=\"Golden Horde\">Golden Horde</a> reaches Moscow, burning areas around the city but failing to take the city itself.", "no_year_html": "Seeking to resubjugate <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Moscow\" class=\"mw-redirect\" title=\"Grand Duchy of Moscow\">Muscovy</a>, Emir <a href=\"https://wikipedia.org/wiki/Edigu\" title=\"Edigu\">Edigu</a> of the <a href=\"https://wikipedia.org/wiki/Golden_Horde\" title=\"Golden Horde\">Golden Horde</a> reaches Moscow, burning areas around the city but failing to take the city itself.", "links": [{"title": "Grand Duchy of Moscow", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Moscow"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edigu"}, {"title": "Golden Horde", "link": "https://wikipedia.org/wiki/Golden_Horde"}]}, {"year": "1456", "text": "The first of two earthquakes measuring Mw  7.2 strikes Italy, causing extreme destruction and killing upwards of 70,000 people.", "html": "1456 - The first of <a href=\"https://wikipedia.org/wiki/1456_Central_Italy_earthquakes\" title=\"1456 Central Italy earthquakes\">two earthquakes</a> measuring M<sub>w</sub>  7.2 strikes Italy, causing extreme destruction and killing upwards of 70,000 people.", "no_year_html": "The first of <a href=\"https://wikipedia.org/wiki/1456_Central_Italy_earthquakes\" title=\"1456 Central Italy earthquakes\">two earthquakes</a> measuring M<sub>w</sub>  7.2 strikes Italy, causing extreme destruction and killing upwards of 70,000 people.", "links": [{"title": "1456 Central Italy earthquakes", "link": "https://wikipedia.org/wiki/1456_Central_Italy_earthquakes"}]}, {"year": "1484", "text": "<PERSON> <PERSON> issues the Summis desiderantes affectibus, a papal bull that deputizes <PERSON> and <PERSON> as inquisitors to root out alleged witchcraft in Germany.", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_VIII\" title=\"Pope Innocent VIII\">Pope Innocent VIII</a> issues the <i><a href=\"https://wikipedia.org/wiki/Summis_desiderantes_affectibus\" title=\"Summis desiderantes affectibus\">Summis desiderantes affectibus</a></i>, a <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> that deputizes <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Inquisition\" title=\"Inquisition\">inquisitors</a> to root out alleged <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> in Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_VIII\" title=\"Pope Innocent VIII\">Pope <PERSON> VIII</a> issues the <i><a href=\"https://wikipedia.org/wiki/Summ<PERSON>_desiderantes_affectibus\" title=\"Summis desiderantes affectibus\">Summis desiderantes affectibus</a></i>, a <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> that deputizes <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Inquisition\" title=\"Inquisition\">inquisitors</a> to root out alleged <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> in Germany.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_VIII"}, {"title": "<PERSON><PERSON><PERSON> desiderantes affectibus", "link": "https://wikipedia.org/wiki/Su<PERSON><PERSON>_desiderantes_affectibus"}, {"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ger"}, {"title": "Inquisition", "link": "https://wikipedia.org/wiki/Inquisition"}, {"title": "Witchcraft", "link": "https://wikipedia.org/wiki/Witchcraft"}]}, {"year": "1496", "text": "King <PERSON> of Portugal issues a decree ordering the expulsion of Jews from the country.", "html": "1496 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> issues a decree ordering the expulsion of Jews from the country.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> issues a decree ordering the expulsion of Jews from the country.", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1560", "text": "Thirteen-year-old <PERSON> becomes king of France, with Queen <PERSON> as regent.", "html": "1560 - Thirteen-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IX of France\"><PERSON> IX</a> becomes king of France, with Queen <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as regent.", "no_year_html": "Thirteen-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IX of France\"><PERSON></a> becomes king of France, with Queen <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as regent.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_IX_of_France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_Medici"}]}, {"year": "1578", "text": "Sir <PERSON>, after sailing through Strait of Magellan, raids Valparaiso.", "html": "1578 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, after sailing through <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>, raids <a href=\"https://wikipedia.org/wiki/Valparaiso\" class=\"mw-redirect\" title=\"Valparaiso\">Valparaiso</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, after sailing through <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>, raids <a href=\"https://wikipedia.org/wiki/Valparaiso\" class=\"mw-redirect\" title=\"Valparaiso\">Valparaiso</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Strait of Magellan", "link": "https://wikipedia.org/wiki/Strait_of_Magellan"}, {"title": "Valparaiso", "link": "https://wikipedia.org/wiki/Valparaiso"}]}, {"year": "1649", "text": "The town of Raahe (Swedish: Brahestad) is founded by Count <PERSON> the Younger.", "html": "1649 - The town of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\">Brahestad</i>) is founded by Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_Younger\" title=\"<PERSON> <PERSON><PERSON> the Younger\"><PERSON><PERSON> the Younger</a>.", "no_year_html": "The town of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\">Brahestad</i>) is founded by Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON> the Younger</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e"}, {"title": "Swedish language", "link": "https://wikipedia.org/wiki/Swedish_language"}, {"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_<PERSON>"}]}, {"year": "1757", "text": "Seven Years' War: Battle of Leuthen: <PERSON> of Prussia leads Prussian forces to a decisive victory over Austrian forces under Prince <PERSON> of Lorraine.", "html": "1757 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Leuthen\" title=\"Battle of Leuthen\">Battle of Leuthen</a>: <a href=\"https://wikipedia.org/wiki/Frederick_II_of_Prussia\" class=\"mw-redirect\" title=\"Frederick <PERSON> of Prussia\"><PERSON> of Prussia</a> leads <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussian</a> forces to a decisive victory over <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> forces under <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Lorraine\" title=\"Prince <PERSON> of Lorraine\">Prince <PERSON> of Lorraine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Leuthen\" title=\"Battle of Leuthen\">Battle of Leuthen</a>: <a href=\"https://wikipedia.org/wiki/Frederick_II_of_Prussia\" class=\"mw-redirect\" title=\"Frederick <PERSON> of Prussia\"><PERSON> of Prussia</a> leads <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussian</a> forces to a decisive victory over <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> forces under <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Lorraine\" title=\"Prince <PERSON> of <PERSON>\">Prince <PERSON> of Lorraine</a>.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "Battle of Leuthen", "link": "https://wikipedia.org/wiki/Battle_of_Leuthen"}, {"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Frederick_II_of_Prussia"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "Prince <PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Lorraine"}]}, {"year": "1766", "text": "In London, auctioneer <PERSON> holds his first sale.", "html": "1766 - In London, auctioneer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auctioneer)\" title=\"<PERSON> (auctioneer)\"><PERSON></a> holds his first sale.", "no_year_html": "In London, auctioneer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auctioneer)\" title=\"<PERSON> (auctioneer)\"><PERSON></a> holds his first sale.", "links": [{"title": "<PERSON> (auctioneer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auctioneer)"}]}, {"year": "1770", "text": "29th Regiment of Foot privates <PERSON> and <PERSON> are found guilty for the manslaughter of <PERSON><PERSON><PERSON> and <PERSON> respectively in the Boston Massacre.", "html": "1770 - <a href=\"https://wikipedia.org/wiki/29th_Regiment_of_Foot\" class=\"mw-redirect\" title=\"29th Regiment of Foot\">29th Regiment of Foot</a> <a href=\"https://wikipedia.org/wiki/Private_(rank)\" title=\"Private (rank)\">privates</a> <a href=\"https://wikipedia.org/wiki/<PERSON>(British_Army_soldier)\" title=\"<PERSON> (British Army soldier)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_soldier)\" title=\"<PERSON> (British Army soldier)\"><PERSON></a> are found guilty for the manslaughter of <a href=\"https://wikipedia.org/wiki/Crispus_Attucks\" title=\"Crispus Attucks\">Crispus Attucks</a> and <PERSON> respectively in the <a href=\"https://wikipedia.org/wiki/Boston_Massacre\" title=\"Boston Massacre\">Boston Massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/29th_Regiment_of_Foot\" class=\"mw-redirect\" title=\"29th Regiment of Foot\">29th Regiment of Foot</a> <a href=\"https://wikipedia.org/wiki/Private_(rank)\" title=\"Private (rank)\">privates</a> <a href=\"https://wikipedia.org/wiki/<PERSON>(British_Army_soldier)\" title=\"<PERSON> (British Army soldier)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_soldier)\" title=\"<PERSON> (British Army soldier)\"><PERSON></a> are found guilty for the manslaughter of <a href=\"https://wikipedia.org/wiki/Crispus_Attucks\" title=\"Crispus Attucks\">Crispus Attucks</a> and <PERSON> respectively in the <a href=\"https://wikipedia.org/wiki/Boston_Massacre\" title=\"Boston Massacre\">Boston Massacre</a>.", "links": [{"title": "29th Regiment of Foot", "link": "https://wikipedia.org/wiki/29th_Regiment_of_Foot"}, {"title": "Private (rank)", "link": "https://wikipedia.org/wiki/Private_(rank)"}, {"title": "<PERSON> (British Army soldier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_soldier)"}, {"title": "<PERSON> (British Army soldier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_soldier)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Crispus_Attucks"}, {"title": "Boston Massacre", "link": "https://wikipedia.org/wiki/Boston_Massacre"}]}, {"year": "1775", "text": "At Fort Ticonderoga, <PERSON> begins his historic transport of artillery to Cambridge, Massachusetts.", "html": "1775 - At <a href=\"https://wikipedia.org/wiki/Fort_Ticonderoga\" title=\"Fort Ticonderoga\">Fort Ticonderoga</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his historic <a href=\"https://wikipedia.org/wiki/Noble_train_of_artillery\" title=\"Noble train of artillery\">transport of artillery</a> to <a href=\"https://wikipedia.org/wiki/Cambridge,_Massachusetts\" title=\"Cambridge, Massachusetts\">Cambridge, Massachusetts</a>.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Fort_Ticonderoga\" title=\"Fort Ticonderoga\">Fort Ticonderoga</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his historic <a href=\"https://wikipedia.org/wiki/Noble_train_of_artillery\" title=\"Noble train of artillery\">transport of artillery</a> to <a href=\"https://wikipedia.org/wiki/Cambridge,_Massachusetts\" title=\"Cambridge, Massachusetts\">Cambridge, Massachusetts</a>.", "links": [{"title": "Fort Ticonderoga", "link": "https://wikipedia.org/wiki/Fort_Ticonderoga"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Noble train of artillery", "link": "https://wikipedia.org/wiki/Noble_train_of_artillery"}, {"title": "Cambridge, Massachusetts", "link": "https://wikipedia.org/wiki/Cambridge,_Massachusetts"}]}, {"year": "1776", "text": "Phi Beta Kappa, the oldest academic honor society in the U.S., holds its first meeting at the College of William & Mary.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/Phi_Beta_Kappa\" title=\"Phi Beta Kappa\">Phi Beta Kappa</a>, the oldest academic honor society in the U.S., holds its first meeting at the <a href=\"https://wikipedia.org/wiki/College_of_William_%26_Mary\" title=\"College of William &amp; Mary\">College of William &amp; Mary</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phi_Beta_Kappa\" title=\"Phi Beta Kappa\">Phi Beta Kappa</a>, the oldest academic honor society in the U.S., holds its first meeting at the <a href=\"https://wikipedia.org/wiki/College_of_William_%26_Mary\" title=\"College of William &amp; Mary\">College of William &amp; Mary</a>.", "links": [{"title": "Phi Beta Kappa", "link": "https://wikipedia.org/wiki/Phi_Beta_Kappa"}, {"title": "College of William & Mary", "link": "https://wikipedia.org/wiki/<PERSON>_of_<PERSON>_%26_Mary"}]}, {"year": "1831", "text": "Former U.S. President <PERSON> takes his seat in the House of Representatives.", "html": "1831 - Former U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes his seat in the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a>.", "no_year_html": "Former U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes his seat in the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}]}, {"year": "1847", "text": "<PERSON> is elected to the U.S. Senate.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected to the U.S. Senate.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected to the U.S. Senate.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "California Gold Rush: In a message to the United States Congress, U.S. President <PERSON> confirms that large amounts of gold had been discovered in California.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/California_Gold_Rush\" class=\"mw-redirect\" title=\"California Gold Rush\">California Gold Rush</a>: In a message to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> confirms that large amounts of gold had been discovered in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/California_Gold_Rush\" class=\"mw-redirect\" title=\"California Gold Rush\">California Gold Rush</a>: In a message to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> confirms that large amounts of gold had been discovered in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "links": [{"title": "California Gold Rush", "link": "https://wikipedia.org/wiki/California_Gold_Rush"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1865", "text": "Chincha Islands War: Peru allies with Chile against Spain.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Chincha_Islands_War\" title=\"Chincha Islands War\">Chincha Islands War</a>: <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> allies with <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> against Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chincha_Islands_War\" title=\"Chincha Islands War\">Chincha Islands War</a>: <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> allies with <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> against Spain.", "links": [{"title": "Chincha Islands War", "link": "https://wikipedia.org/wiki/Chincha_Islands_War"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}]}, {"year": "1895", "text": "New Haven Symphony Orchestra of Connecticut performs its first concert.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/New_Haven_Symphony_Orchestra\" title=\"New Haven Symphony Orchestra\">New Haven Symphony Orchestra</a> of Connecticut performs its first concert.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Haven_Symphony_Orchestra\" title=\"New Haven Symphony Orchestra\">New Haven Symphony Orchestra</a> of Connecticut performs its first concert.", "links": [{"title": "New Haven Symphony Orchestra", "link": "https://wikipedia.org/wiki/New_Haven_Symphony_Orchestra"}]}, {"year": "1914", "text": "The Imperial Trans-Antarctic Expedition began in an attempt to make the first land crossing of Antarctica.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a> began in an attempt to make the first land crossing of <a href=\"https://wikipedia.org/wiki/Antarctica\" title=\"Antarctica\">Antarctica</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a> began in an attempt to make the first land crossing of <a href=\"https://wikipedia.org/wiki/Antarctica\" title=\"Antarctica\">Antarctica</a>.", "links": [{"title": "Imperial Trans-Antarctic Expedition", "link": "https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition"}, {"title": "Antarctica", "link": "https://wikipedia.org/wiki/Antarctica"}]}, {"year": "1919", "text": "Ukrainian War of Independence: The <PERSON>nsky conspiracy is suppressed and its participants are executed by the Kontrrazvedka.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Polonsky_conspiracy\" title=\"Polonsky conspiracy\">Polonsky conspiracy</a> is suppressed and its participants are executed by the <a href=\"https://wikipedia.org/wiki/Kontrrazvedka\" title=\"Kontrrazvedka\">Kontrrazvedka</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Polonsky_conspiracy\" title=\"Polonsky conspiracy\">Polonsky conspiracy</a> is suppressed and its participants are executed by the <a href=\"https://wikipedia.org/wiki/Kontrrazvedka\" title=\"Kontrrazvedka\">Kontrrazvedka</a>.", "links": [{"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}, {"title": "<PERSON><PERSON> conspiracy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_conspiracy"}, {"title": "Kontrrazvedka", "link": "https://wikipedia.org/wiki/Kontrrazvedka"}]}, {"year": "1921", "text": "The Football Association bans women's football in England from league grounds, a ban that stays in place for 50 years.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/The_Football_Association\" title=\"The Football Association\">The Football Association</a> bans <a href=\"https://wikipedia.org/wiki/Women%27s_football_in_England\" title=\"Women's football in England\">women's football in England</a> from league grounds, a ban that stays in place for 50 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Football_Association\" title=\"The Football Association\">The Football Association</a> bans <a href=\"https://wikipedia.org/wiki/Women%27s_football_in_England\" title=\"Women's football in England\">women's football in England</a> from league grounds, a ban that stays in place for 50 years.", "links": [{"title": "The Football Association", "link": "https://wikipedia.org/wiki/The_Football_Association"}, {"title": "Women's football in England", "link": "https://wikipedia.org/wiki/Women%27s_football_in_England"}]}, {"year": "1933", "text": "The Twenty-first Amendment to the United States Constitution is ratified, repealing Prohibition in the United States.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/Twenty-first_Amendment_to_the_United_States_Constitution\" title=\"Twenty-first Amendment to the United States Constitution\">Twenty-first Amendment to the United States Constitution</a> is ratified, repealing <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Prohibition in the United States</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Twenty-first_Amendment_to_the_United_States_Constitution\" title=\"Twenty-first Amendment to the United States Constitution\">Twenty-first Amendment to the United States Constitution</a> is ratified, repealing <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Prohibition in the United States</a>.", "links": [{"title": "Twenty-first Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-first_Amendment_to_the_United_States_Constitution"}, {"title": "Prohibition in the United States", "link": "https://wikipedia.org/wiki/Prohibition_in_the_United_States"}]}, {"year": "1934", "text": "Abyssinia Crisis: Italian troops attack Wal Wal in Abyssinia, taking four days to capture the city.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Abyssinia_Crisis\" title=\"Abyssinia Crisis\">Abyssinia Crisis</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> troops attack <a href=\"https://wikipedia.org/wiki/Wal_Wal\" class=\"mw-redirect\" title=\"Wal Wal\">Wal Wal</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Abyssinia</a>, taking four days to capture the city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abyssinia_Crisis\" title=\"Abyssinia Crisis\">Abyssinia Crisis</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> troops attack <a href=\"https://wikipedia.org/wiki/Wal_Wal\" class=\"mw-redirect\" title=\"Wal Wal\">Wal Wal</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Abyssinia</a>, taking four days to capture the city.", "links": [{"title": "Abyssinia Crisis", "link": "https://wikipedia.org/wiki/Abyssinia_Crisis"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Wal <PERSON>al", "link": "https://wikipedia.org/wiki/Wal_Wal"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1935", "text": "<PERSON> founds the National Council of Negro Women in New York City.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/National_Council_of_Negro_Women\" title=\"National Council of Negro Women\">National Council of Negro Women</a> in New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/National_Council_of_Negro_Women\" title=\"National Council of Negro Women\">National Council of Negro Women</a> in New York City.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "National Council of Negro Women", "link": "https://wikipedia.org/wiki/National_Council_of_Negro_Women"}]}, {"year": "1936", "text": "The Soviet Union adopts a new constitution and the Kirghiz Soviet Socialist Republic is established as a full Union Republic of the USSR.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> adopts a new <a href=\"https://wikipedia.org/wiki/1936_Constitution_of_the_Soviet_Union\" title=\"1936 Constitution of the Soviet Union\">constitution</a> and the <a href=\"https://wikipedia.org/wiki/Kirghiz_Soviet_Socialist_Republic\" title=\"Kirghiz Soviet Socialist Republic\">Kirghiz Soviet Socialist Republic</a> is established as a full <a href=\"https://wikipedia.org/wiki/Republics_of_the_Soviet_Union\" title=\"Republics of the Soviet Union\">Union Republic</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> adopts a new <a href=\"https://wikipedia.org/wiki/1936_Constitution_of_the_Soviet_Union\" title=\"1936 Constitution of the Soviet Union\">constitution</a> and the <a href=\"https://wikipedia.org/wiki/Kirghiz_Soviet_Socialist_Republic\" title=\"Kirghiz Soviet Socialist Republic\">Kirghiz Soviet Socialist Republic</a> is established as a full <a href=\"https://wikipedia.org/wiki/Republics_of_the_Soviet_Union\" title=\"Republics of the Soviet Union\">Union Republic</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "1936 Constitution of the Soviet Union", "link": "https://wikipedia.org/wiki/1936_Constitution_of_the_Soviet_Union"}, {"title": "Kirghiz Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Kirghiz_Soviet_Socialist_Republic"}, {"title": "Republics of the Soviet Union", "link": "https://wikipedia.org/wiki/Republics_of_the_Soviet_Union"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1941", "text": "World War II: In the Battle of Moscow, <PERSON><PERSON> launches a massive Soviet counter-attack against the German army.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Moscow\" title=\"Battle of Moscow\">Battle of Moscow</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> launches a massive Soviet counter-attack against the German army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Moscow\" title=\"Battle of Moscow\">Battle of Moscow</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> launches a massive Soviet counter-attack against the German army.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Moscow", "link": "https://wikipedia.org/wiki/Battle_of_Moscow"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "World War II: Great Britain declares war on Finland, Hungary and Romania.", "html": "1941 - World War II: Great Britain declares war on <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Hungary</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>.", "no_year_html": "World War II: Great Britain declares war on <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Hungary</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>.", "links": [{"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Kingdom of Hungary", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary"}, {"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}]}, {"year": "1943", "text": "World War II: Allied air forces begin attacking Germany's secret weapons bases in Operation Crossbow.", "html": "1943 - World War II: Allied air forces begin attacking Germany's secret weapons bases in <a href=\"https://wikipedia.org/wiki/Operation_Crossbow\" title=\"Operation Crossbow\">Operation Crossbow</a>.", "no_year_html": "World War II: Allied air forces begin attacking Germany's secret weapons bases in <a href=\"https://wikipedia.org/wiki/Operation_Crossbow\" title=\"Operation Crossbow\">Operation Crossbow</a>.", "links": [{"title": "Operation Crossbow", "link": "https://wikipedia.org/wiki/Operation_Crossbow"}]}, {"year": "1945", "text": "Flight 19, a group of TBF Avengers, disappears in the Bermuda Triangle.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Flight_19\" title=\"Flight 19\">Flight 19</a>, a group of TBF Avengers, disappears in the <a href=\"https://wikipedia.org/wiki/Bermuda_Triangle\" title=\"Bermuda Triangle\">Bermuda Triangle</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flight_19\" title=\"Flight 19\">Flight 19</a>, a group of TBF Avengers, disappears in the <a href=\"https://wikipedia.org/wiki/Bermuda_Triangle\" title=\"Bermuda Triangle\">Bermuda Triangle</a>.", "links": [{"title": "Flight 19", "link": "https://wikipedia.org/wiki/Flight_19"}, {"title": "Bermuda Triangle", "link": "https://wikipedia.org/wiki/Bermuda_Triangle"}]}, {"year": "1952", "text": "Beginning of the Great Smog in London. A cold fog combines with air pollution and brings the city to a standstill for four days. Later, a Ministry of Health report estimates 4,000 fatalities as a result of it.", "html": "1952 - Beginning of the <a href=\"https://wikipedia.org/wiki/Great_Smog\" class=\"mw-redirect\" title=\"Great Smog\">Great Smog</a> in London. A cold fog combines with air pollution and brings the city to a standstill for four days. Later, a <a href=\"https://wikipedia.org/wiki/Department_of_Health_and_Social_Care\" title=\"Department of Health and Social Care\">Ministry of Health</a> report estimates 4,000 fatalities as a result of it.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/Great_Smog\" class=\"mw-redirect\" title=\"Great Smog\">Great Smog</a> in London. A cold fog combines with air pollution and brings the city to a standstill for four days. Later, a <a href=\"https://wikipedia.org/wiki/Department_of_Health_and_Social_Care\" title=\"Department of Health and Social Care\">Ministry of Health</a> report estimates 4,000 fatalities as a result of it.", "links": [{"title": "Great Smog", "link": "https://wikipedia.org/wiki/Great_Smog"}, {"title": "Department of Health and Social Care", "link": "https://wikipedia.org/wiki/Department_of_Health_and_Social_Care"}]}, {"year": "1955", "text": "The American Federation of Labor and the Congress of Industrial Organizations merge and form the AFL-CIO.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/American_Federation_of_Labor\" title=\"American Federation of Labor\">American Federation of Labor</a> and the <a href=\"https://wikipedia.org/wiki/Congress_of_Industrial_Organizations\" title=\"Congress of Industrial Organizations\">Congress of Industrial Organizations</a> merge and form the <a href=\"https://wikipedia.org/wiki/AFL-CIO\" title=\"AFL-CIO\">AFL-CIO</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_Federation_of_Labor\" title=\"American Federation of Labor\">American Federation of Labor</a> and the <a href=\"https://wikipedia.org/wiki/Congress_of_Industrial_Organizations\" title=\"Congress of Industrial Organizations\">Congress of Industrial Organizations</a> merge and form the <a href=\"https://wikipedia.org/wiki/AFL-CIO\" title=\"AFL-CIO\">AFL-CIO</a>.", "links": [{"title": "American Federation of Labor", "link": "https://wikipedia.org/wiki/American_Federation_of_Labor"}, {"title": "Congress of Industrial Organizations", "link": "https://wikipedia.org/wiki/Congress_of_Industrial_Organizations"}, {"title": "AFL-CIO", "link": "https://wikipedia.org/wiki/AFL-CIO"}]}, {"year": "1955", "text": "<PERSON><PERSON> <PERSON><PERSON> and <PERSON> lead the Montgomery bus boycott.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rosa Parks\"><PERSON></a> lead the <a href=\"https://wikipedia.org/wiki/Montgomery_bus_boycott\" title=\"Montgomery bus boycott\">Montgomery bus boycott</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rosa Parks\"><PERSON></a> lead the <a href=\"https://wikipedia.org/wiki/Montgomery_bus_boycott\" title=\"Montgomery bus boycott\">Montgomery bus boycott</a>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Rosa <PERSON>", "link": "https://wikipedia.org/wiki/Rosa_Parks"}, {"title": "Montgomery bus boycott", "link": "https://wikipedia.org/wiki/Montgomery_bus_boycott"}]}, {"year": "1958", "text": "Subscriber <PERSON><PERSON>alling (STD) is inaugurated in the United Kingdom by Queen <PERSON> when she speaks to the Lord <PERSON> in a call from Bristol to Edinburgh.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Subscriber_trunk_dialling\" title=\"Subscriber trunk dialling\">Subscriber Trunk Dialling (STD)</a> is inaugurated in the United Kingdom by Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a> when she speaks to the <a href=\"https://wikipedia.org/wiki/Lord_Provost\" class=\"mw-redirect\" title=\"Lord Provost\">Lord <PERSON></a> in a call from <a href=\"https://wikipedia.org/wiki/Bristol\" title=\"Bristol\">Bristol</a> to <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Subscriber_trunk_dialling\" title=\"Subscriber trunk dialling\">Subscriber Trunk Dialling (STD)</a> is inaugurated in the United Kingdom by Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a> when she speaks to the <a href=\"https://wikipedia.org/wiki/Lord_Provost\" class=\"mw-redirect\" title=\"Lord Provost\">Lord <PERSON></a> in a call from <a href=\"https://wikipedia.org/wiki/Bristol\" title=\"Bristol\">Bristol</a> to <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>.", "links": [{"title": "Subscriber trunk dialling", "link": "https://wikipedia.org/wiki/Subscriber_trunk_dialling"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Lord Provost", "link": "https://wikipedia.org/wiki/Lord_Provost"}, {"title": "Bristol", "link": "https://wikipedia.org/wiki/Bristol"}, {"title": "Edinburgh", "link": "https://wikipedia.org/wiki/Edinburgh"}]}, {"year": "1958", "text": "The Preston By-pass, the UK's first stretch of motorway, opens to traffic for the first time. (It is now part of the M6 and M55 motorways.)", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Preston_By-pass\" class=\"mw-redirect\" title=\"Preston By-pass\">Preston By-pass</a>, the UK's first stretch of <a href=\"https://wikipedia.org/wiki/Motorway\" class=\"mw-redirect\" title=\"Motorway\">motorway</a>, opens to traffic for the first time. (It is now part of the <a href=\"https://wikipedia.org/wiki/M6_motorway\" title=\"M6 motorway\">M6</a> and <a href=\"https://wikipedia.org/wiki/M55_motorway\" title=\"M55 motorway\">M55</a> motorways.)", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Preston_By-pass\" class=\"mw-redirect\" title=\"Preston By-pass\">Preston By-pass</a>, the UK's first stretch of <a href=\"https://wikipedia.org/wiki/Motorway\" class=\"mw-redirect\" title=\"Motorway\">motorway</a>, opens to traffic for the first time. (It is now part of the <a href=\"https://wikipedia.org/wiki/M6_motorway\" title=\"M6 motorway\">M6</a> and <a href=\"https://wikipedia.org/wiki/M55_motorway\" title=\"M55 motorway\">M55</a> motorways.)", "links": [{"title": "<PERSON> By-pass", "link": "https://wikipedia.org/wiki/Preston_By-pass"}, {"title": "Motorway", "link": "https://wikipedia.org/wiki/Motorway"}, {"title": "M6 motorway", "link": "https://wikipedia.org/wiki/M6_motorway"}, {"title": "M55 motorway", "link": "https://wikipedia.org/wiki/M55_motorway"}]}, {"year": "1964", "text": "Vietnam War: For his heroism in battle earlier in the year, Captain <PERSON> is awarded the first Medal of Honor of the war.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: For his heroism in battle earlier in the year, Captain <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the first <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: For his heroism in battle earlier in the year, Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the first <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> of the war.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1964", "text": "<PERSON> discovers the first linkage between the major histocompatibility complex (MHC) and disease—mouse leukemia—opening the way for the recognition of the importance of the MHC in the immune response.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the first linkage between the <a href=\"https://wikipedia.org/wiki/Major_histocompatibility_complex\" title=\"Major histocompatibility complex\">major histocompatibility complex</a> (MHC) and disease—mouse leukemia—opening the way for the recognition of the importance of the MHC in the immune response.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the first linkage between the <a href=\"https://wikipedia.org/wiki/Major_histocompatibility_complex\" title=\"Major histocompatibility complex\">major histocompatibility complex</a> (MHC) and disease—mouse leukemia—opening the way for the recognition of the importance of the MHC in the immune response.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Major histocompatibility complex", "link": "https://wikipedia.org/wiki/Major_histocompatibility_complex"}]}, {"year": "1971", "text": "Battle of Gazipur: Pakistani forces stand defeated as India cedes Gazipur to Bangladesh.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Battle_of_Gazipur\" title=\"Battle of Gazipur\">Battle of Gazipur</a>: Pakistani forces stand defeated as India cedes <a href=\"https://wikipedia.org/wiki/Gazipur\" title=\"Gazipur\">Gazipur</a> to <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Gazipur\" title=\"Battle of Gazipur\">Battle of Gazipur</a>: Pakistani forces stand defeated as India cedes <a href=\"https://wikipedia.org/wiki/Gazipur\" title=\"Gazipur\">Gazipur</a> to <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "links": [{"title": "Battle of Gazipur", "link": "https://wikipedia.org/wiki/Battle_of_Gazipur"}, {"title": "Gazipur", "link": "https://wikipedia.org/wiki/Gazipur"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1977", "text": "Egypt breaks diplomatic relations with Syria, Libya, Algeria, Iraq and South Yemen in retaliation to preventing President <PERSON><PERSON> from pursuing negotiations with Israel at the Tripoli confer.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> <a href=\"https://wikipedia.org/wiki/Egypt%E2%80%93Iraq_relations\" title=\"Egypt-Iraq relations\">breaks diplomatic relations</a> with <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>, <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> and <a href=\"https://wikipedia.org/wiki/South_Yemen\" title=\"South Yemen\">South Yemen</a> in retaliation to preventing President <a href=\"https://wikipedia.org/wiki/Anwar_Sadat\" title=\"Anwar Sadat\"><PERSON><PERSON> <PERSON></a> from pursuing negotiations with Israel at the Tripoli confer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> <a href=\"https://wikipedia.org/wiki/Egypt%E2%80%93Iraq_relations\" title=\"Egypt-Iraq relations\">breaks diplomatic relations</a> with <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>, <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> and <a href=\"https://wikipedia.org/wiki/South_Yemen\" title=\"South Yemen\">South Yemen</a> in retaliation to preventing President <a href=\"https://wikipedia.org/wiki/Anwar_Sadat\" title=\"Anwar Sadat\"><PERSON><PERSON> <PERSON></a> from pursuing negotiations with Israel at the Tripoli confer.", "links": [{"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Egypt-Iraq relations", "link": "https://wikipedia.org/wiki/Egypt%E2%80%93Iraq_relations"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "South Yemen", "link": "https://wikipedia.org/wiki/South_Yemen"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>at"}]}, {"year": "1983", "text": "Dissolution of the Military Junta in Argentina.", "html": "1983 - Dissolution of the <a href=\"https://wikipedia.org/wiki/National_Reorganization_Process\" title=\"National Reorganization Process\">Military Junta</a> in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "no_year_html": "Dissolution of the <a href=\"https://wikipedia.org/wiki/National_Reorganization_Process\" title=\"National Reorganization Process\">Military Junta</a> in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "links": [{"title": "National Reorganization Process", "link": "https://wikipedia.org/wiki/National_Reorganization_Process"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1991", "text": "<PERSON><PERSON> is elected the first president of Ukraine.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is elected the first <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">president of Ukraine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is elected the first <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">president of Ukraine</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Ukraine", "link": "https://wikipedia.org/wiki/President_of_Ukraine"}]}, {"year": "1994", "text": "The Budapest Memorandum is signed at the OSCE conference in Budapest, Hungary.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/Budapest_Memorandum\" title=\"Budapest Memorandum\">Budapest Memorandum</a> is signed at the <a href=\"https://wikipedia.org/wiki/Organization_for_Security_and_Co-operation_in_Europe\" title=\"Organization for Security and Co-operation in Europe\">OSCE</a> conference in <a href=\"https://wikipedia.org/wiki/Budapest\" title=\"Budapest\">Budapest</a>, <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Budapest_Memorandum\" title=\"Budapest Memorandum\">Budapest Memorandum</a> is signed at the <a href=\"https://wikipedia.org/wiki/Organization_for_Security_and_Co-operation_in_Europe\" title=\"Organization for Security and Co-operation in Europe\">OSCE</a> conference in <a href=\"https://wikipedia.org/wiki/Budapest\" title=\"Budapest\">Budapest</a>, <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a>.", "links": [{"title": "Budapest Memorandum", "link": "https://wikipedia.org/wiki/Budapest_Memorandum"}, {"title": "Organization for Security and Co-operation in Europe", "link": "https://wikipedia.org/wiki/Organization_for_Security_and_Co-operation_in_Europe"}, {"title": "Budapest", "link": "https://wikipedia.org/wiki/Budapest"}, {"title": "Hungary", "link": "https://wikipedia.org/wiki/Hungary"}]}, {"year": "1995", "text": "Sri Lankan Civil War: Sri Lanka's government announces the conquest of the Tamil stronghold of Jaffna.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>'s government announces the <a href=\"https://wikipedia.org/wiki/Battle_of_Jaffna_(1995)\" title=\"Battle of Jaffna (1995)\">conquest</a> of the <a href=\"https://wikipedia.org/wiki/Tamil_people\" class=\"mw-redirect\" title=\"Tamil people\">Tamil</a> stronghold of <a href=\"https://wikipedia.org/wiki/Jaffna\" title=\"Jaffna\">Jaffna</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>'s government announces the <a href=\"https://wikipedia.org/wiki/Battle_of_Jaffna_(1995)\" title=\"Battle of Jaffna (1995)\">conquest</a> of the <a href=\"https://wikipedia.org/wiki/Tamil_people\" class=\"mw-redirect\" title=\"Tamil people\">Tamil</a> stronghold of <a href=\"https://wikipedia.org/wiki/Jaffna\" title=\"Jaffna\">Jaffna</a>.", "links": [{"title": "Sri Lankan civil war", "link": "https://wikipedia.org/wiki/Sri_Lankan_civil_war"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}, {"title": "Battle of Jaffna (1995)", "link": "https://wikipedia.org/wiki/Battle_of_Jaffna_(1995)"}, {"title": "Tamil people", "link": "https://wikipedia.org/wiki/Tamil_people"}, {"title": "Jaffna", "link": "https://wikipedia.org/wiki/Jaffna"}]}, {"year": "1995", "text": "Azerbaijan Airlines Flight A-56 crashes near Nakhchivan International Airport in Nakhchivan, Azerbaijan, killing 52 people.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_A-56\" title=\"Azerbaijan Airlines Flight A-56\">Azerbaijan Airlines Flight A-56</a> crashes near <a href=\"https://wikipedia.org/wiki/Nakhchivan_International_Airport\" title=\"Nakhchivan International Airport\">Nakhchivan International Airport</a> in <a href=\"https://wikipedia.org/wiki/Nakhchivan_Autonomous_Republic\" title=\"Nakhchivan Autonomous Republic\">Nakhchivan</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, killing 52 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_A-56\" title=\"Azerbaijan Airlines Flight A-56\">Azerbaijan Airlines Flight A-56</a> crashes near <a href=\"https://wikipedia.org/wiki/Nakhchivan_International_Airport\" title=\"Nakhchivan International Airport\">Nakhchivan International Airport</a> in <a href=\"https://wikipedia.org/wiki/Nakhchivan_Autonomous_Republic\" title=\"Nakhchivan Autonomous Republic\">Nakhchivan</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, killing 52 people.", "links": [{"title": "Azerbaijan Airlines Flight A-56", "link": "https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_A-56"}, {"title": "Nakhchivan International Airport", "link": "https://wikipedia.org/wiki/Nakhchivan_International_Airport"}, {"title": "Nakhchivan Autonomous Republic", "link": "https://wikipedia.org/wiki/Nakhchivan_Autonomous_Republic"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}]}, {"year": "2001", "text": "Space Shuttle Endeavour launches on STS-108, carrying the Expedition 4 crew to the International Space Station.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-108\" title=\"STS-108\">STS-108</a>, carrying the <a href=\"https://wikipedia.org/wiki/Expedition_4\" title=\"Expedition 4\">Expedition 4</a> crew to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-108\" title=\"STS-108\">STS-108</a>, carrying the <a href=\"https://wikipedia.org/wiki/Expedition_4\" title=\"Expedition 4\">Expedition 4</a> crew to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-108", "link": "https://wikipedia.org/wiki/STS-108"}, {"title": "Expedition 4", "link": "https://wikipedia.org/wiki/Expedition_4"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2005", "text": "The Civil Partnership Act comes into effect in the United Kingdom, and the first civil partnership is registered there.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Civil_Partnership_Act_2004\" title=\"Civil Partnership Act 2004\">Civil Partnership Act</a> comes into effect in the United Kingdom, and the first <a href=\"https://wikipedia.org/wiki/Civil_partnership_in_the_United_Kingdom\" title=\"Civil partnership in the United Kingdom\">civil partnership</a> is registered there.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Civil_Partnership_Act_2004\" title=\"Civil Partnership Act 2004\">Civil Partnership Act</a> comes into effect in the United Kingdom, and the first <a href=\"https://wikipedia.org/wiki/Civil_partnership_in_the_United_Kingdom\" title=\"Civil partnership in the United Kingdom\">civil partnership</a> is registered there.", "links": [{"title": "Civil Partnership Act 2004", "link": "https://wikipedia.org/wiki/Civil_Partnership_Act_2004"}, {"title": "Civil partnership in the United Kingdom", "link": "https://wikipedia.org/wiki/Civil_partnership_in_the_United_Kingdom"}]}, {"year": "2005", "text": "The 6.8 Mw  Lake Tanganyika earthquake shakes the eastern provinces of the Democratic Republic of the Congo with a maximum Mercalli intensity of X (Extreme), killing six people.", "html": "2005 - The 6.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2005_Lake_Tanganyika_earthquake\" title=\"2005 Lake Tanganyika earthquake\">Lake Tanganyika earthquake</a> shakes the eastern provinces of the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>), killing six people.", "no_year_html": "The 6.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2005_Lake_Tanganyika_earthquake\" title=\"2005 Lake Tanganyika earthquake\">Lake Tanganyika earthquake</a> shakes the eastern provinces of the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>), killing six people.", "links": [{"title": "2005 Lake Tanganyika earthquake", "link": "https://wikipedia.org/wiki/2005_Lake_Tanganyika_earthquake"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "2006", "text": "Commodore <PERSON> overthrows the government in Fiji.", "html": "2006 - Commodore <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/2006_Fijian_coup_d%27%C3%A9tat\" title=\"2006 Fijian coup d'état\">overthrows</a> the government in <a href=\"https://wikipedia.org/wiki/Fiji\" title=\"Fiji\">Fiji</a>.", "no_year_html": "Commodore <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/2006_Fijian_coup_d%27%C3%A9tat\" title=\"2006 Fijian coup d'état\">overthrows</a> the government in <a href=\"https://wikipedia.org/wiki/Fiji\" title=\"Fiji\">Fiji</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2006 Fijian coup d'état", "link": "https://wikipedia.org/wiki/2006_Fijian_coup_d%27%C3%A9tat"}, {"title": "Fiji", "link": "https://wikipedia.org/wiki/Fiji"}]}, {"year": "2007", "text": "Westroads Mall shooting: Nineteen-year-old <PERSON> kills nine people, including himself, with a WASR-10 at a Von Maur department store in Omaha, Nebraska.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Westroads_Mall_shooting\" title=\"Westroads Mall shooting\">Westroads Mall shooting</a>: Nineteen-year-old <PERSON> kills nine people, including himself, with a <a href=\"https://wikipedia.org/wiki/WASR-series_rifles\" title=\"WASR-series rifles\">WASR-10</a> at a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> department store in <a href=\"https://wikipedia.org/wiki/Omaha,_Nebraska\" title=\"Omaha, Nebraska\">Omaha, Nebraska</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Westroads_Mall_shooting\" title=\"Westroads Mall shooting\">Westroads Mall shooting</a>: Nineteen-year-old <PERSON> kills nine people, including himself, with a <a href=\"https://wikipedia.org/wiki/WASR-series_rifles\" title=\"WASR-series rifles\">WASR-10</a> at a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> department store in <a href=\"https://wikipedia.org/wiki/Omaha,_Nebraska\" title=\"Omaha, Nebraska\">Omaha, Nebraska</a>.", "links": [{"title": "Westroads Mall shooting", "link": "https://wikipedia.org/wiki/Westroads_Mall_shooting"}, {"title": "WASR-series rifles", "link": "https://wikipedia.org/wiki/WASR-series_rifles"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Omaha, Nebraska", "link": "https://wikipedia.org/wiki/Omaha,_Nebraska"}]}, {"year": "2013", "text": "Militants attack a Defense Ministry compound in Sana'a, Yemen, killing at least 56 people and injuring 200 others.", "html": "2013 - Militants <a href=\"https://wikipedia.org/wiki/2013_Yemeni_Ministry_of_Defense_attack\" title=\"2013 Yemeni Ministry of Defense attack\">attack</a> a Defense Ministry compound in <a href=\"https://wikipedia.org/wiki/Sana%27a\" class=\"mw-redirect\" title=\"Sana'a\">Sana'a</a>, <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a>, killing at least 56 people and injuring 200 others.", "no_year_html": "Militants <a href=\"https://wikipedia.org/wiki/2013_Yemeni_Ministry_of_Defense_attack\" title=\"2013 Yemeni Ministry of Defense attack\">attack</a> a Defense Ministry compound in <a href=\"https://wikipedia.org/wiki/Sana%27a\" class=\"mw-redirect\" title=\"Sana'a\">Sana'a</a>, <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a>, killing at least 56 people and injuring 200 others.", "links": [{"title": "2013 Yemeni Ministry of Defense attack", "link": "https://wikipedia.org/wiki/2013_Yemeni_Ministry_of_Defense_attack"}, {"title": "Sana'a", "link": "https://wikipedia.org/wiki/Sana%27a"}, {"title": "Yemen", "link": "https://wikipedia.org/wiki/Yemen"}]}, {"year": "2014", "text": "Exploration Flight Test-1, the first flight test of Orion, is launched.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Exploration_Flight_Test-1\" title=\"Exploration Flight Test-1\">Exploration Flight Test-1</a>, the first flight test of <a href=\"https://wikipedia.org/wiki/Orion_(spacecraft)\" title=\"Orion (spacecraft)\">Orion</a>, is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Exploration_Flight_Test-1\" title=\"Exploration Flight Test-1\">Exploration Flight Test-1</a>, the first flight test of <a href=\"https://wikipedia.org/wiki/Orion_(spacecraft)\" title=\"Orion (spacecraft)\">Orion</a>, is launched.", "links": [{"title": "Exploration Flight Test-1", "link": "https://wikipedia.org/wiki/Exploration_Flight_Test-1"}, {"title": "Orion (spacecraft)", "link": "https://wikipedia.org/wiki/Orion_(spacecraft)"}]}, {"year": "2017", "text": "The International Olympic Committee bans Russia from competing at the 2018 Winter Olympics for doping at the 2014 Winter Olympics.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a> bans <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a> from competing at the <a href=\"https://wikipedia.org/wiki/2018_Winter_Olympics\" title=\"2018 Winter Olympics\">2018 Winter Olympics</a> for doping at the <a href=\"https://wikipedia.org/wiki/2014_Winter_Olympics\" title=\"2014 Winter Olympics\">2014 Winter Olympics</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a> bans <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a> from competing at the <a href=\"https://wikipedia.org/wiki/2018_Winter_Olympics\" title=\"2018 Winter Olympics\">2018 Winter Olympics</a> for doping at the <a href=\"https://wikipedia.org/wiki/2014_Winter_Olympics\" title=\"2014 Winter Olympics\">2014 Winter Olympics</a>.", "links": [{"title": "International Olympic Committee", "link": "https://wikipedia.org/wiki/International_Olympic_Committee"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}, {"title": "2018 Winter Olympics", "link": "https://wikipedia.org/wiki/2018_Winter_Olympics"}, {"title": "2014 Winter Olympics", "link": "https://wikipedia.org/wiki/2014_Winter_Olympics"}]}], "Births": [{"year": "852", "text": "<PERSON>, Chinese emperor (d. 912)", "html": "852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (d. 912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (d. 912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1377", "text": "<PERSON><PERSON><PERSON> Emperor of China (d. 1402)", "html": "1377 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Jianwen Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (d. 1402)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Jianwen Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (d. 1402)", "links": [{"title": "<PERSON><PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1389", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish cardinal and statesman (d. 1455)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C5%9B<PERSON><PERSON>_(cardinal)\" title=\"<PERSON><PERSON><PERSON><PERSON> (cardinal)\"><PERSON><PERSON><PERSON><PERSON></a>, Polish cardinal and statesman (d. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C5%9B<PERSON><PERSON>_(cardinal)\" title=\"<PERSON><PERSON><PERSON><PERSON> (cardinal)\"><PERSON><PERSON><PERSON><PERSON></a>, Polish cardinal and statesman (d. 1455)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (cardinal)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ole%C5%9B<PERSON><PERSON>_(cardinal)"}]}, {"year": "1443", "text": "<PERSON> (d. 1513)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Julius_II\" title=\"Pope Julius II\">Pope <PERSON> II</a> (d. 1513)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Julius II\">Pope <PERSON> II</a> (d. 1513)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1470", "text": "<PERSON><PERSON><PERSON>, German lawyer and author (d. 1530)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and author (d. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and author (d. 1530)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1495", "text": "<PERSON>, Flemish philologist and lexicographer (d. 1542)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish philologist and lexicographer (d. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish philologist and lexicographer (d. 1542)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1537", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (d. 1597)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Yoshiaki\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>shiaki\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1597)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1539", "text": "<PERSON><PERSON>, Italian theologian and author (d. 1604)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian theologian and author (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian theologian and author (d. 1604)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1547", "text": "<PERSON><PERSON><PERSON>, Dutch historian and geographer (d. 1625)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>bb<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch historian and geographer (d. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch historian and geographer (d. 1625)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ubb<PERSON>_<PERSON>us"}]}, {"year": "1556", "text": "<PERSON>, Countess of Oxford, English countess (d. 1588)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Oxford\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Oxford\"><PERSON>, Countess of Oxford</a>, English countess (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Oxford\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Oxford\"><PERSON>, Countess of Oxford</a>, English countess (d. 1588)", "links": [{"title": "<PERSON>, Countess of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Oxford"}]}, {"year": "1596", "text": "<PERSON>, English composer (d. 1662)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1662)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, 1st Earl of Oxford and Earl <PERSON>, English lawyer and politician, Secretary of State for the Northern Department (d. 1724)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Oxford_and_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl of Oxford and Earl <PERSON>\"><PERSON>, 1st Earl of Oxford and <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (d. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Oxford_and_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl of Oxford and Earl <PERSON>\"><PERSON>, 1st Earl of Oxford and <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (d. 1724)", "links": [{"title": "<PERSON>, 1st Earl of Oxford and Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Oxford_and_<PERSON>_<PERSON>"}, {"title": "Secretary of State for the Northern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department"}]}, {"year": "1666", "text": "<PERSON>, Italian violinist and composer (d. 1741)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON>, Italian violinist and composer (d. 1762)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1697", "text": "<PERSON>, Italian organist and composer (d. 1771)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, American lawyer and politician, 8th President of the United States (d. 1862)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1784", "text": "<PERSON>, English illustrator and painter (d. 1862)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English illustrator and painter (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English illustrator and painter (d. 1862)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1803", "text": "<PERSON><PERSON><PERSON>, Russian poet and diplomat (d. 1873)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and diplomat (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and diplomat (d. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian poet and author (d. 1892)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Afanasy_Fet\" title=\"Afanas<PERSON> Fet\"><PERSON><PERSON><PERSON><PERSON></a>, Russian poet and author (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Afanasy_<PERSON>t\" title=\"Afanasy Fet\"><PERSON><PERSON><PERSON><PERSON></a>, Russian poet and author (d. 1892)", "links": [{"title": "A<PERSON><PERSON><PERSON> Fet", "link": "https://wikipedia.org/wiki/Afanasy_Fet"}]}, {"year": "1822", "text": "<PERSON>, American philosopher and academic, co-founded Radcliffe College (d. 1907)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic, co-founded <a href=\"https://wikipedia.org/wiki/Radcliffe_College\" title=\"Radcliffe College\">Radcliffe College</a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic, co-founded <a href=\"https://wikipedia.org/wiki/Radcliffe_College\" title=\"Radcliffe College\">Radcliffe College</a> (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Radcliffe College", "link": "https://wikipedia.org/wiki/Radcliffe_College"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON><PERSON>, French-Canadian lawyer and politician, 4th Premier of Quebec (d. 1908)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>bini%C3%A8re\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Canadian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>bini%C3%A8re\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Canadian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>i%C3%A8re"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1830", "text": "<PERSON>, English poet and author (d. 1894)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, American general (d. 1876)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, Irish-American businessman (d. 1900)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American businessman (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American businessman (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, German anthropologist, ethnohistorian, linguist, and academic (d. 1922)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist, ethnohistorian, linguist, and academic (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist, ethnohistorian, linguist, and academic (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, American zoologist, ornithologist, entomologist, and ethnographer (d. 1942)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist, ornithologist, entomologist, and ethnographer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist, ornithologist, entomologist, and ethnographer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, 1st <PERSON>, English admiral and politician, 2nd Governor-General of New Zealand (d. 1935)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl <PERSON></a>, English admiral and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English admiral and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1935)", "links": [{"title": "<PERSON>, 1st Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1861", "text": "<PERSON>, Russian-French painter and set designer (d. 1939)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and set designer (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and set designer (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, English entomologist (d. 1900)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English entomologist (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English entomologist (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, French mathematician and politician, 84th Prime Minister of France (d. 1933)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician and politician, 84th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician and politician, 84th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1866", "text": "<PERSON>, Irish polo player (d. 1944)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(polo_player)\" class=\"mw-redirect\" title=\"<PERSON> (polo player)\"><PERSON></a>, Irish polo player (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(polo_player)\" class=\"mw-redirect\" title=\"<PERSON> (polo player)\"><PERSON></a>, Irish polo player (d. 1944)", "links": [{"title": "<PERSON> (polo player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(polo_player)"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, Romanian poet and author (d. 1896)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet and author (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet and author (d. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, Finnish author and academic (d. 1925)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author and academic (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author and academic (d. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, Polish field marshal and politician, 15th Prime Minister of Poland (d. 1935)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish field marshal and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of Poland</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>%C5%82sudski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish field marshal and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of Poland</a> (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski"}, {"title": "List of Prime Ministers of Poland", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland"}]}, {"year": "1868", "text": "<PERSON>, German physicist and academic (d. 1951)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American author and poet (d. 1937)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Czech composer and educator (d. 1949)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/V%C3%ADt%C4%9B<PERSON><PERSON>_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech composer and educator (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%ADt%C4%9B<PERSON><PERSON>_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech composer and educator (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADt%C4%9Bzslav_Nov%C3%A1k"}]}, {"year": "1872", "text": "<PERSON>, American chess player (d. 1906)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Canadian general (d. 1933)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American pilot and businessman, founded the Cessna Aircraft Corporation (d. 1954)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Cessna\"><PERSON></a>, American pilot and businessman, founded the <a href=\"https://wikipedia.org/wiki/Cessna\" title=\"Cessna\">Cessna Aircraft Corporation</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Cessna\"><PERSON></a>, American pilot and businessman, founded the <a href=\"https://wikipedia.org/wiki/Cessna\" title=\"Cessna\">Cessna Aircraft Corporation</a> (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>na"}, {"title": "Cessna", "link": "https://wikipedia.org/wiki/Cessna"}]}, {"year": "1881", "text": "<PERSON>, French actor and director (d. 1922)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Crest%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Crest%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Crest%C3%A9"}]}, {"year": "1886", "text": "<PERSON>, American journalist and author (d. 1968)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rose Wilder Lane\"><PERSON></a>, American journalist and author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rose Wilder Lane\"><PERSON></a>, American journalist and author (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Dutch historian, academic, and politician, Minister of Finance of the Netherlands (d. 1968)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian, academic, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Netherlands)\" title=\"Ministry of Finance (Netherlands)\">Minister of Finance of the Netherlands</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian, academic, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Netherlands)\" title=\"Ministry of Finance (Netherlands)\">Minister of Finance of the Netherlands</a> (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Finance (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Netherlands)"}]}, {"year": "1886", "text": "<PERSON>, Soviet politician (d. 1937)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English painter, illustrator, and academic (d. 1957)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter, illustrator, and academic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter, illustrator, and academic (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Austrian-American director, producer, and screenwriter (d. 1976)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American director, producer, and screenwriter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American director, producer, and screenwriter (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Estonian chemist and academic (d. 1951)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian chemist and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian chemist and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, South African lawyer and politician, 1st State President of South Africa (d. 1982)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}]}, {"year": "1895", "text": "<PERSON><PERSON>, American mathematician and academic (d. 1969)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and academic (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and academic (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American historian, author, and educator (d. 1995)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and educator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and educator (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Czech-American biochemist and pharmacologist, Nobel Prize laureate (d. 1984)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON>, American director, producer, and screenwriter (d. 1977)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American director, producer, and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American director, producer, and screenwriter (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, German-Israeli philosopher and historian (d. 1982)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>holem\" title=\"G<PERSON><PERSON> Scholem\"><PERSON><PERSON><PERSON></a>, German-Israeli philosopher and historian (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>holem\" title=\"G<PERSON><PERSON> Scholem\"><PERSON><PERSON><PERSON></a>, German-Israeli philosopher and historian (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>holem"}]}, {"year": "1898", "text": "<PERSON>, Indian-Pakistani poet and translator (d. 1982)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Pakistani poet and translator (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Pakistani poet and translator (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American soprano and actress (d. 1947)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English footballer (d. 1972)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American animator, director, producer, and screenwriter, co-founded The Walt Disney Company (d. 1966)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a>, American animator, director, producer, and screenwriter, co-founded <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a>, American animator, director, producer, and screenwriter, co-founded <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Walt Disney Company", "link": "https://wikipedia.org/wiki/The_Walt_Disney_Company"}]}, {"year": "1901", "text": "<PERSON>, American psychiatrist and author (d. 1980)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and author (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and author (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (d. 1976)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Hungarian-English director, producer, and screenwriter (d. 1988)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>eric_<PERSON>burger\" title=\"Emeric Pressburger\"><PERSON><PERSON></a>, Hungarian-English director, producer, and screenwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>burger\" title=\"Emeric Pressburger\"><PERSON><PERSON></a>, Hungarian-English director, producer, and screenwriter (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emeric_Pressburger"}]}, {"year": "1902", "text": "<PERSON><PERSON>, American educator, general, and politician, 103rd Governor of South Carolina (d. 2003)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thur<PERSON>\" title=\"<PERSON>rom Thurmond\"><PERSON><PERSON></a>, American educator, general, and politician, 103rd <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hur<PERSON>\" title=\"<PERSON><PERSON> Thurmond\"><PERSON><PERSON></a>, American educator, general, and politician, 103rd <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thurmond"}, {"title": "Governor of South Carolina", "link": "https://wikipedia.org/wiki/Governor_of_South_Carolina"}]}, {"year": "1903", "text": "<PERSON>, Dutch-German actor and singer (d. 2011)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-German actor and singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-German actor and singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON> <PERSON><PERSON>, English-Italian physicist and academic, Nobel Prize laureate (d. 1969)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-Italian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-Italian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1969)", "links": [{"title": "C<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1905", "text": "<PERSON>, Guatemalan Army colonel and briefly Guatemalan head of state (d. 1949)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan Army colonel and briefly Guatemalan head of state (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan Army colonel and briefly Guatemalan head of state (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Austrian-American actor, director, and producer (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actor, director, and producer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actor, director, and producer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Chinese general and politician, 2nd Vice Premier of the People's Republic of China (d. 1971)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lin_<PERSON>o"}, {"title": "Vice Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "1907", "text": "<PERSON>, Italian-French physicist and academic (d. 1993)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French physicist and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French physicist and academic (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American director and screenwriter (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish pianist and composer (d. 2000)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist and composer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist and composer (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American travel writer (d. 1990)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American travel writer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American travel writer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American singer-songwriter and harmonica player (d. 1965)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Boy_Williamson_II\" title=\"Sonny Boy Williamson II\"><PERSON></a>, American singer-songwriter and harmonica player (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Boy_<PERSON>_II\" title=\"Sonny Boy Williamson II\"><PERSON></a>, American singer-songwriter and harmonica player (d. 1965)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_II"}]}, {"year": "1913", "text": "<PERSON>, Cuban soprano and actress (d. 2013)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban soprano and actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban soprano and actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American golfer (d. 1990)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, German lieutenant and author (d. 1989)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and author (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Polish-American virologist and immunologist, created the world's first effective live polio vaccine (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American virologist and immunologist, created the world's first effective live <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American virologist and immunologist, created the world's first effective live <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Polio vaccine", "link": "https://wikipedia.org/wiki/Polio_vaccine"}]}, {"year": "1916", "text": "<PERSON>, American basketball player and coach (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English racing driver (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Baron <PERSON>, English historian and politician (d. 2020)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Baron_<PERSON><PERSON>\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, English historian and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, English historian and politician (d. 2020)", "links": [{"title": "<PERSON><PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American actor and producer (d. 1997)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American philanthropist (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American songwriter and pianist (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American songwriter and pianist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American songwriter and pianist (d. 2015)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "1924", "text": "<PERSON>, South African banker and politician (d. 1978)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African banker and politician (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African banker and politician (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Nicaraguan politician, 73rd President of Nicaragua (d. 1980)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Anastasio_<PERSON>_<PERSON>\" title=\"Anastasio <PERSON>\">Ana<PERSON><PERSON></a>, Nicaraguan politician, 73rd <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anastasio_<PERSON>_<PERSON>\" title=\"Anastasio <PERSON>\">Anasta<PERSON></a>, Nicaraguan politician, 73rd <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (d. 1980)", "links": [{"title": "Anastasio So<PERSON>", "link": "https://wikipedia.org/wiki/Anastasio_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, first female Nigerian professor and university dean", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, first female Nigerian professor and university dean", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, first female Nigerian professor and university dean", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, King of Thailand (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Bhumibol_Adulyadej\" title=\"Bhumibol Adulyadej\">Bhumib<PERSON> Adulyadej</a>, King of Thailand (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bhumibol_Adulyadej\" title=\"Bhumibol Adulyadej\">Bhumibol Adulyadej</a>, King of Thailand (d. 2016)", "links": [{"title": "Bhumibol Adulyadej", "link": "https://wikipedia.org/wiki/Bhumibol_Adulyadej"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Sri Lankan musician and composer (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/W.<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"W.<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan musician and composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W.<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"W.<PERSON><PERSON> Amaradeva\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan musician and composer (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W.<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Estonian physicist, philosopher, and author (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B5iv\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian physicist, philosopher, and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B5iv\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian physicist, philosopher, and author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Madis_K%C3%B5iv"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Chinese-American geographer (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-American geographer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-American geographer (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Czech footballer and manager (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Ladislav_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ladislav_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ladislav_Nov%C3%A1k"}]}, {"year": "1932", "text": "<PERSON><PERSON>, <PERSON>, British politician", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, British politician", "links": [{"title": "<PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1932", "text": "<PERSON>, American race car driver (d. 1989)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Indian actress (d. 2006)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Indian_actress)\" title=\"<PERSON><PERSON> (Indian actress)\"><PERSON><PERSON></a>, Indian actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Indian_actress)\" title=\"<PERSON><PERSON> (Indian actress)\"><PERSON><PERSON></a>, Indian actress (d. 2006)", "links": [{"title": "<PERSON><PERSON> (Indian actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Indian_actress)"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter, pianist, and actor (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richard\"><PERSON></a>, American singer-songwriter, pianist, and actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richard\"><PERSON></a>, American singer-songwriter, pianist, and actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian race walker (d. 1999)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Gennadiy_Agapov\" title=\"Gennadiy Agapov\"><PERSON><PERSON><PERSON><PERSON></a>, Russian race walker (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gennadiy_Agapov\" title=\"Gennadiy Agapov\"><PERSON><PERSON><PERSON><PERSON></a>, Russian race walker (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gennadiy_Agapov"}]}, {"year": "1933", "text": "<PERSON>, Australian politician, 36th Premier of Tasmania (d. 1997)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1934", "text": "<PERSON>, American novelist and screenwriter (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American novelist, humorist, and journalist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, humorist, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, humorist, and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Ukrainian-Russian weightlifter and politician (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian weightlifter and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian weightlifter and politician (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American journalist, author, and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer-songwriter and guitarist (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. J<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. J<PERSON> Cale\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian cricket umpire", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricket umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricket umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Russian footballer and manager", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Swedish author, director, and screenwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter and producer (d. 2012)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer (d. 2012)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1942", "text": "<PERSON>, Canadian ice hockey coach (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey coach (d. 2017)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1943", "text": "<PERSON>, Norwegian-French judge and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-French judge and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-French judge and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, South Korean cardinal", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-jung\" title=\"<PERSON>jung\"><PERSON>jung</a>, South Korean cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-jung\" title=\"<PERSON>jung\"><PERSON>jung</a>, South Korean cardinal", "links": [{"title": "<PERSON>jung", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>-jung"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Dutch actor, director, and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>b%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>b%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch actor, director, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Krabb%C3%A9"}]}, {"year": "1945", "text": "<PERSON>, Canadian cartoonist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Iranian-Israeli educator and politician, 8th President of Israel", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-Israeli educator and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-Israeli educator and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "1946", "text": "<PERSON>, Spanish tenor and actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>s\" title=\"<PERSON>\"><PERSON></a>, Spanish tenor and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>s\" title=\"<PERSON>\"><PERSON></a>, Spanish tenor and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian pop singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Canadian pop singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Canadian pop singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, South African racing driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Filipino triathlete (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(triathlete)\" title=\"<PERSON> (triathlete)\"><PERSON></a>, Filipino triathlete (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(triathlete)\" title=\"<PERSON> (triathlete)\"><PERSON></a>, Filipino triathlete (d. 2022)", "links": [{"title": "<PERSON> (triathlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(triathlete)"}]}, {"year": "1947", "text": "<PERSON>, Jamaican lawyer and politician, 8th Prime Minister of Jamaica", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Jamaica\" title=\"Prime Minister of Jamaica\">Prime Minister of Jamaica</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Jamaica\" title=\"Prime Minister of Jamaica\">Prime Minister of Jamaica</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Jamaica", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Jamaica"}]}, {"year": "1947", "text": "<PERSON>, Irish activist and politician (d. 2009)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish activist and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish activist and politician (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian cosmonaut and military leader", "html": "1947 - <a href=\"https://wikipedia.org/wiki/J%C3%BCgderdemidiin_G%C3%BCrragchaa\" title=\"Jügderdemidiin Gürragchaa\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian cosmonaut and military leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BCgderdemidiin_G%C3%BCrragchaa\" title=\"Jügderdemidiin Gürragchaa\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian cosmonaut and military leader", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCgderdemidiin_G%C3%BCrragchaa"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON>, American football player and radio host", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Welsh blues-rock singer-songwriter, guitarist, and producer (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh blues-rock singer-songwriter, guitarist, and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh blues-rock singer-songwriter, guitarist, and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Welsh journalist and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian television host and actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English composer and conductor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and conductor", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1949", "text": "<PERSON>, English civil servant and diplomat, British Ambassador to the United States", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ambassadors of the United Kingdom to the United States", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States"}]}, {"year": "1951", "text": "<PERSON>, American actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Morgan Brittany\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Morgan Brittany\"><PERSON></a>, American actress", "links": [{"title": "Morgan <PERSON>", "link": "https://wikipedia.org/wiki/Morgan_Brittany"}]}, {"year": "1951", "text": "<PERSON>, Canadian journalist and author (d. 2015)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Link_Byfield\" title=\"Link Byfield\"><PERSON></a>, Canadian journalist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Link_Byfield\" title=\"Link Byfield\"><PERSON></a>, Canadian journalist and author (d. 2015)", "links": [{"title": "Link Byfield", "link": "https://wikipedia.org/wiki/Link_Byfield"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian painter and illustrator", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian painter and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, South African-Namibian journalist, publisher, and activist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Namibian journalist, publisher, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Namibian journalist, publisher, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1954", "text": "<PERSON><PERSON>, English author and playwright", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Japanese singer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Finnish hammer thrower (d. 2003)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish hammer thrower (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish hammer thrower (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German footballer and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Puerto Rican basketball player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, French-English author, poet, and playwright", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish virtuoso pianist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish virtuoso pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish virtuoso pianist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Chilean model, actress, and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean model, actress, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean model, actress, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>do%C3%B1a"}]}, {"year": "1957", "text": "<PERSON>, American football player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Monk\" title=\"<PERSON> Monk\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Monk\" title=\"Art Monk\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Monk"}]}, {"year": "1958", "text": "<PERSON><PERSON> Kid, English wrestler (d. 2018)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Dynamite_Kid\" title=\"Dynamite Kid\">Dynamite Kid</a>, English wrestler (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dynamite_Kid\" title=\"Dynamite Kid\">Dynamite Kid</a>, English wrestler (d. 2018)", "links": [{"title": "Dynamite Kid", "link": "https://wikipedia.org/wiki/Dynamite_Kid"}]}, {"year": "1959", "text": "<PERSON>, English footballer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian businessman", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian businessman", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Dutch footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fr<PERSON> Ad<PERSON>ar\"><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr<PERSON>_Ad<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian-American composer and educator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>lijo<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian-American composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>li<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian-American composer and educator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>jov"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and producer (d. 2024)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer (d. 2024)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1960", "text": "<PERSON>, English businessman and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Labour_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Labour politician)\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Labour_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Labour politician)\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON> (Labour politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Labour_politician)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, German mountaineer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mountaineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, British journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Laura_<PERSON>\" title=\"Laura <PERSON>\"><PERSON></a>, British journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Laura_<PERSON>\" title=\"Laura <PERSON>\"><PERSON></a>, British journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Laura_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Argentinian tenor, conductor, and director", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Cura\" title=\"<PERSON>\"><PERSON></a>, Argentinian tenor, conductor, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Cura\" title=\"<PERSON>\"><PERSON></a>, Argentinian tenor, conductor, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Cura"}]}, {"year": "1962", "text": "<PERSON>, American swimmer and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Nivek_Ogre\" title=\"Nivek Ogre\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nivek_Ogre\" title=\"Nivek Ogre\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "Nivek Ogre", "link": "https://wikipedia.org/wiki/Nivek_Ogre"}]}, {"year": "1962", "text": "<PERSON>, Dutch footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American television and radio host", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Doctor_Dr%C3%A9\" title=\"Doctor <PERSON><PERSON>\">Doctor <PERSON></a>, American television and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Doctor_Dr%C3%A9\" title=\"Doctor <PERSON><PERSON>\">Doctor <PERSON><PERSON></a>, American television and radio host", "links": [{"title": "Doctor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Doctor_Dr%C3%A9"}]}, {"year": "1963", "text": "<PERSON>, American actress and playwright (d. 2002)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Argentinian lawyer (d. 2015)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian cyclist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Indian fashion designer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Man<PERSON>\"><PERSON><PERSON></a>, Indian fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Man<PERSON>\"><PERSON><PERSON></a>, Indian fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Man<PERSON>_<PERSON>hotra"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Jamaican rapper (d. 2014)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Jamaican rapper (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Jamaican rapper (d. 2014)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Russian race walker", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian race walker", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valeriy_Spitsyn"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American comedian, actress, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American model and actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1968", "text": "<PERSON>, American novelist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mill<PERSON>\"><PERSON></a>, American novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Millet\"><PERSON></a>, American novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Millet"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Nigerian sprinter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Falilat_Ogunkoya\" title=\"Falilat Ogunkoya\"><PERSON><PERSON><PERSON></a>, Nigerian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falilat_Ogunkoya\" title=\"Falilat Ogunkoya\"><PERSON><PERSON><PERSON></a>, Nigerian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Falilat_Ogunkoya"}]}, {"year": "1969", "text": "<PERSON>, American actor, director, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> J. Freeman\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON> J<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, British Pakistani banker and politician, former Chancellor of the Exchequer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Sa<PERSON>_<PERSON>avid\" title=\"Sajid Javid\"><PERSON><PERSON></a>, British Pakistani banker and politician, former <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d\" title=\"Sa<PERSON> Javid\"><PERSON><PERSON></a>, British Pakistani banker and politician, former <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1969", "text": "<PERSON>, English swimmer and lawyer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Mexican footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Ram%C3%<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Ram%C3%<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Ram%C3%<PERSON><PERSON>_(footballer)"}]}, {"year": "1969", "text": "<PERSON>, English actress, comedian, and writer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, comedian, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, comedian, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27le\" title=\"<PERSON><PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27le\" title=\"<PERSON><PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>%27le"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, German businessman and politician, German Federal Minister of Defence", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German businessman and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)\" title=\"Federal Ministry of Defence (Germany)\">German Federal Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German businessman and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)\" title=\"Federal Ministry of Defence (Germany)\">German Federal Minister of Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Federal Ministry of Defence (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American-English triple jumper", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-English triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-English triple jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Swedish golfer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Floyd\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Floyd\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American hurdler and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Argo_Arbeiter\" title=\"Argo Arbeiter\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_Arbeiter\" title=\"Argo Arbeiter\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_Arbeiter"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Italian singer-songwriter, producer, and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Locon<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>con<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer-songwriter, producer, and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lang<PERSON>_Loconte"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Czech physicist and academic", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Lubo%C5%A1_Motl\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lubo%C5%A1_Motl\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech physicist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lubo%C5%A1_Motl"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Indian journalist and author", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American sprinter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1975", "text": "<PERSON>, English snooker player and radio host", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, English snooker player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, English snooker player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan"}]}, {"year": "1975", "text": "<PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xavier_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese doctor and astronaut", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Norishige_<PERSON>\" title=\"Norishige Kanai\"><PERSON><PERSON><PERSON></a>, Japanese doctor and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norishige_Ka<PERSON>\" title=\"Norishige Kanai\"><PERSON><PERSON><PERSON></a>, Japanese doctor and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norishige_Kanai"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese actress and model", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American swimmer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Dutch footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American video game designer and author", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jo<PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jo<PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Uruguayan footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Matteo Ferrari\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Matteo Ferrari\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Northern Irish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Par%C3%A9"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Mexican actor (d. 2024)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actor (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n_<PERSON>to"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Swedish ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lindstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lindstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joakim_Lindstr%C3%B6m"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Lauren_London\" title=\"Lauren London\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lauren_London\" title=\"Lauren London\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lauren_London"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor, drummer, and race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, drummer, and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, drummer, and race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Blount\" title=\"Le<PERSON><PERSON><PERSON><PERSON> Blount\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Blount\" title=\"LeG<PERSON><PERSON><PERSON> Blount\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>nt"}]}, {"year": "1986", "text": "<PERSON>, Canadian Indycar racing driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian Indycar racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian Indycar racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, South Korean singer-songwriter and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ri\" title=\"Kwon Yu-ri\"><PERSON><PERSON></a>, South Korean singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Yu-ri\" title=\"Kwon Yu-ri\"><PERSON><PERSON></a>, South Korean singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>i", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ri"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Monte<PERSON> Ball\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Monte<PERSON> Ball\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian-American ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian field hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Argentine footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Slovak footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ondre<PERSON>_<PERSON>da\" title=\"Ondre<PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ondre<PERSON>_<PERSON>\" title=\"Ondre<PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ondrej_<PERSON>da"}]}, {"year": "1994", "text": "<PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Semi_Ojeleye\" title=\"Semi Ojeleye\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Semi_Ojeleye\" title=\"Semi Ojeleye\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> O<PERSON>leye", "link": "https://wikipedia.org/wiki/Semi_Oje<PERSON>e"}]}, {"year": "1995", "text": "<PERSON>, New Zealand rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, French footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Martial\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Canadian figure skater", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Osmond\"><PERSON><PERSON><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>smo<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995   -<PERSON>, American chess International Master, streamer and YouTuber[53]", "text": null, "html": "1995   -<PERSON>, American chess International Master, streamer and YouTuber[53] - 1995 -<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess <a href=\"https://wikipedia.org/wiki/International_master\" class=\"mw-redirect\" title=\"International master\">International Master</a>, streamer and YouTuber", "no_year_html": "1995 -<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess <a href=\"https://wikipedia.org/wiki/International_master\" class=\"mw-redirect\" title=\"International master\">International Master</a>, streamer and YouTuber", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International master", "link": "https://wikipedia.org/wiki/International_master"}]}, {"year": "1995", "text": "<PERSON>, Norwegian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8rloth\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B8rloth\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_S%C3%B8rloth"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter and musician", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Maddie Poppe\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Maddie Poppe\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gray\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gray\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, French footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "63 BC", "text": "<PERSON><PERSON>, Roman politician (b. 114 BC)", "html": "63 BC - 63 BC - <a href=\"https://wikipedia.org/wiki/Pub<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pub<PERSON>\"><PERSON><PERSON></a>, Roman politician (b. 114 BC)", "no_year_html": "63 BC - <a href=\"https://wikipedia.org/wiki/Pub<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Publius <PERSON>\"><PERSON><PERSON></a>, Roman politician (b. 114 BC)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "334", "text": "<PERSON>, emperor of Cheng Han (b. 288)", "html": "334 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ban\" title=\"Li Ban\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Cheng <PERSON>\"><PERSON></a> (b. 288)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_Ban\" title=\"Li Ban\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (b. 288)", "links": [{"title": "Li Ban", "link": "https://wikipedia.org/wiki/Li_Ban"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "902", "text": "<PERSON><PERSON><PERSON><PERSON>, queen consort and wife of <PERSON> the <PERSON>, King of Wessex", "html": "902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Queen_consort\" title=\"Queen consort\">queen consort</a> and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, King of <a href=\"https://wikipedia.org/wiki/Wessex\" title=\"Wessex\">Wessex</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Queen_consort\" title=\"Queen consort\">queen consort</a> and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, King of <a href=\"https://wikipedia.org/wiki/Wessex\" title=\"Wessex\">Wessex</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>with"}, {"title": "Queen consort", "link": "https://wikipedia.org/wiki/Queen_consort"}, {"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "Wessex", "link": "https://wikipedia.org/wiki/Wessex"}]}, {"year": "1082", "text": "<PERSON>, Count of Barcelona (b. 1053)", "html": "1082 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Barcelona\" title=\"<PERSON>, Count of Barcelona\"><PERSON>, Count of Barcelona</a> (b. 1053)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Barcelona\" title=\"<PERSON>, Count of Barcelona\"><PERSON>, Count of Barcelona</a> (b. 1053)", "links": [{"title": "<PERSON>, Count of Barcelona", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_of_Barcelona"}]}, {"year": "1212", "text": "<PERSON>, bishop and lord of Utrecht", "html": "1212 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop and lord of Utrecht", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop and lord of Utrecht", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1244", "text": "<PERSON>, Countess of Flanders and Hainault (b. 1199 or 1200)", "html": "1244 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Flanders\" title=\"<PERSON>, Countess of Flanders\"><PERSON>, Countess of Flanders</a> and <a href=\"https://wikipedia.org/wiki/County_of_Hainaut\" title=\"County of Hainaut\">Hainault</a> (b. 1199 or 1200)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Flanders\" title=\"<PERSON>, Countess of Flanders\"><PERSON>, Countess of Flanders</a> and <a href=\"https://wikipedia.org/wiki/County_of_Hainaut\" title=\"County of Hainaut\">Hainault</a> (b. 1199 or 1200)", "links": [{"title": "<PERSON>, Countess of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_of_Flanders"}, {"title": "County of Hainaut", "link": "https://wikipedia.org/wiki/County_of_Hainaut"}]}, {"year": "1355", "text": "<PERSON>, Duke of Brabant (b. 1300)", "html": "1355 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (b. 1300)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (b. 1300)", "links": [{"title": "<PERSON>, Duke of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1560", "text": "<PERSON> of France (b. 1544)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1544)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1570", "text": "<PERSON>, Danish politician (b. 1494)", "html": "1570 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician (b. 1494)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1624", "text": "<PERSON><PERSON>, Swiss botanist and physician (b. 1560)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bauhin\"><PERSON><PERSON></a>, Swiss botanist and physician (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bauhin\"><PERSON><PERSON></a>, Swiss botanist and physician (b. 1560)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1654", "text": "<PERSON>, French author and poet (b. 1611)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1663", "text": "<PERSON><PERSON><PERSON>, Italian organist and composer (b. 1582)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian organist and composer (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian organist and composer (b. 1582)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, <PERSON><PERSON> <PERSON> Vérendrye, Canadian commander and explorer (b. 1685)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Varennes,_sieur_de_La_V%C3%A9rendrye\" title=\"<PERSON>arennes, sieur de <PERSON> Vérendrye\"><PERSON>, sieur de <PERSON> Vé<PERSON>drye</a>, Canadian commander and explorer (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Varennes,_sieur_de_La_V%C3%A9rendrye\" title=\"<PERSON>arennes, sieur de La Vérendrye\"><PERSON>nnes, sieur de <PERSON> Vérendrye</a>, Canadian commander and explorer (b. 1685)", "links": [{"title": "<PERSON>, <PERSON><PERSON> de La Vérendrye", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_<PERSON>eur_de_La_V%C3%A9rendrye"}]}, {"year": "1758", "text": "<PERSON>, German violinist and composer (b. 1688)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, Scottish mathematician and surveyor (b. 1692)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Scottish mathematician and surveyor (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Scottish mathematician and surveyor (b. 1692)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "1784", "text": "<PERSON><PERSON>, Senegal-born slave, later American poet (b. 1753)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegal-born slave, later American poet (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegal-born slave, later American poet (b. 1753)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ley"}]}, {"year": "1791", "text": "<PERSON>, Austrian composer and musician (b. 1756)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Amadeus Mozart\"><PERSON></a>, Austrian composer and musician (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Amade<PERSON> Mozart\"><PERSON></a>, Austrian composer and musician (b. 1756)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Canadian-Australian gold miner (b. 1829)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Australian gold miner (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Australian gold miner (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>-Stolberg, German poet and lawyer (b. 1750)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Stolberg-Stolberg\" title=\"<PERSON> zu Stolberg-Stolberg\"><PERSON> zu Stolberg-Stolberg</a>, German poet and lawyer (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Stolberg-Stolberg\" title=\"<PERSON> zu Stolberg-Stolberg\"><PERSON> zu Stolberg-Stolberg</a>, German poet and lawyer (b. 1750)", "links": [{"title": "<PERSON> Stolberg-Stolberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Stolberg-Stolberg"}]}, {"year": "1870", "text": "<PERSON>, French novelist and playwright (b. 1802)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and playwright (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and playwright (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American poet and songwriter (b. 1804)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and songwriter (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and songwriter (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON> of Brazil (b. 1825)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Brazil\" title=\"Pedro II of Brazil\"><PERSON> II of Brazil</a> (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_II_of_Brazil\" title=\"Pedro II of Brazil\">Pedro II of Brazil</a> (b. 1825)", "links": [{"title": "Pedro II of Brazil", "link": "https://wikipedia.org/wiki/Pedro_II_of_Brazil"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, South African commander, lawyer, and politician, 6th President of the South African Republic (b. 1852)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African commander, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_South_African_Republic\" class=\"mw-redirect\" title=\"List of Presidents of the South African Republic\">President of the South African Republic</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African commander, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_South_African_Republic\" class=\"mw-redirect\" title=\"List of Presidents of the South African Republic\">President of the South African Republic</a> (b. 1852)", "links": [{"title": "Schalk Willem Burger", "link": "https://wikipedia.org/wiki/<PERSON>halk_Willem_Burger"}, {"title": "List of Presidents of the South African Republic", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_South_African_Republic"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish novelist, Nobel Prize laureate (b. 1867)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Reymont\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish novelist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Reymont\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish novelist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Reymont"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1926", "text": "<PERSON>, French painter (b. 1840)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, American poet (b. 1879)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Armenian physician and anarchist publisher (b. 1869)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian physician and anarchist publisher (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian physician and anarchist publisher (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Czech violinist and composer (b. 1880)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Hungarian-Pakistani painter (b. 1913)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>r-<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Pakistani painter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>r-<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Pakistani painter (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, English captain (b. 1883)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English captain (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English captain (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Belgian-French painter and educator (b. 1872)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French painter and educator (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French painter and educator (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON> <PERSON>, American baseball player and manager (b. 1887)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Shoe<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American baseball player and manager (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Shoe<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American baseball player and manager (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian painter, author, and academic (b. 1871)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Abanindranath_Tagore\" title=\"Abanindranath Tagore\"><PERSON><PERSON><PERSON><PERSON><PERSON> Tagore</a>, Indian painter, author, and academic (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abanindranath_Tagore\" title=\"Abanindranath Tagore\"><PERSON><PERSON><PERSON><PERSON><PERSON> Tagore</a>, Indian painter, author, and academic (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>th Tagore", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American admiral (b. 1901)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American pilot and businessman, founded the Glenn L. Martin Company (b. 1886)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Martin_Company\" title=\"Glenn L. Martin Company\">Glenn L. Martin Company</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Company\" title=\"Glenn L. Martin Company\">Glenn L. Martin Company</a> (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Glenn L. Martin Company", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Martin_Company"}]}, {"year": "1961", "text": "<PERSON>, German-American lawyer and businessman (b. 1878)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, German-American lawyer and businessman (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, German-American lawyer and businessman (b. 1878)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1963", "text": "<PERSON>, German composer and educator (b. 1905)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Indian-Pakistani lawyer and politician, 5th Prime Minister of Pakistan (b. 1892)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Sri Lankan educator and politician (b. 1892)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> V<PERSON>\"><PERSON><PERSON></a>, Sri Lankan educator and politician (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan educator and politician (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>am"}]}, {"year": "1965", "text": "<PERSON>, American physiologist, neuroscientist, and academic Nobel Prize laureate (b. 1874)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist, neuroscientist, and academic <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist, neuroscientist, and academic <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1968", "text": "<PERSON>, American actor (b. 1914)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German engineer and businessman, founded Dornier Flugzeugwerke (b. 1884)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Dornier_Flugzeugwerke\" title=\"Dornier Flugzeugwerke\">Dornier Flugzeugwerke</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Dornier_Flugzeugwerke\" title=\"Dornier Flugzeugwerke\">Dornier Flugzeugwerke</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Dornier Flugzeugwerke", "link": "https://wikipedia.org/wiki/Dornier_Flugzeugwerke"}]}, {"year": "1969", "text": "Princess <PERSON> of Battenberg (b. 1885)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Battenberg\" title=\"Princess <PERSON> of Battenberg\">Princess <PERSON> of Battenberg</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Battenberg\" title=\"Princess <PERSON> of Battenberg\">Princess <PERSON> of Battenberg</a> (b. 1885)", "links": [{"title": "Princess <PERSON> of Battenberg", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_Batten<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Scottish engineer, invented the radar (b. 1892)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer, invented the <a href=\"https://wikipedia.org/wiki/Radar\" title=\"Radar\">radar</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer, invented the <a href=\"https://wikipedia.org/wiki/Radar\" title=\"Radar\">radar</a> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Radar", "link": "https://wikipedia.org/wiki/Radar"}]}, {"year": "1975", "text": "<PERSON>, American historian and author (b. 1897)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American author and illustrator (b. 1894)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Russian marshal and politician, Minister of Defence for the Soviet Union (b. 1895)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian marshal and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)\" title=\"Minister of Defence (Soviet Union)\">Minister of Defence for the Soviet Union</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian marshal and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)\" title=\"Minister of Defence (Soviet Union)\">Minister of Defence for the Soviet Union</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Defence (Soviet Union)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)"}]}, {"year": "1979", "text": "<PERSON>, American actor, singer, and screenwriter (b. 1930)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and screenwriter (b. 1930)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1983", "text": "<PERSON>, American director, producer, and screenwriter (b. 1918)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American politician (b. 1894)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Welsh-Chinese sinologist and diplomat, 26th Governor of Hong Kong (b. 1924)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Chinese sinologist and diplomat, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Hong_Kong\" title=\"Governor of Hong Kong\">Governor of Hong Kong</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Chinese sinologist and diplomat, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Hong_Kong\" title=\"Governor of Hong Kong\">Governor of Hong Kong</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Hong Kong", "link": "https://wikipedia.org/wiki/Governor_of_Hong_Kong"}]}, {"year": "1989", "text": "<PERSON>, English conductor and director (b. 1921)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor and director (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor and director (b. 1921)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_(conductor)"}]}, {"year": "1990", "text": "<PERSON>, Filipino-American painter and sculptor (b. 1916)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American painter and sculptor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American painter and sculptor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American mass murderer (b. 1941)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Czech-American director, producer, and production designer (b. 1910)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American director, producer, and production designer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American director, producer, and production designer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON> <PERSON><PERSON>, American illustrator and publisher (b. 1918)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"L. B<PERSON> Cole\"><PERSON><PERSON> <PERSON><PERSON></a>, American illustrator and publisher (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"L. <PERSON><PERSON> Cole\"><PERSON><PERSON> <PERSON><PERSON></a>, American illustrator and publisher (b. 1918)", "links": [{"title": "L. B<PERSON> Cole", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English mountaineer, surgeon, and educator (b. 1918)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, English mountaineer, surgeon, and educator (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, English mountaineer, surgeon, and educator (b. 1918)", "links": [{"title": "<PERSON> (mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)"}]}, {"year": "1995", "text": "<PERSON>, Australian poet and playwright (b. 1920)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and playwright (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and playwright (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American scientist (b. 1922)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Romanian-German jazz pianist (b. 1940)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-German jazz pianist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-German jazz pianist (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Sr., American lawyer and politician (b. 1907)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American lawyer and politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American lawyer and politician (b. 1907)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Sr."}]}, {"year": "2001", "text": "<PERSON>, Italian-American physicist and academic (b. 1901)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Franco_Rasetti\" title=\"<PERSON>\"><PERSON></a>, Italian-American physicist and academic (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_Rasetti\" title=\"<PERSON>\"><PERSON></a>, Italian-American physicist and academic (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Rasetti"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, American sportscaster and producer (b. 1931)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sportscaster and producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sportscaster and producer (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Burmese general and politician, 4th President of Burma (b. 1911)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Ne_<PERSON>\" title=\"Ne Win\"><PERSON><PERSON> <PERSON></a>, Burmese general and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Burma\" class=\"mw-redirect\" title=\"President of Burma\">President of Burma</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ne_<PERSON>\" title=\"Ne Win\"><PERSON><PERSON> <PERSON></a>, Burmese general and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Burma\" class=\"mw-redirect\" title=\"President of Burma\">President of Burma</a> (b. 1911)", "links": [{"title": "<PERSON>e Win", "link": "https://wikipedia.org/wiki/Ne_Win"}, {"title": "President of Burma", "link": "https://wikipedia.org/wiki/President_of_Burma"}]}, {"year": "2005", "text": "<PERSON>, American lawyer and politician (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Ukrainian-Belarusian chess player and theoretician (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Belarusian chess player and theoretician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Belarusian chess player and theoretician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American composer and academic (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Greek-Cypriot businessman and philanthropist, co-founded Joannou & Paraskevaides (b. 1916)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Cypriot businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Joannou_%26_<PERSON>ides\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> &amp; <PERSON>ides\"><PERSON><PERSON><PERSON> &amp; <PERSON></a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Cypriot businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Joannou_%26_<PERSON>ides\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> &amp; <PERSON>ides\"><PERSON><PERSON><PERSON> &amp; <PERSON></a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Joannou & Paraskevaides", "link": "https://wikipedia.org/wiki/Joannou_%26_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, German composer and academic (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Karlheinz_Stockhausen\" title=\"Karlheinz Stockhausen\"><PERSON><PERSON><PERSON>hausen</a>, German composer and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karlheinz_Stockhausen\" title=\"Karlheinz Stockhausen\"><PERSON><PERSON><PERSON></a>, German composer and academic (b. 1928)", "links": [{"title": "Karlheinz Stockhausen", "link": "https://wikipedia.org/wiki/Karlheinz_Stockhausen"}]}, {"year": "2008", "text": "Patriarch <PERSON><PERSON> of Moscow (b. 1929)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON>_<PERSON>_of_Moscow\" title=\"Patriarch <PERSON><PERSON> of Moscow\">Patriarch <PERSON><PERSON> of Moscow</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON>_<PERSON>_of_Moscow\" title=\"Patriarch <PERSON><PERSON> of Moscow\">Patriarch <PERSON><PERSON> of Moscow</a> (b. 1929)", "links": [{"title": "Patriarch <PERSON><PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_II_of_Moscow"}]}, {"year": "2008", "text": "<PERSON>, American chemist and composer (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Dutch-American actress (b. 1924)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American actress (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actress and businesswoman (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Garland\"><PERSON></a>, American actress and businesswoman (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Beverly Garland\"><PERSON></a>, American actress and businesswoman (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Romanian singer-songwriter and pianist (b. 1957)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian singer-songwriter and pianist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian singer-songwriter and pianist (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American soldier and author (b. 1912)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American director, producer, and screenwriter (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American football player, sportscaster, and actor (b. 1938)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English racing driver (b. 1940)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Russian footballer and manager (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>na<PERSON>_<PERSON>go<PERSON>t\" title=\"Gennady Logo<PERSON>t\"><PERSON><PERSON><PERSON></a>, Russian footballer and manager (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>go<PERSON>t\" title=\"Gennady Lo<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and manager (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nady_Logofet"}]}, {"year": "2012", "text": "<PERSON>, American pianist and composer (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian philanthropist (b. 1909)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, Australian philanthropist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, Australian philanthropist (b. 1909)", "links": [{"title": "<PERSON> (philanthropist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)"}]}, {"year": "2012", "text": "<PERSON>, Brazilian architect, designed the United Nations Headquarters and Cathedral of Brasília (b. 1907)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian architect, designed the <a href=\"https://wikipedia.org/wiki/United_Nations_Headquarters\" class=\"mw-redirect\" title=\"United Nations Headquarters\">United Nations Headquarters</a> and <a href=\"https://wikipedia.org/wiki/Cathedral_of_Bras%C3%ADlia\" title=\"Cathedral of Brasília\">Cathedral of Brasília</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian architect, designed the <a href=\"https://wikipedia.org/wiki/United_Nations_Headquarters\" class=\"mw-redirect\" title=\"United Nations Headquarters\">United Nations Headquarters</a> and <a href=\"https://wikipedia.org/wiki/Cathedral_of_Bras%C3%ADlia\" title=\"Cathedral of Brasília\">Cathedral of Brasília</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Nations Headquarters", "link": "https://wikipedia.org/wiki/United_Nations_Headquarters"}, {"title": "Cathedral of Brasília", "link": "https://wikipedia.org/wiki/Cathedral_of_Bras%C3%ADlia"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON> of Antioch, Syrian patriarch (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Antioch\" title=\"<PERSON><PERSON><PERSON> IV of Antioch\"><PERSON><PERSON><PERSON> <PERSON> of Antioch</a>, Syrian patriarch (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Antioch\" title=\"<PERSON><PERSON><PERSON> IV of Antioch\"><PERSON><PERSON><PERSON> IV of Antioch</a>, Syrian patriarch (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON> of Antioch", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Antioch"}]}, {"year": "2013", "text": "<PERSON>, American architect and academic, founded Bassetti Architects (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Architects\" title=\"Bassetti Architects\">Bassetti Architects</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Architects\" title=\"Bassetti Architects\">Bassetti Architects</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Bassetti Architects", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Architects"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and diplomat, United States Ambassador to South Africa (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_South_Africa\" class=\"mw-redirect\" title=\"United States Ambassador to South Africa\">United States Ambassador to South Africa</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_South_Africa\" class=\"mw-redirect\" title=\"United States Ambassador to South Africa\">United States Ambassador to South Africa</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to South Africa", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_South_Africa"}]}, {"year": "2013", "text": "<PERSON>, South African lawyer and politician, 1st President of South Africa, Nobel Prize laureate (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}, {"title": "President of South Africa", "link": "https://wikipedia.org/wiki/President_of_South_Africa"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "2014", "text": "<PERSON>, American captain and pilot (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Queen of Belgium (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>_of_Belgium\" class=\"mw-redirect\" title=\"Queen <PERSON><PERSON><PERSON> of Belgium\"><PERSON><PERSON><PERSON></a>, Queen of Belgium (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>_of_Belgium\" class=\"mw-redirect\" title=\"Queen <PERSON><PERSON>ola of Belgium\"><PERSON><PERSON><PERSON></a>, Queen of Belgium (b. 1928)", "links": [{"title": "Queen <PERSON><PERSON><PERSON> of Belgium", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_of_Belgium"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish poet, translator, and historian (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Tal%C3%A2t_<PERSON><PERSON>_<PERSON>\" title=\"Ta<PERSON>â<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish poet, translator, and historian (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tal%C3%A2<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Talâ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish poet, translator, and historian (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tal%C3%A2<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>-<PERSON>, Irish hurdler and politician (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurdler and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurdler and politician (b. 1931)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Mexican historian and author (b. 1909)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican historian and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican historian and author (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American clergyman and radio host, founded VCY America (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clergyman and radio host, founded <a href=\"https://wikipedia.org/wiki/VCY_America\" title=\"VCY America\">VCY America</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clergyman and radio host, founded <a href=\"https://wikipedia.org/wiki/VCY_America\" title=\"VCY America\">VCY America</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_<PERSON>"}, {"title": "VCY America", "link": "https://wikipedia.org/wiki/VCY_America"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Hungarian-American soldier, Medal of Honor recipient (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2015", "text": "<PERSON>, American businessman and author, founded Williams Sonoma (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American businessman and author, founded <a href=\"https://wikipedia.org/wiki/Williams_Sonoma_(brand)\" class=\"mw-redirect\" title=\"Williams Sonoma (brand)\">Williams Sonoma</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American businessman and author, founded <a href=\"https://wikipedia.org/wiki/Williams_Sonoma_(brand)\" class=\"mw-redirect\" title=\"Williams Sonoma (brand)\">Williams Sonoma</a> (b. 1915)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}, {"title": "Williams Sonoma (brand)", "link": "https://wikipedia.org/wiki/Williams_Sonoma_(brand)"}]}, {"year": "2016", "text": "<PERSON><PERSON> (\"<PERSON> Syke\"), American rapper (b. 1968)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Him<PERSON>\"><PERSON><PERSON></a> (\"Big Syke\"), American rapper (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Him<PERSON>\"><PERSON><PERSON></a> (\"<PERSON> Syke\"), American rapper (b. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>es"}]}, {"year": "2017", "text": "<PERSON> of Romania, fifth and last king of Romania (b. 1921)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a>, fifth and last king of Romania (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a>, fifth and last king of Romania (b. 1921)", "links": [{"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Romania"}]}, {"year": "2017", "text": "<PERSON>, Canadian American pornographic actress (b. 1994)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Canadian American pornographic actress (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Canadian American pornographic actress (b. 1994)", "links": [{"title": "August Ames", "link": "https://wikipedia.org/wiki/August_Ames"}]}, {"year": "2019", "text": "<PERSON>, American actor (b. 1940)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1940)\" title=\"<PERSON> (actor, born 1940)\"><PERSON></a>, American actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1940)\" title=\"<PERSON> (actor, born 1940)\"><PERSON></a>, American actor (b. 1940)", "links": [{"title": "<PERSON> (actor, born 1940)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor,_born_1940)"}]}, {"year": "2020", "text": "<PERSON>, English professional golfer (b. 1931) ", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English professional golfer (b. 1931) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English professional golfer (b. 1931) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American politician (b. 1923)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, American actress and producer (b. 1951)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Ki<PERSON>ie_Alley\" title=\"Kirstie Alley\"><PERSON><PERSON><PERSON></a>, American actress and producer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ie_<PERSON>\" title=\"Kirstie Alley\"><PERSON><PERSON><PERSON></a>, American actress and producer (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American screenwriter and producer (b. 1922)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, French poet, writer, and mathematician (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet, writer, and mathematician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet, writer, and mathematician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}