{"date": "July 16", "url": "https://wikipedia.org/wiki/July_16", "data": {"Events": [{"year": "622", "text": "The Hijrah of <PERSON> begins, marking the beginning of the Islamic calendar.", "html": "622 - The <a href=\"https://wikipedia.org/wiki/Hijrah\" title=\"Hijrah\">Hijrah</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Islamic_calendar\" title=\"Islamic calendar\">Islamic calendar</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hijrah\" title=\"Hijrah\">Hijrah</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Islamic_calendar\" title=\"Islamic calendar\">Islamic calendar</a>.", "links": [{"title": "Hi<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hijrah"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Islamic calendar", "link": "https://wikipedia.org/wiki/Islamic_calendar"}]}, {"year": "997", "text": "Battle of Spercheios: Bulgarian forces of Tsar <PERSON> are defeated by a Byzantine army under general <PERSON><PERSON><PERSON><PERSON> at the Spercheios River in Greece.", "html": "997 - <a href=\"https://wikipedia.org/wiki/Battle_of_Spercheios\" title=\"Battle of Spercheios\">Battle of Spercheios</a>: Bulgarian forces of Tsar <a href=\"https://wikipedia.org/wiki/Samuel_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON></a> are defeated by a <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army under general <a href=\"https://wikipedia.org/wiki/Nikephoros_Ouranos\" title=\"<PERSON>ph<PERSON><PERSON> Ouranos\"><PERSON>ph<PERSON>s Ouranos</a> at the <a href=\"https://wikipedia.org/wiki/Spercheios\" title=\"Spercheios\">Spercheios River</a> in Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Spercheios\" title=\"Battle of Spercheios\">Battle of Spercheios</a>: Bulgarian forces of Tsar <a href=\"https://wikipedia.org/wiki/Samuel_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON></a> are defeated by a <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army under general <a href=\"https://wikipedia.org/wiki/Nikephoros_Ouranos\" title=\"<PERSON>ph<PERSON><PERSON> Ouranos\"><PERSON><PERSON><PERSON><PERSON> Ouranos</a> at the <a href=\"https://wikipedia.org/wiki/Spercheios\" title=\"Spercheios\">Spercheios River</a> in Greece.", "links": [{"title": "Battle of Spercheios", "link": "https://wikipedia.org/wiki/Battle_of_Sper<PERSON>ios"}, {"title": "Samuel of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bulgaria"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ph<PERSON>s_Ouranos"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Spercheios"}]}, {"year": "1054", "text": "Three Roman legates break relations between Western and Eastern Christian churches through the act of placing a papal bull (of doubtful validity) of excommunication on the altar of Hagia Sophia during Saturday afternoon divine liturgy. Historians frequently describe the event as the formal start of the East-West Schism.", "html": "1054 - Three Roman <a href=\"https://wikipedia.org/wiki/Papal_legate\" title=\"Papal legate\">legates</a> break relations between <a href=\"https://wikipedia.org/wiki/Western_Christianity\" title=\"Western Christianity\">Western</a> and <a href=\"https://wikipedia.org/wiki/Eastern_Christianity\" title=\"Eastern Christianity\">Eastern Christian churches</a> through the act of placing a <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> (of doubtful validity) of <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunication</a> on the altar of <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a> during Saturday afternoon divine liturgy. Historians frequently describe the event as the formal start of the <a href=\"https://wikipedia.org/wiki/East%E2%80%93West_Schism\" title=\"East-West Schism\">East-West Schism</a>.", "no_year_html": "Three Roman <a href=\"https://wikipedia.org/wiki/Papal_legate\" title=\"Papal legate\">legates</a> break relations between <a href=\"https://wikipedia.org/wiki/Western_Christianity\" title=\"Western Christianity\">Western</a> and <a href=\"https://wikipedia.org/wiki/Eastern_Christianity\" title=\"Eastern Christianity\">Eastern Christian churches</a> through the act of placing a <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> (of doubtful validity) of <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunication</a> on the altar of <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a> during Saturday afternoon divine liturgy. Historians frequently describe the event as the formal start of the <a href=\"https://wikipedia.org/wiki/East%E2%80%93West_Schism\" title=\"East-West Schism\">East-West Schism</a>.", "links": [{"title": "Papal legate", "link": "https://wikipedia.org/wiki/Papal_legate"}, {"title": "Western Christianity", "link": "https://wikipedia.org/wiki/Western_Christianity"}, {"title": "Eastern Christianity", "link": "https://wikipedia.org/wiki/Eastern_Christianity"}, {"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}, {"title": "Excommunication", "link": "https://wikipedia.org/wiki/Excommunication"}, {"title": "Hagia Sophia", "link": "https://wikipedia.org/wiki/Hagia_Sophia"}, {"title": "East-West Schism", "link": "https://wikipedia.org/wiki/East%E2%80%93West_Schism"}]}, {"year": "1212", "text": "Battle of Las Navas de Tolosa: After <PERSON> <PERSON> calls European knights to a crusade, the forces of kings <PERSON> of Castile, <PERSON><PERSON> of Navarre, <PERSON> of Aragon and <PERSON><PERSON><PERSON> of Portugal defeat those of the Berber Muslim leader <PERSON><PERSON><PERSON>, thus marking a significant turning point in the Reconquista and in the medieval history of Spain.", "html": "1212 - <a href=\"https://wikipedia.org/wiki/Battle_of_Las_Navas_de_Tolosa\" title=\"Battle of Las Navas de Tolosa\">Battle of Las Navas de Tolosa</a>: After <a href=\"https://wikipedia.org/wiki/Pope_Innocent_III\" title=\"Pope Innocent III\">Pope Innocent III</a> calls European knights to a <a href=\"https://wikipedia.org/wiki/Crusades\" title=\"Crusades\">crusade</a>, the forces of kings <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_Castile\" title=\"<PERSON> VIII of Castile\"><PERSON> VIII of Castile</a>, <a href=\"https://wikipedia.org/wiki/Sancho_VII_of_Navarre\" title=\"Sancho VII of Navarre\">Sancho VII of Navarre</a>, <a href=\"https://wikipedia.org/wiki/Peter_II_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Portugal\" title=\"<PERSON><PERSON><PERSON> II of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> defeat those of the <a href=\"https://wikipedia.org/wiki/Berbers\" title=\"Berbers\">Berber</a> Muslim leader <a href=\"https://wikipedia.org/wiki/Almohad_Caliphate\" title=\"Almohad Caliphate\">Almohad</a>, thus marking a significant turning point in the <i><a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a></i> and in the <a href=\"https://wikipedia.org/wiki/Spain_in_the_Middle_Ages\" title=\"Spain in the Middle Ages\">medieval history of Spain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Las_Navas_de_Tolosa\" title=\"Battle of Las Navas de Tolosa\">Battle of Las Navas de Tolosa</a>: After <a href=\"https://wikipedia.org/wiki/Pope_Innocent_III\" title=\"Pope Innocent III\">Pope Innocent III</a> calls European knights to a <a href=\"https://wikipedia.org/wiki/Crusades\" title=\"Crusades\">crusade</a>, the forces of kings <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_Castile\" title=\"<PERSON> VIII of Castile\"><PERSON> VIII of Castile</a>, <a href=\"https://wikipedia.org/wiki/Sancho_VII_of_Navarre\" title=\"Sancho VII of Navarre\">Sancho VII of Navarre</a>, <a href=\"https://wikipedia.org/wiki/Peter_II_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Portugal\" title=\"<PERSON><PERSON><PERSON> II of Portugal\"><PERSON><PERSON><PERSON> II of Portugal</a> defeat those of the <a href=\"https://wikipedia.org/wiki/Berbers\" title=\"Berbers\">Berber</a> Muslim leader <a href=\"https://wikipedia.org/wiki/Almohad_Caliphate\" title=\"Almohad Caliphate\">Almohad</a>, thus marking a significant turning point in the <i><a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a></i> and in the <a href=\"https://wikipedia.org/wiki/Spain_in_the_Middle_Ages\" title=\"Spain in the Middle Ages\">medieval history of Spain</a>.", "links": [{"title": "Battle of Las Navas de Tolosa", "link": "https://wikipedia.org/wiki/Battle_of_Las_Navas_de_Tolosa"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_<PERSON>"}, {"title": "Crusades", "link": "https://wikipedia.org/wiki/Crusades"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/Alfonso_VIII_of_Castile"}, {"title": "Sancho VII of Navarre", "link": "https://wikipedia.org/wiki/Sancho_VII_of_Navarre"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "<PERSON><PERSON><PERSON> II of Portugal", "link": "https://wikipedia.org/wiki/Afonso_II_of_Portugal"}, {"title": "Berbers", "link": "https://wikipedia.org/wiki/Berbers"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al<PERSON>had_Caliphate"}, {"title": "Reconquista", "link": "https://wikipedia.org/wiki/Reconquista"}, {"title": "Spain in the Middle Ages", "link": "https://wikipedia.org/wiki/Spain_in_the_Middle_Ages"}]}, {"year": "1228", "text": "The canonization of <PERSON> of Assisi", "html": "1228 - The <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonization</a> of <a href=\"https://wikipedia.org/wiki/Saint_<PERSON>_of_Assisi\" class=\"mw-redirect\" title=\"<PERSON> Francis of Assisi\"><PERSON> of Assisi</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonization</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Assisi\" class=\"mw-redirect\" title=\"<PERSON> of Assisi\"><PERSON> of Assisi</a>", "links": [{"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}, {"title": "<PERSON> of Assisi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Assisi"}]}, {"year": "1232", "text": "The Spanish town of Arjona declares independence and names its native <PERSON> ibn <PERSON> as ruler. This marks the <PERSON>'s first rise to prominence; he later established the Nasrid Emirate of Granada, the last independent Muslim state in Spain.", "html": "1232 - The Spanish town of <a href=\"https://wikipedia.org/wiki/Arjona,_Spain\" title=\"Arjona, Spain\">Arjona</a> declares independence and names its native <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Granada\" title=\"<PERSON> I of Granada\"><PERSON> ibn <PERSON></a> as ruler. This marks the <PERSON>'s first rise to prominence; he later established the <a href=\"https://wikipedia.org/wiki/Nasrid_dynasty\" title=\"Nasrid dynasty\">Nasrid</a> <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a>, the last independent Muslim state in Spain.", "no_year_html": "The Spanish town of <a href=\"https://wikipedia.org/wiki/Arjona,_Spain\" title=\"Arjona, Spain\">Arjona</a> declares independence and names its native <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Granada\" title=\"<PERSON> I of Granada\"><PERSON> ibn <PERSON></a> as ruler. This marks the <PERSON>'s first rise to prominence; he later established the <a href=\"https://wikipedia.org/wiki/Nasrid_dynasty\" title=\"Nasrid dynasty\">Nasrid</a> <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a>, the last independent Muslim state in Spain.", "links": [{"title": "A<PERSON>jona, Spain", "link": "https://wikipedia.org/wiki/Arjona,_Spain"}, {"title": "<PERSON> of Granada", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Granada"}, {"title": "Nasrid dynasty", "link": "https://wikipedia.org/wiki/Nasrid_dynasty"}, {"title": "Emirate of Granada", "link": "https://wikipedia.org/wiki/Emirate_of_Granada"}]}, {"year": "1251", "text": "Celebrated by the Carmelite Order-but doubted by modern historians-as the day when <PERSON> had a vision of the Virgin <PERSON>.", "html": "1251 - Celebrated by the <a href=\"https://wikipedia.org/wiki/Carmelite_Order\" class=\"mw-redirect\" title=\"Carmelite Order\">Carmelite Order</a>-but doubted by modern historians-as the day when Saint <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stock\"><PERSON></a> had a vision of the <a href=\"https://wikipedia.org/wiki/Virgin_Mary\" class=\"mw-redirect\" title=\"Virgin Mary\">Virgin Mary</a>.", "no_year_html": "Celebrated by the <a href=\"https://wikipedia.org/wiki/Carmelite_Order\" class=\"mw-redirect\" title=\"Carmelite Order\">Carmelite Order</a>-but doubted by modern historians-as the day when Saint <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> had a vision of the <a href=\"https://wikipedia.org/wiki/Virgin_Mary\" class=\"mw-redirect\" title=\"Virgin Mary\">Virgin Mary</a>.", "links": [{"title": "Carmelite Order", "link": "https://wikipedia.org/wiki/Carmelite_Order"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Virgin Mary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1377", "text": "King <PERSON> of England is crowned.", "html": "1377 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> is crowned.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> is crowned.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1536", "text": "<PERSON>, navigator and explorer, returns home to St. Malo after claiming Stadacona (Quebec), Hochelaga (Montereal) and the River of Canada (St. Lawrence River) region for France.", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, navigator and explorer, returns home to St. Malo after claiming Stadacona (Quebec), Hochelaga (Montereal) and the River of Canada (St. Lawrence River) region for France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, navigator and explorer, returns home to St. Malo after claiming Stadacona (Quebec), Hochelaga (Montereal) and the River of Canada (St. Lawrence River) region for France.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1661", "text": "The first banknotes in Europe are issued by the Swedish bank Stockholms Banco.", "html": "1661 - The first <a href=\"https://wikipedia.org/wiki/Banknote\" title=\"Banknote\">banknotes</a> in Europe are issued by the Swedish bank <a href=\"https://wikipedia.org/wiki/Stockholms_Banco\" title=\"Stockholms Banco\">Stockholms Banco</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Banknote\" title=\"Banknote\">banknotes</a> in Europe are issued by the Swedish bank <a href=\"https://wikipedia.org/wiki/Stockholms_Banco\" title=\"Stockholms Banco\">Stockholms Banco</a>.", "links": [{"title": "Banknote", "link": "https://wikipedia.org/wiki/Banknote"}, {"title": "Stockholms Banco", "link": "https://wikipedia.org/wiki/Stockholms_Banco"}]}, {"year": "1683", "text": "Manchu Qing dynasty naval forces under commander <PERSON> defeat the Kingdom of Tungning in the Battle of Penghu near the Pescadores Islands.", "html": "1683 - <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> naval forces under commander <a href=\"https://wikipedia.org/wiki/<PERSON>_Lang\" title=\"<PERSON> Lang\"><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Tungning\" title=\"Kingdom of Tungning\">Kingdom of Tungning</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Penghu\" title=\"Battle of Penghu\">Battle of Penghu</a> near the <a href=\"https://wikipedia.org/wiki/Penghu\" title=\"Penghu\">Pescadores Islands</a>.", "no_year_html": "Manchu <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> naval forces under commander <a href=\"https://wikipedia.org/wiki/<PERSON>_Lang\" title=\"<PERSON> Lang\"><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Tungning\" title=\"Kingdom of Tungning\">Kingdom of Tungning</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Penghu\" title=\"Battle of Penghu\">Battle of Penghu</a> near the <a href=\"https://wikipedia.org/wiki/Penghu\" title=\"Penghu\">Pescadores Islands</a>.", "links": [{"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kingdom of Tungning", "link": "https://wikipedia.org/wiki/Kingdom_of_Tungning"}, {"title": "Battle of Penghu", "link": "https://wikipedia.org/wiki/Battle_of_Penghu"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Penghu"}]}, {"year": "1769", "text": "Father <PERSON><PERSON><PERSON><PERSON> founds California's first mission, Mission San Diego de Alcalá. Over the following decades, it evolves into the city of San Diego, California.", "html": "1769 - Father <a href=\"https://wikipedia.org/wiki/Jun%C3%ADpero_Serra\" title=\"Junípero Serra\">Jun<PERSON><PERSON><PERSON> Serra</a> founds <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>'s first mission, <a href=\"https://wikipedia.org/wiki/Mission_San_Diego_de_Alcal%C3%A1\" title=\"Mission San Diego de Alcalá\">Mission San Diego de Alcalá</a>. Over the following decades, it evolves into the city of <a href=\"https://wikipedia.org/wiki/San_Diego\" title=\"San Diego\">San Diego, California</a>.", "no_year_html": "Father <a href=\"https://wikipedia.org/wiki/Jun%C3%ADpero_Serra\" title=\"Junípero Serra\">Juníper<PERSON> Serra</a> founds <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>'s first mission, <a href=\"https://wikipedia.org/wiki/Mission_San_Diego_de_Alcal%C3%A1\" title=\"Mission San Diego de Alcalá\">Mission San Diego de Alcalá</a>. Over the following decades, it evolves into the city of <a href=\"https://wikipedia.org/wiki/San_Diego\" title=\"San Diego\">San Diego, California</a>.", "links": [{"title": "Juní<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jun%C3%ADpero_Serra"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Mission San Diego de Alcalá", "link": "https://wikipedia.org/wiki/Mission_San_Diego_de_Alcal%C3%A1"}, {"title": "San Diego", "link": "https://wikipedia.org/wiki/San_Diego"}]}, {"year": "1779", "text": "American Revolutionary War: Light infantry of the Continental Army seize a fortified British Army position in a midnight bayonet attack at the Battle of Stony Point.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Light_infantry\" title=\"Light infantry\">Light infantry</a> of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> seize a fortified <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> position in a midnight bayonet attack at the <a href=\"https://wikipedia.org/wiki/Battle_of_Stony_Point\" title=\"Battle of Stony Point\">Battle of Stony Point</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Light_infantry\" title=\"Light infantry\">Light infantry</a> of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> seize a fortified <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> position in a midnight bayonet attack at the <a href=\"https://wikipedia.org/wiki/Battle_of_Stony_Point\" title=\"Battle of Stony Point\">Battle of Stony Point</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Light infantry", "link": "https://wikipedia.org/wiki/Light_infantry"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "Battle of Stony Point", "link": "https://wikipedia.org/wiki/Battle_of_Stony_Point"}]}, {"year": "1790", "text": "The District of Columbia is established as the capital of the United States after signature of the Residence Act.", "html": "1790 - The <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">District of Columbia</a> is established as the capital of the United States after signature of the <a href=\"https://wikipedia.org/wiki/Residence_Act\" title=\"Residence Act\">Residence Act</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">District of Columbia</a> is established as the capital of the United States after signature of the <a href=\"https://wikipedia.org/wiki/Residence_Act\" title=\"Residence Act\">Residence Act</a>.", "links": [{"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}, {"title": "Residence Act", "link": "https://wikipedia.org/wiki/Residence_Act"}]}, {"year": "1809", "text": "The city of La Paz, in what is today Bolivia, declares its independence from the Spanish Crown during the La Paz revolution and forms the Junta Tuitiva, the first independent government in Spanish America, led by <PERSON>.", "html": "1809 - The city of <a href=\"https://wikipedia.org/wiki/La_Paz\" title=\"La Paz\">La Paz</a>, in what is today <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a>, declares its independence from the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Spain\" title=\"Monarchy of Spain\">Spanish Crown</a> during the <a href=\"https://wikipedia.org/wiki/La_Paz_revolution\" title=\"La Paz revolution\">La Paz revolution</a> and forms the Junta Tuitiva, the first independent government in Spanish America, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/La_Paz\" title=\"La Paz\">La Paz</a>, in what is today <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a>, declares its independence from the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Spain\" title=\"Monarchy of Spain\">Spanish Crown</a> during the <a href=\"https://wikipedia.org/wiki/La_Paz_revolution\" title=\"La Paz revolution\">La Paz revolution</a> and forms the Junta Tuitiva, the first independent government in Spanish America, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "La Paz", "link": "https://wikipedia.org/wiki/La_Paz"}, {"title": "Bolivia", "link": "https://wikipedia.org/wiki/Bolivia"}, {"title": "Monarchy of Spain", "link": "https://wikipedia.org/wiki/Monarchy_of_Spain"}, {"title": "La Paz revolution", "link": "https://wikipedia.org/wiki/La_Paz_revolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON> founds the Congregation of the Missionary Sons of the Immaculate Heart of Mary, popularly known as the Claretians in Vic, in the province of Barcelona, Catalonia, Spain.", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Antonio_Mar%C3%ADa_Claret_y_Clar%C3%A1\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> founds the Congregation of the Missionary Sons of the Immaculate Heart of Mary, popularly known as the <a href=\"https://wikipedia.org/wiki/Claretians\" title=\"Claretians\">Claretians</a> in <a href=\"https://wikipedia.org/wiki/Vic,_Spain\" title=\"Vic, Spain\">Vic</a>, in the province of <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>, <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonio_Mar%C3%ADa_Claret_y_Clar%C3%A1\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> founds the Congregation of the Missionary Sons of the Immaculate Heart of Mary, popularly known as the <a href=\"https://wikipedia.org/wiki/Claretians\" title=\"Claretians\">Claretians</a> in <a href=\"https://wikipedia.org/wiki/Vic,_Spain\" title=\"Vic, Spain\">Vic</a>, in the province of <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>, <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, Spain.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_Mar%C3%AD<PERSON>_Claret_y_Clar%C3%A1"}, {"title": "Clare<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Claretians"}, {"title": "Vic, Spain", "link": "https://wikipedia.org/wiki/Vic,_Spain"}, {"title": "Barcelona", "link": "https://wikipedia.org/wiki/Barcelona"}, {"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}]}, {"year": "1858", "text": "The last apparition of the Blessed Virgin <PERSON> to Bernadette Soubirous in Lourdes, France.", "html": "1858 - The last <a href=\"https://wikipedia.org/wiki/Lourdes_apparitions\" title=\"Lourdes apparitions\">apparition</a> of the Blessed Virgin <PERSON> to <a href=\"https://wikipedia.org/wiki/Bernadette_Soubirous\" title=\"Bernadette Soubirous\">Bernadette Soubirous</a> in Lourdes, France.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Lourdes_apparitions\" title=\"Lourdes apparitions\">apparition</a> of the Blessed Virgin <PERSON> to <a href=\"https://wikipedia.org/wiki/Bernadette_Soubirous\" title=\"Bernadette Soubirous\">Bernadette Soubirous</a> in Lourdes, France.", "links": [{"title": "Lourdes apparitions", "link": "https://wikipedia.org/wiki/Lourdes_apparitions"}, {"title": "Bernadette Soubirous", "link": "https://wikipedia.org/wiki/Bernadette_Soubirous"}]}, {"year": "1861", "text": "American Civil War: At the order of President <PERSON>, Union troops begin a 25-mile march into Virginia for what will become the First Battle of Bull Run, the first major land battle of the war.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: At the order of President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham <PERSON>\"><PERSON></a>, Union troops begin a 25-mile <a href=\"https://wikipedia.org/wiki/Manassas_campaign\" class=\"mw-redirect\" title=\"Manassas campaign\">march</a> into <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> for what will become the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bull_Run\" title=\"First Battle of Bull Run\">First Battle of Bull Run</a>, the first major land battle of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: At the order of President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a>, Union troops begin a 25-mile <a href=\"https://wikipedia.org/wiki/Manassas_campaign\" class=\"mw-redirect\" title=\"Manassas campaign\">march</a> into <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> for what will become the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bull_Run\" title=\"First Battle of Bull Run\">First Battle of Bull Run</a>, the first major land battle of the war.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Manassas campaign", "link": "https://wikipedia.org/wiki/<PERSON>ass<PERSON>_campaign"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "First Battle of Bull Run", "link": "https://wikipedia.org/wiki/First_Battle_of_Bull_Run"}]}, {"year": "1862", "text": "American Civil War: <PERSON> is promoted to rear admiral, becoming the first officer in United States Navy to hold an admiral rank.", "html": "1862 - American Civil War: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is promoted to <a href=\"https://wikipedia.org/wiki/Rear_admiral\" title=\"Rear admiral\">rear admiral</a>, becoming the first officer in <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> to hold an admiral rank.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is promoted to <a href=\"https://wikipedia.org/wiki/Rear_admiral\" title=\"Rear admiral\">rear admiral</a>, becoming the first officer in <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> to hold an admiral rank.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Rear admiral", "link": "https://wikipedia.org/wiki/Rear_admiral"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}]}, {"year": "1909", "text": "Persian Constitutional Revolution: <PERSON> is forced out as <PERSON> of Persia and is replaced by his son <PERSON>.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Persian_Constitutional_Revolution\" title=\"Persian Constitutional Revolution\">Persian Constitutional Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is forced out as <PERSON> of Persia and is replaced by his son <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Persian_Constitutional_Revolution\" title=\"Persian Constitutional Revolution\">Persian Constitutional Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is forced out as <PERSON> of Persia and is replaced by his son <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Persian Constitutional Revolution", "link": "https://wikipedia.org/wiki/Persian_Constitutional_Revolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON> makes the first flight of the <PERSON><PERSON> pusher biplane, the first aircraft built in Australia.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes the first flight of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_pusher_biplane\" title=\"Du<PERSON> pusher biplane\"><PERSON><PERSON> pusher biplane</a>, the first aircraft built in Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes the first flight of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_pusher_biplane\" title=\"Duigan pusher biplane\"><PERSON><PERSON> pusher biplane</a>, the first aircraft built in Australia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Duigan pusher biplane", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_pusher_biplane"}]}, {"year": "1915", "text": "<PERSON> becomes a British citizen to highlight his commitment to Britain during the first World War.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes a British citizen to highlight his commitment to Britain during the first World War.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes a British citizen to highlight his commitment to Britain during the first World War.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "At Treasure Island on the Delaware River in the United States, the First Order of the Arrow ceremony takes place and the Order of the Arrow is founded to honor American Boy Scouts who best exemplify the Scout Oath and Law.", "html": "1915 - At Treasure Island on the <a href=\"https://wikipedia.org/wiki/Delaware_River\" title=\"Delaware River\">Delaware River</a> in the United States, the First <a href=\"https://wikipedia.org/wiki/Order_of_the_Arrow\" title=\"Order of the Arrow\">Order of the Arrow</a> ceremony takes place and the Order of the Arrow is founded to honor American Boy Scouts who best exemplify the Scout Oath and Law.", "no_year_html": "At Treasure Island on the <a href=\"https://wikipedia.org/wiki/Delaware_River\" title=\"Delaware River\">Delaware River</a> in the United States, the First <a href=\"https://wikipedia.org/wiki/Order_of_the_Arrow\" title=\"Order of the Arrow\">Order of the Arrow</a> ceremony takes place and the Order of the Arrow is founded to honor American Boy Scouts who best exemplify the Scout Oath and Law.", "links": [{"title": "Delaware River", "link": "https://wikipedia.org/wiki/Delaware_River"}, {"title": "Order of the Arrow", "link": "https://wikipedia.org/wiki/Order_of_the_Arrow"}]}, {"year": "1927", "text": "<PERSON><PERSON> leads a raid on U.S. Marines and Nicaraguan Guardia Nacional that had been sent to apprehend him in the village of Ocotal, but is repulsed by one of the first dive-bombing attacks in history.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Augusto_C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">August<PERSON></a> leads a raid on U.S. Marines and Nicaraguan Guardia Nacional that had been sent to apprehend him in the village of <a href=\"https://wikipedia.org/wiki/Ocotal\" title=\"Ocotal\">Ocotal</a>, but is repulsed by one of the first <a href=\"https://wikipedia.org/wiki/Dive_bomber\" title=\"Dive bomber\">dive-bombing</a> attacks in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusto_C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">August<PERSON></a> leads a raid on U.S. Marines and Nicaraguan Guardia Nacional that had been sent to apprehend him in the village of <a href=\"https://wikipedia.org/wiki/Ocotal\" title=\"Ocotal\">Ocotal</a>, but is repulsed by one of the first <a href=\"https://wikipedia.org/wiki/Dive_bomber\" title=\"Dive bomber\">dive-bombing</a> attacks in history.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Augusto_C%C3%A9sar_<PERSON><PERSON>"}, {"title": "Ocotal", "link": "https://wikipedia.org/wiki/Ocotal"}, {"title": "Dive bomber", "link": "https://wikipedia.org/wiki/Dive_bomber"}]}, {"year": "1931", "text": "Emperor <PERSON><PERSON> signs the first constitution of Ethiopia.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Haile_Selassie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/1931_Constitution_of_Ethiopia\" title=\"1931 Constitution of Ethiopia\">first constitution of Ethiopia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Haile_Selassie\" title=\"Haile Selassie\"><PERSON><PERSON> Se<PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/1931_Constitution_of_Ethiopia\" title=\"1931 Constitution of Ethiopia\">first constitution of Ethiopia</a>.", "links": [{"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "1931 Constitution of Ethiopia", "link": "https://wikipedia.org/wiki/1931_Constitution_of_Ethiopia"}]}, {"year": "1935", "text": "The world's first parking meter is installed in Oklahoma City, Oklahoma.", "html": "1935 - The world's first <a href=\"https://wikipedia.org/wiki/Parking_meter\" title=\"Parking meter\">parking meter</a> is installed in <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City, Oklahoma</a>.", "no_year_html": "The world's first <a href=\"https://wikipedia.org/wiki/Parking_meter\" title=\"Parking meter\">parking meter</a> is installed in <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City, Oklahoma</a>.", "links": [{"title": "Parking meter", "link": "https://wikipedia.org/wiki/Parking_meter"}, {"title": "Oklahoma City", "link": "https://wikipedia.org/wiki/Oklahoma_City"}]}, {"year": "1941", "text": "<PERSON> hits safely for the 56th consecutive game, a streak that still stands as an MLB record.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> hits safely for the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_56-game_hitting_streak\" title=\"<PERSON>'s 56-game hitting streak\">56th consecutive game</a>, a streak that still stands as an <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">MLB</a> record.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> hits safely for the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_56-game_hitting_streak\" title=\"<PERSON>'s 56-game hitting streak\">56th consecutive game</a>, a streak that still stands as an <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">MLB</a> record.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s 56-game hitting streak", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_56-game_hitting_streak"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}]}, {"year": "1942", "text": "Holocaust: Vel' d'Hiv Roundup (<PERSON><PERSON> du Vel' d'Hiv): The government of Vichy France orders the mass arrest of 13,152 Jews who are held at the Vélodrome d'Hiver in Paris before deportation to Auschwitz.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Vel%27_d%27Hiv_Roundup\" title=\"Vel' d'Hiv Roundup\">Vel' d'Hiv Roundup</a> (<i><PERSON><PERSON> du <PERSON>el' d'Hiv</i>): The government of <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy France</a> orders the <a href=\"https://wikipedia.org/wiki/Mass_arrest\" title=\"Mass arrest\">mass arrest</a> of 13,152 Jews who are held at the <a href=\"https://wikipedia.org/wiki/V%C3%A9lodrome_d%27Hiver\" title=\"Vélodrome d'Hiver\">Vélodrome d'Hiver</a> in Paris before deportation to <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Vel%27_d%27Hiv_Roundup\" title=\"Vel' d'Hiv Roundup\">Vel' d'Hiv Roundup</a> (<i><PERSON><PERSON> du <PERSON>el' d'Hiv</i>): The government of <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy France</a> orders the <a href=\"https://wikipedia.org/wiki/Mass_arrest\" title=\"Mass arrest\">mass arrest</a> of 13,152 Jews who are held at the <a href=\"https://wikipedia.org/wiki/V%C3%A9lodrome_d%27Hiver\" title=\"Vélodrome d'Hiver\">Vélodrome d'Hiver</a> in Paris before deportation to <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Vel' d'Hiv Roundup", "link": "https://wikipedia.org/wiki/Vel%27_d%27Hiv_Roundup"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}, {"title": "Mass arrest", "link": "https://wikipedia.org/wiki/Mass_arrest"}, {"title": "Vélodrome d'Hiver", "link": "https://wikipedia.org/wiki/V%C3%A9lodrome_d%27Hiver"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1945", "text": "Manhattan Project: The Atomic Age begins when the United States successfully detonates a plutonium-based test nuclear weapon near Alamogordo, New Mexico.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Manhattan_Project\" title=\"Manhattan Project\">Manhattan Project</a>: The <a href=\"https://wikipedia.org/wiki/Atomic_Age\" title=\"Atomic Age\">Atomic Age</a> begins when the United States <a href=\"https://wikipedia.org/wiki/Trinity_(nuclear_test)\" title=\"Trinity (nuclear test)\">successfully detonates</a> a <a href=\"https://wikipedia.org/wiki/Plutonium\" title=\"Plutonium\">plutonium</a>-based test <a href=\"https://wikipedia.org/wiki/Nuclear_weapon\" title=\"Nuclear weapon\">nuclear weapon</a> near <a href=\"https://wikipedia.org/wiki/Alamogordo,_New_Mexico\" title=\"Alamogordo, New Mexico\">Alamogordo, New Mexico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manhattan_Project\" title=\"Manhattan Project\">Manhattan Project</a>: The <a href=\"https://wikipedia.org/wiki/Atomic_Age\" title=\"Atomic Age\">Atomic Age</a> begins when the United States <a href=\"https://wikipedia.org/wiki/Trinity_(nuclear_test)\" title=\"Trinity (nuclear test)\">successfully detonates</a> a <a href=\"https://wikipedia.org/wiki/Plutonium\" title=\"Plutonium\">plutonium</a>-based test <a href=\"https://wikipedia.org/wiki/Nuclear_weapon\" title=\"Nuclear weapon\">nuclear weapon</a> near <a href=\"https://wikipedia.org/wiki/Alamogordo,_New_Mexico\" title=\"Alamogordo, New Mexico\">Alamogordo, New Mexico</a>.", "links": [{"title": "Manhattan Project", "link": "https://wikipedia.org/wiki/Manhattan_Project"}, {"title": "Atomic Age", "link": "https://wikipedia.org/wiki/Atomic_Age"}, {"title": "Trinity (nuclear test)", "link": "https://wikipedia.org/wiki/Trinity_(nuclear_test)"}, {"title": "Plutonium", "link": "https://wikipedia.org/wiki/Plutonium"}, {"title": "Nuclear weapon", "link": "https://wikipedia.org/wiki/Nuclear_weapon"}, {"title": "Alamogordo, New Mexico", "link": "https://wikipedia.org/wiki/Alamogordo,_New_Mexico"}]}, {"year": "1945", "text": "World War II: The heavy cruiser USS Indianapolis leaves San Francisco with parts for the atomic bomb \"Little Boy\" bound for Tinian Island.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Heavy_cruiser\" title=\"Heavy cruiser\">heavy cruiser</a> <a href=\"https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)\" title=\"USS Indianapolis (CA-35)\">USS <i>Indianapolis</i></a> leaves San Francisco with parts for the atomic bomb \"<a href=\"https://wikipedia.org/wiki/Little_Boy\" title=\"Little Boy\">Little Boy</a>\" bound for <a href=\"https://wikipedia.org/wiki/Tinian\" class=\"mw-redirect\" title=\"Tinian\">Tinian Island</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Heavy_cruiser\" title=\"Heavy cruiser\">heavy cruiser</a> <a href=\"https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)\" title=\"USS Indianapolis (CA-35)\">USS <i>Indianapolis</i></a> leaves San Francisco with parts for the atomic bomb \"<a href=\"https://wikipedia.org/wiki/Little_Boy\" title=\"Little Boy\">Little Boy</a>\" bound for <a href=\"https://wikipedia.org/wiki/Tinian\" class=\"mw-redirect\" title=\"Tinian\">Tinian Island</a>.", "links": [{"title": "Heavy cruiser", "link": "https://wikipedia.org/wiki/Heavy_cruiser"}, {"title": "USS Indianapolis (CA-35)", "link": "https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)"}, {"title": "Little Boy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}]}, {"year": "1948", "text": "Following token resistance, the city of Nazareth, revered by Christians as the hometown of <PERSON>, capitulates to Israeli troops during Operation Dekel in the 1948 Arab-Israeli War.", "html": "1948 - Following token resistance, the city of <a href=\"https://wikipedia.org/wiki/Nazareth\" title=\"Nazareth\">Nazareth</a>, revered by Christians as the hometown of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Jesus</a>, capitulates to <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> troops during <a href=\"https://wikipedia.org/wiki/Operation_Dekel\" title=\"Operation Dekel\">Operation Dekel</a> in the <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">1948 Arab-Israeli War</a>.", "no_year_html": "Following token resistance, the city of <a href=\"https://wikipedia.org/wiki/Nazareth\" title=\"Nazareth\">Nazareth</a>, revered by Christians as the hometown of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Jesus</a>, capitulates to <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> troops during <a href=\"https://wikipedia.org/wiki/Operation_Dekel\" title=\"Operation Dekel\">Operation Dekel</a> in the <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">1948 Arab-Israeli War</a>.", "links": [{"title": "Nazareth", "link": "https://wikipedia.org/wiki/Nazareth"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Operation Dekel", "link": "https://wikipedia.org/wiki/Operation_Dekel"}, {"title": "1948 Arab-Israeli War", "link": "https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War"}]}, {"year": "1948", "text": "The storming of the cockpit of the Miss <PERSON> passenger seaplane, operated by a subsidiary of the Cathay Pacific Airways, marks the first aircraft hijacking of a commercial plane.", "html": "1948 - The storming of the cockpit of the <i><a href=\"https://wikipedia.org/wiki/Miss_Mac<PERSON>\" title=\"Miss Macao\">Miss <PERSON></a></i> passenger seaplane, operated by a subsidiary of the <a href=\"https://wikipedia.org/wiki/Cathay_Pacific\" title=\"Cathay Pacific\">Cathay Pacific</a> Airways, marks the first <a href=\"https://wikipedia.org/wiki/Aircraft_hijacking\" title=\"Aircraft hijacking\">aircraft hijacking</a> of a commercial plane.", "no_year_html": "The storming of the cockpit of the <i><a href=\"https://wikipedia.org/wiki/Miss_Mac<PERSON>\" title=\"Miss Macao\">Miss <PERSON></a></i> passenger seaplane, operated by a subsidiary of the <a href=\"https://wikipedia.org/wiki/Cathay_Pacific\" title=\"Cathay Pacific\">Cathay Pacific</a> Airways, marks the first <a href=\"https://wikipedia.org/wiki/Aircraft_hijacking\" title=\"Aircraft hijacking\">aircraft hijacking</a> of a commercial plane.", "links": [{"title": "Miss <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cathay Pacific", "link": "https://wikipedia.org/wiki/Cathay_Pacific"}, {"title": "Aircraft hijacking", "link": "https://wikipedia.org/wiki/Aircraft_hijacking"}]}, {"year": "1950", "text": "Chaplain-Medic massacre: American POWs are massacred by North Korean Army.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Chaplain%E2%80%93Medic_massacre\" title=\"Chaplain-Medic massacre\">Chaplain-Medic massacre</a>: American POWs are massacred by North Korean Army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chaplain%E2%80%93Medic_massacre\" title=\"Chaplain-Medic massacre\">Chaplain-Medic massacre</a>: American POWs are massacred by North Korean Army.", "links": [{"title": "Chaplain-Medic massacre", "link": "https://wikipedia.org/wiki/Chaplain%E2%80%93Medic_massacre"}]}, {"year": "1950", "text": "Uruguay beats Brazil 2-1, to win the 1950 World Cup hat has been dubbed the Maracanazo.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Uruguay_national_football_team\" title=\"Uruguay national football team\">Uruguay</a> beats <a href=\"https://wikipedia.org/wiki/Brazil_national_football_team\" title=\"Brazil national football team\">Brazil</a> <a href=\"https://wikipedia.org/wiki/Uruguay_v_Brazil_(1950_FIFA_World_Cup)\" title=\"Uruguay v Brazil (1950 FIFA World Cup)\">2-1</a>, to win the <a href=\"https://wikipedia.org/wiki/1950_FIFA_World_Cup\" title=\"1950 FIFA World Cup\">1950 World Cup</a> hat has been dubbed the <a href=\"https://wikipedia.org/wiki/Maracanazo\" class=\"mw-redirect\" title=\"Maracanazo\">Maracanazo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uruguay_national_football_team\" title=\"Uruguay national football team\">Uruguay</a> beats <a href=\"https://wikipedia.org/wiki/Brazil_national_football_team\" title=\"Brazil national football team\">Brazil</a> <a href=\"https://wikipedia.org/wiki/Uruguay_v_Brazil_(1950_FIFA_World_Cup)\" title=\"Uruguay v Brazil (1950 FIFA World Cup)\">2-1</a>, to win the <a href=\"https://wikipedia.org/wiki/1950_FIFA_World_Cup\" title=\"1950 FIFA World Cup\">1950 World Cup</a> hat has been dubbed the <a href=\"https://wikipedia.org/wiki/Maracanazo\" class=\"mw-redirect\" title=\"Maracanazo\">Maracanazo</a>.", "links": [{"title": "Uruguay national football team", "link": "https://wikipedia.org/wiki/Uruguay_national_football_team"}, {"title": "Brazil national football team", "link": "https://wikipedia.org/wiki/Brazil_national_football_team"}, {"title": "Uruguay v Brazil (1950 FIFA World Cup)", "link": "https://wikipedia.org/wiki/Uruguay_v_Brazil_(1950_FIFA_World_Cup)"}, {"title": "1950 FIFA World Cup", "link": "https://wikipedia.org/wiki/1950_FIFA_World_Cup"}, {"title": "Maracanazo", "link": "https://wikipedia.org/wiki/Maracanazo"}]}, {"year": "1951", "text": "King <PERSON> of Belgium abdicates in favor of his son, <PERSON><PERSON><PERSON> of Belgium.", "html": "1951 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"Leopold III of Belgium\"><PERSON> of Belgium</a> abdicates in favor of his son, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Belgium\" title=\"<PERSON><PERSON><PERSON> of Belgium\"><PERSON><PERSON><PERSON> of Belgium</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"Leopold III of Belgium\"><PERSON> of Belgium</a> abdicates in favor of his son, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Belgium\" title=\"<PERSON><PERSON><PERSON> of Belgium\"><PERSON><PERSON><PERSON> of Belgium</a>.", "links": [{"title": "<PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Leopold_III_of_Belgium"}, {"title": "<PERSON><PERSON><PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Baudouin_of_Belgium"}]}, {"year": "1951", "text": "<PERSON><PERSON> <PERSON><PERSON> publishes his popular yet controversial novel, The Catcher in the Rye.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> publishes his popular yet controversial novel, <i><a href=\"https://wikipedia.org/wiki/The_Catcher_in_the_Rye\" title=\"The Catcher in the Rye\">The Catcher in the Rye</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> publishes his popular yet controversial novel, <i><a href=\"https://wikipedia.org/wiki/The_Catcher_in_the_Rye\" title=\"The Catcher in the Rye\">The Catcher in the Rye</a></i>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "The Catcher in the Rye", "link": "https://wikipedia.org/wiki/The_Catcher_in_the_Rye"}]}, {"year": "1956", "text": "Ringling Bros. and Barnum & Bailey Circus closes its last \"Big Tent\" show in Pittsburgh, Pennsylvania; due to changing economics, all subsequent circus shows will be held in arenas.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_Bailey_Circus\" title=\"Ringling Bros. and Barnum &amp; Bailey Circus\">Ringling Bros. and Barnum &amp; Bailey Circus</a> closes its last \"Big Tent\" show in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania</a>; due to changing economics, all subsequent circus shows will be held in arenas.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_Bailey_Circus\" title=\"Ringling Bros. and Barnum &amp; Bailey Circus\">Ringling Bros. and Barnum &amp; Bailey Circus</a> closes its last \"Big Tent\" show in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania</a>; due to changing economics, all subsequent circus shows will be held in arenas.", "links": [{"title": "Ringling Bros. and Barnum & Bailey Circus", "link": "https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_<PERSON>_<PERSON>"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}]}, {"year": "1957", "text": "KLM Flight 844 crashes off the Schouten Islands in present day Indonesia (then Netherlands New Guinea), killing 58 people.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/KLM_Flight_844\" title=\"KLM Flight 844\">KLM Flight 844</a> crashes off the <a href=\"https://wikipedia.org/wiki/Schouten_Islands\" class=\"mw-redirect\" title=\"Schouten Islands\">Schouten Islands</a> in present day <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> (then <a href=\"https://wikipedia.org/wiki/Netherlands_New_Guinea\" class=\"mw-redirect\" title=\"Netherlands New Guinea\">Netherlands New Guinea</a>), killing 58 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/KLM_Flight_844\" title=\"KLM Flight 844\">KLM Flight 844</a> crashes off the <a href=\"https://wikipedia.org/wiki/Schouten_Islands\" class=\"mw-redirect\" title=\"Schouten Islands\">Schouten Islands</a> in present day <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> (then <a href=\"https://wikipedia.org/wiki/Netherlands_New_Guinea\" class=\"mw-redirect\" title=\"Netherlands New Guinea\">Netherlands New Guinea</a>), killing 58 people.", "links": [{"title": "KLM Flight 844", "link": "https://wikipedia.org/wiki/KLM_Flight_844"}, {"title": "Schouten Islands", "link": "https://wikipedia.org/wiki/Schouten_Islands"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Netherlands New Guinea", "link": "https://wikipedia.org/wiki/Netherlands_New_Guinea"}]}, {"year": "1965", "text": "The Mont Blanc Tunnel linking France and Italy opens.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Mont_Blanc_Tunnel\" title=\"Mont Blanc Tunnel\">Mont Blanc Tunnel</a> linking France and Italy opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mont_Blanc_Tunnel\" title=\"Mont Blanc Tunnel\">Mont Blanc Tunnel</a> linking France and Italy opens.", "links": [{"title": "Mont Blanc Tunnel", "link": "https://wikipedia.org/wiki/Mont_Blanc_Tunnel"}]}, {"year": "1965", "text": "South Vietnamese Colonel <PERSON><PERSON><PERSON>, a formerly undetected communist spy and double agent, is hunted down and killed by unknown individuals after being sentenced to death in absentia for a February 1965 coup attempt against <PERSON><PERSON><PERSON><PERSON>.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese</a> Colonel <a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_Ng%E1%BB%8Dc_Th%E1%BA%A3o\" title=\"<PERSON>ạ<PERSON>\"><PERSON><PERSON><PERSON></a>, a formerly undetected <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">communist spy and double agent</a>, is hunted down and killed by unknown individuals after being sentenced to death <i>in absentia</i> for a <a href=\"https://wikipedia.org/wiki/1965_South_Vietnamese_coup\" title=\"1965 South Vietnamese coup\">February 1965 coup attempt</a> against <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese</a> Colonel <a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_Ng%E1%BB%8Dc_Th%E1%BA%A3o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a formerly undetected <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">communist spy and double agent</a>, is hunted down and killed by unknown individuals after being sentenced to death <i>in absentia</i> for a <a href=\"https://wikipedia.org/wiki/1965_South_Vietnamese_coup\" title=\"1965 South Vietnamese coup\">February 1965 coup attempt</a> against <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ph%E1%BA%A1m_Ng%E1%BB%8Dc_Th%E1%BA%A3o"}, {"title": "People's Army of Vietnam", "link": "https://wikipedia.org/wiki/People%27s_Army_of_Vietnam"}, {"title": "1965 South Vietnamese coup", "link": "https://wikipedia.org/wiki/1965_South_Vietnamese_coup"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}]}, {"year": "1969", "text": "The Apollo 11 lunar landing mission is launched from Cape Kennedy in Florida, USA.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> lunar landing mission is launched from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\">Cape Kennedy</a> in Florida, USA.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> lunar landing mission is launched from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\">Cape Kennedy</a> in Florida, USA.", "links": [{"title": "Apollo 11", "link": "https://wikipedia.org/wiki/Apollo_11"}, {"title": "Cape Canaveral", "link": "https://wikipedia.org/wiki/Cape_Canaveral"}]}, {"year": "1979", "text": "Iraqi President <PERSON> resigns and is replaced by <PERSON>.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/President_of_Iraq\" title=\"President of Iraq\">Iraqi President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns and is replaced by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Iraq\" title=\"President of Iraq\">Iraqi President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns and is replaced by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "President of Iraq", "link": "https://wikipedia.org/wiki/President_of_Iraq"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "Sikorsky S-61 disaster: A helicopter crashes off the Isles of Scilly, causing 20 fatalities.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/1983_British_Airways_Sikorsky_S-61_crash\" class=\"mw-redirect\" title=\"1983 British Airways Sikorsky S-61 crash\">Sikorsky S-61 disaster</a>: A helicopter crashes off the <a href=\"https://wikipedia.org/wiki/Isles_of_Scilly\" title=\"Isles of Scilly\">Isles of Scilly</a>, causing 20 fatalities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1983_British_Airways_Sikorsky_S-61_crash\" class=\"mw-redirect\" title=\"1983 British Airways Sikorsky S-61 crash\">Sikorsky S-61 disaster</a>: A helicopter crashes off the <a href=\"https://wikipedia.org/wiki/Isles_of_Scilly\" title=\"Isles of Scilly\">Isles of Scilly</a>, causing 20 fatalities.", "links": [{"title": "1983 British Airways Sikorsky S-61 crash", "link": "https://wikipedia.org/wiki/1983_British_Airways_Sikorsky_S-61_crash"}, {"title": "Isles of Scilly", "link": "https://wikipedia.org/wiki/Isles_of_Scilly"}]}, {"year": "1990", "text": "The Luzon earthquake strikes the Philippines with an intensity of 7.7, affecting Benguet, Pangasinan, Nueva Ecija, La Union, Aurora, Bataan, Zambales and Tarlac.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/1990_Luzon_earthquake\" title=\"1990 Luzon earthquake\">Luzon earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> with an intensity of 7.7, affecting <a href=\"https://wikipedia.org/wiki/Benguet\" title=\"Benguet\">Benguet</a>, <a href=\"https://wikipedia.org/wiki/Pangasinan\" title=\"Pangasinan\">Pangasinan</a>, <a href=\"https://wikipedia.org/wiki/Nueva_Ecija\" title=\"Nueva Ecija\">Nueva Ecija</a>, <a href=\"https://wikipedia.org/wiki/La_Union\" title=\"La Union\">La Union</a>, <a href=\"https://wikipedia.org/wiki/Aurora_(province)\" title=\"Aurora (province)\">Aurora</a>, <a href=\"https://wikipedia.org/wiki/Bataan\" title=\"Bataan\">Bataan</a>, <a href=\"https://wikipedia.org/wiki/Zambales\" title=\"Zambales\">Zambales</a> and <a href=\"https://wikipedia.org/wiki/Tarlac\" title=\"Tarlac\">Tarlac</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1990_Luzon_earthquake\" title=\"1990 Luzon earthquake\">Luzon earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> with an intensity of 7.7, affecting <a href=\"https://wikipedia.org/wiki/Benguet\" title=\"Benguet\">Benguet</a>, <a href=\"https://wikipedia.org/wiki/Pangasinan\" title=\"Pangasinan\">Pangasinan</a>, <a href=\"https://wikipedia.org/wiki/Nueva_Ecija\" title=\"Nueva Ecija\">Nueva Ecija</a>, <a href=\"https://wikipedia.org/wiki/La_Union\" title=\"La Union\">La Union</a>, <a href=\"https://wikipedia.org/wiki/Aurora_(province)\" title=\"Aurora (province)\">Aurora</a>, <a href=\"https://wikipedia.org/wiki/Bataan\" title=\"Bataan\">Bataan</a>, <a href=\"https://wikipedia.org/wiki/Zambales\" title=\"Zambales\">Zambales</a> and <a href=\"https://wikipedia.org/wiki/Tarlac\" title=\"Tarlac\">Tarlac</a>.", "links": [{"title": "1990 Luzon earthquake", "link": "https://wikipedia.org/wiki/1990_Luzon_earthquake"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benguet"}, {"title": "Pangasinan", "link": "https://wikipedia.org/wiki/Pangasinan"}, {"title": "Nueva Ecija", "link": "https://wikipedia.org/wiki/Nueva_Ecija"}, {"title": "La Union", "link": "https://wikipedia.org/wiki/La_Union"}, {"title": "Aurora (province)", "link": "https://wikipedia.org/wiki/Aurora_(province)"}, {"title": "Bataan", "link": "https://wikipedia.org/wiki/Bataan"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zambales"}, {"title": "Tarlac", "link": "https://wikipedia.org/wiki/<PERSON>rlac"}]}, {"year": "1990", "text": "The Parliament of the Ukrainian SSR declares state sovereignty over the territory of the Ukrainian SSR.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Verkhovna_Rada\" title=\"Verkhovna Rada\">Parliament of the Ukrainian SSR</a> <a href=\"https://wikipedia.org/wiki/Declaration_of_State_Sovereignty_of_Ukraine\" title=\"Declaration of State Sovereignty of Ukraine\">declares state sovereignty</a> over the territory of the <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian SSR</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Verkhovna_Rada\" title=\"Verkhovna Rada\">Parliament of the Ukrainian SSR</a> <a href=\"https://wikipedia.org/wiki/Declaration_of_State_Sovereignty_of_Ukraine\" title=\"Declaration of State Sovereignty of Ukraine\">declares state sovereignty</a> over the territory of the <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian SSR</a>.", "links": [{"title": "Verkhovna Rada", "link": "https://wikipedia.org/wiki/Verkhovna_Rada"}, {"title": "Declaration of State Sovereignty of Ukraine", "link": "https://wikipedia.org/wiki/Declaration_of_State_Sovereignty_of_Ukraine"}, {"title": "Ukrainian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic"}]}, {"year": "1994", "text": "The comet Shoemaker-Levy 9 is destroyed in a head-on collision with Jupiter.", "html": "1994 - The comet <a href=\"https://wikipedia.org/wiki/Comet_Shoemaker%E2%80%93Levy_9\" title=\"Comet Shoemaker-Levy 9\"><PERSON><PERSON><PERSON>-<PERSON> 9</a> is destroyed in a head-on collision with <a href=\"https://wikipedia.org/wiki/Jupiter\" title=\"Jupiter\">Jupiter</a>.", "no_year_html": "The comet <a href=\"https://wikipedia.org/wiki/Comet_Shoemaker%E2%80%93Levy_9\" title=\"Comet Shoemaker-Levy 9\"><PERSON><PERSON><PERSON>-<PERSON> 9</a> is destroyed in a head-on collision with <a href=\"https://wikipedia.org/wiki/Jupiter\" title=\"Jupiter\">Jupiter</a>.", "links": [{"title": "Comet Shoemaker-Levy 9", "link": "https://wikipedia.org/wiki/Comet_Shoemaker%E2%80%93Levy_9"}, {"title": "Jupiter", "link": "https://wikipedia.org/wiki/Jupiter"}]}, {"year": "1999", "text": "<PERSON>, his wife, <PERSON>, and her sister, <PERSON>, die when the aircraft he is piloting crashes into the Atlantic Ocean off the coast of Martha's Vineyard.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr</a>., his wife, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, and her sister, <PERSON>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>._plane_crash\" class=\"mw-redirect\" title=\"<PERSON> Jr. plane crash\">die when the aircraft</a> he is piloting crashes into the Atlantic Ocean off the coast of Martha's Vineyard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr</a>., his wife, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and her sister, <PERSON>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>._plane_crash\" class=\"mw-redirect\" title=\"<PERSON> Jr. plane crash\">die when the aircraft</a> he is piloting crashes into the Atlantic Ocean off the coast of Martha's Vineyard.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> plane crash", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>._plane_crash"}]}, {"year": "2004", "text": "Millennium Park, considered Chicago's first and most ambitious early 21st-century architectural project, is opened to the public by Mayor <PERSON>.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Millennium_Park\" title=\"Millennium Park\">Millennium Park</a>, considered Chicago's first and most ambitious early 21st-century architectural project, is opened to the public by <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Millennium_Park\" title=\"Millennium Park\">Millennium Park</a>, considered Chicago's first and most ambitious early 21st-century architectural project, is opened to the public by <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Millennium Park", "link": "https://wikipedia.org/wiki/Millennium_Park"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "An Antonov An-24 crashes near Baney in Bioko Norte, Equatorial Guinea, killing 60 people.", "html": "2005 - An <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\"><PERSON>ov An-24</a> <a href=\"https://wikipedia.org/wiki/2005_Equatorial_Express_Airlines_An-24_crash\" title=\"2005 Equatorial Express Airlines An-24 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Baney\" title=\"Baney\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Bioko_Norte\" title=\"Bioko Norte\">Bioko Norte</a>, <a href=\"https://wikipedia.org/wiki/Equatorial_Guinea\" title=\"Equatorial Guinea\">Equatorial Guinea</a>, killing 60 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\"><PERSON><PERSON> An-24</a> <a href=\"https://wikipedia.org/wiki/2005_Equatorial_Express_Airlines_An-24_crash\" title=\"2005 Equatorial Express Airlines An-24 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Baney\" title=\"Baney\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Bioko_Norte\" title=\"Bioko Norte\">Bioko Norte</a>, <a href=\"https://wikipedia.org/wiki/Equatorial_Guinea\" title=\"Equatorial Guinea\">Equatorial Guinea</a>, killing 60 people.", "links": [{"title": "Antonov An-24", "link": "https://wikipedia.org/wiki/Antonov_An-24"}, {"title": "2005 Equatorial Express Airlines An-24 crash", "link": "https://wikipedia.org/wiki/2005_Equatorial_Express_Airlines_An-24_crash"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ey"}, {"title": "Bioko Norte", "link": "https://wikipedia.org/wiki/Bioko_Norte"}, {"title": "Equatorial Guinea", "link": "https://wikipedia.org/wiki/Equatorial_Guinea"}]}, {"year": "2007", "text": "An earthquake of magnitude 6.8 and 6.6 aftershock occurs off the Niigata coast of Japan killing eight people, injuring at least 800 and damaging a nuclear power plant.", "html": "2007 - An <a href=\"https://wikipedia.org/wiki/2007_Ch%C5%ABetsu_offshore_earthquake\" title=\"2007 Chūetsu offshore earthquake\">earthquake</a> of magnitude 6.8 and 6.6 aftershock occurs off the <a href=\"https://wikipedia.org/wiki/Niigata_(city)\" title=\"Niigata (city)\">Niigata</a> coast of Japan killing eight people, injuring at least 800 and damaging a nuclear power plant.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2007_Ch%C5%ABetsu_offshore_earthquake\" title=\"2007 Chūetsu offshore earthquake\">earthquake</a> of magnitude 6.8 and 6.6 aftershock occurs off the <a href=\"https://wikipedia.org/wiki/Niigata_(city)\" title=\"Niigata (city)\">Niigata</a> coast of Japan killing eight people, injuring at least 800 and damaging a nuclear power plant.", "links": [{"title": "2007 Chūetsu offshore earthquake", "link": "https://wikipedia.org/wiki/2007_Ch%C5%ABetsu_offshore_earthquake"}, {"title": "Niigata (city)", "link": "https://wikipedia.org/wiki/Niigata_(city)"}]}, {"year": "2009", "text": "<PERSON><PERSON>, an aide to a politician in Malaysia is found dead on the rooftop of a building adjacent to the offices of the Anti-Corruption Commission, sparking an inquest that gains nationwide attention.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, an aide to a politician in <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> is found dead on the rooftop of a building adjacent to the offices of the <a href=\"https://wikipedia.org/wiki/Malaysian_Anti-Corruption_Commission\" title=\"Malaysian Anti-Corruption Commission\">Anti-Corruption Commission</a>, sparking an <a href=\"https://wikipedia.org/wiki/Inquest\" title=\"Inquest\">inquest</a> that gains nationwide attention.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, an aide to a politician in <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> is found dead on the rooftop of a building adjacent to the offices of the <a href=\"https://wikipedia.org/wiki/Malaysian_Anti-Corruption_Commission\" title=\"Malaysian Anti-Corruption Commission\">Anti-Corruption Commission</a>, sparking an <a href=\"https://wikipedia.org/wiki/Inquest\" title=\"Inquest\">inquest</a> that gains nationwide attention.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}, {"title": "Malaysian Anti-Corruption Commission", "link": "https://wikipedia.org/wiki/Malaysian_Anti-Corruption_Commission"}, {"title": "Inquest", "link": "https://wikipedia.org/wiki/Inquest"}]}, {"year": "2013", "text": "As many as 27 children die and 25 others are hospitalized after eating lunch served at their school in eastern India.", "html": "2013 - As many as 27 children die and 25 others are hospitalized after <a href=\"https://wikipedia.org/wiki/Bihar_school_meal_poisoning_incident\" title=\"Bihar school meal poisoning incident\">eating lunch</a> served at their school in eastern India.", "no_year_html": "As many as 27 children die and 25 others are hospitalized after <a href=\"https://wikipedia.org/wiki/Bihar_school_meal_poisoning_incident\" title=\"Bihar school meal poisoning incident\">eating lunch</a> served at their school in eastern India.", "links": [{"title": "Bihar school meal poisoning incident", "link": "https://wikipedia.org/wiki/Bihar_school_meal_poisoning_incident"}]}, {"year": "2013", "text": "Syrian civil war: The Battle of Ras al-Ayn resumes between the People's Protection Units (YPG) and Islamist forces, beginning the Rojava-Islamist conflict.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Ras_al-Ayn_(2012%E2%80%9313)\" title=\"Battle of Ras al-Ayn (2012-13)\">Battle of Ras al-Ayn</a> resumes between the <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) and Islamist forces, beginning the <a href=\"https://wikipedia.org/wiki/Rojava%E2%80%93Islamist_conflict\" title=\"Rojava-Islamist conflict\">Rojava-Islamist conflict</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Ras_al-Ayn_(2012%E2%80%9313)\" title=\"Battle of Ras al-Ayn (2012-13)\">Battle of Ras al-Ayn</a> resumes between the <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) and Islamist forces, beginning the <a href=\"https://wikipedia.org/wiki/Rojava%E2%80%93Islamist_conflict\" title=\"Rojava-Islamist conflict\">Rojava-Islamist conflict</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Battle of Ras al-Ayn (2012-13)", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>_<PERSON>-<PERSON>yn_(2012%E2%80%9313)"}, {"title": "People's Protection Units", "link": "https://wikipedia.org/wiki/People%27s_Protection_Units"}, {"title": "Rojava-Islamist conflict", "link": "https://wikipedia.org/wiki/Rojava%E2%80%93Islamist_conflict"}]}, {"year": "2015", "text": "Four U.S. Marines and one gunman die in a shooting spree targeting military installations in Chattanooga, Tennessee.", "html": "2015 - Four <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> and one gunman die in <a href=\"https://wikipedia.org/wiki/2015_Chattanooga_shootings\" title=\"2015 Chattanooga shootings\">a shooting spree</a> targeting military installations in <a href=\"https://wikipedia.org/wiki/Chattanooga,_Tennessee\" title=\"Chattanooga, Tennessee\">Chattanooga, Tennessee</a>.", "no_year_html": "Four <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> and one gunman die in <a href=\"https://wikipedia.org/wiki/2015_Chattanooga_shootings\" title=\"2015 Chattanooga shootings\">a shooting spree</a> targeting military installations in <a href=\"https://wikipedia.org/wiki/Chattanooga,_Tennessee\" title=\"Chattanooga, Tennessee\">Chattanooga, Tennessee</a>.", "links": [{"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "2015 Chattanooga shootings", "link": "https://wikipedia.org/wiki/2015_Chattanooga_shootings"}, {"title": "Chattanooga, Tennessee", "link": "https://wikipedia.org/wiki/Chattanooga,_Tennessee"}]}, {"year": "2019", "text": "A 100-year-old building in Mumbai, India, collapses, killing at least 10 people and leaving many others trapped.", "html": "2019 - A 100-year-old building in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a>, India, collapses, killing at least 10 people and leaving many others trapped.", "no_year_html": "A 100-year-old building in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a>, India, collapses, killing at least 10 people and leaving many others trapped.", "links": [{"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}]}], "Births": [{"year": "1194", "text": "<PERSON> of Assisi, Italian nun and saint (d. 1253)", "html": "1194 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Assisi\" title=\"<PERSON> of Assisi\"><PERSON> of Assisi</a>, Italian nun and saint (d. 1253)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Assisi\"><PERSON> of Assisi</a>, Italian nun and saint (d. 1253)", "links": [{"title": "Clare of Assisi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1486", "text": "<PERSON>, Italian painter (d. 1530)", "html": "1486 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1530)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1517", "text": "<PERSON>, Duchess of Suffolk, English duchess (d. 1559)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk\" title=\"<PERSON>, Duchess of Suffolk\"><PERSON>, Duchess of Suffolk</a>, English duchess (d. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk\" title=\"<PERSON>, Duchess of Suffolk\"><PERSON>, Duchess of Suffolk</a>, English duchess (d. 1559)", "links": [{"title": "<PERSON>, Duchess of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Suffolk"}]}, {"year": "1529", "text": "<PERSON><PERSON> the Elder, Dutch jurist, writer on international maritime law (d. 1589)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, Dutch jurist, writer on international maritime law (d. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, Dutch jurist, writer on international maritime law (d. 1589)", "links": [{"title": "<PERSON><PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_the_Elder"}]}, {"year": "1611", "text": "<PERSON> of Austria (d. 1644)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1644)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/Cecilia_Renata_of_Austria"}]}, {"year": "1661", "text": "<PERSON>Iber<PERSON>, Canadian captain, explorer, and politician (d. 1706)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Iberville\" title=\"<PERSON> d'Iberville\"><PERSON>Iber<PERSON></a>, Canadian captain, explorer, and politician (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Iberville\" title=\"<PERSON> d'Iberville\"><PERSON>Iberville</a>, Canadian captain, explorer, and politician (d. 1706)", "links": [{"title": "<PERSON>Iber<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Iberville"}]}, {"year": "1714", "text": "<PERSON>, marquis <PERSON>, French engineer and author (d. 1800)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9,_marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, marquis de Montale<PERSON>\"><PERSON>, marquis de Mont<PERSON></a>, French engineer and author (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9,_marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, marquis de Montale<PERSON>t\"><PERSON>, marquis de <PERSON></a>, French engineer and author (d. 1800)", "links": [{"title": "<PERSON>, marquis de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9,_marquis_<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON>, English sculptor and academic (d. 1803)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1723", "text": "<PERSON>, English painter and academic (d. 1792)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1731", "text": "<PERSON>, American jurist and politician, 18th Governor of Connecticut (d. 1796)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(statesman)\" class=\"mw-redirect\" title=\"<PERSON> (statesman)\"><PERSON></a>, American jurist and politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Connecticut\" class=\"mw-redirect\" title=\"List of Governors of Connecticut\">Governor of Connecticut</a> (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(statesman)\" class=\"mw-redirect\" title=\"<PERSON> (statesman)\"><PERSON></a>, American jurist and politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Connecticut\" class=\"mw-redirect\" title=\"List of Governors of Connecticut\">Governor of Connecticut</a> (d. 1796)", "links": [{"title": "<PERSON> (statesman)", "link": "https://wikipedia.org/wiki/<PERSON>_(statesman)"}, {"title": "List of Governors of Connecticut", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Connecticut"}]}, {"year": "1748", "text": "<PERSON>, American lawyer, judge, and politician, 16th President of the Continental Congress (d. 1810)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 16th <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 16th <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Continental Congress", "link": "https://wikipedia.org/wiki/President_of_the_Continental_Congress"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter and etcher (d. 1875)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French painter and etcher (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French painter and etcher (d. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, American religious leader and author, founded Christian Science (d. 1910)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and author, founded <a href=\"https://wikipedia.org/wiki/Christian_Science\" title=\"Christian Science\">Christian Science</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and author, founded <a href=\"https://wikipedia.org/wiki/Christian_Science\" title=\"Christian Science\">Christian Science</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Christian Science", "link": "https://wikipedia.org/wiki/Christian_Science"}]}, {"year": "1841", "text": "<PERSON>, Estonian-German architect and activist (d. 1923)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German architect and activist (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German architect and activist (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Belgian violinist, composer, and conductor (d. 1931)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Ysa%C3%BFe\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist, composer, and conductor (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Ysa%C3%BFe\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist, composer, and conductor (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Ysa%C3%BFe"}]}, {"year": "1862", "text": "<PERSON>, American journalist and activist (d. 1931)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Ida_B._Wells\" title=\"Ida B. Wells\"><PERSON></a>, American journalist and activist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ida_B._Wells\" title=\"Ida B. Wells\"><PERSON></a>, American journalist and activist (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Australian politician, 14th Premier of Queensland (d. 1910)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1870", "text": "<PERSON>, Irish priest, lexicographer, and scholar (d. 1956)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest, lexicographer, and scholar (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest, lexicographer, and scholar (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON> (suffragette), British suffragette (d. 1921)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(suffragette)\" title=\"<PERSON> (suffragette)\"><PERSON> (suffragette)</a>, British suffragette (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(suffragette)\" title=\"<PERSON> (suffragette)\"><PERSON> (suffragette)</a>, British suffragette (d. 1921)", "links": [{"title": "<PERSON> (suffragette)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(suffragette)"}]}, {"year": "1871", "text": "<PERSON>, American golfer (d. 1906)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1906)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, Norwegian pilot and explorer (d. 1928)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Roald_Am<PERSON>\" title=\"Roald Amundsen\"><PERSON><PERSON><PERSON></a>, Norwegian pilot and explorer (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roa<PERSON>_Am<PERSON>\" title=\"Roald Am<PERSON>en\"><PERSON><PERSON><PERSON></a>, Norwegian pilot and explorer (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roald_Am<PERSON>en"}]}, {"year": "1872", "text": "<PERSON>, Australian politician, 25th Premier of Queensland (d. 1949)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1949)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1880", "text": "<PERSON>, American journalist and author (d. 1966)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, American judge (d. 1937)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American judge (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American judge (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American photographer and painter (d. 1965)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and painter (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and painter (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Russian author (d. 1964)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON> <PERSON>, American baseball player and manager (d. 1951)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Shoe<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American baseball player and manager (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Shoe<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American baseball player and manager (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American actor (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Dutch physicist and academic, Nobel Prize laureate (d. 1966)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Frits_Zernike\" title=\"Frits Zernike\"><PERSON><PERSON> Zernike</a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frits_Zernike\" title=\"Frits Zernike\"><PERSON><PERSON> Zernike</a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1966)", "links": [{"title": "Frits Zernike", "link": "https://wikipedia.org/wiki/Frits_Zernike"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1889", "text": "<PERSON>, American author (d. 1953)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Chrisman\"><PERSON></a>, American author (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian businessman and politician, 35th Mayor of Quebec City (d. 1968)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON><PERSON>\">Wil<PERSON><PERSON></a>, Canadian businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Quebec_City\" title=\"List of mayors of Quebec City\">Mayor of Quebec City</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">Wil<PERSON><PERSON></a>, Canadian businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Quebec_City\" title=\"List of mayors of Quebec City\">Mayor of Quebec City</a> (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>"}, {"title": "List of mayors of Quebec City", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Quebec_City"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, German biologist and eugenicist (d. 1969)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, German biologist and eugenicist (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German biologist and eugenicist (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Norwegian trade union leader and politician, 1st Secretary-General of the United Nations (d. 1968)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"Trygve <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian trade union leader and politician, 1st <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian trade union leader and politician, 1st <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trygve_Lie"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "1898", "text": "<PERSON> <PERSON>, British farmer, educator, and founding figure in the organic movement (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, British farmer, educator, and founding figure in the organic movement (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, British farmer, educator, and founding figure in the organic movement (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Russian psychologist and physician (d. 1977)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian psychologist and physician (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian psychologist and physician (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American actress (d. 1993)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, German lawyer and judge (d. 1968)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and judge (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and judge (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Canadian singer-songwriter (d. 1971)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, German mathematician and engineer (d. 1974)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Irmgard_Fl%C3%BCgge-Lotz\" title=\"<PERSON>rm<PERSON> Flügge-Lotz\"><PERSON><PERSON><PERSON></a>, German mathematician and engineer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irmgard_Fl%C3%BCgge-Lotz\" title=\"<PERSON><PERSON><PERSON> Flügge-Lotz\"><PERSON><PERSON><PERSON></a>, German mathematician and engineer (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>ügge-Lotz", "link": "https://wikipedia.org/wiki/Irmgard_Fl%C3%BCgge-Lotz"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Italian composer and conductor (d. 2003)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and conductor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and conductor (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American actor, director, and screenwriter (d. 2006)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American educator and television host (d. 2001)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and television host (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and television host (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American farmer and businessman, founded Orville Redenbacher's (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Orville_Redenbacher\" title=\"Orville Redenbacher\"><PERSON><PERSON> Redenbacher</a>, American farmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Orville_Redenbacher%27s\" title=\"Orville Redenbacher's\"><PERSON><PERSON> Redenbacher's</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orville_Redenbacher\" title=\"Orville Redenbacher\"><PERSON><PERSON> Redenbacher</a>, American farmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Orville_Redenbacher%27s\" title=\"Orville Redenbacher's\">Orville Redenbacher's</a> (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Or<PERSON>_<PERSON>enbacher"}, {"title": "Orville <PERSON>enbacher's", "link": "https://wikipedia.org/wiki/Orville_Redenbacher%27s"}]}, {"year": "1907", "text": "<PERSON>, American actress (d. 1990)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Australian cricketer (d. 1968)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American historian, author, and academic (d. 1980)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>range"}]}, {"year": "1911", "text": "<PERSON>, American actress, singer, and dancer (d. 1995)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American actor (d. 1970)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American baseball player (d. 2007)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Argentine composer, singer, poet, and teacher (d. 2019)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine composer, singer, poet, and teacher (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine composer, singer, poet, and teacher (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actor (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actress (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English soldier (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, British Royal Air Force (RAF) fighter pilot (d. 2020)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Royal Air Force (RAF) fighter pilot (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Royal Air Force (RAF) fighter pilot (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English biochemist and rugby player (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and rugby player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and rugby player (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Austrian SS officer (d. 1999)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1919", "text": "<PERSON>, South Korean politician, 4th President of South Korea (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hah\" title=\"<PERSON>hah\"><PERSON></a>, South Korean politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>h\" title=\"<PERSON>h\"><PERSON></a>, South Korean politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 2006)", "links": [{"title": "<PERSON>hah", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hah"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, American critic and editor (d. 1990)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American critic and editor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American critic and editor (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American psychologist, theorist, and academic (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, theorist, and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, theorist, and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Brazilian guitarist (d. 1987)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian guitarist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian guitarist (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1924", "text": "<PERSON>, American journalist and politician (d. 2024)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American model, actress, game show panelist, and politician, Miss America 1945 (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, game show panelist, and politician, <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America 1945</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, game show panelist, and politician, <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America 1945</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Miss America", "link": "https://wikipedia.org/wiki/Miss_America"}]}, {"year": "1924", "text": "<PERSON>, Northern Mariana Islander ceramic artist (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Mariana Islander ceramic artist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Mariana Islander ceramic artist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American sergeant and surgeon (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and surgeon (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and surgeon (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Argentine actress (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine actress (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American jazz musician (d. 1982)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Cal_Tjader\" title=\"<PERSON> T<PERSON>der\"><PERSON></a>, American jazz musician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cal_Tjader\" title=\"<PERSON> Tjader\"><PERSON></a>, American jazz musician (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cal_Tjader"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Croatian footballer and manager (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer and manager (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1926", "text": "<PERSON>, American biologist and academic, Nobel Prize laureate (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rose\"><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1927", "text": "<PERSON>, Canadian lawyer and civil servant (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Pierre_F._C%C3%B4t%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and civil servant (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pierre_F._C%C3%B4t%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and civil servant (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_F._C%C3%B4t%C3%A9"}]}, {"year": "1927", "text": "<PERSON>, English author and illustrator (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English footballer (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English novelist and art historian (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and art historian (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and art historian (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Soviet-American pianist", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet-American pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet-American pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American author and screenwriter (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American race car driver (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(race_car_driver)\" class=\"mw-redirect\" title=\"<PERSON> (race car driver)\"><PERSON></a>, American race car driver (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(race_car_driver)\" class=\"mw-redirect\" title=\"<PERSON> (race car driver)\"><PERSON></a>, American race car driver (d. 2011)", "links": [{"title": "<PERSON> (race car driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(race_car_driver)"}]}, {"year": "1928", "text": "<PERSON>, American lawyer and politician, 51st Governor of Louisiana (d. 2009)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Louisiana\" class=\"mw-redirect\" title=\"List of Governors of Louisiana\">Governor of Louisiana</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Louisiana\" class=\"mw-redirect\" title=\"List of Governors of Louisiana\">Governor of Louisiana</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Louisiana", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Louisiana"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer and author (d. 2000)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer and author (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American serial killer (d. 1984)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American author and poet (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and poet (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and poet (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, French swimmer (d. 1981)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French swimmer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French swimmer (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Egyptian-French singer-songwriter (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Guy_B%C3%A9art\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A9art\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guy_B%C3%A9art"}]}, {"year": "1930", "text": "<PERSON>, American lawyer and politician", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American football defensive back and kicker (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football defensive back and kicker (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football defensive back and kicker (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Scottish Roman Catholic priest of the English Dominican Province", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish Roman Catholic priest of the English Dominican Province", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish Roman Catholic priest of the English Dominican Province", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American baseball player, manager, and coach (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rry\" title=\"<PERSON><PERSON> Sherry\"><PERSON><PERSON></a>, American baseball player, manager, and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sherry\"><PERSON><PERSON></a>, American baseball player, manager, and coach (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_She<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English trumpet player and composer (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player and composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player and composer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American football player and sportscaster (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American lawyer and politician, 76th United States Attorney General (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 76th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 76th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1933", "text": "<PERSON>, American businessman", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American singer-songwriter and producer (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Argentine journalist (d. 2010)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Eloy_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine journalist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Eloy_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine journalist (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Eloy_Mart%C3%ADnez"}]}, {"year": "1934", "text": "<PERSON>, 38th Treasurer of the United States", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 38th Treasurer of the United States", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 38th Treasurer of the United States", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American educator and politician (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American general (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American general (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American general (d. 2014)", "links": [{"title": "<PERSON> Mu<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1935", "text": "<PERSON>, American socialite and philanthropist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Japanese politician, 91st Prime Minister of Japan", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 91st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 91st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1936", "text": "<PERSON>, American guitarist (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American sinologist and linguist (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sinologist)\" title=\"<PERSON> (sinologist)\"><PERSON></a>, American sinologist and linguist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sinologist)\" title=\"<PERSON> (sinologist)\"><PERSON></a>, American sinologist and linguist (d. 2012)", "links": [{"title": "<PERSON> (sinologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sinologist)"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian-Australian cricketer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Venkataraman_Subramanya\" title=\"Venkataraman Subramanya\">Venkataraman Subramanya</a>, Indian-Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venkataraman_Subramanya\" title=\"Venkataraman Subramanya\">Venkataraman Subramanya</a>, Indian-Australian cricketer", "links": [{"title": "Venkataraman Subramanya", "link": "https://wikipedia.org/wiki/Venkataraman_Subramanya"}]}, {"year": "1937", "text": "<PERSON>, American lawyer and politician, 25th Governor of Nevada", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_of_Nevada\" class=\"mw-redirect\" title=\"Governor of Nevada\">Governor of Nevada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_of_Nevada\" class=\"mw-redirect\" title=\"Governor of Nevada\">Governor of Nevada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Nevada", "link": "https://wikipedia.org/wiki/Governor_of_Nevada"}]}, {"year": "1937", "text": "<PERSON>, English director, producer, and screenwriter (d. 2008)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, English director, producer, and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, English director, producer, and screenwriter (d. 2008)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>(producer)"}]}, {"year": "1938", "text": "<PERSON>, American author and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English singer and bass player (d. 2003)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer and bass player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer and bass player (d. 2003)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1939", "text": "<PERSON>, Iranian cleric and politician, 2nd Supreme Leader of Iran", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian cleric and politician, 2nd <a href=\"https://wikipedia.org/wiki/Supreme_Leader_of_Iran\" title=\"Supreme Leader of Iran\">Supreme Leader of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian cleric and politician, 2nd <a href=\"https://wikipedia.org/wiki/Supreme_Leader_of_Iran\" title=\"Supreme Leader of Iran\">Supreme Leader of Iran</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Supreme Leader of Iran", "link": "https://wikipedia.org/wiki/Supreme_Leader_of_Iran"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Italian football manager and football player", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Li<PERSON>_<PERSON>\" title=\"Li<PERSON>\"><PERSON><PERSON></a>, Italian football manager and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lido_<PERSON>\" title=\"Li<PERSON>\"><PERSON><PERSON></a>, Italian football manager and football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lido_Vieri"}]}, {"year": "1939", "text": "<PERSON>, president of Liberia (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of Liberia (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of Liberia (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Indian actor and producer (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Shringar_<PERSON>aj\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shringar_<PERSON>aj\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and producer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shri<PERSON><PERSON>_<PERSON>aj"}]}, {"year": "1939", "text": "<PERSON><PERSON>, English actor and activist (d. 2010)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and activist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and activist (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>grave"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Italian singer and conductor (d. 1995)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer and conductor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer and conductor (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>re"}]}, {"year": "1941", "text": "<PERSON>, Jamaican singer-songwriter (d. 2006)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Norwegian author and playwright", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian author and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dag_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Dutch journalist and politician, Deputy Prime Minister of the Netherlands", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands"}]}, {"year": "1941", "text": "Sir <PERSON>, 6th Baronet, English banker and politician, Secretary of State for Transport", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_6th_Baronet\" class=\"mw-redirect\" title=\"Sir <PERSON>, 6th Baronet\">Sir <PERSON>, 6th Baronet</a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_6th_Baronet\" class=\"mw-redirect\" title=\"Sir <PERSON>, 6th Baronet\">Sir <PERSON>, 6th Baronet</a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a>", "links": [{"title": "Sir <PERSON>, 6th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_6th_Baronet"}, {"title": "Secretary of State for Transport", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Transport"}]}, {"year": "1942", "text": "<PERSON>, Australian tennis player and minister", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Margaret <PERSON>\"><PERSON></a>, Australian tennis player and minister", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Margaret <PERSON>\"><PERSON></a>, Australian tennis player and minister", "links": [{"title": "Margaret <PERSON>", "link": "https://wikipedia.org/wiki/Margaret_Court"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Cuban-American author, poet, and playwright (d. 1990)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Reinaldo_Arenas\" title=\"Reinaldo Arenas\"><PERSON><PERSON><PERSON></a>, Cuban-American author, poet, and playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reinaldo_Arenas\" title=\"Reinaldo Arenas\"><PERSON><PERSON><PERSON></a>, Cuban-American author, poet, and playwright (d. 1990)", "links": [{"title": "Reinaldo Arenas", "link": "https://wikipedia.org/wiki/Reinaldo_Arenas"}]}, {"year": "1943", "text": "<PERSON>, English political scientist and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American football player and coach", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, English-Welsh actress and jewellery designer (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Welsh actress and jewellery designer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Welsh actress and jewellery designer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian civil servant and diplomat, Deputy Secretary-General of the United Nations", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9chette\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and diplomat, <a href=\"https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations\" title=\"Deputy Secretary-General of the United Nations\">Deputy Secretary-General of the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9chette\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and diplomat, <a href=\"https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations\" title=\"Deputy Secretary-General of the United Nations\">Deputy Secretary-General of the United Nations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9chette"}, {"title": "Deputy Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations"}]}, {"year": "1947", "text": "<PERSON>, Australian television host and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American businesswoman and politician, 23rd United States Secretary of Labor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American-Cuban criminal and activist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ur\"><PERSON><PERSON><PERSON></a>, American-Cuban criminal and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ur\"><PERSON><PERSON><PERSON></a>, American-Cuban criminal and activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Panamanian singer-songwriter, guitarist, and actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Blades\" title=\"<PERSON><PERSON><PERSON>s\"><PERSON><PERSON><PERSON></a>, Panamanian singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Blades\" title=\"<PERSON><PERSON><PERSON>s\"><PERSON><PERSON><PERSON></a>, Panamanian singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Blades"}]}, {"year": "1948", "text": "<PERSON>, Swedish footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ck\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ck\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ck"}]}, {"year": "1948", "text": "<PERSON>, South African cricketer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Israeli violinist and conductor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli violinist and conductor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American writer, playwright and poet (d. 2024)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Gary_Indiana\" title=\"Gary Indiana\"><PERSON></a>, American writer, playwright and poet (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gary_Indiana\" title=\"Gary Indiana\"><PERSON></a>, American writer, playwright and poet (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gary_Indiana"}]}, {"year": "1950", "text": "<PERSON>, Canadian lawyer and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English darts player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English darts player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English darts player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English historian and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lding"}]}, {"year": "1950", "text": "<PERSON>, American journalist and photographer (d. 2007)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and photographer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and photographer (d. 2007)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Canadian journalist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Malaysian politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Che_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Che R<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Che_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Che R<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian politician", "links": [{"title": "<PERSON><PERSON> Rosli", "link": "https://wikipedia.org/wiki/<PERSON>e_<PERSON><PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American drummer and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, French director and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, South African cricketer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American lawyer and politician, Under Secretary of Defense for Policy", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_Defense_for_Policy\" title=\"Under Secretary of Defense for Policy\">Under Secretary of Defense for Policy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_Defense_for_Policy\" title=\"Under Secretary of Defense for Policy\">Under Secretary of Defense for Policy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Under Secretary of Defense for Policy", "link": "https://wikipedia.org/wiki/Under_Secretary_of_Defense_for_Policy"}]}, {"year": "1955", "text": "<PERSON>, American poet and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Malaysian badminton player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>e_<PERSON>\" title=\"<PERSON> Swee Leon<PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Swee Leong\"><PERSON><PERSON><PERSON></a>, Malaysian badminton player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>g"}]}, {"year": "1956", "text": "<PERSON>, American playwright and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Swiss ichthyologist specializing in Eurasian freshwater fishes", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Swiss_(people)\" class=\"mw-redirect\" title=\"Swiss (people)\">Swiss</a> <a href=\"https://wikipedia.org/wiki/Ichthyology\" title=\"Ichthyology\">ichthyologist</a> specializing in Eurasian freshwater fishes", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Swiss_(people)\" class=\"mw-redirect\" title=\"Swiss (people)\">Swiss</a> <a href=\"https://wikipedia.org/wiki/Ichthyology\" title=\"Ichthyology\">ichthyologist</a> specializing in Eurasian freshwater fishes", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Swiss (people)", "link": "https://wikipedia.org/wiki/Swiss_(people)"}, {"title": "Ichthyology", "link": "https://wikipedia.org/wiki/Ichthyology"}]}, {"year": "1958", "text": "<PERSON>, American politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American-Irish dancer and choreographer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Irish dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Irish dancer and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, South African-American football player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(placekicker)\" title=\"<PERSON> (placekicker)\"><PERSON></a>, South African-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(placekicker)\" title=\"<PERSON> (placekicker)\"><PERSON></a>, South African-American football player", "links": [{"title": "<PERSON> (placekicker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(placekicker)"}]}, {"year": "1959", "text": "<PERSON>, Scottish composer and conductor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Estonian economist and politician, 25th Estonian Minister of Defence", "html": "1959 - <a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Ligi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian economist and politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_<PERSON>gi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian economist and politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_Ligi"}, {"title": "Minister of Defence (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Russian singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>es"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian footballer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Sre%C4%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sre%C4%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sre%C4%8Dko_Katanec"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Swedish tennis player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>fo<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>fo<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nfors"}]}, {"year": "1964", "text": "<PERSON>, American poker player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Spanish cyclist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1in\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1in\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Indur%C3%A1in"}]}, {"year": "1965", "text": "<PERSON>, French sailor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>mme"}]}, {"year": "1967", "text": "<PERSON>, American actor, comedian, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Indian field hockey player and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American football player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American philosopher and businessman, co-founded Wikipedia and Citizendium", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Wikipedia\" title=\"Wikipedia\">Wikipedia</a> and <a href=\"https://wikipedia.org/wiki/Citizendium\" title=\"Citizendium\">Citizendium</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Wikipedia\" title=\"Wikipedia\">Wikipedia</a> and <a href=\"https://wikipedia.org/wiki/Citizendium\" title=\"Citizendium\">Citizendium</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Wikipedia", "link": "https://wikipedia.org/wiki/Wikipedia"}, {"title": "Citizendium", "link": "https://wikipedia.org/wiki/Citizendium"}]}, {"year": "1968", "text": "<PERSON>, Australian rugby league player and businessman", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and businessman", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1968", "text": "<PERSON>, American songwriter and businessman", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Brazilian singer and dancer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>-<PERSON>, Australian netball player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian netball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian netball player and sportscaster", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gli<PERSON>\" title=\"<PERSON><PERSON><PERSON>gli<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gli<PERSON>\" title=\"<PERSON><PERSON><PERSON>glinie<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ks"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Thai director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Apichatpong_Weerasethakul\" title=\"Apichatpong Weerasethakul\"><PERSON><PERSON><PERSON><PERSON><PERSON> Weerasethakul</a>, Thai director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apichatpong_Weerasethakul\" title=\"Apichatpong Weerasethakul\"><PERSON><PERSON><PERSON><PERSON><PERSON> Weerasethakul</a>, Thai director, producer, and screenwriter", "links": [{"title": "Apichatpong Weerasethakul", "link": "https://wikipedia.org/wiki/Apichatpong_Weerasethakul"}]}, {"year": "1970", "text": "<PERSON>, American social psychologist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social psychologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social psychologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American-Canadian football player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian speed skater", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>t"}]}, {"year": "1973", "text": "<PERSON>, Portuguese politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Portuguese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Portuguese politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON><PERSON>_(politician)"}]}, {"year": "1973", "text": "<PERSON>, South African cricketer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American director and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_(filmmaker)"}]}, {"year": "1973", "text": "<PERSON>, American politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)\" title=\"<PERSON> (Ohio politician)\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)\" title=\"<PERSON> (Ohio politician)\"><PERSON></a>, American politician", "links": [{"title": "<PERSON> (Ohio politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Estonian politician, Estonian Minister of Social Affairs", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (Estonia)\">Estonian Minister of Social Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (Estonia)\">Estonian Minister of Social Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mare<PERSON>_<PERSON>"}, {"title": "Minister of Social Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sailor\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sailor\" title=\"<PERSON> Sailor\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Polish racing driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American professional wrestler and mixed martial artist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler and mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Paraguayan footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Belarusian-Israeli tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Israeli tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Northern Ireland-born English soldier, Victoria Cross recipient (d. 2006)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Ireland-born English soldier, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Ireland-born English soldier, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mays"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American sport shooter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sport shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sport shooter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Russian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian golfer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1981", "text": "<PERSON>, Italian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Slovenian ski jumper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rodr%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Rodr%C3%ADguez"}]}, {"year": "1982", "text": "<PERSON>, German cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gre<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Greipel\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>el"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American soccer player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Costa Rican footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a"}]}, {"year": "1983", "text": "<PERSON>, British Indian actress and model", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Indian actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Indian actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Katrina_<PERSON>f"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Japanese racing driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Hungarian decathlete", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Attila_Szab%C3%B3_(athlete)\" title=\"<PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON></a>, Hungarian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attil<PERSON>_Szab%C3%B3_(athlete)\" title=\"<PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON></a>, Hungarian decathlete", "links": [{"title": "<PERSON><PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/Attila_Szab%C3%B3_(athlete)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Krav%C4%8Denko\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Krav%C4%8Denko\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Krav%C4%8Denko"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese actress, singer, and fashion designer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress, singer, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress, singer, and fashion designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Mousa_Demb%C3%A9l%C3%A9_(Belgian_footballer)\" title=\"<PERSON><PERSON> (Belgian footballer)\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Demb%C3%A9l%C3%A9_(Belgian_footballer)\" title=\"<PERSON><PERSON> (Belgian footballer)\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON> (Belgian footballer)", "link": "https://wikipedia.org/wiki/Mousa_Demb%C3%A9l%C3%A9_(Belgian_footballer)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress and producer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Moreno\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Moreno\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Spanish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Welsh footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Bureta_Faraimo\" title=\"Bureta Faraimo\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bureta_Faraimo\" title=\"Bureta Faraimo\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "B<PERSON>ta <PERSON>", "link": "https://wikipedia.org/wiki/Bureta_Faraimo"}]}, {"year": "1990", "text": "<PERSON>,  American actor, singer and dancer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian singer and songwriter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Wizkid\" title=\"Wizkid\">Wizki<PERSON></a>, Nigerian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wizkid\" title=\"Wizkid\">Wizki<PERSON></a>, Nigerian singer and songwriter", "links": [{"title": "Wizkid", "link": "https://wikipedia.org/wiki/Wizkid"}]}, {"year": "1990", "text": "<PERSON>, French motorcycle racer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian Rules footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Rules footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American YouTuber", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Jamaican sprinter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American rapper and singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tract\" title=\"<PERSON> Abstract\"><PERSON></a>, American rapper and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Abstract\"><PERSON></a>, American rapper and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bstract"}]}, {"year": "1996", "text": "<PERSON>, Australian singer and musician", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English actor and presenter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and presenter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American social media personality", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Island_Boys\" title=\"Island Boys\"><PERSON></a>, American social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Island_Boys\" title=\"Island Boys\"><PERSON></a>, American social media personality", "links": [{"title": "Island Boys", "link": "https://wikipedia.org/wiki/Island_Boys"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American social media personality", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Island_Boys\" title=\"Island Boys\"><PERSON><PERSON></a>, American social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Island_Boys\" title=\"Island Boys\"><PERSON><PERSON></a>, American social media personality", "links": [{"title": "Island Boys", "link": "https://wikipedia.org/wiki/Island_Boys"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American actress and model", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "784", "text": "<PERSON><PERSON><PERSON>, Frankish diplomat and saint (b. 710)", "html": "784 - <a href=\"https://wikipedia.org/wiki/Fulrad\" title=\"Fulrad\"><PERSON><PERSON><PERSON></a>, Frankish diplomat and saint (b. 710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fulrad\" title=\"Fulrad\"><PERSON><PERSON><PERSON></a>, Frankish diplomat and saint (b. 710)", "links": [{"title": "Fulrad", "link": "https://wikipedia.org/wiki/Fulrad"}]}, {"year": "851", "text": "<PERSON><PERSON><PERSON><PERSON>, Cordoban deacon and martyr (b. c. 825)", "html": "851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Beja\" title=\"<PERSON><PERSON><PERSON><PERSON> of Beja\"><PERSON><PERSON><PERSON><PERSON></a>, Cordoban deacon and martyr (b. c. 825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Beja\" title=\"<PERSON><PERSON><PERSON><PERSON> of Beja\"><PERSON><PERSON><PERSON><PERSON></a>, Cordoban deacon and martyr (b. c. 825)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Beja", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>and<PERSON>_of_Beja"}]}, {"year": "866", "text": "<PERSON><PERSON><PERSON>, Frankish abbess", "html": "866 - <a href=\"https://wikipedia.org/wiki/Irm<PERSON>_of_Chiemsee\" title=\"Irm<PERSON> of Chiemsee\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Abbess\" title=\"Abbess\">abbess</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irm<PERSON>_of_Chiemsee\" title=\"Irm<PERSON> of Chiemsee\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Abbess\" title=\"Abbess\">abbess</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Chiemsee", "link": "https://wikipedia.org/wiki/Irm<PERSON>_of_Chiemsee"}, {"title": "Abbess", "link": "https://wikipedia.org/wiki/Abbess"}]}, {"year": "1212", "text": "<PERSON>, 3rd Lord of Annandale", "html": "1212 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Lord_of_Annandale\" title=\"<PERSON>, 3rd Lord of Annandale\"><PERSON>, 3rd Lord of Annandale</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Lord_of_Annandale\" title=\"<PERSON>, 3rd Lord of Annandale\"><PERSON>, 3rd Lord of Annandale</a>", "links": [{"title": "<PERSON>, 3rd Lord of Annandale", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Lord_of_Annandale"}]}, {"year": "1216", "text": "<PERSON> (b. 1160)", "html": "1216 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_III\" title=\"Pope Innocent III\">Pope <PERSON> III</a> (b. 1160)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_III\" title=\"Pope Innocent III\">Pope <PERSON> III</a> (b. 1160)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_<PERSON>"}]}, {"year": "1324", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (b. 1267)", "html": "1324 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>da\" title=\"Emperor <PERSON>-Uda\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1267)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>da\" title=\"Emperor <PERSON>-Uda\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1267)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1342", "text": "<PERSON> of Hungary (b. 1288)", "html": "1342 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (b. 1288)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (b. 1288)", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary"}]}, {"year": "1344", "text": "<PERSON><PERSON><PERSON><PERSON>, Sultan of Egypt (b. 1316)", "html": "1344 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>,_Sultan_of_Egypt\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Sultan of Egypt\"><PERSON><PERSON><PERSON><PERSON>, Sultan of Egypt</a> (b. 1316)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>,_Sultan_of_Egypt\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Sultan of Egypt\"><PERSON><PERSON><PERSON><PERSON>, Sultan of Egypt</a> (b. 1316)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Sultan of Egypt", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>,_Sultan_of_Egypt"}]}, {"year": "1509", "text": "<PERSON>, Portuguese explorer (b. 1460)", "html": "1509 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_da_Nova\" title=\"João da Nova\"><PERSON></a>, Portuguese explorer (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_da_Nova\" title=\"João da Nova\"><PERSON></a>, Portuguese explorer (b. 1460)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_da_Nova"}]}, {"year": "1546", "text": "<PERSON>, English author and poet (b. 1520)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1520)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1557", "text": "<PERSON> Cleves, Queen consort of England (b. 1515)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Cleves\"><PERSON> Cleves</a>, Queen consort of England (b. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cleves\"><PERSON>leves</a>, Queen consort of England (b. 1515)", "links": [{"title": "<PERSON> Cleves", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1576", "text": "<PERSON>, Italian noble (b. 1542)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noble (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noble (b. 1542)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_Medici"}]}, {"year": "1647", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian rebel (b. 1622)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian rebel (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian rebel (b. 1622)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1664", "text": "<PERSON>, German poet and playwright (b. 1616)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (b. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_G<PERSON>us"}]}, {"year": "1686", "text": "<PERSON>, English bishop and scholar (b. 1612)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and scholar (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and scholar (b. 1612)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1691", "text": "<PERSON><PERSON><PERSON>, <PERSON>, French politician, French Secretary of State for War (b. 1641)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON>, <PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Secretary of State for War</a> (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON>, <PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Secretary of State for War</a> (b. 1641)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1729", "text": "<PERSON>, German composer and theorist (b. 1683)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (b. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON>, Italian painter (b. 1665)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1665)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, English painter and academic (b. 1726)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francis_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, English field marshal and politician (b. 1718)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English field marshal and politician (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English field marshal and politician (b. 1718)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1831", "text": "<PERSON>, French-Russian general (b. 1763)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Russian general (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Russian general (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, African-American missionary for the African Methodist Episcopal Church (b. 1764)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, African-American missionary for the <a href=\"https://wikipedia.org/wiki/African_Methodist_Episcopal_Church\" title=\"African Methodist Episcopal Church\">African Methodist Episcopal Church</a> (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, African-American missionary for the <a href=\"https://wikipedia.org/wiki/African_Methodist_Episcopal_Church\" title=\"African Methodist Episcopal Church\">African Methodist Episcopal Church</a> (b. 1764)", "links": [{"title": "<PERSON> (missionary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(missionary)"}, {"title": "African Methodist Episcopal Church", "link": "https://wikipedia.org/wiki/African_Methodist_Episcopal_Church"}]}, {"year": "1868", "text": "<PERSON>, Russian author and critic (b. 1840)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and critic (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and critic (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Scottish-Australian politician, 3rd Chief Secretary of New South Wales (b. 1800)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Secretary_of_New_South_Wales\" title=\"Chief Secretary of New South Wales\">Chief Secretary of New South Wales</a> (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Secretary_of_New_South_Wales\" title=\"Chief Secretary of New South Wales\">Chief Secretary of New South Wales</a> (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Secretary of New South Wales", "link": "https://wikipedia.org/wiki/Chief_Secretary_of_New_South_Wales"}]}, {"year": "1882", "text": "<PERSON>, First Lady of the United States 1861-1865 (b. 1818)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> 1861-1865 (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> 1861-1865 (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Spanish poet (b. 1837)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Rosal%C3%AD<PERSON>_de_<PERSON>\" title=\"<PERSON><PERSON><PERSON> de <PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish poet (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rosal%C3%AD<PERSON>_de_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish poet (b. 1837)", "links": [{"title": "Rosalía de Castro", "link": "https://wikipedia.org/wiki/Rosal%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American journalist and author (b. 1823)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Ned_Buntline\" title=\"Ned Buntline\"><PERSON></a>, American journalist and author (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Buntline\" title=\"Ned Buntline\"><PERSON></a>, American journalist and author (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ned_Buntline"}]}, {"year": "1896", "text": "<PERSON>, French critic and publisher, founded Académie Goncourt (b. 1822)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French critic and publisher, founded <a href=\"https://wikipedia.org/wiki/Acad%C3%A9<PERSON>_<PERSON>\" title=\"Académie Goncourt\">Académie Goncourt</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French critic and publisher, founded <a href=\"https://wikipedia.org/wiki/Acad%C3%A9<PERSON>_<PERSON>\" title=\"Académie Goncourt\">Académie Goncourt</a> (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Académie Goncourt", "link": "https://wikipedia.org/wiki/Acad%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American author and co-founder of the Seventh-day Adventist Church(b. 1827)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and co-founder of the <a href=\"https://wikipedia.org/wiki/Seventh-day_Adventist_Church\" title=\"Seventh-day Adventist Church\">Seventh-day Adventist Church</a>(b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and co-founder of the <a href=\"https://wikipedia.org/wiki/Seventh-day_Adventist_Church\" title=\"Seventh-day Adventist Church\">Seventh-day Adventist Church</a>(b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Seventh-day Adventist Church", "link": "https://wikipedia.org/wiki/Seventh-day_Adventist_Church"}]}, {"year": "1917", "text": "<PERSON>, German composer and educator (b. 1847)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Chinese filmmaker (b. 1889)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese filmmaker (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese filmmaker (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch swimmer (b. 1866)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Bartholomeus_Roodenburch\" title=\"Bartholomeus Roodenburch\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch swimmer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartholomeus_Roodenburch\" title=\"Bartholomeus Roodenburch\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch swimmer (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartholomeus_Roodenburch"}]}, {"year": "1943", "text": "<PERSON>, Polish Jewish lawyer, journalist, publicist and Zionist activist (b. 1870)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish Jewish lawyer, journalist, publicist and Zionist activist (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish Jewish lawyer, journalist, publicist and Zionist activist (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian poet and playwright (b. 1866)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)\" title=\"<PERSON><PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON><PERSON></a>, Russian poet and playwright (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)\" title=\"<PERSON><PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON><PERSON></a>, Russian poet and playwright (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, French-born British writer and historian (b. 1870)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-born British writer and historian (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-born British writer and historian (b. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, German soldier, trombonist, and composer (b. 1888)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Her<PERSON>el\"><PERSON><PERSON></a>, German soldier, trombonist, and composer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Her<PERSON>el\"><PERSON><PERSON></a>, German soldier, trombonist, and composer (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Her<PERSON>_<PERSON>el"}]}, {"year": "1960", "text": "<PERSON>, German field marshal (b. 1881)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author (b. 1893)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Turkish colonel and politician, Prime Minister of Turkey (b. 1881)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish colonel and politician, Prime Minister of Turkey (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish colonel and politician, Prime Minister of Turkey (b. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Ukrainian-American illustrator (b. 1899)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American illustrator (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American illustrator (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English-born Scottish race car driver and 6th Baronet <PERSON> (b. 1930)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Scottish race car driver and 6th Baronet <PERSON> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Scottish race car driver and 6th Baronet <PERSON> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1942)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, South African lawyer and politician, 1st State President of South Africa (b. 1894)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}]}, {"year": "1985", "text": "<PERSON>, German novelist and short story writer, Nobel Prize laureate (b. 1917)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ll\" title=\"<PERSON>\"><PERSON></a>, German novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ll\" title=\"<PERSON>\"><PERSON></a>, German novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heinrich_B%C3%B6ll"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1985", "text": "<PERSON>, American saxophonist, songwriter, and bandleader (b. 1901)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and bandleader (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and bandleader (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Austrian conductor and manager (b. 1908)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and manager (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and manager (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Irish educator (b. 1927)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educationalist)\" title=\"<PERSON> (educationalist)\"><PERSON></a>, Irish educator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educationalist)\" title=\"<PERSON> (educationalist)\"><PERSON></a>, Irish educator (b. 1927)", "links": [{"title": "<PERSON> (educationalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(educationalist)"}]}, {"year": "1990", "text": "<PERSON>, Spanish footballer and manager (b. 1922)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_Mu%C3%B1oz"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Dutch-American soldier and author (b. 1906)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American soldier and author (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American soldier and author (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>t_DeJong"}]}, {"year": "1991", "text": "<PERSON>, American painter and academic (b. 1915)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American police officer and politician, 93rd Mayor of Philadelphia (b. 1920)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician, 93rd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Philadelphia\" class=\"mw-redirect\" title=\"List of mayors of Philadelphia\">Mayor of Philadelphia</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician, 93rd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Philadelphia\" class=\"mw-redirect\" title=\"List of mayors of Philadelphia\">Mayor of Philadelphia</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Philadelphia", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Philadelphia"}]}, {"year": "1992", "text": "<PERSON>, American football player and coach (b. 1940)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1918)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1995", "text": "<PERSON>, American playwright and novelist (b. 1912)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/May_<PERSON>\" title=\"May <PERSON>\"><PERSON></a>, American playwright and novelist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_<PERSON>\" title=\"May <PERSON>\"><PERSON></a>, American playwright and novelist (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/May_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English author and poet (b. 1909)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, German lieutenant and politician (b. 1921)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American historian and scholar (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American lawyer and publisher (b. 1960)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American lawyer and publisher (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American lawyer and publisher (b. 1960)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1999", "text": "<PERSON>-<PERSON>, American publicist and wife of <PERSON> (b. 1966)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist and wife of <PERSON> (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist and wife of <PERSON> (b. 1966)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian lawyer and politician, Speaker of the Canadian House of Commons (b. 1903)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(Canada)\" title=\"Speaker of the House of Commons (Canada)\">Speaker of the Canadian House of Commons</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(Canada)\" title=\"Speaker of the House of Commons (Canada)\">Speaker of the Canadian House of Commons</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Speaker of the House of Commons (Canada)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(Canada)"}]}, {"year": "2001", "text": "<PERSON>, Belgian cartoonist (b. 1923)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Belgian cartoonist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Belgian cartoonist (b. 1923)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "2002", "text": "<PERSON>, American computer scientist and engineer (b. 1925)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and engineer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and engineer (b. 1925)", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}]}, {"year": "2003", "text": "<PERSON>, Cuban-American singer and actress (b. 1925)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer and actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer and actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American-Canadian novelist and short story writer (b. 1935)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian novelist and short story writer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian novelist and short story writer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American lawyer and politician, 77th Governor of Georgia (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 77th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 77th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "2004", "text": "<PERSON>, American general and pilot (b. 1919)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Italian sculptor (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pietro_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Luxembourgian singer-songwriter and radio host (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON> Felgen\"><PERSON><PERSON></a>, Luxembourgian singer-songwriter and radio host (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cam<PERSON>_Felgen\" title=\"<PERSON><PERSON> Felgen\"><PERSON><PERSON></a>, Luxembourgian singer-songwriter and radio host (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American businessman and politician, 13th Lieutenant Governor of Arkansas (b. 1948)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Win<PERSON>\"><PERSON><PERSON></a>, American businessman and politician, 13th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Arkansas\" title=\"Lieutenant Governor of Arkansas\">Lieutenant Governor of Arkansas</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Win<PERSON>\"><PERSON><PERSON></a>, American businessman and politician, 13th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Arkansas\" title=\"Lieutenant Governor of Arkansas\">Lieutenant Governor of Arkansas</a> (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Arkansas", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Arkansas"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Italian singer and historian (b. 1943)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer and historian (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer and historian (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>o"}]}, {"year": "2008", "text": "<PERSON>, American singer (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Australian politician, 40th Premier of Victoria (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "2011", "text": "<PERSON>, American football player (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blue\"><PERSON></a>, American football player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blue\"><PERSON></a>, American football player (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American director, producer, and screenwriter (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American businessman and author (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American businessman and politician (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Brazilian bassist, pianist, and composer (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian bassist, pianist, and composer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian bassist, pianist, and composer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese businessman (b. 1913)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese businessman (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese businessman (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wells\"><PERSON></a>, American singer-songwriter and guitarist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American internet celebrity (b. 1999)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American internet celebrity (b. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American internet celebrity (b. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Talia_Castellano"}]}, {"year": "2013", "text": "<PERSON>, Canadian painter and academic (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alex <PERSON>\"><PERSON></a>, Canadian painter and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alex <PERSON>\"><PERSON></a>, Canadian painter and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American baseball player (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Marv_<PERSON>tt\" title=\"Marv Rotblatt\"><PERSON><PERSON></a>, American baseball player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marv_<PERSON>tt\" title=\"Marv Rotblatt\"><PERSON><PERSON></a>, American baseball player (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marv_R<PERSON>tt"}]}, {"year": "2014", "text": "<PERSON>, German businessman, co-founded <PERSON><PERSON> (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>di\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman, co-founded <a href=\"https://wikipedia.org/wiki/Aldi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldi"}]}, {"year": "2014", "text": "<PERSON>, American educator and politician (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Winter\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Winter\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Austrian computer scientist and academic (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian computer scientist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian computer scientist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English soldier, engineer, and author (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, engineer, and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, engineer, and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English chemist and academic (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer and manager (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Alcides_Ghiggia\" title=\"Alcides Ghiggia\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer and manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alcid<PERSON>_<PERSON>hi<PERSON>\" title=\"Alcid<PERSON> Ghiggia\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer and manager (b. 1926)", "links": [{"title": "Al<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alcides_Ghiggia"}]}, {"year": "2015", "text": "<PERSON>, English anthropologist, author, and academic (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anthropologist, author, and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anthropologist, author, and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American filmmaker (b. 1940)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American lawyer and jurist, Associate Justice of the Supreme Court of the United States (b. 1920)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, Associate Justice of the Supreme Court of the United States (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, Associate Justice of the Supreme Court of the United States (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Cuban baseball player (b. 1935)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Cuban baseball player (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Cuban baseball player (b. 1935)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2021", "text": "<PERSON><PERSON>, American rapper (b. 1964)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American rapper (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON>_<PERSON>ie"}]}, {"year": "2023", "text": "<PERSON>, American hacker (b. 1963)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hacker (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hacker (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American basketball player (b. 1954)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, New Zealand rugby union player (b. 1968)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby union player (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby union player (b. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian radio host and sportscaster (b. 1953)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(commentator)\" title=\"<PERSON> (commentator)\"><PERSON></a>, Australian radio host and sportscaster (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(commentator)\" title=\"<PERSON> (commentator)\"><PERSON></a>, Australian radio host and sportscaster (b. 1953)", "links": [{"title": "<PERSON> (commentator)", "link": "https://wikipedia.org/wiki/<PERSON>_(commentator)"}]}]}}