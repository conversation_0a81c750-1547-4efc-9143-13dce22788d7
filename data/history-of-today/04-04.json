{"date": "April 4", "url": "https://wikipedia.org/wiki/April_4", "data": {"Events": [{"year": "503 BC", "text": "Roman consul <PERSON><PERSON><PERSON><PERSON> celebrates a triumph for a military victory over the Sabines.", "html": "503 BC - 503 BC - <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">Roman consul</a> <a href=\"https://wikipedia.org/wiki/Ag<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(consul_503_BC)\" title=\"Ag<PERSON><PERSON> (consul 503 BC)\">Agrippa <PERSON></a> celebrates a <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> for a military victory over the <a href=\"https://wikipedia.org/wiki/Sabines\" title=\"Sabines\">Sabines</a>.", "no_year_html": "503 BC - <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">Roman consul</a> <a href=\"https://wikipedia.org/wiki/Ag<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(consul_503_BC)\" title=\"Ag<PERSON><PERSON> (consul 503 BC)\">Agrippa <PERSON>us <PERSON></a> celebrates a <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> for a military victory over the <a href=\"https://wikipedia.org/wiki/Sabines\" title=\"Sabines\">Sabines</a>.", "links": [{"title": "Roman consul", "link": "https://wikipedia.org/wiki/Roman_consul"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (consul 503 BC)", "link": "https://wikipedia.org/wiki/Ag<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(consul_503_BC)"}, {"title": "Roman triumph", "link": "https://wikipedia.org/wiki/Roman_triumph"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sabines"}]}, {"year": "190", "text": "<PERSON> has his troops evacuate the capital Luoyang and burn it to the ground.", "html": "190 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> has his troops evacuate the capital <a href=\"https://wikipedia.org/wiki/Luoyang\" title=\"Luoyang\">Luoyang</a> and burn it to the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> has his troops evacuate the capital <a href=\"https://wikipedia.org/wiki/Luoyang\" title=\"Luoyang\">Luoyang</a> and burn it to the ground.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luoyang"}]}, {"year": "611", "text": "Maya king <PERSON><PERSON> of Calakmul sacks rival city-state Palenque in southern Mexico.", "html": "611 - Maya king <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Serpent\" title=\"Scroll Serpent\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Calakmul\" title=\"Calakmul\">Calakmul</a> sacks rival city-state <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palenque\">Pa<PERSON><PERSON></a> in southern Mexico.", "no_year_html": "Maya king <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Serpent\" title=\"<PERSON><PERSON> Serpent\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Calakmul\" title=\"Calakmul\">Calakmul</a> sacks rival city-state <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palenque\"><PERSON><PERSON><PERSON></a> in southern Mexico.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Scroll_Serpent"}, {"title": "Calakmul", "link": "https://wikipedia.org/wiki/Calakmul"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palenque"}]}, {"year": "619", "text": "The Bijapur-Mumbai inscription is issued by <PERSON><PERSON><PERSON><PERSON> II, describing the Battle of Narmada.: 207 ", "html": "619 - The Bijapur-Mumbai inscription is issued by <a href=\"https://wikipedia.org/wiki/Pulakeshin_II\" title=\"Pulakeshin II\">Pulakeshin II</a>, describing the <a href=\"https://wikipedia.org/wiki/Battle_of_Narmada\" title=\"Battle of Narmada\">Battle of Narmada</a>.", "no_year_html": "The Bijapur-Mumbai inscription is issued by <a href=\"https://wikipedia.org/wiki/Pulakeshin_II\" title=\"Pulakeshin II\">Pulakeshin II</a>, describing the <a href=\"https://wikipedia.org/wiki/Battle_of_Narmada\" title=\"Battle of Narmada\">Battle of Narmada</a>.", "links": [{"title": "Pulakeshin II", "link": "https://wikipedia.org/wiki/Pulakeshin_II"}, {"title": "Battle of Narmada", "link": "https://wikipedia.org/wiki/Battle_of_Narmada"}]}, {"year": "801", "text": "King <PERSON> the Pious captures Barcelona from the Moors after a siege of several months.", "html": "801 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pi<PERSON>\" title=\"<PERSON> Pious\"><PERSON> the Pi<PERSON></a> captures <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a> from the <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moors</a> after <a href=\"https://wikipedia.org/wiki/Siege_of_Barcelona_(801)\" title=\"Siege of Barcelona (801)\">a siege</a> of several months.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Pious\"><PERSON> the Pi<PERSON></a> captures <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a> from the <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moors</a> after <a href=\"https://wikipedia.org/wiki/Siege_of_Barcelona_(801)\" title=\"Siege of Barcelona (801)\">a siege</a> of several months.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Barcelona", "link": "https://wikipedia.org/wiki/Barcelona"}, {"title": "Moors", "link": "https://wikipedia.org/wiki/Moors"}, {"title": "Siege of Barcelona (801)", "link": "https://wikipedia.org/wiki/Siege_of_Barcelona_(801)"}]}, {"year": "1268", "text": "A five-year Byzantine-Venetian peace treaty is concluded between Venetian envoys and Emperor <PERSON>.", "html": "1268 - A <a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Venetian_treaty_of_1268\" title=\"Byzantine-Venetian treaty of 1268\">five-year Byzantine-Venetian peace treaty</a> is concluded between Venetian envoys and Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Venetian_treaty_of_1268\" title=\"Byzantine-Venetian treaty of 1268\">five-year Byzantine-Venetian peace treaty</a> is concluded between Venetian envoys and Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Michael VIII <PERSON>\"><PERSON></a>.", "links": [{"title": "Byzantine-Venetian treaty of 1268", "link": "https://wikipedia.org/wiki/Byzantine%E2%80%93Venetian_treaty_of_1268"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1423", "text": "Death of the Venetian Dog<PERSON><PERSON>, under whose rule victories were achieved against the Kingdom of Hungary and against the Ottoman Empire at the Battle of Gallipoli (1416).", "html": "1423 - Death of the Venetian <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(title)\" title=\"<PERSON><PERSON> (title)\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, under whose rule victories were achieved against the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Kingdom of Hungary</a> and against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Gallipoli_(1416)\" title=\"Battle of Gallipoli (1416)\">Battle of Gallipoli (1416)</a>.", "no_year_html": "Death of the Venetian <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(title)\" title=\"<PERSON><PERSON> (title)\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Tom<PERSON><PERSON>_<PERSON>go\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, under whose rule victories were achieved against the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Kingdom of Hungary</a> and against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Gallipoli_(1416)\" title=\"Battle of Gallipoli (1416)\">Battle of Gallipoli (1416)</a>.", "links": [{"title": "<PERSON><PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(title)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>go"}, {"title": "Kingdom of Hungary", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Battle of Gallipoli (1416)", "link": "https://wikipedia.org/wiki/Battle_of_Gallipoli_(1416)"}]}, {"year": "1581", "text": "<PERSON> is knighted by Queen <PERSON> for completing a circumnavigation of the world.", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is knighted by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a> for completing a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation\" title=\"<PERSON>'s circumnavigation\">circumnavigation</a> of the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is knighted by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a> for completing a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation\" title=\"<PERSON>'s circumnavigation\">circumnavigation</a> of the world.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s circumnavigation", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation"}]}, {"year": "1609", "text": "Moriscos are expelled from the Kingdom of Valencia.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Moriscos\" class=\"mw-redirect\" title=\"Moriscos\">Moriscos</a> are expelled from the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Valencia\" title=\"Kingdom of Valencia\">Kingdom of Valencia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moriscos\" class=\"mw-redirect\" title=\"Moriscos\">Moriscos</a> are expelled from the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Valencia\" title=\"Kingdom of Valencia\">Kingdom of Valencia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moriscos"}, {"title": "Kingdom of Valencia", "link": "https://wikipedia.org/wiki/Kingdom_of_Valencia"}]}, {"year": "1660", "text": "Declaration of Breda by King <PERSON> of Great Britain promises, among other things, a general pardon to all royalists and opponents of the monarchy for crimes committed during the English Civil War and the Interregnum.", "html": "1660 - <a href=\"https://wikipedia.org/wiki/Declaration_of_Breda\" title=\"Declaration of Breda\">Declaration of Breda</a> by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" class=\"mw-redirect\" title=\"<PERSON> II of Great Britain\"><PERSON> of Great Britain</a> promises, among other things, a general pardon to all royalists and opponents of the monarchy for crimes committed during the <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a> and the <a href=\"https://wikipedia.org/wiki/Interregnum_(England)\" title=\"Interregnum (England)\">Interregnum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Declaration_of_Breda\" title=\"Declaration of Breda\">Declaration of Breda</a> by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" class=\"mw-redirect\" title=\"<PERSON> II of Great Britain\"><PERSON> of Great Britain</a> promises, among other things, a general pardon to all royalists and opponents of the monarchy for crimes committed during the <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a> and the <a href=\"https://wikipedia.org/wiki/Interregnum_(England)\" title=\"Interregnum (England)\">Interregnum</a>.", "links": [{"title": "Declaration of Breda", "link": "https://wikipedia.org/wiki/Declaration_of_Breda"}, {"title": "<PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain"}, {"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}, {"title": "Interregnum (England)", "link": "https://wikipedia.org/wiki/Interregnum_(England)"}]}, {"year": "1796", "text": "<PERSON> delivers the first paleontological lecture.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the first <a href=\"https://wikipedia.org/wiki/Paleontology\" title=\"Paleontology\">paleontological</a> lecture.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the first <a href=\"https://wikipedia.org/wiki/Paleontology\" title=\"Paleontology\">paleontological</a> lecture.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Paleontology", "link": "https://wikipedia.org/wiki/Paleontology"}]}, {"year": "1814", "text": "<PERSON> abdicates (conditionally) for the first time and names his son <PERSON> as Emperor of the French, followed by unconditional abdication two days later.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Napoleon\"><PERSON></a> abdicates (conditionally) for the first time and names his son <a href=\"https://wikipedia.org/wiki/Napoleon_II\" title=\"Napoleon II\">Napoleon II</a> as <a href=\"https://wikipedia.org/wiki/Emperor_of_the_French\" title=\"Emperor of the French\">Emperor of the French</a>, followed by unconditional abdication two days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> abdicates (conditionally) for the first time and names his son <a href=\"https://wikipedia.org/wiki/Napoleon_II\" title=\"Napoleon II\">Napoleon II</a> as <a href=\"https://wikipedia.org/wiki/Emperor_of_the_French\" title=\"Emperor of the French\">Emperor of the French</a>, followed by unconditional abdication two days later.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Napoleon II", "link": "https://wikipedia.org/wiki/Napoleon_II"}, {"title": "Emperor of the French", "link": "https://wikipedia.org/wiki/Emperor_of_the_French"}]}, {"year": "1818", "text": "The United States Congress, affirming the Second Continental Congress, adopts the flag of the United States with 13 red and white stripes and one star for each state (20 at that time).", "html": "1818 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, affirming the <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a>, adopts the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">flag of the United States</a> with 13 red and white stripes and one star for each state (20 at that time).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, affirming the <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a>, adopts the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">flag of the United States</a> with 13 red and white stripes and one star for each state (20 at that time).", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Second Continental Congress", "link": "https://wikipedia.org/wiki/Second_Continental_Congress"}, {"title": "Flag of the United States", "link": "https://wikipedia.org/wiki/Flag_of_the_United_States"}]}, {"year": "1841", "text": "<PERSON> dies of pneumonia, becoming the first President of the United States to die in office, and setting the record for the briefest administration. Vice President <PERSON> succeeds <PERSON> as President.", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies of <a href=\"https://wikipedia.org/wiki/Pneumonia\" title=\"Pneumonia\">pneumonia</a>, becoming the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> to die in office, and setting the record for the briefest administration. Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> succeeds <PERSON> as President.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies of <a href=\"https://wikipedia.org/wiki/Pneumonia\" title=\"Pneumonia\">pneumonia</a>, becoming the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> to die in office, and setting the record for the briefest administration. Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> succeeds <PERSON> as President.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pneumonia", "link": "https://wikipedia.org/wiki/Pneumonia"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "The declaration on the introduction of the Finnish markka as an official currency is read in different parts of the Grand Duchy of Finland.", "html": "1860 - The declaration on the introduction of the <a href=\"https://wikipedia.org/wiki/Finnish_markka\" title=\"Finnish markka\">Finnish markka</a> as an official currency is read in different parts of the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>.", "no_year_html": "The declaration on the introduction of the <a href=\"https://wikipedia.org/wiki/Finnish_markka\" title=\"Finnish markka\">Finnish markka</a> as an official currency is read in different parts of the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>.", "links": [{"title": "Finnish markka", "link": "https://wikipedia.org/wiki/Finnish_markka"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}]}, {"year": "1865", "text": "American Civil War: A day after Union forces capture Richmond, Virginia, U.S. President <PERSON> visits the Confederate capital.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A day after <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces capture <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> visits the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> capital.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A day after <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces capture <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham <PERSON>\"><PERSON></a> visits the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> capital.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1866", "text": "<PERSON> of Russia narrowly escapes an assassination attempt by <PERSON> in the city of Saint Petersburg.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> narrowly escapes an assassination attempt by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the city of <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> narrowly escapes an assassination attempt by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the city of <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "1887", "text": "Argonia, Kansas elects <PERSON><PERSON> as the first female mayor in the United States.", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Argonia,_Kansas\" title=\"Argonia, Kansas\">Argonia, Kansas</a> elects <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as the first female mayor in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Argonia,_Kansas\" title=\"Argonia, Kansas\">Argonia, Kansas</a> elects <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as the first female mayor in the United States.", "links": [{"title": "Argonia, Kansas", "link": "https://wikipedia.org/wiki/Argonia,_Kansas"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "Two Ms  ~7.1 earthquakes, among the largest in Europe, strikes Bulgaria, killing over 200 people and causing destruction.", "html": "1904 - Two M<sub>s</sub>  <a href=\"https://wikipedia.org/wiki/1904_Kresna_earthquakes\" title=\"1904 Kresna earthquakes\">~7.1 earthquakes</a>, among the largest in Europe, strikes Bulgaria, killing over 200 people and causing destruction.", "no_year_html": "Two M<sub>s</sub>  <a href=\"https://wikipedia.org/wiki/1904_Kresna_earthquakes\" title=\"1904 Kresna earthquakes\">~7.1 earthquakes</a>, among the largest in Europe, strikes Bulgaria, killing over 200 people and causing destruction.", "links": [{"title": "1904 Kresna earthquakes", "link": "https://wikipedia.org/wiki/1904_Kresna_earthquakes"}]}, {"year": "1905", "text": "In India, an earthquake hits the Kangra Valley, killing 20,000, and destroying most buildings in Kangra, McLeod Ganj and Dharamshala.", "html": "1905 - In India, <a href=\"https://wikipedia.org/wiki/1905_Kangra_earthquake\" title=\"1905 Kangra earthquake\">an earthquake</a> hits the <a href=\"https://wikipedia.org/wiki/Kangra_Valley\" title=\"Kangra Valley\">Kangra Valley</a>, killing 20,000, and destroying most buildings in <a href=\"https://wikipedia.org/wiki/Kangra,_Himachal_Pradesh\" title=\"Kangra, Himachal Pradesh\">Kangra</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ganj\" title=\"<PERSON><PERSON><PERSON>anj\"><PERSON><PERSON><PERSON>anj</a> and <a href=\"https://wikipedia.org/wiki/Dharamshala\" title=\"Dharamshala\">Dharamshala</a>.", "no_year_html": "In India, <a href=\"https://wikipedia.org/wiki/1905_Kangra_earthquake\" title=\"1905 Kangra earthquake\">an earthquake</a> hits the <a href=\"https://wikipedia.org/wiki/Kangra_Valley\" title=\"Kangra Valley\">Kangra Valley</a>, killing 20,000, and destroying most buildings in <a href=\"https://wikipedia.org/wiki/Kangra,_Himachal_Pradesh\" title=\"Kangra, Himachal Pradesh\">Kangra</a>, <a href=\"https://wikipedia.org/wiki/Mc<PERSON>_Ganj\" title=\"<PERSON><PERSON><PERSON> Ganj\"><PERSON><PERSON><PERSON>anj</a> and <a href=\"https://wikipedia.org/wiki/Dharamshala\" title=\"Dharamshala\">Dharamshala</a>.", "links": [{"title": "1905 Kangra earthquake", "link": "https://wikipedia.org/wiki/1905_Kangra_earthquake"}, {"title": "Kangra Valley", "link": "https://wikipedia.org/wiki/Kangra_Valley"}, {"title": "Kangra, Himachal Pradesh", "link": "https://wikipedia.org/wiki/Kangra,_Himachal_Pradesh"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>j"}, {"title": "Dharamshala", "link": "https://wikipedia.org/wiki/Dharamshala"}]}, {"year": "1913", "text": "First Balkan War: Greek aviator <PERSON><PERSON><PERSON> becomes the first pilot to die in the Hellenic Air Force when his plane crashes.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: Greek aviator <a href=\"https://wikipedia.org/wiki/Emmanouil_Argyropoulos\" title=\"<PERSON><PERSON><PERSON> Argyropoulos\"><PERSON><PERSON><PERSON></a> becomes the first pilot to die in the <a href=\"https://wikipedia.org/wiki/Hellenic_Air_Force\" title=\"Hellenic Air Force\">Hellenic Air Force</a> when his plane crashes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: Greek aviator <a href=\"https://wikipedia.org/wiki/Emmanouil_Argyropoulos\" title=\"<PERSON><PERSON><PERSON> Argyropoulos\"><PERSON><PERSON><PERSON></a> becomes the first pilot to die in the <a href=\"https://wikipedia.org/wiki/Hellenic_Air_Force\" title=\"Hellenic Air Force\">Hellenic Air Force</a> when his plane crashes.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_A<PERSON>los"}, {"title": "Hellenic Air Force", "link": "https://wikipedia.org/wiki/Hellenic_Air_Force"}]}, {"year": "1920", "text": "The four-day N<PERSON>i <PERSON> riots commence.", "html": "1920 - The four-day <a href=\"https://wikipedia.org/wiki/1920_Nebi_Musa_riots\" title=\"1920 Nebi Musa riots\">Nebi Musa riots</a> commence.", "no_year_html": "The four-day <a href=\"https://wikipedia.org/wiki/1920_Nebi_Musa_riots\" title=\"1920 Nebi Musa riots\">Nebi Musa riots</a> commence.", "links": [{"title": "1920 Nebi Musa riots", "link": "https://wikipedia.org/wiki/1920_Nebi_Musa_riots"}]}, {"year": "1925", "text": "The Schutzstaffel (SS) is founded under Adolf <PERSON>'s Nazi Party in Germany.", "html": "1925 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>staffel\" title=\"<PERSON>hu<PERSON>staffel\">Schutzstaffel</a> (SS) is founded under <PERSON>'s <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a> in <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Germany</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>staffel\" title=\"<PERSON>hu<PERSON>staffel\">Schutzstaffel</a> (SS) is founded under <PERSON>'s <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a> in <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Germany</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>sta<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tzstaffel"}, {"title": "Nazi Party", "link": "https://wikipedia.org/wiki/Nazi_Party"}, {"title": "Weimar Republic", "link": "https://wikipedia.org/wiki/Weimar_Republic"}]}, {"year": "1933", "text": "U.S. Navy airship USS Akron is wrecked off the New Jersey coast due to severe weather.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">U.S. Navy</a> <a href=\"https://wikipedia.org/wiki/Airship\" title=\"Airship\">airship</a> <a href=\"https://wikipedia.org/wiki/USS_Akron\" title=\"USS Akron\">USS <i>Akron</i></a> is wrecked off the <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> coast due to severe weather.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">U.S. Navy</a> <a href=\"https://wikipedia.org/wiki/Airship\" title=\"Airship\">airship</a> <a href=\"https://wikipedia.org/wiki/USS_Akron\" title=\"USS Akron\">USS <i>Akron</i></a> is wrecked off the <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> coast due to severe weather.", "links": [{"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Airship", "link": "https://wikipedia.org/wiki/Airship"}, {"title": "USS Akron", "link": "https://wikipedia.org/wiki/USS_Akron"}, {"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}]}, {"year": "1944", "text": "World War II: First bombardment of oil refineries in Bucharest by Anglo-American forces kills 3,000 civilians.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Bombing_of_Bucharest_in_World_War_II\" title=\"Bombing of Bucharest in World War II\">First bombardment of oil refineries in Bucharest by Anglo-American forces</a> kills 3,000 civilians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Bombing_of_Bucharest_in_World_War_II\" title=\"Bombing of Bucharest in World War II\">First bombardment of oil refineries in Bucharest by Anglo-American forces</a> kills 3,000 civilians.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Bombing of Bucharest in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Bucharest_in_World_War_II"}]}, {"year": "1945", "text": "World War II: United States Army troops liberate Ohrdruf forced labor camp in Germany.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> troops liberate <a href=\"https://wikipedia.org/wiki/Ohrdruf_forced_labor_camp\" class=\"mw-redirect\" title=\"Ohrdruf forced labor camp\">Ohrdruf forced labor camp</a> in Germany.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> troops liberate <a href=\"https://wikipedia.org/wiki/Ohrdruf_forced_labor_camp\" class=\"mw-redirect\" title=\"Ohrdruf forced labor camp\">Ohrdruf forced labor camp</a> in Germany.", "links": [{"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "<PERSON>rdruf forced labor camp", "link": "https://wikipedia.org/wiki/Ohrdruf_forced_labor_camp"}]}, {"year": "1945", "text": "World War II: United States Army troops capture Kassel.", "html": "1945 - World War II: United States Army troops <a href=\"https://wikipedia.org/wiki/Battle_of_Kassel_(1945)\" title=\"Battle of Kassel (1945)\">capture Kassel</a>.", "no_year_html": "World War II: United States Army troops <a href=\"https://wikipedia.org/wiki/Battle_of_Kassel_(1945)\" title=\"Battle of Kassel (1945)\">capture Kassel</a>.", "links": [{"title": "Battle of Kassel (1945)", "link": "https://wikipedia.org/wiki/Battle_of_Kassel_(1945)"}]}, {"year": "1945", "text": "World War II: Soviet Red Army troops liberate Hungary from German occupation and occupy the country themselves.", "html": "1945 - World War II: Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> troops liberate <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a> from <a href=\"https://wikipedia.org/wiki/Government_of_National_Unity_(Hungary)\" title=\"Government of National Unity (Hungary)\">German occupation</a> and occupy the country themselves.", "no_year_html": "World War II: Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> troops liberate <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a> from <a href=\"https://wikipedia.org/wiki/Government_of_National_Unity_(Hungary)\" title=\"Government of National Unity (Hungary)\">German occupation</a> and occupy the country themselves.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Hungary", "link": "https://wikipedia.org/wiki/Hungary"}, {"title": "Government of National Unity (Hungary)", "link": "https://wikipedia.org/wiki/Government_of_National_Unity_(Hungary)"}]}, {"year": "1946", "text": "Greek judge and archeologist <PERSON><PERSON><PERSON><PERSON> is appointed Prime Minister of Greece in the midst of the Greek Civil War.", "html": "1946 - Greek judge and archeologist <a href=\"https://wikipedia.org/wiki/Pan<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> in the midst of the <a href=\"https://wikipedia.org/wiki/Greek_Civil_War\" title=\"Greek Civil War\">Greek Civil War</a>.", "no_year_html": "Greek judge and archeologist <a href=\"https://wikipedia.org/wiki/Pan<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> in the midst of the <a href=\"https://wikipedia.org/wiki/Greek_Civil_War\" title=\"Greek Civil War\">Greek Civil War</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panagiot<PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}, {"title": "Greek Civil War", "link": "https://wikipedia.org/wiki/Greek_Civil_War"}]}, {"year": "1949", "text": "Cold War: Twelve nations sign the North Atlantic Treaty creating the North Atlantic Treaty Organization.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Twelve nations sign the <a href=\"https://wikipedia.org/wiki/North_Atlantic_Treaty\" title=\"North Atlantic Treaty\">North Atlantic Treaty</a> creating the <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">North Atlantic Treaty Organization</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Twelve nations sign the <a href=\"https://wikipedia.org/wiki/North_Atlantic_Treaty\" title=\"North Atlantic Treaty\">North Atlantic Treaty</a> creating the <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">North Atlantic Treaty Organization</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "North Atlantic Treaty", "link": "https://wikipedia.org/wiki/North_Atlantic_Treaty"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "1958", "text": "The CND peace symbol is displayed in public for the first time in London.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Campaign_for_Nuclear_Disarmament\" title=\"Campaign for Nuclear Disarmament\">CND</a> <a href=\"https://wikipedia.org/wiki/Peace_symbol\" class=\"mw-redirect\" title=\"Peace symbol\">peace symbol</a> is displayed in public for the first time in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Campaign_for_Nuclear_Disarmament\" title=\"Campaign for Nuclear Disarmament\">CND</a> <a href=\"https://wikipedia.org/wiki/Peace_symbol\" class=\"mw-redirect\" title=\"Peace symbol\">peace symbol</a> is displayed in public for the first time in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "Campaign for Nuclear Disarmament", "link": "https://wikipedia.org/wiki/Campaign_for_Nuclear_Disarmament"}, {"title": "Peace symbol", "link": "https://wikipedia.org/wiki/Peace_symbol"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1960", "text": "France agrees to grant independence to the Mali Federation, a union of Senegal and French Sudan.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> agrees to grant independence to the <a href=\"https://wikipedia.org/wiki/Mali_Federation\" title=\"Mali Federation\">Mali Federation</a>, a union of <a href=\"https://wikipedia.org/wiki/Senegal\" title=\"Senegal\">Senegal</a> and <a href=\"https://wikipedia.org/wiki/French_Sudan\" title=\"French Sudan\">French Sudan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> agrees to grant independence to the <a href=\"https://wikipedia.org/wiki/Mali_Federation\" title=\"Mali Federation\">Mali Federation</a>, a union of <a href=\"https://wikipedia.org/wiki/Senegal\" title=\"Senegal\">Senegal</a> and <a href=\"https://wikipedia.org/wiki/French_Sudan\" title=\"French Sudan\">French Sudan</a>.", "links": [{"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Mali Federation", "link": "https://wikipedia.org/wiki/Mali_Federation"}, {"title": "Senegal", "link": "https://wikipedia.org/wiki/Senegal"}, {"title": "French Sudan", "link": "https://wikipedia.org/wiki/French_Sudan"}]}, {"year": "1963", "text": "Bye Bye Birdie, a musical romantic comedy film directed by <PERSON>, was released.", "html": "1963 - <i><a href=\"https://wikipedia.org/wiki/Bye_Bye_<PERSON><PERSON>_(1963_film)\" title=\"Bye Bye <PERSON>ie (1963 film)\">Bye Bye <PERSON>ie</a></i>, a musical romantic comedy film directed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, was released.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Bye_Bye_<PERSON><PERSON>_(1963_film)\" title=\"Bye Bye <PERSON>ie (1963 film)\">Bye Bye <PERSON>ie</a></i>, a musical romantic comedy film directed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, was released.", "links": [{"title": "Bye Bye <PERSON><PERSON> (1963 film)", "link": "https://wikipedia.org/wiki/Bye_Bye_<PERSON><PERSON>_(1963_film)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "The Beatles occupy the top five positions on the Billboard Hot 100 pop chart.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> occupy the top five positions on the <a href=\"https://wikipedia.org/wiki/Billboard_Hot_100\" title=\"Billboard Hot 100\"><i>Billboard</i> Hot 100</a> pop chart.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> occupy the top five positions on the <a href=\"https://wikipedia.org/wiki/Billboard_Hot_100\" title=\"Billboard Hot 100\"><i>Billboard</i> Hot 100</a> pop chart.", "links": [{"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "Billboard Hot 100", "link": "https://wikipedia.org/wiki/Billboard_Hot_100"}]}, {"year": "1967", "text": "<PERSON> delivers his \"Beyond Vietnam: A Time to Break Silence\" speech in New York City's Riverside Church.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a> delivers his \"<a href=\"https://wikipedia.org/wiki/Beyond_Vietnam:_A_Time_to_Break_Silence\" title=\"Beyond Vietnam: A Time to Break Silence\">Beyond Vietnam: A Time to Break Silence</a>\" speech in New York City's <a href=\"https://wikipedia.org/wiki/Riverside_Church\" title=\"Riverside Church\">Riverside Church</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> delivers his \"<a href=\"https://wikipedia.org/wiki/Beyond_Vietnam:_A_Time_to_Break_Silence\" title=\"Beyond Vietnam: A Time to Break Silence\">Beyond Vietnam: A Time to Break Silence</a>\" speech in New York City's <a href=\"https://wikipedia.org/wiki/Riverside_Church\" title=\"Riverside Church\">Riverside Church</a>.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Beyond Vietnam: A Time to Break Silence", "link": "https://wikipedia.org/wiki/Beyond_Vietnam:_A_Time_to_Break_Silence"}, {"title": "Riverside Church", "link": "https://wikipedia.org/wiki/Riverside_Church"}]}, {"year": "1968", "text": "<PERSON> is assassinated by <PERSON> at a motel in Memphis, Tennessee.", "html": "1968 - <PERSON>. is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"Assassination of <PERSON>.\">assassinated</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at a <a href=\"https://wikipedia.org/wiki/National_Civil_Rights_Museum\" title=\"National Civil Rights Museum\">motel</a> in <a href=\"https://wikipedia.org/wiki/Memphis,_Tennessee\" title=\"Memphis, Tennessee\">Memphis, Tennessee</a>.", "no_year_html": "<PERSON> Jr. is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"Assassination of <PERSON>.\">assassinated</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at a <a href=\"https://wikipedia.org/wiki/National_Civil_Rights_Museum\" title=\"National Civil Rights Museum\">motel</a> in <a href=\"https://wikipedia.org/wiki/Memphis,_Tennessee\" title=\"Memphis, Tennessee\">Memphis, Tennessee</a>.", "links": [{"title": "Assassination of <PERSON>.", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "National Civil Rights Museum", "link": "https://wikipedia.org/wiki/National_Civil_Rights_Museum"}, {"title": "Memphis, Tennessee", "link": "https://wikipedia.org/wiki/Memphis,_Tennessee"}]}, {"year": "1968", "text": "Apollo program: NASA launches Apollo 6.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Apollo_6\" title=\"Apollo 6\">Apollo 6</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Apollo_6\" title=\"Apollo 6\">Apollo 6</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Apollo 6", "link": "https://wikipedia.org/wiki/Apollo_6"}]}, {"year": "1969", "text": "Dr. <PERSON> implants the first temporary artificial heart.", "html": "1969 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Implant_(medicine)\" title=\"Implant (medicine)\">implants</a> the first temporary <a href=\"https://wikipedia.org/wiki/Artificial_heart\" title=\"Artificial heart\">artificial heart</a>.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Implant_(medicine)\" title=\"Implant (medicine)\">implants</a> the first temporary <a href=\"https://wikipedia.org/wiki/Artificial_heart\" title=\"Artificial heart\">artificial heart</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> (medicine)", "link": "https://wikipedia.org/wiki/Implant_(medicine)"}, {"title": "Artificial heart", "link": "https://wikipedia.org/wiki/Artificial_heart"}]}, {"year": "1973", "text": "The Twin Towers of the World Trade Center in New York City are officially dedicated.", "html": "1973 - The Twin Towers of the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> in New York City are officially dedicated.", "no_year_html": "The Twin Towers of the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> in New York City are officially dedicated.", "links": [{"title": "World Trade Center (1973-2001)", "link": "https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)"}]}, {"year": "1973", "text": "A Lockheed C-141 Starlifter, dubbed the Hanoi Taxi, makes the last flight of Operation Homecoming.", "html": "1973 - A <a href=\"https://wikipedia.org/wiki/Lockheed_C-141_Starlifter\" title=\"Lockheed C-141 Starlifter\">Lockheed C-141 Starlifter</a>, dubbed the <i><a href=\"https://wikipedia.org/wiki/Hanoi_Taxi\" title=\"Hanoi Taxi\">Han<PERSON></a></i>, makes the last flight of <a href=\"https://wikipedia.org/wiki/Operation_Homecoming\" title=\"Operation Homecoming\">Operation Homecoming</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Lockheed_C-141_Starlifter\" title=\"Lockheed C-141 Starlifter\">Lockheed C-141 Starlifter</a>, dubbed the <i><a href=\"https://wikipedia.org/wiki/Hanoi_Taxi\" title=\"Hanoi Taxi\">Han<PERSON></a></i>, makes the last flight of <a href=\"https://wikipedia.org/wiki/Operation_Homecoming\" title=\"Operation Homecoming\">Operation Homecoming</a>.", "links": [{"title": "Lockheed C-141 Starlifter", "link": "https://wikipedia.org/wiki/Lockheed_C-141_Starlifter"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hanoi_Taxi"}, {"title": "Operation Homecoming", "link": "https://wikipedia.org/wiki/Operation_Homecoming"}]}, {"year": "1975", "text": "Microsoft is founded as a partnership between <PERSON> and <PERSON> in Albuquerque, New Mexico.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> is founded as a partnership between <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Albuquerque,_New_Mexico\" title=\"Albuquerque, New Mexico\">Albuquerque, New Mexico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> is founded as a partnership between <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Albuquerque,_New_Mexico\" title=\"Albuquerque, New Mexico\">Albuquerque, New Mexico</a>.", "links": [{"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Albuquerque, New Mexico", "link": "https://wikipedia.org/wiki/Albuquerque,_New_Mexico"}]}, {"year": "1975", "text": "Vietnam War: A United States Air Force Lockheed C-5A Galaxy transporting orphans, crashes near Saigon, South Vietnam shortly after takeoff, killing 172 people.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: A United States Air Force <a href=\"https://wikipedia.org/wiki/Lockheed_C-5_Galaxy\" title=\"Lockheed C-5 Galaxy\">Lockheed C-5A Galaxy</a> <a href=\"https://wikipedia.org/wiki/Operation_Babylift\" title=\"Operation Babylift\">transporting orphans</a>, <a href=\"https://wikipedia.org/wiki/1975_T%C3%A2n_S%C6%A1n_Nh%E1%BB%A9t_C-5_accident\" title=\"1975 Tân Sơn Nhứt C-5 accident\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Ho_Chi_Minh_City\" title=\"Ho Chi Minh City\">Saigon</a>, <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> shortly after takeoff, killing 172 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: A United States Air Force <a href=\"https://wikipedia.org/wiki/Lockheed_C-5_Galaxy\" title=\"Lockheed C-5 Galaxy\">Lockheed C-5A Galaxy</a> <a href=\"https://wikipedia.org/wiki/Operation_Babylift\" title=\"Operation Babylift\">transporting orphans</a>, <a href=\"https://wikipedia.org/wiki/1975_T%C3%A2n_S%C6%A1n_Nh%E1%BB%A9t_C-5_accident\" title=\"1975 Tân Sơn Nhứt C-5 accident\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Ho_Chi_Minh_City\" title=\"Ho Chi Minh City\">Saigon</a>, <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> shortly after takeoff, killing 172 people.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Lockheed C-5 Galaxy", "link": "https://wikipedia.org/wiki/Lockheed_C-5_Galaxy"}, {"title": "Operation Babylift", "link": "https://wikipedia.org/wiki/Operation_Babylift"}, {"title": "1975 Tân Sơn N<PERSON>ứt C-5 accident", "link": "https://wikipedia.org/wiki/1975_T%C3%A2n_S%C6%A1n_Nh%E1%BB%A9t_C-5_accident"}, {"title": "Ho Chi Minh City", "link": "https://wikipedia.org/wiki/Ho_Chi_Minh_City"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1977", "text": "Southern Airways Flight 242 crashes in New Hope, Paulding County, Georgia, killing 72.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Southern_Airways_Flight_242\" title=\"Southern Airways Flight 242\">Southern Airways Flight 242</a> crashes in <a href=\"https://wikipedia.org/wiki/New_Hope,_Paulding_County,_Georgia\" title=\"New Hope, Paulding County, Georgia\">New Hope, Paulding County, Georgia</a>, killing 72.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Southern_Airways_Flight_242\" title=\"Southern Airways Flight 242\">Southern Airways Flight 242</a> crashes in <a href=\"https://wikipedia.org/wiki/New_Hope,_Paulding_County,_Georgia\" title=\"New Hope, Paulding County, Georgia\">New Hope, Paulding County, Georgia</a>, killing 72.", "links": [{"title": "Southern Airways Flight 242", "link": "https://wikipedia.org/wiki/Southern_Airways_Flight_242"}, {"title": "New Hope, Paulding County, Georgia", "link": "https://wikipedia.org/wiki/New_Hope,_Paulding_County,_Georgia"}]}, {"year": "1979", "text": "Prime Minister <PERSON><PERSON><PERSON><PERSON> of Pakistan is executed.", "html": "1979 - Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> of Pakistan is executed.", "no_year_html": "Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> of Pakistan is executed.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "Iran-Iraq War: The Islamic Republic of Iran Air Force mounts an attack on H-3 Airbase and destroys about 50 Iraqi aircraft.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Iraq_War\" title=\"Iran-Iraq War\">Iran-Iraq War</a>: The <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force\" title=\"Islamic Republic of Iran Air Force\">Islamic Republic of Iran Air Force</a> mounts an <a href=\"https://wikipedia.org/wiki/Attack_on_H3\" class=\"mw-redirect\" title=\"Attack on H3\">attack on H-3 Airbase</a> and destroys about 50 Iraqi aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Iraq_War\" title=\"Iran-Iraq War\">Iran-Iraq War</a>: The <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force\" title=\"Islamic Republic of Iran Air Force\">Islamic Republic of Iran Air Force</a> mounts an <a href=\"https://wikipedia.org/wiki/Attack_on_H3\" class=\"mw-redirect\" title=\"Attack on H3\">attack on H-3 Airbase</a> and destroys about 50 Iraqi aircraft.", "links": [{"title": "Iran-Iraq War", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Iraq_War"}, {"title": "Islamic Republic of Iran Air Force", "link": "https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force"}, {"title": "Attack on H3", "link": "https://wikipedia.org/wiki/Attack_on_H3"}]}, {"year": "1983", "text": "Space Shuttle program: Space Shuttle Challenger makes its maiden voyage into space on STS-6.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: Space Shuttle <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Challenger</a></i> makes its maiden voyage into space on <a href=\"https://wikipedia.org/wiki/STS-6\" title=\"STS-6\">STS-6</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: Space Shuttle <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Challenger</a></i> makes its maiden voyage into space on <a href=\"https://wikipedia.org/wiki/STS-6\" title=\"STS-6\">STS-6</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Challenger", "link": "https://wikipedia.org/wiki/Space_Shuttle_Challenger"}, {"title": "STS-6", "link": "https://wikipedia.org/wiki/STS-6"}]}, {"year": "1984", "text": "President <PERSON> calls for an international ban on chemical weapons.", "html": "1984 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> calls for an international ban on <a href=\"https://wikipedia.org/wiki/Chemical_weapon\" title=\"Chemical weapon\">chemical weapons</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> calls for an international ban on <a href=\"https://wikipedia.org/wiki/Chemical_weapon\" title=\"Chemical weapon\">chemical weapons</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chemical weapon", "link": "https://wikipedia.org/wiki/Chemical_weapon"}]}, {"year": "1987", "text": "Garuda Indonesia Flight 032 crashes at Medan Airport, killing 23.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Garuda_Indonesia_Flight_035\" title=\"Garuda Indonesia Flight 035\">Garuda Indonesia Flight 032</a> crashes at <a href=\"https://wikipedia.org/wiki/Soewondo_Air_Force_Base\" title=\"Soewondo Air Force Base\">Medan Airport</a>, killing 23.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garuda_Indonesia_Flight_035\" title=\"Garuda Indonesia Flight 035\">Garuda Indonesia Flight 032</a> crashes at <a href=\"https://wikipedia.org/wiki/Soewondo_Air_Force_Base\" title=\"Soewondo Air Force Base\">Medan Airport</a>, killing 23.", "links": [{"title": "Garuda Indonesia Flight 035", "link": "https://wikipedia.org/wiki/Garuda_Indonesia_Flight_035"}, {"title": "Soewondo Air Force Base", "link": "https://wikipedia.org/wiki/Soewondo_Air_Force_Base"}]}, {"year": "1988", "text": "Governor <PERSON> of Arizona is convicted in his impeachment trial and removed from office.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Arizona\" class=\"mw-redirect\" title=\"List of Governors of Arizona\">Governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a> is convicted in his <a href=\"https://wikipedia.org/wiki/Impeachment_trial\" title=\"Impeachment trial\">impeachment trial</a> and removed from office.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Arizona\" class=\"mw-redirect\" title=\"List of Governors of Arizona\">Governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a> is convicted in his <a href=\"https://wikipedia.org/wiki/Impeachment_trial\" title=\"Impeachment trial\">impeachment trial</a> and removed from office.", "links": [{"title": "List of Governors of Arizona", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Arizona"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Arizona", "link": "https://wikipedia.org/wiki/Arizona"}, {"title": "Impeachment trial", "link": "https://wikipedia.org/wiki/Impeachment_trial"}]}, {"year": "1990", "text": "The current flag of Hong Kong is adopted for post-colonial Hong Kong during the Third Session of the Seventh National People's Congress.", "html": "1990 - The current <a href=\"https://wikipedia.org/wiki/Flag_of_Hong_Kong\" title=\"Flag of Hong Kong\">flag of Hong Kong</a> is adopted for post-colonial Hong Kong during the Third Session of the <a href=\"https://wikipedia.org/wiki/7th_National_People%27s_Congress\" title=\"7th National People's Congress\">Seventh</a> <a href=\"https://wikipedia.org/wiki/National_People%27s_Congress\" title=\"National People's Congress\">National People's Congress</a>.", "no_year_html": "The current <a href=\"https://wikipedia.org/wiki/Flag_of_Hong_Kong\" title=\"Flag of Hong Kong\">flag of Hong Kong</a> is adopted for post-colonial Hong Kong during the Third Session of the <a href=\"https://wikipedia.org/wiki/7th_National_People%27s_Congress\" title=\"7th National People's Congress\">Seventh</a> <a href=\"https://wikipedia.org/wiki/National_People%27s_Congress\" title=\"National People's Congress\">National People's Congress</a>.", "links": [{"title": "Flag of Hong Kong", "link": "https://wikipedia.org/wiki/Flag_of_Hong_Kong"}, {"title": "7th National People's Congress", "link": "https://wikipedia.org/wiki/7th_National_People%27s_Congress"}, {"title": "National People's Congress", "link": "https://wikipedia.org/wiki/National_People%27s_Congress"}]}, {"year": "1991", "text": "Senator <PERSON> of Pennsylvania and six others are killed when a helicopter collides with their airplane over an elementary school in Merion, Pennsylvania.", "html": "1991 - Senator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> and six others are killed when a helicopter <a href=\"https://wikipedia.org/wiki/Merion_air_disaster\" class=\"mw-redirect\" title=\"Merion air disaster\">collides with their airplane</a> over an elementary school in <a href=\"https://wikipedia.org/wiki/Merion,_Pennsylvania\" class=\"mw-redirect\" title=\"Merion, Pennsylvania\">Merion, Pennsylvania</a>.", "no_year_html": "Senator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> and six others are killed when a helicopter <a href=\"https://wikipedia.org/wiki/Merion_air_disaster\" class=\"mw-redirect\" title=\"Merion air disaster\">collides with their airplane</a> over an elementary school in <a href=\"https://wikipedia.org/wiki/Merion,_Pennsylvania\" class=\"mw-redirect\" title=\"Merion, Pennsylvania\">Merion, Pennsylvania</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}, {"title": "Merion air disaster", "link": "https://wikipedia.org/wiki/Merion_air_disaster"}, {"title": "Merion, Pennsylvania", "link": "https://wikipedia.org/wiki/Merion,_Pennsylvania"}]}, {"year": "1991", "text": "Forty-one people are taken hostage inside a Good Guys! Electronics store in Sacramento, California. Three of the hostage takers and three hostages are killed.", "html": "1991 - Forty-one people are <a href=\"https://wikipedia.org/wiki/1991_Sacramento_hostage_crisis\" title=\"1991 Sacramento hostage crisis\">taken hostage</a> inside a <a href=\"https://wikipedia.org/wiki/Good_Guys_(American_company)\" title=\"Good Guys (American company)\">Good Guys! Electronics</a> store in <a href=\"https://wikipedia.org/wiki/Sacramento\" class=\"mw-redirect\" title=\"Sacramento\">Sacramento</a>, <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>. Three of the hostage takers and three hostages are killed.", "no_year_html": "Forty-one people are <a href=\"https://wikipedia.org/wiki/1991_Sacramento_hostage_crisis\" title=\"1991 Sacramento hostage crisis\">taken hostage</a> inside a <a href=\"https://wikipedia.org/wiki/Good_Guys_(American_company)\" title=\"Good Guys (American company)\">Good Guys! Electronics</a> store in <a href=\"https://wikipedia.org/wiki/Sacramento\" class=\"mw-redirect\" title=\"Sacramento\">Sacramento</a>, <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>. Three of the hostage takers and three hostages are killed.", "links": [{"title": "1991 Sacramento hostage crisis", "link": "https://wikipedia.org/wiki/1991_Sacramento_hostage_crisis"}, {"title": "Good Guys (American company)", "link": "https://wikipedia.org/wiki/Good_Guys_(American_company)"}, {"title": "Sacramento", "link": "https://wikipedia.org/wiki/Sacramento"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1994", "text": "Three people are killed when KLM Cityhopper Flight 433 crashes at Amsterdam Airport Schiphol.", "html": "1994 - Three people are killed when <a href=\"https://wikipedia.org/wiki/KLM_Cityhopper_Flight_433\" title=\"KLM Cityhopper Flight 433\">KLM Cityhopper Flight 433</a> crashes at <a href=\"https://wikipedia.org/wiki/Amsterdam_Airport_Schiphol\" title=\"Amsterdam Airport Schiphol\">Amsterdam Airport Schiphol</a>.", "no_year_html": "Three people are killed when <a href=\"https://wikipedia.org/wiki/KLM_Cityhopper_Flight_433\" title=\"KLM Cityhopper Flight 433\">KLM Cityhopper Flight 433</a> crashes at <a href=\"https://wikipedia.org/wiki/Amsterdam_Airport_Schiphol\" title=\"Amsterdam Airport Schiphol\">Amsterdam Airport Schiphol</a>.", "links": [{"title": "KLM Cityhopper Flight 433", "link": "https://wikipedia.org/wiki/KLM_Cityhopper_Flight_433"}, {"title": "Amsterdam Airport Schiphol", "link": "https://wikipedia.org/wiki/Amsterdam_Airport_Schiphol"}]}, {"year": "1996", "text": "Comet Hyakutake is imaged by the USA Asteroid Orbiter Near Earth Asteroid Rendezvous.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Comet_Hyakutake\" title=\"Comet Hyakutake\">Comet Hyakutake</a> is imaged by the USA Asteroid Orbiter <a href=\"https://wikipedia.org/wiki/Near_Earth_Asteroid_Rendezvous\" class=\"mw-redirect\" title=\"Near Earth Asteroid Rendezvous\">Near Earth Asteroid Rendezvous</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comet_Hyakutake\" title=\"Comet Hyakutake\">Comet Hyakutake</a> is imaged by the USA Asteroid Orbiter <a href=\"https://wikipedia.org/wiki/Near_Earth_Asteroid_Rendezvous\" class=\"mw-redirect\" title=\"Near Earth Asteroid Rendezvous\">Near Earth Asteroid Rendezvous</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>take"}, {"title": "Near Earth Asteroid Ren<PERSON>", "link": "https://wikipedia.org/wiki/Near_Earth_Asteroid_Rendezvous"}]}, {"year": "1997", "text": "Space Shuttle program: Space Shuttle Columbia is launched on STS-83. However, the mission is later cut short due to a fuel cell problem.", "html": "1997 - Space Shuttle program: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-83\" title=\"STS-83\">STS-83</a>. However, the mission is later cut short due to a fuel cell problem.", "no_year_html": "Space Shuttle program: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-83\" title=\"STS-83\">STS-83</a>. However, the mission is later cut short due to a fuel cell problem.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-83", "link": "https://wikipedia.org/wiki/STS-83"}]}, {"year": "2002", "text": "The MPLA government of Angola and UNITA rebels sign a peace treaty ending the Angolan Civil War.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/MPLA\" title=\"MPLA\">MPLA</a> government of Angola and <a href=\"https://wikipedia.org/wiki/UNITA\" title=\"UNITA\">UNITA</a> rebels sign a peace treaty ending the <a href=\"https://wikipedia.org/wiki/Angolan_Civil_War\" title=\"Angolan Civil War\">Angolan Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/MPLA\" title=\"MPLA\">MPLA</a> government of Angola and <a href=\"https://wikipedia.org/wiki/UNITA\" title=\"UNITA\">UNITA</a> rebels sign a peace treaty ending the <a href=\"https://wikipedia.org/wiki/Angolan_Civil_War\" title=\"Angolan Civil War\">Angolan Civil War</a>.", "links": [{"title": "MPLA", "link": "https://wikipedia.org/wiki/MPLA"}, {"title": "UNITA", "link": "https://wikipedia.org/wiki/UNITA"}, {"title": "Angolan Civil War", "link": "https://wikipedia.org/wiki/Angolan_Civil_War"}]}, {"year": "2009", "text": "France announces its return to full participation of its military forces within NATO.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> announces its return to full participation of its military forces within <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> announces its return to full participation of its military forces within <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "links": [{"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "2010", "text": "A magnitude 7.2 earthquake hits south of the Mexico-USA border, killing at least two and damaging buildings across the two countries.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/2010_Baja_California_earthquake\" title=\"2010 Baja California earthquake\">A magnitude 7.2 earthquake</a> hits south of the <a href=\"https://wikipedia.org/wiki/Mexico-United_States_border\" class=\"mw-redirect\" title=\"Mexico-United States border\">Mexico-USA border</a>, killing at least two and damaging buildings across the two countries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2010_Baja_California_earthquake\" title=\"2010 Baja California earthquake\">A magnitude 7.2 earthquake</a> hits south of the <a href=\"https://wikipedia.org/wiki/Mexico-United_States_border\" class=\"mw-redirect\" title=\"Mexico-United States border\">Mexico-USA border</a>, killing at least two and damaging buildings across the two countries.", "links": [{"title": "2010 Baja California earthquake", "link": "https://wikipedia.org/wiki/2010_Baja_California_earthquake"}, {"title": "Mexico-United States border", "link": "https://wikipedia.org/wiki/Mexico-United_States_border"}]}, {"year": "2011", "text": "Georgian Airways Flight 834 crashes at N'djili Airport in Kinshasa, killing 32.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Georgian_Airways_Flight_834\" title=\"Georgian Airways Flight 834\">Georgian Airways Flight 834</a> crashes at <a href=\"https://wikipedia.org/wiki/N%27djili_Airport\" title=\"N'djili Airport\">N'djili Airport</a> in <a href=\"https://wikipedia.org/wiki/Kinshasa\" title=\"Kinshasa\">Kinshasa</a>, killing 32.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgian_Airways_Flight_834\" title=\"Georgian Airways Flight 834\">Georgian Airways Flight 834</a> crashes at <a href=\"https://wikipedia.org/wiki/N%27djili_Airport\" title=\"N'djili Airport\">N'djili Airport</a> in <a href=\"https://wikipedia.org/wiki/Kinshasa\" title=\"Kinshasa\">Kinshasa</a>, killing 32.", "links": [{"title": "Georgian Airways Flight 834", "link": "https://wikipedia.org/wiki/Georgian_Airways_Flight_834"}, {"title": "N'djili Airport", "link": "https://wikipedia.org/wiki/N%27djili_Airport"}, {"title": "Kinshasa", "link": "https://wikipedia.org/wiki/Kinshasa"}]}, {"year": "2013", "text": "74 people are killed in a building collapse in Thane, India.", "html": "2013 - 74 people are killed in a <a href=\"https://wikipedia.org/wiki/2013_Thane_building_collapse\" title=\"2013 Thane building collapse\">building collapse</a> in <a href=\"https://wikipedia.org/wiki/Thane\" title=\"Thane\">Thane</a>, India.", "no_year_html": "74 people are killed in a <a href=\"https://wikipedia.org/wiki/2013_Thane_building_collapse\" title=\"2013 Thane building collapse\">building collapse</a> in <a href=\"https://wikipedia.org/wiki/Thane\" title=\"Thane\">Thane</a>, India.", "links": [{"title": "2013 Thane building collapse", "link": "https://wikipedia.org/wiki/2013_<PERSON>e_building_collapse"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thane"}]}, {"year": "2017", "text": "Syria conducts an air strike on Khan <PERSON> using chemical weapons, killing 89 civilians.", "html": "2017 - Syria conducts an <a href=\"https://wikipedia.org/wiki/<PERSON>_Shaykhun_chemical_attack\" title=\"<PERSON> Shaykhun chemical attack\">air strike</a> on <a href=\"https://wikipedia.org/wiki/Khan_Shaykhun\" title=\"<PERSON> Shaykh<PERSON>\"><PERSON></a> using <a href=\"https://wikipedia.org/wiki/Syria_chemical_weapons_program\" class=\"mw-redirect\" title=\"Syria chemical weapons program\">chemical weapons</a>, killing 89 civilians.", "no_year_html": "Syria conducts an <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_chemical_attack\" title=\"<PERSON> Shaykhun chemical attack\">air strike</a> on <a href=\"https://wikipedia.org/wiki/Khan_Shaykhun\" title=\"<PERSON> Shay<PERSON>\"><PERSON></a> using <a href=\"https://wikipedia.org/wiki/Syria_chemical_weapons_program\" class=\"mw-redirect\" title=\"Syria chemical weapons program\">chemical weapons</a>, killing 89 civilians.", "links": [{"title": "Khan <PERSON>un chemical attack", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_chemical_attack"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Syria chemical weapons program", "link": "https://wikipedia.org/wiki/Syria_chemical_weapons_program"}]}, {"year": "2020", "text": "China holds a national day of mourning for martyrs who died in the fight against the novel coronavirus disease outbreak.", "html": "2020 - China holds a <a href=\"https://wikipedia.org/wiki/National_day_of_mourning\" title=\"National day of mourning\">national day of mourning</a> for martyrs who died in the fight against the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_mainland_China\" title=\"COVID-19 pandemic in mainland China\">novel coronavirus disease outbreak</a>.", "no_year_html": "China holds a <a href=\"https://wikipedia.org/wiki/National_day_of_mourning\" title=\"National day of mourning\">national day of mourning</a> for martyrs who died in the fight against the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_mainland_China\" title=\"COVID-19 pandemic in mainland China\">novel coronavirus disease outbreak</a>.", "links": [{"title": "National day of mourning", "link": "https://wikipedia.org/wiki/National_day_of_mourning"}, {"title": "COVID-19 pandemic in mainland China", "link": "https://wikipedia.org/wiki/COVID-19_pandemic_in_mainland_China"}]}, {"year": "2023", "text": "Finland becomes a member of NATO after Turkey accepts its membership request.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> becomes <a href=\"https://wikipedia.org/wiki/Enlargement_of_NATO\" title=\"Enlargement of NATO\">a member</a> of <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> after <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> accepts its membership request.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> becomes <a href=\"https://wikipedia.org/wiki/Enlargement_of_NATO\" title=\"Enlargement of NATO\">a member</a> of <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> after <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> accepts its membership request.", "links": [{"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Enlargement of NATO", "link": "https://wikipedia.org/wiki/Enlargement_of_NATO"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}]}], "Births": [{"year": "188", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 217)", "html": "188 - <a href=\"https://wikipedia.org/wiki/Caracalla\" title=\"Caracal<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 217)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caracalla\" title=\"Caracal<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 217)", "links": [{"title": "Caracal<PERSON>", "link": "https://wikipedia.org/wiki/Caracalla"}]}, {"year": "1436", "text": "<PERSON><PERSON> of Saxony, Duchess of Bavaria-Landshut (d. 1501)", "html": "1436 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Saxony,_Duchess_of_Bavaria\" title=\"<PERSON><PERSON> of Saxony, Duchess of Bavaria\"><PERSON><PERSON> of Saxony, Duchess of Bavaria</a>-<PERSON><PERSON><PERSON> (d. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Saxony,_Duchess_of_Bavaria\" title=\"<PERSON><PERSON> of Saxony, Duchess of Bavaria\"><PERSON><PERSON> of Saxony, Duchess of Bavaria</a>-<PERSON><PERSON><PERSON> (d. 1501)", "links": [{"title": "<PERSON><PERSON> of Saxony, Duchess of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Saxony,_Duchess_of_Bavaria"}]}, {"year": "1490", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Pernstein, Bohemian nobleman (d. 1534)", "html": "1490 - <a href=\"https://wikipedia.org/wiki/Vojt%C4%9Bch_I_of_Pernstein\" class=\"mw-redirect\" title=\"Vojtěch I of Pernstein\">V<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> of Pernstein</a>, Bohemian nobleman (d. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vojt%C4%9Bch_I_of_Pernstein\" class=\"mw-redirect\" title=\"Vojtěch I of Pernstein\">Voj<PERSON><PERSON><PERSON> <PERSON> of Pernstein</a>, Bohemian nobleman (d. 1534)", "links": [{"title": "Vojtěch I of Pernstein", "link": "https://wikipedia.org/wiki/Vojt%C4%9Bch_I_of_<PERSON><PERSON>"}]}, {"year": "1492", "text": "<PERSON><PERSON><PERSON>, German-Swiss theologian and reformer (d. 1564)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss theologian and reformer (d. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss theologian and reformer (d. 1564)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1572", "text": "<PERSON>, English author (d. 1621)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1586", "text": "<PERSON>, English diplomat (d. 1661)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (d. 1661)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1593", "text": "<PERSON>, English soldier and politician, Secretary of State for the Southern Department (d. 1669)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (d. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1640", "text": "<PERSON><PERSON>, Spanish guitarist, composer, and priest (d. 1710)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/Gaspar_<PERSON>z\" title=\"Gaspar Sanz\"><PERSON><PERSON></a>, Spanish guitarist, composer, and priest (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaspar_<PERSON>z\" title=\"Gaspar Sanz\"><PERSON><PERSON></a>, Spanish guitarist, composer, and priest (d. 1710)", "links": [{"title": "Gaspar Sanz", "link": "https://wikipedia.org/wiki/Gaspar_Sanz"}]}, {"year": "1646", "text": "<PERSON>, French orientalist and archaeologist (d. 1715)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orientalist and archaeologist (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orientalist and archaeologist (d. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON><PERSON><PERSON>, Dutch-English sculptor (d. 1721)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-English sculptor (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-English sculptor (d. 1721)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1676", "text": "<PERSON>, Italian composer (d. 1760)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON><PERSON><PERSON>, French astronomer and cartographer (d. 1768)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and cartographer (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and cartographer (d. 1768)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, English theologian and scholar (d. 1783)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and scholar (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and scholar (d. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1752", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (d. 1837)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1837)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, Venezuelan organist and composer (d. 1797)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan organist and composer (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan organist and composer (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, English actor and composer (d. 1796)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and composer (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and composer (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON> of Breslov, Ukrainian founder of the Breslov Hasidic movement (d. 1810)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_of_Breslov\" title=\"<PERSON><PERSON><PERSON> of Breslov\"><PERSON><PERSON><PERSON> of Breslov</a>, Ukrainian founder of the Breslov Hasidic movement (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Breslov\" title=\"<PERSON><PERSON><PERSON> of Breslov\"><PERSON><PERSON><PERSON> of Breslov</a>, Ukrainian founder of the Breslov Hasidic movement (d. 1810)", "links": [{"title": "<PERSON><PERSON><PERSON> of Breslov", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Breslov"}]}, {"year": "1780", "text": "<PERSON>, American minister and painter (d. 1849)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and painter (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and painter (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON><PERSON>, German author, illustrator, and composer (d. 1859)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author, illustrator, and composer (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author, illustrator, and composer (d. 1859)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON><PERSON><PERSON><PERSON>, American lawyer and politician (d. 1868)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American lawyer and politician (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American lawyer and politician (d. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, American nurse and activist (d. 1887)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and activist (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and activist (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Irish-American author and poet (d. 1883)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American author and poet (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American author and poet (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON> of Portugal (d. 1853)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Maria_II_of_Portugal\" title=\"Maria II of Portugal\">Maria II of Portugal</a> (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_II_of_Portugal\" title=\"Maria II of Portugal\">Maria II of Portugal</a> (d. 1853)", "links": [{"title": "Maria II of Portugal", "link": "https://wikipedia.org/wiki/Maria_II_of_Portugal"}]}, {"year": "1821", "text": "<PERSON><PERSON>, American engineer and businessman (d. 1868)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, American engineer and businessman (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American engineer and businessman (d. 1868)", "links": [{"title": "<PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1826", "text": "<PERSON><PERSON><PERSON>, Belgian engineer, invented the <PERSON><PERSON> machine (d. 1901)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Z%C3%A9nobe_Gramme\" title=\"Zénobe Gramme\"><PERSON><PERSON><PERSON></a>, Belgian engineer, invented the <a href=\"https://wikipedia.org/wiki/Gramme_machine\" title=\"Gramme machine\">Gramme machine</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Z%C3%A9nobe_Gramme\" title=\"Zénobe Gramme\"><PERSON><PERSON><PERSON></a>, Belgian engineer, invented the <a href=\"https://wikipedia.org/wiki/Gramme_machine\" title=\"Gramme machine\">Gramme machine</a> (d. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Z%C3%A9nobe_Gramme"}, {"title": "Gramme machine", "link": "https://wikipedia.org/wiki/Gramme_machine"}]}, {"year": "1829", "text": "<PERSON>, Australian bushranger, poet, confidence-man and author (d. ?)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Owen_<PERSON>\" title=\"Owen Suffolk\"><PERSON></a>, Australian bushranger, poet, confidence-man and author (d. ?)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Owen_<PERSON>\" title=\"Owen Suffolk\"><PERSON></a>, Australian bushranger, poet, confidence-man and author (d. ?)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Owen_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, English physician and neurologist (d. 1911)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and neurologist (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and neurologist (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON>, French mathematician and theorist (d. 1891)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (d. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American painter and photographer (d. 1942)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and photographer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and photographer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON><PERSON><PERSON>, Uruguayan-French poet and educator (d. 1870)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Com<PERSON>_de_Lautr%C3%A9amont\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan-French poet and educator (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Com<PERSON>_de_Lautr%C3%A9amont\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan-French poet and educator (d. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Comte_de_Lautr%C3%A9amont"}]}, {"year": "1851", "text": "<PERSON>, 1st Baron <PERSON>, Irish lawyer and politician (d. 1931)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron Glenavy\"><PERSON>, 1st Baron <PERSON></a>, Irish lawyer and politician (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>avy\"><PERSON>, 1st Baron <PERSON></a>, Irish lawyer and politician (d. 1931)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON>, French poet, novelist, and critic (d. 1915)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet, novelist, and critic (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet, novelist, and critic (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, English mathematician and educator (d. 1948)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English mathematician and educator (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English mathematician and educator (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American architect, designed the Desert View Watchtower (d. 1958)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Desert_View_Watchtower\" title=\"Desert View Watchtower\">Desert View Watchtower</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Desert_View_Watchtower\" title=\"Desert View Watchtower\">Desert View Watchtower</a> (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Desert View Watchtower", "link": "https://wikipedia.org/wiki/Desert_View_Watchtower"}]}, {"year": "1875", "text": "<PERSON>, Sephardic Jewish French-American viola player and conductor (d. 1964)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sephardic\" class=\"mw-redirect\" title=\"Sephardic\"><PERSON><PERSON><PERSON></a> Jewish French-American viola player and conductor (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sephardic\" class=\"mw-redirect\" title=\"Sephardic\"><PERSON><PERSON><PERSON></a> Jewish French-American viola player and conductor (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sephardic"}]}, {"year": "1876", "text": "<PERSON>, French painter and poet (d. 1958)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and poet (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and poet (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American art collector, critic and poet (d. 1954)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector, critic and poet (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector, critic and poet (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek admiral and historian (d. 1958)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek admiral and historian (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek admiral and historian (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, German rower (d. 1940)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fler\" title=\"<PERSON>\"><PERSON></a>, German rower (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fler\" title=\"<PERSON>\"><PERSON></a>, German rower (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gustav_Go%C3%9Fler"}]}, {"year": "1884", "text": "<PERSON>, Italian priest, founded the Society of St. Paul (d. 1971)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, founded the <a href=\"https://wikipedia.org/wiki/Society_of_St._Paul\" class=\"mw-redirect\" title=\"Society of St. Paul\">Society of St. Paul</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, founded the <a href=\"https://wikipedia.org/wiki/Society_of_St._Paul\" class=\"mw-redirect\" title=\"Society of St. Paul\">Society of St. Paul</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_<PERSON>berione"}, {"title": "Society of St. Paul", "link": "https://wikipedia.org/wiki/Society_of_St._Paul"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Japanese admiral (d. 1943)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American historian and journalist (d. 1964)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and journalist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and journalist (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON> Speaker, American baseball player and manager (d. 1958)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Tri<PERSON>_Speaker\" title=\"Tris Speaker\"><PERSON><PERSON> Speaker</a>, American baseball player and manager (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tri<PERSON>_Speaker\" title=\"Tris Speaker\"><PERSON><PERSON> Speaker</a>, American baseball player and manager (d. 1958)", "links": [{"title": "Tris Speaker", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Speaker"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON><PERSON>, Sr., Polish historian and academic (d. 1975)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Sr.\"><PERSON><PERSON><PERSON><PERSON>, Sr.</a>, Polish historian and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Sr.\"><PERSON><PERSON><PERSON><PERSON>, Sr.</a>, Polish historian and academic (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Sr.", "link": "https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,_<PERSON>."}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian journalist, poet, and playwright (d. 1968)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist, poet, and playwright (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist, poet, and playwright (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Italian painter (d. 1967)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Italo_Mus\" title=\"Italo Mus\"><PERSON><PERSON></a>, Italian painter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italo_Mus\" title=\"Italo Mus\"><PERSON><PERSON></a>, Italian painter (d. 1967)", "links": [{"title": "Italo <PERSON>", "link": "https://wikipedia.org/wiki/Italo_Mus"}]}, {"year": "1892", "text": "<PERSON>, Swedish-Finnish poet (d. 1923)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6dergran\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Swedish-speaking_population_of_Finland\" title=\"Swedish-speaking population of Finland\">Swedish-Finnish</a> poet (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B6dergran\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Swedish-speaking_population_of_Finland\" title=\"Swedish-speaking population of Finland\">Swedish-Finnish</a> poet (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edith_S%C3%B6dergran"}, {"title": "Swedish-speaking population of Finland", "link": "https://wikipedia.org/wiki/Swedish-speaking_population_of_Finland"}]}, {"year": "1895", "text": "<PERSON>, American dancer and educator (d. 1991)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and educator (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and educator (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American playwright and screenwriter (d. 1955)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, French actor and screenwriter (d. 1975)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and screenwriter (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and screenwriter (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American actress (d. 1940)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, German-Israeli botanist and academic (d. 1971)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Israeli botanist and academic (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Israeli botanist and academic (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, French journalist and author (d. 1969)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9v%C3%AAque_de_Vilmorin\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9v%C3%AAque_de_Vilmorin\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louise_L%C3%A9v%C3%AAque_de_Vilmorin"}]}, {"year": "1902", "text": "<PERSON>, American author and poet (d. 1935)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French composer and conductor (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Estonian architect and engineer (d. 1987)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Erik<PERSON>_N%C3%B5va\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect and engineer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erik<PERSON>_N%C3%B5va\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect and engineer (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erika_N%C3%B5va"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Turkish-Irish-American television, radio, and voice actress (d. 1968)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-Irish-American television, radio, and voice actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-Irish-American television, radio, and voice actress (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>a_Benaderet"}]}, {"year": "1906", "text": "<PERSON>, American journalist (d. 1995)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Australian sergeant and politician, 32nd Premier of New South Wales (d. 1981)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sergeant and politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sergeant and politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1910", "text": "Đặng <PERSON><PERSON><PERSON>, Vietnamese physician and academic (d. 1967)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_V%C4%83n_Ng%E1%BB%AF\" title=\"Đặng Văn <PERSON>\">Đặng <PERSON><PERSON><PERSON></a>, Vietnamese physician and academic (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_V%C4%83n_Ng%E1%BB%AF\" title=\"Đặng Văn <PERSON>\">Đặng <PERSON><PERSON><PERSON></a>, Vietnamese physician and academic (d. 1967)", "links": [{"title": "Đặng <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_V%C4%83n_Ng%E1%BB%AF"}]}, {"year": "1913", "text": "<PERSON>, Australian rugby league player (d. 1974)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1913)\" title=\"<PERSON> (rugby league, born 1913)\"><PERSON></a>, Australian rugby league player (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1913)\" title=\"<PERSON> (rugby league, born 1913)\"><PERSON></a>, Australian rugby league player (d. 1974)", "links": [{"title": "<PERSON> (rugby league, born 1913)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1913)"}]}, {"year": "1913", "text": "<PERSON>, American actress and singer (d. 1974)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and singer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and singer (d. 1974)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1913", "text": "<PERSON>, American actress and singer (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian lawyer and politician, 21st Governor General of Canada (d. 1980)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ger\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ger\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jules_L%C3%A9ger"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 1983)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mu<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mu<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 2014)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French novelist, screenwriter, and director (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, screenwriter, and director (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, screenwriter, and director (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian ecologist and botanist (d. 2018)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian ecologist and botanist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian ecologist and botanist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Canadian sculptor (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Serbian general and politician, 10th President of Serbia (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian general and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Serbia\" class=\"mw-redirect\" title=\"List of Presidents of Serbia\">President of Serbia</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian general and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Serbia\" class=\"mw-redirect\" title=\"List of Presidents of Serbia\">President of Serbia</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_Ljubi%C4%8Di%C4%87"}, {"title": "List of Presidents of Serbia", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Serbia"}]}, {"year": "1916", "text": "<PERSON>, American baseball player and coach (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actor (d. 1990)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1990)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1918", "text": "<PERSON>, 2nd Earl <PERSON>, English soldier and politician, Leader of the House of Lords (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a> (d. 2007)", "links": [{"title": "<PERSON>, 2nd <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>"}, {"title": "Leader of the House of Lords", "link": "https://wikipedia.org/wiki/Leader_of_the_House_of_Lords"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON> of Antioch, Greek patriarch (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Antioch\" title=\"<PERSON><PERSON><PERSON> IV of Antioch\"><PERSON><PERSON><PERSON> <PERSON> of Antioch</a>, Greek patriarch (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Antioch\" title=\"<PERSON><PERSON><PERSON> IV of Antioch\"><PERSON><PERSON><PERSON> IV of Antioch</a>, Greek patriarch (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON> of Antioch", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Antioch"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, American-Canadian author and educator (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Orunamamu\" title=\"Orunamamu\"><PERSON><PERSON><PERSON><PERSON></a>, American-Canadian author and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orunamamu\" title=\"Orunama<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American-Canadian author and educator (d. 2014)", "links": [{"title": "Orunamamu", "link": "https://wikipedia.org/wiki/Orunamamu"}]}, {"year": "1921", "text": "<PERSON>, American actress (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American composer and conductor (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actor (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actor, director, producer and screenwriter (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer and screenwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American race car driver (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 2009)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1924", "text": "<PERSON>, American baseball player and manager (d. 1972)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, German footballer and manager (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American basketball player and coach (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian lawyer, judge, and politician (d. 1979)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American poet and author (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and author (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Irish actress (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/1926\" title=\"1926\">1926</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1926\" title=\"1926\">1926</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress (d. 2014)", "links": [{"title": "1926", "link": "https://wikipedia.org/wiki/1926"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Italian-American author and illustrator (d. 1998)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joe Orlando\"><PERSON></a>, Italian-American author and illustrator (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Orlando\" title=\"Joe Orlando\"><PERSON></a>, Italian-American author and illustrator (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American memoirist and poet (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Angelo<PERSON>\"><PERSON></a>, American memoirist and poet (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Angelo<PERSON>\"><PERSON></a>, American memoirist and poet (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Scottish actor, director, and producer (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and producer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and producer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English singer-songwriter and producer (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Norman\"><PERSON></a>, English singer-songwriter and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, American actor (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Indonesian actress (d. 1989)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actress (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actress (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Netty_Herawaty"}]}, {"year": "1931", "text": "<PERSON>, English politician (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American admiral and intelligence officer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and intelligence officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and intelligence officer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, New Zealand politician, 16th Governor-General of New Zealand (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 16th <a href=\"https://wikipedia.org/wiki/List_of_Governors-General_of_New_Zealand\" class=\"mw-redirect\" title=\"List of Governors-General of New Zealand\">Governor-General of New Zealand</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 16th <a href=\"https://wikipedia.org/wiki/List_of_Governors-General_of_New_Zealand\" class=\"mw-redirect\" title=\"List of Governors-General of New Zealand\">Governor-General of New Zealand</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors-General of New Zealand", "link": "https://wikipedia.org/wiki/List_of_Governors-General_of_New_Zealand"}]}, {"year": "1932", "text": "<PERSON>, American record producer, founded Arista Records and J Records", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, founded <a href=\"https://wikipedia.org/wiki/Arista_Records\" title=\"Arista Records\">Arista Records</a> and <a href=\"https://wikipedia.org/wiki/J_Records\" title=\"J Records\">J Records</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, founded <a href=\"https://wikipedia.org/wiki/Arista_Records\" title=\"Arista Records\">Arista Records</a> and <a href=\"https://wikipedia.org/wiki/J_Records\" title=\"J Records\">J Records</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Arista Records", "link": "https://wikipedia.org/wiki/Arista_Records"}, {"title": "J Records", "link": "https://wikipedia.org/wiki/J_Records"}]}, {"year": "1932", "text": "<PERSON>, American lieutenant and politician, 44th Mayor of Indianapolis (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 44th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Indianapolis\" title=\"List of mayors of Indianapolis\">Mayor of Indianapolis</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 44th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Indianapolis\" title=\"List of mayors of Indianapolis\">Mayor of Indianapolis</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Indianapolis", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Indianapolis"}]}, {"year": "1932", "text": "<PERSON>, American actor (d. 1992)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Dutch-American author", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Russian director and producer (d. 1986)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and producer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and producer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American businessman (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American businessman (d. 2007)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1933", "text": "<PERSON>, English runner (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Indian cricketer (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nad<PERSON>ni\" title=\"<PERSON><PERSON> Nadkarni\"><PERSON><PERSON></a>, Indian cricketer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nad<PERSON>ni\" title=\"<PERSON><PERSON> Nadkarni\"><PERSON><PERSON></a>, Indian cricketer (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ni"}]}, {"year": "1934", "text": "<PERSON>, American actress (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Russian journalist and activist (d. 1996)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist and activist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist and activist (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English-New Zealand soldier and politician (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand soldier and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand soldier and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actor and comedian (d. 2011)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English playwright and educator (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and educator (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and educator (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American businessman and academic (d. 1989)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and academic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and academic (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American golfer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American educator and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, South African trumpeter, flugelhornist, cornetist, composer, and singer (d. 2018)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African trumpeter, flugelhornist, cornetist, composer, and singer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African trumpeter, flugelhornist, cornetist, composer, and singer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English race car driver", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter (d. 2002)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Bangladeshi Islamic scholar and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi Islamic scholar and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi Islamic scholar and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American baseball player and manager (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American journalist and biographer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and biographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and biographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Belgian politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Mag<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mag<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Magda_<PERSON><PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Irish journalist, author, and playwright", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist, author, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist, author, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American country music songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Brazilian triple jumper and educator (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AAncio\" title=\"<PERSON>\"><PERSON></a>, Brazilian triple jumper and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AAncio\" title=\"<PERSON>\"><PERSON></a>, Brazilian triple jumper and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Prud%C3%AAncio"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish academician, political commentator, columnist and writer (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Toktam%C4%B1%C5%9F_Ate%C5%9F\" title=\"Toktam<PERSON>ş Ateş\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish academician, political commentator, columnist and writer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toktam%C4%B1%C5%9F_Ate%C5%9F\" title=\"Toktam<PERSON>ş Ateş\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish academician, political commentator, columnist and writer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ateş", "link": "https://wikipedia.org/wiki/Toktam%C4%B1%C5%9F_Ate%C5%9F"}]}, {"year": "1945", "text": "<PERSON>, French-German educator and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actress (d. 2010)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian speed skater", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English guitarist", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(guitarist)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Japanese martial artist and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Katsuaki_Sat%C5%8D"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian author and playwright", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Spir%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Spir%C3%B3\" title=\"<PERSON><PERSON>ö<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and playwright", "links": [{"title": "György <PERSON>", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_Spir%C3%B3"}]}, {"year": "1946", "text": "<PERSON>, American football player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ych<PERSON>\" title=\"<PERSON>ych<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yche"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Indonesian general and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Wiranto\" title=\"Wira<PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian general and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiranto\" title=\"Wira<PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian general and politician", "links": [{"title": "Wira<PERSON>", "link": "https://wikipedia.org/wiki/Wiranto"}]}, {"year": "1947", "text": "<PERSON>, American baseball player and sportscaster (d. 2021)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Filipino minister and television host (d. 2021)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino minister and television host (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino minister and television host (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Turkish activist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96calan\" title=\"<PERSON>\"><PERSON></a>, Turkish activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96calan\" title=\"<PERSON>\"><PERSON></a>, Turkish activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%96calan"}]}, {"year": "1948", "text": "<PERSON>, American bass player (d. 1972)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Oakley\"><PERSON></a>, American bass player (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Berry Oakley\"><PERSON></a>, American bass player (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American lawyer and businessman (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American lawyer and businessman (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American lawyer and businessman (d. 2024)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "1948", "text": "<PERSON>, American author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Northern Irish actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Northern Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Northern Irish actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1948", "text": "<PERSON>, English drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Pick_Withers\" title=\"Pick Withers\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pick_Withers\" title=\"Pick Withers\"><PERSON></a>, English drummer", "links": [{"title": "Pick Withers", "link": "https://wikipedia.org/wiki/Pick_Withers"}]}, {"year": "1949", "text": "<PERSON>, Jamaican-American singer (d. 1999)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>waite\"><PERSON></a>, Jamaican-American singer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Braithwaite\"><PERSON></a>, Jamaican-American singer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Greek singer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Chinese-American mathematician and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Shing-<PERSON>ng_<PERSON>u\" title=\"Shing-Tung Yau\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shing-<PERSON>ng_<PERSON>u\" title=\"Shing-Tung Yau\"><PERSON><PERSON>-<PERSON><PERSON></a>, Chinese-American mathematician and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shing-<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1950", "text": "<PERSON>, American actress and director", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American football player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, German high jumper", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German high jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2010)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian race car driver and motorcycle racer (d. 1995)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and motorcycle racer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and motorcycle racer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, English actress and dancer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Cherie_Lunghi\" title=\"Cherie Lunghi\"><PERSON><PERSON></a>, English actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cherie_Lunghi\" title=\"Cherie Lunghi\"><PERSON><PERSON></a>, English actress and dancer", "links": [{"title": "Cherie Lunghi", "link": "https://wikipedia.org/wiki/Cherie_<PERSON><PERSON>hi"}]}, {"year": "1952", "text": "<PERSON>, Canadian figure skater and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Northern Irish singer-songwriter, guitarist, and producer (d. 2011)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish singer-songwriter, guitarist, and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish singer-songwriter, guitarist, and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Danish educator and politician, Danish Minister of Foreign Affairs", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Villy_S%C3%B8vndal\" title=\"V<PERSON>y <PERSON>øvndal\"><PERSON><PERSON><PERSON></a>, Danish educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_Danish_foreign_ministers\" class=\"mw-redirect\" title=\"List of Danish foreign ministers\">Danish Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Villy_S%C3%B8vndal\" title=\"V<PERSON>y <PERSON>øvndal\"><PERSON><PERSON><PERSON></a>, Danish educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_Danish_foreign_ministers\" class=\"mw-redirect\" title=\"List of Danish foreign ministers\">Danish Minister of Foreign Affairs</a>", "links": [{"title": "Villy <PERSON>ø<PERSON>l", "link": "https://wikipedia.org/wiki/Villy_S%C3%B8vndal"}, {"title": "List of Danish foreign ministers", "link": "https://wikipedia.org/wiki/List_of_Danish_foreign_ministers"}]}, {"year": "1953", "text": "<PERSON>, Canadian politician (d. 2022)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, South African cricketer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Canadian director, producer, journalist, and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian director, producer, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian director, producer, journalist, and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Northern Irish politician, 31st Lord Mayor of Belfast", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Northern Irish politician, 31st <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Belfast\" class=\"mw-redirect\" title=\"Lord Mayor of Belfast\">Lord Mayor of Belfast</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Northern Irish politician, 31st <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Belfast\" class=\"mw-redirect\" title=\"Lord Mayor of Belfast\">Lord Mayor of Belfast</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Lord Mayor of Belfast", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_Belfast"}]}, {"year": "1953", "text": "<PERSON>, Chinese violinist and composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Chinese violinist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Chinese violinist and composer", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1956", "text": "<PERSON>, Canadian ballerina", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ballerina", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ballerina", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American screenwriter and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English cricketer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Finnish director, producer, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>%C3%A4ki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>%C3%A4ki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aki_Kaurism%C3%A4ki"}]}, {"year": "1957", "text": "<PERSON>, Scottish guitarist (d. 2004)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Japanese singer and trumpet player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and trumpet player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and trumpet player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, German bass player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Brazilian singer-songwriter (d. 1990)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Cazuza\" title=\"Cazuza\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cazuza\" title=\"Cazuza\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter (d. 1990)", "links": [{"title": "Cazuza", "link": "https://wikipedia.org/wiki/Cazuza"}]}, {"year": "1958", "text": "<PERSON>, Australian footballer and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1960", "text": "<PERSON>, English cricketer and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English soprano", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "Godk<PERSON>ws <PERSON>, Nigerian diplomat, civil servant and technocrat", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Godknows_Igali\" title=\"Godknows Igali\">Godknows Igali</a>, Nigerian diplomat, civil servant and technocrat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Godknows_Igali\" title=\"Godknows Igali\">Godknows Igali</a>, Nigerian diplomat, civil servant and technocrat", "links": [{"title": "Godknows Igali", "link": "https://wikipedia.org/wiki/Godknows_Igali"}]}, {"year": "1960", "text": "<PERSON>, Nigerian-Australian actor and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Weaving\"><PERSON></a>, Nigerian-Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_We<PERSON>\" title=\"<PERSON> Weaving\"><PERSON></a>, Nigerian-Australian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American interior decorator", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Hild<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American interior decorator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hild<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American interior decorator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hildi_Santo-Tomas"}]}, {"year": "1962", "text": "<PERSON>, English bass player and songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Indian social worker and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian social worker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian social worker and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lash<PERSON>_Devi"}]}, {"year": "1963", "text": "<PERSON><PERSON> <PERSON>, American actor, producer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jack_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2020)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer and broadcaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Irish actor and talk show host", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American drummer and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. Chud\">Dr. <PERSON></a>, American drummer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. Chud\">Dr. <PERSON></a>, American drummer and singer", "links": [{"title": "Dr. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1964", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Japanese surgeon and astronaut", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese surgeon and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese surgeon and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, England international footballer and TV pundit", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, England international footballer and TV pundit", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, England international footballer and TV pundit", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1964", "text": "Đặng <PERSON><PERSON><PERSON>, Vietnamese writer and poet", "html": "1964 - <a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%A2n\" title=\"Đặng Thân\">Đặng <PERSON>h<PERSON></a>, Vietnamese writer and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%A2n\" title=\"Đặng Thân\">Đặng Thân</a>, Vietnamese writer and poet", "links": [{"title": "Đặng Thân", "link": "https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%A2n"}]}, {"year": "1965", "text": "<PERSON><PERSON>, English guitarist and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English guitarist and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor, producer, and screenwriter.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor, producer, and screenwriter.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor, producer, and screenwriter.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1966", "text": "<PERSON>, American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American bass player (d. 2011)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player (d. 2011)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball)\" title=\"<PERSON><PERSON> (basketball)\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball)\" title=\"<PERSON><PERSON> (basketball)\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball)"}]}, {"year": "1967", "text": "<PERSON>, Kenyan-German runner", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-German runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Greek water polo player and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek water polo player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek water polo player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Spanish water polo player (d. 2006)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Roll%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish water polo player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Roll%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish water polo player (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Roll%C3%A1n"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Polish pianist and composer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, English journalist and businesswoman", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and businesswoman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Greek footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Amanatidis"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Greek singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>(producer)"}]}, {"year": "1970", "text": "<PERSON>, Canadian actor and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian tennis player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Russian high jumper", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor, producer, and poet", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American wrestler and promoter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Swedish archaeologist and professor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish archaeologist and professor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish archaeologist and professor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1972", "text": "<PERSON>, Swedish bass player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American football player (d. 2014)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2014)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1973", "text": "<PERSON>, American magician and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Italian motorcycle racer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Dutch footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1973", "text": "<PERSON>, Australian triathlete and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(triathlete)\" title=\"<PERSON> (triathlete)\"><PERSON></a>, Australian triathlete and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(triathlete)\" title=\"<PERSON> (triathlete)\"><PERSON></a>, Australian triathlete and coach", "links": [{"title": "<PERSON> (triathlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(triathlete)"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, French businesswoman", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish skier", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Thobia<PERSON>_<PERSON>\" title=\"Thobia<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thobia<PERSON>_<PERSON>\" title=\"Thobia<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish skier", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thobia<PERSON>_<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Puerto Rican television actress and producer, Miss Puerto Rico 1994", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican television actress and producer, <a href=\"https://wikipedia.org/wiki/Miss_Puerto_Rico\" title=\"Miss Puerto Rico\">Miss Puerto Rico 1994</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican television actress and producer, <a href=\"https://wikipedia.org/wiki/Miss_Puerto_Rico\" title=\"Miss Puerto Rico\">Miss Puerto Rico 1994</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss Puerto Rico", "link": "https://wikipedia.org/wiki/Miss_Puerto_Rico"}]}, {"year": "1975", "text": "<PERSON>, American actress, screenwriter, and author", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, screenwriter, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, screenwriter, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, French race car driver (d. 1997)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Brazilian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American mixed martial artist (d. 2022)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English magician and television host", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English magician and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English magician and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American football player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and scout", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Irish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian actor (d. 2008)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ledger\"><PERSON></a>, Australian actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ledger\"><PERSON></a>, Australian actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ger"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Russian canoeist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ale<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian canoeist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "1980", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 2021)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2021)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>(comedian)"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B6rn_Wirdheim\" title=\"<PERSON><PERSON><PERSON><PERSON>ir<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B6rn_Wirdheim\" title=\"<PERSON><PERSON><PERSON><PERSON> Wirdheim\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B6rn_Wirdheim"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>rensy"}]}, {"year": "1981", "text": "<PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Russian triple jumper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian triple jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American author and screenwriter (d. 2013)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_V<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Viz<PERSON>\" title=\"<PERSON> V<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ned_Vizzini"}]}, {"year": "1982", "text": "<PERSON>, American voice actor and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Swedish chef (d. 2012)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chef)\" class=\"mw-redirect\" title=\"<PERSON> (chef)\"><PERSON></a>, Swedish chef (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(chef)\" class=\"mw-redirect\" title=\"<PERSON> (chef)\"><PERSON></a>, Swedish chef (d. 2012)", "links": [{"title": "<PERSON> (chef)", "link": "https://wikipedia.org/wiki/<PERSON>_(chef)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American comedian", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1983", "text": "<PERSON>, Scottish-English model and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Russian swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_V<PERSON>anin\" title=\"<PERSON><PERSON> Vyatchanin\"><PERSON><PERSON></a>, Russian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>anin\" title=\"<PERSON><PERSON> Vyatchanin\"><PERSON><PERSON></a>, Russian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1985", "text": "<PERSON>, Spanish basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez_(basketball)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Israeli tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>la"}]}, {"year": "1985", "text": "<PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean singer-songwriter and dancer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French skier", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Scottish-born Irish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-born Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-born Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Norwegian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sami_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Kenyan footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mariga\"><PERSON></a>, Kenyan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ga"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Greek footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Anita\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Anita\" title=\"<PERSON><PERSON><PERSON> Anita\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Asia_Muhammad\" title=\"<PERSON> Muhammad\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asia_Muhammad\" title=\"<PERSON> Muhammad\"><PERSON></a>, American tennis player", "links": [{"title": "Asia Muhammad", "link": "https://wikipedia.org/wiki/Asia_Muhammad"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Justin_O%27Neill"}]}, {"year": "1991", "text": "<PERSON>, Venezuelan baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_P%C3%A9<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_P%C3%A9<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_P%C3%A9rez_(baseball)"}]}, {"year": "1991", "text": "<PERSON>, American actress and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Filipino race car driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Marlon_St%C3%B6ckinger\" title=\"<PERSON><PERSON> Stöckinger\"><PERSON><PERSON></a>, Filipino race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marlon_St%C3%B6ckinger\" title=\"<PERSON><PERSON> St<PERSON>ckinger\"><PERSON><PERSON></a>, Filipino race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marlon_St%C3%B6ckinger"}]}, {"year": "1992", "text": "<PERSON>, English actress and singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American YouTuber and singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese singer and actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American singer-songwriter and actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Austin_Mahone\" title=\"Austin Mahone\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Mahone\" title=\"Austin Mahone\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Mahone"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Congolese actor and singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "397", "text": "<PERSON>, Roman archbishop and saint (b. 338)", "html": "397 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman archbishop and saint (b. 338)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman archbishop and saint (b. 338)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "636", "text": "<PERSON><PERSON><PERSON> of Seville, Spanish archbishop and saint (b. 560)", "html": "636 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Seville\" title=\"<PERSON><PERSON><PERSON> of Seville\"><PERSON><PERSON><PERSON> of Seville</a>, Spanish archbishop and saint (b. 560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Seville\" title=\"<PERSON><PERSON><PERSON> of Seville\"><PERSON><PERSON><PERSON> of Seville</a>, Spanish archbishop and saint (b. 560)", "links": [{"title": "<PERSON><PERSON><PERSON> of Seville", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Seville"}]}, {"year": "814", "text": "<PERSON> of <PERSON><PERSON><PERSON><PERSON><PERSON>, Byzantine monk and saint (b. 735)", "html": "814 - <a href=\"https://wikipedia.org/wiki/Plato_of_Sa<PERSON>on\" title=\"Plato of Sakkoudion\">Plato of Sak<PERSON>on</a>, Byzantine monk and saint (b. 735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plato_of_Sa<PERSON>\" title=\"Plato of Sakkoudion\">Plato of Sak<PERSON>on</a>, Byzantine monk and saint (b. 735)", "links": [{"title": "<PERSON> of Sakkoudion", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "896", "text": "<PERSON><PERSON><PERSON>, pope of the Catholic Church (b. 816)", "html": "896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>osus\" title=\"Pope <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, pope of the Catholic Church (b. 816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>os<PERSON>\" title=\"Pope <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, pope of the Catholic Church (b. 816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>osus"}]}, {"year": "911", "text": "<PERSON>, Chinese warlord and governor (b. 874)", "html": "911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Han)\" title=\"<PERSON> (Southern Han)\"><PERSON></a>, Chinese warlord and governor (b. 874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Han)\" title=\"<PERSON> (Southern Han)\"><PERSON></a>, Chinese warlord and governor (b. 874)", "links": [{"title": "<PERSON> (Southern Han)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Han)"}]}, {"year": "931", "text": "<PERSON>, Chinese official and governor (b. 884)", "html": "931 - <a href=\"https://wikipedia.org/wiki/Kong_Xun\" title=\"Kong Xun\">Kong Xun</a>, Chinese official and governor (b. 884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kong_Xun\" title=\"Kong Xun\">Kong Xun</a>, Chinese official and governor (b. 884)", "links": [{"title": "Kong Xun", "link": "https://wikipedia.org/wiki/Kong_Xun"}]}, {"year": "968", "text": "<PERSON>, Arab prince and poet (b. 932)", "html": "968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Hamdani\" title=\"<PERSON>-Hamdani\"><PERSON></a>, Arab prince and poet (b. 932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>Hamdani\" title=\"<PERSON> Firas al-Hamdani\"><PERSON></a>, Arab prince and poet (b. 932)", "links": [{"title": "Abu <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>dan<PERSON>"}]}, {"year": "991", "text": "<PERSON><PERSON><PERSON>, bishop of Eichstätt", "html": "991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Eichst%C3%A4tt\" title=\"<PERSON><PERSON><PERSON> of Eichstätt\"><PERSON><PERSON><PERSON></a>, bishop of Eichstätt", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Eichst%C3%A4tt\" title=\"<PERSON><PERSON><PERSON> of Eichstätt\"><PERSON><PERSON><PERSON></a>, bishop of Eichstätt", "links": [{"title": "<PERSON><PERSON><PERSON> of Eichstätt", "link": "https://wikipedia.org/wiki/Reginold_of_Eichst%C3%A4tt"}]}, {"year": "1284", "text": "<PERSON>, king of Castile and León (b. 1221)", "html": "1284 - <a href=\"https://wikipedia.org/wiki/Alfonso_X_of_Castile\" title=\"<PERSON> of Castile\"><PERSON></a>, king of Castile and León (b. 1221)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_X_of_Castile\" title=\"<PERSON> of Castile\"><PERSON></a>, king of Castile and León (b. 1221)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/Alfonso_X_of_Castile"}]}, {"year": "1292", "text": "<PERSON>, pope of the Catholic Church (b. 1227)", "html": "1292 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"Pope Nicholas IV\"><PERSON> IV</a>, pope of the Catholic Church (b. 1227)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"Pope Nicholas IV\"><PERSON> IV</a>, pope of the Catholic Church (b. 1227)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1406", "text": "<PERSON>, king of Scotland (b. 1337)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON></a>, king of Scotland (b. 1337)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> III</a>, king of Scotland (b. 1337)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1483", "text": "<PERSON>, 1st Earl of Essex (b. c. 1405)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Essex\" title=\"<PERSON>, 1st Earl of Essex\"><PERSON>, 1st Earl of Essex</a> (b. c. 1405)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Essex\" title=\"<PERSON>, 1st Earl of Essex\"><PERSON>, 1st Earl <PERSON> Essex</a> (b. c. 1405)", "links": [{"title": "<PERSON>, 1st Earl of Essex", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Essex"}]}, {"year": "1536", "text": "<PERSON>, Margrave of Brandenburg-Ansbach (b. 1460)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Margrave_of_Brandenburg-Ansbach\" title=\"<PERSON>, Margrave of Brandenburg-Ansbach\"><PERSON>, Margrave of Brandenburg-Ansbach</a> (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Brandenburg-Ansbach\" title=\"<PERSON>, Margrave of Brandenburg-Ansbach\"><PERSON>, Margrave of Brandenburg-Ansbach</a> (b. 1460)", "links": [{"title": "<PERSON>, Margrave of Brandenburg-Ansbach", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Ansbach"}]}, {"year": "1538", "text": "<PERSON>, Grand Princess and regent of Russia", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Princess and regent of Russia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Princess and regent of Russia", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1588", "text": "<PERSON>, king of Denmark and Norway (b. 1534)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> II of Denmark\"><PERSON> II</a>, king of Denmark and Norway (b. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> II of Denmark\"><PERSON> II</a>, king of Denmark and Norway (b. 1534)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>_of_Denmark"}]}, {"year": "1589", "text": "<PERSON> the <PERSON>, Sicilian Franciscan friar and saint (b. 1526) 1", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Moor\"><PERSON> the <PERSON></a>, Sicilian Franciscan friar and saint (b. 1526) <a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttps://www.blackpast.org/global-african-history/people-global-african-history/st-benedict-moor-1526-1589/\">1</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Moor\"><PERSON> the <PERSON></a>, Sicilian Franciscan friar and saint (b. 1526) <a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttps://www.blackpast.org/global-african-history/people-global-african-history/st-benedict-moor-1526-1589/\">1</a>", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1596", "text": "<PERSON>, Duke of Brunswick-<PERSON><PERSON><PERSON>n (b. 1533)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Grubenhagen\" title=\"<PERSON>, Duke of Brunswick-Grubenhagen\"><PERSON>, Duke of Brunswick-Grubenhagen</a> (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Grubenhagen\" title=\"<PERSON>, Duke of Brunswick-Grubenhagen\"><PERSON>, Duke of Brunswick-Grubenhagen</a> (b. 1533)", "links": [{"title": "<PERSON>, Duke of Brunswick-Grubenhagen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-<PERSON>"}]}, {"year": "1609", "text": "<PERSON><PERSON>, Flemish botanist, mycologist, and academic (b. 1526)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish botanist, mycologist, and academic (b. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish botanist, mycologist, and academic (b. 1526)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1617", "text": "<PERSON>, Scottish mathematician, physicist, and astronomer (b. 1550)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician, physicist, and astronomer (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician, physicist, and astronomer (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, Dutch theologian and academic (b. 1583)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian and academic (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian and academic (b. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1661", "text": "<PERSON>, 1st Earl of Leven, Scottish field marshal (b. 1580)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Leven\" title=\"<PERSON>, 1st Earl of Leven\"><PERSON>, 1st Earl of Leven</a>, Scottish field marshal (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Leven\" title=\"<PERSON>, 1st Earl of Leven\"><PERSON>, 1st Earl of Leven</a>, Scottish field marshal (b. 1580)", "links": [{"title": "<PERSON>, 1st Earl of Leven", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_<PERSON>"}]}, {"year": "1743", "text": "<PERSON>, English historian and author (b. 1678)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss painter (b. 1722)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Th<PERSON>od<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, Swiss painter (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Th<PERSON>od<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, Swiss painter (b. 1722)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, English librarian and scholar (b. 1704)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classical_scholar)\" title=\"<PERSON> (classical scholar)\"><PERSON></a>, English librarian and scholar (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classical_scholar)\" title=\"<PERSON> (classical scholar)\"><PERSON></a>, English librarian and scholar (b. 1704)", "links": [{"title": "<PERSON> (classical scholar)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classical_scholar)"}]}, {"year": "1774", "text": "<PERSON>, Irish novelist, playwright and poet (b. 1728)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, playwright and poet (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, playwright and poet (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>smith"}]}, {"year": "1792", "text": "<PERSON>, American lawyer and politician (b. 1725)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)\" title=\"<PERSON> (Continental Congress)\"><PERSON></a>, American lawyer and politician (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)\" title=\"<PERSON> (Continental Congress)\"><PERSON></a>, American lawyer and politician (b. 1725)", "links": [{"title": "<PERSON> (Continental Congress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congress)"}]}, {"year": "1807", "text": "<PERSON><PERSON><PERSON><PERSON>, French astronomer and academic (b. 1732)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON>e\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French astronomer and academic (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON>e\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French astronomer and academic (b. 1732)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_Lalande"}]}, {"year": "1817", "text": "<PERSON>, French general (b. 1758)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Mass%C3%A9na\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Mass%C3%A9na\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Mass%C3%A9na"}]}, {"year": "1841", "text": "<PERSON>, American general and politician, 9th President of the United States (b. 1773)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1846", "text": "<PERSON>, American lawyer and politician, 1st Mayor of Detroit (b. 1769)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Detroit\" title=\"List of mayors of Detroit\">Mayor of Detroit</a> (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Detroit\" title=\"List of mayors of Detroit\">Mayor of Detroit</a> (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Detroit", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Detroit"}]}, {"year": "1861", "text": "<PERSON>, American jurist and politician, 6th United States Postmaster General (b. 1785)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Postmaster General", "link": "https://wikipedia.org/wiki/United_States_Postmaster_General"}]}, {"year": "1863", "text": "<PERSON>, German painter and engraver (b. 1790)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and engraver (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and engraver (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American commander and paleontologist (b. 1808)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and paleontologist (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and paleontologist (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, German chemist and physicist (b. 1802)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physicist (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physicist (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, French archaeologist and politician (b. 1826)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French archaeologist and politician (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French archaeologist and politician (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1875", "text": "<PERSON>, German geographer and explorer (b. 1837)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and explorer (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and explorer (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American criminal (b. 1850)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, German physicist and meteorologist (b. 1803)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and meteorologist (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and meteorologist (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American businessman and philanthropist, founded Cooper Union (b. 1791)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Cooper_Union\" title=\"Cooper Union\">Cooper Union</a> (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Cooper_Union\" title=\"Cooper Union\">Cooper Union</a> (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cooper Union", "link": "https://wikipedia.org/wiki/Cooper_Union"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, 1st Premier of Quebec (b. 1820)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1820)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1890", "text": "<PERSON>, French geologist and academic (b. 1812)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_H%C3%A9bert\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bert\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edmond_H%C3%A9bert"}]}, {"year": "1912", "text": "<PERSON>, American lawyer and politician, 50th Governor of North Carolina (b. 1859)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "1912", "text": "<PERSON>, American minister, lexicographer, and publisher, co-founded Funk & Wagnalls (b. 1839)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, lexicographer, and publisher, co-founded <a href=\"https://wikipedia.org/wiki/Funk_%26_Wagnalls\" title=\"Funk &amp; Wagnalls\">Funk &amp; Wagnalls</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, lexicographer, and publisher, co-founded <a href=\"https://wikipedia.org/wiki/Funk_%26_Wagnalls\" title=\"Funk &amp; Wagnalls\">Funk &amp; Wagnalls</a> (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Funk & Wagnalls", "link": "https://wikipedia.org/wiki/Funk_%26_Wagnalls"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Greek pioneer aviator (b. 1889)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Argyrop<PERSON>los\" title=\"<PERSON><PERSON><PERSON> Argyropoulos\"><PERSON><PERSON><PERSON></a>, Greek pioneer aviator (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Argy<PERSON>los\" title=\"<PERSON><PERSON><PERSON> Argyropoulos\"><PERSON><PERSON><PERSON></a>, Greek pioneer aviator (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_A<PERSON>los"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Greek politician, poet, soldier and sportsman (b. 1869)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, poet, soldier and sportsman (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, poet, soldier and sportsman (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English chemist and physicist (b. 1832)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and physicist (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and physicist (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Portuguese saint (b. 1908)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_and_Francisco_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> and <PERSON>\"><PERSON></a>, Portuguese saint (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_and_Francisco_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> and <PERSON>\"><PERSON></a>, Portuguese saint (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> and Francisco <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_and_Francisco_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English mathematician and philosopher, created the <PERSON><PERSON><PERSON> diagram (b. 1834)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and philosopher, created the <a href=\"https://wikipedia.org/wiki/Venn_diagram\" title=\"Venn diagram\">Venn diagram</a> (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and philosopher, created the <a href=\"https://wikipedia.org/wiki/Venn_diagram\" title=\"Venn diagram\">Venn diagram</a> (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}, {"title": "Venn diagram", "link": "https://wikipedia.org/wiki/Venn_diagram"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Greek painter (b. 1879)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German engineer and businessman, founded Mercedes-Benz (b. 1844)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Karl_Benz\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Mercedes-Benz\" title=\"Mercedes-Benz\">Mercedes-Benz</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karl_Benz\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Mercedes-Benz\" title=\"Mercedes-Benz\">Mercedes-Benz</a> (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mercedes-Benz", "link": "https://wikipedia.org/wiki/Mercedes-Benz"}]}, {"year": "1931", "text": "<PERSON>, French businessman, co-founded the Michelin Tyre Company (b. 1853)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Michelin\" title=\"<PERSON>\"><PERSON></a>, French businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Michelin\" title=\"<PERSON>in\">Michelin Tyre Company</a> (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Michelin\" title=\"<PERSON>\"><PERSON></a>, French businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Michelin\" title=\"<PERSON>in\">Michelin Tyre Company</a> (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>in"}, {"title": "<PERSON>in", "link": "https://wikipedia.org/wiki/<PERSON>in"}]}, {"year": "1932", "text": "<PERSON>, Latvian-German chemist and academic, Nobel Prize laureate (b. 1853)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_<PERSON>wald"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1933", "text": "<PERSON>, American author and educator (b. 1842)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American architect (b. 1878)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Morris_H<PERSON>_<PERSON>\" title=\"Morris H. Whitehouse\"><PERSON></a>, American architect (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morris_H._<PERSON>\" title=\"Morris H. Whitehouse\"><PERSON></a>, American architect (b. 1878)", "links": [{"title": "Morris H. <PERSON>", "link": "https://wikipedia.org/wiki/Morris_H._Whitehouse"}]}, {"year": "1951", "text": "<PERSON>, American religious leader, 8th President of The Church of Jesus Christ of Latter-day Saints (b. 1870)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 8th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 8th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1953", "text": "<PERSON> of Romania (b. 1893)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Romania\" title=\"<PERSON> II of Romania\"><PERSON> of Romania</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Romania\" title=\"<PERSON> II of Romania\"><PERSON> of Romania</a> (b. 1893)", "links": [{"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/Carol_II_of_Romania"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Canadian historian and diplomat (b. 1909)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian and diplomat (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Canadian historian and diplomat (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American soldier and bodyguard (b. 1925)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and bodyguard (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and bodyguard (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Estonian military commander (b. 1912)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian military commander (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian military commander (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Harald_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Romanian mathematician and academic (b. 1873)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stoilow\" title=\"Si<PERSON><PERSON> Stoilow\"><PERSON><PERSON><PERSON></a>, Romanian mathematician and academic (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ow\" title=\"<PERSON><PERSON><PERSON> Stoilow\"><PERSON><PERSON><PERSON></a>, Romanian mathematician and academic (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mion_Stoilow"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Finnish socialist and the Chairman of the Senate of Finland (b. 1873)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish socialist and the Chairman of the Senate of Finland (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish socialist and the Chairman of the Senate of Finland (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_To<PERSON>i"}]}, {"year": "1967", "text": "<PERSON>, American songwriter (b. 1901)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)\" title=\"<PERSON> (lyricist)\"><PERSON></a>, American songwriter (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)\" title=\"<PERSON> (lyricist)\"><PERSON></a>, American songwriter (b. 1901)", "links": [{"title": "<PERSON> (lyricist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer and manager (b. 1898)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>aro<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer and manager (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>aro<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer and manager (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Scarone"}]}, {"year": "1968", "text": "<PERSON>, American minister and activist, Nobel Prize laureate (assassinated) (b. 1929)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American minister and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (<a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"Assassination of <PERSON>.\">assassinated</a>) (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American minister and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (<a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"Assassination of <PERSON>.\">assassinated</a>) (b. 1929)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}, {"title": "Assassination of <PERSON>.", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1972", "text": "<PERSON>, American pastor and politician (b. 1908)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American pastor and politician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American pastor and politician (b. 1908)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1972", "text": "<PERSON>, German-American composer and academic (b. 1902)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and academic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and academic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish engineer and theorist (b. 1889)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish engineer and theorist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish engineer and theorist (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Ukrainian-American journalist, historian, and politician (b. 1893)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American journalist, historian, and politician (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American journalist, historian, and politician (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andrey_Dikiy"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani lawyer and politician, 4th President of Pakistan (b. 1928)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1979", "text": "<PERSON>, American actor (b. 1903)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1917)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Red_Sovine\" title=\"Red Sovine\"><PERSON></a>, American singer-songwriter and guitarist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Sovine\" title=\"Red Sovine\"><PERSON></a>, American singer-songwriter and guitarist (b. 1917)", "links": [{"title": "Red Sovine", "link": "https://wikipedia.org/wiki/Red_Sovine"}]}, {"year": "1983", "text": "<PERSON>, American actress (b. 1899)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Croatian football player, played for 1953 FIFA's \"Rest of the World\" team against England at Wembley (b. 1927)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian football player, played for 1953 FIFA's \"Rest of the World\" team against England at Wembley (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian football player, played for 1953 FIFA's \"Rest of the World\" team against England at Wembley (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Russian-Ukrainian engineer and businessman, founded Antonov Design Bureau (b. 1906)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(aircraft_designer)\" title=\"<PERSON><PERSON> (aircraft designer)\"><PERSON><PERSON></a>, Russian-Ukrainian engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>ov_Design_Bureau\" class=\"mw-redirect\" title=\"Antonov Design Bureau\">Antonov Design Bureau</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(aircraft_designer)\" title=\"<PERSON><PERSON> (aircraft designer)\"><PERSON><PERSON></a>, Russian-Ukrainian engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Antonov_Design_Bureau\" class=\"mw-redirect\" title=\"Antonov Design Bureau\">Antonov Design Bureau</a> (b. 1906)", "links": [{"title": "<PERSON><PERSON> (aircraft designer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(aircraft_designer)"}, {"title": "Antonov Design Bureau", "link": "https://wikipedia.org/wiki/Antonov_Design_Bureau"}]}, {"year": "1985", "text": "<PERSON>, Welsh author and activist (b. 1891)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Welsh author and activist (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Welsh author and activist (b. 1891)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1987", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and academic (b. 1911)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and academic (b. 1911)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Tibetan guru, poet, and scholar (b. 1939)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ch%C3%B6gyam_Trungpa\" title=\"Chögyam Trungpa\"><PERSON><PERSON><PERSON><PERSON></a>, Tibetan guru, poet, and scholar (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ch%C3%B6gyam_Trungpa\" title=\"Chögyam Trungpa\"><PERSON><PERSON><PERSON><PERSON></a>, Tibetan guru, poet, and scholar (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ch%C3%B6gyam_Trungpa"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian journalist and author (b. 1911)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>ch<PERSON>dan<PERSON>_Vats<PERSON>yan\" class=\"mw-redirect\" title=\"<PERSON>ch<PERSON>dan<PERSON> Vatsyayan\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist and author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>yan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Vatsyayan\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist and author (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, German footballer (b. 1920)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Swiss playwright and novelist (b. 1911)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss playwright and novelist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss playwright and novelist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON> <PERSON>, American soldier and politician (b. 1938)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American soldier and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American soldier and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American illustrator (b. 1915)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Canadian actress and director (b. 1918)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rind%27Amour\" title=\"<PERSON><PERSON><PERSON>rind'Amour\"><PERSON><PERSON><PERSON></a>, Canadian actress and director (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rind%27Amour\" title=\"<PERSON><PERSON><PERSON>rin<PERSON>'Amour\"><PERSON><PERSON><PERSON></a>, Canadian actress and director (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yvette_Brind%27Amour"}]}, {"year": "1992", "text": "<PERSON>, Australian footballer (b. 1928)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)\" title=\"<PERSON> (footballer, born 1928)\"><PERSON></a>, Australian footballer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)\" title=\"<PERSON> (footballer, born 1928)\"><PERSON></a>, Australian footballer (b. 1928)", "links": [{"title": "<PERSON> (footballer, born 1928)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter and cellist (b. 1951)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and cellist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and cellist (b. 1951)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1993", "text": "<PERSON>, American game designer, invented <PERSON><PERSON><PERSON> (b. 1899)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, invented <a href=\"https://wikipedia.org/wiki/Scrabble\" title=\"Scrabble\">Scrabble</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, invented <a href=\"https://wikipedia.org/wiki/Scrabble\" title=\"Scrabble\">Scrabble</a> (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Scrabble", "link": "https://wikipedia.org/wiki/Scrabble"}]}, {"year": "1993", "text": "<PERSON>, Canadian radio and television host (b. 1947)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio and television host (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio and television host (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English radio and television host (b. 1944)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1915)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American runner and long jumper (b. 1918)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and long jumper (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and long jumper (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American lieutenant and pilot (b. 1913)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, German-Israeli geologist and academic (b. 1900)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli geologist and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli geologist and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Picard"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish colonel and politician, 39th Deputy Prime Minister of Turkey (b. 1917)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Alparslan_T%C3%BCrke%C5%9F\" title=\"Alparslan Türkeş\">Alparslan Türkeş</a>, Turkish colonel and politician, 39th <a href=\"https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Deputy Prime Ministers of Turkey\">Deputy Prime Minister of Turkey</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alparslan_T%C3%BCrke%C5%9F\" title=\"Alparslan Türkeş\">Alparsla<PERSON>ü<PERSON>ş</a>, Turkish colonel and politician, 39th <a href=\"https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Deputy Prime Ministers of Turkey\">Deputy Prime Minister of Turkey</a> (b. 1917)", "links": [{"title": "Alparslan Türkeş", "link": "https://wikipedia.org/wiki/Alparslan_T%C3%BCrke%C5%9F"}, {"title": "List of Deputy Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American actress, artistic director and producer (b. 1900)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, artistic director and producer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, artistic director and producer (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ille_<PERSON>l"}]}, {"year": "1999", "text": "<PERSON>, American baseball player and sportscaster (b. 1920)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Early_Wynn\" title=\"Early Wynn\"><PERSON> <PERSON><PERSON></a>, American baseball player and sportscaster (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Early_Wynn\" title=\"Early Wynn\">Early <PERSON><PERSON></a>, American baseball player and sportscaster (b. 1920)", "links": [{"title": "Early Wynn", "link": "https://wikipedia.org/wiki/Early_Wynn"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Finnish astronomer (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Oterma\"><PERSON><PERSON></a>, Finnish astronomer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Oterma\"><PERSON><PERSON></a>, Finnish astronomer (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ma"}]}, {"year": "2001", "text": "<PERSON>, American illustrator and engineer (b. 1932)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and engineer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and engineer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American-Canadian academic (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian academic (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1916)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist and coach (b. 1919)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist and coach (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist and coach (b. 1919)", "links": [{"title": "Briek Schotte", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>k_Schotte"}]}, {"year": "2005", "text": "<PERSON>, Canadian businessman and philanthropist (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1941)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English computer scientist and academic (b. 1935)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sp%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sp%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sp%C3%A4<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, South African race car driver (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African race car driver (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African race car driver (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American actress, activist and photographer (b. 1924)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, activist and photographer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, activist and photographer (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American drummer (b. 1956)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Scott Columbus\"><PERSON></a>, American drummer (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American politician, 46th Governor of Tennessee (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Israeli actor, director, and activist (b. 1958)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actor, director, and activist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actor, director, and activist (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON>, American psychologist and academic (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American psychologist and academic (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American psychologist and academic (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Greek pensioner who committed suicide in public (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pensioner who committed suicide in public (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pensioner who committed suicide in public (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Norwegian author and educator (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author and educator (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author and educator (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French director, producer, and screenwriter (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian footballer (b. 1967)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Dubrav<PERSON>_Pavli%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian footballer (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rav<PERSON>_Pavli%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian footballer (b. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dubravko_Pavli%C4%8Di%C4%87"}]}, {"year": "2012", "text": "<PERSON>, Puerto Rican academic and politician, 10th President of the Senate of Puerto Rico (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican academic and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_Puerto_Rico\" title=\"President of the Senate of Puerto Rico\">President of the Senate of Puerto Rico</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican academic and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_Puerto_Rico\" title=\"President of the Senate of Puerto Rico\">President of the Senate of Puerto Rico</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%C3%ADtez"}, {"title": "President of the Senate of Puerto Rico", "link": "https://wikipedia.org/wiki/President_of_the_Senate_of_Puerto_Rico"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Swedish actor, director, and screenwriter (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor, director, and screenwriter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor, director, and screenwriter (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist, critic, and screenwriter (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, critic, and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, critic, and screenwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American illustrator (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Infantino"}]}, {"year": "2013", "text": "<PERSON>, Hungarian-Australian pianist, composer, and conductor (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian pianist, composer, and conductor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian pianist, composer, and conductor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian rugby player and coach (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby player and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby player and coach (b. 1933)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Japanese author (b. 1972)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(author)\" title=\"<PERSON><PERSON><PERSON> (author)\"><PERSON><PERSON><PERSON></a>, Japanese author (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(author)\" title=\"<PERSON><PERSON><PERSON> (author)\"><PERSON><PERSON><PERSON></a>, Japanese author (b. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(author)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Turkish wrestler and trainer (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/%C4%B0smet_Atl%C4%B1\" title=\"İsmet Atlı\"><PERSON><PERSON><PERSON></a>, Turkish wrestler and trainer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0smet_Atl%C4%B1\" title=\"<PERSON>smet Atlı\"><PERSON><PERSON><PERSON></a>, Turkish wrestler and trainer (b. 1931)", "links": [{"title": "İsmet Atlı", "link": "https://wikipedia.org/wiki/%C4%B0smet_Atl%C4%B1"}]}, {"year": "2014", "text": "<PERSON>, American trombonist and producer (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trombonist and producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trombonist and producer (b. 1939)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Bissau-Guinean soldier and politician, President of Guinea-Bissau (b. 1953)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Kumba_Ial%C3%A1\" title=\"Kumba Ialá\"><PERSON><PERSON> Ialá</a>, Bissau-Guinean soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"List of heads of state of Guinea-Bissau\">President of Guinea-Bissau</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kumba_Ial%C3%A1\" title=\"Kumba Ialá\"><PERSON><PERSON> Ialá</a>, Bissau-Guinean soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"List of heads of state of Guinea-Bissau\">President of Guinea-Bissau</a> (b. 1953)", "links": [{"title": "Kumba Ialá", "link": "https://wikipedia.org/wiki/Kumba_Ial%C3%A1"}, {"title": "List of heads of state of Guinea-Bissau", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Guinea-Bissau"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Scottish journalist and politician (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish journalist and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish journalist and politician (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American journalist and author (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Egyptian author and academic (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian author and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian author and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Malaysian engineer and politician (b. 1951)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian engineer and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian engineer and politician (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American sociologist and academic (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Danish author and poet (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and poet (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and poet (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Spanish actress (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lampreave"}]}, {"year": "2024", "text": "<PERSON><PERSON>, British author (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British author (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American Roman Catholic prelate (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic prelate (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic prelate (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American baseball player (b. 1952)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}