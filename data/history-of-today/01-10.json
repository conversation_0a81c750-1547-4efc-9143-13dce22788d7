{"date": "January 10", "url": "https://wikipedia.org/wiki/January_10", "data": {"Events": [{"year": "49 BC", "text": "<PERSON> crosses the Rubicon, signalling the start of civil war.", "html": "49 BC - 49 BC - <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a> crosses the <a href=\"https://wikipedia.org/wiki/Rubicon\" title=\"Rubicon\">Rubicon</a>, signalling the start of <a href=\"https://wikipedia.org/wiki/Caesar%27s_Civil_War\" class=\"mw-redirect\" title=\"Caesar's Civil War\">civil war</a>.", "no_year_html": "49 BC - <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\">Julius Caesar</a> crosses the <a href=\"https://wikipedia.org/wiki/Rubicon\" title=\"Rubicon\">Rubicon</a>, signalling the start of <a href=\"https://wikipedia.org/wiki/Caesar%27s_Civil_War\" class=\"mw-redirect\" title=\"Caesar's Civil War\">civil war</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rubicon", "link": "https://wikipedia.org/wiki/Rubicon"}, {"title": "<PERSON>'s Civil War", "link": "https://wikipedia.org/wiki/Caesar%27s_Civil_War"}]}, {"year": "9", "text": "The Western Han dynasty ends when <PERSON> claims that the divine Mandate of Heaven called for the end of the dynasty and the beginning of his own, the Xin dynasty.", "html": "9 - The Western <a href=\"https://wikipedia.org/wiki/Han_dynasty\" title=\"Han dynasty\">Han dynasty</a> ends when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> claims that the divine <a href=\"https://wikipedia.org/wiki/Mandate_of_Heaven\" title=\"Mandate of Heaven\">Mandate of Heaven</a> called for the end of the dynasty and the beginning of his own, the <a href=\"https://wikipedia.org/wiki/Xin_dynasty\" title=\"Xin dynasty\">Xin dynasty</a>.", "no_year_html": "The Western <a href=\"https://wikipedia.org/wiki/Han_dynasty\" title=\"Han dynasty\">Han dynasty</a> ends when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> claims that the divine <a href=\"https://wikipedia.org/wiki/Mandate_of_Heaven\" title=\"Mandate of Heaven\">Mandate of Heaven</a> called for the end of the dynasty and the beginning of his own, the <a href=\"https://wikipedia.org/wiki/Xin_dynasty\" title=\"Xin dynasty\">Xin dynasty</a>.", "links": [{"title": "Han dynasty", "link": "https://wikipedia.org/wiki/Han_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mandate of Heaven", "link": "https://wikipedia.org/wiki/Mandate_of_Heaven"}, {"title": "Xin dynasty", "link": "https://wikipedia.org/wiki/Xin_dynasty"}]}, {"year": "69", "text": "<PERSON> is appointed by <PERSON><PERSON><PERSON> as deputy Roman Emperor.", "html": "69 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nius_Piso_<PERSON>anus\" class=\"mw-redirect\" title=\"Lucius <PERSON>nius <PERSON>so <PERSON>anus\">Lucius <PERSON></a> is appointed by <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">deputy Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>purnius_Piso_Licinianus\" class=\"mw-redirect\" title=\"Lucius <PERSON>purnius Piso <PERSON>anus\">Lucius <PERSON></a> is appointed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">deputy Roman Emperor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>purnius_Pi<PERSON>_<PERSON>anus"}, {"title": "Galba", "link": "https://wikipedia.org/wiki/Galba"}, {"title": "<PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON>_(title)"}]}, {"year": "236", "text": "Pope <PERSON> succeeds <PERSON><PERSON><PERSON> to become the twentieth pope of Rome.", "html": "236 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fabian\" title=\"Pope Fabian\">Pope <PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/Pope_Anterus\" title=\"Pope Anterus\"><PERSON><PERSON><PERSON></a> to become the twentieth pope of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Fabian\" title=\"Pope Fabian\">Pope <PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/Pope_Anterus\" title=\"Pope Anterus\"><PERSON><PERSON><PERSON></a> to become the twentieth pope of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Anterus"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}]}, {"year": "1072", "text": "<PERSON> conquers Palermo in Sicily for the Normans.", "html": "1072 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> conquers <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a> in Sicily for the Normans.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> conquers <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a> in Sicily for the Normans.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Palermo", "link": "https://wikipedia.org/wiki/Palermo"}]}, {"year": "1430", "text": "<PERSON>, the Duke of Burgundy, establishes the Order of the Golden Fleece, the most prestigious, exclusive, and expensive order of chivalry in the world.", "html": "1430 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Good\" title=\"<PERSON> the Good\"><PERSON> the Good</a>, the <a href=\"https://wikipedia.org/wiki/Duke_of_Burgundy\" title=\"Duke of Burgundy\">Duke of Burgundy</a>, establishes the <a href=\"https://wikipedia.org/wiki/Order_of_the_Golden_Fleece\" title=\"Order of the Golden Fleece\">Order of the Golden Fleece</a>, the most prestigious, exclusive, and expensive <a href=\"https://wikipedia.org/wiki/Order_of_chivalry\" title=\"Order of chivalry\">order of chivalry</a> in the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Good\" title=\"<PERSON> the Good\"><PERSON> the Good</a>, the <a href=\"https://wikipedia.org/wiki/Duke_of_Burgundy\" title=\"Duke of Burgundy\">Duke of Burgundy</a>, establishes the <a href=\"https://wikipedia.org/wiki/Order_of_the_Golden_Fleece\" title=\"Order of the Golden Fleece\">Order of the Golden Fleece</a>, the most prestigious, exclusive, and expensive <a href=\"https://wikipedia.org/wiki/Order_of_chivalry\" title=\"Order of chivalry\">order of chivalry</a> in the world.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Duke of Burgundy", "link": "https://wikipedia.org/wiki/Duke_of_Burgundy"}, {"title": "Order of the Golden Fleece", "link": "https://wikipedia.org/wiki/Order_of_the_Golden_Fleece"}, {"title": "Order of chivalry", "link": "https://wikipedia.org/wiki/Order_of_chivalry"}]}, {"year": "1475", "text": "<PERSON> of Moldavia defeats the Ottoman Empire at the Battle of Vaslui.", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moldavia\" class=\"mw-redirect\" title=\"<PERSON> III of Moldavia\"><PERSON> of Moldavia</a> defeats the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Vaslui\" title=\"Battle of Vaslui\">Battle of Vaslui</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moldavia\" class=\"mw-redirect\" title=\"<PERSON> III of Moldavia\"><PERSON> of Moldavia</a> defeats the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Vaslui\" title=\"Battle of Vaslui\">Battle of Vaslui</a>.", "links": [{"title": "<PERSON> of Moldavia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Moldavia"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Battle of Vaslui", "link": "https://wikipedia.org/wiki/Battle_of_Vaslui"}]}, {"year": "1645", "text": "Archbishop <PERSON> is beheaded for treason at the Tower of London.", "html": "1645 - Archbishop <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is beheaded for treason at the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a>.", "no_year_html": "Archbishop <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is beheaded for treason at the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tower of London", "link": "https://wikipedia.org/wiki/Tower_of_London"}]}, {"year": "1776", "text": "American Revolution: <PERSON> publishes his pamphlet Common Sense.", "html": "1776 - American Revolution: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes his pamphlet <i><a href=\"https://wikipedia.org/wiki/Common_Sense_(pamphlet)\" class=\"mw-redirect\" title=\"Common Sense (pamphlet)\">Common Sense</a></i>.", "no_year_html": "American Revolution: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes his pamphlet <i><a href=\"https://wikipedia.org/wiki/Common_Sense_(pamphlet)\" class=\"mw-redirect\" title=\"Common Sense (pamphlet)\">Common Sense</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Common Sense (pamphlet)", "link": "https://wikipedia.org/wiki/Common_Sense_(pamphlet)"}]}, {"year": "1791", "text": "The Siege of Dunlap's Station begins near Cincinnati during the Northwest Indian War.", "html": "1791 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Dunlap%27s_Station\" title=\"Siege of Dunlap's Station\">Siege of Dunlap's Station</a> begins near <a href=\"https://wikipedia.org/wiki/Cincinnati\" title=\"Cincinnati\">Cincinnati</a> during the <a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Dunlap%27s_Station\" title=\"Siege of Dunlap's Station\">Siege of Dunlap's Station</a> begins near <a href=\"https://wikipedia.org/wiki/Cincinnati\" title=\"Cincinnati\">Cincinnati</a> during the <a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>.", "links": [{"title": "Siege of Dunlap's Station", "link": "https://wikipedia.org/wiki/Siege_of_Dunlap%27s_Station"}, {"title": "Cincinnati", "link": "https://wikipedia.org/wiki/Cincinnati"}, {"title": "Northwest Indian War", "link": "https://wikipedia.org/wiki/Northwest_Indian_War"}]}, {"year": "1812", "text": "The first steamboat on the Ohio River or the Mississippi River arrives in New Orleans, 82 days after departing from Pittsburgh.", "html": "1812 - The <a href=\"https://wikipedia.org/wiki/New_Orleans_(steamboat)\" title=\"New Orleans (steamboat)\">first steamboat</a> on the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a> or the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> arrives in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>, 82 days after departing from <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Orleans_(steamboat)\" title=\"New Orleans (steamboat)\">first steamboat</a> on the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a> or the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> arrives in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>, 82 days after departing from <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>.", "links": [{"title": "New Orleans (steamboat)", "link": "https://wikipedia.org/wiki/New_Orleans_(steamboat)"}, {"title": "Ohio River", "link": "https://wikipedia.org/wiki/Ohio_River"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}]}, {"year": "1861", "text": "American Civil War: Florida becomes the third state to secede from the Union.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> becomes the third state to secede from the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> becomes the third state to secede from the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1863", "text": "The Metropolitan Railway, the world's oldest underground railway, opens between Paddington and Farringdon, marking the beginning of the London Underground.", "html": "1863 - The <a href=\"https://wikipedia.org/wiki/Metropolitan_Railway\" title=\"Metropolitan Railway\">Metropolitan Railway</a>, the world's oldest underground railway, opens between <a href=\"https://wikipedia.org/wiki/Paddington_tube_station_(Circle_and_Hammersmith_%26_City_lines)\" title=\"Paddington tube station (Circle and Hammersmith &amp; City lines)\">Paddington</a> and <a href=\"https://wikipedia.org/wiki/Farringdon_station\" title=\"Farringdon station\">Farringdon</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">London Underground</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Metropolitan_Railway\" title=\"Metropolitan Railway\">Metropolitan Railway</a>, the world's oldest underground railway, opens between <a href=\"https://wikipedia.org/wiki/Paddington_tube_station_(Circle_and_Hammersmith_%26_City_lines)\" title=\"Paddington tube station (Circle and Hammersmith &amp; City lines)\">Paddington</a> and <a href=\"https://wikipedia.org/wiki/Farringdon_station\" title=\"Farringdon station\">Farringdon</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">London Underground</a>.", "links": [{"title": "Metropolitan Railway", "link": "https://wikipedia.org/wiki/Metropolitan_Railway"}, {"title": "Paddington tube station (Circle and Hammersmith & City lines)", "link": "https://wikipedia.org/wiki/Paddington_tube_station_(Circle_and_Hammersmith_%26_City_lines)"}, {"title": "Farringdon station", "link": "https://wikipedia.org/wiki/Farringdon_station"}, {"title": "London Underground", "link": "https://wikipedia.org/wiki/London_Underground"}]}, {"year": "1870", "text": "<PERSON> incorporates Standard Oil.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> incorporates <a href=\"https://wikipedia.org/wiki/Standard_Oil\" title=\"Standard Oil\">Standard Oil</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> incorporates <a href=\"https://wikipedia.org/wiki/Standard_Oil\" title=\"Standard Oil\">Standard Oil</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Standard Oil", "link": "https://wikipedia.org/wiki/Standard_Oil"}]}, {"year": "1876", "text": "The Plan of Tuxtepec is announced.", "html": "1876 - The <a href=\"https://wikipedia.org/wiki/Plan_of_Tuxtepec\" title=\"Plan of Tuxtepec\">Plan of Tuxtepec</a> is announced.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Plan_of_Tuxtepec\" title=\"Plan of Tuxtepec\">Plan of Tuxtepec</a> is announced.", "links": [{"title": "Plan of Tuxtepec", "link": "https://wikipedia.org/wiki/Plan_of_Tuxtepec"}]}, {"year": "1901", "text": "The first great Texas oil gusher is discovered at Spindletop in Beaumont, Texas.", "html": "1901 - The first great <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> <a href=\"https://wikipedia.org/wiki/Blowout_(well_drilling)\" title=\"Blowout (well drilling)\">oil gusher</a> is discovered at <a href=\"https://wikipedia.org/wiki/Spindletop\" title=\"Spindletop\">Spindletop</a> in <a href=\"https://wikipedia.org/wiki/Beaumont,_Texas\" title=\"Beaumont, Texas\">Beaumont, Texas</a>.", "no_year_html": "The first great <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> <a href=\"https://wikipedia.org/wiki/Blowout_(well_drilling)\" title=\"Blowout (well drilling)\">oil gusher</a> is discovered at <a href=\"https://wikipedia.org/wiki/Spindletop\" title=\"Spindletop\">Spindletop</a> in <a href=\"https://wikipedia.org/wiki/Beaumont,_Texas\" title=\"Beaumont, Texas\">Beaumont, Texas</a>.", "links": [{"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Blowout (well drilling)", "link": "https://wikipedia.org/wiki/Blowout_(well_drilling)"}, {"title": "Spindletop", "link": "https://wikipedia.org/wiki/Spindletop"}, {"title": "Beaumont, Texas", "link": "https://wikipedia.org/wiki/Beaumont,_Texas"}]}, {"year": "1901", "text": "New York: Automobile Club of America installs signs on major highways.", "html": "1901 - New York: <a href=\"https://wikipedia.org/wiki/Automobile_Club_of_America\" title=\"Automobile Club of America\">Automobile Club of America</a> installs signs on major highways.", "no_year_html": "New York: <a href=\"https://wikipedia.org/wiki/Automobile_Club_of_America\" title=\"Automobile Club of America\">Automobile Club of America</a> installs signs on major highways.", "links": [{"title": "Automobile Club of America", "link": "https://wikipedia.org/wiki/Automobile_Club_of_America"}]}, {"year": "1916", "text": "World War I: Imperial Russia begins the Erzurum Offensive, leading to the defeat of the Ottoman Empire's Third Army.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Imperial Russia</a> begins the <a href=\"https://wikipedia.org/wiki/Erzurum_Offensive\" class=\"mw-redirect\" title=\"Erzurum Offensive\">Erzurum Offensive</a>, leading to the defeat of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> Third Army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Imperial Russia</a> begins the <a href=\"https://wikipedia.org/wiki/Erzurum_Offensive\" class=\"mw-redirect\" title=\"Erzurum Offensive\">Erzurum Offensive</a>, leading to the defeat of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> Third Army.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Erzurum Offensive", "link": "https://wikipedia.org/wiki/Erzurum_Offensive"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1917", "text": "Imperial Trans-Antarctic Expedition: Seven survivors of the Ross Sea party were rescued after being stranded for several months.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a>: Seven survivors of the <a href=\"https://wikipedia.org/wiki/Ross_Sea_party\" title=\"Ross Sea party\">Ross Sea party</a> were rescued after being stranded for several months.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a>: Seven survivors of the <a href=\"https://wikipedia.org/wiki/Ross_Sea_party\" title=\"Ross Sea party\">Ross Sea party</a> were rescued after being stranded for several months.", "links": [{"title": "Imperial Trans-Antarctic Expedition", "link": "https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition"}, {"title": "Ross Sea party", "link": "https://wikipedia.org/wiki/Ross_Sea_party"}]}, {"year": "1920", "text": "The Treaty of Versailles takes effect, officially ending World War I for all combatant nations except the United States.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a> takes effect, officially ending World War I for all combatant nations except the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a> takes effect, officially ending World War I for all combatant nations except the United States.", "links": [{"title": "Treaty of Versailles", "link": "https://wikipedia.org/wiki/Treaty_of_Versailles"}]}, {"year": "1920", "text": "League of Nations Covenant automatically enters into force after the Treaty of Versailles is ratified by Germany.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/League_of_Nations_Covenant\" class=\"mw-redirect\" title=\"League of Nations Covenant\">League of Nations Covenant</a> automatically enters into force after the <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a> is ratified by <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/League_of_Nations_Covenant\" class=\"mw-redirect\" title=\"League of Nations Covenant\">League of Nations Covenant</a> automatically enters into force after the <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a> is ratified by <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a>.", "links": [{"title": "League of Nations Covenant", "link": "https://wikipedia.org/wiki/League_of_Nations_Covenant"}, {"title": "Treaty of Versailles", "link": "https://wikipedia.org/wiki/Treaty_of_Versailles"}, {"title": "Germany", "link": "https://wikipedia.org/wiki/Germany"}]}, {"year": "1927", "text": "<PERSON>'s futuristic film Metropolis is released in Germany.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s futuristic film <i><a href=\"https://wikipedia.org/wiki/Metropolis_(1927_film)\" title=\"Metropolis (1927 film)\">Metropolis</a></i> is released in Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s futuristic film <i><a href=\"https://wikipedia.org/wiki/Metropolis_(1927_film)\" title=\"Metropolis (1927 film)\">Metropolis</a></i> is released in Germany.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Metropolis (1927 film)", "link": "https://wikipedia.org/wiki/Metropolis_(1927_film)"}]}, {"year": "1941", "text": "World War II: The Greek army captures Kleisoura.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Greek army <a href=\"https://wikipedia.org/wiki/Capture_of_Klisura_Pass\" title=\"Capture of Klisura Pass\">captures</a> <a href=\"https://wikipedia.org/wiki/K%C3%ABlcyr%C3%AB\" title=\"Këlcyrë\">Kleisoura</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Greek army <a href=\"https://wikipedia.org/wiki/Capture_of_Klisura_Pass\" title=\"Capture of Klisura Pass\">captures</a> <a href=\"https://wikipedia.org/wiki/K%C3%ABlcyr%C3%AB\" title=\"Këlcyrë\">Kleisoura</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Capture of Klisura Pass", "link": "https://wikipedia.org/wiki/Capture_of_Klisura_Pass"}, {"title": "Këlcyrë", "link": "https://wikipedia.org/wiki/K%C3%ABlcyr%C3%AB"}]}, {"year": "1946", "text": "The first General Assembly of the United Nations assembles in the Methodist Central Hall, Westminster. Fifty-one nations are represented.", "html": "1946 - The first General Assembly of the United Nations assembles in the <a href=\"https://wikipedia.org/wiki/Methodist_Central_Hall,_Westminster\" title=\"Methodist Central Hall, Westminster\">Methodist Central Hall, Westminster</a>. Fifty-one nations are represented.", "no_year_html": "The first General Assembly of the United Nations assembles in the <a href=\"https://wikipedia.org/wiki/Methodist_Central_Hall,_Westminster\" title=\"Methodist Central Hall, Westminster\">Methodist Central Hall, Westminster</a>. Fifty-one nations are represented.", "links": [{"title": "Methodist Central Hall, Westminster", "link": "https://wikipedia.org/wiki/Methodist_Central_Hall,_Westminster"}]}, {"year": "1946", "text": "The United States Army Signal Corps successfully conducts Project Diana, bouncing radio waves off the Moon and receiving the reflected signals.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Signal_Corps_(United_States_Army)\" class=\"mw-redirect\" title=\"Signal Corps (United States Army)\">United States Army Signal Corps</a> successfully conducts <a href=\"https://wikipedia.org/wiki/<PERSON>_Diana\" title=\"Project Diana\">Project Diana</a>, bouncing <a href=\"https://wikipedia.org/wiki/Radio_wave\" title=\"Radio wave\">radio waves</a> off the Moon and receiving the reflected signals.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Signal_Corps_(United_States_Army)\" class=\"mw-redirect\" title=\"Signal Corps (United States Army)\">United States Army Signal Corps</a> successfully conducts <a href=\"https://wikipedia.org/wiki/<PERSON>_Diana\" title=\"Project Diana\">Project Diana</a>, bouncing <a href=\"https://wikipedia.org/wiki/Radio_wave\" title=\"Radio wave\">radio waves</a> off the Moon and receiving the reflected signals.", "links": [{"title": "Signal Corps (United States Army)", "link": "https://wikipedia.org/wiki/Signal_Corps_(United_States_Army)"}, {"title": "Project Diana", "link": "https://wikipedia.org/wiki/<PERSON>_Diana"}, {"title": "Radio wave", "link": "https://wikipedia.org/wiki/Radio_wave"}]}, {"year": "1954", "text": "BOAC Flight 781, a de Havilland DH.106 Comet 1, explodes and falls into the Tyrrhenian Sea, killing 35 people.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/BOAC_Flight_781\" title=\"BOAC Flight 781\">BOAC Flight 781</a>, a <a href=\"https://wikipedia.org/wiki/De_Havilland_Comet\" title=\"De Havilland Comet\">de Havilland DH.106 Comet 1</a>, explodes and falls into the <a href=\"https://wikipedia.org/wiki/Tyrrhenian_Sea\" title=\"Tyrrhenian Sea\">Tyrrhenian Sea</a>, killing 35 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BOAC_Flight_781\" title=\"BOAC Flight 781\">BOAC Flight 781</a>, a <a href=\"https://wikipedia.org/wiki/De_Havilland_Comet\" title=\"De Havilland Comet\">de Havilland DH.106 Comet 1</a>, explodes and falls into the <a href=\"https://wikipedia.org/wiki/Tyrrhenian_Sea\" title=\"Tyrrhenian Sea\">Tyrrhenian Sea</a>, killing 35 people.", "links": [{"title": "BOAC Flight 781", "link": "https://wikipedia.org/wiki/BOAC_Flight_781"}, {"title": "De Havilland Comet", "link": "https://wikipedia.org/wiki/De_Havilland_Comet"}, {"title": "Tyrrhenian Sea", "link": "https://wikipedia.org/wiki/Tyrrhenian_Sea"}]}, {"year": "1966", "text": "Tashkent Declaration, a peace agreement between India and Pakistan signed that resolved the Indo-Pakistani War of 1965.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Tashkent_Declaration\" title=\"Tashkent Declaration\">Tashkent Declaration</a>, a peace agreement between India and Pakistan signed that resolved the <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1965\">Indo-Pakistani War of 1965</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tashkent_Declaration\" title=\"Tashkent Declaration\">Tashkent Declaration</a>, a peace agreement between India and Pakistan signed that resolved the <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1965\">Indo-Pakistani War of 1965</a>.", "links": [{"title": "Tashkent Declaration", "link": "https://wikipedia.org/wiki/Tashkent_Declaration"}, {"title": "Indo-Pakistani War of 1965", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965"}]}, {"year": "1972", "text": "Sheikh <PERSON><PERSON><PERSON> returns to the newly independent Bangladesh as president after spending over nine months in prison in Pakistan.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON><PERSON>\">Sheikh <PERSON><PERSON><PERSON></a> returns to the newly independent <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> as president after spending over nine months in prison in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON><PERSON>\">Sheikh <PERSON><PERSON><PERSON></a> returns to the newly independent <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> as president after spending over nine months in prison in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "links": [{"title": "Sheikh <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "1980", "text": "The New England Journal of Medicine publishes the letter Addiction Rare in Patients Treated with Narcotics, which is later misused to downplay the general risk of addiction to opioids.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/The_New_England_Journal_of_Medicine\" title=\"The New England Journal of Medicine\">The New England Journal of Medicine</a> publishes the letter <a href=\"https://wikipedia.org/wiki/Addiction_Rare_in_Patients_Treated_with_Narcotics\" title=\"Addiction Rare in Patients Treated with Narcotics\">Addiction Rare in Patients Treated with Narcotics</a>, which is later misused to downplay the general risk of addiction to <a href=\"https://wikipedia.org/wiki/Opioid\" title=\"Opioid\">opioids</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_New_England_Journal_of_Medicine\" title=\"The New England Journal of Medicine\">The New England Journal of Medicine</a> publishes the letter <a href=\"https://wikipedia.org/wiki/Addiction_Rare_in_Patients_Treated_with_Narcotics\" title=\"Addiction Rare in Patients Treated with Narcotics\">Addiction Rare in Patients Treated with Narcotics</a>, which is later misused to downplay the general risk of addiction to <a href=\"https://wikipedia.org/wiki/Opioid\" title=\"Opioid\">opioids</a>.", "links": [{"title": "The New England Journal of Medicine", "link": "https://wikipedia.org/wiki/The_New_England_Journal_of_Medicine"}, {"title": "Addiction Rare in Patients Treated with Narcotics", "link": "https://wikipedia.org/wiki/Addiction_Rare_in_Patients_Treated_with_Narcotics"}, {"title": "Opioid", "link": "https://wikipedia.org/wiki/Opioid"}]}, {"year": "1981", "text": "Salvadoran Civil War: The FMLN launches its first major offensive, gaining control of most of Morazán and Chalatenango departments", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Salvadoran_Civil_War\" title=\"Salvadoran Civil War\">Salvadoran Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Farabundo_Mart%C3%AD_National_Liberation_Front\" title=\"Farabundo Martí National Liberation Front\">FMLN</a> launches its first major offensive, gaining control of most of <a href=\"https://wikipedia.org/wiki/Moraz%C3%A1n_Department\" title=\"Morazán Department\">Morazán</a> and <a href=\"https://wikipedia.org/wiki/Chalatenango_Department\" title=\"Chalatenango Department\">Chalatenango</a> departments", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvadoran_Civil_War\" title=\"Salvadoran Civil War\">Salvadoran Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Farabundo_Mart%C3%AD_National_Liberation_Front\" title=\"Farabundo Martí National Liberation Front\">FMLN</a> launches its first major offensive, gaining control of most of <a href=\"https://wikipedia.org/wiki/Moraz%C3%A1n_Department\" title=\"Morazán Department\">Morazán</a> and <a href=\"https://wikipedia.org/wiki/Chalatenango_Department\" title=\"Chalatenango Department\">Chalatenango</a> departments", "links": [{"title": "Salvadoran Civil War", "link": "https://wikipedia.org/wiki/Salvadoran_Civil_War"}, {"title": "Farabundo Martí National Liberation Front", "link": "https://wikipedia.org/wiki/Farabundo_Mart%C3%AD_National_Liberation_Front"}, {"title": "Morazán Department", "link": "https://wikipedia.org/wiki/Moraz%C3%A1n_Department"}, {"title": "Chalatenango Department", "link": "https://wikipedia.org/wiki/Chalatenango_Department"}]}, {"year": "1984", "text": "Holy See-United States relations: The United States and Holy See (Vatican City) re-establish full diplomatic relations after almost 117 years, overturning the United States Congress's 1867 ban on public funding for such a diplomatic envoy.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Holy_See%E2%80%93United_States_relations\" title=\"Holy See-United States relations\">Holy See-United States relations</a>: The United States and <a href=\"https://wikipedia.org/wiki/Holy_See\" title=\"Holy See\">Holy See</a> (<a href=\"https://wikipedia.org/wiki/Vatican_City\" title=\"Vatican City\">Vatican City</a>) re-establish full diplomatic relations after almost 117 years, overturning the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>'s <a href=\"https://wikipedia.org/wiki/1867\" title=\"1867\">1867</a> ban on public funding for such a diplomatic envoy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Holy_See%E2%80%93United_States_relations\" title=\"Holy See-United States relations\">Holy See-United States relations</a>: The United States and <a href=\"https://wikipedia.org/wiki/Holy_See\" title=\"Holy See\">Holy See</a> (<a href=\"https://wikipedia.org/wiki/Vatican_City\" title=\"Vatican City\">Vatican City</a>) re-establish full diplomatic relations after almost 117 years, overturning the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>'s <a href=\"https://wikipedia.org/wiki/1867\" title=\"1867\">1867</a> ban on public funding for such a diplomatic envoy.", "links": [{"title": "Holy See-United States relations", "link": "https://wikipedia.org/wiki/Holy_See%E2%80%93United_States_relations"}, {"title": "Holy See", "link": "https://wikipedia.org/wiki/Holy_See"}, {"title": "Vatican City", "link": "https://wikipedia.org/wiki/Vatican_City"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "1867", "link": "https://wikipedia.org/wiki/1867"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON> becomes president of Nicaragua and vows to continue the transformation to socialism and alliance with the Soviet Union and Cuba; American policy continues to support the Contras in their revolt against the Nicaraguan government.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">Sandinista</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">president</a> of <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a> and vows to continue the transformation to socialism and alliance with the Soviet Union and Cuba; American policy continues to support the <a href=\"https://wikipedia.org/wiki/Contras\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in their revolt against the Nicaraguan government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">Sandinista</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">president</a> of <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a> and vows to continue the transformation to socialism and alliance with the Soviet Union and Cuba; American policy continues to support the <a href=\"https://wikipedia.org/wiki/Contras\" title=\"Contras\"><PERSON>tras</a> in their revolt against the Nicaraguan government.", "links": [{"title": "Sandinista National Liberation Front", "link": "https://wikipedia.org/wiki/Sandinista_National_Liberation_Front"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}, {"title": "Contras", "link": "https://wikipedia.org/wiki/Contras"}]}, {"year": "1990", "text": "Time Warner is formed by the merger of Time Inc. and Warner Communications.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Time_Warner\" class=\"mw-redirect\" title=\"Time Warner\">Time Warner</a> is formed by the merger of <a href=\"https://wikipedia.org/wiki/Time_Inc.\" title=\"Time Inc.\">Time Inc.</a> and <a href=\"https://wikipedia.org/wiki/Warner_Communications\" class=\"mw-redirect\" title=\"Warner Communications\">Warner Communications</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Time_Warner\" class=\"mw-redirect\" title=\"Time Warner\">Time Warner</a> is formed by the merger of <a href=\"https://wikipedia.org/wiki/Time_Inc.\" title=\"Time Inc.\">Time Inc.</a> and <a href=\"https://wikipedia.org/wiki/Warner_Communications\" class=\"mw-redirect\" title=\"Warner Communications\">Warner Communications</a>.", "links": [{"title": "Time Warner", "link": "https://wikipedia.org/wiki/Time_Warner"}, {"title": "Time Inc.", "link": "https://wikipedia.org/wiki/Time_Inc."}, {"title": "Warner Communications", "link": "https://wikipedia.org/wiki/Warner_Communications"}]}, {"year": "2000", "text": "Crossair Flight 498, a Saab 340 aircraft, crashes in Niederhasli, Switzerland, after taking off from Zurich Airport, killing 13 people.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Crossair_Flight_498\" title=\"Crossair Flight 498\">Crossair Flight 498</a>, a <a href=\"https://wikipedia.org/wiki/Saab_340\" title=\"Saab 340\">Saab 340</a> aircraft, crashes in <a href=\"https://wikipedia.org/wiki/Niederhasli\" title=\"Ni<PERSON><PERSON>has<PERSON>\">Niederhasli</a>, Switzerland, after taking off from <a href=\"https://wikipedia.org/wiki/Zurich_Airport\" title=\"Zurich Airport\">Zurich Airport</a>, killing 13 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crossair_Flight_498\" title=\"Crossair Flight 498\">Crossair Flight 498</a>, a <a href=\"https://wikipedia.org/wiki/Saab_340\" title=\"Saab 340\">Saab 340</a> aircraft, crashes in <a href=\"https://wikipedia.org/wiki/Niederhasli\" title=\"Niederhasli\">Niederhasli</a>, Switzerland, after taking off from <a href=\"https://wikipedia.org/wiki/Zurich_Airport\" title=\"Zurich Airport\">Zurich Airport</a>, killing 13 people.", "links": [{"title": "Crossair Flight 498", "link": "https://wikipedia.org/wiki/Crossair_Flight_498"}, {"title": "Saab 340", "link": "https://wikipedia.org/wiki/Saab_340"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "Zurich Airport", "link": "https://wikipedia.org/wiki/Zurich_Airport"}]}, {"year": "2007", "text": "A general strike begins in Guinea in an attempt to get President <PERSON><PERSON><PERSON> to resign.", "html": "2007 - A <a href=\"https://wikipedia.org/wiki/2007_Guinean_general_strike\" title=\"2007 Guinean general strike\">general strike</a> begins in <a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinea</a> in an attempt to get President <a href=\"https://wikipedia.org/wiki/Lansana_Cont%C3%A9\" title=\"<PERSON>ns<PERSON>\"><PERSON><PERSON><PERSON></a> to resign.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2007_Guinean_general_strike\" title=\"2007 Guinean general strike\">general strike</a> begins in <a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinea</a> in an attempt to get President <a href=\"https://wikipedia.org/wiki/Lansana_Cont%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to resign.", "links": [{"title": "2007 Guinean general strike", "link": "https://wikipedia.org/wiki/2007_Guinean_general_strike"}, {"title": "Guinea", "link": "https://wikipedia.org/wiki/Guinea"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lansana_Cont%C3%A9"}]}, {"year": "2012", "text": "A bombing at Jamrud in Pakistan, kills at least 30 people and injures 78 others.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/2012_Khyber_Agency_bombing\" title=\"2012 Khyber Agency bombing\">bombing</a> at <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>rud\"><PERSON><PERSON></a> in Pakistan, kills at least 30 people and injures 78 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2012_Khyber_Agency_bombing\" title=\"2012 Khyber Agency bombing\">bombing</a> at <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>rud\"><PERSON><PERSON></a> in Pakistan, kills at least 30 people and injures 78 others.", "links": [{"title": "2012 Khyber Agency bombing", "link": "https://wikipedia.org/wiki/2012_Khyber_Agency_bombing"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rud"}]}, {"year": "2013", "text": "More than 100 people are killed and 270 injured in several bomb blasts in the Quetta area of Pakistan.", "html": "2013 - More than 100 people are killed and 270 injured in several <a href=\"https://wikipedia.org/wiki/January_2013_Pakistan_bombings\" title=\"January 2013 Pakistan bombings\">bomb blasts</a> in the <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a> area of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "no_year_html": "More than 100 people are killed and 270 injured in several <a href=\"https://wikipedia.org/wiki/January_2013_Pakistan_bombings\" title=\"January 2013 Pakistan bombings\">bomb blasts</a> in the <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a> area of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "links": [{"title": "January 2013 Pakistan bombings", "link": "https://wikipedia.org/wiki/January_2013_Pakistan_bombings"}, {"title": "Quetta", "link": "https://wikipedia.org/wiki/Quetta"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "2015", "text": "A traffic accident between an oil tanker truck and passenger coach en route to Shikarpur from Karachi on the Pakistan National Highway Link Road near Gulshan-e-Hadeed, Karachi, killing at least 62 people.", "html": "2015 - A <a href=\"https://wikipedia.org/wiki/2015_Karachi_traffic_accident\" title=\"2015 Karachi traffic accident\">traffic accident</a> between an oil tanker truck and passenger coach en route to <a href=\"https://wikipedia.org/wiki/Shikarpur,_Sindh\" title=\"Shikarpur, Sindh\">Shikarpur</a> from <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a> on the <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> <a href=\"https://wikipedia.org/wiki/National_Highways_of_Pakistan\" class=\"mw-redirect\" title=\"National Highways of Pakistan\">National Highway Link Road</a> near <a href=\"https://wikipedia.org/wiki/Gulshan-e-Hadeed\" title=\"Gulshan-e-Hadeed\">Gulshan-e-<PERSON><PERSON></a>, Karachi, killing at least 62 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2015_Karachi_traffic_accident\" title=\"2015 Karachi traffic accident\">traffic accident</a> between an oil tanker truck and passenger coach en route to <a href=\"https://wikipedia.org/wiki/Shikarpur,_Sindh\" title=\"Shikarpur, Sindh\">Shikarpur</a> from <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a> on the <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> <a href=\"https://wikipedia.org/wiki/National_Highways_of_Pakistan\" class=\"mw-redirect\" title=\"National Highways of Pakistan\">National Highway Link Road</a> near <a href=\"https://wikipedia.org/wiki/Gulshan-e-<PERSON>eed\" title=\"Gulshan-e-<PERSON><PERSON>\"><PERSON><PERSON><PERSON>-e-<PERSON><PERSON></a>, Karachi, killing at least 62 people.", "links": [{"title": "2015 Karachi traffic accident", "link": "https://wikipedia.org/wiki/2015_Karachi_traffic_accident"}, {"title": "Shikarpur, Sindh", "link": "https://wikipedia.org/wiki/Shikarpur,_Sindh"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "National Highways of Pakistan", "link": "https://wikipedia.org/wiki/National_Highways_of_Pakistan"}, {"title": "<PERSON>ul<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>-<PERSON>"}]}, {"year": "2019", "text": "A 13-year-old American girl, <PERSON><PERSON>, is found alive in Gordon, Wisconsin, having been kidnapped 88 days earlier from her parents' home whilst they were murdered.", "html": "2019 - A 13-year-old American girl, <a href=\"https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON>_<PERSON>\" title=\"Kidnapping of <PERSON><PERSON>\"><PERSON><PERSON></a>, is found alive in <a href=\"https://wikipedia.org/wiki/<PERSON>,_Douglas_County,_Wisconsin\" title=\"Gordon, Douglas County, Wisconsin\">Gordon, Wisconsin</a>, having been kidnapped 88 days earlier from her parents' home whilst they were murdered.", "no_year_html": "A 13-year-old American girl, <a href=\"https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON>_<PERSON>\" title=\"Kidnapping of <PERSON><PERSON>\"><PERSON><PERSON></a>, is found alive in <a href=\"https://wikipedia.org/wiki/<PERSON>,_Douglas_County,_Wisconsin\" title=\"Gordon, Douglas County, Wisconsin\"><PERSON>, Wisconsin</a>, having been kidnapped 88 days earlier from her parents' home whilst they were murdered.", "links": [{"title": "Kidnapping of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON>_<PERSON>loss"}, {"title": "Gordon, Douglas County, Wisconsin", "link": "https://wikipedia.org/wiki/<PERSON>,_Douglas_County,_Wisconsin"}]}], "Births": [{"year": "626", "text": "<PERSON><PERSON><PERSON> ibn <PERSON>, the third Shia Imam (d. 680)", "html": "626 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, the third Shia Imam (d. 680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, the third Shia Imam (d. 680)", "links": [{"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1480", "text": "<PERSON> of Austria, Duchess of Savoy (d. 1530)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Duchess_of_Savoy\" title=\"<PERSON> of Austria, Duchess of Savoy\"><PERSON> of Austria, Duchess of Savoy</a> (d. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Duchess_of_Savoy\" title=\"<PERSON> of Austria, Duchess of Savoy\"><PERSON> of Austria, Duchess of Savoy</a> (d. 1530)", "links": [{"title": "<PERSON> of Austria, Duchess of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Austria,_Duchess_of_Savoy"}]}, {"year": "1538", "text": "<PERSON> Nassau (d. 1574)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/Louis_of_Nassau\" title=\"<PERSON> of Nassau\"><PERSON> of Nassau</a> (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_of_Nassau\" title=\"<PERSON> of Nassau\"><PERSON> of Nassau</a> (d. 1574)", "links": [{"title": "Louis of Nassau", "link": "https://wikipedia.org/wiki/Louis_of_Nassau"}]}, {"year": "1607", "text": "<PERSON>, French priest and missionary (d. 1646)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and missionary (d. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and missionary (d. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1644", "text": "<PERSON>, duc <PERSON>, French general (d. 1711)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_duc_de_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de Boufflers\"><PERSON>, duc <PERSON></a>, French general (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de Boufflers\"><PERSON>, duc <PERSON></a>, French general (d. 1711)", "links": [{"title": "<PERSON>, duc de Bo<PERSON>lers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>,_duc_<PERSON>_<PERSON>"}]}, {"year": "1654", "text": "<PERSON>, English historian and scholar (d. 1712)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and scholar (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and scholar (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON>, German painter (d. 1762)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON> <PERSON>, German philosopher and theologian (d. 1775)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Christian_August_<PERSON>\" title=\"Christian August <PERSON>\">Christian <PERSON></a>, German philosopher and theologian (d. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_August_<PERSON>\" title=\"Christian August <PERSON>\">Christian <PERSON></a>, German philosopher and theologian (d. 1775)", "links": [{"title": "Christian <PERSON>", "link": "https://wikipedia.org/wiki/Christian_August_<PERSON><PERSON>"}]}, {"year": "1750", "text": "<PERSON>, 1st Baron <PERSON>, Scottish-English lawyer and politician, Lord Chancellor of Great Britain (d. 1823)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of Great Britain</a> (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON><PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of Great Britain</a> (d. 1823)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON><PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1760", "text": "<PERSON>, German composer and conductor (d. 1802)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, French general (d. 1815)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, English physician and academic, founded Birkbeck, University of London (d. 1841)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_University_of_London\" title=\"<PERSON><PERSON><PERSON>, University of London\">B<PERSON><PERSON>, University of London</a> (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_University_of_London\" title=\"<PERSON><PERSON><PERSON>, University of London\"><PERSON><PERSON><PERSON>, University of London</a> (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>, University of London", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_University_of_London"}]}, {"year": "1780", "text": "<PERSON>, German physician and explorer (d. 1857)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/Martin_Lichtenstein\" class=\"mw-redirect\" title=\"Martin Li<PERSON>\"><PERSON></a>, German physician and explorer (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martin_Lichtenstein\" class=\"mw-redirect\" title=\"Martin Lichtenstein\"><PERSON></a>, German physician and explorer (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_Lichtenstein"}]}, {"year": "1802", "text": "<PERSON>, Italian-Austrian engineer, designed the Semmering railway (d. 1860)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian-Austrian engineer, designed the <a href=\"https://wikipedia.org/wiki/Semmering_railway\" title=\"Semmering railway\">Semmering railway</a> (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian-Austrian engineer, designed the <a href=\"https://wikipedia.org/wiki/Semmering_railway\" title=\"Semmering railway\">Semmering railway</a> (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Semmering railway", "link": "https://wikipedia.org/wiki/Semmering_railway"}]}, {"year": "1810", "text": "<PERSON>, French engineer (d. 1892)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, American jurist and politician, 23rd United States Secretary of State (d. 1883)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1810", "text": "<PERSON>, English-Australian politician, 1st Premier of Victoria (d. 1866)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1866)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1823", "text": "<PERSON><PERSON>, Azerbaijani national industrial magnate and philanthropist (d. 1924)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani national industrial magnate and philanthropist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani national industrial magnate and philanthropist (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, Finnish medical reformer (d. 1871)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish medical reformer (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish medical reformer (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, German bishop and missionary (d. 1892)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop and missionary (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop and missionary (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Greek lawyer, journalist and politician, Prime Minister of Greece (d. 1879)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Epameinondas_Deligeorgis\" title=\"Epameinondas Deligeorgis\"><PERSON><PERSON><PERSON><PERSON><PERSON> Deligeorgis</a>, Greek lawyer, journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Epameinondas_Deligeorgis\" title=\"Epameinondas Deligeorgis\"><PERSON><PERSON><PERSON><PERSON><PERSON> Deligeorgis</a>, Greek lawyer, journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1879)", "links": [{"title": "Epameinondas Deligeorgis", "link": "https://wikipedia.org/wiki/Epameinondas_Deligeorgis"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1834", "text": "<PERSON><PERSON>, 1st Baron <PERSON>, Italian-English historian and politician (d. 1902)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Italian-English historian and politician (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Italian-English historian and politician (d. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian cardinal (d. 1925)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Louis-<PERSON>_B%C3%A9gin\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> B<PERSON>gin\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis-<PERSON>_B%C3%A9gin\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> B<PERSON>gin\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-Nazaire_B%C3%A9gin"}]}, {"year": "1842", "text": "<PERSON>, Italian paleontologist, archaeologist, and ethnographer (d. 1925)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian paleontologist, archaeologist, and ethnographer (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian paleontologist, archaeologist, and ethnographer (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American soldier and criminal (d. 1915)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and criminal (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and criminal (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, American merchant and politician, 9th Governor of Nevada (d. 1906)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American merchant and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Nevada\" class=\"mw-redirect\" title=\"List of Governors of Nevada\">Governor of Nevada</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American merchant and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Nevada\" class=\"mw-redirect\" title=\"List of Governors of Nevada\">Governor of Nevada</a> (d. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Governors of Nevada", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Nevada"}]}, {"year": "1849", "text": "<PERSON>, Canadian theosophist, founded the United Lodge of Theosophists (d. 1919)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian theosophist, founded the <a href=\"https://wikipedia.org/wiki/United_Lodge_of_Theosophists\" title=\"United Lodge of Theosophists\">United Lodge of Theosophists</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian theosophist, founded the <a href=\"https://wikipedia.org/wiki/United_Lodge_of_Theosophists\" title=\"United Lodge of Theosophists\">United Lodge of Theosophists</a> (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United Lodge of Theosophists", "link": "https://wikipedia.org/wiki/United_Lodge_of_Theosophists"}]}, {"year": "1850", "text": "<PERSON>, American architect, designed the Rookery Building and Monadnock Building (d. 1891)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Rookery_Building\" title=\"Rookery Building\">Rookery Building</a> and <a href=\"https://wikipedia.org/wiki/Monadnock_Building\" title=\"Monadnock Building\">Monadnock Building</a> (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Rookery_Building\" title=\"Rookery Building\">Rookery Building</a> and <a href=\"https://wikipedia.org/wiki/Monadnock_Building\" title=\"Monadnock Building\">Monadnock Building</a> (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Rookery Building", "link": "https://wikipedia.org/wiki/Rookery_Building"}, {"title": "Monadnock Building", "link": "https://wikipedia.org/wiki/Monadnock_Building"}]}, {"year": "1853", "text": "<PERSON>, mezzo-soprano roles in Gilbert and Sullivan comic operas.(d. 1942)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, mezzo-soprano roles in Gilbert and <PERSON> comic operas.(d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, mezzo-soprano roles in Gilbert and <PERSON> comic operas.(d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Mexican general and politician, 6th Vice President of Mexico (d. 1912)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Corral\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician, 6th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Mexico\" class=\"mw-redirect\" title=\"Vice President of Mexico\">Vice President of Mexico</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Corral\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician, 6th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Mexico\" class=\"mw-redirect\" title=\"Vice President of Mexico\">Vice President of Mexico</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Corral"}, {"title": "Vice President of Mexico", "link": "https://wikipedia.org/wiki/Vice_President_of_Mexico"}]}, {"year": "1858", "text": "<PERSON>, German illustrator and photographer (d. 1929)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German illustrator and photographer (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German illustrator and photographer (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON>, Spanish philosopher and academic (d. 1909)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rrer_i_Gu%C3%A0rdia\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Ferrer i Guàrdia\"><PERSON><PERSON> i Guàrdia</a>, Spanish philosopher and academic (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_i_Gu%C3%A0rdia\" class=\"mw-redirect\" title=\"<PERSON><PERSON> i Guàrdia\"><PERSON><PERSON> i Guàrdia</a>, Spanish philosopher and academic (d. 1909)", "links": [{"title": "Francesc Ferrer i Guàrdia", "link": "https://wikipedia.org/wiki/Francesc_Ferrer_i_Gu%C3%A0rdia"}]}, {"year": "1860", "text": "<PERSON>, Canadian poet and author (d. 1943)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "Grand Duke <PERSON> of Russia (d. 1931)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a> (d. 1931)", "links": [{"title": "Grand Duke <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, English sailor (d. 1948)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English sailor (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Al<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English sailor (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Irish-American baseball player (d. 1935)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Irish-American baseball player (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Irish-American baseball player (d. 1935)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill_(baseball)"}]}, {"year": "1873", "text": "<PERSON>, Canadian runner and hurdler (d. 1958)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner and hurdler (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner and hurdler (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON>, German mathematician and academic (d. 1941)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and academic (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hur"}]}, {"year": "1877", "text": "<PERSON>, American physical chemist, inventor and philanthropist (d. 1948)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physical chemist, inventor and philanthropist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physical chemist, inventor and philanthropist (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American hurdler, football player, and coach (d. 1955)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler, football player, and coach (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler, football player, and coach (d. 1955)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1880", "text": "<PERSON>, Spanish jurist and politician, 7th President of Spain (d. 1940)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Spanish jurist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_(Spain)\" title=\"President of the Republic (Spain)\">President of Spain</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Spanish jurist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_(Spain)\" title=\"President of the Republic (Spain)\">President of Spain</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_<PERSON>za%C3%B1a"}, {"title": "President of the Republic (Spain)", "link": "https://wikipedia.org/wiki/President_of_the_Republic_(Spain)"}]}, {"year": "1883", "text": "<PERSON>, American actor, director, and screenwriter (d. 1966)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, Russian journalist, author, and poet (d. 1945)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist, author, and poet (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist, author, and poet (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American poet and philosopher (d. 1962)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and philosopher (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jeff<PERSON>\"><PERSON></a>, American poet and philosopher (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Italian actress (d. 1984)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German mathematician and academic (d. 1970)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American actress (d. 1978)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, American historian and author (d. 1986)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and author (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, Polish soldier, journalist, and author (d. 1974)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Melchior_Wa%C5%84ko<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier, journalist, and author (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Melchior_Wa%C5%84ko<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier, journalist, and author (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Melchior_Wa%C5%<PERSON><PERSON><PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Australian captain, Victoria Cross recipient (d. 1932)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian captain, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian captain, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Indian poet and author (d. 1972)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and author (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and author (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Australian athletics coach (d. 1975)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian athletics coach (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian athletics coach (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Malaysian watercolour painter (d. 1962)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian watercolour painter (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian watercolour painter (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Indian art collector (d. 1990)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian art collector (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian art collector (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, American physicist and engineer (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and engineer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and engineer (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, English racing driver (d. 1983)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ry\" title=\"<PERSON><PERSON> Cordery\"><PERSON><PERSON></a>, English racing driver (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cordery\"><PERSON><PERSON></a>, English racing driver (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Violette_Cordery"}]}, {"year": "1903", "text": "<PERSON>, English sculptor (d. 1975)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Estonian wrestler (d. 1997)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Voldemar_V%C3%A4li\" title=\"Voldemar Väli\"><PERSON><PERSON><PERSON></a>, Estonian wrestler (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Voldemar_V%C3%A4li\" title=\"Voldemar Väli\"><PERSON>de<PERSON></a>, Estonian wrestler (d. 1997)", "links": [{"title": "Voldemar <PERSON>", "link": "https://wikipedia.org/wiki/Voldemar_V%C3%A4li"}]}, {"year": "1904", "text": "<PERSON>, American actor and dancer (d. 1987)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American engineer and inventor (d. 2003)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and inventor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and inventor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Italian-American actor and director (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor and director (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor and director (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English actor (d. 1981)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French conductor and composer (d. 1976)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Bangladeshi activist (d. 2013)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Binod_Bihari_Chowdhury\" title=\"Binod Bihari Chowdhury\">Binod <PERSON></a>, Bangladeshi activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Binod_Bihari_Chowdhury\" title=\"Binod Bihari Chowdhury\"><PERSON><PERSON> Bihar<PERSON></a>, Bangladeshi activist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Binod_Bihari_Chowdhury"}]}, {"year": "1911", "text": "<PERSON>, English biologist and chemist (d. 2004)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and chemist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and chemist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American Army Air Corps officer (d. 1987)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/United_States_Army_Air_Corps\" title=\"United States Army Air Corps\">Army Air Corps</a> officer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/United_States_Army_Air_Corps\" title=\"United States Army Air Corps\">Army Air Corps</a> officer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Army Air Corps", "link": "https://wikipedia.org/wiki/United_States_Army_Air_Corps"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovak politician, 9th President of Czechoslovakia (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Gust%C3%A1v_Hus%C3%A1k\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gust%C3%A1v_Hus%C3%A1k\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gust%C3%A1v_Hus%C3%A1k"}, {"title": "President of Czechoslovakia", "link": "https://wikipedia.org/wiki/President_of_Czechoslovakia"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Albanian soldier and politician, 22nd Prime Minister of Albania (d. 1981)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian soldier and politician, 22nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Albania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Albania\">Prime Minister of Albania</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian soldier and politician, 22nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Albania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Albania\">Prime Minister of Albania</a> (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "List of Prime Ministers of Albania", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Albania"}]}, {"year": "1914", "text": "<PERSON>, Chinese politician, 23rd Premier of the Republic of China (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wa\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_the_Republic_of_China\" title=\"Premier of the Republic of China\">Premier of the Republic of China</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wa\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_the_Republic_of_China\" title=\"Premier of the Republic of China\">Premier of the Republic of China</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-hwa"}, {"title": "Premier of the Republic of China", "link": "https://wikipedia.org/wiki/Premier_of_the_Republic_of_China"}]}, {"year": "1915", "text": "<PERSON>, American-Swiss conductor (d. 1976)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss conductor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss conductor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American author (d. 1988)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Swedish biochemist and academic, Nobel Prize laureate (d. 2004)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Sune_Bergstr%C3%B6m\" title=\"<PERSON><PERSON>röm\"><PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>e_Bergstr%C3%B6m\" title=\"<PERSON><PERSON>röm\"><PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sune_Bergstr%C3%B6m"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, American painter (d. 2015)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cortor\" title=\"<PERSON><PERSON><PERSON> Cortor\"><PERSON><PERSON><PERSON>rtor</a>, American painter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cortor\" title=\"<PERSON><PERSON>zier Cortor\"><PERSON><PERSON><PERSON></a>, American painter (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON> Cortor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>zier_Cortor"}]}, {"year": "1916", "text": "<PERSON>, Canadian ice hockey player (d. 2007)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2007)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1917", "text": "<PERSON>, American journalist and producer (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English footballer and manager (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Guyanese lawyer and politician, 1st President of Guyana (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Guyana", "link": "https://wikipedia.org/wiki/President_of_Guyana"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 38th <PERSON><PERSON><PERSON><PERSON> (d. 1977)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Terukuni_Manz%C5%8D\" title=\"Terukuni Manzō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 38th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON><PERSON>na\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ruk<PERSON>_Manz%C5%8D\" title=\"Teruk<PERSON> Man<PERSON>ō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 38th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Terukuni_Manz%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1919", "text": "<PERSON>, American businessman, co-founded the Carnegie Deli (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Carnegie_Deli\" title=\"Carnegie Deli\">Carnegie Deli</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Carnegie_Deli\" title=\"Carnegie Deli\">Carnegie Deli</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Carnegie Deli", "link": "https://wikipedia.org/wiki/Carnegie_Deli"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American ballerina (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>tower\"><PERSON><PERSON></a>, American ballerina (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>tower\"><PERSON><PERSON></a>, American ballerina (d. 2008)", "links": [{"title": "<PERSON><PERSON> Hightower", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hightower"}]}, {"year": "1920", "text": "<PERSON>, Argentinian general and politician, 36th President of Argentina (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American aviator, race car driver and sportscaster (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Rodger_Ward\" title=\"Rodger Ward\"><PERSON><PERSON></a>, American aviator, race car driver and sportscaster (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rodger_Ward\" title=\"Rodger Ward\"><PERSON><PERSON></a>, American aviator, race car driver and sportscaster (d. 2004)", "links": [{"title": "Rod<PERSON> Ward", "link": "https://wikipedia.org/wiki/<PERSON>ger_Ward"}]}, {"year": "1922", "text": "<PERSON>, Scottish-English footballer (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American inventor (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ballerina, choreographer, and director (d. 1996)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ballerina, choreographer, and director (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ballerina, choreographer, and director (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>f"}]}, {"year": "1925", "text": "<PERSON>, American financier and businessman (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billie <PERSON>\"><PERSON></a>, American financier and businessman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and businessman (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Palestinian journalist and politician (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Musalla<PERSON>_Bseiso\" title=\"Musalla<PERSON> Bseiso\"><PERSON><PERSON><PERSON></a>, Palestinian journalist and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Musalla<PERSON>_Bseiso\" title=\"Musallam Bseiso\"><PERSON><PERSON><PERSON></a>, Palestinian journalist and politician (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>so", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>so"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Canadian-American singer and actress (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American singer and actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American singer and actress (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (d. 1990)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Swiss lawyer and politician, 140th President of the Swiss Confederation (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 140th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 140th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1928", "text": "<PERSON>, American poet and academic (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and academic (d. 2015)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1928", "text": "<PERSON>, English historian and academic (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American businessman (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English playwright and screenwriter (d. 2004)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English playwright and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English playwright and screenwriter (d. 2004)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_(playwright)"}]}, {"year": "1931", "text": "<PERSON><PERSON>, <PERSON> of St Davids, Grenadian-English academic and politician", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_St_Davids\" title=\"<PERSON><PERSON>, <PERSON> of St Davids\"><PERSON><PERSON>, Baroness <PERSON> of St Davids</a>, Grenadian-English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Baroness_<PERSON>_of_St_Davids\" title=\"<PERSON><PERSON>, <PERSON> of St Davids\"><PERSON><PERSON>, Baroness <PERSON> of St Davids</a>, Grenadian-English academic and politician", "links": [{"title": "<PERSON><PERSON>, Baroness <PERSON> of St Davids", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_St_David<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Malaysian cleric and politician, 12th <PERSON><PERSON><PERSON> of Kelantan (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian cleric and politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_Menteris_Besar_of_Kelantan\" class=\"mw-redirect\" title=\"List of Menteris Besar of Kelantan\"><PERSON><PERSON><PERSON> of Kelantan</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian cleric and politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_Menteris_Besar_of_Kelantan\" class=\"mw-redirect\" title=\"List of Menteris Besar of Kelantan\"><PERSON><PERSON><PERSON> of Kelantan</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of <PERSON><PERSON><PERSON> of Kelantan", "link": "https://wikipedia.org/wiki/List_of_<PERSON><PERSON><PERSON>_Besar_of_Kelantan"}]}, {"year": "1932", "text": "<PERSON>, American college basketball coach (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/College_basketball\" title=\"College basketball\">college basketball</a> coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/College_basketball\" title=\"College basketball\">college basketball</a> coach (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "College basketball", "link": "https://wikipedia.org/wiki/College_basketball"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Ukrainian politician, 1st President of Ukraine (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Ukraine", "link": "https://wikipedia.org/wiki/President_of_Ukraine"}]}, {"year": "1935", "text": "<PERSON>, American rockabilly singer-songwriter and guitarist (d. 2022).", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rockabilly singer-songwriter and guitarist (d. 2022).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rockabilly singer-songwriter and guitarist (d. 2022).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, American opera singer and educator", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American opera singer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American opera singer and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American historian and author (d. 2002)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American physicist and astronomer, Nobel Prize laureate", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Azerbaijani composer (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani composer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani composer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American computer scientist and mathematician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian ice hockey player and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American writer and activist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2012)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actor (d. 1976)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1976)", "links": [{"title": "Sal <PERSON>", "link": "https://wikipedia.org/wiki/Sal_<PERSON>o"}]}, {"year": "1939", "text": "<PERSON>, American athlete", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English geneticist and academic (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian singer and music director", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian singer and music director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian singer and music director", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Scottish politician, Shadow Secretary of State for Scotland", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland\" title=\"Shadow Secretary of State for Scotland\">Shadow Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland\" title=\"Shadow Secretary of State for Scotland\">Shadow Secretary of State for Scotland</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Shadow Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland"}]}, {"year": "1942", "text": "<PERSON>, Australian footballer and coach (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter (d. 1973)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American comics and fantasy artist (d. 2011)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comics and fantasy artist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comics and fantasy artist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Jr., American singer and actor (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American singer and actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American singer and actor (d. 2016)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1945", "text": "<PERSON>, New Zealand-Australian lawyer and politician, 38th Premier of New South Wales (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, New Zealand-Australian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, New Zealand-Australian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 2020)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1945", "text": "<PERSON>, British singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German anatomist, invented plastination", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, German anatomist, invented <a href=\"https://wikipedia.org/wiki/Plastination\" title=\"Plastination\">plastination</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German anatomist, invented <a href=\"https://wikipedia.org/wiki/Plastination\" title=\"Plastination\">plastination</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Plastination", "link": "https://wikipedia.org/wiki/Plastination"}]}, {"year": "1947", "text": "<PERSON>, American author (d. 2002)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American opera singer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass-baritone)\" title=\"<PERSON> (bass-baritone)\"><PERSON></a>, American opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass-baritone)\" title=\"<PERSON> (bass-baritone)\"><PERSON></a>, American opera singer", "links": [{"title": "<PERSON> (bass-baritone)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass-baritone)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, German politician, German Minister of Finance", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Peer_<PERSON>r%C3%BCck\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_German_finance_ministers\" title=\"List of German finance ministers\">German Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P<PERSON>_<PERSON>r%C3%BCck\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_German_finance_ministers\" title=\"List of German finance ministers\">German Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peer_Steinbr%C3%BCck"}, {"title": "List of German finance ministers", "link": "https://wikipedia.org/wiki/List_of_German_finance_ministers"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Estonian engineer and politician, 11th Prime Minister of Estonia", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Tiit_V%C3%A4hi\" title=\"Tiit V<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian engineer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiit_V%C3%A4hi\" title=\"Tiit V<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian engineer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "links": [{"title": "Tiit Vähi", "link": "https://wikipedia.org/wiki/Tiit_V%C3%A4hi"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Finnish musician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>n"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and musician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, French cyclist and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Th%C3%A9venet\" title=\"<PERSON>\"><PERSON></a>, French cyclist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9venet\" title=\"<PERSON>\"><PERSON></a>, French cyclist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_Th%C3%A9venet"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Turkish economist and politician, Turkish Minister of Economy (d. 2023)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Economy_(Turkey)\" title=\"Ministry of Economy (Turkey)\">Turkish Minister of Economy</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Economy_(Turkey)\" title=\"Ministry of Economy (Turkey)\">Turkish Minister of Economy</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kemal_Dervi%C5%9F"}, {"title": "Ministry of Economy (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Economy_(Turkey)"}]}, {"year": "1949", "text": "<PERSON>, American boxer, actor, and businessman", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer, actor, and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer, actor, and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American pornographic actress and activist (d. 2002)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pornographic actress and activist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pornographic actress and activist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_Benatar"}]}, {"year": "1953", "text": "<PERSON>, American race car driver", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Azerbaijani writer, poet and translator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Baba_Vaziroglu\" title=\"Baba Vaziroglu\"><PERSON>aziroglu</a>, Azerbaijani writer, poet and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baba_Vaziroglu\" title=\"Baba Vaziroglu\"><PERSON>azi<PERSON></a>, Azerbaijani writer, poet and translator", "links": [{"title": "Baba Vaziroglu", "link": "https://wikipedia.org/wiki/Baba_Vaziroglu"}]}, {"year": "1955", "text": "<PERSON>, German musician and songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Spanish author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz_<PERSON>lina\" title=\"<PERSON>\"><PERSON></a>, Spanish author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz_<PERSON>lina\" title=\"<PERSON>\"><PERSON></a>, Spanish author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_Mu%C3%B1oz_<PERSON>lina"}]}, {"year": "1959", "text": "<PERSON>, American sprinter and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American lawyer and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, New Zealand screenwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand screenwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Kenyan-English director, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan-English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan-English director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Irish lawyer and politician, 12th Taoiseach of Ireland", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>tier\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>elletier\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>tier"}]}, {"year": "1960", "text": "<PERSON>, British game designer and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British game designer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British game designer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Italian-American violinist, author, and educator", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Nadja_Salerno-Sonnenberg\" title=\"Nadja <PERSON>-Sonnenberg\">Nad<PERSON>-<PERSON></a>, Italian-American violinist, author, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nadja_Salerno-Sonnenberg\" title=\"Nadja <PERSON>-Sonnenberg\">Nad<PERSON>-<PERSON></a>, Italian-American violinist, author, and educator", "links": [{"title": "Nadja <PERSON>-Sonnenberg", "link": "https://wikipedia.org/wiki/Nadja_Salerno-Sonnenberg"}]}, {"year": "1962", "text": "<PERSON>, Canadian lawyer and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American computer scientist and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, New Zealand-Australian footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Russian figure skater (d. 2001)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Singaporean actress and model", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German businessperson", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessperson", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessperson", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Serbian chess player and politician, Serbian Minister of Youth and Sports", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian chess player and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Youth_and_Sports_(Serbia)\" class=\"mw-redirect\" title=\"Ministry of Youth and Sports (Serbia)\">Serbian Minister of Youth and Sports</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian chess player and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Youth_and_Sports_(Serbia)\" class=\"mw-redirect\" title=\"Ministry of Youth and Sports (Serbia)\">Serbian Minister of Youth and Sports</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alisa_Mari%C4%87"}, {"title": "Ministry of Youth and Sports (Serbia)", "link": "https://wikipedia.org/wiki/Ministry_of_Youth_and_Sports_(Serbia)"}]}, {"year": "1972", "text": "<PERSON>, Moroccan-Dutch journalist, poet, and author", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan-Dutch journalist, poet, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan-Dutch journalist, poet, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Puerto Rican boxer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Trinidad\" title=\"Félix <PERSON>\"><PERSON></a>, Puerto Rican boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Trinidad\" title=\"Félix <PERSON>\"><PERSON></a>, Puerto Rican boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Trinidad"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, New Zealand comedian, actor, and musician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand comedian, actor, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand comedian, actor, and musician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Italian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Belgian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Indian actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Malaysian politician, Malaysian Minister of Health", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Malaysia)\" title=\"Ministry of Health (Malaysia)\">Malaysian Minister of Health</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Malaysia)\" title=\"Ministry of Health (Malaysia)\">Malaysian Minister of Health</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Health (Malaysia)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Malaysia)"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player (d. 2023)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer and songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Austrian politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>l<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swedish ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%88a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%88a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%88a"}]}, {"year": "1981", "text": "<PERSON>, American real estate investor and political figure", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate investor and political figure", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate investor and political figure", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Polish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Moroccan footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, German high jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian-Swedish ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Belgian tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football coach", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Brazilian swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Cielo\" title=\"<PERSON>\"><PERSON></a>, Brazilian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Cielo"}]}, {"year": "1988", "text": "<PERSON>, Kenyan runner", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Egyptian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>uke"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, German ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Baker\" title=\"<PERSON><PERSON> Baker\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Baker\" title=\"<PERSON><PERSON> Baker\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> Baker", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Baker"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Egyptian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, New Zealand rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Mason_Mount\" title=\"Mason Mount\"><PERSON> Mount</a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mason_Mount\" title=\"Mason Mount\"><PERSON></a>, English footballer", "links": [{"title": "Mason Mount", "link": "https://wikipedia.org/wiki/Mason_Mount"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(French_footballer)\" title=\"<PERSON><PERSON><PERSON> (French footballer)\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(French_footballer)\" title=\"<PERSON><PERSON><PERSON> (French footballer)\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (French footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_(French_footballer)"}]}, {"year": "2000", "text": "<PERSON>, Norwegian footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Rene%C3%A9_Rapp\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rene%C3%A9_Rapp\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rene%C3%A9_Rapp"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Spanish basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>a"}]}], "Deaths": [{"year": "259", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman saint", "html": "259 - <a href=\"https://wikipedia.org/wiki/<PERSON>ye<PERSON><PERSON>\" title=\"<PERSON>ye<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Roman saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>ye<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Roman saint", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Polyeuctus"}]}, {"year": "314", "text": "<PERSON><PERSON><PERSON><PERSON>, pope of the Catholic Church", "html": "314 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Miltia<PERSON>\" title=\"Pope Miltiades\"><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Miltia<PERSON>\" title=\"Pope Miltiades\"><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Miltiades"}]}, {"year": "681", "text": "<PERSON><PERSON><PERSON>, pope of the Catholic Church", "html": "681 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"Pope Agatho\"><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"Pope A<PERSON>ho\"><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "976", "text": "<PERSON>, Byzantine emperor (b. 925)", "html": "976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "987", "text": "<PERSON>, doge of Venice (b. 928)", "html": "987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of Venice (b. 928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of Venice (b. 928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1055", "text": "<PERSON><PERSON><PERSON>, duke of Bohemia", "html": "1055 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON></a>, duke of Bohemia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON></a>, duke of Bohemia", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1094", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Egyptian caliph (b. 1029)", "html": "1094 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Al-Must<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Egyptian caliph (b. 1029)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-Mustansir_<PERSON>\" title=\"Al-Mustansir Billah\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Egyptian caliph (b. 1029)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Must<PERSON><PERSON>_<PERSON>"}]}, {"year": "1218", "text": "<PERSON>, king of Cyprus", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON></a>, king of Cyprus", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON></a>, king of Cyprus", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus"}]}, {"year": "1276", "text": "<PERSON>, pope of the Catholic Church (b. c.1210)", "html": "1276 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. c.1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. c.1210)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1322", "text": "<PERSON><PERSON>, scholastic philosopher", "html": "1322 - <a href=\"https://wikipedia.org/wiki/Pet<PERSON>_Aureolus\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Aureolus\"><PERSON><PERSON></a>, scholastic philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Aureolus\" class=\"mw-redirect\" title=\"Pet<PERSON> Aureolus\"><PERSON><PERSON></a>, scholastic philosopher", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Aureolus"}]}, {"year": "1358", "text": "<PERSON>, Marinid ruler of Morocco (b. 1329)", "html": "1358 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Abu Inan Faris\"><PERSON></a>, <PERSON><PERSON> ruler of <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a> (b. 1329)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Abu Inan Faris\"><PERSON></a>, <PERSON><PERSON> ruler of <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a> (b. 1329)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}]}, {"year": "1552", "text": "<PERSON>, German humanist and controversialist (b. 1479)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and controversialist (b. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and controversialist (b. 1479)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "<PERSON>, English archbishop and academic (b. 1573)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (b. 1573)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1654", "text": "<PERSON>, English botanist, physician, and astrologer (b. 1616)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist, physician, and astrologer (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist, physician, and astrologer (b. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1698", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French priest and historian (b. 1637)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/Louis-S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French priest and historian (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis-S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French priest and historian (b. 1637)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, English publisher, founded The Gentleman's Magazine (b. 1691)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/Edward_Cave\" title=\"Edward Cave\">Edward Cave</a>, English publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_Gentleman%27s_Magazine\" title=\"The Gentleman's Magazine\">The Gentleman's Magazine</a></i> (b. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edward_Cave\" title=\"Edward Cave\">Edward Cave</a>, English publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_Gentleman%27s_Magazine\" title=\"The Gentleman's Magazine\">The Gentleman's Magazine</a></i> (b. 1691)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edward_<PERSON>"}, {"title": "The Gentleman's Magazine", "link": "https://wikipedia.org/wiki/The_Gentleman%27s_Magazine"}]}, {"year": "1761", "text": "<PERSON>, English admiral and politician (b. 1711)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1711)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, Swedish botanist and physician (b. 1707)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Linnaeus\"><PERSON></a>, Swedish botanist and physician (b. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Linnaeus\" title=\"<PERSON> Linnaeus\"><PERSON></a>, Swedish botanist and physician (b. 1707)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, German-Polish ethnologist and journalist (b. 1754)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Polish ethnologist and journalist (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Polish ethnologist and journalist (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, French poet, playwright, and politician (b. 1764)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nier\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French poet, playwright, and politician (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nier\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French poet, playwright, and politician (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ch%C3%A9nier"}]}, {"year": "1824", "text": "<PERSON>, duke of Savoy and king of Sardinia (b. 1759)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON></a>, duke of Savoy and king of Sardinia (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON></a>, duke of Savoy and king of Sardinia (b. 1759)", "links": [{"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sardinia"}]}, {"year": "1828", "text": "<PERSON>, French poet, academic, and politician, French Minister of the Interior (b. 1750)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Neufch%C3%A2teau\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French poet, academic, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(France)\" class=\"mw-redirect\" title=\"Minister of the Interior (France)\">French Minister of the Interior</a> (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Neufch%C3%A2teau\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French poet, academic, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(France)\" class=\"mw-redirect\" title=\"Minister of the Interior (France)\">French Minister of the Interior</a> (b. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_de_Neufch%C3%A2teau"}, {"title": "Minister of the Interior (France)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(France)"}]}, {"year": "1829", "text": "<PERSON><PERSON>, Argentinian clergyman, historian, and educator (b. 1749)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian clergyman, historian, and educator (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian clergyman, historian, and educator (b. 1749)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gregor<PERSON>_Funes"}]}, {"year": "1843", "text": "<PERSON><PERSON>, Greek-Romanian captain and politician (b. 1780)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-Romanian captain and politician (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-Romanian captain and politician (b. 1780)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, Prussian field marshal (b. 1775)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCffling\" title=\"<PERSON>\"><PERSON></a>, Prussian field marshal (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCffling\" title=\"<PERSON>\"><PERSON></a>, Prussian field marshal (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCffling"}]}, {"year": "1855", "text": "<PERSON>, English author and playwright (b. 1787)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American engineer and businessman, founded Colt's Manufacturing Company (b. 1814)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Samuel Colt\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Colt%27s_Manufacturing_Company\" title=\"Colt's Manufacturing Company\">Colt's Manufacturing Company</a> (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Samuel_<PERSON>\" title=\"Samuel Colt\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Colt%27s_Manufacturing_Company\" title=\"Colt's Manufacturing Company\">Colt's Manufacturing Company</a> (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Colt's Manufacturing Company", "link": "https://wikipedia.org/wiki/Colt%27s_Manufacturing_Company"}]}, {"year": "1863", "text": "<PERSON><PERSON>, American minister and activist, co-founded the American Temperance Society (b. 1775)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Beecher\"><PERSON><PERSON></a>, American minister and activist, co-founded the <a href=\"https://wikipedia.org/wiki/American_Temperance_Society\" title=\"American Temperance Society\">American Temperance Society</a> (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Beecher\"><PERSON><PERSON></a>, American minister and activist, co-founded the <a href=\"https://wikipedia.org/wiki/American_Temperance_Society\" title=\"American Temperance Society\">American Temperance Society</a> (b. 1775)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yman_Beecher"}, {"title": "American Temperance Society", "link": "https://wikipedia.org/wiki/American_Temperance_Society"}]}, {"year": "1895", "text": "<PERSON>, French violinist and composer (b. 1849)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, English-Australian businessman and politician, 1st Australian Minister for Defence (b. 1832)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_politician)\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, English-Australian businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Queensland_politician)\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, English-Australian businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (b. 1832)", "links": [{"title": "<PERSON> (Queensland politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_politician)"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, French painter and sculptor (b. 1824)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9on_G%C3%A9r%C3%B4me\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and sculptor (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9on_G%C3%A9r%C3%B4me\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and sculptor (b. 1824)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9on_G%C3%A9r%C3%B4me"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian composer (b. 1835)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/K%C4%81rl<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian composer (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C4%81rl<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian composer (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C4%81rl<PERSON>_<PERSON><PERSON>is"}]}, {"year": "1917", "text": "<PERSON>, American soldier and hunter (b. 1846)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Buffalo_Bill\" title=\"Buffalo Bill\"><PERSON></a>, American soldier and hunter (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buffalo_Bill\" title=\"Buffalo Bill\"><PERSON></a>, American soldier and hunter (b. 1846)", "links": [{"title": "Buffalo Bill", "link": "https://wikipedia.org/wiki/Buffalo_Bill"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Russian fencer and captain (b. 1875)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian fencer and captain (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian fencer and captain (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Albanian journalist and politician (b. 1890)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian journalist and politician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian journalist and politician (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Australian politician, 6th Australian Minister for Trade and Investment (b. 1866)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Minister_for_Trade_and_Investment_(Australia)\" class=\"mw-redirect\" title=\"Minister for Trade and Investment (Australia)\">Australian Minister for Trade and Investment</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Minister_for_Trade_and_Investment_(Australia)\" class=\"mw-redirect\" title=\"Minister for Trade and Investment (Australia)\">Australian Minister for Trade and Investment</a> (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Trade and Investment (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Trade_and_Investment_(Australia)"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Finnish poet and journalist (b. 1878)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish poet and journalist (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish poet and journalist (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian tennis player and runner (b. 1873)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and runner (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and runner (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English cricketer and footballer (b. 1871)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English viola player and composer (b. 1879)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bridge\"><PERSON></a>, English viola player and composer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank Bridge\"><PERSON></a>, English viola player and composer (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Irish painter and academic (b. 1856)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and academic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and academic (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Belarusian-German mathematician and academic (b. 1875)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-German mathematician and academic (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-German mathematician and academic (b. 1875)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hur"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Finnish politician (b. 1871)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Matti_Turkia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matti_Turkia\" title=\"<PERSON>i Tu<PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matti_Turkia"}]}, {"year": "1949", "text": "<PERSON>, German geographer and geophysicist (b. 1865)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and geophysicist (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and geophysicist (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American novelist, short-story writer, and playwright, Nobel Prize laureate (b. 1885)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short-story writer, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short-story writer, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Japanese physicist and academic (b. 1890)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1954", "text": "<PERSON>, American journalist and historian (b. 1911)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Chester_Wilmot\" title=\"<PERSON> Wilmot\"><PERSON></a>, American journalist and historian (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chester_Wilmot\" title=\"<PERSON> Wilmot\"><PERSON></a>, American journalist and historian (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chester_Wilmot"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American geographer and geologist (b. 1862)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Zonia_Baber\" title=\"Zonia Baber\"><PERSON><PERSON></a>, American geographer and geologist (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zonia_Baber\" title=\"Zonia Baber\"><PERSON><PERSON></a>, American geographer and geologist (b. 1862)", "links": [{"title": "Zonia Baber", "link": "https://wikipedia.org/wiki/Zonia_Baber"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Chilean poet and academic, Nobel Prize laureate (b. 1889)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean poet and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean poet and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish jurist and politician, Turkish Minister of Foreign Affairs (b. 1883)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Kaya\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs (Turkey)\">Turkish Minister of Foreign Affairs</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Kaya\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs (Turkey)\">Turkish Minister of Foreign Affairs</a> (b. 1883)", "links": [{"title": "Şükrü <PERSON>", "link": "https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Kaya"}, {"title": "List of Ministers of Foreign Affairs (Turkey)", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player, coach, and manager (b. 1879)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American detective novelist and screenwriter (b. 1894)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American detective novelist and screenwriter (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American detective novelist and screenwriter (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American painter (b. 1893)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Turkish general and politician, 6th Speaker of the Parliament of Turkey (b. 1882)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish general and politician, 6th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Turkey\" class=\"mw-redirect\" title=\"Speaker of the Parliament of Turkey\">Speaker of the Parliament of Turkey</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish general and politician, 6th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Turkey\" class=\"mw-redirect\" title=\"Speaker of the Parliament of Turkey\">Speaker of the Parliament of Turkey</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Speaker of the Parliament of Turkey", "link": "https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Turkey"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian educator and politician, 2nd Governor of Rajasthan (b. 1891)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Sampurnanand\" title=\"Sampurnanand\">Sampurnan<PERSON></a>, Indian educator and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Rajasthan\" class=\"mw-redirect\" title=\"Governor of Rajasthan\">Governor of Rajasthan</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sampurnanand\" title=\"Sampurnanand\">Sampurnanand</a>, Indian educator and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Rajasthan\" class=\"mw-redirect\" title=\"Governor of Rajasthan\">Governor of Rajasthan</a> (b. 1891)", "links": [{"title": "Sampurnanand", "link": "https://wikipedia.org/wiki/Sampurnanand"}, {"title": "Governor of Rajasthan", "link": "https://wikipedia.org/wiki/Governor_of_Rajasthan"}]}, {"year": "1970", "text": "<PERSON>, Russian pilot and astronaut (b. 1925)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot and astronaut (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot and astronaut (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, French fashion designer, founded <PERSON><PERSON> (b. 1883)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chanel\" title=\"Coco Chanel\"><PERSON><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chan<PERSON>\" title=\"Coco Chanel\"><PERSON><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>co_Chanel"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Italian racing driver (b. 1941)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Danish lawyer and politician (b. 1897)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish lawyer and politician (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish lawyer and politician (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON> <PERSON>, American singer-songwriter and guitarist (b. 1910)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Howlin%27_<PERSON>\" title=\"Howlin' <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American singer-songwriter and guitarist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Howlin%27_<PERSON>\" title=\"Howlin' <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American singer-songwriter and guitarist (b. 1910)", "links": [{"title": "Howlin' Wolf", "link": "https://wikipedia.org/wiki/How<PERSON>%27_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Nicaraguan journalist and author (b. 1924)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Chamorro_Cardenal\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan journalist and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Chamorro_Cardenal\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan journalist and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Chamorro_Cardenal"}]}, {"year": "1978", "text": "<PERSON>, American composer and conductor (b. 1912)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and conductor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and conductor (b. 1912)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1978", "text": "<PERSON>, British painter (b. 1895)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(painter)\" title=\"<PERSON><PERSON> (painter)\"><PERSON></a>, British painter (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(painter)\" title=\"<PERSON><PERSON> (painter)\"><PERSON></a>, British painter (b. 1895)", "links": [{"title": "<PERSON><PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(painter)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American historian and author (b. 1915)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>awn_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>aw<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American historian and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>awn_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>aw<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American historian and author (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>awn_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Laotian politician, 8th Prime Minister of Laos (b. 1901)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Laotian politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Laos\" title=\"Prime Minister of Laos\">Prime Minister of Laos</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Laotian politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Laos\" title=\"Prime Minister of Laos\">Prime Minister of Laos</a> (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sou<PERSON>_<PERSON>"}, {"title": "Prime Minister of Laos", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Laos"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Czech journalist and poet, Nobel Prize laureate (b. 1901)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech journalist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech journalist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1987", "text": "<PERSON>, American singer (b. 1919)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English businessman and philanthropist (b. 1904)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, English businessman and philanthropist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, English businessman and philanthropist (b. 1904)", "links": [{"title": "<PERSON> (philanthropist)", "link": "https://wikipedia.org/wiki/<PERSON>_(philanthropist)"}]}, {"year": "1989", "text": "<PERSON>, American journalist and producer (b. 1905)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(announcer)\" class=\"mw-redirect\" title=\"<PERSON> (announcer)\"><PERSON></a>, American journalist and producer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(announcer)\" class=\"mw-redirect\" title=\"<PERSON> (announcer)\"><PERSON></a>, American journalist and producer (b. 1905)", "links": [{"title": "<PERSON> (announcer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(announcer)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 44th <PERSON><PERSON><PERSON><PERSON> (b. 1925)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 44th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 44th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON><PERSON>na\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/To<PERSON><PERSON><PERSON>_Kiyotaka"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1992", "text": "<PERSON>, Argentinian racing driver (b. 1919)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Canadian-English journalist, author, and screenwriter (b. 1937)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English journalist, author, and screenwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English journalist, author, and screenwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Kenyan-English journalist and author (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan-English journalist and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan-English journalist and author (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sp<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor, director, and producer (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, <PERSON>, Scottish biochemist and academic, Nobel Prize laureate (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, Scottish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, Scottish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1907)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1999", "text": "<PERSON>, Australian lieutenant, pilot, and judge (b. 1921)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_judge)\" title=\"<PERSON> (Queensland judge)\"><PERSON></a>, Australian lieutenant, pilot, and judge (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_judge)\" title=\"<PERSON> (Queensland judge)\"><PERSON></a>, Australian lieutenant, pilot, and judge (b. 1921)", "links": [{"title": "<PERSON> (Queensland judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_judge)"}]}, {"year": "2000", "text": "<PERSON>, American screenwriter and producer (b. 1901)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American screenwriter and producer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American screenwriter and producer (b. 1901)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American actor and screenwriter (b. 1941)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Spalding_Gray\" title=\"Spalding Gray\"><PERSON><PERSON></a>, American actor and screenwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spalding_Gray\" title=\"Spalding Gray\"><PERSON><PERSON></a>, American actor and screenwriter (b. 1941)", "links": [{"title": "Spalding Gray", "link": "https://wikipedia.org/wiki/Spalding_Gray"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Canadian bishop (b. 1909)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Fedak)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Fedak)\"><PERSON><PERSON><PERSON></a>, Ukrainian-Canadian bishop (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Fedak)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Fedak)\"><PERSON><PERSON><PERSON></a>, Ukrainian-Canadian bishop (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON> (Fedak)", "link": "https://wikipedia.org/wiki/Wasyly_(Fedak)"}]}, {"year": "2005", "text": "<PERSON>, American journalist (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (b. 1912)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "2005", "text": "Princess <PERSON><PERSON> of Belgium (b. 1927)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_of_Belgium\" class=\"mw-redirect\" title=\"Princess <PERSON><PERSON> of Belgium\">Princess <PERSON><PERSON> of Belgium</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_of_Belgium\" class=\"mw-redirect\" title=\"Princess <PERSON><PERSON> of Belgium\">Princess <PERSON><PERSON> of Belgium</a> (b. 1927)", "links": [{"title": "Princess <PERSON><PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>_of_Belgium"}]}, {"year": "2007", "text": "<PERSON>, Italian film producer (b. 1912)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian film producer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian film producer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American explorer, photographer, and cartographer (b. 1910)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Bradford_Washburn\" title=\"Bradford Washburn\"><PERSON></a>, American explorer, photographer, and cartographer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bradford_Washburn\" title=\"Bradford Washburn\"><PERSON></a>, American explorer, photographer, and cartographer (b. 1910)", "links": [{"title": "Bradford Washburn", "link": "https://wikipedia.org/wiki/Bradford_Washburn"}]}, {"year": "2008", "text": "<PERSON>, American figure skater and actor (b. 1967)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and actor (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and actor (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Finnish-American actress, producer, and screenwriter (b. 1922)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ur<PERSON>\"><PERSON><PERSON></a>, Finnish-American actress, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>urmi\"><PERSON><PERSON></a>, Finnish-American actress, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Indian metallurgist, educator and administrator (b. 1942)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian metallurgist, educator and administrator (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian metallurgist, educator and administrator (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American singer (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian businesswoman and politician (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Russian intelligence agent (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Gevork_Vartanian\" title=\"Gevork Vartanian\">Gevork Vartanian</a>, Russian intelligence agent (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gevork_Vartanian\" title=\"Gevork Vartanian\">Gevork Vartanian</a>, Russian intelligence agent (b. 1924)", "links": [{"title": "Gevork Vartanian", "link": "https://wikipedia.org/wiki/Gevork_Vartanian"}]}, {"year": "2013", "text": "<PERSON>, Swiss pianist and composer (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pianist and composer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pianist and composer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Swiss businessman, founded the Montreux Jazz Festival (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss businessman, founded the <a href=\"https://wikipedia.org/wiki/Montreux_Jazz_Festival\" title=\"Montreux Jazz Festival\">Montreux Jazz Festival</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss businessman, founded the <a href=\"https://wikipedia.org/wiki/Montreux_Jazz_Festival\" title=\"Montreux Jazz Festival\">Montreux Jazz Festival</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montreux Jazz Festival", "link": "https://wikipedia.org/wiki/Montreux_Jazz_Festival"}]}, {"year": "2014", "text": "<PERSON>, American activist (b. 1996)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Czech shoemaker and academic (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Petr_Hlav%C3%A1%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech shoemaker and academic (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petr_Hlav%C3%A1%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech shoemaker and academic (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petr_Hlav%C3%A1%C4%8Dek"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish economist and politician, 9th Prime Minister of the Republic of Poland (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish economist and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of the Republic of Poland</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish economist and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of the Republic of Poland</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Poland", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland"}]}, {"year": "2014", "text": "<PERSON>, American journalist, 16th White House Press Secretary (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 16th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 16th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian jeweller (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Dajika<PERSON>_Gadgil\" title=\"Dajikaka Gadgil\"><PERSON><PERSON><PERSON><PERSON></a>, Indian jeweller (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dajika<PERSON>_Gadgil\" title=\"Dajikaka Gadgil\"><PERSON><PERSON><PERSON><PERSON></a>, Indian jeweller (b. 1915)", "links": [{"title": "Dajikaka Gadgil", "link": "https://wikipedia.org/wiki/Dajikaka_Gadgil"}]}, {"year": "2015", "text": "<PERSON>, Belgian footballer (b. 1994)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Junior Malanda\"><PERSON></a>, Belgian footballer (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Junior Malanda\"><PERSON></a>, Belgian footballer (b. 1994)", "links": [{"title": "Junior <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actor, playwright, and painter (b. 1957)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Negron\"><PERSON></a>, American actor, playwright, and painter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Negron\"><PERSON></a>, American actor, playwright, and painter (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "2015", "text": "<PERSON>, Italian director and screenwriter (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>i\"><PERSON></a>, Italian director and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>i\"><PERSON></a>, Italian director and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American novelist and short story writer (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American novelist and short story writer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American novelist and short story writer (b. 1937)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "2016", "text": "<PERSON>, English singer-songwriter, producer, and actor (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer, and actor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer, and actor (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Norwegian sculptor and art instructor (b. 1948)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/B%C3%A5rd_Breivik\" title=\"Bård Breivik\"><PERSON><PERSON><PERSON></a>, Norwegian sculptor and art instructor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A5rd_Breivik\" title=\"Bård Breivik\"><PERSON><PERSON><PERSON></a>, Norwegian sculptor and art instructor (b. 1948)", "links": [{"title": "B<PERSON>rd Breivik", "link": "https://wikipedia.org/wiki/B%C3%A5rd_Breivik"}]}, {"year": "2016", "text": "<PERSON>, Hungarian-Canadian journalist, author, and poet (b. 1935)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Canadian journalist, author, and poet (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Canadian journalist, author, and poet (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American jazz and pop singer and pianist (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Greco\"><PERSON></a>, American jazz and pop singer and pianist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Greco\"><PERSON></a>, American jazz and pop singer and pianist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, English journalist (b. 1911)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, ruler of Oman (b. 1940)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, ruler of Oman (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, ruler of Oman (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American television personality (b. 1934)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American real estate heir and convicted murderer (b. 1943)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate heir and convicted murderer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate heir and convicted murderer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, English guitarist and songwriter (b. 1944)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON> of Greece, King of Greece (1964-1973) (b. 1940)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Constantine_II_of_Greece\" title=\"Constantine II of Greece\"><PERSON> of Greece</a>, King of Greece (1964-1973) (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_II_of_Greece\" title=\"<PERSON> II of Greece\"><PERSON> of Greece</a>, King of Greece (1964-1973) (b. 1940)", "links": [{"title": "Constantine II of Greece", "link": "https://wikipedia.org/wiki/Constantine_II_of_Greece"}]}, {"year": "2025", "text": "<PERSON>, Puerto Rican activist (b. 1948)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Jim%C3%A9<PERSON><PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Puerto Rican activist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>%C3%A9<PERSON><PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Puerto Rican activist (b. 1948)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>%C3%A9ne<PERSON>_(activist)"}]}, {"year": "2025", "text": "<PERSON>, American football player and coach (b. 1940)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American soul singer-songwriter (b. 1935)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer-songwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer-songwriter (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}