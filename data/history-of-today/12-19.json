{"date": "December 19", "url": "https://wikipedia.org/wiki/December_19", "data": {"Events": [{"year": "1154", "text": "<PERSON> of England is crowned at Westminster Abbey.", "html": "1154 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> is crowned at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> is crowned at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_England"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1187", "text": "<PERSON> is elected.", "html": "1187 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_III\" title=\"<PERSON> Clement III\">Pope <PERSON></a> is elected.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_<PERSON>\" title=\"<PERSON> Clement III\"><PERSON></a> is elected.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1490", "text": "<PERSON>, Duchess of Brittany, is married to <PERSON>, Holy Roman Emperor by proxy.", "html": "1490 - <a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Brittany\" title=\"<PERSON> of Brittany\"><PERSON>, Duchess of Brittany</a>, is married to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> I, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> by <a href=\"https://wikipedia.org/wiki/Proxy_marriage\" title=\"Proxy marriage\">proxy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Brittany\"><PERSON>, Duchess of Brittany</a>, is married to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> by <a href=\"https://wikipedia.org/wiki/Proxy_marriage\" title=\"Proxy marriage\">proxy</a>.", "links": [{"title": "<PERSON> of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brittany"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Proxy marriage", "link": "https://wikipedia.org/wiki/Proxy_marriage"}]}, {"year": "1562", "text": "The Battle of Dreux takes place during the French Wars of Religion.", "html": "1562 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Dreux\" title=\"Battle of Dreux\">Battle of Dreux</a> takes place during the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Dreux\" title=\"Battle of Dreux\">Battle of Dreux</a> takes place during the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "links": [{"title": "Battle of Dreux", "link": "https://wikipedia.org/wiki/Battle_of_Dreux"}, {"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}]}, {"year": "1606", "text": "The ships <PERSON>, Gods<PERSON>ed, and Discovery depart England carrying settlers who founded, at Jamestown, Virginia, the first of the thirteen colonies that became the United States.", "html": "1606 - The ships <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><i><PERSON></i></a>, <a href=\"https://wikipedia.org/wiki/Godspeed_(ship)\" title=\"Godspeed (ship)\"><i>Godspeed</i></a>, and <a href=\"https://wikipedia.org/wiki/Discovery_(1602_ship)\" title=\"Discovery (1602 ship)\"><i>Discovery</i></a> depart England carrying settlers who founded, at <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown, Virginia</a>, the first of the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">thirteen colonies</a> that became the United States.", "no_year_html": "The ships <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><i><PERSON></i></a>, <a href=\"https://wikipedia.org/wiki/Godspeed_(ship)\" title=\"Godspeed (ship)\"><i>Godspeed</i></a>, and <a href=\"https://wikipedia.org/wiki/Discovery_(1602_ship)\" title=\"Discovery (1602 ship)\"><i>Discovery</i></a> depart England carrying settlers who founded, at <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown, Virginia</a>, the first of the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">thirteen colonies</a> that became the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Godspeed (ship)", "link": "https://wikipedia.org/wiki/Godspeed_(ship)"}, {"title": "Discovery (1602 ship)", "link": "https://wikipedia.org/wiki/Discovery_(1602_ship)"}, {"title": "Jamestown, Virginia", "link": "https://wikipedia.org/wiki/Jamestown,_Virginia"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}]}, {"year": "1675", "text": "The Great Swamp Fight, a pivotal battle in <PERSON>'s War, gives the English settlers a bitterly won victory.", "html": "1675 - The <a href=\"https://wikipedia.org/wiki/Great_Swamp_Fight\" title=\"Great Swamp Fight\">Great Swamp Fight</a>, a pivotal battle in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_War\" title=\"King <PERSON>'s War\">King <PERSON>'s War</a>, gives the English settlers a bitterly won victory.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Swamp_Fight\" title=\"Great Swamp Fight\">Great Swamp Fight</a>, a pivotal battle in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_War\" title=\"King <PERSON>'s War\">King <PERSON>'s War</a>, gives the English settlers a bitterly won victory.", "links": [{"title": "Great Swamp Fight", "link": "https://wikipedia.org/wiki/Great_Swamp_Fight"}, {"title": "King <PERSON>'s War", "link": "https://wikipedia.org/wiki/King_<PERSON>%27s_War"}]}, {"year": "1776", "text": "<PERSON> publishes one of a series of pamphlets in The Pennsylvania Journal entitled \"The American Crisis\".", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes one of a series of pamphlets in <i><a href=\"https://wikipedia.org/wiki/The_Pennsylvania_Journal\" title=\"The Pennsylvania Journal\">The Pennsylvania Journal</a></i> entitled \"<a href=\"https://wikipedia.org/wiki/The_American_Crisis\" title=\"The American Crisis\">The American Crisis</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes one of a series of pamphlets in <i><a href=\"https://wikipedia.org/wiki/The_Pennsylvania_Journal\" title=\"The Pennsylvania Journal\">The Pennsylvania Journal</a></i> entitled \"<a href=\"https://wikipedia.org/wiki/The_American_Crisis\" title=\"The American Crisis\">The American Crisis</a>\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Pennsylvania Journal", "link": "https://wikipedia.org/wiki/The_Pennsylvania_Journal"}, {"title": "The American Crisis", "link": "https://wikipedia.org/wiki/The_American_Crisis"}]}, {"year": "1777", "text": "American Revolutionary War: <PERSON>'s Continental Army goes into winter quarters at Valley Forge, Pennsylvania.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> goes into winter quarters at <a href=\"https://wikipedia.org/wiki/Valley_Forge\" title=\"Valley Forge\">Valley Forge, Pennsylvania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> goes into winter quarters at <a href=\"https://wikipedia.org/wiki/Valley_Forge\" title=\"Valley Forge\">Valley Forge, Pennsylvania</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Valley Forge", "link": "https://wikipedia.org/wiki/Valley_Forge"}]}, {"year": "1783", "text": "<PERSON> the Younger becomes the youngest Prime Minister of the United Kingdom at 24.", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a> becomes the youngest <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> at 24.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a> becomes the youngest <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> at 24.", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1793", "text": "War of the First Coalition: The Siege of Toulon ends when <PERSON>'s French artillery forces the British to abandon the city, securing southern France from invasion.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Toulon\" class=\"mw-redirect\" title=\"Siege of Toulon\">Siege of Toulon</a> ends when <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a>'s French artillery forces the British to abandon the city, securing southern France from invasion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Toulon\" class=\"mw-redirect\" title=\"Siege of Toulon\">Siege of Toulon</a> ends when <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a>'s French artillery forces the British to abandon the city, securing southern France from invasion.", "links": [{"title": "War of the First Coalition", "link": "https://wikipedia.org/wiki/War_of_the_First_Coalition"}, {"title": "Siege of Toulon", "link": "https://wikipedia.org/wiki/Siege_of_Toulon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1796", "text": "French Revolutionary Wars: Two British frigates under Commodore <PERSON><PERSON><PERSON> and two Spanish frigates under Commodore <PERSON> engage in battle off the coast of Murcia.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: Two British frigates under Commodore <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON><PERSON></a> and two Spanish frigates under Commodore <PERSON> <a href=\"https://wikipedia.org/wiki/Action_of_19_December_1796\" title=\"Action of 19 December 1796\">engage in battle</a> off the coast of <a href=\"https://wikipedia.org/wiki/Murcia\" title=\"Murcia\">Murcia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: Two British frigates under Commodore <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON><PERSON></a> and two Spanish frigates under Commodore <PERSON> <a href=\"https://wikipedia.org/wiki/Action_of_19_December_1796\" title=\"Action of 19 December 1796\">engage in battle</a> off the coast of <a href=\"https://wikipedia.org/wiki/Murcia\" title=\"Murcia\">Murcia</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rat<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Action of 19 December 1796", "link": "https://wikipedia.org/wiki/Action_of_19_December_1796"}, {"title": "Murcia", "link": "https://wikipedia.org/wiki/Murcia"}]}, {"year": "1828", "text": "Vice President of the United States <PERSON> sparks the Nullification Crisis when he anonymously publishes the South Carolina Exposition and Protest, protesting the Tariff of 1828.", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sparks the <a href=\"https://wikipedia.org/wiki/Nullification_Crisis\" class=\"mw-redirect\" title=\"Nullification Crisis\">Nullification Crisis</a> when he anonymously publishes the <i><a href=\"https://wikipedia.org/wiki/South_Carolina_Exposition_and_Protest\" title=\"South Carolina Exposition and Protest\">South Carolina Exposition and Protest</a></i>, protesting the <a href=\"https://wikipedia.org/wiki/Tariff_of_1828\" class=\"mw-redirect\" title=\"Tariff of 1828\">Tariff of 1828</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sparks the <a href=\"https://wikipedia.org/wiki/Nullification_Crisis\" class=\"mw-redirect\" title=\"Nullification Crisis\">Nullification Crisis</a> when he anonymously publishes the <i><a href=\"https://wikipedia.org/wiki/South_Carolina_Exposition_and_Protest\" title=\"South Carolina Exposition and Protest\">South Carolina Exposition and Protest</a></i>, protesting the <a href=\"https://wikipedia.org/wiki/Tariff_of_1828\" class=\"mw-redirect\" title=\"Tariff of 1828\">Tariff of 1828</a>.", "links": [{"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nullification Crisis", "link": "https://wikipedia.org/wiki/Nullification_Crisis"}, {"title": "South Carolina Exposition and Protest", "link": "https://wikipedia.org/wiki/South_Carolina_Exposition_and_Protest"}, {"title": "Tariff of 1828", "link": "https://wikipedia.org/wiki/Tariff_of_1828"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>: The first Governor-General of Australia <PERSON>, 7th Earl of Hopetoun, appoints Sir <PERSON> premier of the new state of New South Wales, but he is unable to persuade other colonial politicians to join his government and is forced to resign.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Hopetoun_Blunder\" title=\"Hopetoun Blunder\">Hopetoun Blunder</a>: The first <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> <a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_<PERSON>_Hope<PERSON>un\" title=\"<PERSON>, 7th Earl of Hopetoun\"><PERSON>, 7th Earl <PERSON> Hopetoun</a>, appoints Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> premier of the new state of <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>, but he is unable to persuade other colonial politicians to join his government and is forced to resign.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hopetoun_Blunder\" title=\"Hopetoun Blunder\">Hopetoun Blunder</a>: The first <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> <a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_<PERSON>_Hope<PERSON>\" title=\"<PERSON>, 7th Earl of Hopetoun\"><PERSON>, 7th Earl of Hopetoun</a>, appoints Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> premier of the new state of <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>, but he is unable to persuade other colonial politicians to join his government and is forced to resign.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Blunder"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}, {"title": "<PERSON>, 7th Earl of Hopetoun", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_<PERSON>un"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New South Wales", "link": "https://wikipedia.org/wiki/New_South_Wales"}]}, {"year": "1900", "text": "French parliament votes amnesty for all involved in scandalous army treason trial known as <PERSON><PERSON><PERSON><PERSON> affair.", "html": "1900 - French parliament votes amnesty for all involved in scandalous army treason trial known as <a href=\"https://wikipedia.org/wiki/Drey<PERSON><PERSON>_affair\" title=\"Drey<PERSON><PERSON> affair\">Drey<PERSON><PERSON> affair</a>.", "no_year_html": "French parliament votes amnesty for all involved in scandalous army treason trial known as <a href=\"https://wikipedia.org/wiki/Drey<PERSON><PERSON>_affair\" title=\"Drey<PERSON><PERSON> affair\"><PERSON><PERSON><PERSON><PERSON> affair</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> affair", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair"}]}, {"year": "1907", "text": "Two hundred thirty-nine coal miners die in the Darr Mine Disaster in Jacobs Creek, Pennsylvania.", "html": "1907 - Two hundred thirty-nine coal miners die in the <a href=\"https://wikipedia.org/wiki/Darr_Mine_disaster\" title=\"Darr Mine disaster\">Darr Mine Disaster</a> in <a href=\"https://wikipedia.org/wiki/Jacobs_Creek_(Pennsylvania)\" class=\"mw-redirect\" title=\"Jacobs Creek (Pennsylvania)\">Jacobs Creek, Pennsylvania</a>.", "no_year_html": "Two hundred thirty-nine coal miners die in the <a href=\"https://wikipedia.org/wiki/Darr_Mine_disaster\" title=\"Darr Mine disaster\">Darr Mine Disaster</a> in <a href=\"https://wikipedia.org/wiki/Jacobs_Creek_(Pennsylvania)\" class=\"mw-redirect\" title=\"Jacobs Creek (Pennsylvania)\">Jacobs Creek, Pennsylvania</a>.", "links": [{"title": "Darr Mine disaster", "link": "https://wikipedia.org/wiki/Darr_Mine_disaster"}, {"title": "Jacobs <PERSON> (Pennsylvania)", "link": "https://wikipedia.org/wiki/Jacobs_Creek_(Pennsylvania)"}]}, {"year": "1912", "text": "<PERSON>, captain of the steamship <PERSON> which caught fire and killed over one thousand people, is pardoned by U.S. President <PERSON> after .mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}3+1⁄2 years in Sing Sing prison.", "html": "1912 - <PERSON>, captain of the <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamship</a> <a href=\"https://wikipedia.org/wiki/PS_General_Slocum\" title=\"PS <PERSON>\"><i>General <PERSON></i></a> which caught fire and killed over one thousand people, is pardoned by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> after <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">3<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> years in <a href=\"https://wikipedia.org/wiki/Sing_Sing\" title=\"Sing Sing\">Sing Sing</a> prison.", "no_year_html": "<PERSON>, captain of the <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamship</a> <a href=\"https://wikipedia.org/wiki/PS_General_Slocum\" title=\"PS General <PERSON>\"><i>General <PERSON></i></a> which caught fire and killed over one thousand people, is pardoned by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> after <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">3<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> years in <a href=\"https://wikipedia.org/wiki/Sing_Sing\" title=\"Sing Sing\">Sing Sing</a> prison.", "links": [{"title": "Steamboat", "link": "https://wikipedia.org/wiki/Steamboat"}, {"title": "PS <PERSON>", "link": "https://wikipedia.org/wiki/PS_General_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sing Sing", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "King <PERSON> is restored as King of the Hellenes after the death of his son <PERSON> of Greece and a plebiscite.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Constantine_I_of_Greece\" title=\"<PERSON> I of Greece\">King <PERSON> I</a> is restored as <a href=\"https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)\" class=\"mw-redirect\" title=\"Kingdom of Greece (Glücksburg)\">King of the Hellenes</a> after the death of his son <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> and a <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">plebiscite</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_I_of_Greece\" title=\"<PERSON> I of Greece\">King <PERSON> I</a> is restored as <a href=\"https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)\" class=\"mw-redirect\" title=\"Kingdom of Greece (Glücksburg)\">King of the Hellenes</a> after the death of his son <a href=\"https://wikipedia.org/wiki/Alexander_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> and a <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">plebiscite</a>.", "links": [{"title": "Constantine I of Greece", "link": "https://wikipedia.org/wiki/Constantine_I_of_Greece"}, {"title": "Kingdom of Greece (Glücksburg)", "link": "https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)"}, {"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/Alexander_of_Greece"}, {"title": "Referendum", "link": "https://wikipedia.org/wiki/Referendum"}]}, {"year": "1924", "text": "The last Rolls-Royce Silver Ghost is sold in London, England.", "html": "1924 - The last <a href=\"https://wikipedia.org/wiki/Rolls-Royce_Silver_Ghost\" title=\"Rolls-Royce Silver Ghost\">Rolls-Royce Silver Ghost</a> is sold in London, England.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Rolls-Royce_Silver_Ghost\" title=\"Rolls-Royce Silver Ghost\">Rolls-Royce Silver Ghost</a> is sold in London, England.", "links": [{"title": "Rolls-Royce Silver Ghost", "link": "https://wikipedia.org/wiki/Rolls-Royce_Silver_Ghost"}]}, {"year": "1924", "text": "German serial killer <PERSON> is sentenced to death for a series of murders.", "html": "1924 - German serial killer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to death for a series of murders.", "no_year_html": "German serial killer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to death for a series of murders.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "Three Indian revolutionaries, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, are executed by the British Raj for participation in the Ka<PERSON>i conspiracy.", "html": "1927 - Three Indian revolutionaries, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Bismil\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, are executed by the <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British Raj</a> for participation in the <a href=\"https://wikipedia.org/wiki/Kakori_conspiracy\" title=\"Kakori conspiracy\">Kakori conspiracy</a>.", "no_year_html": "Three Indian revolutionaries, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, are executed by the <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British Raj</a> for participation in the <a href=\"https://wikipedia.org/wiki/Kakori_conspiracy\" title=\"Kakori conspiracy\">Kakori conspiracy</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}, {"title": "Kakori conspiracy", "link": "https://wikipedia.org/wiki/Kakori_conspiracy"}]}, {"year": "1929", "text": "The Indian National Congress promulgates the Purna Swaraj (the Declaration of the Independence of India).", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a> promulgates the <a href=\"https://wikipedia.org/wiki/Purna_<PERSON>waraj\" title=\"<PERSON><PERSON><PERSON>waraj\"><PERSON><PERSON><PERSON>j</a> (the Declaration of the Independence of India).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a> promulgates the <a href=\"https://wikipedia.org/wiki/Purna_<PERSON>waraj\" title=\"<PERSON><PERSON><PERSON>waraj\"><PERSON><PERSON><PERSON>j</a> (the Declaration of the Independence of India).", "links": [{"title": "Indian National Congress", "link": "https://wikipedia.org/wiki/Indian_National_Congress"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>j"}]}, {"year": "1932", "text": "BBC World Service begins broadcasting as the BBC Empire Service.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/BBC_World_Service\" title=\"BBC World Service\">BBC World Service</a> begins broadcasting as the BBC Empire Service.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BBC_World_Service\" title=\"BBC World Service\">BBC World Service</a> begins broadcasting as the BBC Empire Service.", "links": [{"title": "BBC World Service", "link": "https://wikipedia.org/wiki/BBC_World_Service"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, the Prime Minister of Finland,  is elected President of the Republic of Finland in a presidential election, which is exceptionally held by the 1937 electoral college.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Rist<PERSON>_<PERSON>yt<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>, is elected <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Finland\" class=\"mw-redirect\" title=\"President of the Republic of Finland\">President of the Republic of Finland</a> in a <a href=\"https://wikipedia.org/wiki/1940_Finnish_presidential_election\" title=\"1940 Finnish presidential election\">presidential election</a>, which is exceptionally held by the <a href=\"https://wikipedia.org/wiki/1937_Finnish_presidential_election\" title=\"1937 Finnish presidential election\">1937 electoral college</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rist<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>, is elected <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Finland\" class=\"mw-redirect\" title=\"President of the Republic of Finland\">President of the Republic of Finland</a> in a <a href=\"https://wikipedia.org/wiki/1940_Finnish_presidential_election\" title=\"1940 Finnish presidential election\">presidential election</a>, which is exceptionally held by the <a href=\"https://wikipedia.org/wiki/1937_Finnish_presidential_election\" title=\"1937 Finnish presidential election\">1937 electoral college</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rist<PERSON>_Ryti"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}, {"title": "President of the Republic of Finland", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Finland"}, {"title": "1940 Finnish presidential election", "link": "https://wikipedia.org/wiki/1940_Finnish_presidential_election"}, {"title": "1937 Finnish presidential election", "link": "https://wikipedia.org/wiki/1937_Finnish_presidential_election"}]}, {"year": "1941", "text": "World War II: <PERSON> appoints himself as head of the Oberkommando des Heeres.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> appoints himself as head of the <i><a href=\"https://wikipedia.org/wiki/Oberkommando_des_Heeres\" title=\"Oberkommando des Heeres\">Oberkommando des Heeres</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_Hitler\" title=\"Adolf Hitler\"><PERSON></a> appoints himself as head of the <i><a href=\"https://wikipedia.org/wiki/Oberkommando_des_Heeres\" title=\"Oberkommando des Heeres\">Oberkommando des Heeres</a></i>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oberkommando des Heeres", "link": "https://wikipedia.org/wiki/Oberkommando_des_He<PERSON>s"}]}, {"year": "1941", "text": "World War II: Limpet mines placed by Italian divers heavily damage HMS Valiant and HMS Queen Elizabeth in Alexandria harbour.", "html": "1941 - World War II: <a href=\"https://wikipedia.org/wiki/Limpet_mine\" title=\"Limpet mine\">Limpet mines</a> placed by <a href=\"https://wikipedia.org/wiki/Italian_Navy\" title=\"Italian Navy\">Italian</a> divers heavily damage <a href=\"https://wikipedia.org/wiki/HMS_Valiant_(1914)\" title=\"HMS Valiant (1914)\">HMS <i>Valiant</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Queen_Elizabeth_(1913)\" title=\"HMS Queen Elizabeth (1913)\">HMS <i>Queen <PERSON></i></a> in <a href=\"https://wikipedia.org/wiki/Raid_on_Alexandria_(1941)\" title=\"Raid on Alexandria (1941)\">Alexandria harbour</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Limpet_mine\" title=\"Limpet mine\">Limpet mines</a> placed by <a href=\"https://wikipedia.org/wiki/Italian_Navy\" title=\"Italian Navy\">Italian</a> divers heavily damage <a href=\"https://wikipedia.org/wiki/HMS_Valiant_(1914)\" title=\"HMS Valiant (1914)\">HMS <i>Valiant</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Queen_Elizabeth_(1913)\" title=\"HMS Queen Elizabeth (1913)\">HMS <i>Queen <PERSON></i></a> in <a href=\"https://wikipedia.org/wiki/Raid_on_Alexandria_(1941)\" title=\"Raid on Alexandria (1941)\">Alexandria harbour</a>.", "links": [{"title": "Limpet mine", "link": "https://wikipedia.org/wiki/Limpet_mine"}, {"title": "Italian Navy", "link": "https://wikipedia.org/wiki/Italian_Navy"}, {"title": "HMS Valiant (1914)", "link": "https://wikipedia.org/wiki/HMS_Valiant_(1914)"}, {"title": "HMS Queen Elizabeth (1913)", "link": "https://wikipedia.org/wiki/HMS_Queen_<PERSON>_(1913)"}, {"title": "Raid on Alexandria (1941)", "link": "https://wikipedia.org/wiki/Raid_on_Alexandria_(1941)"}]}, {"year": "1941", "text": "Twenty-eight men die when German submarine U-574, is struck by HMS Stork (L81), commanded by Captain <PERSON> of Royal Navy, off Punta Delgada, as also by depth charges.", "html": "1941 - Twenty-eight men die when <a href=\"https://wikipedia.org/wiki/German_submarine_U-574\" title=\"German submarine U-574\">German submarine U-574</a>, is struck by <a href=\"https://wikipedia.org/wiki/HMS_Stork_(L81)\" title=\"HMS Stork (L81)\">HMS Stork (L81)</a>, commanded by Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>, off <a href=\"https://wikipedia.org/wiki/Ponta_Delgada_(Azores)\" class=\"mw-redirect\" title=\"Ponta Delgada (Azores)\">Punta Delgada</a>, as also by <a href=\"https://wikipedia.org/wiki/Depth_charge\" title=\"Depth charge\">depth charges</a>.", "no_year_html": "Twenty-eight men die when <a href=\"https://wikipedia.org/wiki/German_submarine_U-574\" title=\"German submarine U-574\">German submarine U-574</a>, is struck by <a href=\"https://wikipedia.org/wiki/HMS_Stork_(L81)\" title=\"HMS Stork (L81)\">HMS Stork (L81)</a>, commanded by Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>, off <a href=\"https://wikipedia.org/wiki/Ponta_Delgada_(Azores)\" class=\"mw-redirect\" title=\"Ponta Delgada (Azores)\">Punta Delgada</a>, as also by <a href=\"https://wikipedia.org/wiki/Depth_charge\" title=\"Depth charge\">depth charges</a>.", "links": [{"title": "German submarine U-574", "link": "https://wikipedia.org/wiki/German_submarine_U-574"}, {"title": "HMS Stork (L81)", "link": "https://wikipedia.org/wiki/HMS_Stork_(L81)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Ponta Delgada (Azores)", "link": "https://wikipedia.org/wiki/Ponta_Delgada_(Azores)"}, {"title": "Depth charge", "link": "https://wikipedia.org/wiki/Depth_charge"}]}, {"year": "1945", "text": "<PERSON>, British Fascist, is executed at the age of 33 by the British Government for treason.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Fascism\" title=\"Fascism\">Fascist</a>, is executed at the age of 33 by the British Government for treason.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Fascism\" title=\"Fascism\">Fascist</a>, is executed at the age of 33 by the British Government for treason.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fascism", "link": "https://wikipedia.org/wiki/Fascism"}]}, {"year": "1946", "text": "Start of the First Indochina War.", "html": "1946 - Start of the <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>.", "no_year_html": "Start of the <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>.", "links": [{"title": "First Indochina War", "link": "https://wikipedia.org/wiki/First_Indochina_War"}]}, {"year": "1956", "text": "Irish-born physician <PERSON> is arrested in connection with the suspicious deaths of more than 160 patients. Eventually he is convicted only of minor charges.", "html": "1956 - Irish-born physician <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested in connection with the suspicious deaths of more than 160 patients. Eventually he is convicted only of minor charges.", "no_year_html": "Irish-born physician <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested in connection with the suspicious deaths of more than 160 patients. Eventually he is convicted only of minor charges.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "India annexes Daman and Diu, part of Portuguese India.", "html": "1961 - India <a href=\"https://wikipedia.org/wiki/Annexation_of_Goa\" title=\"Annexation of Goa\">annexes</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON><PERSON> and <PERSON><PERSON>\"><PERSON><PERSON> and <PERSON><PERSON></a>, part of <a href=\"https://wikipedia.org/wiki/Portuguese_India\" title=\"Portuguese India\">Portuguese India</a>.", "no_year_html": "India <a href=\"https://wikipedia.org/wiki/Annexation_of_Goa\" title=\"Annexation of Goa\">annexes</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON><PERSON> and <PERSON><PERSON>\"><PERSON><PERSON> and <PERSON><PERSON></a>, part of <a href=\"https://wikipedia.org/wiki/Portuguese_India\" title=\"Portuguese India\">Portuguese India</a>.", "links": [{"title": "Annexation of Goa", "link": "https://wikipedia.org/wiki/Annexation_of_Goa"}, {"title": "<PERSON><PERSON> and <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON>"}, {"title": "Portuguese India", "link": "https://wikipedia.org/wiki/Portuguese_India"}]}, {"year": "1967", "text": "<PERSON>, the Prime Minister of Australia, is officially presumed dead.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>, is <a href=\"https://wikipedia.org/wiki/Declared_death_in_absentia\" class=\"mw-redirect\" title=\"Declared death in absentia\">officially presumed dead</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>, is <a href=\"https://wikipedia.org/wiki/Declared_death_in_absentia\" class=\"mw-redirect\" title=\"Declared death in absentia\">officially presumed dead</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}, {"title": "Declared death in absentia", "link": "https://wikipedia.org/wiki/Declared_death_in_absentia"}]}, {"year": "1972", "text": "Apollo program: The last crewed lunar flight, Apollo 17, carrying <PERSON>, <PERSON>, and <PERSON>, returns to Earth.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: The last crewed lunar flight, <a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a>, carrying <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, returns to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: The last crewed lunar flight, <a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a>, carrying <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, returns to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 17", "link": "https://wikipedia.org/wiki/Apollo_17"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_(astronaut)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}]}, {"year": "1974", "text": "<PERSON> is sworn in as Vice President of the United States under President <PERSON> under the provisions of the Twenty-fifth Amendment to the United States Constitution.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> under President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> under the provisions of the <a href=\"https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fifth Amendment to the United States Constitution\">Twenty-fifth Amendment to the United States Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> under President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> under the provisions of the <a href=\"https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fifth Amendment to the United States Constitution\">Twenty-fifth Amendment to the United States Constitution</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Twenty-fifth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1977", "text": "The Ms  5.8 Bob-Tangol earthquake strikes Kerman Province in Iran, destroying villages and killing 665 people.", "html": "1977 - The M<sub>s</sub>  5.8 <a href=\"https://wikipedia.org/wiki/1977_Bob%E2%80%93Tangol_earthquake\" title=\"1977 Bob-Tangol earthquake\">Bob-Tangol earthquake</a> strikes Kerman Province in Iran, destroying villages and killing 665 people.", "no_year_html": "The M<sub>s</sub>  5.8 <a href=\"https://wikipedia.org/wiki/1977_Bob%E2%80%93Tangol_earthquake\" title=\"1977 Bob-Tangol earthquake\">Bob-Tangol earthquake</a> strikes Kerman Province in Iran, destroying villages and killing 665 people.", "links": [{"title": "1977 Bob-Tangol earthquake", "link": "https://wikipedia.org/wiki/1977_Bob%E2%80%93Tangol_earthquake"}]}, {"year": "1981", "text": "Sixteen lives are lost when the <PERSON><PERSON> lifeboat goes to the aid of the stricken coaster Union Star in heavy seas.", "html": "1981 - Sixteen lives are lost when the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_lifeboat_disaster\" title=\"Penlee lifeboat disaster\"><PERSON><PERSON> lifeboat</a> goes to the aid of the stricken coaster Union Star in heavy seas.", "no_year_html": "Sixteen lives are lost when the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_lifeboat_disaster\" title=\"Penlee lifeboat disaster\"><PERSON><PERSON> lifeboat</a> goes to the aid of the stricken coaster Union Star in heavy seas.", "links": [{"title": "Penlee lifeboat disaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_lifeboat_disaster"}]}, {"year": "1983", "text": "The original FIFA World Cup trophy, the Jules Rimet Trophy, is stolen from the headquarters of the Brazilian Football Confederation in Rio de Janeiro, Brazil.", "html": "1983 - The original <a href=\"https://wikipedia.org/wiki/FIFA_World_Cup\" title=\"FIFA World Cup\">FIFA World Cup</a> trophy, the <a href=\"https://wikipedia.org/wiki/FIFA_World_Cup_Trophy#Jules_Rimet_Trophy\" title=\"FIFA World Cup Trophy\">Jules R<PERSON>t Trophy</a>, is stolen from the headquarters of the <a href=\"https://wikipedia.org/wiki/Brazilian_Football_Confederation\" title=\"Brazilian Football Confederation\">Brazilian Football Confederation</a> in <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro\" title=\"Rio de Janeiro\">Rio de Janeiro</a>, Brazil.", "no_year_html": "The original <a href=\"https://wikipedia.org/wiki/FIFA_World_Cup\" title=\"FIFA World Cup\">FIFA World Cup</a> trophy, the <a href=\"https://wikipedia.org/wiki/FIFA_World_Cup_Trophy#Jules_Rimet_Trophy\" title=\"FIFA World Cup Trophy\">Jules R<PERSON>t Trophy</a>, is stolen from the headquarters of the <a href=\"https://wikipedia.org/wiki/Brazilian_Football_Confederation\" title=\"Brazilian Football Confederation\">Brazilian Football Confederation</a> in <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro\" title=\"Rio de Janeiro\">Rio de Janeiro</a>, Brazil.", "links": [{"title": "FIFA World Cup", "link": "https://wikipedia.org/wiki/FIFA_World_Cup"}, {"title": "FIFA World Cup Trophy", "link": "https://wikipedia.org/wiki/FIFA_World_Cup_Trophy#Jules_<PERSON><PERSON>t_Trophy"}, {"title": "Brazilian Football Confederation", "link": "https://wikipedia.org/wiki/Brazilian_Football_Confederation"}, {"title": "Rio de Janeiro", "link": "https://wikipedia.org/wiki/Rio_de_Janeiro"}]}, {"year": "1984", "text": "The Sino-British Joint Declaration, stating that China would resume the exercise of sovereignty over Hong Kong and the United Kingdom would restore Hong Kong to China with effect from July 1, 1997, is signed in Beijing by <PERSON><PERSON> and <PERSON>.", "html": "1984 - The <a href=\"https://wikipedia.org/wiki/Sino-British_Joint_Declaration\" title=\"Sino-British Joint Declaration\">Sino-British Joint Declaration</a>, stating that China would resume the exercise of sovereignty over Hong Kong and the United Kingdom would restore Hong Kong to China with effect from July 1, 1997, is signed in Beijing by <a href=\"https://wikipedia.org/wiki/<PERSON>g_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sino-British_Joint_Declaration\" title=\"Sino-British Joint Declaration\">Sino-British Joint Declaration</a>, stating that China would resume the exercise of sovereignty over Hong Kong and the United Kingdom would restore Hong Kong to China with effect from July 1, 1997, is signed in Beijing by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Sino-British Joint Declaration", "link": "https://wikipedia.org/wiki/Sino-British_Joint_Declaration"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "Aeroflot Flight 101/435 is hijacked to China by its first officer.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_101/435\" title=\"Aeroflot Flight 101/435\">Aeroflot Flight 101/435</a> is hijacked to China by its first officer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_101/435\" title=\"Aeroflot Flight 101/435\">Aeroflot Flight 101/435</a> is hijacked to China by its first officer.", "links": [{"title": "Aeroflot Flight 101/435", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_101/435"}]}, {"year": "1986", "text": "<PERSON>, leader of the Soviet Union, releases <PERSON> and his wife from exile in Gorky.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, releases <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his wife from <a href=\"https://wikipedia.org/wiki/Exile\" title=\"Exile\">exile</a> in <a href=\"https://wikipedia.org/wiki/Nizhny_Novgorod\" title=\"Nizhny Novgorod\">Gorky</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, releases <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his wife from <a href=\"https://wikipedia.org/wiki/Exile\" title=\"Exile\">exile</a> in <a href=\"https://wikipedia.org/wiki/Nizhny_Novgorod\" title=\"Nizhny Novgorod\">Gorky</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Exile", "link": "https://wikipedia.org/wiki/Exile"}, {"title": "Nizhny Novgorod", "link": "https://wikipedia.org/wiki/Nizhny_Novgorod"}]}, {"year": "1995", "text": "The United States Government restores federal recognition to the Nottawaseppi Huron Band of Potawatomi Native American tribe.", "html": "1995 - The United States Government restores federal recognition to the <a href=\"https://wikipedia.org/wiki/Nottawaseppi_Huron_Band_of_Potawatomi\" title=\"Nottawaseppi Huron Band of Potawatomi\">Nottawaseppi Huron Band of Potawatomi</a> Native American tribe.", "no_year_html": "The United States Government restores federal recognition to the <a href=\"https://wikipedia.org/wiki/Nottawaseppi_Huron_Band_of_Potawatomi\" title=\"Nottawaseppi Huron Band of Potawatomi\">Nottawaseppi Huron Band of Potawatomi</a> Native American tribe.", "links": [{"title": "Nottawaseppi Huron Band of Potawatomi", "link": "https://wikipedia.org/wiki/Nottawaseppi_Huron_Band_of_Potawatomi"}]}, {"year": "1997", "text": "SilkAir Flight 185 crashes into the Musi River, near Palembang in Indonesia, killing 104.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/SilkAir_Flight_185\" title=\"SilkAir Flight 185\">SilkAir Flight 185</a> crashes into the <a href=\"https://wikipedia.org/wiki/Musi_River_(Indonesia)\" title=\"Musi River (Indonesia)\">Musi River</a>, near <a href=\"https://wikipedia.org/wiki/Palembang\" title=\"Palembang\">Palembang</a> in <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, killing 104.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SilkAir_Flight_185\" title=\"SilkAir Flight 185\">SilkAir Flight 185</a> crashes into the <a href=\"https://wikipedia.org/wiki/Musi_River_(Indonesia)\" title=\"Musi River (Indonesia)\">Musi River</a>, near <a href=\"https://wikipedia.org/wiki/Palembang\" title=\"Palembang\">Palembang</a> in <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, killing 104.", "links": [{"title": "SilkAir Flight 185", "link": "https://wikipedia.org/wiki/SilkAir_Flight_185"}, {"title": "<PERSON>si <PERSON> (Indonesia)", "link": "https://wikipedia.org/wiki/Musi_River_(Indonesia)"}, {"title": "Palembang", "link": "https://wikipedia.org/wiki/Palembang"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}]}, {"year": "1998", "text": "President <PERSON> is impeached by the United States House of Representatives, becoming the second president of the United States to be impeached.", "html": "1998 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>\" title=\"Impeachment of <PERSON>\">impeached</a> by the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a>, becoming the second president of the United States to be <a href=\"https://wikipedia.org/wiki/Federal_impeachment_in_the_United_States\" title=\"Federal impeachment in the United States\">impeached</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>\" title=\"Impeachment of <PERSON>\">impeached</a> by the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a>, becoming the second president of the United States to be <a href=\"https://wikipedia.org/wiki/Federal_impeachment_in_the_United_States\" title=\"Federal impeachment in the United States\">impeached</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "Federal impeachment in the United States", "link": "https://wikipedia.org/wiki/Federal_impeachment_in_the_United_States"}]}, {"year": "1999", "text": "Space Shuttle Discovery is launched on STS-103, the third Hubble Space Telescope servicing mission.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-103\" title=\"STS-103\">STS-103</a>, the third <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a> servicing mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-103\" title=\"STS-103\">STS-103</a>, the third <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a> servicing mission.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-103", "link": "https://wikipedia.org/wiki/STS-103"}, {"title": "<PERSON>bble Space Telescope", "link": "https://wikipedia.org/wiki/Hubble_Space_Telescope"}]}, {"year": "2000", "text": "The Leninist Guerrilla Units wing of the Communist Labour Party of Turkey/Leninist attack a Nationalist Movement Party office in Istanbul, Turkey, killing one person and injuring three.", "html": "2000 - The Leninist Guerrilla Units wing of the <a href=\"https://wikipedia.org/wiki/Communist_Labour_Party_of_Turkey/Leninist\" title=\"Communist Labour Party of Turkey/Leninist\">Communist Labour Party of Turkey/Leninist</a> attack a <a href=\"https://wikipedia.org/wiki/Nationalist_Movement_Party\" title=\"Nationalist Movement Party\">Nationalist Movement Party</a> office in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, killing one person and injuring three.", "no_year_html": "The Leninist Guerrilla Units wing of the <a href=\"https://wikipedia.org/wiki/Communist_Labour_Party_of_Turkey/Leninist\" title=\"Communist Labour Party of Turkey/Leninist\">Communist Labour Party of Turkey/Leninist</a> attack a <a href=\"https://wikipedia.org/wiki/Nationalist_Movement_Party\" title=\"Nationalist Movement Party\">Nationalist Movement Party</a> office in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, killing one person and injuring three.", "links": [{"title": "Communist Labour Party of Turkey/Leninist", "link": "https://wikipedia.org/wiki/Communist_Labour_Party_of_Turkey/Leninist"}, {"title": "Nationalist Movement Party", "link": "https://wikipedia.org/wiki/Nationalist_Movement_Party"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}]}, {"year": "2001", "text": "A record high barometric pressure of 1,085.6 hectopascals (32.06 inHg) is recorded at Tosontsengel, Khövsgöl, Mongolia.", "html": "2001 - A record high <a href=\"https://wikipedia.org/wiki/Atmospheric_pressure\" title=\"Atmospheric pressure\">barometric pressure</a> of 1,085.6 <a href=\"https://wikipedia.org/wiki/<PERSON>_(unit)\" title=\"<PERSON> (unit)\">hectopascals</a> (32.06 <a href=\"https://wikipedia.org/wiki/Inch_of_mercury\" title=\"Inch of mercury\">inHg</a>) is recorded at <a href=\"https://wikipedia.org/wiki/Tosontsengel,_Kh%C3%B6vsg%C3%B6l\" title=\"Tosontsengel, Khövsgöl\">Tosontsengel, Khövsgöl</a>, <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a>.", "no_year_html": "A record high <a href=\"https://wikipedia.org/wiki/Atmospheric_pressure\" title=\"Atmospheric pressure\">barometric pressure</a> of 1,085.6 <a href=\"https://wikipedia.org/wiki/<PERSON>_(unit)\" title=\"<PERSON> (unit)\">hectopascals</a> (32.06 <a href=\"https://wikipedia.org/wiki/Inch_of_mercury\" title=\"Inch of mercury\">inHg</a>) is recorded at <a href=\"https://wikipedia.org/wiki/Tosontsengel,_Kh%C3%B6vsg%C3%B6l\" title=\"Tosontsengel, Khövsgöl\">Tosontsengel, Khövsgöl</a>, <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a>.", "links": [{"title": "Atmospheric pressure", "link": "https://wikipedia.org/wiki/Atmospheric_pressure"}, {"title": "<PERSON> (unit)", "link": "https://wikipedia.org/wiki/<PERSON>_(unit)"}, {"title": "Inch of mercury", "link": "https://wikipedia.org/wiki/Inch_of_mercury"}, {"title": "Tosontsengel, Khövsgöl", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>,_Kh%C3%B6vsg%C3%B6l"}, {"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}]}, {"year": "2001", "text": "Argentine economic crisis: December riots: Riots erupt in Buenos Aires, Argentina.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/1998%E2%80%932002_Argentine_great_depression\" title=\"1998-2002 Argentine great depression\">Argentine economic crisis</a>: <a href=\"https://wikipedia.org/wiki/December_2001_riots_in_Argentina\" title=\"December 2001 riots in Argentina\">December riots</a>: Riots erupt in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1998%E2%80%932002_Argentine_great_depression\" title=\"1998-2002 Argentine great depression\">Argentine economic crisis</a>: <a href=\"https://wikipedia.org/wiki/December_2001_riots_in_Argentina\" title=\"December 2001 riots in Argentina\">December riots</a>: Riots erupt in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "links": [{"title": "1998-2002 Argentine great depression", "link": "https://wikipedia.org/wiki/1998%E2%80%932002_Argentine_great_depression"}, {"title": "December 2001 riots in Argentina", "link": "https://wikipedia.org/wiki/December_2001_riots_in_Argentina"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "2005", "text": "Chalk's Ocean Airways Flight 101 crashes into the Government Cut channel immediately after takeoff from Miami Seaplane Base, killing 20.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Cha<PERSON>%27s_Ocean_Airways_Flight_101\" title=\"<PERSON><PERSON>'s Ocean Airways Flight 101\">Cha<PERSON>'s Ocean Airways Flight 101</a> crashes into the <a href=\"https://wikipedia.org/wiki/Government_Cut\" title=\"Government Cut\">Government Cut</a> channel immediately after takeoff from <a href=\"https://wikipedia.org/wiki/Miami_Seaplane_Base\" title=\"Miami Seaplane Base\">Miami Seaplane Base</a>, killing 20.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha<PERSON>%27s_Ocean_Airways_Flight_101\" title=\"<PERSON><PERSON>'s Ocean Airways Flight 101\">Cha<PERSON>'s Ocean Airways Flight 101</a> crashes into the <a href=\"https://wikipedia.org/wiki/Government_Cut\" title=\"Government Cut\">Government Cut</a> channel immediately after takeoff from <a href=\"https://wikipedia.org/wiki/Miami_Seaplane_Base\" title=\"Miami Seaplane Base\">Miami Seaplane Base</a>, killing 20.", "links": [{"title": "Chalk's Ocean Airways Flight 101", "link": "https://wikipedia.org/wiki/Chalk%27s_Ocean_Airways_Flight_101"}, {"title": "Government Cut", "link": "https://wikipedia.org/wiki/Government_Cut"}, {"title": "Miami Seaplane Base", "link": "https://wikipedia.org/wiki/Miami_Seaplane_Base"}]}, {"year": "2012", "text": "<PERSON> is elected the first female president of South Korea.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hye\" title=\"<PERSON>hye\"><PERSON>-<PERSON>ye</a> <a href=\"https://wikipedia.org/wiki/2012_South_Korean_presidential_election\" title=\"2012 South Korean presidential election\">is elected</a> the first female <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">president of South Korea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ye\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/2012_South_Korean_presidential_election\" title=\"2012 South Korean presidential election\">is elected</a> the first female <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">president of South Korea</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eu<PERSON>-hye"}, {"title": "2012 South Korean presidential election", "link": "https://wikipedia.org/wiki/2012_South_Korean_presidential_election"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "2013", "text": "Spacecraft Gaia is launched by the European Space Agency.", "html": "2013 - Spacecraft <i><a href=\"https://wikipedia.org/wiki/Gaia_(spacecraft)\" title=\"Gaia (spacecraft)\">Gaia</a></i> is launched by the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>.", "no_year_html": "Spacecraft <i><a href=\"https://wikipedia.org/wiki/Gaia_(spacecraft)\" title=\"Gaia (spacecraft)\">Gaia</a></i> is launched by the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>.", "links": [{"title": "G<PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/G<PERSON>_(spacecraft)"}, {"title": "European Space Agency", "link": "https://wikipedia.org/wiki/European_Space_Agency"}]}, {"year": "2016", "text": "Russian ambassador to Turkey <PERSON> is assassinated while at an art exhibition in Ankara. The assassin, <PERSON><PERSON><PERSON><PERSON><PERSON>, is shot and killed by a Turkish guard.", "html": "2016 - Russian ambassador to Turkey <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> while at an art exhibition in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>. The assassin, <PERSON><PERSON><PERSON><PERSON><PERSON>, is shot and killed by a Turkish guard.", "no_year_html": "Russian ambassador to Turkey <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> while at an art exhibition in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>. The assassin, <PERSON><PERSON><PERSON><PERSON><PERSON>, is shot and killed by a Turkish guard.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}, {"title": "Ankara", "link": "https://wikipedia.org/wiki/Ankara"}]}, {"year": "2016", "text": "A vehicular attack in Berlin, Germany, kills 12 and injures 56 people at a Christmas market.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/2016_Berlin_truck_attack\" title=\"2016 Berlin truck attack\">A vehicular attack</a> in Berlin, Germany, kills 12 and injures 56 people at a <a href=\"https://wikipedia.org/wiki/Christmas_market\" title=\"Christmas market\">Christmas market</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2016_Berlin_truck_attack\" title=\"2016 Berlin truck attack\">A vehicular attack</a> in Berlin, Germany, kills 12 and injures 56 people at a <a href=\"https://wikipedia.org/wiki/Christmas_market\" title=\"Christmas market\">Christmas market</a>.", "links": [{"title": "2016 Berlin truck attack", "link": "https://wikipedia.org/wiki/2016_Berlin_truck_attack"}, {"title": "Christmas market", "link": "https://wikipedia.org/wiki/Christmas_market"}]}], "Births": [{"year": "1343", "text": "<PERSON>, Margrave of Meissen (d. 1407)", "html": "1343 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON>, Margrave of Meissen\"><PERSON>, Margrave of Meissen</a> (d. 1407)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON>, Margrave of Meissen\"><PERSON>, Margrave of Meissen</a> (d. 1407)", "links": [{"title": "<PERSON>, Margrave of Meissen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1498", "text": "<PERSON>, German Protestant theologian (d. 1552)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Protestant theologian (d. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Protestant theologian (d. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1554", "text": "<PERSON>, Prince of Orange (d. 1618)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1618)", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}]}, {"year": "1587", "text": "<PERSON>, Abbess of Quedlinburg (d. 1645)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON>, Abbess of Quedlinburg\"><PERSON>, Abbess of Quedlinburg</a> (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON>, Abbess of Quedlinburg\"><PERSON>, Abbess of Quedlinburg</a> (d. 1645)", "links": [{"title": "<PERSON>, Abbess of Quedlinburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg"}]}, {"year": "1683", "text": "<PERSON> of Spain (d. 1746)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/Philip_V_of_Spain\" title=\"<PERSON> V of Spain\"><PERSON> of Spain</a> (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philip_<PERSON>_of_Spain\" title=\"<PERSON> V of Spain\"><PERSON> of Spain</a> (d. 1746)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Philip_V_of_Spain"}]}, {"year": "1699", "text": "<PERSON>, English printer (d. 1777)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English printer (d. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English printer (d. 1777)", "links": [{"title": "<PERSON> (printer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)"}]}, {"year": "1714", "text": "<PERSON>, American astronomer and educator (d. 1779)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educator)\" title=\"<PERSON> (educator)\"><PERSON></a>, American astronomer and educator (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educator)\" title=\"<PERSON> (educator)\"><PERSON></a>, American astronomer and educator (d. 1779)", "links": [{"title": "<PERSON> (educator)", "link": "https://wikipedia.org/wiki/<PERSON>_(educator)"}]}, {"year": "1778", "text": "<PERSON> of France (d. 1851)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/Marie_<PERSON>h%C3%A9r%C3%A8se_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9r%C3%A8se_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1851)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Marie_Th%C3%A9r%C3%A8se_of_France"}]}, {"year": "1796", "text": "<PERSON> los Herreros, Spanish poet, playwright, and critic (d. 1873)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_los_Herreros\" title=\"Manuel Bretón de los Herreros\"><PERSON> los Herreros</a>, Spanish poet, playwright, and critic (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_los_Herreros\" title=\"Manuel Bretón de los Herreros\"><PERSON> los Herreros</a>, Spanish poet, playwright, and critic (d. 1873)", "links": [{"title": "<PERSON> de los Herreros", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ret%C3%B3n_de_los_Herreros"}]}, {"year": "1797", "text": "<PERSON>, French obstetrician and naturalist (d. 1838)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French obstetrician and naturalist (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French obstetrician and naturalist (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s"}]}, {"year": "1817", "text": "<PERSON>, American lawyer and general (d. 1864)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and general (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and general (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, American journalist and activist (d. 1905)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, American violinist and composer (d. 1898)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON>, American philanthropist (d. 1884)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philanthropist (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philanthropist (d. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, American businessman and financier (d. 1919)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Prussian-American physicist, chemist, and academic, Nobel Prize laureate (d. 1931)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Prussian-American physicist, chemist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Prussian-American physicist, chemist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1853", "text": "<PERSON>, Canadian lawyer and politician, 12th Lieutenant-Governor of Quebec (d. 1942)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Quebec\" title=\"List of lieutenant governors of Quebec\">Lieutenant-Governor of Quebec</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Quebec\" title=\"List of lieutenant governors of Quebec\">Lieutenant-Governor of Quebec</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of lieutenant governors of Quebec", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Quebec"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Italian author and playwright (d. 1928)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Italo_Svevo\" title=\"Italo Svevo\">Italo <PERSON></a>, Italian author and playwright (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italo_Svevo\" title=\"Italo Svevo\">Italo <PERSON></a>, Italian author and playwright (d. 1928)", "links": [{"title": "Italo Svevo", "link": "https://wikipedia.org/wiki/Italo_Svevo"}]}, {"year": "1863", "text": "<PERSON>, American archer (d. 1953)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archer)\" title=\"<PERSON> (archer)\"><PERSON></a>, American archer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(archer)\" title=\"<PERSON> (archer)\"><PERSON></a>, American archer (d. 1953)", "links": [{"title": "<PERSON> (archer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archer)"}]}, {"year": "1865", "text": "<PERSON><PERSON>, American actress and playwright (d. 1932)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and playwright (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and playwright (d. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Fiske"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, French fencer (d. 1913)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French fencer (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French fencer (d. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON>, Serbian physicist (d. 1948)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian physicist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian physicist (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mileva_Mari%C4%87"}]}, {"year": "1875", "text": "<PERSON>, American historian and author, founded Black History Month (d. 1950)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author, founded <a href=\"https://wikipedia.org/wiki/Black_History_Month\" title=\"Black History Month\">Black History Month</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author, founded <a href=\"https://wikipedia.org/wiki/Black_History_Month\" title=\"Black History Month\">Black History Month</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Black History Month", "link": "https://wikipedia.org/wiki/Black_History_Month"}]}, {"year": "1875", "text": "<PERSON>, American mathematician (d. 1962)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Austrian-Israeli scholar and author (d. 1961)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Israeli scholar and author (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Israeli scholar and author (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Czech politician, President of the Czechoslovak Socialist Republic (d. 1957)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Z%C3%A1potock%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia\" class=\"mw-redirect\" title=\"List of Presidents of Czechoslovakia\">President</a> of the <a href=\"https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic\" title=\"Czechoslovak Socialist Republic\">Czechoslovak Socialist Republic</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Z%C3%A1potock%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia\" class=\"mw-redirect\" title=\"List of Presidents of Czechoslovakia\">President</a> of the <a href=\"https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic\" title=\"Czechoslovak Socialist Republic\">Czechoslovak Socialist Republic</a> (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton%C3%ADn_Z%C3%A1potock%C3%BD"}, {"title": "List of Presidents of Czechoslovakia", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia"}, {"title": "Czechoslovak Socialist Republic", "link": "https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic"}]}, {"year": "1888", "text": "<PERSON>, Hungarian-American conductor (d. 1963)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American conductor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American conductor (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Polish politician and diplomat, 4th President-in-exile of Poland (d. 1993)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish politician and diplomat, 4th <a href=\"https://wikipedia.org/wiki/Polish_government-in-exile#Presidents\" title=\"Polish government-in-exile\">President-in-exile of Poland</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish politician and diplomat, 4th <a href=\"https://wikipedia.org/wiki/Polish_government-in-exile#Presidents\" title=\"Polish government-in-exile\">President-in-exile of Poland</a> (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%84ski"}, {"title": "Polish government-in-exile", "link": "https://wikipedia.org/wiki/Polish_government-in-exile#Presidents"}]}, {"year": "1894", "text": "<PERSON>, American journalist and businessman (d. 1978)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Ford_C._Frick\" class=\"mw-redirect\" title=\"Ford C. Frick\"><PERSON></a>, American journalist and businessman (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ford_C._Frick\" class=\"mw-redirect\" title=\"Ford C. Frick\"><PERSON></a>, American journalist and businessman (d. 1978)", "links": [{"title": "Ford C. <PERSON>", "link": "https://wikipedia.org/wiki/Ford_C._Frick"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Norwegian author and educator (d. 1989)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Ingeborg_Refling_Hagen\" title=\"Ingeborg Refling Hagen\">Ingeborg Ref<PERSON> Hagen</a>, Norwegian author and educator (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingeborg_Refling_Hagen\" title=\"Ingeborg Refling Hagen\">Ingeborg Ref<PERSON></a>, Norwegian author and educator (d. 1989)", "links": [{"title": "Ingeborg Refling Hagen", "link": "https://wikipedia.org/wiki/Ingeborg_Refling_Hagen"}]}, {"year": "1899", "text": "<PERSON>, American pastor, missionary, and activist (d. 1984)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American pastor, missionary, and activist (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American pastor, missionary, and activist (d. 1984)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1901", "text": "<PERSON>, German engineer, invented the Hellschreiber (d. 2002)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rudolf <PERSON>\"><PERSON></a>, German engineer, invented the <a href=\"https://wikipedia.org/wiki/Hellschreiber\" title=\"Hellschreiber\">Hellschreiber</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rudolf <PERSON>\"><PERSON></a>, German engineer, invented the <a href=\"https://wikipedia.org/wiki/Hellschreiber\" title=\"Hellschreiber\">Hellschreiber</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hellschreiber"}]}, {"year": "1901", "text": "<PERSON>, American anthropologist and author (d. 1963)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, German footballer and manager (d. 1974)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English actor (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American geneticist and immunologist, Nobel Prize laureate (d. 1996)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1905", "text": "<PERSON>, American businessman (d. 2015)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Italian race car driver, engineer, and journalist (d. 1995)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver, engineer, and journalist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver, engineer, and journalist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Ukrainian-Russian marshal, engineer, and politician, 4th Head of State of the Soviet Union (d. 1982)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian marshal, engineer, and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Head of State of the Soviet Union</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian marshal, engineer, and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Head of State of the Soviet Union</a> (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union"}]}, {"year": "1907", "text": "<PERSON>, Irish-American boxer, actor, and golfer (d. 2004)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American boxer, actor, and golfer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American boxer, actor, and golfer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON> <PERSON><PERSON>, American pastor and author (d. 2002)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American pastor and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American pastor and author (d. 2002)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French novelist, playwright, and poet (d. 1986)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, playwright, and poet (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, playwright, and poet (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American animator and screenwriter (d. 2012)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, French singer-songwriter and actress (d. 1963)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/%C3%89dith_Piaf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer-songwriter and actress (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89dith_<PERSON>af\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer-songwriter and actress (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89dith_<PERSON>af"}]}, {"year": "1915", "text": "<PERSON>, Italian hurdler, sprinter, and long jumper (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian hurdler, sprinter, and long jumper (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian hurdler, sprinter, and long jumper (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English director and producer (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, German political scientist, journalist, and academic (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German political scientist, journalist, and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German political scientist, journalist, and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "Professor <PERSON>, American singer-songwriter and pianist (d. 1980)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Professor_<PERSON><PERSON>\" title=\"Professor <PERSON><PERSON>\">Professor <PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Professor_<PERSON><PERSON>\" title=\"Professor <PERSON>\">Professor <PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1980)", "links": [{"title": "Professor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Professor_<PERSON><PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American producer and production manager (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American producer and production manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lee Rich\"><PERSON></a>, American producer and production manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Little Jimmy <PERSON>\"><PERSON> <PERSON></a>, American singer-songwriter and guitarist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Little <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American talk show host and producer (d. 1987)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and producer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and producer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Irish radio and television host (d. 1987)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish radio and television host (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish radio and television host (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American historian and author (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Scottish-English actor and singer (d. 1990)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish-English actor and singer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish-English actor and singer (d. 1990)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1924", "text": "<PERSON>, Italian engineer (d. 1994)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1989)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 1989)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1924", "text": "<PERSON>, American comedian and producer (d. 1999)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, British-Italian actor (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Italian actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Italian actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, French journalist and author (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, American actress (d. 2021) ", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2021) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2021) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, German author and playwright (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and playwright (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dorst\"><PERSON><PERSON></a>, German author and playwright (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American psychologist and academic (d. 2002)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American songwriter and screenwriter (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American football player and coach (d. 1986)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Turkish painter and journalist (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish painter and journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish painter and journalist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>am"}]}, {"year": "1927", "text": "<PERSON>, English actor and screenwriter (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Irish-American author and academic (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>unting\" title=\"<PERSON> Bunting\"><PERSON></a>, Irish-American author and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bunting\" title=\"<PERSON> Bunting\"><PERSON></a>, Irish-American author and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American painter and sculptor (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American trombonist, pianist, and composer (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, pianist, and composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, pianist, and composer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter and producer (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(R%26B_singer)\" title=\"<PERSON> (R&amp;B singer)\"><PERSON></a>, American singer-songwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(R%26B_singer)\" title=\"<PERSON> (R&amp;B singer)\"><PERSON></a>, American singer-songwriter and producer (d. 2013)", "links": [{"title": "<PERSON> (R&B singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(R%26B_singer)"}]}, {"year": "1929", "text": "<PERSON>, 12th Marquess of Queensberry, Scottish potter", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_12th_Marquess_of_Queensberry\" title=\"<PERSON>, 12th Marquess of Queensberry\"><PERSON>, 12th Marquess of Queensberry</a>, Scottish potter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Marquess_of_Queensberry\" title=\"<PERSON>, 12th Marquess of Queensberry\"><PERSON>, 12th Marquess of Queensberry</a>, Scottish potter", "links": [{"title": "<PERSON>, 12th Marquess of Queensberry", "link": "https://wikipedia.org/wiki/<PERSON>,_12th_Marquess_of_Queensberry"}]}, {"year": "1929", "text": "<PERSON>, American playwright and screenwriter (d. 1982)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Norwegian historian and professor (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Knut <PERSON>\"><PERSON><PERSON></a>, Norwegian historian and professor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian historian and professor (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1930", "text": "<PERSON>, English businessman and academic (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": " <PERSON>, American model, actress and stunt woman (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress and stunt woman (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress and stunt woman (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Mexican author, poet, playwright, and critic (d. 2006)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Salvador_Elizondo\" title=\"Salvador Elizondo\"><PERSON></a>, Mexican author, poet, playwright, and critic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Elizondo\" title=\"Salvador Elizondo\"><PERSON></a>, Mexican author, poet, playwright, and critic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvador_Elizondo"}]}, {"year": "1932", "text": "<PERSON>, African American civil rights activist (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African American civil rights activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African American civil rights activist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>pit"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Australian runner and politician (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian runner and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian runner and politician (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Scottish historian and academic", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American baseball player and sportscaster (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ne\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Kaline\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Kaline"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 12th President of India", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>il"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1934", "text": "<PERSON>, Jr., American lawyer and politician (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician (d. 2023)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1935", "text": "<PERSON>, Cuban baseball player and coach (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Cuban baseball player and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Cuban baseball player and coach (d. 2020)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1935", "text": "<PERSON>, American pianist and composer (d. 1974)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American baseball player (d. 2000)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American basketball player", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1976)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, South Korean businessman and politician, 10th President of South Korea", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-bak\" title=\"<PERSON>bak\"><PERSON></a>, South Korean businessman and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-bak\" title=\"<PERSON>bak\"><PERSON></a>, South Korean businessman and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-bak"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and producer (d. 2016)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American guitarist (d. 2011)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American sports announcer (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports announcer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports announcer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American general and politician, 22nd United States National Security Advisor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 22nd <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(United_States)\" title=\"National Security Advisor (United States)\">United States National Security Advisor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 22nd <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(United_States)\" title=\"National Security Advisor (United States)\">United States National Security Advisor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "National Security Advisor (United States)", "link": "https://wikipedia.org/wiki/National_Security_Advisor_(United_States)"}]}, {"year": "1943", "text": "<PERSON>, American actress, singer, and dancer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American political scientist and academic (d. 2006)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American-French harpsichord player and conductor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-French <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-French <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and conductor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1944", "text": "<PERSON>, American physicist and mathematician (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English physiologist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Kenyan paleontologist and politician (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan paleontologist and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan paleontologist and politician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and guitarist  (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor and director", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Canadian singer-songwriter and guitarist (d. 2002)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and guitarist ", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English businesswoman, author, and broadcaster", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman, author, and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman, author, and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor and producer (d. 2002)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish bass player and songwriter (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bass player and songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bass player and songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian ice hockey player and sportscaster (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster (d. 2022)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Israeli computer scientist and businesswoman", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli computer scientist and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli computer scientist and businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American swimmer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Danish singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Danish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Danish singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1949", "text": "<PERSON>, American musician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American lawyer and diplomat", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Iranian engineer and politician, 2nd Vice President of Iran", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian engineer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Iran\" class=\"mw-redirect\" title=\"Vice President of Iran\">Vice President of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian engineer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Iran\" class=\"mw-redirect\" title=\"Vice President of Iran\">Vice President of Iran</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of Iran", "link": "https://wikipedia.org/wiki/Vice_President_of_Iran"}]}, {"year": "1951", "text": "<PERSON>, English mountaineer and author (d. 1986)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American pianist and composer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English race car driver", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English author and translator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tim_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian mountaineer and author (d. 2012)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Lincoln_Hall_(climber)\" title=\"Lincoln Hall (climber)\">Lincoln Hall</a>, Australian mountaineer and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lincoln_Hall_(climber)\" title=\"Lincoln Hall (climber)\">Lincoln Hall</a>, Australian mountaineer and author (d. 2012)", "links": [{"title": "<PERSON> Hall (climber)", "link": "https://wikipedia.org/wiki/Lincoln_Hall_(climber)"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>man"}]}, {"year": "1956", "text": "<PERSON>, American captain and fisherman (d. 2010)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fisherman)\" title=\"<PERSON> (fisherman)\"><PERSON></a>, American captain and fisherman (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fisherman)\" title=\"<PERSON> (fisherman)\"><PERSON></a>, American captain and fisherman (d. 2010)", "links": [{"title": "<PERSON> (fisherman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fisherman)"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Irish farmer and politician, Minister of State at the Department of Agriculture, Food and the Marine (d. 2012)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish farmer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_at_the_Department_of_Agriculture,_Food_and_the_Marine\" title=\"Minister of State at the Department of Agriculture, Food and the Marine\">Minister of State at the Department of Agriculture, Food and the Marine</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish farmer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_at_the_Department_of_Agriculture,_Food_and_the_Marine\" title=\"Minister of State at the Department of Agriculture, Food and the Marine\">Minister of State at the Department of Agriculture, Food and the Marine</a> (d. 2012)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Minister of State at the Department of Agriculture, Food and the Marine", "link": "https://wikipedia.org/wiki/Minister_of_State_at_the_Department_of_Agriculture,_Food_and_the_Marine"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Japanese noise musician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese noise musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese noise musician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1957", "text": "<PERSON>, French actor, director, and composer (d. 1993)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and composer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and composer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American basketball player, coach, and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player, coach, and manager", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1958", "text": "<PERSON>, English cellist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, English pop singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English pop singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Limahl"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Ecuadorian mountaineer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Vallejo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Vallejo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian mountaineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Vallejo"}]}, {"year": "1959", "text": "<PERSON>, Australian television host and journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author and activist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American journalist and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>orile\"><PERSON><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Signorile\"><PERSON><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1961", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1961", "text": "<PERSON>, English actor and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matthew_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American football player and wrestler (d. 2004)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American model and actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, German actor, director, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Til_<PERSON>weiger\" title=\"T<PERSON>weiger\"><PERSON><PERSON></a>, German actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Til_<PERSON>weiger\" title=\"Til <PERSON>weiger\"><PERSON><PERSON></a>, German actor, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Til_<PERSON>ger"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/B%C3%A9atrice_<PERSON><PERSON>\" title=\"B<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9atrice_<PERSON><PERSON>\" title=\"B<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9atrice_Dalle"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Canadian golfer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American football player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ar<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Belizean-American baseball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Chito_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belizean-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chito_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belizean-American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chito_Mart%C3%ADnez"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Italian skier", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American ice hockey player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, American magician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Criss_Angel\" title=\"Criss Angel\"><PERSON><PERSON><PERSON> <PERSON></a>, American magician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Criss_<PERSON>\" title=\"Criss Angel\"><PERSON><PERSON><PERSON> <PERSON></a>, American magician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>s_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American high jumper", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(high_jumper)\" title=\"<PERSON> (high jumper)\"><PERSON></a>, American high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(high_jumper)\" title=\"<PERSON> (high jumper)\"><PERSON></a>, American high jumper", "links": [{"title": "<PERSON> (high jumper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(high_jumper)"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American-Australian politician, 42nd Premier of New South Wales", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Australian politician, 42nd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Australian politician, 42nd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1968", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American sprinter and football player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American sprinter and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American sprinter and football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1969", "text": "<PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English journalist and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>yan <PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>yan <PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gia"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Azerbaijani composer, pianist, and singer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani composer, pianist, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani composer, pianist, and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American model and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Czech ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(ice_hockey)"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English swimmer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress, television personality, and activist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, television personality, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, television personality, and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player and analyst", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and analyst", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Greek footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Kenyan runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Russian cyclist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Zabirova\" title=\"<PERSON><PERSON><PERSON> Zabirova\"><PERSON><PERSON><PERSON> Zabirov<PERSON></a>, Russian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Z<PERSON><PERSON>_Zabirova\" title=\"Zulfiya Zabirova\"><PERSON><PERSON><PERSON> Zabirov<PERSON></a>, Russian cyclist", "links": [{"title": "Zulfiya Zabirova", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rova"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Japanese actor and singer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Indonesian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Dominican-American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_L%C3%B3pez_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Dominican-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_L%C3%B3pez_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Dominican-American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/Felipe_L%C3%B3pez_(basketball)"}]}, {"year": "1974", "text": "<PERSON>, American football player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian cricketer and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Greek footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American author and academic", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American composer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Ivorian-French footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%A9bily\" title=\"<PERSON>\"><PERSON></a>, Ivorian-French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%A9bily\" title=\"<PERSON>\"><PERSON></a>, Ivorian-French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Olivier_T%C3%A9bily"}]}, {"year": "1975", "text": "<PERSON>, Australian rugby league player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Spanish basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, American sprinter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Russian model", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Dominican baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actress and musician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Gr%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gr%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gr%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Nektarios_<PERSON>\" title=\"Nektarios <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nektarios_<PERSON>\" title=\"Nektar<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nektarios_<PERSON>rou"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Casey_<PERSON>o"}]}, {"year": "1983", "text": "<PERSON>, English politician", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Italian fencer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fencer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English-Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Kenyan runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English bass player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English rapper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Lady_Sovereign\" title=\"Lady Sovereign\">Lady Sovereign</a>, English rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_Sovereign\" title=\"Lady Sovereign\">Lady Sovereign</a>, English rapper", "links": [{"title": "Lady Sovereign", "link": "https://wikipedia.org/wiki/Lady_Sovereign"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Dutch footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American rapper and songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Czech hurdler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>%C3%A1"}]}, {"year": "1986", "text": "<PERSON>, Portuguese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, French-Congolese footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Congolese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9dric_Baseya"}]}, {"year": "1987", "text": "<PERSON><PERSON>, French footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American activist, journalist, and lawyer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, journalist, and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, journalist, and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Chilean footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexis_S%C3%A1nchez"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1989", "text": "<PERSON>, South Korean singer-songwriter, rapper and producer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter, rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter, rapper and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Micha%C5%82_Mas%C5%82owski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Micha%C5%82_Mas%C5%82owski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C5%82_Mas%C5%82owski"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Japanese jockey", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese jockey", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, English cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American snowboarder", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Dutch footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English singer-songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian actor, singer-songwriter, and dancer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Lonsdale\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Australian actor, singer-songwriter, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Lonsdale\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Australian actor, singer-songwriter, and dancer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Lonsdale"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>in"}]}, {"year": "1992", "text": "<PERSON>, Swiss footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, South Korean singer-songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_K\" title=\"<PERSON> K\"><PERSON></a>, South Korean singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_K\" title=\"Young K\"><PERSON></a>, South Korean singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_K"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Kenyan runner", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Indonesian actress and singer-songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actress and singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/M%27B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%27B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%27Baye_<PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Fran<PERSON>_<PERSON>ssi%C3%A9\" title=\"<PERSON>an<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran<PERSON>_<PERSON><PERSON>%C3%A9\" title=\"<PERSON>an<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran<PERSON>_<PERSON><PERSON>%C3%A9"}]}, {"year": "1997", "text": "<PERSON>, Brazilian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3es\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3es\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3es"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter and musician", "html": "1998 - <a href=\"https://wikipedia.org/wiki/King_Princess\" title=\"King Princess\">King Princess</a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_Princess\" title=\"King Princess\">King Princess</a>, American singer-songwriter and musician", "links": [{"title": "King Princess", "link": "https://wikipedia.org/wiki/King_Princess"}]}], "Deaths": [{"year": "401", "text": "<PERSON><PERSON>", "html": "401 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>sius_I\" title=\"Pope Ana<PERSON>sius I\">Pope <PERSON><PERSON><PERSON> I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>sius_I\" title=\"Pope Ana<PERSON>sius I\">Pope <PERSON><PERSON><PERSON> I</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>sius_I"}]}, {"year": "966", "text": "<PERSON><PERSON>, king of León", "html": "966 - <a href=\"https://wikipedia.org/wiki/Sancho_I_of_Le%C3%B3n\" title=\"Sancho I of León\"><PERSON><PERSON> <PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sancho_I_of_Le%C3%B3n\" title=\"Sancho I of León\">San<PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a>", "links": [{"title": "Sancho I of León", "link": "https://wikipedia.org/wiki/Sancho_I_of_Le%C3%B3n"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}]}, {"year": "1091", "text": "<PERSON> of Susa, margravine of Turin", "html": "1091 - <a href=\"https://wikipedia.org/wiki/Adelaide_of_Susa\" title=\"Adelaide of Susa\"><PERSON> of Susa</a>, margravine of Turin", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_of_Susa\" title=\"Adelaide of Susa\">Adelaide of Susa</a>, margravine of Turin", "links": [{"title": "Adelaide of Susa", "link": "https://wikipedia.org/wiki/Adelaide_of_Susa"}]}, {"year": "1111", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Persian jurist, philosopher, theologian, and mystic (b. 1058)", "html": "1111 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"Al-Gha<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian jurist, philosopher, theologian, and mystic (b. 1058)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON>G<PERSON>i\" title=\"Al<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian jurist, philosopher, theologian, and mystic (b. 1058)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-<PERSON>hazali"}]}, {"year": "1123", "text": "<PERSON> <PERSON><PERSON><PERSON>, Italian bishop and saint", "html": "1123 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Be<PERSON>do\" title=\"<PERSON> Be<PERSON>\"><PERSON></a>, Italian bishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Be<PERSON>do\" title=\"Saint Be<PERSON>\"><PERSON></a>, Italian bishop and saint", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Berardo"}]}, {"year": "1327", "text": "<PERSON> of France, Duchess of Burgundy (b. 1260)", "html": "1327 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_France,_Duchess_of_Burgundy\" title=\"<PERSON> of France, Duchess of Burgundy\"><PERSON> of France, Duchess of Burgundy</a> (b. 1260)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_France,_Duchess_of_Burgundy\" title=\"<PERSON> of France, Duchess of Burgundy\"><PERSON> of France, Duchess of Burgundy</a> (b. 1260)", "links": [{"title": "<PERSON> of France, Duchess of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Burgundy"}]}, {"year": "1370", "text": "<PERSON> (b. 1310)", "html": "1370 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_V\" title=\"Pope Urban V\">Pope Urban V</a> (b. 1310)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_V\" title=\"Pope Urban V\">Pope Urban V</a> (b. 1310)", "links": [{"title": "Pope Urban V", "link": "https://wikipedia.org/wiki/Pope_Urban_V"}]}, {"year": "1442", "text": "<PERSON> Luxembourg (b. 1409)", "html": "1442 - <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Luxembourg\" title=\"<PERSON> of Luxembourg\"><PERSON> of Luxembourg</a> (b. 1409)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_of_Luxembourg\" title=\"<PERSON> of Luxembourg\"><PERSON> of Luxembourg</a> (b. 1409)", "links": [{"title": "<PERSON> of Luxembourg", "link": "https://wikipedia.org/wiki/Elizabeth_of_Luxembourg"}]}, {"year": "1385", "text": "<PERSON><PERSON><PERSON>, Lord of Milan (b. 1319)", "html": "1385 - <a href=\"https://wikipedia.org/wiki/Bernab%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lord of <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> (b. 1319)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bernab%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Visconti\"><PERSON><PERSON><PERSON></a>, Lord of <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> (b. 1319)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bernab%C3%B2_<PERSON><PERSON><PERSON>"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}]}, {"year": "1558", "text": "<PERSON>, Flemish writer (b. 1482)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_G<PERSON>heus\" title=\"Cornelius Grapheus\"><PERSON></a>, Flemish writer (b. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_G<PERSON>\" title=\"Cornelius Grapheus\"><PERSON></a>, Flemish writer (b. 1482)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cornelius_Grapheus"}]}, {"year": "1637", "text": "<PERSON> of Lorraine, Grand Duchess consort of Tuscany (b. 1565)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lorraine\" title=\"<PERSON> of Lorraine\"><PERSON> of Lorraine</a>, Grand Duchess consort of Tuscany (b. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lorraine\"><PERSON> of Lorraine</a>, Grand Duchess consort of Tuscany (b. 1565)", "links": [{"title": "<PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lorraine"}]}, {"year": "1741", "text": "<PERSON><PERSON><PERSON>, Danish-born Russian explorer (b. 1681)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/Vitus_<PERSON>\" title=\"Vitus Be<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-born Russian explorer (b. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vitus_<PERSON>\" title=\"Vitus Bering\"><PERSON><PERSON><PERSON></a>, Danish-born Russian explorer (b. 1681)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitus_<PERSON>"}]}, {"year": "1745", "text": "<PERSON><PERSON><PERSON>, French painter (b. 1684)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1684)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, Italian priest and composer (b. 1672)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (b. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, Baron <PERSON>, German-French author and playwright (b. 1723)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, German-French author and playwright (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, German-French author and playwright (b. 1723)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, Scottish-Canadian businessman and philanthropist, founded McGill University (b. 1744)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/McGill_University\" title=\"McGill University\">McGill University</a> (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/McGill_University\" title=\"McGill University\">McGill University</a> (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McGill University", "link": "https://wikipedia.org/wiki/McGill_University"}]}, {"year": "1819", "text": "<PERSON>, English admiral and politician (b. 1765)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician (b. 1765)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1848", "text": "<PERSON>, English novelist and poet (b. 1818)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>t%C3%AB\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AB\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emily_Bront%C3%AB"}]}, {"year": "1851", "text": "<PERSON>, English painter (b. 1775)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, American author and poet (b. 1825)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and poet (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and poet (b. 1825)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American general (b. 1843)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, German psychiatrist and neuropathologist (b. 1864)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Alzheimer\" title=\"Alois Alzheimer\"><PERSON><PERSON></a>, German psychiatrist and neuropathologist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Alzheimer\" title=\"Alois Alzheimer\"><PERSON><PERSON></a>, German psychiatrist and neuropathologist (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Burmese king (b. 1859)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Thiba<PERSON>_<PERSON>\" title=\"Thibaw <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thibaw_<PERSON>\" title=\"Thibaw Min\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Burmese king (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thibaw_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian activist (b. 1900)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian activist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian activist (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Indian poet and activist (b. 1897)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian poet and activist (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Indian poet and activist (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, South Korean activist (b. 1908)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gil\" title=\"<PERSON> Bong-gil\"><PERSON>-g<PERSON></a>, South Korean activist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gil\" title=\"<PERSON> Bong-gil\"><PERSON>-<PERSON></a>, South Korean activist (b. 1908)", "links": [{"title": "<PERSON>gil", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-gil"}]}, {"year": "1933", "text": "<PERSON>, English engineer and businessman (b. 1857)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and politician (b. 1873)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish politician, the 4th President of Finland (b. 1873)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Ky%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish politician, the 4th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ky%C3%B6<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish politician, the 4th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ky%C3%B6sti_Ka<PERSON>o"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1944", "text": "<PERSON> of Egypt (b. 1874)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Abbas_II_of_Egypt\" title=\"Abbas II of Egypt\"><PERSON> II of Egypt</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abbas_II_of_Egypt\" title=\"Abbas II of Egypt\"><PERSON> II of Egypt</a> (b. 1874)", "links": [{"title": "<PERSON> of Egypt", "link": "https://wikipedia.org/wiki/Abbas_II_of_Egypt"}]}, {"year": "1944", "text": "<PERSON>, German businessman (b. 1856)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, French physicist and academic (b. 1872)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American physicist and eugenicist, Nobel Prize laureate (b. 1868)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and eugenicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and eugenicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1968", "text": "<PERSON>, American minister and politician (b. 1884)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Turkish journalist, author, and academic (b. 1888)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>met <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Turkish journalist, author, and academic (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Turkish journalist, author, and academic (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian painter (b. 1893)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American philosopher, author, and critic (b. 1906)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, author, and critic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, author, and critic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American missionary (b. 1903)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joy_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author (b. 1923)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author (b. 1923)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Russian-German colonel and diplomat (b. 1895)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German colonel and diplomat (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German colonel and diplomat (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Estonian author, playwright, and politician (b. 1900)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/August_M%C3%A4lk\" title=\"August Mälk\">August <PERSON></a>, Estonian author, playwright, and politician (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_M%C3%A4lk\" title=\"August Mälk\">August <PERSON></a>, Estonian author, playwright, and politician (b. 1900)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_M%C3%A4lk"}]}, {"year": "1988", "text": "<PERSON>, American author and playwright (b. 1919)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and playwright (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and playwright (b. 1919)", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_(comics)"}]}, {"year": "1988", "text": "<PERSON>, Burmese student activist (b. 1971)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>w_Oo\" title=\"Win Maw Oo\"><PERSON></a>, Burmese student activist (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Win Maw Oo\"><PERSON></a>, Burmese student activist (b. 1971)", "links": [{"title": "<PERSON> <PERSON>w <PERSON>o", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>w_Oo"}]}, {"year": "1989", "text": "<PERSON>, English journalist, author, and poet (b. 1902)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and poet (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and poet (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Belarusian Soviet politician (b. 1914)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> politician (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1993", "text": "<PERSON>, American drummer (b. 1946)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer (b. 1946)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Italian-French actor and singer (b. 1924)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French actor and singer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French actor and singer (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American occultist (b. 1924)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Hollister\"><PERSON></a>, American occultist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Hollister\"><PERSON></a>, American occultist (b. 1924)", "links": [{"title": "<PERSON>llister", "link": "https://wikipedia.org/wiki/<PERSON>_Northrup_Hollister"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Japanese businessman, co-founded Sony (b. 1908)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Masaru_I<PERSON>\" title=\"Masaru Ibuka\"><PERSON><PERSON><PERSON></a>, Japanese businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sar<PERSON>_<PERSON>\" title=\"Masaru Ibuka\"><PERSON><PERSON><PERSON></a>, Japanese businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Sony", "link": "https://wikipedia.org/wiki/Sony"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1924)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American treasure hunter (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American treasure hunter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American treasure hunter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Welsh soldier and actor (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh soldier and actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh soldier and actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American guitarist and songwriter (b. 1958)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Buck\"><PERSON></a>, American guitarist and songwriter (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American bassist and photographer (b. 1910)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bassist and photographer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bassist and photographer (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nton"}]}, {"year": "2000", "text": "<PERSON>, American lawyer and politician, 103rd Mayor of New York City (b. 1921)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 103rd <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 103rd <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "2002", "text": "<PERSON>, English race car driver (b. 1952)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English footballer and manager (b. 1926)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American author, playwright, and journalist (b. 1907)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and journalist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and journalist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English lawyer, founded <PERSON><PERSON><PERSON><PERSON> (b. 1914)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON><PERSON>\"><PERSON>-<PERSON></a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON><PERSON>\"><PERSON>-<PERSON></a> (b. 1914)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actress (b. 1933)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English-American chemist and academic, Nobel Prize laureate (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Italian soprano and actress (b. 1922)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American mobster (b. 1927)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mobster (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mobster (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American minister and activist (b. 1936)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American linguist and educator (b. 1930)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and educator (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and educator (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American political consultant (b. 1963)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political consultant (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political consultant (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American baseball player and coach (b. 1945)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ellis"}]}, {"year": "2009", "text": "<PERSON>, American megasavant (b. 1951)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Megasavant\" class=\"mw-redirect\" title=\"Megasavant\">megasavant</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Megasavant\" class=\"mw-redirect\" title=\"Megasavant\">megasavant</a> (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Megasavant", "link": "https://wikipedia.org/wiki/Megasavant"}]}, {"year": "2010", "text": "<PERSON>, English journalist and author (b. 1934)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author (b. 1934)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "2012", "text": "<PERSON>, American lawyer, judge, and scholar, United States Attorney General (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and scholar, <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and scholar, <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Israeli general and politician, 22nd Transportation Minister of Israel (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 22nd <a href=\"https://wikipedia.org/wiki/Transportation_Minister_of_Israel\" class=\"mw-redirect\" title=\"Transportation Minister of Israel\">Transportation Minister of Israel</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 22nd <a href=\"https://wikipedia.org/wiki/Transportation_Minister_of_Israel\" class=\"mw-redirect\" title=\"Transportation Minister of Israel\">Transportation Minister of Israel</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}, {"title": "Transportation Minister of Israel", "link": "https://wikipedia.org/wiki/Transportation_Minister_of_Israel"}]}, {"year": "2012", "text": "<PERSON>, American football player (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, German lawyer and politician, 13th German Federal Minister of Defence (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/List_of_German_defence_ministers\" title=\"List of German defence ministers\">German Federal Minister of Defence</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/List_of_German_defence_ministers\" title=\"List of German defence ministers\">German Federal Minister of Defence</a> (b. 1943)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "List of German defence ministers", "link": "https://wikipedia.org/wiki/List_of_German_defence_ministers"}]}, {"year": "2013", "text": "<PERSON><PERSON>, English musicologist and author (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musicologist and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dean\"><PERSON><PERSON></a>, English musicologist and author (b. 1916)", "links": [{"title": "<PERSON><PERSON> Dean", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American publisher and pornographer (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and pornographer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and pornographer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Goldstein"}]}, {"year": "2013", "text": "<PERSON>, American author and screenwriter (b. 1981)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Viz<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Viz<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ned_Vizzini"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Indian journalist and director (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/S._Balasubramanian\" class=\"mw-redirect\" title=\"S. Balasubramanian\"><PERSON><PERSON></a>, Indian journalist and director (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._Balasubramanian\" class=\"mw-redirect\" title=\"S. Balasubramanian\"><PERSON><PERSON></a>, Indian journalist and director (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._Balasubramanian"}]}, {"year": "2014", "text": "<PERSON>, English lawyer and politician (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actor and producer (b. 1910)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American actor and producer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American actor and producer (b. 1910)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "2014", "text": "<PERSON>, Russian general and politician, 3rd Russian Minister of Defence (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Russian Minister of Defence</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Russian Minister of Defence</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Defence (Russia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)"}]}, {"year": "2014", "text": "<PERSON>, American-Canadian football player and coach (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, American-Canadian football player and coach (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, American-Canadian football player and coach (b. 1939)", "links": [{"title": "<PERSON> (Canadian football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)"}]}, {"year": "2014", "text": "<PERSON> (<PERSON>), British writer, artist and TV producer (b. 1926).", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (<PERSON>), British writer, artist and TV producer (b. 1926).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (<PERSON>), British writer, artist and TV producer (b. 1926).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English footballer, manager, and sportscaster (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Baron <PERSON> of Braunstone, Welsh-English lawyer and politician (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Baron <PERSON> of Braunstone, Welsh-English lawyer and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Baron <PERSON> of Braunstone, Welsh-English lawyer and politician (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Swedish educator and politician, 33rd Swedish Minister for Foreign Affairs (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B6der\" title=\"<PERSON>\"><PERSON></a>, Swedish educator and politician, 33rd <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)\" title=\"Minister for Foreign Affairs (Sweden)\">Swedish Minister for Foreign Affairs</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B6der\" title=\"<PERSON>\"><PERSON></a>, Swedish educator and politician, 33rd <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)\" title=\"Minister for Foreign Affairs (Sweden)\">Swedish Minister for Foreign Affairs</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karin_S%C3%B6der"}, {"title": "Minister for Foreign Affairs (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)"}]}, {"year": "2016", "text": "<PERSON>, Russian diplomat, Ambassador to Turkey (b. 1954)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian diplomat, Ambassador to Turkey (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian diplomat, Ambassador to Turkey (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, English actress (b. 1933)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English-American singer and actress (b. 1930)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer and actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer and actress (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American politician (b. 1944)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian cartoonist (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}