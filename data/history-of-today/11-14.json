{"date": "November 14", "url": "https://wikipedia.org/wiki/November_14", "data": {"Events": [{"year": "332 BC", "text": "<PERSON> the Great is crowned pharaoh of Egypt.", "html": "332 BC - 332 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> is crowned <a href=\"https://wikipedia.org/wiki/<PERSON>araoh\" title=\"<PERSON>araoh\">pharaoh</a> of <a href=\"https://wikipedia.org/wiki/Ancient_Egypt#Ptolemaic_period_(332-30_BC)\" title=\"Ancient Egypt\">Egypt</a>.", "no_year_html": "332 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> is crowned <a href=\"https://wikipedia.org/wiki/<PERSON>araoh\" title=\"<PERSON>araoh\">pharaoh</a> of <a href=\"https://wikipedia.org/wiki/Ancient_Egypt#Ptolemaic_period_(332-30_BC)\" title=\"Ancient Egypt\">Egypt</a>.", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Ancient Egypt", "link": "https://wikipedia.org/wiki/Ancient_Egypt#Ptolemaic_period_(332-30_BC)"}]}, {"year": "1680", "text": "German astronomer <PERSON><PERSON><PERSON> discovers the Great Comet of 1680, the first comet to be discovered by telescope.", "html": "1680 - German astronomer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/Great_Comet_of_1680\" title=\"Great Comet of 1680\">Great Comet of 1680</a>, the first <a href=\"https://wikipedia.org/wiki/Comet\" title=\"Comet\">comet</a> to be discovered by telescope.", "no_year_html": "German astronomer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/Great_Comet_of_1680\" title=\"Great Comet of 1680\">Great Comet of 1680</a>, the first <a href=\"https://wikipedia.org/wiki/Comet\" title=\"Comet\">comet</a> to be discovered by telescope.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Great Comet of 1680", "link": "https://wikipedia.org/wiki/Great_Comet_of_1680"}, {"title": "Comet", "link": "https://wikipedia.org/wiki/Comet"}]}, {"year": "1770", "text": "<PERSON> discovers what he believes to be the source of the Nile.", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers what he believes to be the source of the <a href=\"https://wikipedia.org/wiki/Nile\" title=\"Nile\">Nile</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers what he believes to be the source of the <a href=\"https://wikipedia.org/wiki/Nile\" title=\"Nile\">Nile</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nile", "link": "https://wikipedia.org/wiki/Nile"}]}, {"year": "1812", "text": "Napoleonic Wars: At the Battle of Smoliani, French Marshals <PERSON> and <PERSON><PERSON><PERSON> are defeated by the Russians under General <PERSON>.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Smoliani\" title=\"Battle of Smoliani\">Battle of Smoliani</a>, French Marshals <a href=\"https://wikipedia.org/wiki/Marshal_<PERSON>\" class=\"mw-redirect\" title=\"Marshal <PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Marshal_<PERSON>\" class=\"mw-redirect\" title=\"Marshal <PERSON>\"><PERSON><PERSON><PERSON></a> are defeated by the Russians under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Smoliani\" title=\"Battle of Smoliani\">Battle of Smoliani</a>, French Marshals <a href=\"https://wikipedia.org/wiki/Marshal_<PERSON>\" class=\"mw-redirect\" title=\"Marshal <PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Marshal_<PERSON>\" class=\"mw-redirect\" title=\"Marshal <PERSON>\"><PERSON><PERSON><PERSON></a> are defeated by the Russians under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Battle of Smoliani", "link": "https://wikipedia.org/wiki/Battle_of_Smoliani"}, {"title": "Marshal <PERSON>", "link": "https://wikipedia.org/wiki/Marshal_<PERSON>_<PERSON>"}, {"title": "Marshal <PERSON>", "link": "https://wikipedia.org/wiki/Marshal_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON><PERSON>, a novel by <PERSON>, is published in the USA.", "html": "1851 - <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a></i>, a novel by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is published in the USA.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a></i>, a novel by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is published in the USA.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "Pioneering female journalist <PERSON><PERSON> (aka <PERSON>) begins a successful attempt to travel around the world in less than 80 days. She completes the trip in 72 days.", "html": "1889 - Pioneering female journalist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (aka <PERSON>) begins a successful attempt to travel around the world in less than 80 days. She completes the trip in 72 days.", "no_year_html": "Pioneering female journalist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (aka <PERSON>) begins a successful attempt to travel around the world in less than 80 days. She completes the trip in 72 days.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "Aviator <PERSON> performs the first takeoff from a ship in Hampton Roads, Virginia, taking off from a makeshift deck on the USS Birmingham in a Curtiss pusher.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Aviator\" class=\"mw-redirect\" title=\"Aviator\">Aviator</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs the first <a href=\"https://wikipedia.org/wiki/Takeoff\" title=\"Takeoff\">takeoff</a> from a ship in <a href=\"https://wikipedia.org/wiki/Hampton_Roads\" title=\"Hampton Roads\">Hampton Roads</a>, <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>, taking off from a makeshift deck on the <a href=\"https://wikipedia.org/wiki/USS_Birmingham_(CL-2)\" title=\"USS Birmingham (CL-2)\">USS <i>Birmingham</i></a> in a <a href=\"https://wikipedia.org/wiki/Curtiss_Model_D\" title=\"Curtiss Model D\"><PERSON><PERSON> pusher</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aviator\" class=\"mw-redirect\" title=\"Aviator\">Aviator</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs the first <a href=\"https://wikipedia.org/wiki/Takeoff\" title=\"Takeoff\">takeoff</a> from a ship in <a href=\"https://wikipedia.org/wiki/Hampton_Roads\" title=\"Hampton Roads\">Hampton Roads</a>, <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>, taking off from a makeshift deck on the <a href=\"https://wikipedia.org/wiki/USS_Birmingham_(CL-2)\" title=\"USS Birmingham (CL-2)\">USS <i>Birmingham</i></a> in a <a href=\"https://wikipedia.org/wiki/Curtiss_Model_D\" title=\"Curtiss Model D\"><PERSON><PERSON> pusher</a>.", "links": [{"title": "Aviator", "link": "https://wikipedia.org/wiki/Aviator"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Takeoff", "link": "https://wikipedia.org/wiki/Takeoff"}, {"title": "Hampton Roads", "link": "https://wikipedia.org/wiki/Hampton_Roads"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "USS Birmingham (CL-2)", "link": "https://wikipedia.org/wiki/USS_Birmingham_(CL-2)"}, {"title": "Curtiss Model D", "link": "https://wikipedia.org/wiki/<PERSON>s_Model_D"}]}, {"year": "1914", "text": "The Joensuu City Hall, designed by <PERSON><PERSON>, was inaugurated in Joensuu, Finland.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Joensuu_City_Hall\" title=\"Joensuu City Hall\">Joensuu City Hall</a>, designed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, was inaugurated in <a href=\"https://wikipedia.org/wiki/Joensuu\" title=\"Joensuu\">Joensuu</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Joensuu_City_Hall\" title=\"Joensuu City Hall\">Joensuu City Hall</a>, designed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, was inaugurated in <a href=\"https://wikipedia.org/wiki/Joensuu\" title=\"Joensuu\">Joensuu</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "links": [{"title": "Joensuu City Hall", "link": "https://wikipedia.org/wiki/Joensuu_City_Hall"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joensuu"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "1918", "text": "The Provisional National Assembly of the new republic of Czechoslovakia meets to devise a constitution.", "html": "1918 - The Provisional National Assembly of the new republic of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> meets to devise a constitution.", "no_year_html": "The Provisional National Assembly of the new republic of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> meets to devise a constitution.", "links": [{"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}]}, {"year": "1920", "text": "Pesäpallo, the Finnish version of baseball developed by <PERSON><PERSON>, is played for the first time at Kaisaniemi Park in Helsinki.", "html": "1920 - <i><a href=\"https://wikipedia.org/wiki/Pes%C3%A4pallo\" title=\"Pesäpallo\"><PERSON><PERSON>äpal<PERSON></a></i>, the Finnish version of <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a> developed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is played for the first time at <a href=\"https://wikipedia.org/wiki/Kaisaniemi_Park\" title=\"Kaisaniemi Park\">Kaisaniemi Park</a> in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Pes%C3%A4pallo\" title=\"Pesäpallo\">P<PERSON>äpal<PERSON></a></i>, the Finnish version of <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a> developed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is played for the first time at <a href=\"https://wikipedia.org/wiki/Kaisaniemi_Park\" title=\"Kaisaniemi Park\">Kaisaniemi Park</a> in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "links": [{"title": "P<PERSON>ä<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pes%C3%A4pallo"}, {"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Kaisaniemi Park", "link": "https://wikipedia.org/wiki/Kaisaniemi_Park"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1921", "text": "The Communist Party of Spain is founded, and issues the first edition of Mundo obrero.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Spain\" title=\"Communist Party of Spain\">Communist Party of Spain</a> is founded, and issues the first edition of <i>Mundo obrero</i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Spain\" title=\"Communist Party of Spain\">Communist Party of Spain</a> is founded, and issues the first edition of <i>Mundo obrero</i>.", "links": [{"title": "Communist Party of Spain", "link": "https://wikipedia.org/wiki/Communist_Party_of_Spain"}]}, {"year": "1922", "text": "The British Broadcasting Company begins radio service in the United Kingdom.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/British_Broadcasting_Company\" title=\"British Broadcasting Company\">British Broadcasting Company</a> begins radio service in the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/British_Broadcasting_Company\" title=\"British Broadcasting Company\">British Broadcasting Company</a> begins radio service in the United Kingdom.", "links": [{"title": "British Broadcasting Company", "link": "https://wikipedia.org/wiki/British_Broadcasting_Company"}]}, {"year": "1938", "text": "The Lions Gate Bridge, connecting Vancouver to the North Shore region, opens to traffic.", "html": "1938 - The <a href=\"https://wikipedia.org/wiki/Lions_Gate_Bridge\" title=\"Lions Gate Bridge\">Lions Gate Bridge</a>, connecting <a href=\"https://wikipedia.org/wiki/Vancouver\" title=\"Vancouver\">Vancouver</a> to the <a href=\"https://wikipedia.org/wiki/North_Shore_(Greater_Vancouver)\" title=\"North Shore (Greater Vancouver)\">North Shore</a> region, opens to traffic.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lions_Gate_Bridge\" title=\"Lions Gate Bridge\">Lions Gate Bridge</a>, connecting <a href=\"https://wikipedia.org/wiki/Vancouver\" title=\"Vancouver\">Vancouver</a> to the <a href=\"https://wikipedia.org/wiki/North_Shore_(Greater_Vancouver)\" title=\"North Shore (Greater Vancouver)\">North Shore</a> region, opens to traffic.", "links": [{"title": "Lions Gate Bridge", "link": "https://wikipedia.org/wiki/Lions_Gate_Bridge"}, {"title": "Vancouver", "link": "https://wikipedia.org/wiki/Vancouver"}, {"title": "North Shore (Greater Vancouver)", "link": "https://wikipedia.org/wiki/North_Shore_(Greater_Vancouver)"}]}, {"year": "1940", "text": "World War II: In England, Coventry is heavily bombed by German Luftwaffe bombers. Coventry Cathedral is almost completely destroyed.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In England, <a href=\"https://wikipedia.org/wiki/Coventry_Blitz\" title=\"Coventry Blitz\">Coventry</a> is heavily bombed by German <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> bombers. <a href=\"https://wikipedia.org/wiki/Coventry_Cathedral\" title=\"Coventry Cathedral\">Coventry Cathedral</a> is almost completely destroyed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In England, <a href=\"https://wikipedia.org/wiki/Coventry_Blitz\" title=\"Coventry Blitz\">Coventry</a> is heavily bombed by German <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> bombers. <a href=\"https://wikipedia.org/wiki/Coventry_Cathedral\" title=\"Coventry Cathedral\">Coventry Cathedral</a> is almost completely destroyed.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Coventry Blitz", "link": "https://wikipedia.org/wiki/Coventry_Blitz"}, {"title": "Luftwaffe", "link": "https://wikipedia.org/wiki/Luftwaffe"}, {"title": "Coventry Cathedral", "link": "https://wikipedia.org/wiki/Coventry_Cathedral"}]}, {"year": "1941", "text": "World War II: The aircraft carrier HMS Ark Royal sinks due to torpedo damage from the German submarine U-81 sustained on November 13.", "html": "1941 - World War II: The <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/HMS_Ark_Royal_(91)\" title=\"HMS Ark Royal (91)\">HMS <i>Ark Royal</i></a> sinks due to torpedo damage from the <a href=\"https://wikipedia.org/wiki/German_submarine_U-81_(1941)\" title=\"German submarine U-81 (1941)\">German submarine <i>U-81</i></a> sustained on November 13.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/HMS_Ark_Royal_(91)\" title=\"HMS Ark Royal (91)\">HMS <i>Ark Royal</i></a> sinks due to torpedo damage from the <a href=\"https://wikipedia.org/wiki/German_submarine_U-81_(1941)\" title=\"German submarine U-81 (1941)\">German submarine <i>U-81</i></a> sustained on November 13.", "links": [{"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}, {"title": "HMS Ark Royal (91)", "link": "https://wikipedia.org/wiki/HMS_Ark_Royal_(91)"}, {"title": "German submarine U-81 (1941)", "link": "https://wikipedia.org/wiki/German_submarine_U-81_(1941)"}]}, {"year": "1941", "text": "World War II: German troops, aided by local auxiliaries, murder nine thousand residents of the Słonim Ghetto in a single day.", "html": "1941 - World War II: German troops, aided by local auxiliaries, murder nine thousand residents of the <a href=\"https://wikipedia.org/wiki/S%C5%82onim_Ghetto\" title=\"Słonim Ghetto\">Słonim Ghetto</a> in a single day.", "no_year_html": "World War II: German troops, aided by local auxiliaries, murder nine thousand residents of the <a href=\"https://wikipedia.org/wiki/S%C5%82onim_Ghetto\" title=\"Słonim Ghetto\">Słonim Ghetto</a> in a single day.", "links": [{"title": "Słonim Ghetto", "link": "https://wikipedia.org/wiki/S%C5%82onim_Ghetto"}]}, {"year": "1952", "text": "The New Musical Express publishes the first regular UK Singles Chart.", "html": "1952 - The <i><a href=\"https://wikipedia.org/wiki/NME\" title=\"NME\">New Musical Express</a></i> publishes the first regular <a href=\"https://wikipedia.org/wiki/UK_Singles_Chart\" class=\"mw-redirect\" title=\"UK Singles Chart\">UK Singles Chart</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/NME\" title=\"NME\">New Musical Express</a></i> publishes the first regular <a href=\"https://wikipedia.org/wiki/UK_Singles_Chart\" class=\"mw-redirect\" title=\"UK Singles Chart\">UK Singles Chart</a>.", "links": [{"title": "NME", "link": "https://wikipedia.org/wiki/NME"}, {"title": "UK Singles Chart", "link": "https://wikipedia.org/wiki/UK_Singles_Chart"}]}, {"year": "1957", "text": "The \"Apalachin meeting\" in rural Tioga County in upstate New York is raided by law enforcement; many high-level Mafia figures are arrested while trying to flee.", "html": "1957 - The \"<a href=\"https://wikipedia.org/wiki/Apalachin_meeting\" title=\"Apalachin meeting\">Apalachin meeting</a>\" in rural <a href=\"https://wikipedia.org/wiki/Tioga_County,_New_York\" title=\"Tioga County, New York\">Tioga County in upstate New York</a> is raided by law enforcement; many high-level Mafia figures are arrested while trying to flee.", "no_year_html": "The \"<a href=\"https://wikipedia.org/wiki/Apalachin_meeting\" title=\"Apalachin meeting\">Apalachin meeting</a>\" in rural <a href=\"https://wikipedia.org/wiki/Tioga_County,_New_York\" title=\"Tioga County, New York\">Tioga County in upstate New York</a> is raided by law enforcement; many high-level Mafia figures are arrested while trying to flee.", "links": [{"title": "Apalachin meeting", "link": "https://wikipedia.org/wiki/Apalachin_meeting"}, {"title": "Tioga County, New York", "link": "https://wikipedia.org/wiki/Tioga_County,_New_York"}]}, {"year": "1960", "text": "<PERSON> becomes the first black child to attend an all-white elementary school in Louisiana.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bridges\" title=\"Ruby Bridges\"><PERSON></a> becomes the first black child to attend an all-white elementary school in <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bridges\" title=\"Ruby Bridges\"><PERSON></a> becomes the first black child to attend an all-white elementary school in <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ruby_Bridges"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}]}, {"year": "1965", "text": "Vietnam War: The Battle of Ia Drang begins: The first major engagement between regular American and North Vietnamese forces.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Ia_Drang\" title=\"Battle of Ia Drang\">Battle of Ia Drang</a> begins: The first major engagement between regular American and <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Ia_Drang\" title=\"Battle of Ia Drang\">Battle of Ia Drang</a> begins: The first major engagement between regular American and <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> forces.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Battle of Ia Drang", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>_Drang"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}]}, {"year": "1967", "text": "The Congress of Colombia, in commemoration of the 150th anniversary of the death of <PERSON><PERSON><PERSON><PERSON>, declares this day as \"Day of the Colombian Woman\".", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Congress_of_Colombia\" title=\"Congress of Colombia\">Congress of Colombia</a>, in commemoration of the 150th anniversary of the death of <a href=\"https://wikipedia.org/wiki/Policarpa_Salavarrieta\" title=\"Policarpa Salavarrieta\">Policarpa Salavarrieta</a>, declares this day as \"Day of the Colombian Woman\".", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congress_of_Colombia\" title=\"Congress of Colombia\">Congress of Colombia</a>, in commemoration of the 150th anniversary of the death of <a href=\"https://wikipedia.org/wiki/Policarpa_Salavarrieta\" title=\"Policarpa Salavarrieta\">Policarpa Salavarrieta</a>, declares this day as \"Day of the Colombian Woman\".", "links": [{"title": "Congress of Colombia", "link": "https://wikipedia.org/wiki/Congress_of_Colombia"}, {"title": "Policarpa <PERSON>", "link": "https://wikipedia.org/wiki/Policarpa_Salavarrieta"}]}, {"year": "1967", "text": "American physicist <PERSON> is given a patent for his ruby laser systems, the world's first laser.", "html": "1967 - American physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is given a patent for his <a href=\"https://wikipedia.org/wiki/Ruby_laser\" title=\"Ruby laser\">ruby laser</a> systems, the world's first laser.", "no_year_html": "American physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is given a patent for his <a href=\"https://wikipedia.org/wiki/Ruby_laser\" title=\"Ruby laser\">ruby laser</a> systems, the world's first laser.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Ruby laser", "link": "https://wikipedia.org/wiki/Ruby_laser"}]}, {"year": "1969", "text": "Apollo program: NASA launches Apollo 12, the second crewed mission to the surface of the Moon.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Apollo_12\" title=\"Apollo 12\">Apollo 12</a>, the second <a href=\"https://wikipedia.org/wiki/Space_exploration\" title=\"Space exploration\">crewed mission</a> to the surface of the Moon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Apollo_12\" title=\"Apollo 12\">Apollo 12</a>, the second <a href=\"https://wikipedia.org/wiki/Space_exploration\" title=\"Space exploration\">crewed mission</a> to the surface of the Moon.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Apollo 12", "link": "https://wikipedia.org/wiki/Apollo_12"}, {"title": "Space exploration", "link": "https://wikipedia.org/wiki/Space_exploration"}]}, {"year": "1970", "text": "Soviet Union enters ICAO, making Russian the fourth official language of organization.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> enters <a href=\"https://wikipedia.org/wiki/International_Civil_Aviation_Organization\" title=\"International Civil Aviation Organization\">ICAO</a>, making Russian the fourth official language of organization.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> enters <a href=\"https://wikipedia.org/wiki/International_Civil_Aviation_Organization\" title=\"International Civil Aviation Organization\">ICAO</a>, making Russian the fourth official language of organization.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "International Civil Aviation Organization", "link": "https://wikipedia.org/wiki/International_Civil_Aviation_Organization"}]}, {"year": "1970", "text": "Southern Airways Flight 932 crashes in the mountains near Huntington, West Virginia, killing 75, including almost all of the Marshall University football team.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Southern_Airways_Flight_932\" title=\"Southern Airways Flight 932\">Southern Airways Flight 932</a> crashes in the mountains near <a href=\"https://wikipedia.org/wiki/Huntington,_West_Virginia\" title=\"Huntington, West Virginia\">Huntington, West Virginia</a>, killing 75, including almost all of the <a href=\"https://wikipedia.org/wiki/Marshall_University\" title=\"Marshall University\">Marshall University</a> <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">football</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_Thundering_Herd_football\" title=\"Marshall Thundering Herd football\">team</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Southern_Airways_Flight_932\" title=\"Southern Airways Flight 932\">Southern Airways Flight 932</a> crashes in the mountains near <a href=\"https://wikipedia.org/wiki/Huntington,_West_Virginia\" title=\"Huntington, West Virginia\">Huntington, West Virginia</a>, killing 75, including almost all of the <a href=\"https://wikipedia.org/wiki/Marshall_University\" title=\"Marshall University\">Marshall University</a> <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">football</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_Thundering_Herd_football\" title=\"Marshall Thundering Herd football\">team</a>.", "links": [{"title": "Southern Airways Flight 932", "link": "https://wikipedia.org/wiki/Southern_Airways_Flight_932"}, {"title": "Huntington, West Virginia", "link": "https://wikipedia.org/wiki/Huntington,_West_Virginia"}, {"title": "Marshall University", "link": "https://wikipedia.org/wiki/Marshall_University"}, {"title": "American football", "link": "https://wikipedia.org/wiki/American_football"}, {"title": "<PERSON> Thundering Herd football", "link": "https://wikipedia.org/wiki/<PERSON>_Thundering_Herd_football"}]}, {"year": "1971", "text": "Mariner 9 enters orbit around Mars.", "html": "1971 - <i><a href=\"https://wikipedia.org/wiki/Mariner_9\" title=\"Mariner 9\">Mariner 9</a></i> enters orbit around <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Mariner_9\" title=\"Mariner 9\">Mariner 9</a></i> enters orbit around <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "Mariner 9", "link": "https://wikipedia.org/wiki/Mariner_9"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "1973", "text": "In the United Kingdom, Princess <PERSON> marries Captain <PERSON>, in Westminster Abbey.", "html": "1973 - In the United Kingdom, <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\">Princess <PERSON></a> marries <a href=\"https://wikipedia.org/wiki/Captain_(British_Army_and_Royal_Marines)\" title=\"Captain (British Army and Royal Marines)\">Captain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "In the United Kingdom, <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\">Princess <PERSON></a> marries <a href=\"https://wikipedia.org/wiki/Captain_(British_Army_and_Royal_Marines)\" title=\"Captain (British Army and Royal Marines)\">Captain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "<PERSON>, Princess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>"}, {"title": "Captain (British Army and Royal Marines)", "link": "https://wikipedia.org/wiki/Captain_(British_Army_and_Royal_Marines)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1973", "text": "The Athens Polytechnic uprising, a massive demonstration of popular rejection of the Greek military junta of 1967-74, begins.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/Athens_Polytechnic_uprising\" title=\"Athens Polytechnic uprising\">Athens Polytechnic uprising</a>, a massive demonstration of popular rejection of the <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">Greek military junta of 1967-74</a>, begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Athens_Polytechnic_uprising\" title=\"Athens Polytechnic uprising\">Athens Polytechnic uprising</a>, a massive demonstration of popular rejection of the <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">Greek military junta of 1967-74</a>, begins.", "links": [{"title": "Athens Polytechnic uprising", "link": "https://wikipedia.org/wiki/Athens_Polytechnic_uprising"}, {"title": "Greek military junta of 1967-74", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374"}]}, {"year": "1975", "text": "With the signing of the Madrid Accords, Spain abandons Western Sahara.", "html": "1975 - With the signing of the <a href=\"https://wikipedia.org/wiki/Madrid_Accords\" title=\"Madrid Accords\">Madrid Accords</a>, Spain abandons <a href=\"https://wikipedia.org/wiki/Western_Sahara\" title=\"Western Sahara\">Western Sahara</a>.", "no_year_html": "With the signing of the <a href=\"https://wikipedia.org/wiki/Madrid_Accords\" title=\"Madrid Accords\">Madrid Accords</a>, Spain abandons <a href=\"https://wikipedia.org/wiki/Western_Sahara\" title=\"Western Sahara\">Western Sahara</a>.", "links": [{"title": "Madrid Accords", "link": "https://wikipedia.org/wiki/Madrid_Accords"}, {"title": "Western Sahara", "link": "https://wikipedia.org/wiki/Western_Sahara"}]}, {"year": "1977", "text": "During a British House of Commons debate, Labour MP <PERSON> poses what would become known as the West Lothian question, referring to issues related to devolution in the United Kingdom.", "html": "1977 - During a British <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons</a> debate, <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour</a> MP <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> poses what would become known as the <a href=\"https://wikipedia.org/wiki/West_Lothian_question\" title=\"West Lothian question\">West Lothian question</a>, referring to issues related to <a href=\"https://wikipedia.org/wiki/Devolution_in_the_United_Kingdom\" title=\"Devolution in the United Kingdom\">devolution in the United Kingdom</a>.", "no_year_html": "During a British <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons</a> debate, <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour</a> MP <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> poses what would become known as the <a href=\"https://wikipedia.org/wiki/West_Lothian_question\" title=\"West Lothian question\">West Lothian question</a>, referring to issues related to <a href=\"https://wikipedia.org/wiki/Devolution_in_the_United_Kingdom\" title=\"Devolution in the United Kingdom\">devolution in the United Kingdom</a>.", "links": [{"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}, {"title": "Labour Party (UK)", "link": "https://wikipedia.org/wiki/Labour_Party_(UK)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "West Lothian question", "link": "https://wikipedia.org/wiki/West_Lothian_question"}, {"title": "Devolution in the United Kingdom", "link": "https://wikipedia.org/wiki/Devolution_in_the_United_Kingdom"}]}, {"year": "1978", "text": "France conducts the Aphrodite nuclear test as 25th in the group of 29 1975-78 French nuclear tests.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> conducts the <i>Aphrodite</i> nuclear test as 25th in the group of 29 <a href=\"https://wikipedia.org/wiki/1975%E2%80%9378_French_nuclear_tests\" title=\"1975-78 French nuclear tests\">1975-78 French nuclear tests</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> conducts the <i>Aphrodite</i> nuclear test as 25th in the group of 29 <a href=\"https://wikipedia.org/wiki/1975%E2%80%9378_French_nuclear_tests\" title=\"1975-78 French nuclear tests\">1975-78 French nuclear tests</a>.", "links": [{"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "1975-78 French nuclear tests", "link": "https://wikipedia.org/wiki/1975%E2%80%9378_French_nuclear_tests"}]}, {"year": "1979", "text": "US President <PERSON> issues Executive Order 12170, freezing all Iranian assets in the United States in response to the hostage crisis.", "html": "1979 - US President <PERSON> issues <a href=\"https://wikipedia.org/wiki/Executive_Order_12170\" title=\"Executive Order 12170\">Executive Order 12170</a>, freezing all Iranian assets in the United States in response to the hostage crisis.", "no_year_html": "US President <PERSON> issues <a href=\"https://wikipedia.org/wiki/Executive_Order_12170\" title=\"Executive Order 12170\">Executive Order 12170</a>, freezing all Iranian assets in the United States in response to the hostage crisis.", "links": [{"title": "Executive Order 12170", "link": "https://wikipedia.org/wiki/Executive_Order_12170"}]}, {"year": "1982", "text": "<PERSON><PERSON>, the leader of Poland's outlawed Solidarity movement, is released after eleven months of internment near the Soviet border.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa\" title=\"Lech Wałęsa\"><PERSON><PERSON></a>, the leader of Poland's outlawed <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a> movement, is released after eleven months of <a href=\"https://wikipedia.org/wiki/Internment\" title=\"Internment\">internment</a> near the Soviet border.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa\" title=\"Lech Wałęsa\"><PERSON><PERSON></a>, the leader of Poland's outlawed <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a> movement, is released after eleven months of <a href=\"https://wikipedia.org/wiki/Internment\" title=\"Internment\">internment</a> near the Soviet border.", "links": [{"title": "Lech Wałęsa", "link": "https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa"}, {"title": "Solidarity (Polish trade union)", "link": "https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)"}, {"title": "Internment", "link": "https://wikipedia.org/wiki/Internment"}]}, {"year": "1984", "text": "Zamboanga City mayor <PERSON><PERSON>, a prominent critic of the government of Philippine President <PERSON>, is assassinated in his home city.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Zamboanga_City\" title=\"Zamboanga City\">Zamboanga City</a> mayor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a prominent critic of the government of Philippine President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is assassinated in his home city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zamboanga_City\" title=\"Zamboanga City\">Zamboanga City</a> mayor <a href=\"https://wikipedia.org/wiki/Ce<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a prominent critic of the government of Philippine President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is assassinated in his home city.", "links": [{"title": "Zamboanga City", "link": "https://wikipedia.org/wiki/Zamboanga_City"}, {"title": "Ce<PERSON>", "link": "https://wikipedia.org/wiki/Cesar_Climaco"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "After German reunification, the Federal Republic of Germany and Poland sign a treaty confirming the Oder-Neisse line as the border between Germany and Poland.", "html": "1990 - After <a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">German reunification</a>, the <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Federal Republic of Germany</a> and Poland sign a <a href=\"https://wikipedia.org/wiki/German%E2%80%93Polish_Border_Treaty\" title=\"German-Polish Border Treaty\">treaty</a> confirming the <a href=\"https://wikipedia.org/wiki/Oder%E2%80%93Neisse_line\" title=\"Oder-Neisse line\">Oder-Neisse line</a> as the border between Germany and Poland.", "no_year_html": "After <a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">German reunification</a>, the <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Federal Republic of Germany</a> and Poland sign a <a href=\"https://wikipedia.org/wiki/German%E2%80%93Polish_Border_Treaty\" title=\"German-Polish Border Treaty\">treaty</a> confirming the <a href=\"https://wikipedia.org/wiki/Oder%E2%80%93Neisse_line\" title=\"Oder-Neisse line\">Oder-Neisse line</a> as the border between Germany and Poland.", "links": [{"title": "German reunification", "link": "https://wikipedia.org/wiki/German_reunification"}, {"title": "Germany", "link": "https://wikipedia.org/wiki/Germany"}, {"title": "German-Polish Border Treaty", "link": "https://wikipedia.org/wiki/German%E2%80%93Polish_Border_Treaty"}, {"title": "Oder-Neisse line", "link": "https://wikipedia.org/wiki/Oder%E2%80%93N<PERSON><PERSON>_line"}]}, {"year": "1990", "text": "While on approach to Zurich Airport, Alitalia Flight 404 crashes into Stadlerberg Mountain near Weiach, killing 46.", "html": "1990 - While on approach to <a href=\"https://wikipedia.org/wiki/Zurich_Airport\" title=\"Zurich Airport\">Zurich Airport</a>, <a href=\"https://wikipedia.org/wiki/Alitalia_Flight_404\" title=\"Alitalia Flight 404\">Alitalia Flight 404</a> crashes into Stadlerberg Mountain near <a href=\"https://wikipedia.org/wiki/Weiach\" title=\"<PERSON>ach\"><PERSON><PERSON></a>, killing 46.", "no_year_html": "While on approach to <a href=\"https://wikipedia.org/wiki/Zurich_Airport\" title=\"Zurich Airport\">Zurich Airport</a>, <a href=\"https://wikipedia.org/wiki/Alitalia_Flight_404\" title=\"Alitalia Flight 404\">Alitalia Flight 404</a> crashes into Stadlerberg Mountain near <a href=\"https://wikipedia.org/wiki/Weiach\" title=\"<PERSON>ach\"><PERSON><PERSON></a>, killing 46.", "links": [{"title": "Zurich Airport", "link": "https://wikipedia.org/wiki/Zurich_Airport"}, {"title": "Alitalia Flight 404", "link": "https://wikipedia.org/wiki/Alitalia_Flight_404"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ach"}]}, {"year": "1991", "text": "American and British authorities announce indictments against two Libyan intelligence officials in connection with the downing of the Pan Am Flight 103.", "html": "1991 - American and British authorities announce indictments against two Libyan <a href=\"https://wikipedia.org/wiki/Intelligence_assessment\" title=\"Intelligence assessment\">intelligence</a> officials in connection with the downing of the <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_103\" title=\"Pan Am Flight 103\">Pan Am Flight 103</a>.", "no_year_html": "American and British authorities announce indictments against two Libyan <a href=\"https://wikipedia.org/wiki/Intelligence_assessment\" title=\"Intelligence assessment\">intelligence</a> officials in connection with the downing of the <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_103\" title=\"Pan Am Flight 103\">Pan Am Flight 103</a>.", "links": [{"title": "Intelligence assessment", "link": "https://wikipedia.org/wiki/Intelligence_assessment"}, {"title": "Pan Am Flight 103", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_103"}]}, {"year": "1991", "text": "Cambodian Prince <PERSON><PERSON><PERSON> returns to Phnom Penh after thirteen years in exile.", "html": "1991 - Cambodian Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> after thirteen years in <a href=\"https://wikipedia.org/wiki/Exile\" title=\"Exile\">exile</a>.", "no_year_html": "Cambodian Prince <a href=\"https://wikipedia.org/wiki/Norod<PERSON>_<PERSON>\" title=\"Norod<PERSON>\"><PERSON><PERSON><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> after thirteen years in <a href=\"https://wikipedia.org/wiki/Exile\" title=\"Exile\">exile</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norodom_<PERSON>"}, {"title": "Phnom Penh", "link": "https://wikipedia.org/wiki/Phnom_Penh"}, {"title": "Exile", "link": "https://wikipedia.org/wiki/Exile"}]}, {"year": "1992", "text": " In poor conditions caused by Cyclone Forrest, Vietnam Airlines Flight 474 crashes near Nha Trang, killing 30.", "html": "1992 - In poor conditions caused by <a href=\"https://wikipedia.org/wiki/Cyclone_Forrest\" title=\"<PERSON> Forrest\">Cyclone <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Vietnam_Airlines_Flight_474\" title=\"Vietnam Airlines Flight 474\">Vietnam Airlines Flight 474</a> crashes near <a href=\"https://wikipedia.org/wiki/Nha_Trang\" title=\"Nha Trang\"><PERSON><PERSON> Trang</a>, killing 30.", "no_year_html": "In poor conditions caused by <a href=\"https://wikipedia.org/wiki/Cyclone_Forrest\" title=\"Cyclone Forrest\">Cyclone <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Vietnam_Airlines_Flight_474\" title=\"Vietnam Airlines Flight 474\">Vietnam Airlines Flight 474</a> crashes near <a href=\"https://wikipedia.org/wiki/Nha_Trang\" title=\"Nha Trang\"><PERSON>ha Trang</a>, killing 30.", "links": [{"title": "Cyclone <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vietnam Airlines Flight 474", "link": "https://wikipedia.org/wiki/Vietnam_Airlines_Flight_474"}, {"title": "Nha Trang", "link": "https://wikipedia.org/wiki/Nha_Trang"}]}, {"year": "1995", "text": "A budget standoff between Democrats and Republicans in the U.S. Congress forces the federal government to temporarily close national parks and museums and to run most government offices with skeleton staffs.", "html": "1995 - A budget standoff between Democrats and Republicans in the U.S. Congress forces the federal government to <a href=\"https://wikipedia.org/wiki/1995%E2%80%931996_United_States_federal_government_shutdowns\" title=\"1995-1996 United States federal government shutdowns\">temporarily close</a> national parks and museums and to run most government offices with skeleton staffs.", "no_year_html": "A budget standoff between Democrats and Republicans in the U.S. Congress forces the federal government to <a href=\"https://wikipedia.org/wiki/1995%E2%80%931996_United_States_federal_government_shutdowns\" title=\"1995-1996 United States federal government shutdowns\">temporarily close</a> national parks and museums and to run most government offices with skeleton staffs.", "links": [{"title": "1995-1996 United States federal government shutdowns", "link": "https://wikipedia.org/wiki/1995%E2%80%931996_United_States_federal_government_shutdowns"}]}, {"year": "2001", "text": "War in Afghanistan: Afghan Northern Alliance fighters take over the capital Kabul.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">War in Afghanistan</a>: Afghan <a href=\"https://wikipedia.org/wiki/Northern_Alliance\" title=\"Northern Alliance\">Northern Alliance</a> fighters <a href=\"https://wikipedia.org/wiki/Fall_of_Kabul_(2001)\" title=\"Fall of Kabul (2001)\">take over</a> the capital <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">War in Afghanistan</a>: Afghan <a href=\"https://wikipedia.org/wiki/Northern_Alliance\" title=\"Northern Alliance\">Northern Alliance</a> fighters <a href=\"https://wikipedia.org/wiki/Fall_of_Kabul_(2001)\" title=\"Fall of Kabul (2001)\">take over</a> the capital <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>.", "links": [{"title": "War in Afghanistan (2001-2021)", "link": "https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)"}, {"title": "Northern Alliance", "link": "https://wikipedia.org/wiki/Northern_Alliance"}, {"title": "Fall of Kabul (2001)", "link": "https://wikipedia.org/wiki/Fall_of_Kabul_(2001)"}, {"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}]}, {"year": "2001", "text": "A magnitude 7.8 earthquake strikes a remote part of the Tibetan plateau. It has the longest known surface rupture recorded on land (~400 km) and is the best documented example of a supershear earthquake.", "html": "2001 - A <a href=\"https://wikipedia.org/wiki/2001_Kunlun_earthquake\" title=\"2001 Kunlun earthquake\">magnitude 7.8 earthquake</a> strikes a remote part of the <a href=\"https://wikipedia.org/wiki/Tibetan_plateau\" class=\"mw-redirect\" title=\"Tibetan plateau\">Tibetan plateau</a>. It has the longest known <a href=\"https://wikipedia.org/wiki/Surface_rupture\" title=\"Surface rupture\">surface rupture</a> recorded on land (~400 km) and is the best documented example of a <a href=\"https://wikipedia.org/wiki/Supershear_earthquake\" title=\"Supershear earthquake\">supershear earthquake</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2001_Kunlun_earthquake\" title=\"2001 Kunlun earthquake\">magnitude 7.8 earthquake</a> strikes a remote part of the <a href=\"https://wikipedia.org/wiki/Tibetan_plateau\" class=\"mw-redirect\" title=\"Tibetan plateau\">Tibetan plateau</a>. It has the longest known <a href=\"https://wikipedia.org/wiki/Surface_rupture\" title=\"Surface rupture\">surface rupture</a> recorded on land (~400 km) and is the best documented example of a <a href=\"https://wikipedia.org/wiki/Supershear_earthquake\" title=\"Supershear earthquake\">supershear earthquake</a>.", "links": [{"title": "2001 Kunlun earthquake", "link": "https://wikipedia.org/wiki/2001_Kunlun_earthquake"}, {"title": "Tibetan plateau", "link": "https://wikipedia.org/wiki/Tibetan_plateau"}, {"title": "Surface rupture", "link": "https://wikipedia.org/wiki/Surface_rupture"}, {"title": "Supershear earthquake", "link": "https://wikipedia.org/wiki/Supershear_earthquake"}]}, {"year": "2003", "text": "Astronomers discover Sedna, a distant trans-Neptunian dwarf planet.", "html": "2003 - Astronomers discover <a href=\"https://wikipedia.org/wiki/Sedna_(dwarf_planet)\" title=\"Sedna (dwarf planet)\"><PERSON><PERSON><PERSON></a>, a distant trans-Neptunian <a href=\"https://wikipedia.org/wiki/Dwarf_planet\" title=\"Dwarf planet\">dwarf planet</a>.", "no_year_html": "Astronomers discover <a href=\"https://wikipedia.org/wiki/Sedna_(dwarf_planet)\" title=\"Sedna (dwarf planet)\"><PERSON><PERSON><PERSON></a>, a distant trans-Neptunian <a href=\"https://wikipedia.org/wiki/Dwarf_planet\" title=\"Dwarf planet\">dwarf planet</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (dwarf planet)", "link": "https://wikipedia.org/wiki/Sedna_(dwarf_planet)"}, {"title": "Dwarf planet", "link": "https://wikipedia.org/wiki/Dwarf_planet"}]}, {"year": "2008", "text": "The first G-20 economic summit opens in Washington, D.C.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/2008_G20_Washington_summit\" title=\"2008 G20 Washington summit\">first G-20 economic summit</a> opens in Washington, D.C.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2008_G20_Washington_summit\" title=\"2008 G20 Washington summit\">first G-20 economic summit</a> opens in Washington, D.C.", "links": [{"title": "2008 G20 Washington summit", "link": "https://wikipedia.org/wiki/2008_G20_Washington_summit"}]}, {"year": "2008", "text": "Space Shuttle Endeavour launches on STS-126 to continue assembly of the International Space Station.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-126\" title=\"STS-126\">STS-126</a> to continue <a href=\"https://wikipedia.org/wiki/Assembly_of_the_International_Space_Station\" title=\"Assembly of the International Space Station\">assembly of the International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-126\" title=\"STS-126\">STS-126</a> to continue <a href=\"https://wikipedia.org/wiki/Assembly_of_the_International_Space_Station\" title=\"Assembly of the International Space Station\">assembly of the International Space Station</a>.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-126", "link": "https://wikipedia.org/wiki/STS-126"}, {"title": "Assembly of the International Space Station", "link": "https://wikipedia.org/wiki/Assembly_of_the_International_Space_Station"}]}, {"year": "2012", "text": "Israel launches a major military operation in the Gaza Strip in response to an escalation of rocket attacks by Hamas.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches a <a href=\"https://wikipedia.org/wiki/2012_Israeli_operation_in_the_Gaza_Strip\" class=\"mw-redirect\" title=\"2012 Israeli operation in the Gaza Strip\">major military operation</a> in the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> in response to an escalation of rocket attacks by <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches a <a href=\"https://wikipedia.org/wiki/2012_Israeli_operation_in_the_Gaza_Strip\" class=\"mw-redirect\" title=\"2012 Israeli operation in the Gaza Strip\">major military operation</a> in the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> in response to an escalation of rocket attacks by <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a>.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "2012 Israeli operation in the Gaza Strip", "link": "https://wikipedia.org/wiki/2012_Israeli_operation_in_the_Gaza_Strip"}, {"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}, {"title": "Hamas", "link": "https://wikipedia.org/wiki/Hamas"}]}, {"year": "2016", "text": "A magnitude 7.8 earthquake strikes Kaikōura, New Zealand, at a depth of 15 km (9 miles), resulting in the deaths of two people.", "html": "2016 - A <a href=\"https://wikipedia.org/wiki/2016_Kaik%C5%8Dura_earthquake\" title=\"2016 Kaikōura earthquake\">magnitude 7.8 earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Kaik%C5%8Dura\" title=\"Kaikōura\">Kaikō<PERSON></a>, <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>, at a depth of 15 km (9 miles), resulting in the deaths of two people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2016_Kaik%C5%8Dura_earthquake\" title=\"2016 Kaikōura earthquake\">magnitude 7.8 earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Kaik%C5%8Dura\" title=\"Kaikōura\">Kaik<PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>, at a depth of 15 km (9 miles), resulting in the deaths of two people.", "links": [{"title": "2016 Kaikōura earthquake", "link": "https://wikipedia.org/wiki/2016_Kaik%C5%8Dura_earthquake"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kaik%C5%8Dura"}, {"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}]}, {"year": "2017", "text": "A gunman kills four people and injures 12 others during a shooting spree across Rancho Tehama, California. He had earlier murdered his wife in their home.", "html": "2017 - A gunman kills four people and injures 12 others during <a href=\"https://wikipedia.org/wiki/Rancho_Tehama_shootings\" title=\"Rancho Tehama shootings\">a shooting spree</a> across <a href=\"https://wikipedia.org/wiki/Rancho_Tehama,_California\" title=\"Rancho Tehama, California\">Rancho Tehama, California</a>. He had earlier murdered his wife in their home.", "no_year_html": "A gunman kills four people and injures 12 others during <a href=\"https://wikipedia.org/wiki/Rancho_Tehama_shootings\" title=\"Rancho Tehama shootings\">a shooting spree</a> across <a href=\"https://wikipedia.org/wiki/Rancho_Tehama,_California\" title=\"Rancho Tehama, California\">Rancho Tehama, California</a>. He had earlier murdered his wife in their home.", "links": [{"title": "Rancho Tehama shootings", "link": "https://wikipedia.org/wiki/Rancho_Tehama_shootings"}, {"title": "Rancho Tehama, California", "link": "https://wikipedia.org/wiki/Rancho_Tehama,_California"}]}, {"year": "2019", "text": "A mass shooting occurs at Saugus High School in Santa Clarita, California, resulting in three deaths, including that of the perpetrator, and three injuries.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/2019_Saugus_High_School_shooting\" class=\"mw-redirect\" title=\"2019 Saugus High School shooting\">A mass shooting</a> occurs at <a href=\"https://wikipedia.org/wiki/Saugus_High_School_(California)\" title=\"Saugus High School (California)\">Saugus High School</a> in <a href=\"https://wikipedia.org/wiki/Santa_Clarita,_California\" title=\"Santa Clarita, California\">Santa Clarita, California</a>, resulting in three deaths, including that of the perpetrator, and three injuries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2019_Saugus_High_School_shooting\" class=\"mw-redirect\" title=\"2019 Saugus High School shooting\">A mass shooting</a> occurs at <a href=\"https://wikipedia.org/wiki/Saugus_High_School_(California)\" title=\"Saugus High School (California)\">Saugus High School</a> in <a href=\"https://wikipedia.org/wiki/Santa_Clarita,_California\" title=\"Santa Clarita, California\">Santa Clarita, California</a>, resulting in three deaths, including that of the perpetrator, and three injuries.", "links": [{"title": "2019 Saugus High School shooting", "link": "https://wikipedia.org/wiki/2019_Saugus_High_School_shooting"}, {"title": "Saugus High School (California)", "link": "https://wikipedia.org/wiki/Saugus_High_School_(California)"}, {"title": "Santa Clarita, California", "link": "https://wikipedia.org/wiki/Santa_Clarita,_California"}]}], "Births": [{"year": "1449", "text": "<PERSON><PERSON><PERSON> of Poděbrady, daughter of King of Bohemia (d. 1510)", "html": "1449 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Pod%C4%9Bbrady\" title=\"<PERSON><PERSON><PERSON> of Poděbrady\"><PERSON><PERSON><PERSON> of Poděbrady</a>, daughter of King of Bohemia (d. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Pod%C4%9Bbrady\" title=\"<PERSON><PERSON><PERSON> of Poděbrady\"><PERSON><PERSON><PERSON> of Poděbrady</a>, daughter of King of Bohemia (d. 1510)", "links": [{"title": "Sidonie of Poděbrady", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_of_Pod%C4%9Bbrady"}]}, {"year": "1487", "text": "<PERSON> of Pernstein, Bohemian land-owner, Governor of Moravia and Count of Kladsko (d. 1548)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Pernstein\" class=\"mw-redirect\" title=\"<PERSON> of Pernstein\"><PERSON> of Pernstein</a>, Bohemian land-owner, Governor of Moravia and Count of Kladsko (d. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Pernstein\" class=\"mw-redirect\" title=\"<PERSON> of Pernstein\"><PERSON> of Pernstein</a>, Bohemian land-owner, Governor of Moravia and Count of Kladsko (d. 1548)", "links": [{"title": "<PERSON> of Pernstein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1501", "text": "<PERSON> of Oldenburg, Regent of East Frisia (d. 1575)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/Anna_of_Oldenburg\" title=\"<PERSON> of Oldenburg\"><PERSON> of Oldenburg</a>, Regent of East Frisia (d. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_of_Oldenburg\" title=\"<PERSON> of Oldenburg\"><PERSON> of Oldenburg</a>, Regent of East Frisia (d. 1575)", "links": [{"title": "Anna of Oldenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Oldenburg"}]}, {"year": "1531", "text": "<PERSON>, English torturer (d. 1604)", "html": "1531 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English torturer (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English torturer (d. 1604)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1601", "text": "<PERSON>, French priest and missionary (d. 1680)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and missionary (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and missionary (d. 1680)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1650", "text": "<PERSON> of England, Prince of Orange, King of England, Scotland and Ireland (d. 1702)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a>, Prince of Orange, King of England, Scotland and Ireland (d. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>, Prince of Orange, King of England, Scotland and Ireland (d. 1702)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1663", "text": "<PERSON>, German organist and composer (d. 1712)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON>, Austrian violinist, composer, and conductor (d. 1787)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leopold Mozart\"><PERSON></a>, Austrian violinist, composer, and conductor (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leopold Mozart\"><PERSON></a>, Austrian violinist, composer, and conductor (d. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1765", "text": "<PERSON>, American engineer, Early steamboat pioneer (d. 1815)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, Early <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamboat</a> pioneer (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, Early <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamboat</a> pioneer (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Steamboat", "link": "https://wikipedia.org/wiki/Steamboat"}]}, {"year": "1771", "text": "<PERSON>, French anatomist and physiologist (d. 1802)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French anatomist and physiologist (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French anatomist and physiologist (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, French physician, botanist, and physiologist (d. 1847)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, botanist, and physiologist (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, botanist, and physiologist (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, American farmer and politician (d. 1859)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, Austrian pianist and composer (d. 1837)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1779", "text": "<PERSON>, Danish poet and playwright (d. 1850)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ger\" title=\"<PERSON>\"><PERSON></a>, Danish poet and playwright (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ger\" title=\"<PERSON>\"><PERSON></a>, Danish poet and playwright (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O<PERSON>lenschl%C3%A4ger"}]}, {"year": "1797", "text": "<PERSON>, Scottish geologist (d. 1875)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, American author (d. 1879)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, German pianist and composer (d. 1847)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON><PERSON><PERSON>, Italian poet (d. 1878)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet (d. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON> of Savoy (d. 1836)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a> (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a> (d. 1836)", "links": [{"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Savoy"}]}, {"year": "1816", "text": "<PERSON>, English minister and educator (d. 1880)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and educator (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and educator (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, American general (d. 1864)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, English-Australian politician, 12th Premier of South Australia (d. 1920)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henry_Strangways"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1838", "text": "<PERSON>, Croatian author, poet, and critic (d. 1881)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/August_%C5%A0enoa\" title=\"August <PERSON>\">August <PERSON></a>, Croatian author, poet, and critic (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_%C5%A0enoa\" title=\"August Š<PERSON>\">August <PERSON></a>, Croatian author, poet, and critic (d. 1881)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_%C5%A0enoa"}]}, {"year": "1840", "text": "<PERSON>, French painter (d. 1926)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American activist (d. 1945)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American historian and author (d. 1932)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Belgian-American chemist and engineer (d. 1944)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American chemist and engineer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American chemist and engineer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Baekeland"}]}, {"year": "1869", "text": "<PERSON>, Irish physician, founded the St. John Ambulance Brigade of Ireland (d. 1944)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physician, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>._John_Ambulance_Brigade_of_Ireland\" class=\"mw-redirect\" title=\"St. John Ambulance Brigade of Ireland\">St. John Ambulance Brigade of Ireland</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physician, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON>_Ambulance_Brigade_of_Ireland\" class=\"mw-redirect\" title=\"St. John Ambulance Brigade of Ireland\">St. John Ambulance Brigade of Ireland</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "St. John Ambulance Brigade of Ireland", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_John_Ambulance_Brigade_of_Ireland"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Bengali aristocrat and philanthropist (d. 1936)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bengali aristocrat and philanthropist (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bengali aristocrat and philanthropist (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON>, Filipino general and politician (d. 1899)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino general and politician (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino general and politician (d. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Swiss author and activist (d. 1944)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and activist (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and activist (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Australian tennis player (d. 1968)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, French painter and art collector (d. 1966)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and art collector (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and art collector (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Ukrainian-Polish poet and academic (d. 1957)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Leopold_Staff\" title=\"Leopold Staff\">Leopold Staff</a>, Ukrainian-Polish poet and academic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopold_Staff\" title=\"Leopold Staff\">Leopold Staff</a>, Ukrainian-Polish poet and academic (d. 1957)", "links": [{"title": "Leopold Staff", "link": "https://wikipedia.org/wiki/Leopold_Staff"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Estonian lawyer and politician, 3rd Prime Minister of Estonia (d. 1942)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 1st Prime Minister of India (d. 1964)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1891", "text": "<PERSON>, Canadian physician and academic, Nobel Prize laureate (d. 1941)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1893", "text": "<PERSON><PERSON>, American lawyer and trade commissioner (d. 1975)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and trade commissioner (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and trade commissioner (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American physician and psychiatrist (d. 1972)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, American physician and psychiatrist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a>, American physician and psychiatrist (d. 1972)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_II"}]}, {"year": "1897", "text": "<PERSON>, American painter and academic (d. 1946)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Romanian-French philosopher, poet, and critic (d. 1944)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French philosopher, poet, and critic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French philosopher, poet, and critic (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American composer, conductor, and educator (d. 1990)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and educator (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and educator (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American lawyer and judge (d. 1970)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English-Australian cricketer (d. 1995)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American actor, singer, director, and producer (d. 1963)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, director, and producer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, director, and producer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American singer and guitarist (d. 1964)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American actress and dancer (d. 1985)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American religious leader, 14th President of The Church of Jesus Christ of Latter-day Saints (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 14th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 14th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Swedish author and screenwriter (d. 2002)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author and screenwriter (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American author, illustrator, and sculptor (d. 2003)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, illustrator, and sculptor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, illustrator, and sculptor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American captain, lawyer, and politician (d. 1957)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actress and singer (d. 2001)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English author (d. 1996)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American philanthropist (d. 1979)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Chinese-American engineer, designed the Guandu Bridge (d. 2003)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Tung-Ye<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American engineer, designed the <a href=\"https://wikipedia.org/wiki/Guandu_Bridge\" title=\"Guandu Bridge\">Guandu Bridge</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ng-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American engineer, designed the <a href=\"https://wikipedia.org/wiki/Guandu_Bridge\" title=\"Guandu Bridge\">Guandu Bridge</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}, {"title": "Guandu Bridge", "link": "https://wikipedia.org/wiki/Guandu_Bridge"}]}, {"year": "1914", "text": "<PERSON>, American Western singer (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_singer)\" title=\"<PERSON> (country singer)\"><PERSON></a>, American Western singer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(country_singer)\" title=\"<PERSON> (country singer)\"><PERSON></a>, American Western singer (d. 1994)", "links": [{"title": "<PERSON> (country singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(country_singer)"}]}, {"year": "1915", "text": "<PERSON>, American figure skater and coach (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and coach (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mabel Fairbanks\"><PERSON></a>, American figure skater and coach (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American singer and actress (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Greek-French mathematician and academic (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, Greek-French mathematician and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, Greek-French mathematician and academic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roger_Ap%C3%A9ry"}]}, {"year": "1916", "text": "<PERSON>, American screenwriter and producer (d. 2011)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, South Korean general and politician, 3rd President of South Korea (d. 1979)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean general and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean general and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1918", "text": "<PERSON>, Australian tennis player (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American singer (d. 1985)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, German soprano and actress (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soprano and actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soprano and actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, the first First Nations woman to join the Canadian Armed Forces (d. 2011)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/First_Nations_in_Canada\" title=\"First Nations in Canada\">First Nations</a> woman to join the <a href=\"https://wikipedia.org/wiki/Canadian_Armed_Forces\" title=\"Canadian Armed Forces\">Canadian Armed Forces</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/First_Nations_in_Canada\" title=\"First Nations in Canada\">First Nations</a> woman to join the <a href=\"https://wikipedia.org/wiki/Canadian_Armed_Forces\" title=\"Canadian Armed Forces\">Canadian Armed Forces</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON>"}, {"title": "First Nations in Canada", "link": "https://wikipedia.org/wiki/First_Nations_in_Canada"}, {"title": "Canadian Armed Forces", "link": "https://wikipedia.org/wiki/Canadian_Armed_Forces"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Estonian historian and academic (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian historian and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian historian and academic (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor and director (d. 1997)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Egyptian politician and diplomat, 6th Secretary General of the United Nations (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian politician and diplomat, 6th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_the_United_Nations\" class=\"mw-redirect\" title=\"Secretary General of the United Nations\">Secretary General of the United Nations</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian politician and diplomat, 6th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_the_United_Nations\" class=\"mw-redirect\" title=\"Secretary General of the United Nations\">Secretary General of the United Nations</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Boutros-Ghali"}, {"title": "Secretary General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary_General_of_the_United_Nations"}]}, {"year": "1922", "text": "<PERSON>, American actress and singer (d. 1973)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Veronica_Lake\" title=\"Veronica Lake\"><PERSON></a>, American actress and singer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Veronica_Lake\" title=\"Veronica Lake\"><PERSON></a>, American actress and singer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Veronica_Lake"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Ukrainian-Russian violinist and educator (d. 1982)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian violinist and educator (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian violinist and educator (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American physicist and academic (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Stirling_Colgate\" title=\"Stirling Colgate\"><PERSON></a>, American physicist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stirling_Colgate\" title=\"Stirling Colgate\"><PERSON></a>, American physicist and academic (d. 2013)", "links": [{"title": "Stirling Colgate", "link": "https://wikipedia.org/wiki/Stirling_Colgate"}]}, {"year": "1925", "text": "<PERSON>, English archaeologist and author (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, English businessman, founded Barratt Developments (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Barratt\" title=\"<PERSON><PERSON> Barratt\"><PERSON><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Barratt_Developments\" class=\"mw-redirect\" title=\"Barratt Developments\">Barratt Developments</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Barratt\" title=\"<PERSON><PERSON> Barratt\"><PERSON><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Barratt_Developments\" class=\"mw-redirect\" title=\"Barratt Developments\">Barratt Developments</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>att"}, {"title": "Barratt Developments", "link": "https://wikipedia.org/wiki/Barratt_Developments"}]}, {"year": "1927", "text": "<PERSON>, Australian horse trainer (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian horse trainer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian horse trainer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor and screenwriter (d. 1996)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish guitarist and composer (d. 1997)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Narciso_<PERSON><PERSON>\" title=\"Narc<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish guitarist and composer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narciso_<PERSON><PERSON>\" title=\"Narcis<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish guitarist and composer (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narcis<PERSON>_<PERSON>es"}]}, {"year": "1928", "text": "<PERSON>, American actress", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English wrestler (d. 1997)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American baseball player and sportscaster (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English pianist and academic (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Canadian actress (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English actor (d. 1992)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American engineer and astronaut (d. 1967)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American engineer and astronaut (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American engineer and astronaut (d. 1967)", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)"}]}, {"year": "1932", "text": "<PERSON><PERSON>, German astrologer and photographer (d. 2011)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German astrologer and photographer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German astrologer and photographer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American pilot, engineer, and astronaut", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Scottish-English footballer and manager (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Jr., American pianist and educator (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American pianist and educator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American pianist and educator (d. 2020)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "1934", "text": "<PERSON>, Irish lawyer, judge, and politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer, judge, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer, judge, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON> of Jordan (d. 1999)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> of Jordan</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> of Jordan</a> (d. 1999)", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/Hussein_of_Jordan"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Greek songwriter and journalist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek songwriter and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek songwriter and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer and harmonica player (d. 2007)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American singer and harmonica player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American singer and harmonica player (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English singer and actor (d. 2006)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American R&B singer (d. 1990)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cornell_Gunter"}]}, {"year": "1937", "text": "<PERSON>, English director and producer (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Oliver\"><PERSON></a>, Canadian ice hockey player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Oliver\"><PERSON></a>, Canadian ice hockey player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American keyboard player and composer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American painter and academic (d. 2011)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cleary\" title=\"Manon Cleary\"><PERSON><PERSON></a>, American painter and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON>_Cleary\" title=\"Manon Cleary\"><PERSON><PERSON></a>, American painter and academic (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manon_Cleary"}]}, {"year": "1942", "text": "<PERSON>, Russian cellist and educator", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian cellist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian cellist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American programmer and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American programmer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American programmer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English author and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American bodybuilder and football player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English academic and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Indian actor and director (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and director (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and director (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American race car driver", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Indian director and screenwriter (d. 1998)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Belgian businessman and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2telet\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2telet\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roland_Duch%C3%A2telet"}]}, {"year": "1947", "text": "<PERSON><PERSON> <PERSON><PERSON>, American political satirist and journalist (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/P._J._O%27Rourke\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American political satirist and journalist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P._J._O%27Rourke\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American political satirist and journalist (d. 2022)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P._J._O%27Rourke"}]}, {"year": "1947", "text": "<PERSON>, Australian surfer and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American accordion player (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Buckwheat_Zydeco\" title=\"Buckwheat Zydeco\">Buckw<PERSON> Zydeco</a>, American accordion player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buckwheat_Zydeco\" title=\"Buckwheat Zydeco\">Buckw<PERSON> Zydec<PERSON></a>, American accordion player (d. 2016)", "links": [{"title": "Buckwheat Zydeco", "link": "https://wikipedia.org/wiki/Buckwheat_Zydeco"}]}, {"year": "1948", "text": "<PERSON>, King of the United Kingdom", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles III\"><PERSON></a>, King of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles III\"><PERSON></a>, King of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}]}, {"year": "1948", "text": "<PERSON>, English journalist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English author and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor and producer (d. 2009)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Argentinian pianist, composer, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_di_Blasio\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian pianist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_di_Blasio\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian pianist, composer, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BA<PERSON>_<PERSON>_B<PERSON>io"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Italian painter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Japanese actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (American musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>zek_<PERSON>ichy\" title=\"Leszek Cichy\"><PERSON><PERSON><PERSON></a>, Polish mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>zek_<PERSON>ichy\" title=\"Leszek Cichy\"><PERSON><PERSON><PERSON></a>, Polish mountaineer", "links": [{"title": "Leszek Cichy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>k_<PERSON>y"}]}, {"year": "1951", "text": "<PERSON>, Chinese actor, director, producer, and cinematographer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor, director, producer, and cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor, director, producer, and cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American guitarist and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Greek singer, composer and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer, composer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer, composer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American voice actress and singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English children's author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English children's author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English children's author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Moroccan-French lawyer and politician, 167th Prime Minister of France", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan-French lawyer and politician, 167th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan-French lawyer and politician, 167th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France"}]}, {"year": "1954", "text": "<PERSON>, Puerto Rican baseball player (d. 2023)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez"}]}, {"year": "1954", "text": "<PERSON>, French cyclist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, American political scientist, academic, and politician, 66th United States Secretary of State", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American political scientist, academic, and politician, 66th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American political scientist, academic, and politician, 66th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Chilean race car driver", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eliseo_Salazar"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Greek-American pianist, composer, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American pianist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American pianist, composer, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ni"}]}, {"year": "1955", "text": "<PERSON>, English bishop", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American basketball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American philosopher, author, and scholar", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philosopher, author, and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philosopher, author, and scholar", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>tte_Ba<PERSON>h"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Israeli footballer and manager (d. 2010)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avi_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Dutch investigative journalist and crime reporter (d. 2021)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch investigative journalist and crime reporter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch investigative journalist and crime reporter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American government official", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American government official", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American government official", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American screenwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "Laura <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Giacomo"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Canadian-American actor and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actor and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, French journalist, radio and television presenter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Bern\" title=\"Stéphane Bern\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist, radio and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Bern\" title=\"Stéphane Bern\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist, radio and television presenter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_Bern"}]}, {"year": "1964", "text": "<PERSON>, American journalist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American hip-hop artist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hip-hop artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hip-hop artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor and comedian", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English conductor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German cyclist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Dana_Stub<PERSON>field\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Stubblefield\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dana_Stubblefield"}]}, {"year": "1971", "text": "<PERSON>, Australian cricketer and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Indian chef and author", "html": "1971 - <a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian chef and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>na"}]}, {"year": "1971", "text": "<PERSON>, Australian-Italian actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Italian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Italian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American wrestler, trainer, and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, trainer, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, trainer, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American model and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Papua New Guinean-Australian politician, 44th Premier of Tasmania", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean-Australian politician, 44th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean-Australian politician, 44th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Polish singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Edyta_G%C3%B3rniak\" title=\"Ed<PERSON><PERSON> Górniak\"><PERSON><PERSON><PERSON></a>, Polish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edyta_G%C3%B3rniak\" title=\"<PERSON><PERSON><PERSON> Górniak\"><PERSON><PERSON><PERSON></a>, Polish singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edyta_G%C3%B3rniak"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Scottish bass player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian footballer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)"}]}, {"year": "1972", "text": "<PERSON>, American football player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1972)\" title=\"<PERSON> (American football, born 1972)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1972)\" title=\"<PERSON> (American football, born 1972)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (American football, born 1972)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1972)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Polish footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%BBuraw\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%BBuraw\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dariusz_%C5%BBuraw"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Lawyer <PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yer_<PERSON><PERSON>\" title=\"Lawyer <PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "Lawyer <PERSON>", "link": "https://wikipedia.org/wiki/Lawyer_<PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Panamanian baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Panamanian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Panamanian baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Irish singer and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American singer-songwriter and chef", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and chef", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Belgian politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k<PERSON>\" title=\"So<PERSON> Merckx\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k<PERSON>\" title=\"So<PERSON> Merck<PERSON>\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kx"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/David_<PERSON>\" title=\"David <PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/David_<PERSON>\" title=\"David Moscow\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/David_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer and bass player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American drummer, songwriter, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Russian-American businessman and critic", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American businessman and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American businessman and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American rapper and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/O<PERSON>_<PERSON>ce\" title=\"Obie Trice\"><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ce\"><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>ce"}]}, {"year": "1978", "text": "<PERSON>, American ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, New Zealand actress and singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, French model and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9ac\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9ac\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Delphine_Chan%C3%A9ac"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, New Zealand rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, German-Austrian actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_H%C3%B6rbiger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6rb<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mavie_H%C3%B6rbiger"}]}, {"year": "1979", "text": "<PERSON>, Ukrainian-French model and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-French model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-French model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Indian singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>le"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Lesothan footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lesothan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lesothan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bo"}]}, {"year": "1979", "text": "<PERSON>, Mexican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Miguel_<PERSON>\" title=\"Miguel Sabah\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miguel_Sabah\" title=\"Miguel Sabah\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_Sabah"}]}, {"year": "1980", "text": "<PERSON>, American actor and businessman", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian model and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ferrier\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tom Ferrier\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Badazz\" title=\"<PERSON><PERSON><PERSON> Badazz\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>z\" title=\"<PERSON><PERSON><PERSON> Badazz\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Boosie_Badazz"}]}, {"year": "1982", "text": "<PERSON><PERSON> bin <PERSON>, Crown Prince of Dubai", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON> bin <PERSON></a>, Crown Prince <PERSON> Dubai", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON> bin <PERSON></a>, Crown Prince <PERSON> Dubai", "links": [{"title": "<PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, English cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Naq<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naq<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naq<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wolfe"}]}, {"year": "1983", "text": "<PERSON>, American sprinter and hurdler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Serbian singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Mari<PERSON>_%C5%A0erifovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari<PERSON>_%C5%A0erifovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marija_%C5%A0erifovi%C4%87"}]}, {"year": "1985", "text": "<PERSON>, Belgian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Mexican-American wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Mexican-American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Mexican-American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1986", "text": "<PERSON>, American-Serbian basketball player and coach", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Serbian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Serbian basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Chiyotairy%C5%AB_Hidemasa\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chiyotairy%C5%AB_Hidemasa\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>de<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chiyotairy%C5%AB_Hidemasa"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1989", "text": "<PERSON>, Romanian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99"}]}, {"year": "1989", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Swiss footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Roman_B%C3%BCrki\" title=\"Roman Bürki\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_B%C3%BCrki\" title=\"Roman Bürki\"><PERSON></a>, Swiss footballer", "links": [{"title": "Roman Bürki", "link": "https://wikipedia.org/wiki/Roman_B%C3%BCrki"}]}, {"year": "1990", "text": "<PERSON>, Australian actress and singer (d. 2008)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and singer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and singer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian cyclist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Taylor_<PERSON>\" title=\"Taylor Hall\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taylor_Hall\" title=\"Taylor Hall\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Taylor_Hall"}]}, {"year": "1991", "text": "<PERSON>, American actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Burmese activist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Puerto Rican baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Sh%C5%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sh%C5%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%AB<PERSON><PERSON>_<PERSON>mura"}]}, {"year": "1993", "text": "<PERSON>, French footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Croatian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Borna_%C4%86ori%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Borna_%C4%86ori%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Borna_%C4%86ori%C4%87"}]}, {"year": "1996", "text": "<PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Moroccan footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, French footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sofia_Kenin"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "565", "text": "<PERSON><PERSON>, Byzantine emperor (b. 482)", "html": "565 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Justinian I\"><PERSON><PERSON></a>, Byzantine emperor (b. 482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Justinian I\"><PERSON><PERSON></a>, Byzantine emperor (b. 482)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "669", "text": "<PERSON><PERSON>, Japanese politician (b. 614)", "html": "669 - <a href=\"https://wikipedia.org/wiki/Fuji<PERSON>_no_Kamatari\" title=\"Fujiwara no Kamatari\"><PERSON><PERSON> <PERSON></a>, Japanese politician (b. 614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Kamatari\" title=\"Fujiwara no Kamatari\"><PERSON><PERSON> no <PERSON></a>, Japanese politician (b. 614)", "links": [{"title": "<PERSON><PERSON> no <PERSON>matari", "link": "https://wikipedia.org/wiki/Fujiwara_no_Kamatari"}]}, {"year": "940", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vizier", "html": "940 - <a href=\"https://wikipedia.org/wiki/Abu%27l-Fadl_al-Bal%27ami\" title=\"<PERSON>'l-F<PERSON>l al-Bal'ami\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al-Bal'ami</a>, <PERSON><PERSON><PERSON> vizier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abu%27l-Fadl_al-Bal%27ami\" title=\"<PERSON>'l-Fadl al-Bal'ami\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al-Bal'ami</a>, <PERSON><PERSON><PERSON> vizier", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'ami", "link": "https://wikipedia.org/wiki/Abu%27l-F<PERSON><PERSON>_al-Bal%27ami"}]}, {"year": "976", "text": "<PERSON><PERSON>, Chinese emperor (b. 927)", "html": "976 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\"><PERSON><PERSON></a>, Chinese emperor (b. 927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\"><PERSON><PERSON></a>, Chinese emperor (b. 927)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1060", "text": "<PERSON>, count of Anjou", "html": "1060 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Anjou\" title=\"<PERSON>, Count of Anjou\"><PERSON></a>, count of Anjou", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Anjou\" title=\"<PERSON>, Count of Anjou\"><PERSON> II</a>, count of Anjou", "links": [{"title": "<PERSON>, Count of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Anjou"}]}, {"year": "1189", "text": "<PERSON>, 3rd Earl of Essex", "html": "1189 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Earl_of_Essex\" title=\"<PERSON>, 3rd Earl of Essex\"><PERSON>, 3rd Earl <PERSON> Essex</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Earl_of_Essex\" title=\"<PERSON>, 3rd Earl <PERSON> Essex\"><PERSON>, 3rd Earl <PERSON> Essex</a>", "links": [{"title": "<PERSON>, 3rd Earl of Essex", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Earl_of_Essex"}]}, {"year": "1226", "text": "<PERSON> of Isenberg, German politician (b. 1193)", "html": "1226 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Isenberg\" title=\"<PERSON> of Isenberg\"><PERSON> of Isenberg</a>, German politician (b. 1193)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Isenberg\" title=\"<PERSON> of Isenberg\"><PERSON> of Isenberg</a>, German politician (b. 1193)", "links": [{"title": "<PERSON> of Isenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1263", "text": "<PERSON>, Russian saint (b. 1220)", "html": "1263 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian saint (b. 1220)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian saint (b. 1220)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1346", "text": "<PERSON><PERSON><PERSON>, Lord of Ravenna", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_da_Polenta\" title=\"<PERSON>stasio I da Polenta\"><PERSON><PERSON><PERSON> <PERSON> da Polenta</a>, Lord of Ravenna", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_da_Polenta\" title=\"<PERSON><PERSON>sio I da Polenta\"><PERSON><PERSON><PERSON> <PERSON> da Polenta</a>, Lord of Ravenna", "links": [{"title": "Ostasio I da Polenta", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1359", "text": "<PERSON>, Greek archbishop and saint (b. 1296)", "html": "1359 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek archbishop and saint (b. 1296)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek archbishop and saint (b. 1296)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1391", "text": "<PERSON>, Croatian missionary and saint (b. 1340)", "html": "1391 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Croatian missionary and saint (b. 1340)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Croatian missionary and saint (b. 1340)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_Taveli%C4%87"}]}, {"year": "1442", "text": "<PERSON><PERSON><PERSON> of Aragon, French noblewoman (b. 1384)", "html": "1442 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon\" title=\"<PERSON><PERSON><PERSON> of Aragon\"><PERSON><PERSON><PERSON> of Aragon</a>, French noblewoman (b. 1384)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon\" title=\"<PERSON><PERSON><PERSON> of Aragon\"><PERSON><PERSON><PERSON> of Aragon</a>, French noblewoman (b. 1384)", "links": [{"title": "<PERSON><PERSON><PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon"}]}, {"year": "1522", "text": "<PERSON> of France, duchess of Bourbon (b. 1461)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/Anne_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, duchess of Bourbon (b. 1461)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, duchess of Bourbon (b. 1461)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_of_France"}]}, {"year": "1539", "text": "<PERSON>, English monk and abbot", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_alias_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> alias Faringdon\"><PERSON></a>, English monk and abbot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_alias_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> alias Faringdon\"><PERSON></a>, English monk and abbot", "links": [{"title": "<PERSON> alias <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_alias_<PERSON>"}]}, {"year": "1556", "text": "<PERSON>, Italian archbishop and poet (b. 1504)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Casa\"><PERSON></a>, Italian archbishop and poet (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Casa\"><PERSON></a>, Italian archbishop and poet (b. 1504)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, English philosopher and academic (b. 1576)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1576)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON>, English mistress of <PERSON> of England (b. 1650)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mistress of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mistress of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1691", "text": "<PERSON><PERSON>, Japanese painter (b. 1617)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese painter (b. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese painter (b. 1617)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON><PERSON><PERSON>, German mathematician and philosopher (b. 1646)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (b. 1646)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1734", "text": "<PERSON>, duchess of Portsmouth (b. 1649)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_Duchess_of_Portsmouth\" title=\"<PERSON>, Duchess of Portsmouth\"><PERSON></a>, duchess of Portsmouth (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_Duchess_of_Portsmouth\" title=\"<PERSON>, Duchess of Portsmouth\"><PERSON></a>, duchess of Portsmouth (b. 1649)", "links": [{"title": "<PERSON>, Duchess of Portsmouth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON><PERSON>,_Duchess_of_Portsmouth"}]}, {"year": "1739", "text": "<PERSON>, Spanish Roman Catholic archbishop of Santo Domingo and Bogotá (b. 1683)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>v%C3%ADs\" title=\"<PERSON>\"><PERSON></a>, Spanish Roman Catholic archbishop of Santo Domingo and Bogotá (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADs\" title=\"<PERSON>\"><PERSON></a>, Spanish Roman Catholic archbishop of Santo Domingo and Bogotá (b. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>v%C3%ADs"}]}, {"year": "1746", "text": "<PERSON>, German botanist, zoologist, physician, and explorer (b. 1709)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist, zoologist, physician, and explorer (b. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist, zoologist, physician, and explorer (b. 1709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 3rd <PERSON><PERSON><PERSON><PERSON> (b. 1713)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/Mar<PERSON><PERSON>_Gondazaemon\" title=\"<PERSON>uy<PERSON> Gondazaemon\"><PERSON><PERSON><PERSON> Gondazaemon</a>, Japanese sumo wrestler, the 3rd <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Gondazaemon\" title=\"<PERSON><PERSON><PERSON> Gondazaemon\"><PERSON><PERSON><PERSON> Gondazaemon</a>, Japanese sumo wrestler, the 3rd <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1713)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Go<PERSON>emon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON><PERSON>, Colombian seamstress and spy (b. 1795)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Policarpa_Salavarrieta\" title=\"Policarpa Salavarrieta\">Policarpa Salavarrieta</a>, Colombian seamstress and spy (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Policarpa_Salavarrieta\" title=\"Policarpa Salavarrieta\">Policarpa Salavarrieta</a>, Colombian seamstress and spy (b. 1795)", "links": [{"title": "Policarpa <PERSON>", "link": "https://wikipedia.org/wiki/Policarpa_Salavarrieta"}]}, {"year": "1825", "text": "<PERSON>, German journalist and author (b. 1763)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, French pharmacist and chemist (b. 1763)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pharmacist and chemist (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pharmacist and chemist (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, German philosopher, author, and academic (b. 1770)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German philosopher, author, and academic (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German philosopher, author, and academic (b. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON>, Austrian-French composer and piano builder (b. 1757)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-French composer and piano builder (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-French composer and piano builder (b. 1757)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON> of Carrollton, American farmer and politician (b. 1737)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Carrollton\" title=\"<PERSON> of Carrollton\"><PERSON> of Carrollton</a>, American farmer and politician (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Carrollton\" title=\"<PERSON> of Carrollton\"><PERSON> of Carrollton</a>, American farmer and politician (b. 1737)", "links": [{"title": "<PERSON> of Carrollton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Carrollton"}]}, {"year": "1844", "text": "<PERSON>, Scottish physician and philosopher (b. 1780)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Scottish physician and philosopher (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Scottish physician and philosopher (b. 1780)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_(physician)"}]}, {"year": "1844", "text": "<PERSON>, French author and activist (b. 1803)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and activist (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tristan\"><PERSON></a>, French author and activist (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Flora_Tristan"}]}, {"year": "1864", "text": "<PERSON>, German tailor and murderer (b. 1840)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German tailor and murderer (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German tailor and murderer (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON> of Portugal (b. 1802)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1802)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Portugal"}]}, {"year": "1907", "text": "<PERSON>, Australian lawyer, judge, and politician (b. 1848)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer, judge, and politician (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer, judge, and politician (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>u Emperor of China (b. 1871)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Guangxu_Emperor\" title=\"Guangxu Emperor\">Guangxu Emperor</a> of China (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guangxu_Emperor\" title=\"Guangxu Emperor\">Guangxu Emperor</a> of China (b. 1871)", "links": [{"title": "Guangxu Emperor", "link": "https://wikipedia.org/wiki/Guangxu_Emperor"}]}, {"year": "1910", "text": "<PERSON>, American artist (b. 1835)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and journalist (b. 1861)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Vengay<PERSON>_<PERSON>_<PERSON>yana<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Nayana<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and journalist (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Veng<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Nayana<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and journalist (b. 1861)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veng<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American educator, essayist and historian (b. 1856)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, essayist and historian (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, essayist and historian (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Jr., American journalist and politician (b. 1862)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and politician (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and politician (b. 1862)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1916", "text": "<PERSON><PERSON>, British short story writer (b. 1870)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British short story writer (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British short story writer (b. 1870)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ki"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Finnish politician (b. 1874)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Princess <PERSON> of Brazil (b. 1846)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Imperial_of_Brazil\" title=\"<PERSON>, Princess Imperial of Brazil\"><PERSON>, Princess Imperial of Brazil</a> (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Imperial_of_Brazil\" title=\"<PERSON>, Princess Imperial of Brazil\"><PERSON>, Princess Imperial of Brazil</a> (b. 1846)", "links": [{"title": "<PERSON>, Princess <PERSON> of Brazil", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_Imperial_of_Brazil"}]}, {"year": "1930", "text": "<PERSON>, Australian rugby league player (b. 1883)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English organist and composer (b. 1884)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American baseball player and manager (b. 1866)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player and manager (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player and manager (b. 1866)", "links": [{"title": "<PERSON> (catcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(catcher)"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Australian cattle dog, second-oldest recorded dog (b. 1910)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(long-lived_dog)\" title=\"<PERSON><PERSON> (long-lived dog)\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Australian_cattle_dog\" class=\"mw-redirect\" title=\"Australian cattle dog\">Australian cattle dog</a>, <a href=\"https://wikipedia.org/wiki/List_of_longest_living_dogs\" class=\"mw-redirect\" title=\"List of longest living dogs\">second-oldest recorded dog</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(long-lived_dog)\" title=\"<PERSON><PERSON> (long-lived dog)\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Australian_cattle_dog\" class=\"mw-redirect\" title=\"Australian cattle dog\">Australian cattle dog</a>, <a href=\"https://wikipedia.org/wiki/List_of_longest_living_dogs\" class=\"mw-redirect\" title=\"List of longest living dogs\">second-oldest recorded dog</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON> (long-lived dog)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(long-lived_dog)"}, {"title": "Australian cattle dog", "link": "https://wikipedia.org/wiki/Australian_cattle_dog"}, {"title": "List of longest living dogs", "link": "https://wikipedia.org/wiki/List_of_longest_living_dogs"}]}, {"year": "1944", "text": "<PERSON>, Hungarian violinist and educator (b. 1873)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist and educator (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist and educator (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>-<PERSON>, English air marshal (b. 1892)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON>\"><PERSON><PERSON></a>, English air marshal (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON>Mallory\"><PERSON><PERSON></a>, English air marshal (b. 1892)", "links": [{"title": "Trafford Leigh-Mallory", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Spanish pianist and composer (b. 1876)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist and composer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist and composer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian fiddler and composer (b. 1873)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fiddler)\" title=\"<PERSON> (fiddler)\"><PERSON></a>, Canadian fiddler and composer (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(fiddler)\" title=\"<PERSON> (fiddler)\"><PERSON></a>, Canadian fiddler and composer (b. 1873)", "links": [{"title": "<PERSON> (fiddler)", "link": "https://wikipedia.org/wiki/<PERSON>_(fiddler)"}]}, {"year": "1966", "text": "<PERSON>, English captain, author, and politician (b. 1921)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English captain, author, and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English captain, author, and politician (b. 1921)", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_politician)"}]}, {"year": "1972", "text": "<PERSON>, Jr., American lawyer and politician (b. 1900)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician (b. 1900)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1974", "text": "<PERSON>, American football player, actor, and singer (b. 1904)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and singer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and singer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian monk and guru, founded the International Society for Krishna Consciousness (b. 1896)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/A._C._<PERSON><PERSON><PERSON><PERSON><PERSON>_Swami_<PERSON>\" title=\"A. C. Bhakti<PERSON><PERSON>a Swami <PERSON>\"><PERSON>. C<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Indian monk and guru, founded the <a href=\"https://wikipedia.org/wiki/International_Society_for_Krishna_Consciousness\" title=\"International Society for Krishna Consciousness\">International Society for Krishna Consciousness</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._C._<PERSON><PERSON><PERSON><PERSON><PERSON>_Swami_<PERSON>\" title=\"A. C. Bhaktivedanta Swami <PERSON>\"><PERSON>. C. <PERSON><PERSON><PERSON><PERSON><PERSON> Swami <PERSON></a>, Indian monk and guru, founded the <a href=\"https://wikipedia.org/wiki/International_Society_for_Krishna_Consciousness\" title=\"International Society for Krishna Consciousness\">International Society for Krishna Consciousness</a> (b. 1896)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>a"}, {"title": "International Society for Krishna Consciousness", "link": "https://wikipedia.org/wiki/International_Society_for_Krishna_Consciousness"}]}, {"year": "1981", "text": "<PERSON>, Irish footballer and politician (b. 1941)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Irish_politician)\" title=\"<PERSON> (Northern Irish politician)\"><PERSON></a>, Irish footballer and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Irish_politician)\" title=\"<PERSON> (Northern Irish politician)\"><PERSON></a>, Irish footballer and politician (b. 1941)", "links": [{"title": "<PERSON> (Northern Irish politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Irish_politician)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Filipino lawyer and politician, 10th Mayor of Zamboanga City (b. 1916)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Mayors_of_Zamboanga_City\" class=\"mw-redirect\" title=\"List of Mayors of Zamboanga City\">Mayor of Zamboanga City</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Mayors_of_Zamboanga_City\" class=\"mw-redirect\" title=\"List of Mayors of Zamboanga City\">Mayor of Zamboanga City</a> (b. 1916)", "links": [{"title": "Ce<PERSON>", "link": "https://wikipedia.org/wiki/Cesar_Climaco"}, {"title": "List of Mayors of Zamboanga City", "link": "https://wikipedia.org/wiki/List_of_Mayors_of_Zamboanga_City"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Greek actor and cinematographer (b. 1912)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Nik<PERSON>_Platis\" title=\"Nikitas Platis\"><PERSON><PERSON></a>, Greek actor and cinematographer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nik<PERSON>_Platis\" title=\"Nikitas Platis\"><PERSON><PERSON></a>, Greek actor and cinematographer (b. 1912)", "links": [{"title": "Nikitas Platis", "link": "https://wikipedia.org/wiki/Nikitas_Platis"}]}, {"year": "1988", "text": "<PERSON><PERSON> <PERSON>, American general (b. 1903)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Haywood_S._Hansell\" title=\"Haywood S. Hansell\"><PERSON><PERSON> <PERSON></a>, American general (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haywood_S._Hansell\" title=\"Haywood S. Hansell\"><PERSON><PERSON> <PERSON></a>, American general (b. 1903)", "links": [{"title": "Haywood S. <PERSON>", "link": "https://wikipedia.org/wiki/Haywood_S._Hans<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Welsh footballer, manager, assistant manager, coach, and scout (b. 1910)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer, manager, assistant manager, coach, and scout (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer, manager, assistant manager, coach, and scout (b. 1910)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1990", "text": "<PERSON>, American composer and conductor (b. 1919)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kaplan"}]}, {"year": "1991", "text": "<PERSON>, English-American director, producer, and screenwriter (b. 1928)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director, producer, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director, producer, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Austrian footballer and coach (b. 1925)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and coach (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1953)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American author and screenwriter (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American cardinal (b. 1928)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American soldier and politician (b. 1929)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American jockey and sportscaster (b. 1916)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and sportscaster (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and sportscaster (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian educator and politician, 35th Secretary of State for Canada (b. 1905)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 35th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 35th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "2000", "text": "<PERSON>, American journalist (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English actress (b. 1968)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Argentinian footballer and manager (b. 1922)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Greek-American soprano and educator (b. 1909)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American soprano and educator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American soprano and educator (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor, singer, dancer, and choreographer (b. 1962)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, dancer, and choreographer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, dancer, and choreographer (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, French-American composer and conductor (b. 1939)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American composer and conductor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American composer and conductor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American admiral (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American author and academic (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hunter\"><PERSON><PERSON></a>, American author and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hunter\"><PERSON><PERSON></a>, American author and academic (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American psychologist, teacher, and author (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, teacher, and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, teacher, and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American runner (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Italian-Turkish singer and actress (b. 1936)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Esin_Af%C5%9Far\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-Turkish singer and actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON>_Af%C5%9Far\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-Turkish singer and actress (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esin_Af%C5%9Far"}]}, {"year": "2011", "text": "<PERSON>, English-Chinese businessman (b. 1970)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Chinese businessman (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Chinese businessman (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Scottish singer-songwriter and guitarist (b. 1950)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON> Nascimento, Brazilian footballer (b. 1974)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_do_Nascimento\" class=\"mw-redirect\" title=\"Alexandro Alves do Nascimento\"><PERSON><PERSON><PERSON> do Nascimento</a>, Brazilian footballer (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_do_Nascimento\" class=\"mw-redirect\" title=\"Alexandro Alves do Nascimento\"><PERSON><PERSON><PERSON> do Nascimento</a>, Brazilian footballer (b. 1974)", "links": [{"title": "Alexandro <PERSON> do Nascimento", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_do_Nascimento"}]}, {"year": "2012", "text": "<PERSON>, Australian rugby player and manager (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby player and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby player and manager (b. 1930)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2012", "text": "<PERSON>, Irish fiddler (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish fiddler (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish fiddler (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Palestinian commander (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian commander (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian commander (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Nigerian physician and politician (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian physician and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian physician and politician (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian producer and manager (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hat\" title=\"<PERSON><PERSON><PERSON> Bhat\"><PERSON><PERSON><PERSON></a>, Indian producer and manager (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hat\" title=\"<PERSON><PERSON><PERSON>hat\"><PERSON><PERSON><PERSON></a>, Indian producer and manager (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Indian journalist and author (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian journalist and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian journalist and author (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, South African footballer (b. 1965)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Russian-American mathematician and theorist (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician and theorist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician and theorist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON> <PERSON>, American director, producer, and screenwriter, created Battlestar Galactica (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter, created <i><a href=\"https://wikipedia.org/wiki/Battlestar_Galactica\" title=\"Battlestar Galactica\">Battlestar Galactica</a></i> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter, created <i><a href=\"https://wikipedia.org/wiki/Battlestar_Galactica\" title=\"Battlestar Galactica\">Battlestar Galactica</a></i> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battlestar Galactica", "link": "https://wikipedia.org/wiki/Battlestar_Galactica"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Iranian singer-songwriter (b. 1984)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian singer-songwriter (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian singer-songwriter (b. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ei"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian director, producer, and screenwriter (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/K._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1929)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English actor and screenwriter (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American television journalist (b. 1955)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Slovene inventor and Olympic athlete (b. 1919)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Slovene inventor and Olympic athlete (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Slovene inventor and Olympic athlete (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English comedian, singer and television presenter (b. 1932)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Des_O%27Connor\" title=\"<PERSON>\"><PERSON></a>, English comedian, singer and television presenter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Des_O%27Connor\" title=\"<PERSON>\"><PERSON></a>, English comedian, singer and television presenter (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Des_O%27Connor"}]}, {"year": "2024", "text": "<PERSON>, English guitarist (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Flick\" title=\"Vic Flick\"><PERSON></a>, English guitarist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Flick\" title=\"Vic Flick\"><PERSON></a>, English guitarist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_Flick"}]}, {"year": "2024", "text": "<PERSON>, English songwriter and producer (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and producer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and producer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}