{"date": "April 26", "url": "https://wikipedia.org/wiki/April_26", "data": {"Events": [{"year": "1336", "text": "<PERSON> (Petrarch) ascends Mont Ventoux.", "html": "1336 - <PERSON> (<a href=\"https://wikipedia.org/wiki/Petrarch\" title=\"Petrarch\"><PERSON><PERSON></a>) ascends <a href=\"https://wikipedia.org/wiki/Mont_Ventoux\" title=\"Mont Ventoux\"><PERSON></a>.", "no_year_html": "<PERSON> (<a href=\"https://wikipedia.org/wiki/Petrarch\" title=\"Petrarch\">Petra<PERSON></a>) ascends <a href=\"https://wikipedia.org/wiki/Mont_Ventoux\" title=\"Mont Ventoux\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petrarch"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mont_V<PERSON>oux"}]}, {"year": "1478", "text": "The <PERSON><PERSON> family attack on <PERSON> in order to displace the ruling Medici family kills his brother <PERSON><PERSON><PERSON><PERSON> during High Mass in Florence Cathedral.", "html": "1478 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_family\" class=\"mw-redirect\" title=\"Paz<PERSON> family\">Pazzi family</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_conspiracy\" title=\"Pazzi conspiracy\">attack</a> on <a href=\"https://wikipedia.org/wiki/<PERSON>_de%27_Medici\" title=\"<PERSON> de<PERSON>\"><PERSON></a> in order to displace the ruling <a href=\"https://wikipedia.org/wiki/Medici_family\" class=\"mw-redirect\" title=\"Medici family\">Medici family</a> kills his brother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> during <a href=\"https://wikipedia.org/wiki/Solemn_Mass\" title=\"Solemn Mass\">High Mass</a> in <a href=\"https://wikipedia.org/wiki/Florence_Cathedral\" title=\"Florence Cathedral\">Florence Cathedral</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_family\" class=\"mw-redirect\" title=\"Pazzi family\"><PERSON><PERSON> family</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_conspiracy\" title=\"Pazzi conspiracy\">attack</a> on <a href=\"https://wikipedia.org/wiki/<PERSON>_de%27_Medici\" title=\"<PERSON> de<PERSON>\"><PERSON></a> in order to displace the ruling <a href=\"https://wikipedia.org/wiki/Medici_family\" class=\"mw-redirect\" title=\"Medici family\">Medici family</a> kills his brother <a href=\"https://wikipedia.org/wiki/Giulian<PERSON>_de%27_Medici\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> during <a href=\"https://wikipedia.org/wiki/Solemn_Mass\" title=\"Solemn Mass\">High Mass</a> in <a href=\"https://wikipedia.org/wiki/Florence_Cathedral\" title=\"Florence Cathedral\">Florence Cathedral</a>.", "links": [{"title": "<PERSON><PERSON> family", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_family"}, {"title": "<PERSON>zi conspiracy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_conspiracy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_Medici"}, {"title": "<PERSON> family", "link": "https://wikipedia.org/wiki/<PERSON>_family"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>"}, {"title": "Solemn Mass", "link": "https://wikipedia.org/wiki/Solemn_Mass"}, {"title": "Florence Cathedral", "link": "https://wikipedia.org/wiki/Florence_Cathedral"}]}, {"year": "1564", "text": "Playwright <PERSON> is baptized in Stratford-upon-Avon, Warwickshire, England (date of birth is unknown).", "html": "1564 - <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Shakespeare\"><PERSON></a> is baptized in <a href=\"https://wikipedia.org/wiki/Stratford-upon-Avon\" title=\"Stratford-upon-Avon\">Stratford-upon-Avon</a>, <a href=\"https://wikipedia.org/wiki/Warwickshire\" title=\"Warwickshire\">Warwickshire</a>, England (date of birth is unknown).", "no_year_html": "Playwright <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Shakespeare\"><PERSON></a> is baptized in <a href=\"https://wikipedia.org/wiki/Stratford-upon-Avon\" title=\"Stratford-upon-Avon\">Stratford-upon-Avon</a>, <a href=\"https://wikipedia.org/wiki/Warwickshire\" title=\"Warwickshire\">Warwickshire</a>, England (date of birth is unknown).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Stratford-upon-Avon", "link": "https://wikipedia.org/wiki/Stratford-upon-Avon"}, {"title": "Warwickshire", "link": "https://wikipedia.org/wiki/Warwickshire"}]}, {"year": "1607", "text": "The Virginia Company colonists make landfall at Cape Henry.", "html": "1607 - The <a href=\"https://wikipedia.org/wiki/Virginia_Company\" title=\"Virginia Company\">Virginia Company</a> colonists make landfall at <a href=\"https://wikipedia.org/wiki/Cape_Henry\" title=\"Cape Henry\">Cape Henry</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Virginia_Company\" title=\"Virginia Company\">Virginia Company</a> colonists make landfall at <a href=\"https://wikipedia.org/wiki/Cape_Henry\" title=\"Cape Henry\">Cape Henry</a>.", "links": [{"title": "Virginia Company", "link": "https://wikipedia.org/wiki/Virginia_Company"}, {"title": "Cape Henry", "link": "https://wikipedia.org/wiki/Cape_Henry"}]}, {"year": "1721", "text": "A massive earthquake devastates the Iranian city of Tabriz.", "html": "1721 - <a href=\"https://wikipedia.org/wiki/1721_Tabriz_earthquake\" title=\"1721 Tabriz earthquake\">A massive earthquake</a> devastates the Iranian city of <a href=\"https://wikipedia.org/wiki/Tabriz\" title=\"Tabriz\">Tabriz</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1721_Tabriz_earthquake\" title=\"1721 Tabriz earthquake\">A massive earthquake</a> devastates the Iranian city of <a href=\"https://wikipedia.org/wiki/Tabriz\" title=\"Tabriz\">Tabriz</a>.", "links": [{"title": "1721 Tabriz earthquake", "link": "https://wikipedia.org/wiki/1721_Tabriz_earthquake"}, {"title": "Tabriz", "link": "https://wikipedia.org/wiki/Tabriz"}]}, {"year": "1777", "text": "<PERSON><PERSON><PERSON>, aged 16, allegedly rode 40 miles (64 km) to alert American colonial forces to the approach of the British regular forces", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, aged 16, allegedly rode 40 miles (64 km) to alert American colonial forces to the approach of the <a href=\"https://wikipedia.org/wiki/British_Armed_Forces\" title=\"British Armed Forces\">British regular forces</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, aged 16, allegedly rode 40 miles (64 km) to alert American colonial forces to the approach of the <a href=\"https://wikipedia.org/wiki/British_Armed_Forces\" title=\"British Armed Forces\">British regular forces</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "British Armed Forces", "link": "https://wikipedia.org/wiki/British_Armed_Forces"}]}, {"year": "1794", "text": "Battle of Beaumont during the Flanders Campaign of the War of the First Coalition.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Battle_of_Beaumont_(1794)\" title=\"Battle of Beaumont (1794)\">Battle of Beaumont</a> during the <a href=\"https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition\" title=\"Low Countries theatre of the War of the First Coalition\">Flanders Campaign</a> of the <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Beaumont_(1794)\" title=\"Battle of Beaumont (1794)\">Battle of Beaumont</a> during the <a href=\"https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition\" title=\"Low Countries theatre of the War of the First Coalition\">Flanders Campaign</a> of the <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>.", "links": [{"title": "Battle of Beaumont (1794)", "link": "https://wikipedia.org/wiki/Battle_of_Beaumont_(1794)"}, {"title": "Low Countries theatre of the War of the First Coalition", "link": "https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition"}, {"title": "War of the First Coalition", "link": "https://wikipedia.org/wiki/War_of_the_First_Coalition"}]}, {"year": "1802", "text": "<PERSON> signs a general amnesty to allow all but about one thousand of the most notorious émigrés of the French Revolution to return to France.", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Napoleon\"><PERSON></a> signs a general amnesty to allow all but about one thousand of the most notorious <a href=\"https://wikipedia.org/wiki/French_emigration_(1789%E2%80%931815)\" title=\"French emigration (1789-1815)\">émigrés of the French Revolution</a> to return to France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Napoleon\"><PERSON></a> signs a general amnesty to allow all but about one thousand of the most notorious <a href=\"https://wikipedia.org/wiki/French_emigration_(1789%E2%80%931815)\" title=\"French emigration (1789-1815)\">émigrés of the French Revolution</a> to return to France.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "French emigration (1789-1815)", "link": "https://wikipedia.org/wiki/French_emigration_(1789%E2%80%931815)"}]}, {"year": "1803", "text": "Thousands of meteor fragments fall from the skies of L'Aigle, France; the event convinces European scientists that meteors exist.", "html": "1803 - Thousands of <a href=\"https://wikipedia.org/wiki/L%27Aigle_(meteorite)\" title=\"<PERSON><PERSON><PERSON>gle (meteorite)\">meteor fragments fall</a> from the skies of <a href=\"https://wikipedia.org/wiki/L%27Aigle\" title=\"L'Aigle\"><PERSON><PERSON><PERSON></a>, France; the event convinces European scientists that meteors exist.", "no_year_html": "Thousands of <a href=\"https://wikipedia.org/wiki/L%27Aigle_(meteorite)\" title=\"<PERSON><PERSON>Aigle (meteorite)\">meteor fragments fall</a> from the skies of <a href=\"https://wikipedia.org/wiki/L%27Aigle\" title=\"L'Aigle\"><PERSON><PERSON><PERSON></a>, France; the event convinces European scientists that meteors exist.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (meteorite)", "link": "https://wikipedia.org/wiki/L%27Aigle_(meteorite)"}, {"title": "L'Aigle", "link": "https://wikipedia.org/wiki/L%27Aigle"}]}, {"year": "1805", "text": "First Barbary War: United States Marines captured Derne under the command of First Lieutenant <PERSON>.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marines\" class=\"mw-redirect\" title=\"United States Marines\">United States Marines</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Derne\" class=\"mw-redirect\" title=\"Battle of Derne\">captured</a> <a href=\"https://wikipedia.org/wiki/Derna,_Libya\" title=\"Derna, Libya\">Derne</a> under the command of <a href=\"https://wikipedia.org/wiki/First_Lieutenant\" class=\"mw-redirect\" title=\"First Lieutenant\">First Lieutenant</a> <a href=\"https://wikipedia.org/wiki/Presley_O%27Bannon\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marines\" class=\"mw-redirect\" title=\"United States Marines\">United States Marines</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Derne\" class=\"mw-redirect\" title=\"Battle of Derne\">captured</a> <a href=\"https://wikipedia.org/wiki/Derna,_Libya\" title=\"Derna, Libya\">Derne</a> under the command of <a href=\"https://wikipedia.org/wiki/First_Lieutenant\" class=\"mw-redirect\" title=\"First Lieutenant\">First Lieutenant</a> <a href=\"https://wikipedia.org/wiki/Presley_O%27Bannon\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "First Barbary War", "link": "https://wikipedia.org/wiki/First_Barbary_War"}, {"title": "United States Marines", "link": "https://wikipedia.org/wiki/United_States_Marines"}, {"title": "Battle of Derne", "link": "https://wikipedia.org/wiki/Battle_of_Derne"}, {"title": "Derna, Libya", "link": "https://wikipedia.org/wiki/Derna,_Libya"}, {"title": "First Lieutenant", "link": "https://wikipedia.org/wiki/First_Lieutenant"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Presley_O%27Bannon"}]}, {"year": "1865", "text": "Union cavalry troopers corner and shoot dead <PERSON>, assassin of President <PERSON>, in Virginia.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> cavalry troopers corner and shoot dead <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassin of President <PERSON></a>, in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> cavalry troopers corner and shoot dead <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassin of President <PERSON></a>, in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "links": [{"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}, {"year": "1900", "text": "Fires destroy Canadian cities Ottawa and Hull, reducing them to ashes in 12 hours. Twelve thousand people are left without a home.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/1900_Hull%E2%80%93Ottawa_fire\" title=\"1900 Hull-Ottawa fire\">Fires</a> destroy Canadian cities Ottawa and Hull, reducing them to ashes in 12 hours. Twelve thousand people are left without a home.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1900_Hull%E2%80%93Ottawa_fire\" title=\"1900 Hull-Ottawa fire\">Fires</a> destroy Canadian cities Ottawa and Hull, reducing them to ashes in 12 hours. Twelve thousand people are left without a home.", "links": [{"title": "1900 Hull-Ottawa fire", "link": "https://wikipedia.org/wiki/1900_Hull%E2%80%93Ottawa_fire"}]}, {"year": "1903", "text": "Atlético Madrid Association football club is founded", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Atl%C3%A9tico_Madrid\" title=\"Atlético Madrid\">Atlético Madrid</a> <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">Association football</a> club is founded", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atl%C3%A9tico_Madrid\" title=\"Atlético Madrid\">Atlético Madrid</a> <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">Association football</a> club is founded", "links": [{"title": "Atlético Madrid", "link": "https://wikipedia.org/wiki/Atl%C3%A9tico_Madrid"}, {"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}]}, {"year": "1915", "text": "World War I: Italy secretly signs the Treaty of London pledging to join the Allied Powers.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> secretly signs the <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1915)\" title=\"Treaty of London (1915)\">Treaty of London</a> pledging to join the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied Powers</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> secretly signs the <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1915)\" title=\"Treaty of London (1915)\">Treaty of London</a> pledging to join the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied Powers</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Treaty of London (1915)", "link": "https://wikipedia.org/wiki/Treaty_of_London_(1915)"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}]}, {"year": "1916", "text": "Easter Rising: Battle of Mount Street Bridge", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Mount_Street_Bridge\" title=\"Battle of Mount Street Bridge\">Battle of Mount Street Bridge</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Mount_Street_Bridge\" title=\"Battle of Mount Street Bridge\">Battle of Mount Street Bridge</a>", "links": [{"title": "Easter Rising", "link": "https://wikipedia.org/wiki/Easter_Rising"}, {"title": "Battle of Mount Street Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Mount_Street_Bridge"}]}, {"year": "1920", "text": "Ice hockey makes its Olympic debut at the Antwerp Games with center <PERSON> scoring seven goals in Canada's 12-1 drubbing of Sweden in the gold medal match.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">Ice hockey</a> makes its Olympic debut at the <a href=\"https://wikipedia.org/wiki/1920_Summer_Olympics\" title=\"1920 Summer Olympics\">Antwerp Games</a> with center <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> scoring seven goals in Canada's 12-1 drubbing of Sweden in the gold medal match.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">Ice hockey</a> makes its Olympic debut at the <a href=\"https://wikipedia.org/wiki/1920_Summer_Olympics\" title=\"1920 Summer Olympics\">Antwerp Games</a> with center <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> scoring seven goals in Canada's 12-1 drubbing of Sweden in the gold medal match.", "links": [{"title": "Ice hockey", "link": "https://wikipedia.org/wiki/Ice_hockey"}, {"title": "1920 Summer Olympics", "link": "https://wikipedia.org/wiki/1920_Summer_Olympics"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "The <PERSON> of York weds Lady <PERSON> at Westminster Abbey.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">The Duke of York</a> <a href=\"https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>,_Duke_of_York,_and_Lady_<PERSON>\" class=\"mw-redirect\" title=\"Wedding of Prince <PERSON>, Duke of <PERSON>, and Lady <PERSON>\">weds</a> <a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother\" title=\"Queen <PERSON> Queen <PERSON>\">Lady <PERSON>-<PERSON></a> at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">The Duke of York</a> <a href=\"https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>,_Duke_of_York,_and_Lady_<PERSON>\" class=\"mw-redirect\" title=\"Wedding of <PERSON>, Duke of York, and Lady <PERSON>\">weds</a> <a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother\" title=\"Queen <PERSON> Queen Mother\">Lady <PERSON>-<PERSON></a> at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wedding of <PERSON>, Duke of York, and Lady <PERSON>", "link": "https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>,_Duke_of_York,_and_Lady_<PERSON>_<PERSON>-<PERSON>"}, {"title": "Queen <PERSON> The Queen Mother", "link": "https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1925", "text": "<PERSON> defeats <PERSON> in the second round of the German presidential election to become the first directly elected head of state of the Weimar Republic.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the second round of the <a href=\"https://wikipedia.org/wiki/1925_German_presidential_election\" title=\"1925 German presidential election\">German presidential election</a> to become the first directly elected <a href=\"https://wikipedia.org/wiki/Head_of_state\" title=\"Head of state\">head of state</a> of the <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Weimar Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the second round of the <a href=\"https://wikipedia.org/wiki/1925_German_presidential_election\" title=\"1925 German presidential election\">German presidential election</a> to become the first directly elected <a href=\"https://wikipedia.org/wiki/Head_of_state\" title=\"Head of state\">head of state</a> of the <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Weimar Republic</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "1925 German presidential election", "link": "https://wikipedia.org/wiki/1925_German_presidential_election"}, {"title": "Head of state", "link": "https://wikipedia.org/wiki/Head_of_state"}, {"title": "Weimar Republic", "link": "https://wikipedia.org/wiki/Weimar_Republic"}]}, {"year": "1933", "text": "The Gestapo, the official secret police force of Nazi Germany, is established by <PERSON>.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/Gestapo\" title=\"Gestapo\">Gestapo</a>, the official <a href=\"https://wikipedia.org/wiki/Secret_police\" title=\"Secret police\">secret police force</a> of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>, is established by <a href=\"https://wikipedia.org/wiki/Hermann_<PERSON>%C3%B6ring\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Gestapo\" title=\"Gestapo\">Gestapo</a>, the official <a href=\"https://wikipedia.org/wiki/Secret_police\" title=\"Secret police\">secret police force</a> of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>, is established by <a href=\"https://wikipedia.org/wiki/Hermann_<PERSON>%C3%B6ring\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Gestapo", "link": "https://wikipedia.org/wiki/Gestapo"}, {"title": "Secret police", "link": "https://wikipedia.org/wiki/Secret_police"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hermann_G%C3%B6ring"}]}, {"year": "1937", "text": "Spanish Civil War: Guernica, Spain, is bombed by the German Condor Legion and the Italian Aviazione Legionaria.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Guernica\" title=\"Guernica\">Guernica</a>, Spain, is <a href=\"https://wikipedia.org/wiki/Bombing_of_Guernica\" title=\"Bombing of Guernica\">bombed</a> by the German <a href=\"https://wikipedia.org/wiki/Condor_Legion\" title=\"Condor Legion\">Condor Legion</a> and the Italian <a href=\"https://wikipedia.org/wiki/Aviazione_Legionaria\" title=\"Aviazione Legionaria\">Aviazione Legionaria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Guernica\" title=\"Guernica\">Guernica</a>, Spain, is <a href=\"https://wikipedia.org/wiki/Bombing_of_Guernica\" title=\"Bombing of Guernica\">bombed</a> by the German <a href=\"https://wikipedia.org/wiki/Condor_Legion\" title=\"Condor Legion\">Condor Legion</a> and the Italian <a href=\"https://wikipedia.org/wiki/Aviazione_Legionaria\" title=\"Aviazione Legionaria\">Aviazione Legionaria</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Guernica", "link": "https://wikipedia.org/wiki/Guernica"}, {"title": "Bombing of Guernica", "link": "https://wikipedia.org/wiki/Bombing_of_Guernica"}, {"title": "Condor Legion", "link": "https://wikipedia.org/wiki/Condor_Legion"}, {"title": "Aviazione Legionaria", "link": "https://wikipedia.org/wiki/Aviazione_Legionaria"}]}, {"year": "1942", "text": "Benxihu Colliery accident in Manchukuo leaves 1,549 Chinese miners dead.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Benxihu_Colliery\" title=\"Benxihu Colliery\"><PERSON><PERSON><PERSON> Colliery</a> accident in <a href=\"https://wikipedia.org/wiki/Manchukuo\" title=\"Manchukuo\">Manchukuo</a> leaves 1,549 Chinese miners dead.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benxihu_Colliery\" title=\"Benxihu Colliery\"><PERSON><PERSON><PERSON></a> accident in <a href=\"https://wikipedia.org/wiki/Manchukuo\" title=\"Manchukuo\">Manchukuo</a> leaves 1,549 Chinese miners dead.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benxihu_Colliery"}, {"title": "Manchukuo", "link": "https://wikipedia.org/wiki/Manchukuo"}]}, {"year": "1943", "text": "The Easter Riots break out in Uppsala, Sweden.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/Easter_Riots\" title=\"Easter Riots\">Easter Riots</a> break out in <a href=\"https://wikipedia.org/wiki/Uppsala\" title=\"Uppsala\">Uppsala</a>, Sweden.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Easter_Riots\" title=\"Easter Riots\">Easter Riots</a> break out in <a href=\"https://wikipedia.org/wiki/Uppsala\" title=\"Uppsala\">Uppsala</a>, Sweden.", "links": [{"title": "Easter Riots", "link": "https://wikipedia.org/wiki/Easter_Riots"}, {"title": "Uppsala", "link": "https://wikipedia.org/wiki/Uppsala"}]}, {"year": "1944", "text": "<PERSON><PERSON> becomes head of the Greek government-in-exile based in Egypt.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes head of the <a href=\"https://wikipedia.org/wiki/Greek_government-in-exile\" title=\"Greek government-in-exile\">Greek government-in-exile</a> based in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes head of the <a href=\"https://wikipedia.org/wiki/Greek_government-in-exile\" title=\"Greek government-in-exile\">Greek government-in-exile</a> based in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_<PERSON>nd<PERSON>ou"}, {"title": "Greek government-in-exile", "link": "https://wikipedia.org/wiki/Greek_government-in-exile"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1944", "text": "<PERSON> is captured by Allied commandos in occupied Crete.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is captured by Allied commandos in <a href=\"https://wikipedia.org/wiki/Axis_occupation_of_Greece\" title=\"Axis occupation of Greece\">occupied Crete</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is captured by Allied commandos in <a href=\"https://wikipedia.org/wiki/Axis_occupation_of_Greece\" title=\"Axis occupation of Greece\">occupied Crete</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Axis occupation of Greece", "link": "https://wikipedia.org/wiki/Axis_occupation_of_Greece"}]}, {"year": "1945", "text": "World War II: Battle of Bautzen: Last successful German tank-offensive of the war and last noteworthy victory of the Wehrmacht.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Bautzen_(1945)\" title=\"Battle of Bautzen (1945)\">Battle of Bautzen</a>: Last successful German tank-offensive of the war and last noteworthy victory of the <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Bautzen_(1945)\" title=\"Battle of Bautzen (1945)\">Battle of Bautzen</a>: Last successful German tank-offensive of the war and last noteworthy victory of the <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Bautzen (1945)", "link": "https://wikipedia.org/wiki/Battle_of_Bautzen_(1945)"}, {"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}]}, {"year": "1945", "text": "World War II: Filipino troops of the 66th Infantry Regiment, Philippine Commonwealth Army, USAFIP-NL and the American troops of the 33rd and 37th Infantry Division, United States Army liberate Baguio as they fight against the Japanese forces under General <PERSON><PERSON><PERSON>.", "html": "1945 - World War II: Filipino troops of the 66th Infantry Regiment, Philippine Commonwealth Army, USAFIP-NL and the American troops of the 33rd and 37th Infantry Division, United States Army liberate <a href=\"https://wikipedia.org/wiki/Baguio\" title=\"Bagu<PERSON>\"><PERSON><PERSON><PERSON></a> as they fight against the Japanese forces under General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ita\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "World War II: Filipino troops of the 66th Infantry Regiment, Philippine Commonwealth Army, USAFIP-NL and the American troops of the 33rd and 37th Infantry Division, United States Army liberate <a href=\"https://wikipedia.org/wiki/Baguio\" title=\"Bagu<PERSON>\"><PERSON><PERSON><PERSON></a> as they fight against the Japanese forces under General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Baguio", "link": "https://wikipedia.org/wiki/Baguio"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "The Geneva Conference, an effort to restore peace in Indochina and Korea, begins.", "html": "1954 - The <a href=\"https://wikipedia.org/wiki/Geneva_Conference_(1954)\" class=\"mw-redirect\" title=\"Geneva Conference (1954)\">Geneva Conference</a>, an effort to restore peace in <a href=\"https://wikipedia.org/wiki/Indochina\" class=\"mw-redirect\" title=\"Indochina\">Indochina</a> and Korea, begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Geneva_Conference_(1954)\" class=\"mw-redirect\" title=\"Geneva Conference (1954)\">Geneva Conference</a>, an effort to restore peace in <a href=\"https://wikipedia.org/wiki/Indochina\" class=\"mw-redirect\" title=\"Indochina\">Indochina</a> and Korea, begins.", "links": [{"title": "Geneva Conference (1954)", "link": "https://wikipedia.org/wiki/Geneva_Conference_(1954)"}, {"title": "Indochina", "link": "https://wikipedia.org/wiki/Indochina"}]}, {"year": "1954", "text": "The first clinical trials of <PERSON>'s polio vaccine begin in Fairfax County, Virginia.", "html": "1954 - The first clinical trials of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a> begin in <a href=\"https://wikipedia.org/wiki/Fairfax_County,_Virginia\" title=\"Fairfax County, Virginia\">Fairfax County, Virginia</a>.", "no_year_html": "The first clinical trials of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a> begin in <a href=\"https://wikipedia.org/wiki/Fairfax_County,_Virginia\" title=\"Fairfax County, Virginia\">Fairfax County, Virginia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Polio vaccine", "link": "https://wikipedia.org/wiki/Polio_vaccine"}, {"title": "Fairfax County, Virginia", "link": "https://wikipedia.org/wiki/Fairfax_County,_Virginia"}]}, {"year": "1956", "text": "SS Ideal X, the world's first successful container ship, leaves Port Newark, New Jersey, for Houston, Texas.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/SS_Ideal_X\" title=\"SS Ideal X\">SS <i>Ideal X</i></a>, the world's first successful <a href=\"https://wikipedia.org/wiki/Container_ship\" title=\"Container ship\">container ship</a>, leaves <a href=\"https://wikipedia.org/wiki/Port_Newark%E2%80%93Elizabeth_Marine_Terminal\" title=\"Port Newark-Elizabeth Marine Terminal\">Port Newark, New Jersey</a>, for <a href=\"https://wikipedia.org/wiki/Houston\" title=\"Houston\">Houston</a>, Texas.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SS_Ideal_X\" title=\"SS Ideal X\">SS <i>Ideal X</i></a>, the world's first successful <a href=\"https://wikipedia.org/wiki/Container_ship\" title=\"Container ship\">container ship</a>, leaves <a href=\"https://wikipedia.org/wiki/Port_Newark%E2%80%93Elizabeth_Marine_Terminal\" title=\"Port Newark-Elizabeth Marine Terminal\">Port Newark, New Jersey</a>, for <a href=\"https://wikipedia.org/wiki/Houston\" title=\"Houston\">Houston</a>, Texas.", "links": [{"title": "SS Ideal X", "link": "https://wikipedia.org/wiki/SS_Ideal_X"}, {"title": "Container ship", "link": "https://wikipedia.org/wiki/Container_ship"}, {"title": "Port Newark-Elizabeth Marine Terminal", "link": "https://wikipedia.org/wiki/Port_Newark%E2%80%93E<PERSON>zabeth_Marine_Terminal"}, {"title": "Houston", "link": "https://wikipedia.org/wiki/Houston"}]}, {"year": "1958", "text": "Final run of the Baltimore and Ohio Railroad's Royal Blue from Washington, D.C., to New York City after 68 years, the first U.S. passenger train to use electric locomotives.", "html": "1958 - Final run of the <a href=\"https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad\" title=\"Baltimore and Ohio Railroad\">Baltimore and Ohio Railroad</a>'s <i><a href=\"https://wikipedia.org/wiki/Royal_Blue_(train)\" title=\"Royal Blue (train)\">Royal Blue</a></i> from Washington, D.C., to New York City after 68 years, the first U.S. passenger train to use <a href=\"https://wikipedia.org/wiki/Railway_electrification_system\" class=\"mw-redirect\" title=\"Railway electrification system\">electric locomotives</a>.", "no_year_html": "Final run of the <a href=\"https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad\" title=\"Baltimore and Ohio Railroad\">Baltimore and Ohio Railroad</a>'s <i><a href=\"https://wikipedia.org/wiki/Royal_Blue_(train)\" title=\"Royal Blue (train)\">Royal Blue</a></i> from Washington, D.C., to New York City after 68 years, the first U.S. passenger train to use <a href=\"https://wikipedia.org/wiki/Railway_electrification_system\" class=\"mw-redirect\" title=\"Railway electrification system\">electric locomotives</a>.", "links": [{"title": "Baltimore and Ohio Railroad", "link": "https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad"}, {"title": "Royal Blue (train)", "link": "https://wikipedia.org/wiki/Royal_Blue_(train)"}, {"title": "Railway electrification system", "link": "https://wikipedia.org/wiki/Railway_electrification_system"}]}, {"year": "1960", "text": "Forced out by the April Revolution, President of South Korea <PERSON><PERSON><PERSON> resigns after 12 years of dictatorial rule.", "html": "1960 - Forced out by the <a href=\"https://wikipedia.org/wiki/April_Revolution\" title=\"April Revolution\">April Revolution</a>, <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> <a href=\"https://wikipedia.org/wiki/Syngman_<PERSON>hee\" title=\"Syngman <PERSON>hee\">Syn<PERSON></a> resigns after 12 years of <a href=\"https://wikipedia.org/wiki/First_Republic_of_Korea\" title=\"First Republic of Korea\">dictatorial rule</a>.", "no_year_html": "Forced out by the <a href=\"https://wikipedia.org/wiki/April_Revolution\" title=\"April Revolution\">April Revolution</a>, <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> <a href=\"https://wikipedia.org/wiki/Syngman_<PERSON>hee\" title=\"Syngman Rhee\">Syn<PERSON></a> resigns after 12 years of <a href=\"https://wikipedia.org/wiki/First_Republic_of_Korea\" title=\"First Republic of Korea\">dictatorial rule</a>.", "links": [{"title": "April Revolution", "link": "https://wikipedia.org/wiki/April_Revolution"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "First Republic of Korea", "link": "https://wikipedia.org/wiki/First_Republic_of_Korea"}]}, {"year": "1962", "text": "NASA's Ranger 4 spacecraft crashes into the Moon.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Ranger_4\" title=\"Ranger 4\">Ranger 4</a> spacecraft crashes into the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Ranger_4\" title=\"Ranger 4\">Ranger 4</a> spacecraft crashes into the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Ranger 4", "link": "https://wikipedia.org/wiki/Ranger_4"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1962", "text": "The British space programme launches its first satellite, the Ariel 1.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/British_space_programme\" title=\"British space programme\">British space programme</a> launches its first satellite, the <a href=\"https://wikipedia.org/wiki/Ariel_1\" title=\"Ariel 1\">Ariel 1</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/British_space_programme\" title=\"British space programme\">British space programme</a> launches its first satellite, the <a href=\"https://wikipedia.org/wiki/Ariel_1\" title=\"Ariel 1\">Ariel 1</a>.", "links": [{"title": "British space programme", "link": "https://wikipedia.org/wiki/British_space_programme"}, {"title": "Ariel 1", "link": "https://wikipedia.org/wiki/Ariel_1"}]}, {"year": "1963", "text": "In Libya, amendments to the constitution transform Libya (United Kingdom of Libya) into one national unity (Kingdom of Libya) and allows for female participation in elections.", "html": "1963 - In <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, amendments to the constitution transform Libya (United Kingdom of Libya) into one national unity (<a href=\"https://wikipedia.org/wiki/Kingdom_of_Libya\" title=\"Kingdom of Libya\">Kingdom of Libya</a>) and allows for female participation in elections.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, amendments to the constitution transform Libya (United Kingdom of Libya) into one national unity (<a href=\"https://wikipedia.org/wiki/Kingdom_of_Libya\" title=\"Kingdom of Libya\">Kingdom of Libya</a>) and allows for female participation in elections.", "links": [{"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "Kingdom of Libya", "link": "https://wikipedia.org/wiki/Kingdom_of_Libya"}]}, {"year": "1964", "text": "Tanganyika and Zanzibar merge to form the United Republic of Tanzania.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)\" title=\"Tanganyika (1961-1964)\">Tanganyika</a> and <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a> merge to form the <a href=\"https://wikipedia.org/wiki/United_Republic_of_Tanzania\" class=\"mw-redirect\" title=\"United Republic of Tanzania\">United Republic of Tanzania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)\" title=\"Tanganyika (1961-1964)\">Tanganyika</a> and <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a> merge to form the <a href=\"https://wikipedia.org/wiki/United_Republic_of_Tanzania\" class=\"mw-redirect\" title=\"United Republic of Tanzania\">United Republic of Tanzania</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (1961-1964)", "link": "https://wikipedia.org/wiki/Tanganyika_(1961%E2%80%931964)"}, {"title": "Zanzibar", "link": "https://wikipedia.org/wiki/Zanzibar"}, {"title": "United Republic of Tanzania", "link": "https://wikipedia.org/wiki/United_Republic_of_Tanzania"}]}, {"year": "1966", "text": "The magnitude 5.1 Tashkent earthquake affects the largest city in Soviet Central Asia with a maximum MSK intensity of VII (Very strong). Tashkent is mostly destroyed and 15-200 are killed.", "html": "1966 - The magnitude 5.1 <a href=\"https://wikipedia.org/wiki/1966_Tashkent_earthquake\" title=\"1966 Tashkent earthquake\">Tashkent earthquake</a> affects the largest city in <a href=\"https://wikipedia.org/wiki/Soviet_Central_Asia\" title=\"Soviet Central Asia\">Soviet Central Asia</a> with a maximum <a href=\"https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale\" title=\"Medvedev-Sponheuer-Karnik scale\">MSK intensity</a> of VII (<i>Very strong</i>). <a href=\"https://wikipedia.org/wiki/Tashkent\" title=\"Tashkent\">Tashkent</a> is mostly destroyed and 15-200 are killed.", "no_year_html": "The magnitude 5.1 <a href=\"https://wikipedia.org/wiki/1966_Tashkent_earthquake\" title=\"1966 Tashkent earthquake\">Tashkent earthquake</a> affects the largest city in <a href=\"https://wikipedia.org/wiki/Soviet_Central_Asia\" title=\"Soviet Central Asia\">Soviet Central Asia</a> with a maximum <a href=\"https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale\" title=\"Medvedev-Sponheuer-Karnik scale\">MSK intensity</a> of VII (<i>Very strong</i>). <a href=\"https://wikipedia.org/wiki/Tashkent\" title=\"Tashkent\">Tashkent</a> is mostly destroyed and 15-200 are killed.", "links": [{"title": "1966 Tashkent earthquake", "link": "https://wikipedia.org/wiki/1966_Tashkent_earthquake"}, {"title": "Soviet Central Asia", "link": "https://wikipedia.org/wiki/Soviet_Central_Asia"}, {"title": "<PERSON>d<PERSON><PERSON>-Sponheuer-Karnik scale", "link": "https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale"}, {"title": "Tashkent", "link": "https://wikipedia.org/wiki/Tashkent"}]}, {"year": "1966", "text": "A new government is formed in the Republic of the Congo, led by <PERSON><PERSON><PERSON>.", "html": "1966 - A new government is formed in the <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo\" title=\"Republic of the Congo\">Republic of the Congo</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "A new government is formed in the <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo\" title=\"Republic of the Congo\">Republic of the Congo</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Republic of the Congo", "link": "https://wikipedia.org/wiki/Republic_of_the_Congo"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "The Convention Establishing the World Intellectual Property Organization enters into force.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Convention_Establishing_the_World_Intellectual_Property_Organization\" class=\"mw-redirect\" title=\"Convention Establishing the World Intellectual Property Organization\">Convention Establishing the World Intellectual Property Organization</a> enters into force.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Convention_Establishing_the_World_Intellectual_Property_Organization\" class=\"mw-redirect\" title=\"Convention Establishing the World Intellectual Property Organization\">Convention Establishing the World Intellectual Property Organization</a> enters into force.", "links": [{"title": "Convention Establishing the World Intellectual Property Organization", "link": "https://wikipedia.org/wiki/Convention_Establishing_the_World_Intellectual_Property_Organization"}]}, {"year": "1981", "text": "Dr. <PERSON> of the University of California, San Francisco Medical Center performs the world's first human open fetal surgery.", "html": "1981 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/UCSF_Medical_Center\" title=\"UCSF Medical Center\">University of California, San Francisco Medical Center</a> performs the world's first human open <a href=\"https://wikipedia.org/wiki/Fetal_surgery\" title=\"Fetal surgery\">fetal surgery</a>.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/UCSF_Medical_Center\" title=\"UCSF Medical Center\">University of California, San Francisco Medical Center</a> performs the world's first human open <a href=\"https://wikipedia.org/wiki/Fetal_surgery\" title=\"Fetal surgery\">fetal surgery</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "UCSF Medical Center", "link": "https://wikipedia.org/wiki/UCSF_Medical_Center"}, {"title": "Fetal surgery", "link": "https://wikipedia.org/wiki/Fetal_surgery"}]}, {"year": "1986", "text": "The Chernobyl disaster occurs in the Ukrainian Soviet Socialist Republic.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Chernobyl_disaster\" title=\"Chernobyl disaster\">Chernobyl disaster</a> occurs in the <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian Soviet Socialist Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chernobyl_disaster\" title=\"Chernobyl disaster\">Chernobyl disaster</a> occurs in the <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian Soviet Socialist Republic</a>.", "links": [{"title": "Chernobyl disaster", "link": "https://wikipedia.org/wiki/Chernobyl_disaster"}, {"title": "Ukrainian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic"}]}, {"year": "1989", "text": "The deadliest known tornado strikes Central Bangladesh, killing upwards of 1,300, injuring 12,000, and leaving as many as 80,000 homeless.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Daulatpur%E2%80%93Saturia_tornado\" title=\"Daulatpur-Saturia tornado\">The deadliest known tornado</a> strikes Central Bangladesh, killing upwards of 1,300, injuring 12,000, and leaving as many as 80,000 homeless.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Daulatpur%E2%80%93Saturia_tornado\" title=\"Daulatpur-Saturia tornado\">The deadliest known tornado</a> strikes Central Bangladesh, killing upwards of 1,300, injuring 12,000, and leaving as many as 80,000 homeless.", "links": [{"title": "Daulatpur-Saturia tornado", "link": "https://wikipedia.org/wiki/Daulatpur%E2%80%93Saturia_tornado"}]}, {"year": "1989", "text": "People's Daily publishes the April 26 Editorial which inflames the nascent Tiananmen Square protests.", "html": "1989 - <i><a href=\"https://wikipedia.org/wiki/People%27s_Daily\" title=\"People's Daily\">People's Daily</a></i> publishes the <a href=\"https://wikipedia.org/wiki/April_26_Editorial\" title=\"April 26 Editorial\">April 26 Editorial</a> which inflames the nascent <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">Tiananmen Square protests</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/People%27s_Daily\" title=\"People's Daily\">People's Daily</a></i> publishes the <a href=\"https://wikipedia.org/wiki/April_26_Editorial\" title=\"April 26 Editorial\">April 26 Editorial</a> which inflames the nascent <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">Tiananmen Square protests</a>.", "links": [{"title": "People's Daily", "link": "https://wikipedia.org/wiki/People%27s_Daily"}, {"title": "April 26 Editorial", "link": "https://wikipedia.org/wiki/April_26_Editorial"}, {"title": "Tiananmen Square protests of 1989", "link": "https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989"}]}, {"year": "1991", "text": "Fifty-five tornadoes break out in the central United States. Before the outbreak's end, Andover, Kansas, would record the year's only F5 tornado.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_April_26,_1991\" class=\"mw-redirect\" title=\"Tornado outbreak of April 26, 1991\">Fifty-five tornadoes</a> break out in the <a href=\"https://wikipedia.org/wiki/Central_United_States\" title=\"Central United States\">central United States</a>. Before the outbreak's end, <a href=\"https://wikipedia.org/wiki/Andover,_Kansas\" title=\"Andover, Kansas\">Andover, Kansas</a>, would record the year's only <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F5</a> tornado.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_April_26,_1991\" class=\"mw-redirect\" title=\"Tornado outbreak of April 26, 1991\">Fifty-five tornadoes</a> break out in the <a href=\"https://wikipedia.org/wiki/Central_United_States\" title=\"Central United States\">central United States</a>. Before the outbreak's end, <a href=\"https://wikipedia.org/wiki/Andover,_Kansas\" title=\"Andover, Kansas\">Andover, Kansas</a>, would record the year's only <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F5</a> tornado.", "links": [{"title": "Tornado outbreak of April 26, 1991", "link": "https://wikipedia.org/wiki/Tornado_outbreak_of_April_26,_1991"}, {"title": "Central United States", "link": "https://wikipedia.org/wiki/Central_United_States"}, {"title": "Andover, Kansas", "link": "https://wikipedia.org/wiki/Andover,_Kansas"}, {"title": "Fujita scale", "link": "https://wikipedia.org/wiki/Fujita_scale"}]}, {"year": "1993", "text": "The Space Shuttle Columbia is launched on mission STS-55 to conduct experiments aboard the Spacelab module.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-55\" title=\"STS-55\">STS-55</a> to conduct experiments aboard the <a href=\"https://wikipedia.org/wiki/Spacelab\" title=\"Spacelab\">Spacelab</a> module.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-55\" title=\"STS-55\">STS-55</a> to conduct experiments aboard the <a href=\"https://wikipedia.org/wiki/Spacelab\" title=\"Spacelab\">Spacelab</a> module.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-55", "link": "https://wikipedia.org/wiki/STS-55"}, {"title": "Spacelab", "link": "https://wikipedia.org/wiki/Spacelab"}]}, {"year": "1994", "text": "China Airlines Flight 140 crashes at Nagoya Airport in Japan, killing 264 of the 271 people on board.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_140\" title=\"China Airlines Flight 140\">China Airlines Flight 140</a> crashes at <a href=\"https://wikipedia.org/wiki/Nagoya_Airfield\" title=\"Nagoya Airfield\">Nagoya Airport</a> in Japan, killing 264 of the 271 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_140\" title=\"China Airlines Flight 140\">China Airlines Flight 140</a> crashes at <a href=\"https://wikipedia.org/wiki/Nagoya_Airfield\" title=\"Nagoya Airfield\">Nagoya Airport</a> in Japan, killing 264 of the 271 people on board.", "links": [{"title": "China Airlines Flight 140", "link": "https://wikipedia.org/wiki/China_Airlines_Flight_140"}, {"title": "Nagoya Airfield", "link": "https://wikipedia.org/wiki/Nagoya_Airfield"}]}, {"year": "1994", "text": "South Africa begins its first multiracial election, which is won by <PERSON>'s African National Congress.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a> begins its <a href=\"https://wikipedia.org/wiki/1994_South_African_general_election\" title=\"1994 South African general election\">first multiracial election</a>, which is won by <a href=\"https://wikipedia.org/wiki/Nelson<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">African National Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a> begins its <a href=\"https://wikipedia.org/wiki/1994_South_African_general_election\" title=\"1994 South African general election\">first multiracial election</a>, which is won by <a href=\"https://wikipedia.org/wiki/Nelson<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">African National Congress</a>.", "links": [{"title": "South Africa", "link": "https://wikipedia.org/wiki/South_Africa"}, {"title": "1994 South African general election", "link": "https://wikipedia.org/wiki/1994_South_African_general_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}, {"title": "African National Congress", "link": "https://wikipedia.org/wiki/African_National_Congress"}]}, {"year": "2002", "text": "<PERSON> kills 16 at Gutenberg-Gymnasium in Erfurt, Germany before committing suicide.", "html": "2002 - <PERSON> <a href=\"https://wikipedia.org/wiki/Erfurt_massacre\" class=\"mw-redirect\" title=\"Erfurt massacre\">kills 16</a> at <a href=\"https://wikipedia.org/wiki/Gutenberg-Gymnasium_Erfurt\" title=\"Gutenberg-Gymnasium Erfurt\">Gutenberg-Gymnasium</a> in <a href=\"https://wikipedia.org/wiki/Erfurt\" title=\"Erfurt\">Erfurt</a>, Germany before committing suicide.", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/Erfurt_massacre\" class=\"mw-redirect\" title=\"Erfurt massacre\">kills 16</a> at <a href=\"https://wikipedia.org/wiki/Gutenberg-Gymnasium_Erfurt\" title=\"Gutenberg-Gymnasium Erfurt\">Gutenberg-Gymnasium</a> in <a href=\"https://wikipedia.org/wiki/Erfurt\" title=\"Erfurt\">Erfurt</a>, Germany before committing suicide.", "links": [{"title": "Erfurt massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Gutenberg-Gymnasium Erfurt", "link": "https://wikipedia.org/wiki/Gutenberg-Gymnasium_Erfurt"}, {"title": "Erfurt", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>"}]}, {"year": "2005", "text": "Cedar Revolution: Under international pressure, Syria withdraws the last of its 14,000 troop military garrison in Lebanon, ending its 29-year military domination of that country (Syrian occupation of Lebanon).", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Cedar_Revolution\" title=\"Cedar Revolution\">Cedar Revolution</a>: Under international pressure, <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> withdraws the last of its 14,000 troop military garrison in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, ending its 29-year military domination of that country (<a href=\"https://wikipedia.org/wiki/Syrian_occupation_of_Lebanon\" title=\"Syrian occupation of Lebanon\">Syrian occupation of Lebanon</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cedar_Revolution\" title=\"Cedar Revolution\">Cedar Revolution</a>: Under international pressure, <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> withdraws the last of its 14,000 troop military garrison in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, ending its 29-year military domination of that country (<a href=\"https://wikipedia.org/wiki/Syrian_occupation_of_Lebanon\" title=\"Syrian occupation of Lebanon\">Syrian occupation of Lebanon</a>).", "links": [{"title": "Cedar Revolution", "link": "https://wikipedia.org/wiki/Cedar_Revolution"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Syrian occupation of Lebanon", "link": "https://wikipedia.org/wiki/Syrian_occupation_of_Lebanon"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON> is re-elected President of Kazakhstan with 97.7% of the vote, one of the biggest vote shares in Kazakhstan's history.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is re-elected <a href=\"https://wikipedia.org/wiki/President_of_Kazakhstan\" title=\"President of Kazakhstan\">President of Kazakhstan</a> with 97.7% of the vote, one of the biggest vote shares in Kazakhstan's history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is re-elected <a href=\"https://wikipedia.org/wiki/President_of_Kazakhstan\" title=\"President of Kazakhstan\">President of Kazakhstan</a> with 97.7% of the vote, one of the biggest vote shares in Kazakhstan's history.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Kazakhstan", "link": "https://wikipedia.org/wiki/President_of_Kazakhstan"}]}], "Births": [{"year": "121", "text": "<PERSON>, Roman emperor (d. 180)", "html": "121 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman emperor (d. 180)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman emperor (d. 180)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "757", "text": "<PERSON><PERSON> of Córdoba (d. 796)", "html": "757 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_C%C3%B3rdoba\" title=\"<PERSON><PERSON> I of Córdoba\"><PERSON><PERSON> of Córdoba</a> (d. 796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_C%C3%B3rdoba\" title=\"<PERSON><PERSON> I of Córdoba\"><PERSON><PERSON> of Córdoba</a> (d. 796)", "links": [{"title": "<PERSON><PERSON> of Córdoba", "link": "https://wikipedia.org/wiki/<PERSON>ham_I_of_C%C3%B3rdoba"}]}, {"year": "764", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph (d. 786)", "html": "764 - <a href=\"https://wikipedia.org/wiki/Al<PERSON>Hadi\" title=\"Al-Hadi\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (d. 786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Hadi\" title=\"Al-Hadi\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (d. 786)", "links": [{"title": "Al-Hadi", "link": "https://wikipedia.org/wiki/<PERSON>-Hadi"}]}, {"year": "1284", "text": "<PERSON>, Countess of Warwick (d. 1324)", "html": "1284 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Warwick\" title=\"<PERSON>, Countess of Warwick\"><PERSON>, Countess of Warwick</a> (d. 1324)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Warwick\" title=\"<PERSON>, Countess of Warwick\"><PERSON>, Countess of Warwick</a> (d. 1324)", "links": [{"title": "<PERSON>, Countess of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Warwick"}]}, {"year": "1319", "text": "<PERSON> of France (d. 1364)", "html": "1319 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"John II of France\"><PERSON> of France</a> (d. 1364)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (d. 1364)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1538", "text": "<PERSON><PERSON>, Italian painter and academic (d. 1600)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian painter and academic (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian painter and academic (d. 1600)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1575", "text": "<PERSON>, queen of <PERSON> of France (d. 1642)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (d. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (d. 1642)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}]}, {"year": "1647", "text": "<PERSON>, English banker, Sheriff of London, Lord Mayor of London and politician (d. 1720)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker, <a href=\"https://wikipedia.org/wiki/Sheriff_of_London\" class=\"mw-redirect\" title=\"Sheriff of London\">Sheriff of London</a>, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a> and politician (d. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker, <a href=\"https://wikipedia.org/wiki/Sheriff_of_London\" class=\"mw-redirect\" title=\"Sheriff of London\">Sheriff of London</a>, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a> and politician (d. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Sheriff of London", "link": "https://wikipedia.org/wiki/Sheriff_of_London"}, {"title": "Lord Mayor of London", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_London"}]}, {"year": "1648", "text": "<PERSON> of Portugal (d. 1706)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> II of Portugal\"><PERSON> of Portugal</a> (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> II of Portugal\"><PERSON> of Portugal</a> (d. 1706)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1697", "text": "<PERSON>, German lute player and composer (d. 1754)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Lute\" title=\"Lute\">lute</a> player and composer (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Lute\" title=\"Lute\">lute</a> player and composer (d. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_<PERSON>ckenhagen"}, {"title": "Lute", "link": "https://wikipedia.org/wiki/Lute"}]}, {"year": "1710", "text": "<PERSON>, Scottish philosopher and academic (d. 1796)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philosopher and academic (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philosopher and academic (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON><PERSON><PERSON>, American commander (d. 1802)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American commander (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American commander (d. 1802)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>se<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, German geologist and paleontologist (d. 1853)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and paleontologist (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and paleontologist (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON> of Naples and Sicily, Queen of France (d. 1866)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples_and_Sicily\" title=\"<PERSON> of Naples and Sicily\"><PERSON> of Naples and Sicily</a>, Queen of France (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples_and_Sicily\" title=\"<PERSON> of Naples and Sicily\"><PERSON> of Naples and Sicily</a>, Queen of France (d. 1866)", "links": [{"title": "<PERSON> of Naples and Sicily", "link": "https://wikipedia.org/wiki/Maria_<PERSON><PERSON>_of_Naples_and_Sicily"}]}, {"year": "1785", "text": "<PERSON>, French-American ornithologist and painter (d. 1851)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American ornithologist and painter (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American ornithologist and painter (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, German poet, philologist, and historian (d. 1862)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, philologist, and historian (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, philologist, and historian (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, French painter and lithographer (d. 1863)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and lithographer (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>x\" title=\"<PERSON>\"><PERSON></a>, French painter and lithographer (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Delacroix"}]}, {"year": "1801", "text": "<PERSON>, American politician and diplomat, 1st United States Assistant Secretary of State (d. 1889)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 1st <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 1st <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Assistant Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State"}]}, {"year": "1804", "text": "<PERSON>, American banker, lawyer, and politician (d. 1876)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American banker, lawyer, and politician (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American banker, lawyer, and politician (d. 1876)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1822", "text": "<PERSON>, American journalist and designer, co-designed Central Park (d. 1903)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and designer, co-designed <a href=\"https://wikipedia.org/wiki/Central_Park\" title=\"Central Park\">Central Park</a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and designer, co-designed <a href=\"https://wikipedia.org/wiki/Central_Park\" title=\"Central Park\">Central Park</a> (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Central Park", "link": "https://wikipedia.org/wiki/Central_Park"}]}, {"year": "1834", "text": "<PERSON>, American author (d. 1867)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Australian-New Zealand businessman and politician, 17th Prime Minister of New Zealand (d. 1930)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand businessman and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand businessman and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1862", "text": "<PERSON>, American painter and educator (d. 1938)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish artist (d. 1931)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish artist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish artist (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, German rower (d. 1959)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rower (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rower (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Irish-Australian politician, 21st Premier of New South Wales (d. 1950)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(New_South_Wales_politician)\" title=\"<PERSON> (New South Wales politician)\"><PERSON></a>, Irish-Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(New_South_Wales_politician)\" title=\"<PERSON> (New South Wales politician)\"><PERSON></a>, Irish-Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1950)", "links": [{"title": "<PERSON> (New South Wales politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(New_South_Wales_politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1878", "text": "<PERSON>, Mexican bishop and saint (d. 1938)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADzar_y_Valencia\" title=\"<PERSON> y <PERSON>\"><PERSON> y <PERSON></a>, Mexican bishop and saint (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADzar_y_Valencia\" title=\"<PERSON> y <PERSON>\"><PERSON> y <PERSON></a>, Mexican bishop and saint (d. 1938)", "links": [{"title": "<PERSON> y <PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Gu%C3%ADzar_y_Valencia"}]}, {"year": "1879", "text": "<PERSON>, British actor (d. 1917)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor (d. 1917)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1879", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (d. 1959)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1886", "text": "<PERSON>, American singer-songwriter (d. 1939)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1886", "text": "Ğabdulla <PERSON><PERSON>, Russian poet and publicist (d. 1913)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/%C4%9Eabdulla_<PERSON>\" title=\"Ğabdulla Tuqay\">Ğabdulla <PERSON><PERSON><PERSON></a>, Russian poet and publicist (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%9Eabdu<PERSON>_<PERSON>\" title=\"Ğabdulla Tuqay\">Ğabdulla <PERSON><PERSON><PERSON></a>, Russian poet and publicist (d. 1913)", "links": [{"title": "Ğabdulla Tuqay", "link": "https://wikipedia.org/wiki/%C4%9Eabdu<PERSON>_<PERSON>y"}]}, {"year": "1889", "text": "<PERSON>, American author, playwright, and screenwriter (d. 1981)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Austrian-English philosopher and academic (d. 1951)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English philosopher and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English philosopher and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, German politician and Deputy Führer in Nazi regime until 1941  (d. 1987)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician and Deputy Führer in Nazi regime until 1941 (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician and Deputy Führer in Nazi regime until 1941 (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Estonian actor and director (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian actor and director (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ru<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian actor and director (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruut_<PERSON>o"}]}, {"year": "1896", "text": "<PERSON>, leading German fighter pilot in World War I and Chief of Procurement and Supply in the Luftwaffe  (d. 1941)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leading German fighter pilot in World War I and Chief of Procurement and Supply in the Luftwaffe (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leading German fighter pilot in World War I and Chief of Procurement and Supply in the Luftwaffe (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American boxer and bobsledder (d. 1967)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and bobsledder (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and bobsledder (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German-American director and screenwriter (d. 1987)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director and screenwriter (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Spanish poet and author, Nobel Prize laureate (d. 1984)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ei<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1898", "text": "<PERSON>, Scottish director and producer (d. 1972)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish director and producer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish director and producer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Latvian-English saxophonist and bandleader (d. 1958)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-English saxophonist and bandleader (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-English saxophonist and bandleader (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German bookbinder and calligrapher (d. 1969)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bookbinder and calligrapher (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bookbinder and calligrapher (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American seismologist and physicist (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American seismologist and physicist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American seismologist and physicist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American baseball player (d. 1948)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ha<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian cardinal (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89mile_L%C3%A9ger\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89mile_L%C3%A9ger\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89mile_L%C3%A9ger"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Greek economist and politician, 177th Prime Minister of Greece (d. 2004)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Xenophon <PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and politician, 177th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eno<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and politician, 177th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>enophon_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1905", "text": "<PERSON>, French director and screenwriter (d. 1934)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Vigo\"><PERSON></a>, French director and screenwriter (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Greek politician, Prime Minister of Greece (d. 1968)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1909", "text": "<PERSON>, German actress (d. 2002)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Japanese screenwriter and producer (d. 1997)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese screenwriter and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese screenwriter and producer (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, German soldier and politician (d. 1986)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian-American author (d. 2000)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-American author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-American author (d. 2000)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American novelist and short story writer (d. 1986)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American real estate developer (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate developer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate developer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, American artist, author, and illustrator (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American artist, author, and illustrator (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American artist, author, and illustrator (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English commander, engineer, and pilot (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English commander, engineer, and pilot (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English commander, engineer, and pilot (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian author and playwright (d. 1999)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Morris_West\" title=\"Morris West\"><PERSON></a>, Australian author and playwright (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morris_West\" title=\"Morris West\"><PERSON></a>, Australian author and playwright (d. 1999)", "links": [{"title": "Morris West", "link": "https://wikipedia.org/wiki/Morris_West"}]}, {"year": "1917", "text": "<PERSON>, American baseball player and coach (d. 1992)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sal_<PERSON>e"}]}, {"year": "1917", "text": "<PERSON><PERSON> <PERSON><PERSON>, Chinese-American architect, designed the National Gallery of Art and Bank of China Tower (d. 2019)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"I. M<PERSON> Pei\"><PERSON><PERSON> <PERSON><PERSON></a>, Chinese-American architect, designed the <a href=\"https://wikipedia.org/wiki/National_Gallery_of_Art\" title=\"National Gallery of Art\">National Gallery of Art</a> and <a href=\"https://wikipedia.org/wiki/Bank_of_China_Tower_(Hong_Kong)\" title=\"Bank of China Tower (Hong Kong)\">Bank of China Tower</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"I. M<PERSON> Pei\"><PERSON><PERSON> <PERSON><PERSON></a>, Chinese-American architect, designed the <a href=\"https://wikipedia.org/wiki/National_Gallery_of_Art\" title=\"National Gallery of Art\">National Gallery of Art</a> and <a href=\"https://wikipedia.org/wiki/Bank_of_China_Tower_(Hong_Kong)\" title=\"Bank of China Tower (Hong Kong)\">Bank of China Tower</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "National Gallery of Art", "link": "https://wikipedia.org/wiki/National_Gallery_of_Art"}, {"title": "Bank of China Tower (Hong Kong)", "link": "https://wikipedia.org/wiki/Bank_of_China_Tower_(Hong_Kong)"}]}, {"year": "1917", "text": "<PERSON>, American baseball player and coach (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Virgil Trucks\"><PERSON></a>, American baseball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Virgil Trucks\"><PERSON></a>, American baseball player and coach (d. 2013)", "links": [{"title": "Virgil Trucks", "link": "https://wikipedia.org/wiki/Virgil_Trucks"}]}, {"year": "1918", "text": "<PERSON>-<PERSON><PERSON>, Dutch sprinter and long jumper (d. 2004)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-Ko<PERSON>\"><PERSON></a>, Dutch sprinter and long jumper (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> B<PERSON>-Ko<PERSON>\"><PERSON></a>, Dutch sprinter and long jumper (d. 2004)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>en"}]}, {"year": "1921", "text": "<PERSON>, American clarinet player, saxophonist, and composer (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player, saxophonist, and composer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player, saxophonist, and composer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON> <PERSON><PERSON>, English historian and academic (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian journalist and politician, Governor General of Canada (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_Sauv%C3%A9"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1922", "text": "<PERSON>, South African-Australian ballerina and choreographer (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, South African-Australian ballerina and choreographer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, South African-Australian ballerina and choreographer (d. 2019)", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(dancer)"}]}, {"year": "1924", "text": "<PERSON>, American runner and soldier (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ross\" title=\"Browning Ross\"><PERSON></a>, American runner and soldier (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ross\" title=\"Browning Ross\"><PERSON></a>, American runner and soldier (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Russian mathematician, educator and author (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician, educator and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician, educator and author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American businessman and philanthropist (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian entrepreneur (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian entrepreneur (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian entrepreneur (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, British economist (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British economist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British economist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, German soldier and illustrator (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and illustrator (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and illustrator (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actor (d. 2008)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2008)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1927", "text": "<PERSON>, British scientist (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American basketball player and coach (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American baseball player (d. 1993)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American author and educator (d. 2002)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Belgian runner and sportscaster", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian director, producer, and screenwriter (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American talent agent and producer (d. 2008)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent agent and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent agent and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Australian politician, 41st Premier of Victoria (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(junior)\" class=\"mw-redirect\" title=\"<PERSON> (junior)\"><PERSON>.</a>, Australian politician, 41st <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(junior)\" class=\"mw-redirect\" title=\"<PERSON> (junior)\"><PERSON> Jr.</a>, Australian politician, 41st <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 2019)", "links": [{"title": "<PERSON> (junior)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(junior)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Indian-Pakistani theologian, philosopher, and scholar (d. 2010)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Pakistani theologian, philosopher, and scholar (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Pakistani theologian, philosopher, and scholar (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English long jumper", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer and guitarist (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rone\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rone\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_D%27Rone"}]}, {"year": "1932", "text": "<PERSON>, French accordion player and composer (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French accordion player and composer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French accordion player and composer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English-Canadian biochemist and geneticist, Nobel Prize laureate (d. 2000)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English-Canadian biochemist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English-Canadian biochemist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2000)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>(chemist)"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1933", "text": "<PERSON>, American actress, singer, and producer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American sports announcer (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, American sports announcer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, American sports announcer (d. 2024)", "links": [{"title": "<PERSON> (sportscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American general (d. 2005)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_R%C3%ADos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American general (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_R%C3%ADos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American general (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fili<PERSON>_<PERSON>_R%C3%ADos"}]}, {"year": "1933", "text": "<PERSON><PERSON>, German-American physicist and academic, Nobel Prize laureate (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, French racing driver and motorcycle racer (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing driver and motorcycle racer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing driver and motorcycle racer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and actor (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American doo-wop/R&B singer-songwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Zodiacs\" title=\"<PERSON> and the Zodiacs\"><PERSON></a>, American doo-wop/R&amp;B singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Zodiacs\" title=\"<PERSON> and the Zodiacs\"><PERSON></a>, American doo-wop/R&amp;B singer-songwriter", "links": [{"title": "<PERSON> and the Zodiacs", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Zod<PERSON>s"}]}, {"year": "1940", "text": "<PERSON>, Italian singer-songwriter and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English rugby league player (d. 2018)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, French model and actress (d. 2019)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model and actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model and actress (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian journalist, author, and critic (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian journalist, author, and critic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian journalist, author, and critic (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian lawyer and politician, Leader of the Government in the Senate", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Representative_of_the_Government_in_the_Senate\" title=\"Representative of the Government in the Senate\">Leader of the Government in the Senate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Representative_of_the_Government_in_the_Senate\" title=\"Representative of the Government in the Senate\">Leader of the Government in the Senate</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Representative of the Government in the Senate", "link": "https://wikipedia.org/wiki/Representative_of_the_Government_in_the_Senate"}]}, {"year": "1942", "text": "<PERSON>, Canadian diplomat, Canadian Ambassador to the United States", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Canadian_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of Canadian ambassadors to the United States\">Canadian Ambassador to the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Canadian_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of Canadian ambassadors to the United States\">Canadian Ambassador to the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Canadian ambassadors to the United States", "link": "https://wikipedia.org/wiki/List_of_Canadian_ambassadors_to_the_United_States"}]}, {"year": "1942", "text": "<PERSON>, American singer and actor (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish sociologist, political scientist, and academic (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish sociologist, political scientist, and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish sociologist, political scientist, and academic (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter, keyboard player, and producer (d. 2023)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Swiss architect and academic, designed the Therme Vals", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Therme_Vals\" class=\"mw-redirect\" title=\"Therme Vals\">Therme <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Therme_Vals\" class=\"mw-redirect\" title=\"Therme Vals\">Therm<PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>rm<PERSON>", "link": "https://wikipedia.org/wiki/Therme_Vals"}]}, {"year": "1944", "text": "<PERSON>, English conductor (d. 2007)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor (d. 2007)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)"}]}, {"year": "1945", "text": "<PERSON>, English director and producer (d. 2016)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer (d. 2016)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_(director)"}]}, {"year": "1945", "text": "<PERSON>, Australian racing driver", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian academic and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian academic and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English international footballer (d. 2010)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American poet and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Chilean footballer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Argentinian footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American wrestler (d. 1995)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Japanese singer (d. 2023)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Estonian chess player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>na\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>na"}]}, {"year": "1954", "text": "<PERSON>, English mountaineer and explorer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and explorer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, German politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American actress and photographer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and photographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>-<PERSON>, 7th Marquess of Bute, Scottish racing driver (d. 2021)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Marquess_of_Bute\" title=\"<PERSON>, 7th Marquess of Bute\"><PERSON>, 7th Marquess of Bute</a>, Scottish racing driver (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Marquess_of_Bute\" title=\"<PERSON>, 7th Marquess of Bute\"><PERSON>, 7th Marquess of Bute</a>, Scottish racing driver (d. 2021)", "links": [{"title": "<PERSON>, 7th Marquess of Bute", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>,_7th_Marquess_of_Bute"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, American actor, director, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ito"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Greek footballer, coach, and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1958)\" title=\"<PERSON><PERSON> (footballer, born 1958)\"><PERSON><PERSON></a>, Greek footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ikos_(footballer,_born_1958)\" title=\"<PERSON><PERSON> (footballer, born 1958)\"><PERSON><PERSON></a>, Greek footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON> (footballer, born 1958)", "link": "https://wikipedia.org/wiki/Georgios_Kostikos_(footballer,_born_1958)"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Puerto Rican politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON> <PERSON><PERSON>, American writer and academic (d. 2020)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"H<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American writer and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"H. G. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American writer and academic (d. 2020)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English drummer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON>_<PERSON>_drummer)\" title=\"<PERSON> (<PERSON>ran Duran drummer)\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON>_<PERSON>_drummer)\" title=\"<PERSON> (<PERSON><PERSON> Du<PERSON> drummer)\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON> (<PERSON><PERSON> drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON>_<PERSON><PERSON>_drummer)"}]}, {"year": "1961", "text": "<PERSON>, Chinese-American actress, director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American actress, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American actress, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American artist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mars\"><PERSON></a>, American artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress and comedian", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Chinese-Singaporean martial artist, actor, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jet Li\"><PERSON></a>, Chinese-Singaporean martial artist, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jet Li\"><PERSON></a>, Chinese-Singaporean martial artist, actor, and producer", "links": [{"title": "Jet Li", "link": "https://wikipedia.org/wiki/Jet_Li"}]}, {"year": "1963", "text": "<PERSON>, Australian-American football player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, German hurdler", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Corne<PERSON>_<PERSON>rich\" title=\"Cornelia Ullrich\"><PERSON><PERSON><PERSON></a>, German hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Corne<PERSON>_<PERSON>rich\" title=\"Cornelia Ullrich\"><PERSON><PERSON><PERSON></a>, German hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Co<PERSON><PERSON>_<PERSON>rich"}]}, {"year": "1963", "text": "<PERSON>, Canadian basketball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor and comedian", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American professional wrestler, actor, businessman and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American professional wrestler, actor, businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American professional wrestler, actor, businessman and politician", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1967", "text": "<PERSON>, English actress and singer-songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Estonian sailor and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T%C3%B5niste\" title=\"<PERSON><PERSON> T<PERSON>nist<PERSON>\"><PERSON><PERSON></a>, Estonian sailor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T%C3%B5niste\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian sailor and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toomas_T%C3%B5niste"}]}, {"year": "1970", "text": "<PERSON>, English footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Slovene-American model; 47th First Lady of the United States", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene-American model; 47th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene-American model; 47th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Trump"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1970", "text": "<PERSON>, American ethnographer and academic", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American ethnographer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American ethnographer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" <PERSON>, American singer-songwriter, dancer, and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" <PERSON></a>, American singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" <PERSON></a>, American singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Japanese comedian and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/1971\" title=\"1971\">1971</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(comedian)\" title=\"<PERSON><PERSON> (comedian)\"><PERSON><PERSON></a>, Japanese comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1971\" title=\"1971\">1971</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(comedian)\" title=\"<PERSON><PERSON> (comedian)\"><PERSON><PERSON></a>, Japanese comedian and actor", "links": [{"title": "1971", "link": "https://wikipedia.org/wiki/1971"}, {"title": "<PERSON><PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(comedian)"}]}, {"year": "1971", "text": "<PERSON>, American bass player, songwriter, and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian racing driver", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1972)\" title=\"<PERSON><PERSON> (footballer, born 1972)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1972)\" title=\"<PERSON><PERSON> (footballer, born 1972)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1972)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1972)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American football player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>rone_Means\" title=\"Natrone Means\"><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rone_Means\" title=\"Natrone Means\"><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON>s", "link": "https://wikipedia.org/wiki/<PERSON>rone_Means"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Israeli footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avi_<PERSON><PERSON>ni"}]}, {"year": "1973", "text": "<PERSON>, American baseball player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French-American director and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French-American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French-American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(English_footballer)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Spanish footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_Garc%C3%<PERSON><PERSON>_(footballer,_born_1973)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1973)\"><PERSON><PERSON><PERSON></a>, Spanish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_Garc%C3%<PERSON><PERSON>_(footballer,_born_1973)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1973)\"><PERSON><PERSON><PERSON></a>, Spanish footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1973)", "link": "https://wikipedia.org/wiki/%C3%93scar_Garc%C3%<PERSON><PERSON>_(footballer,_born_1973)"}]}, {"year": "1975", "text": "<PERSON>, American musician and songwriter (d. 2021)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Indian social worker and activist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian social worker and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian social worker and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/1976\" title=\"1976\">1976</a> - <a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Vara%C4%8Fa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1976\" title=\"1976\">1976</a> - <a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Vara%C4%8Fa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "1976", "link": "https://wikipedia.org/wiki/1976"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A1clav_Vara%C4%8Fa"}]}, {"year": "1977", "text": "<PERSON>, Italian astronaut", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dome\" title=\"<PERSON>suke Fukudome\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dome\" title=\"<PERSON><PERSON> Fukudome\"><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dome"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American journalist and author", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>ox<PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ox<PERSON>_<PERSON>i\" title=\"Rox<PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rox<PERSON>_<PERSON>beri"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Canadian actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Danish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Panamanian-American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Panamanian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Panamanian-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"Marlon King\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"Marlon King\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_King"}]}, {"year": "1980", "text": "<PERSON>, Polish actress and journalist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish actress and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish actress and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actor and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ta<PERSON>\"><PERSON><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ta<PERSON>\"><PERSON><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tum"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Dutch pop and jazz singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Caro_Emerald\" title=\"Caro Emerald\"><PERSON><PERSON></a>, Dutch pop and jazz singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caro_Emerald\" title=\"Caro Emerald\"><PERSON><PERSON></a>, Dutch pop and jazz singer", "links": [{"title": "Caro Emerald", "link": "https://wikipedia.org/wiki/Caro_Emerald"}]}, {"year": "1981", "text": "<PERSON><PERSON> <PERSON><PERSON>, English rapper and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Ms._Dynamite\" title=\"Ms. Dynamite\">Ms. <PERSON><PERSON></a>, English rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ms._Dynamite\" title=\"Ms. Dynamite\">Ms. Dynamite</a>, English rapper and producer", "links": [{"title": "Ms. D<PERSON>", "link": "https://wikipedia.org/wiki/Ms._Dynamite"}]}, {"year": "1981", "text": "<PERSON>, German skier (d. 2000)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German skier (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German skier (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>-<PERSON>, Jamaican sprinter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican sprinter", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Argentinian racing driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_L%C3%B3pez\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_L%C3%B3pez\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_L%C3%B3pez"}]}, {"year": "1983", "text": "<PERSON>, American soldier", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Russian runner", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Spanish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_And%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, South Korean singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Mexican footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Northern Irish rugby player (d. 2012)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Northern Irish rugby player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Northern Irish rugby player (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian cricketer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/1991\" title=\"1991\">1991</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1991\" title=\"1991\">1991</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "1991", "link": "https://wikipedia.org/wiki/1991"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, New Zealand rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Aaron_Judge\" title=\"Aaron Judge\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aaron_Judge\" title=\"Aaron Judge\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> Wright", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Russian racing driver", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek international footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Odysseas_Vlachodimos\" title=\"Odysseas Vlachodimos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O<PERSON>sseas_Vlachodimos\" title=\"Odysseas Vlachodimos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek international footballer", "links": [{"title": "Odysseas Vlachodimos", "link": "https://wikipedia.org/wiki/Odysseas_Vlachodimos"}]}, {"year": "1996", "text": "<PERSON>, American footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Jordan_Pefok\" title=\"<PERSON> Pefok\"><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Pefok\" title=\"<PERSON> Pefok\"><PERSON></a>, American footballer", "links": [{"title": "Jordan Pefok", "link": "https://wikipedia.org/wiki/Jordan_Pefok"}]}, {"year": "1997", "text": "<PERSON>, Indonesian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Thiago_Almada\" title=\"Thiago Almada\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thiago_Almada\" title=\"Thiago Almada\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thiago_Almada"}]}], "Deaths": [{"year": "499", "text": "Emperor <PERSON><PERSON> of Northern Wei (b. 467)", "html": "499 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON> of Northern Wei\">Emperor <PERSON><PERSON> of Northern Wei</a> (b. 467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON> of Northern Wei\">Emperor <PERSON><PERSON> of Northern Wei</a> (b. 467)", "links": [{"title": "Emperor <PERSON><PERSON> of Northern Wei", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei"}]}, {"year": "645", "text": "<PERSON><PERSON><PERSON>, Frankish monk and saint (b. 560)", "html": "645 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish monk and saint (b. 560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish monk and saint (b. 560)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Richarius"}]}, {"year": "680", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> caliph (b. 602)", "html": "680 - <a href=\"https://wikipedia.org/wiki/Mu%27awiya_I\" title=\"Mu<PERSON><PERSON><PERSON>ya <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> I</a>, <PERSON><PERSON><PERSON><PERSON> caliph (b. 602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mu%27awiya_I\" title=\"Mu'<PERSON><PERSON>ya <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> I</a>, <PERSON><PERSON><PERSON><PERSON> caliph (b. 602)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> I", "link": "https://wikipedia.org/wiki/Mu%27awiya_I"}]}, {"year": "757", "text": "<PERSON> (b. 715)", "html": "757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Stephen II\"><PERSON> <PERSON> II</a> (b. 715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Stephen II\"><PERSON> II</a> (b. 715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "893", "text": "<PERSON>, general of the Tang Dynasty", "html": "893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jing<PERSON>\"><PERSON></a>, general of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang Dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang Dynasty</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "962", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Metz", "html": "962 - <a href=\"https://wikipedia.org/wiki/Adalbero_I_of_Metz\" title=\"Adalbero I of Metz\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Metz\" title=\"Roman Catholic Diocese of Metz\">Metz</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adalbero_I_of_Metz\" title=\"Adalbero I of Metz\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Metz\" title=\"Roman Catholic Diocese of Metz\">Metz</a>", "links": [{"title": "Adalbero I of Metz", "link": "https://wikipedia.org/wiki/Adalbero_I_of_Metz"}, {"title": "Roman Catholic Diocese of Metz", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Metz"}]}, {"year": "1192", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1127)", "html": "1192 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1127)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Shi<PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1127)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1366", "text": "<PERSON>, Archbishop of Canterbury", "html": "1366 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1392", "text": "<PERSON><PERSON><PERSON>, Korean civil minister, diplomat and scholar (b. 1338)", "html": "1392 - <a href=\"https://wikipedia.org/wiki/Ch%C5%8Fng_Mong-ju\" title=\"Chŏng Mong-ju\"><PERSON><PERSON><PERSON>-ju</a>, Korean civil minister, diplomat and scholar (b. 1338)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ch%C5%8Fng_Mong-ju\" title=\"Chŏng Mong-ju\"><PERSON><PERSON><PERSON>-ju</a>, Korean civil minister, diplomat and scholar (b. 1338)", "links": [{"title": "<PERSON><PERSON><PERSON>u", "link": "https://wikipedia.org/wiki/Ch%C5%8Fng_Mong-ju"}]}, {"year": "1444", "text": "<PERSON>, Flemish painter (b. 1378)", "html": "1444 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (b. 1378)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (b. 1378)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1478", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian ruler (b. 1453)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian ruler (b. 1453)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian ruler (b. 1453)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>"}]}, {"year": "1489", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1465)", "html": "1489 - <a href=\"https://wikipedia.org/wiki/Ashika<PERSON>_Yoshihisa\" title=\"Ashikaga Yoshihisa\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1465)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ash<PERSON><PERSON>_Yoshi<PERSON>a\" title=\"Ashika<PERSON> Yoshihisa\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1465)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1558", "text": "<PERSON>, French physician (b. 1497)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician (b. 1497)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, Swedish statesman and military man (b. 1622)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish statesman and military man (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish statesman and military man (b. 1622)", "links": [{"title": "<PERSON>ardie", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, 1st Baron <PERSON>, English jurist and politician, Lord High Chancellor of Great Britain (b. 1651)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English jurist and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a> (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English jurist and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a> (b. 1651)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1784", "text": "<PERSON><PERSON>, Irish nun and educator, founded the Presentation Sisters (b. 1718)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish nun and educator, founded the <a href=\"https://wikipedia.org/wiki/Presentation_Sisters\" title=\"Presentation Sisters\">Presentation Sisters</a> (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish nun and educator, founded the <a href=\"https://wikipedia.org/wiki/Presentation_Sisters\" title=\"Presentation Sisters\">Presentation Sisters</a> (b. 1718)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gle"}, {"title": "Presentation Sisters", "link": "https://wikipedia.org/wiki/Presentation_Sisters"}]}, {"year": "1789", "text": "<PERSON><PERSON>, Russian general (b. 1721)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1721)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, German music publisher (b. 1748)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German music publisher (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German music publisher (b. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American actor, assassin of <PERSON> (b. 1838)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham <PERSON>\"><PERSON></a> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham <PERSON>\"><PERSON></a> (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON> Tann-Rathsamhausen, German general (b. 1815)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_und_zu_der_Tann-Rathsamhausen\" title=\"<PERSON> von und zu der Tann-Rathsamhausen\"><PERSON> und zu der Tann-Rathsamhausen</a>, German general (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_und_zu_der_Tann-Rathsamhausen\" title=\"<PERSON> von und zu der Tann-Rathsamhausen\"><PERSON> von und zu der Tann-Rathsamhausen</a>, German general (b. 1815)", "links": [{"title": "<PERSON> zu der Tann-Rathsamhausen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_und_<PERSON>_<PERSON>_<PERSON>-<PERSON>hsam<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Estonian-English author and poet (b. 1860)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-English author and poet (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-English author and poet (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian-French author, poet, and playwright, Nobel Prize laureate (b. 1832)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B8rnstjerne_Bj%C3%B8rnson\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Bjørnson\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian-French author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B8rnstjerne_Bj%C3%B8rnson\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Bjørnson\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian-French author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1832)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B8rnstjerne_Bj%C3%B8rnson"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1915", "text": "<PERSON>, American actor (b. 1863)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American diarist (b. 1858)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diarist (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diarist (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>l"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Portuguese poet and writer (b. 1890)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_de_S%C3%A1-Carneiro\" title=\"<PERSON><PERSON><PERSON> de <PERSON>-Carneiro\"><PERSON><PERSON><PERSON></a>, Portuguese poet and writer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1rio_de_S%C3%A1-Carneiro\" title=\"<PERSON><PERSON><PERSON> de Sá-Carneiro\"><PERSON><PERSON><PERSON></a>, Portuguese poet and writer (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>Carneiro", "link": "https://wikipedia.org/wiki/M%C3%A1rio_de_S%C3%A1-Carneiro"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian mathematician and theorist (b. 1887)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian mathematician and theorist (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian mathematician and theorist (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English cricketer (b. 1868)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1868)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Latvian politician, former Prime Minister of Latvia (b. 1876)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Arturs_Alberings\" title=\"Arturs Alberings\"><PERSON><PERSON></a>, Latvian politician, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arturs_Alberings\" title=\"Arturs Alberings\"><PERSON><PERSON></a>, Latvian politician, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a> (b. 1876)", "links": [{"title": "Arturs <PERSON>", "link": "https://wikipedia.org/wiki/Arturs_Alberings"}, {"title": "Prime Minister of Latvia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Latvia"}]}, {"year": "1934", "text": "<PERSON>, Russian poet and novelist (b. 1899)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and novelist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and novelist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, German chemist and engineer, Nobel Prize laureate (b. 1874)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1944", "text": "<PERSON><PERSON>, French footballer, shot putter, and discus thrower (b. 1893)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer, shot putter, and discus thrower (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer, shot putter, and discus thrower (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Morris"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German physician (b. 1909)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German-Ukrainian general and politician, Hetman of Ukraine (b. 1871)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Ukrainian general and politician, <a href=\"https://wikipedia.org/wiki/Hetman_of_Ukraine\" class=\"mw-redirect\" title=\"Hetman of Ukraine\">Hetman of Ukraine</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Ukrainian general and politician, <a href=\"https://wikipedia.org/wiki/Hetman_of_Ukraine\" class=\"mw-redirect\" title=\"Hetman of Ukraine\">Hetman of Ukraine</a> (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pavlo_Skoropadskyi"}, {"title": "<PERSON><PERSON> of Ukraine", "link": "https://wikipedia.org/wiki/Hetman_of_Ukraine"}]}, {"year": "1946", "text": "<PERSON>, American miner, explorer, and park ranger (b. 1882)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American miner, explorer, and park ranger (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American miner, explorer, and park ranger (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American lawyer, judge, and politician (b. 1881)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, German physicist and academic (b. 1868)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor (b. 1890)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1890)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Japanese martial artist, founded Shot<PERSON>n (b. 1868)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Funakoshi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Shotokan\" title=\"Shotokan\">Shotokan</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Fun<PERSON>oshi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Shotokan\" title=\"Shotokan\">Shotokan</a> (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Shotokan", "link": "https://wikipedia.org/wiki/Shotokan"}]}, {"year": "1964", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian poet and author (b. 1882)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian poet and author (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian poet and author (b. 1882)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German illustrator and photographer (b. 1891)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German illustrator and photographer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German illustrator and photographer (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese martial artist, founded aikido (b. 1883)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Morihei_Ueshiba\" title=\"<PERSON><PERSON><PERSON><PERSON> Ueshiba\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Aikido\" title=\"Aikido\">aikido</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>i_Ueshiba\" title=\"<PERSON><PERSON><PERSON><PERSON> Ueshiba\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Aikido\" title=\"Aikido\">aikido</a> (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>i_Ueshiba"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>do"}]}, {"year": "1970", "text": "<PERSON>, Swedish minister and author (b. 1886)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lutheran_minister)\" title=\"<PERSON> (Lutheran minister)\"><PERSON></a>, Swedish minister and author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lutheran_minister)\" title=\"<PERSON> (Lutheran minister)\"><PERSON></a>, Swedish minister and author (b. 1886)", "links": [{"title": "<PERSON> (Lutheran minister)", "link": "https://wikipedia.org/wiki/<PERSON>_(Lutheran_minister)"}]}, {"year": "1970", "text": "<PERSON>, American actress, striptease dancer, and writer (b. 1911)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Gypsy_<PERSON>_<PERSON>\" title=\"Gypsy Rose Lee\">Gypsy <PERSON></a>, American actress, striptease dancer, and writer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gypsy_<PERSON>_<PERSON>\" title=\"Gypsy Rose Lee\"><PERSON></a>, American actress, striptease dancer, and writer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress and philanthropist (b. 1902)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and philanthropist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and philanthropist (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American bullfighter (b. 1903)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bullfighter)\" title=\"<PERSON> (bullfighter)\"><PERSON></a>, American bullfighter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bullfighter)\" title=\"<PERSON> (bullfighter)\"><PERSON></a>, American bullfighter (b. 1903)", "links": [{"title": "<PERSON> (bullfighter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bullfighter)"}]}, {"year": "1976", "text": "<PERSON>, South African-English actor (b. 1913)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American author and illustrator (b. 1897)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>ry\"><PERSON></a>, American author and illustrator (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Armstrong_<PERSON>perry"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Australian-born British actress, comedian and singer (b. 1893)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-born British actress, comedian and singer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-born British actress, comedian and singer (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor (b. 1909)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1909)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1984", "text": "<PERSON>, American pianist, composer, and bandleader (b. 1904)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON>\" title=\"Count <PERSON>\">Count <PERSON></a>, American pianist, composer, and bandleader (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON>\" title=\"Count <PERSON>\">Count <PERSON></a>, American pianist, composer, and bandleader (b. 1904)", "links": [{"title": "Count <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1911)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress (b. 1898)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Love\"><PERSON><PERSON></a>, American actress (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Love\"><PERSON><PERSON></a>, American actress (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Bulgarian painter (b. 1899)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian painter (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian painter (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Indian composer and conductor (b. 1922)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indian composer and conductor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indian composer and conductor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English lawyer and politician, Shadow Leader of the House of Commons (b. 1923)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons\" title=\"Shadow Leader of the House of Commons\">Shadow Leader of the House of Commons</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons\" title=\"Shadow Leader of the House of Commons\">Shadow Leader of the House of Commons</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Leader of the House of Commons", "link": "https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American model, actress, comedian, and producer (b. 1911)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ball\" title=\"Lucille Ball\"><PERSON><PERSON></a>, American model, actress, comedian, and producer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ball\" title=\"Lucille Ball\"><PERSON><PERSON></a>, American model, actress, comedian, and producer (b. 1911)", "links": [{"title": "<PERSON><PERSON> Ball", "link": "https://wikipedia.org/wiki/<PERSON>ille_Ball"}]}, {"year": "1991", "text": "<PERSON>, French-American composer and conductor (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American composer and conductor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American composer and conductor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American composer and conductor (b. 1910)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON> <PERSON><PERSON>, Jr., American novelist and historian, (b. 1901)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Jr.\"><PERSON><PERSON> <PERSON><PERSON>, Jr.</a>, American novelist and historian, (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Jr.\"><PERSON><PERSON> <PERSON><PERSON>, Jr.</a>, American novelist and historian, (b. 1901)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1991", "text": "<PERSON>, Canadian lawyer and politician, 26th Premier of New Brunswick (b. 1931)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New Brunswick", "link": "https://wikipedia.org/wiki/Premier_of_New_Brunswick"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese martial artist, founded K<PERSON><PERSON><PERSON> kaikan (b. 1923)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Kyokushin\" title=\"Kyokus<PERSON>\"><PERSON><PERSON><PERSON><PERSON> kaikan</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Kyokushin\" title=\"Kyokus<PERSON>\">K<PERSON><PERSON><PERSON> kaikan</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Kyokushin", "link": "https://wikipedia.org/wiki/Kyokushin"}]}, {"year": "1996", "text": "<PERSON>, American screenwriter and producer (b. 1918)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Silliphant\" title=\"<PERSON> Silliphant\"><PERSON></a>, American screenwriter and producer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Silliphant\" title=\"<PERSON> Silliphant\"><PERSON></a>, American screenwriter and producer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lliphant"}]}, {"year": "1999", "text": "<PERSON>, English singer-songwriter, guitarist, and producer (b. 1957)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English journalist and television personality (b. 1961)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television personality (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television personality (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Jamaican-Canadian academic and politician (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Jamaican-Canadian academic and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Jamaican-Canadian academic and politician (b. 1930)", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)"}]}, {"year": "2003", "text": "<PERSON>, South Korean poet and author (b. 1984)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean poet and author (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean poet and author (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-seok"}]}, {"year": "2003", "text": "<PERSON>, Irish environmentalist, co-founded the World Wide Fund for Nature (b. 1904)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish environmentalist, co-founded the <a href=\"https://wikipedia.org/wiki/World_Wide_Fund_for_Nature\" title=\"World Wide Fund for Nature\">World Wide Fund for Nature</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish environmentalist, co-founded the <a href=\"https://wikipedia.org/wiki/World_Wide_Fund_for_Nature\" title=\"World Wide Fund for Nature\">World Wide Fund for Nature</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World Wide Fund for Nature", "link": "https://wikipedia.org/wiki/World_Wide_Fund_for_Nature"}]}, {"year": "2004", "text": "<PERSON>, Jr., American author, poet, and screenwriter (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author, poet, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author, poet, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2005", "text": "<PERSON>, American actor (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Prime Minister of the Central African Republic (b. 1925)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prime Minister of the Central African Republic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prime Minister of the Central African Republic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Dom<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Austrian-Swiss actress (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss actress (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Paraguayan journalist, author, and academic (b. 1917)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Augusto Roa Basto<PERSON>\"><PERSON><PERSON></a>, Paraguayan journalist, author, and academic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"August<PERSON> R<PERSON> Basto<PERSON>\"><PERSON><PERSON></a>, Paraguayan journalist, author, and academic (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American businessman, created the MPAA film rating system (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, created the <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system\" class=\"mw-redirect\" title=\"Motion Picture Association of America film rating system\">MPAA film rating system</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, created the <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system\" class=\"mw-redirect\" title=\"Motion Picture Association of America film rating system\">MPAA film rating system</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Motion Picture Association of America film rating system", "link": "https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian footballer (b. 1938)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_Orb%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_Orb%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81rp%C3%A1d_Orb%C3%A1n"}]}, {"year": "2009", "text": "<PERSON>, Austrian-American paranormal investigator and author (b. 1920)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American paranormal investigator and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American paranormal investigator and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Egyptian graphic designer and academic (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Maria<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Egyptian graphic designer and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Egyptian graphic designer and academic (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Swiss engineer and businessman (b. 1942)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss engineer and businessman (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss engineer and businessman (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_<PERSON><PERSON>ber"}]}, {"year": "2011", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1950)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Snow\"><PERSON></a>, American singer-songwriter and guitarist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Snow\"><PERSON></a>, American singer-songwriter and guitarist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English boxer and trainer (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English boxer and trainer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English boxer and trainer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress and educator (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and educator (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and educator (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian men's rights advocate (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian men's rights advocate (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian men's rights advocate (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American physicist and academic (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Jr., American historian and author (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American historian and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American historian and author (b. 1927)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "2014", "text": "<PERSON> <PERSON><PERSON><PERSON>, American electronic musician, producer and DJ  (b. 1979)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/DJ_Rashad\" title=\"DJ Rashad\">DJ <PERSON><PERSON></a>, American electronic musician, producer and DJ (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_Rashad\" title=\"DJ Rashad\">DJ <PERSON><PERSON></a>, American electronic musician, producer and DJ (b. 1979)", "links": [{"title": "DJ <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actress (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Chinese human rights activist (b. 1937)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese human rights activist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese human rights activist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American filmmaker, producer and screenwriter (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker, producer and screenwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker, producer and screenwriter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, German composer and musician (b. 1947)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and musician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and musician (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American politician, 24th Governor of New Mexico (b. 1934)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Mexico", "link": "https://wikipedia.org/wiki/Governor_of_New_Mexico"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Singaporean drug trafficker (b. 1977)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Tangaraju_Suppiah\" class=\"mw-redirect\" title=\"Tangaraju Suppiah\"><PERSON><PERSON><PERSON></a>, Singaporean drug trafficker (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tangaraju_Suppiah\" class=\"mw-redirect\" title=\"Tangaraju Suppiah\"><PERSON><PERSON><PERSON></a>, Singaporean drug trafficker (b. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tangaraju_Suppiah"}]}]}}