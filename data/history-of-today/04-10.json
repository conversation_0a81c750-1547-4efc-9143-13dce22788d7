{"date": "April 10", "url": "https://wikipedia.org/wiki/April_10", "data": {"Events": [{"year": "428", "text": "<PERSON><PERSON><PERSON><PERSON> becomes the Patriarch of Constantinople.", "html": "428 - <a href=\"https://wikipedia.org/wiki/Nestorius\" title=\"Nestorius\"><PERSON><PERSON><PERSON><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">Patriarch of Constantinople</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nest<PERSON>us\" title=\"Nest<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">Patriarch of Constantinople</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nestorius"}, {"title": "Ecumenical Patriarch of Constantinople", "link": "https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople"}]}, {"year": "837", "text": "<PERSON>y's Comet makes its closest approach to Earth at a distance equal to 0.0342 AU (5.1 million kilometres/3.2 million miles).", "html": "837 - <a href=\"https://wikipedia.org/wiki/Halley%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a> makes its closest approach to Earth at a distance equal to 0.0342 <a href=\"https://wikipedia.org/wiki/Astronomical_unit\" title=\"Astronomical unit\">AU</a> (5.1 million kilometres/3.2 million miles).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halley%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a> makes its closest approach to Earth at a distance equal to 0.0342 <a href=\"https://wikipedia.org/wiki/Astronomical_unit\" title=\"Astronomical unit\">AU</a> (5.1 million kilometres/3.2 million miles).", "links": [{"title": "Halley's Comet", "link": "https://wikipedia.org/wiki/Halley%27s_Comet"}, {"title": "Astronomical unit", "link": "https://wikipedia.org/wiki/Astronomical_unit"}]}, {"year": "1407", "text": "<PERSON><PERSON>, 5th Karmapa Lama visits the Ming dynasty capital at Nanjing and is awarded the title \"Great Treasure Prince of Dharma\".", "html": "1407 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_5th_<PERSON><PERSON><PERSON>_Lama\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, 5th Karmapa Lama\"><PERSON><PERSON>, 5th Karmapa Lama</a> visits the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> capital at <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a> and is awarded the title \"Great Treasure Prince of Dharma\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_5th_<PERSON><PERSON><PERSON>_Lama\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, 5th Karmapa Lama\"><PERSON><PERSON>, 5th Karmapa Lama</a> visits the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> capital at <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a> and is awarded the title \"Great Treasure Prince of Dharma\".", "links": [{"title": "<PERSON><PERSON>, 5th Karmapa Lama", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_5th_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}]}, {"year": "1500", "text": "<PERSON><PERSON><PERSON><PERSON> is captured by Swiss troops at Novara and is handed over to the French.", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is captured by Swiss troops at <a href=\"https://wikipedia.org/wiki/Novara\" title=\"Novara\">Novara</a> and is handed over to the French.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is captured by Swiss troops at <a href=\"https://wikipedia.org/wiki/Novara\" title=\"Novara\">Novara</a> and is handed over to the French.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "Novara", "link": "https://wikipedia.org/wiki/Novara"}]}, {"year": "1545", "text": "The settlement of Villa Imperial de Carlos V (now the city of Potosí) in Bolivia is founded after the discovery of huge silver deposits in the area.", "html": "1545 - The settlement of Villa Imperial de Carlos V (now the city of <a href=\"https://wikipedia.org/wiki/Potos%C3%AD\" title=\"Potosí\">Potosí</a>) in <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a> is founded after the discovery of <a href=\"https://wikipedia.org/wiki/Cerro_Rico\" title=\"Cerro Rico\">huge silver deposits</a> in the area.", "no_year_html": "The settlement of Villa Imperial de Carlos V (now the city of <a href=\"https://wikipedia.org/wiki/Potos%C3%AD\" title=\"Potosí\">Potosí</a>) in <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a> is founded after the discovery of <a href=\"https://wikipedia.org/wiki/Cerro_Rico\" title=\"Cerro Rico\">huge silver deposits</a> in the area.", "links": [{"title": "Potosí", "link": "https://wikipedia.org/wiki/Potos%C3%AD"}, {"title": "Bolivia", "link": "https://wikipedia.org/wiki/Bolivia"}, {"title": "Cerro Rico", "link": "https://wikipedia.org/wiki/Cerro_Rico"}]}, {"year": "1606", "text": "The Virginia Company of London is established by royal charter by <PERSON> England with the purpose of establishing colonial settlements in North America.", "html": "1606 - The <a href=\"https://wikipedia.org/wiki/London_Company\" class=\"mw-redirect\" title=\"London Company\">Virginia Company of London</a> is established by royal charter by <a href=\"https://wikipedia.org/wiki/James_I_of_England\" class=\"mw-redirect\" title=\"James I of England\"><PERSON> of England</a> with the purpose of establishing <a href=\"https://wikipedia.org/wiki/British_colonization_of_the_Americas\" title=\"British colonization of the Americas\">colonial settlements in North America</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/London_Company\" class=\"mw-redirect\" title=\"London Company\">Virginia Company of London</a> is established by royal charter by <a href=\"https://wikipedia.org/wiki/James_I_of_England\" class=\"mw-redirect\" title=\"<PERSON> I of England\"><PERSON> of England</a> with the purpose of establishing <a href=\"https://wikipedia.org/wiki/British_colonization_of_the_Americas\" title=\"British colonization of the Americas\">colonial settlements in North America</a>.", "links": [{"title": "London Company", "link": "https://wikipedia.org/wiki/London_Company"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "British colonization of the Americas", "link": "https://wikipedia.org/wiki/British_colonization_of_the_Americas"}]}, {"year": "1710", "text": "The Statute of Anne, the first law regulating copyright, comes into force in Great Britain.", "html": "1710 - The <a href=\"https://wikipedia.org/wiki/Statute_of_Anne\" title=\"Statute of Anne\">Statute of Anne</a>, the first law regulating <a href=\"https://wikipedia.org/wiki/Copyright\" title=\"Copyright\">copyright</a>, comes into force in <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Statute_of_Anne\" title=\"Statute of Anne\">Statute of Anne</a>, the first law regulating <a href=\"https://wikipedia.org/wiki/Copyright\" title=\"Copyright\">copyright</a>, comes into force in <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a>.", "links": [{"title": "Statute of Anne", "link": "https://wikipedia.org/wiki/Statute_of_Anne"}, {"title": "Copyright", "link": "https://wikipedia.org/wiki/Copyright"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1717", "text": "<PERSON> resigns from the British government, commencing the Whig Split which lasts until 1720.", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns from the British government, commencing the <a href=\"https://wikipedia.org/wiki/Whig_Split\" title=\"Whig Split\">Whig Split</a> which lasts until 1720.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns from the British government, commencing the <a href=\"https://wikipedia.org/wiki/Whig_Split\" title=\"Whig Split\">Whig Split</a> which lasts until 1720.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Whig Split", "link": "https://wikipedia.org/wiki/Whig_Split"}]}, {"year": "1724", "text": "<PERSON> leads the first performance of his cantata <PERSON><PERSON><PERSON><PERSON> euch, <PERSON><PERSON>, BWV 66, his first cantata composed for Easter in Leipzig.", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of his cantata <a href=\"https://wikipedia.org/wiki/Erfre<PERSON>_euch,_i<PERSON>_<PERSON><PERSON>,_BWV_66\" title=\"<PERSON>rfre<PERSON> euch, ihr <PERSON><PERSON>, BWV 66\"><i><PERSON>rfreut euch, ihr <PERSON></i>, BWV 66</a>, his first cantata composed for Easter in Leipzig.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of his cantata <a href=\"https://wikipedia.org/wiki/Erfre<PERSON>_euch,_ihr_<PERSON><PERSON>,_BWV_66\" title=\"<PERSON>rfre<PERSON> euch, ihr <PERSON><PERSON>, BWV 66\"><i><PERSON>rfreut euch, ihr <PERSON></i>, BWV 66</a>, his first cantata composed for Easter in Leipzig.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> euch, <PERSON><PERSON>, BWV 66", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_euch,_<PERSON><PERSON>_<PERSON>,_<PERSON>_66"}]}, {"year": "1741", "text": "War of the Austrian Succession: Prussia gains control of Silesia at the Battle of Mollwitz.", "html": "1741 - <a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a>: Prussia gains control of <a href=\"https://wikipedia.org/wiki/Silesia\" title=\"Silesia\">Silesia</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Mollwitz\" title=\"Battle of Mollwitz\">Battle of Mollwitz</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a>: Prussia gains control of <a href=\"https://wikipedia.org/wiki/Silesia\" title=\"Silesia\">Silesia</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Mollwitz\" title=\"Battle of Mollwitz\">Battle of Mollwitz</a>.", "links": [{"title": "War of the Austrian Succession", "link": "https://wikipedia.org/wiki/War_of_the_Austrian_Succession"}, {"title": "Silesia", "link": "https://wikipedia.org/wiki/Silesia"}, {"title": "Battle of Mollwitz", "link": "https://wikipedia.org/wiki/Battle_of_Mollwitz"}]}, {"year": "1809", "text": "Napoleonic Wars: The War of the Fifth Coalition begins when forces of the Austrian Empire invade Bavaria.", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/War_of_the_Fifth_Coalition\" title=\"War of the Fifth Coalition\">War of the Fifth Coalition</a> begins when forces of the <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian Empire</a> invade <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavaria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/War_of_the_Fifth_Coalition\" title=\"War of the Fifth Coalition\">War of the Fifth Coalition</a> begins when forces of the <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian Empire</a> invade <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavaria</a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "War of the Fifth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fifth_Coalition"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "Bavaria", "link": "https://wikipedia.org/wiki/Bavaria"}]}, {"year": "1815", "text": "The Mount Tambora volcano begins a three-month-long eruption, lasting until July 15. The eruption ultimately kills 71,000 people and affects Earth's climate for the next two years.", "html": "1815 - The <a href=\"https://wikipedia.org/wiki/Mount_Tambora\" title=\"Mount Tambora\">Mount Tambora</a> volcano begins a three-month-long eruption, lasting until <a href=\"https://wikipedia.org/wiki/July_15\" title=\"July 15\">July 15</a>. The eruption ultimately kills 71,000 people and <a href=\"https://wikipedia.org/wiki/Year_Without_a_Summer\" title=\"Year Without a Summer\">affects Earth's climate</a> for the next two years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mount_Tambora\" title=\"Mount Tambora\">Mount Tambora</a> volcano begins a three-month-long eruption, lasting until <a href=\"https://wikipedia.org/wiki/July_15\" title=\"July 15\">July 15</a>. The eruption ultimately kills 71,000 people and <a href=\"https://wikipedia.org/wiki/Year_Without_a_Summer\" title=\"Year Without a Summer\">affects Earth's climate</a> for the next two years.", "links": [{"title": "Mount Tambora", "link": "https://wikipedia.org/wiki/Mount_Tambora"}, {"title": "July 15", "link": "https://wikipedia.org/wiki/July_15"}, {"title": "Year Without a Summer", "link": "https://wikipedia.org/wiki/Year_Without_a_Summer"}]}, {"year": "1816", "text": "The Federal government of the United States approves the creation of the Second Bank of the United States.", "html": "1816 - The <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">Federal government of the United States</a> approves the creation of the <a href=\"https://wikipedia.org/wiki/Second_Bank_of_the_United_States\" title=\"Second Bank of the United States\">Second Bank of the United States</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">Federal government of the United States</a> approves the creation of the <a href=\"https://wikipedia.org/wiki/Second_Bank_of_the_United_States\" title=\"Second Bank of the United States\">Second Bank of the United States</a>.", "links": [{"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}, {"title": "Second Bank of the United States", "link": "https://wikipedia.org/wiki/Second_Bank_of_the_United_States"}]}, {"year": "1821", "text": "Patriarch <PERSON> of Constantinople is hanged by the Ottoman government from the main gate of the Patriarchate and his body is thrown into the Bosphorus.", "html": "1821 - Patriarch <a href=\"https://wikipedia.org/wiki/<PERSON>_V_of_Constantinople\" title=\"<PERSON> V of Constantinople\"><PERSON> of Constantinople</a> is hanged by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman government</a> from the main gate of the <a href=\"https://wikipedia.org/wiki/Patriarchate\" title=\"Patriarchate\">Patriarchate</a> and his body is thrown into the <a href=\"https://wikipedia.org/wiki/Bosphorus\" class=\"mw-redirect\" title=\"Bosphorus\">Bosphorus</a>.", "no_year_html": "Patriarch <a href=\"https://wikipedia.org/wiki/<PERSON>_V_of_Constantinople\" title=\"<PERSON> V of Constantinople\"><PERSON> of Constantinople</a> is hanged by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman government</a> from the main gate of the <a href=\"https://wikipedia.org/wiki/Patriarchate\" title=\"Patriarchate\">Patriarchate</a> and his body is thrown into the <a href=\"https://wikipedia.org/wiki/Bosphorus\" class=\"mw-redirect\" title=\"Bosphorus\">Bosphorus</a>.", "links": [{"title": "<PERSON> V of Constantinople", "link": "https://wikipedia.org/wiki/Gregory_V_of_Constantinople"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Patriarchate", "link": "https://wikipedia.org/wiki/Patriarchate"}, {"title": "Bosphor<PERSON>", "link": "https://wikipedia.org/wiki/Bosphorus"}]}, {"year": "1821", "text": "Greek War of Independence: the island of Psara joins the Greek struggle for independence.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: the island of <a href=\"https://wikipedia.org/wiki/<PERSON>sara\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> joins the Greek struggle for independence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: the island of <a href=\"https://wikipedia.org/wiki/Psara\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> joins the Greek struggle for independence.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sara"}]}, {"year": "1826", "text": "The 10,500 inhabitants of the Greek town of Missolonghi begin leaving the town after a year's siege by Turkish forces. Very few of them survive.", "html": "1826 - The 10,500 inhabitants of the Greek town of <a href=\"https://wikipedia.org/wiki/Missolonghi\" title=\"Missolonghi\">Missolonghi</a> begin leaving the town after <a href=\"https://wikipedia.org/wiki/Third_Siege_of_Missolonghi\" class=\"mw-redirect\" title=\"Third Siege of Missolonghi\">a year's siege</a> by Turkish forces. Very few of them survive.", "no_year_html": "The 10,500 inhabitants of the Greek town of <a href=\"https://wikipedia.org/wiki/Missolonghi\" title=\"Missolonghi\">Missolonghi</a> begin leaving the town after <a href=\"https://wikipedia.org/wiki/Third_Siege_of_Missolonghi\" class=\"mw-redirect\" title=\"Third Siege of Missolonghi\">a year's siege</a> by Turkish forces. Very few of them survive.", "links": [{"title": "Missolonghi", "link": "https://wikipedia.org/wiki/Missolonghi"}, {"title": "Third Siege of Missolonghi", "link": "https://wikipedia.org/wiki/Third_Siege_of_Missolonghi"}]}, {"year": "1858", "text": "After the original Big Ben, a 14.5 tonnes (32,000 lb) bell for the Palace of Westminster, had cracked during testing, it is recast into the current 13.76 tonnes (30,300 lb) bell by Whitechapel Bell Foundry.", "html": "1858 - After the original <a href=\"https://wikipedia.org/wiki/<PERSON>_Ben\" title=\"Big Ben\">Big Ben</a>, a 14.5 tonnes (32,000 lb) bell for the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Palace of Westminster</a>, had cracked during testing, it is recast into the current 13.76 tonnes (30,300 lb) bell by <a href=\"https://wikipedia.org/wiki/Whitechapel_Bell_Foundry\" title=\"Whitechapel Bell Foundry\">Whitechapel Bell Foundry</a>.", "no_year_html": "After the original <a href=\"https://wikipedia.org/wiki/<PERSON>_Ben\" title=\"Big Ben\">Big Ben</a>, a 14.5 tonnes (32,000 lb) bell for the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Palace of Westminster</a>, had cracked during testing, it is recast into the current 13.76 tonnes (30,300 lb) bell by <a href=\"https://wikipedia.org/wiki/Whitechapel_Bell_Foundry\" title=\"Whitechapel Bell Foundry\">Whitechapel Bell Foundry</a>.", "links": [{"title": "Big Ben", "link": "https://wikipedia.org/wiki/Big_Ben"}, {"title": "Palace of Westminster", "link": "https://wikipedia.org/wiki/Palace_of_Westminster"}, {"title": "Whitechapel Bell Foundry", "link": "https://wikipedia.org/wiki/Whitechapel_Bell_Foundry"}]}, {"year": "1864", "text": "Archduke <PERSON> of Habsburg is proclaimed emperor of Mexico during the French intervention in Mexico.", "html": "1864 - Arch<PERSON>ke <a href=\"https://wikipedia.org/wiki/Maximilian_I_of_Mexico\" title=\"Maximilian I of Mexico\"><PERSON> Habsburg</a> is proclaimed emperor of Mexico during the <a href=\"https://wikipedia.org/wiki/Second_French_intervention_in_Mexico\" title=\"Second French intervention in Mexico\">French intervention in Mexico</a>.", "no_year_html": "Archduke <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Mexico\" title=\"Maximilian I of Mexico\"><PERSON></a> is proclaimed emperor of Mexico during the <a href=\"https://wikipedia.org/wiki/Second_French_intervention_in_Mexico\" title=\"Second French intervention in Mexico\">French intervention in Mexico</a>.", "links": [{"title": "<PERSON> I of Mexico", "link": "https://wikipedia.org/wiki/Maximilian_I_of_Mexico"}, {"title": "Second French intervention in Mexico", "link": "https://wikipedia.org/wiki/Second_French_intervention_in_Mexico"}]}, {"year": "1865", "text": "American Civil War: A day after his surrender to Union forces, Confederate General <PERSON> addresses his troops for the last time.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A day after his surrender to Union forces, Confederate General <PERSON> <a href=\"https://wikipedia.org/wiki/Lee%27s_Farewell_Address\" title=\"<PERSON>'s Farewell Address\">addresses</a> his troops for the last time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A day after his surrender to Union forces, Confederate General <PERSON> <a href=\"https://wikipedia.org/wiki/Lee%27s_Farewell_Address\" title=\"<PERSON>'s Farewell Address\">addresses</a> his troops for the last time.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>'s <PERSON><PERSON><PERSON> Address", "link": "https://wikipedia.org/wiki/Lee%27s_<PERSON><PERSON><PERSON>_Address"}]}, {"year": "1866", "text": "The American Society for the Prevention of Cruelty to Animals (ASPCA) is founded in New York City by <PERSON>.", "html": "1866 - The <a href=\"https://wikipedia.org/wiki/American_Society_for_the_Prevention_of_Cruelty_to_Animals\" title=\"American Society for the Prevention of Cruelty to Animals\">American Society for the Prevention of Cruelty to Animals</a> (ASPCA) is founded in New York City by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_Society_for_the_Prevention_of_Cruelty_to_Animals\" title=\"American Society for the Prevention of Cruelty to Animals\">American Society for the Prevention of Cruelty to Animals</a> (ASPCA) is founded in New York City by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Society for the Prevention of Cruelty to Animals", "link": "https://wikipedia.org/wiki/American_Society_for_the_Prevention_of_Cruelty_to_Animals"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "At Arogee in Abyssinia, British and Indian forces defeat an army of Emperor <PERSON><PERSON><PERSON><PERSON>. While 700 Ethiopians are killed and many more injured, only two British/Indian troops die.", "html": "1868 - At Arogee in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Abyssinia</a>, British and Indian forces <a href=\"https://wikipedia.org/wiki/British_Expedition_to_Abyssinia\" class=\"mw-redirect\" title=\"British Expedition to Abyssinia\">defeat</a> an army of Emperor <a href=\"https://wikipedia.org/wiki/Tewodros_II\" title=\"Tewodros II\">Tewodros II</a>. While 700 <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopians</a> are killed and many more injured, only two British/Indian troops die.", "no_year_html": "At Arogee in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Abyssinia</a>, British and Indian forces <a href=\"https://wikipedia.org/wiki/British_Expedition_to_Abyssinia\" class=\"mw-redirect\" title=\"British Expedition to Abyssinia\">defeat</a> an army of Emperor <a href=\"https://wikipedia.org/wiki/Tewodros_II\" title=\"Tewodros II\">Tewodros II</a>. While 700 <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopians</a> are killed and many more injured, only two British/Indian troops die.", "links": [{"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "British Expedition to Abyssinia", "link": "https://wikipedia.org/wiki/British_Expedition_to_Abyssinia"}, {"title": "Tewodros II", "link": "https://wikipedia.org/wiki/Tewodros_II"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1872", "text": "The first Arbor Day is celebrated in Nebraska.", "html": "1872 - The first <a href=\"https://wikipedia.org/wiki/Arbor_Day\" title=\"Arbor Day\">Arbor Day</a> is celebrated in <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Arbor_Day\" title=\"Arbor Day\">Arbor Day</a> is celebrated in <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>.", "links": [{"title": "Arbor Day", "link": "https://wikipedia.org/wiki/Arbor_Day"}, {"title": "Nebraska", "link": "https://wikipedia.org/wiki/Nebraska"}]}, {"year": "1875", "text": "India: <PERSON><PERSON> is founded in Mumbai by Swami <PERSON><PERSON><PERSON> to propagate his goal of social reform.", "html": "1875 - India: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Arya Samaj\"><PERSON><PERSON></a> is founded in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a> by <a href=\"https://wikipedia.org/wiki/Swami_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\">Swami <PERSON></a> to propagate his goal of social reform.", "no_year_html": "India: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Arya Samaj\"><PERSON><PERSON></a> is founded in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a> by <a href=\"https://wikipedia.org/wiki/Swami_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Swami <PERSON>\">Swami <PERSON></a> to propagate his goal of social reform.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}, {"title": "Swami <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "On Easter Sunday, Pope <PERSON> authorizes the establishment of the Catholic University of America.", "html": "1887 - On <a href=\"https://wikipedia.org/wiki/Easter_Sunday\" class=\"mw-redirect\" title=\"Easter Sunday\">Easter Sunday</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> authorizes the establishment of the <a href=\"https://wikipedia.org/wiki/Catholic_University_of_America\" title=\"Catholic University of America\">Catholic University of America</a>.", "no_year_html": "On <a href=\"https://wikipedia.org/wiki/Easter_Sunday\" class=\"mw-redirect\" title=\"Easter Sunday\">Easter Sunday</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> authorizes the establishment of the <a href=\"https://wikipedia.org/wiki/Catholic_University_of_America\" title=\"Catholic University of America\">Catholic University of America</a>.", "links": [{"title": "Easter Sunday", "link": "https://wikipedia.org/wiki/Easter_Sunday"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Catholic University of America", "link": "https://wikipedia.org/wiki/Catholic_University_of_America"}]}, {"year": "1896", "text": "1896 Summer Olympics: The Olympic marathon is run ending with the victory of Greek athlete <PERSON><PERSON><PERSON>.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/1896_Summer_Olympics\" title=\"1896 Summer Olympics\">1896 Summer Olympics</a>: The Olympic <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> is run ending with the victory of Greek athlete <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Louis\" title=\"Spyrid<PERSON> Louis\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1896_Summer_Olympics\" title=\"1896 Summer Olympics\">1896 Summer Olympics</a>: The Olympic <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> is run ending with the victory of Greek athlete <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Louis\" title=\"Spyridon Louis\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "1896 Summer Olympics", "link": "https://wikipedia.org/wiki/1896_Summer_Olympics"}, {"title": "Marathon", "link": "https://wikipedia.org/wiki/Marathon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rid<PERSON>_Louis"}]}, {"year": "1900", "text": "British suffer a sharp defeat by the Boers south of Brandfort. 600 British troops are killed and wounded and 800 taken prisoner.", "html": "1900 - British suffer a sharp defeat by the Boers south of <a href=\"https://wikipedia.org/wiki/Brandfort\" title=\"Brandfort\">Brandfort</a>. 600 British troops are killed and wounded and 800 taken prisoner.", "no_year_html": "British suffer a sharp defeat by the Boers south of <a href=\"https://wikipedia.org/wiki/Brandfort\" title=\"Brandfort\">Brandfort</a>. 600 British troops are killed and wounded and 800 taken prisoner.", "links": [{"title": "Brandfort", "link": "https://wikipedia.org/wiki/Brandfort"}]}, {"year": "1912", "text": "RMS Titanic sets sail from Southampton, England on her maiden and only voyage.", "html": "1912 - <i><a href=\"https://wikipedia.org/wiki/RMS_Titanic\" class=\"mw-redirect\" title=\"RMS Titanic\">RMS Titanic</a></i> sets sail from <a href=\"https://wikipedia.org/wiki/Southampton\" title=\"Southampton\">Southampton</a>, England on her maiden and only voyage.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/RMS_Titanic\" class=\"mw-redirect\" title=\"RMS Titanic\">RMS Titanic</a></i> sets sail from <a href=\"https://wikipedia.org/wiki/Southampton\" title=\"Southampton\">Southampton</a>, England on her maiden and only voyage.", "links": [{"title": "RMS Titanic", "link": "https://wikipedia.org/wiki/RMS_Titanic"}, {"title": "Southampton", "link": "https://wikipedia.org/wiki/Southampton"}]}, {"year": "1916", "text": "The Professional Golfers' Association of America (PGA) is created in New York City.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/Professional_Golfers%27_Association_of_America\" title=\"Professional Golfers' Association of America\">Professional Golfers' Association of America</a> (PGA) is created in New York City.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Professional_Golfers%27_Association_of_America\" title=\"Professional Golfers' Association of America\">Professional Golfers' Association of America</a> (PGA) is created in New York City.", "links": [{"title": "Professional Golfers' Association of America", "link": "https://wikipedia.org/wiki/Professional_Golfers%27_Association_of_America"}]}, {"year": "1919", "text": "Mexican Revolution leader <PERSON><PERSON> is ambushed and shot dead by government forces in Morelos.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a> leader <a href=\"https://wikipedia.org/wiki/Emiliano_Zapata\" title=\"Emiliano Zapata\"><PERSON><PERSON></a> is ambushed and shot dead by government forces in <a href=\"https://wikipedia.org/wiki/Morelos\" title=\"Morelos\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a> leader <a href=\"https://wikipedia.org/wiki/Emiliano_Zapata\" title=\"Emiliano Zapata\"><PERSON><PERSON> Zapa<PERSON></a> is ambushed and shot dead by government forces in <a href=\"https://wikipedia.org/wiki/Morelos\" title=\"Morelos\"><PERSON><PERSON></a>.", "links": [{"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emiliano_Zapata"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>los"}]}, {"year": "1919", "text": "The Third Regional Congress of Peasants, Workers and Insurgents is held by the Makhnovshchina at Huliaipole.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#Third_Congress_(April_1919)\" title=\"Regional Congress of Peasants, Workers and Insurgents\">Third Regional Congress of Peasants, Workers and Insurgents</a> is held by the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a> at <a href=\"https://wikipedia.org/wiki/Huliaipole\" title=\"Huliaipole\">Huliaipole</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#Third_Congress_(April_1919)\" title=\"Regional Congress of Peasants, Workers and Insurgents\">Third Regional Congress of Peasants, Workers and Insurgents</a> is held by the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a> at <a href=\"https://wikipedia.org/wiki/Huliaipole\" title=\"Huliaipole\">Huliaipole</a>.", "links": [{"title": "Regional Congress of Peasants, Workers and Insurgents", "link": "https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#Third_Congress_(April_1919)"}, {"title": "Makhnovshchina", "link": "https://wikipedia.org/wiki/Makhnovshchina"}, {"title": "Huliaipole", "link": "https://wikipedia.org/wiki/Huliaipole"}]}, {"year": "1925", "text": "The Great Gatsby by <PERSON><PERSON> is first published in New York City, by <PERSON>'s Sons.", "html": "1925 - <i><a href=\"https://wikipedia.org/wiki/The_Great_Gatsby\" title=\"The Great Gatsby\">The Great Gatsby</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is first published in New York City, by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Sons\" title=\"<PERSON>'s Sons\"><PERSON>'s Sons</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Great_Gatsby\" title=\"The Great Gatsby\">The Great Gatsby</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is first published in New York City, by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Sons\" title=\"<PERSON>'s Sons\"><PERSON>'s Sons</a>.", "links": [{"title": "The Great Gatsby", "link": "https://wikipedia.org/wiki/The_Great_Gatsby"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Charles <PERSON>'s Sons", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Sons"}]}, {"year": "1938", "text": "The 1938 German parliamentary election and referendum seeks approval for a single list of Nazi candidates and the recent annexation of Austria.", "html": "1938 - The <a href=\"https://wikipedia.org/wiki/1938_German_parliamentary_election_and_referendum\" title=\"1938 German parliamentary election and referendum\">1938 German parliamentary election and referendum</a> seeks approval for a single list of Nazi candidates and the recent annexation of Austria.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1938_German_parliamentary_election_and_referendum\" title=\"1938 German parliamentary election and referendum\">1938 German parliamentary election and referendum</a> seeks approval for a single list of Nazi candidates and the recent annexation of Austria.", "links": [{"title": "1938 German parliamentary election and referendum", "link": "https://wikipedia.org/wiki/1938_German_parliamentary_election_and_referendum"}]}, {"year": "1939", "text": "Alcoholics Anonymous, A.A.'s \"Big Book\", is first published.", "html": "1939 - <i><a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a></i>, A.A.'s \"Big Book\", is first published.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a></i>, A.A.'s \"Big Book\", is first published.", "links": [{"title": "Alcoholics Anonymous", "link": "https://wikipedia.org/wiki/Alcoholics_Anonymous"}]}, {"year": "1941", "text": "World War II: The Axis powers establish the Independent State of Croatia.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis powers</a> establish the <a href=\"https://wikipedia.org/wiki/Independent_State_of_Croatia\" title=\"Independent State of Croatia\">Independent State of Croatia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis powers</a> establish the <a href=\"https://wikipedia.org/wiki/Independent_State_of_Croatia\" title=\"Independent State of Croatia\">Independent State of Croatia</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Axis powers", "link": "https://wikipedia.org/wiki/Axis_powers"}, {"title": "Independent State of Croatia", "link": "https://wikipedia.org/wiki/Independent_State_of_Croatia"}]}, {"year": "1944", "text": "<PERSON> and <PERSON><PERSON><PERSON><PERSON> escape from Birkenau death camp.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Alfr%C3%A9d_<PERSON>zler\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> escape from <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Birkenau death camp</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Alfr%C3%A9d_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> escape from <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Birkenau death camp</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alfr%C3%A9d_<PERSON><PERSON>r"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1963", "text": "One hundred twenty-nine American sailors die when the submarine USS Thresher sinks at sea.", "html": "1963 - One hundred twenty-nine American sailors die when the <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> <a href=\"https://wikipedia.org/wiki/USS_Thresher_(SSN-593)\" title=\"<PERSON> Thresher (SSN-593)\">USS <i>Thresher</i></a> sinks at sea.", "no_year_html": "One hundred twenty-nine American sailors die when the <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> <a href=\"https://wikipedia.org/wiki/USS_Thresher_(SSN-593)\" title=\"<PERSON> Thresher (SSN-593)\">USS <i>Thresher</i></a> sinks at sea.", "links": [{"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}, {"title": "<PERSON> Thresher (SSN-593)", "link": "https://wikipedia.org/wiki/<PERSON>_Thr<PERSON><PERSON>_(SSN-593)"}]}, {"year": "1968", "text": "The TEV Wahine, a New Zealand ferry sinks in Wellington harbour due to a fierce storm - the strongest winds ever in Wellington. Out of the 734 people on board, fifty-three died.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/TEV_Wahine\" title=\"TEV Wahine\">TEV <i>Wahine</i></a>, a New Zealand ferry sinks in Wellington harbour due to a fierce storm - the strongest winds ever in Wellington. Out of the 734 people on board, fifty-three died.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/TEV_Wahine\" title=\"TEV Wahine\">TEV <i>Wahine</i></a>, a New Zealand ferry sinks in Wellington harbour due to a fierce storm - the strongest winds ever in Wellington. Out of the 734 people on board, fifty-three died.", "links": [{"title": "TEV Wahine", "link": "https://wikipedia.org/wiki/TEV_Wahine"}]}, {"year": "1970", "text": "<PERSON> announces that he is leaving The Beatles for personal and professional reasons.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that he is leaving <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> for personal and professional reasons.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that he is leaving <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> for personal and professional reasons.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}]}, {"year": "1971", "text": "Ping-pong diplomacy: In an attempt to thaw relations with the United States, China hosts the U.S. table tennis team for a week-long visit.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Ping-pong_diplomacy\" title=\"Ping-pong diplomacy\">Ping-pong diplomacy</a>: In an attempt to thaw relations with the United States, China hosts the U.S. <a href=\"https://wikipedia.org/wiki/Table_tennis\" title=\"Table tennis\">table tennis</a> team for a week-long visit.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ping-pong_diplomacy\" title=\"Ping-pong diplomacy\">Ping-pong diplomacy</a>: In an attempt to thaw relations with the United States, China hosts the U.S. <a href=\"https://wikipedia.org/wiki/Table_tennis\" title=\"Table tennis\">table tennis</a> team for a week-long visit.", "links": [{"title": "Ping-pong diplomacy", "link": "https://wikipedia.org/wiki/Ping-pong_diplomacy"}, {"title": "Table tennis", "link": "https://wikipedia.org/wiki/Table_tennis"}]}, {"year": "1972", "text": "Tombs containing bamboo slips, among them <PERSON>'s Art of War and <PERSON>'s lost military treatise, are discovered by construction workers in Shandong.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Yinqueshan_Han_Tombs\" class=\"mw-redirect\" title=\"Yinqueshan Han Tombs\">Tombs</a> containing <a href=\"https://wikipedia.org/wiki/Yinqueshan_Han_Slips\" title=\"Yinqueshan Han Slips\">bamboo slips</a>, among them <a href=\"https://wikipedia.org/wiki/Sun_Tzu\" title=\"Sun Tzu\">Sun Tzu</a>'s <i><a href=\"https://wikipedia.org/wiki/The_Art_of_War\" title=\"The Art of War\">Art of War</a></i> and <a href=\"https://wikipedia.org/wiki/Sun_Bin\" title=\"Sun Bin\">Sun Bin</a>'s lost <a href=\"https://wikipedia.org/wiki/Sun_Bin%27s_Art_of_War\" title=\"Sun Bin's Art of War\">military treatise</a>, are discovered by construction workers in <a href=\"https://wikipedia.org/wiki/Shandong\" title=\"Shandong\">Shandong</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yinqueshan_Han_Tombs\" class=\"mw-redirect\" title=\"Yinqueshan Han Tombs\">Tombs</a> containing <a href=\"https://wikipedia.org/wiki/Yinqueshan_Han_Slips\" title=\"Yinqueshan Han Slips\">bamboo slips</a>, among them <a href=\"https://wikipedia.org/wiki/Sun_Tzu\" title=\"Sun Tzu\">Sun Tzu</a>'s <i><a href=\"https://wikipedia.org/wiki/The_Art_of_War\" title=\"The Art of War\">Art of War</a></i> and <a href=\"https://wikipedia.org/wiki/Sun_Bin\" title=\"Sun Bin\">Sun Bin</a>'s lost <a href=\"https://wikipedia.org/wiki/Sun_Bin%27s_Art_of_War\" title=\"Sun Bin's Art of War\">military treatise</a>, are discovered by construction workers in <a href=\"https://wikipedia.org/wiki/Shandong\" title=\"Shandong\">Shandong</a>.", "links": [{"title": "Yinqueshan Han Tombs", "link": "https://wikipedia.org/wiki/Yinqueshan_Han_Tombs"}, {"title": "<PERSON><PERSON><PERSON>lips", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Slips"}, {"title": "Sun Tzu", "link": "https://wikipedia.org/wiki/Sun_Tzu"}, {"title": "The Art of War", "link": "https://wikipedia.org/wiki/The_Art_of_War"}, {"title": "Sun Bin", "link": "https://wikipedia.org/wiki/Sun_Bin"}, {"title": "<PERSON> Bin's Art of War", "link": "https://wikipedia.org/wiki/Sun_Bin%27s_Art_of_War"}, {"title": "Shandong", "link": "https://wikipedia.org/wiki/Shandong"}]}, {"year": "1972", "text": "Vietnam War: For the first time since November 1967, American B-52 bombers reportedly begin bombing North Vietnam.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: For the first time since November <a href=\"https://wikipedia.org/wiki/1967\" title=\"1967\">1967</a>, American <a href=\"https://wikipedia.org/wiki/B-52_Stratofortress\" class=\"mw-redirect\" title=\"B-52 Stratofortress\">B-52 bombers</a> reportedly begin bombing <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: For the first time since November <a href=\"https://wikipedia.org/wiki/1967\" title=\"1967\">1967</a>, American <a href=\"https://wikipedia.org/wiki/B-52_Stratofortress\" class=\"mw-redirect\" title=\"B-52 Stratofortress\">B-52 bombers</a> reportedly begin bombing <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "1967", "link": "https://wikipedia.org/wiki/1967"}, {"title": "B-52 Stratofortress", "link": "https://wikipedia.org/wiki/B-52_Stratofortress"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}]}, {"year": "1973", "text": "Invicta International Airlines Flight 435 crashes in a snowstorm on approach to Basel, Switzerland, killing 108 people.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Invicta_International_Airlines_Flight_435\" title=\"Invicta International Airlines Flight 435\">Invicta International Airlines Flight 435</a> crashes in a snowstorm on approach to <a href=\"https://wikipedia.org/wiki/Basel\" title=\"Basel\">Basel</a>, Switzerland, killing 108 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Invicta_International_Airlines_Flight_435\" title=\"Invicta International Airlines Flight 435\">Invicta International Airlines Flight 435</a> crashes in a snowstorm on approach to <a href=\"https://wikipedia.org/wiki/Basel\" title=\"Basel\">Basel</a>, Switzerland, killing 108 people.", "links": [{"title": "Invicta International Airlines Flight 435", "link": "https://wikipedia.org/wiki/Invicta_International_Airlines_Flight_435"}, {"title": "Basel", "link": "https://wikipedia.org/wiki/Basel"}]}, {"year": "1979", "text": "Red River Valley tornado outbreak: A tornado lands in Wichita Falls, Texas killing 42 people.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/1979_Red_River_Valley_tornado_outbreak\" title=\"1979 Red River Valley tornado outbreak\">Red River Valley tornado outbreak</a>: A tornado lands in <a href=\"https://wikipedia.org/wiki/Wichita_Falls,_Texas\" title=\"Wichita Falls, Texas\">Wichita Falls, Texas</a> killing 42 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1979_Red_River_Valley_tornado_outbreak\" title=\"1979 Red River Valley tornado outbreak\">Red River Valley tornado outbreak</a>: A tornado lands in <a href=\"https://wikipedia.org/wiki/Wichita_Falls,_Texas\" title=\"Wichita Falls, Texas\">Wichita Falls, Texas</a> killing 42 people.", "links": [{"title": "1979 Red River Valley tornado outbreak", "link": "https://wikipedia.org/wiki/1979_Red_River_Valley_tornado_outbreak"}, {"title": "Wichita Falls, Texas", "link": "https://wikipedia.org/wiki/Wichita_Falls,_Texas"}]}, {"year": "1988", "text": "The Ojhri Camp explosion kills or injures more than 1,000 people in Rawalpindi and Islamabad, Pakistan.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/Ojhri_Camp\" class=\"mw-redirect\" title=\"Ojhri Camp\">Ojhri Camp</a> explosion kills or injures more than 1,000 people in <a href=\"https://wikipedia.org/wiki/Rawalpindi\" title=\"Rawalpindi\">Rawalpindi</a> and <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad</a>, Pakistan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ojhri_Camp\" class=\"mw-redirect\" title=\"Ojhri Camp\">Ojhri Camp</a> explosion kills or injures more than 1,000 people in <a href=\"https://wikipedia.org/wiki/Rawalpindi\" title=\"Rawalpindi\">Rawalpindi</a> and <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad</a>, Pakistan.", "links": [{"title": "Ojhri Camp", "link": "https://wikipedia.org/wiki/Ojhri_Camp"}, {"title": "Rawalpindi", "link": "https://wikipedia.org/wiki/Rawalpindi"}, {"title": "Islamabad", "link": "https://wikipedia.org/wiki/Islamabad"}]}, {"year": "1991", "text": "Italian ferry MS Moby Prince collides with an oil tanker in dense fog off Livorno, Italy, killing 140.", "html": "1991 - Italian ferry <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Prince_disaster\" title=\"Moby Prince disaster\">MS <i><PERSON><PERSON></i></a> collides with an oil tanker in dense fog off <a href=\"https://wikipedia.org/wiki/Livorno\" title=\"Livorno\">Livorno</a>, Italy, killing 140.", "no_year_html": "Italian ferry <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Prince_disaster\" title=\"Moby Prince disaster\">MS <i><PERSON><PERSON></i></a> collides with an oil tanker in dense fog off <a href=\"https://wikipedia.org/wiki/Livorno\" title=\"Livorno\">Livorno</a>, Italy, killing 140.", "links": [{"title": "Moby Prince disaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_disaster"}, {"title": "Livorno", "link": "https://wikipedia.org/wiki/Livorno"}]}, {"year": "1991", "text": "A rare tropical storm develops in the South Atlantic Ocean near Angola; the first to be documented by satellites.", "html": "1991 - A <a href=\"https://wikipedia.org/wiki/South_Atlantic_tropical_cyclone\" title=\"South Atlantic tropical cyclone\">rare</a> <a href=\"https://wikipedia.org/wiki/Tropical_cyclone\" title=\"Tropical cyclone\">tropical storm</a> develops in the South <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a> near <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>; the first to be documented by <a href=\"https://wikipedia.org/wiki/Weather_satellite\" title=\"Weather satellite\">satellites</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/South_Atlantic_tropical_cyclone\" title=\"South Atlantic tropical cyclone\">rare</a> <a href=\"https://wikipedia.org/wiki/Tropical_cyclone\" title=\"Tropical cyclone\">tropical storm</a> develops in the South <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a> near <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>; the first to be documented by <a href=\"https://wikipedia.org/wiki/Weather_satellite\" title=\"Weather satellite\">satellites</a>.", "links": [{"title": "South Atlantic tropical cyclone", "link": "https://wikipedia.org/wiki/South_Atlantic_tropical_cyclone"}, {"title": "Tropical cyclone", "link": "https://wikipedia.org/wiki/Tropical_cyclone"}, {"title": "Atlantic Ocean", "link": "https://wikipedia.org/wiki/Atlantic_Ocean"}, {"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}, {"title": "Weather satellite", "link": "https://wikipedia.org/wiki/Weather_satellite"}]}, {"year": "1998", "text": "The Good Friday Agreement is signed in Northern Ireland.", "html": "1998 - The <a href=\"https://wikipedia.org/wiki/Good_Friday_Agreement\" title=\"Good Friday Agreement\">Good Friday Agreement</a> is signed in Northern Ireland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Good_Friday_Agreement\" title=\"Good Friday Agreement\">Good Friday Agreement</a> is signed in Northern Ireland.", "links": [{"title": "Good Friday Agreement", "link": "https://wikipedia.org/wiki/Good_Friday_Agreement"}]}, {"year": "2009", "text": "President of Fiji <PERSON><PERSON> announces the abrogation of the constitution and assumes all governance in the country, creating a constitutional crisis.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/President_of_Fiji\" title=\"President of Fiji\">President of Fiji</a> <a href=\"https://wikipedia.org/wiki/Ratu\" title=\"Ratu\">Ratu</a> <a href=\"https://wikipedia.org/wiki/Josefa_Iloil<PERSON>\" title=\"Josefa Iloilo\"><PERSON><PERSON></a> announces the abrogation of the <a href=\"https://wikipedia.org/wiki/1997_Constitution_of_Fiji\" title=\"1997 Constitution of Fiji\">constitution</a> and assumes all governance in the country, creating a <a href=\"https://wikipedia.org/wiki/2009_Fijian_constitutional_crisis\" title=\"2009 Fijian constitutional crisis\">constitutional crisis</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Fiji\" title=\"President of Fiji\">President of Fiji</a> <a href=\"https://wikipedia.org/wiki/Ratu\" title=\"Ratu\">Ratu</a> <a href=\"https://wikipedia.org/wiki/Josefa_Iloilo\" title=\"Josefa Iloilo\"><PERSON><PERSON></a> announces the abrogation of the <a href=\"https://wikipedia.org/wiki/1997_Constitution_of_Fiji\" title=\"1997 Constitution of Fiji\">constitution</a> and assumes all governance in the country, creating a <a href=\"https://wikipedia.org/wiki/2009_Fijian_constitutional_crisis\" title=\"2009 Fijian constitutional crisis\">constitutional crisis</a>.", "links": [{"title": "President of Fiji", "link": "https://wikipedia.org/wiki/President_of_Fiji"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ratu"}, {"title": "Josefa Iloilo", "link": "https://wikipedia.org/wiki/Josefa_Iloilo"}, {"title": "1997 Constitution of Fiji", "link": "https://wikipedia.org/wiki/1997_Constitution_of_Fiji"}, {"title": "2009 Fijian constitutional crisis", "link": "https://wikipedia.org/wiki/2009_Fijian_constitutional_crisis"}]}, {"year": "2010", "text": "Polish Air Force Tu-154M crashes near Smolensk, Russia, killing 96 people, including Polish President <PERSON><PERSON>, his wife, and dozens of other senior officials and dignitaries.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Polish_Air_Force\" title=\"Polish Air Force\">Polish Air Force</a> <a href=\"https://wikipedia.org/wiki/Tu-154\" class=\"mw-redirect\" title=\"Tu-154\">Tu-154</a>M <a href=\"https://wikipedia.org/wiki/Smolensk_air_disaster\" title=\"Smolensk air disaster\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Smolensk\" title=\"Smolensk\">Smolensk</a>, Russia, killing 96 people, including Polish President <a href=\"https://wikipedia.org/wiki/Lech_Kaczy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, his wife, and dozens of other senior officials and dignitaries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish_Air_Force\" title=\"Polish Air Force\">Polish Air Force</a> <a href=\"https://wikipedia.org/wiki/Tu-154\" class=\"mw-redirect\" title=\"Tu-154\">Tu-154</a>M <a href=\"https://wikipedia.org/wiki/Smolensk_air_disaster\" title=\"Smolensk air disaster\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Smolensk\" title=\"Smolensk\">Smolensk</a>, Russia, killing 96 people, including Polish President <a href=\"https://wikipedia.org/wiki/Lech_Kaczy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, his wife, and dozens of other senior officials and dignitaries.", "links": [{"title": "Polish Air Force", "link": "https://wikipedia.org/wiki/Polish_Air_Force"}, {"title": "Tu-154", "link": "https://wikipedia.org/wiki/Tu-154"}, {"title": "Smolensk air disaster", "link": "https://wikipedia.org/wiki/Smolensk_air_disaster"}, {"title": "Smolensk", "link": "https://wikipedia.org/wiki/Smolensk"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lech_Kaczy%C5%84ski"}]}, {"year": "2016", "text": "The Paravur temple accident in which a devastating fire caused by the explosion of firecrackers stored for <PERSON><PERSON><PERSON>, kills more than one hundred people out of the thousands gathered for seventh day of Bhadrakali worship.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/Paravur_temple_accident\" class=\"mw-redirect\" title=\"Paravur temple accident\">Paravur temple accident</a> in which a devastating fire caused by the explosion of firecrackers stored for <a href=\"https://wikipedia.org/wiki/Vishu\" title=\"Vishu\">Vishu</a>, kills more than one hundred people out of the thousands gathered for seventh day of <a href=\"https://wikipedia.org/wiki/Bhadrakali\" title=\"Bhadrakali\"><PERSON><PERSON><PERSON><PERSON></a> worship.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Paravur_temple_accident\" class=\"mw-redirect\" title=\"Paravur temple accident\">Paravur temple accident</a> in which a devastating fire caused by the explosion of firecrackers stored for <a href=\"https://wikipedia.org/wiki/Vishu\" title=\"Vishu\">Vishu</a>, kills more than one hundred people out of the thousands gathered for seventh day of <a href=\"https://wikipedia.org/wiki/Bhadrakali\" title=\"Bhadrakali\">Bhad<PERSON>ali</a> worship.", "links": [{"title": "Paravur temple accident", "link": "https://wikipedia.org/wiki/Paravur_temple_accident"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vishu"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2016", "text": "An earthquake of 6.6 magnitude strikes 39 km west-southwest of Ashkasham, impacting India, Afghanistan, Tajikistan, Srinagar and Pakistan.", "html": "2016 - An <a href=\"https://wikipedia.org/wiki/2016_Afghanistan_earthquake\" title=\"2016 Afghanistan earthquake\">earthquake of 6.6 magnitude</a> strikes 39 km west-southwest of <a href=\"https://wikipedia.org/wiki/Ashkasham\" class=\"mw-redirect\" title=\"Ashkasham\">Ashkasham</a>, impacting India, Afghanistan, Tajikistan, Srinagar and Pakistan.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2016_Afghanistan_earthquake\" title=\"2016 Afghanistan earthquake\">earthquake of 6.6 magnitude</a> strikes 39 km west-southwest of <a href=\"https://wikipedia.org/wiki/Ashkasham\" class=\"mw-redirect\" title=\"Ashkasham\">Ashkasham</a>, impacting India, Afghanistan, Tajikistan, Srinagar and Pakistan.", "links": [{"title": "2016 Afghanistan earthquake", "link": "https://wikipedia.org/wiki/2016_Afghanistan_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashkasham"}]}, {"year": "2019", "text": "Scientists from the Event Horizon Telescope project announce the first ever image of a black hole, which was located in the centre of the M87 galaxy.", "html": "2019 - Scientists from the <a href=\"https://wikipedia.org/wiki/Event_Horizon_Telescope\" title=\"Event Horizon Telescope\">Event Horizon Telescope</a> project announce the first ever image of a <a href=\"https://wikipedia.org/wiki/Black_hole\" title=\"Black hole\">black hole</a>, which was located in the centre of the <a href=\"https://wikipedia.org/wiki/Messier_87\" title=\"Messier 87\">M87</a> galaxy.", "no_year_html": "Scientists from the <a href=\"https://wikipedia.org/wiki/Event_Horizon_Telescope\" title=\"Event Horizon Telescope\">Event Horizon Telescope</a> project announce the first ever image of a <a href=\"https://wikipedia.org/wiki/Black_hole\" title=\"Black hole\">black hole</a>, which was located in the centre of the <a href=\"https://wikipedia.org/wiki/Messier_87\" title=\"Messier 87\">M87</a> galaxy.", "links": [{"title": "Event Horizon Telescope", "link": "https://wikipedia.org/wiki/Event_Horizon_Telescope"}, {"title": "Black hole", "link": "https://wikipedia.org/wiki/Black_hole"}, {"title": "Messier 87", "link": "https://wikipedia.org/wiki/Messier_87"}]}, {"year": "2023", "text": "A mass shooting occurs at the Old National Bank in Louisville, Kentucky that leaves five victims dead and eight wounded.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/2023_Louisville_bank_shooting\" title=\"2023 Louisville bank shooting\">A mass shooting</a> occurs at the <a href=\"https://wikipedia.org/wiki/Old_National_Bank\" title=\"Old National Bank\">Old National Bank</a> in <a href=\"https://wikipedia.org/wiki/Louisville,_Kentucky\" title=\"Louisville, Kentucky\">Louisville, Kentucky</a> that leaves five victims dead and eight wounded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2023_Louisville_bank_shooting\" title=\"2023 Louisville bank shooting\">A mass shooting</a> occurs at the <a href=\"https://wikipedia.org/wiki/Old_National_Bank\" title=\"Old National Bank\">Old National Bank</a> in <a href=\"https://wikipedia.org/wiki/Louisville,_Kentucky\" title=\"Louisville, Kentucky\">Louisville, Kentucky</a> that leaves five victims dead and eight wounded.", "links": [{"title": "2023 Louisville bank shooting", "link": "https://wikipedia.org/wiki/2023_Louisville_bank_shooting"}, {"title": "Old National Bank", "link": "https://wikipedia.org/wiki/Old_National_Bank"}, {"title": "Louisville, Kentucky", "link": "https://wikipedia.org/wiki/Louisville,_Kentucky"}]}], "Births": [{"year": "401", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 450)", "html": "401 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (d. 450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (d. 450)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theodosius_II"}]}, {"year": "1018", "text": "<PERSON><PERSON>,  Persian scholar and vizier (d. 1092)", "html": "1018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> al<PERSON>\"><PERSON><PERSON></a>, Persian scholar and vizier (d. 1092)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> al<PERSON>\"><PERSON><PERSON></a>, Persian scholar and vizier (d. 1092)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1472", "text": "<PERSON> York, English princess (d. 1472)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_York_(1472)\" title=\"<PERSON> of York (1472)\"><PERSON> York</a>, English princess (d. 1472)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_York_(1472)\" title=\"<PERSON> of York (1472)\"><PERSON> of York</a>, English princess (d. 1472)", "links": [{"title": "<PERSON> of York (1472)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1472)"}]}, {"year": "1480", "text": "<PERSON><PERSON><PERSON> <PERSON>, duke of Savoy (d. 1504)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON> <PERSON></a>, duke of Savoy (d. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON> <PERSON></a>, duke of Savoy (d. 1504)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_of_Savoy"}]}, {"year": "1487", "text": "<PERSON>, count of Nassau-Siegen (d. 1559)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON></a>, count of Nassau-Siegen (d. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON></a>, count of Nassau-Siegen (d. 1559)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1512", "text": "<PERSON>, king of Scotland (d. 1542)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/James_V_of_Scotland\" class=\"mw-redirect\" title=\"James V of Scotland\"><PERSON></a>, king of Scotland (d. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_V_of_Scotland\" class=\"mw-redirect\" title=\"James V of Scotland\"><PERSON></a>, king of Scotland (d. 1542)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/James_V_of_Scotland"}]}, {"year": "1579", "text": "<PERSON>, duke of Brunswick-Lüneburg (d. 1666)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger,_Duke_of_Brunswick-L%C3%BCneburg\" class=\"mw-redirect\" title=\"<PERSON> the Younger, Duke of Brunswick-Lüneburg\"><PERSON> II</a>, duke of Brunswick-Lüneburg (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger,_Duke_of_Brunswick-L%C3%BCneburg\" class=\"mw-redirect\" title=\"<PERSON> the Younger, Duke of Brunswick-Lüneburg\"><PERSON> II</a>, duke of Brunswick-Lüneburg (d. 1666)", "links": [{"title": "<PERSON> the Younger, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_the_Younger,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1583", "text": "<PERSON>, Dutch philosopher and jurist (d. 1645)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher and jurist (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher and jurist (d. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON>, Prince<PERSON><PERSON><PERSON><PERSON> of Denmark (d. 1647)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince-<PERSON><PERSON><PERSON>_of_Denmark\" title=\"<PERSON>, Prince-<PERSON><PERSON>t of Denmark\">Christian</a>, <PERSON><PERSON> of Denmark (d. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince-<PERSON><PERSON><PERSON>_of_Denmark\" title=\"<PERSON>, Prince-El<PERSON>t of Denmark\">Christian</a>, Prince<PERSON> of Denmark (d. 1647)", "links": [{"title": "<PERSON>, Prince<PERSON><PERSON><PERSON><PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>-<PERSON><PERSON>t_of_Denmark"}]}, {"year": "1651", "text": "<PERSON><PERSON><PERSON>, German mathematician, physicist, and physician (d. 1708)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>irn<PERSON>\" title=\"<PERSON><PERSON><PERSON> Tschirnhaus\"><PERSON><PERSON><PERSON></a>, German mathematician, physicist, and physician (d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>schirn<PERSON>\" title=\"<PERSON><PERSON><PERSON> von Tschirnhaus\"><PERSON><PERSON><PERSON></a>, German mathematician, physicist, and physician (d. 1708)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1656", "text": "<PERSON>, French-Canadian settler, founded Rimouski (d. 1718)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Lepage_de_<PERSON>-Claire\" title=\"<PERSON>-Claire\"><PERSON></a>, French-Canadian settler, founded <a href=\"https://wikipedia.org/wiki/Rimouski\" title=\"Rimouski\">Rimouski</a> (d. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>page_de_Sainte-Claire\" title=\"<PERSON> Sainte-Claire\"><PERSON>-Claire</a>, French-Canadian settler, founded <a href=\"https://wikipedia.org/wiki/Rimouski\" title=\"Rimouski\">Rimouski</a> (d. 1718)", "links": [{"title": "<PERSON>-Claire", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rimouski"}]}, {"year": "1704", "text": "<PERSON>, English scholar and author (d. 1766)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and author (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and author (d. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1707", "text": "<PERSON>, French organist, composer, and author (d. 1795)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist, composer, and author (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist, composer, and author (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, English geologist and clockmaker (d. 1788)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist and clockmaker (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist and clockmaker (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, German-French physician and academic (d. 1843)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French physician and academic (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French physician and academic (d. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, Italian physicist and academic (d. 1834)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, French marshal (d. 1809)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French marshal (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French marshal (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, English essayist and critic (d. 1830)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist and critic (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist and critic (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, English-Scottish American commander (d. 1858)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish American commander (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish American commander (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, French actress (d. 1883)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>et"}]}, {"year": "1806", "text": "<PERSON><PERSON>, Scottish-American general and bishop (d. 1884)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American general and bishop (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American general and bishop (d. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leonidas_Polk"}]}, {"year": "1827", "text": "<PERSON><PERSON>, American general, lawyer, and politician, 11th Governor of New Mexico Territory (d. 1905)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general, lawyer, and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico_Territory\" class=\"mw-redirect\" title=\"Governor of New Mexico Territory\">Governor of New Mexico Territory</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general, lawyer, and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico_Territory\" class=\"mw-redirect\" title=\"Governor of New Mexico Territory\">Governor of New Mexico Territory</a> (d. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>w_<PERSON>"}, {"title": "Governor of New Mexico Territory", "link": "https://wikipedia.org/wiki/Governor_of_New_Mexico_Territory"}]}, {"year": "1829", "text": "<PERSON>, English minister, founded The Salvation Army (d. 1912)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, founded <a href=\"https://wikipedia.org/wiki/The_Salvation_Army\" title=\"The Salvation Army\">The Salvation Army</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, founded <a href=\"https://wikipedia.org/wiki/The_Salvation_Army\" title=\"The Salvation Army\">The Salvation Army</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Salvation Army", "link": "https://wikipedia.org/wiki/The_Salvation_Army"}]}, {"year": "1847", "text": "<PERSON>, Hungarian-American journalist, publisher, and politician, founded Pulitzer, Inc. (d. 1911)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American journalist, publisher, and politician, founded <a href=\"https://wikipedia.org/wiki/Pulitzer,_Inc.\" title=\"Pulitzer, Inc.\">Pulitzer, Inc.</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American journalist, publisher, and politician, founded <a href=\"https://wikipedia.org/wiki/Pulitzer,_Inc.\" title=\"Pulitzer, Inc.\">Pulitzer, Inc.</a> (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pulitzer, Inc.", "link": "https://wikipedia.org/wiki/Pulitzer,_Inc."}]}, {"year": "1864", "text": "<PERSON><PERSON>, Scottish-German pianist and composer (d. 1932)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Albert\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-German pianist and composer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27A<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-German pianist and composer (d. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eugen_d%27Albert"}]}, {"year": "1865", "text": "<PERSON>, American-Canadian farmer, hunter, and environmentalist (d. 1944)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian farmer, hunter, and environmentalist (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian farmer, hunter, and environmentalist (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Irish author, poet, and painter (d. 1935)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author, poet, and painter (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author, poet, and painter (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, English actor and playwright (d. 1946)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Moravian rabbi (d. 1931)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Asriel_G%C3%BCnzig\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moravian rabbi (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/As<PERSON>_G%C3%BCnz<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moravian rabbi (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asriel_G%C3%BCnzig"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish farmer, banker, and politician, 4th President of Finland (d. 1940)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Ky%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish farmer, banker, and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ky%C3%B6st<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish farmer, banker, and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ky%C3%B6sti_Ka<PERSON>o"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1875", "text": "<PERSON>, English footballer (d. 1920)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Austrian author and illustrator (d. 1959)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and illustrator (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and illustrator (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch rower and physician (d. 1921)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Coenra<PERSON>_<PERSON>ebendaal\" title=\"Coenra<PERSON> Hiebendaal\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch rower and physician (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coenra<PERSON>_<PERSON>aal\" title=\"Coenra<PERSON>endaal\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch rower and physician (d. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>en<PERSON><PERSON>_<PERSON>aal"}]}, {"year": "1880", "text": "<PERSON>, American sociologist, academic, and politician, United States Secretary of Labor (d. 1965)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, academic, and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, academic, and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "1880", "text": "<PERSON><PERSON>, English clergyman and author (d. 1948)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Montague_Summers\" title=\"Montague Summers\"><PERSON><PERSON></a>, English clergyman and author (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mont<PERSON>_Summers\" title=\"Montague Summers\"><PERSON><PERSON></a>, English clergyman and author (d. 1948)", "links": [{"title": "Montague Summers", "link": "https://wikipedia.org/wiki/Montague_Summers"}]}, {"year": "1886", "text": "<PERSON>, American runner and trainer (d. 1965)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and trainer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and trainer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Argentinian physiologist and academic, Nobel Prize laureate (d. 1971)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1889", "text": "<PERSON>, French philosopher from the Vienna Circle (d. 1982)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher from the Vienna Circle (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher from the Vienna Circle (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>gie<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, English footballer and coach (d. 1968)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Austrian zoologist (d. 1969)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ck\" title=\"<PERSON>\"><PERSON></a>, Austrian zoologist (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ck\" title=\"<PERSON>\"><PERSON></a>, Austrian zoologist (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ck"}]}, {"year": "1894", "text": "<PERSON>, British painter (d. 1982)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian accountant and politician, 3rd Chief Minister of West Bengal (d. 1990)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian accountant and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian accountant and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1900", "text": "<PERSON>, American chemist, inventor, and philanthropist (d. 2004)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist, inventor, and philanthropist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>man\"><PERSON></a>, American chemist, inventor, and philanthropist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian economist (d. 1971)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Greek architect (d. 1976)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek architect (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek architect (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American author and illustrator (d. 1970)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American hurdler (d. 1988)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (d. 1988)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1910", "text": "<PERSON>, American scholar and academic (d. 1974)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and academic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Argentinian footballer and manager (d. 1997)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer and manager (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer and manager (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American economist and publisher, founded the Monthly Review (d. 2004)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and publisher, founded the <i><a href=\"https://wikipedia.org/wiki/Monthly_Review\" title=\"Monthly Review\">Monthly Review</a></i> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and publisher, founded the <i><a href=\"https://wikipedia.org/wiki/Monthly_Review\" title=\"Monthly Review\">Monthly Review</a></i> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Monthly Review", "link": "https://wikipedia.org/wiki/Monthly_Review"}]}, {"year": "1911", "text": "<PERSON>, American pianist and composer (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, French journalist and politician, Minister of Foreign and European Affairs for France (d. 1998)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">Minister of Foreign and European Affairs</a> for France (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">Minister of Foreign and European Affairs</a> for France (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign and European Affairs (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)"}]}, {"year": "1912", "text": "<PERSON>, Austrian-Slovenian politician, 1st Prime Minister of Slovenia (d. 1953)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Austrian-Slovenian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovenia\" title=\"Prime Minister of Slovenia\">Prime Minister of Slovenia</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Austrian-Slovenian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovenia\" title=\"Prime Minister of Slovenia\">Prime Minister of Slovenia</a> (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D"}, {"title": "Prime Minister of Slovenia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Slovenia"}]}, {"year": "1913", "text": "<PERSON>, German-American soldier and author (d. 2001)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American soldier and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American soldier and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian cricketer (d. 1982)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, Australian cricketer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actor and director (d. 2011)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Dutch-American hematologist, poet, and illustrator (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American hematologist, poet, and illustrator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American hematologist, poet, and illustrator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Vroman"}]}, {"year": "1916", "text": "<PERSON>, Korean painter (d. 1956)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean painter (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean painter (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Indian politician (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 1979)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1919", "text": "<PERSON>, American engineer and academic (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American baseball player and actor (d. 1992)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Canadian soldier and diplomat, Canadian Ambassador to the United States (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and diplomat, <a href=\"https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"Canadian Ambassador to the United States\">Canadian Ambassador to the United States</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and diplomat, <a href=\"https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"Canadian Ambassador to the United States\">Canadian Ambassador to the United States</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canadian Ambassador to the United States", "link": "https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_States"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American singer-songwriter and actor (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>ooley\" title=\"She<PERSON> Wooley\"><PERSON><PERSON></a>, American singer-songwriter and actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ooley\" title=\"<PERSON><PERSON> Wooley\"><PERSON><PERSON></a>, American singer-songwriter and actor (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_Wooley"}]}, {"year": "1923", "text": "<PERSON>, Haitian historian and author (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(historian)\" class=\"mw-redirect\" title=\"<PERSON> (historian)\"><PERSON></a>, Haitian historian and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(historian)\" class=\"mw-redirect\" title=\"<PERSON> (historian)\"><PERSON></a>, Haitian historian and author (d. 2000)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_(historian)"}]}, {"year": "1923", "text": "<PERSON>, American actress and singer (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American decathlete and actor (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete and actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete and actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English footballer (d. 1997)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, South African cricketer (d. 2021)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_cricketer)\" title=\"<PERSON> (South African cricketer)\"><PERSON></a>, South African cricketer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_cricketer)\" title=\"<PERSON> (South African cricketer)\"><PERSON></a>, South African cricketer (d. 2021)", "links": [{"title": "<PERSON> (South African cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_cricketer)"}]}, {"year": "1924", "text": "<PERSON>, American soldier and painter (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American wrestler and promoter (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, French pianist and composer (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r%C3%A8de\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r%C3%A8de\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_Cast%C3%A9r%C3%A8de"}]}, {"year": "1926", "text": "<PERSON>, American comedian (d. 1983)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Junior_<PERSON>\" title=\"Junior Samples\"><PERSON></a>, American comedian (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Junior_<PERSON>\" title=\"Junior Samples\"><PERSON></a>, American comedian (d. 1983)", "links": [{"title": "Junior Samples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Puerto Rican actress (d. 2006)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American biochemist and geneticist, Nobel Prize laureate (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1929", "text": "<PERSON>, English race car driver (d. 1959)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actress (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Swedish-French actor (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-French actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-French actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, French pianist, composer, and actor (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American activist, co-founded the United Farm Workers", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/United_Farm_Workers\" title=\"United Farm Workers\">United Farm Workers</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/United_Farm_Workers\" title=\"United Farm Workers\">United Farm Workers</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Farm Workers", "link": "https://wikipedia.org/wiki/United_Farm_Workers"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Finnish film director and producer, comedian, and inventor (d. 2001)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Spede_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pa<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish film director and producer, comedian, and inventor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spede_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish film director and producer, comedian, and inventor (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Spede_<PERSON><PERSON>en"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Indian classical vocalist (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian classical vocalist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian classical vocalist (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Swiss/Alsatian French actress (d. 1990)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Swiss_people\" title=\"Swiss people\">Swiss</a>/<a href=\"https://wikipedia.org/wiki/List_of_Alsatians_and_Lotharingians\" title=\"List of Alsatians and Lotharingians\">Alsatian</a> French actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Swiss_people\" title=\"Swiss people\">Swiss</a>/<a href=\"https://wikipedia.org/wiki/List_of_Alsatians_and_Lotharingians\" title=\"List of Alsatians and Lotharingians\">Alsatian</a> French actress (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Swiss people", "link": "https://wikipedia.org/wiki/Swiss_people"}, {"title": "List of Alsatians and Lotharingians", "link": "https://wikipedia.org/wiki/List_of_Alsatians_and_Lotharingians"}]}, {"year": "1932", "text": "<PERSON>, Egyptian actor and screenwriter (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian actor and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian actor and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Japanese composer and author (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_E<PERSON>\" title=\"<PERSON><PERSON><PERSON> E<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_E<PERSON>\" title=\"R<PERSON><PERSON> E<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and author (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ei"}]}, {"year": "1933", "text": "<PERSON>, Scottish politician (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American journalist and author (d. 2007)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English actor and director (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian bishop, 23rd Governor General of Australia", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bishop, 23rd <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bishop, 23rd <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor General of Australia", "link": "https://wikipedia.org/wiki/Governor_General_of_Australia"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Greek philosopher, theologian and author (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek philosopher, theologian and author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek philosopher, theologian and author (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American soldier (d. 1961)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, British artist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English long jumper", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English long jumper", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1936", "text": "<PERSON>, American football player, coach, and sportscaster (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rhythm_and_blues_singer)\" title=\"<PERSON> (rhythm and blues singer)\"><PERSON></a>, American singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rhythm_and_blues_singer)\" title=\"<PERSON> (rhythm and blues singer)\"><PERSON></a>, American singer (d. 2013)", "links": [{"title": "<PERSON> (rhythm and blues singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rhythm_and_blues_singer)"}]}, {"year": "1937", "text": "<PERSON>, Soviet and Russian poet, short story writer, and translator (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Akhmadulina\"><PERSON></a>, Soviet and Russian poet, short story writer, and translator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Akhmadulina\"><PERSON></a>, Soviet and Russian poet, short story writer, and translator (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American football player and sportscaster (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Italian scholar, author, and translator", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian scholar, author, and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian scholar, author, and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, British radio and television host", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1941", "text": "Chrysostomos II of Cyprus, (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Chrysostomos_II_of_Cyprus\" title=\"Chrysostomos II of Cyprus\">Chrysostomos II of Cyprus</a>, (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chrysostomos_II_of_Cyprus\" title=\"Chrysostomos II of Cyprus\">Chrysostomos II of Cyprus</a>, (d. 2022)", "links": [{"title": "Chrysostomos II of Cyprus", "link": "https://wikipedia.org/wiki/Chrysostomos_II_of_Cyprus"}]}, {"year": "1941", "text": "<PERSON>, Canadian politician (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician (d. 2013)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1941", "text": "<PERSON>, American novelist, short story writer, and travel writer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and travel writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and travel writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian journalist and politician (d. 1998)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English footballer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>,  American novelist, short story writer, and poet", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Polish-German sprinter (d. 2008)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-German sprinter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-German sprinter (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e%C5%84ski"}]}, {"year": "1943", "text": "<PERSON>, English author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian swimmer (d. 2006)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American screenwriter and producer (d. 2001)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American baseball player and manager (d. 2020)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, German director, producer, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, German director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, German director, producer, and screenwriter", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_(film_director)"}]}, {"year": "1947", "text": "<PERSON>, American author and educator", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Jamaican singer-songwriter and drummer (d. 2021)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ailer\" title=\"<PERSON> Wailer\"><PERSON></a>, Jamaican singer-songwriter and drummer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Wailer\" title=\"<PERSON> Wailer\"><PERSON></a>, Jamaican singer-songwriter and drummer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wailer"}]}, {"year": "1948", "text": "<PERSON>, American football player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nt"}]}, {"year": "1948", "text": "<PERSON>, Welsh artist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French banker and sportscaster", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French banker and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French banker and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter, keyboardist and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboardist and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboardist and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Sr., American baseball player and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American baseball player and manager", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "1950", "text": "<PERSON>, American guitarist (d. 1992)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American journalist and activist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Indian politician, 16th Chief Minister of Maharashtra", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian politician, 16th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian politician, 16th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Japanese singer, lyricist, composer, novelist, actor, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer, lyricist, composer, novelist, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer, lyricist, composer, novelist, actor, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1952", "text": "<PERSON>, American actor, producer, and martial artist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English runner and businessman", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Swedish-Canadian journalist, academic, and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Canadian journalist, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Canadian journalist, academic, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American wrestler and manager (d. 2013)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American author and educator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Panamanian-American journalist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian-American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian-American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Norwegian handball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Mari<PERSON>_Breivik\" title=\"Marit Breivik\"><PERSON><PERSON></a>, Norwegian handball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari<PERSON>_Breivik\" title=\"Marit Breivik\"><PERSON><PERSON></a>, Norwegian handball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marit_Breivik"}]}, {"year": "1955", "text": "<PERSON>, English soprano and actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian-American former Scientologist, critic (d. 2025)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American former Scientologist, critic (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American former Scientologist, critic (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English chemist and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Nigerian businessman, founded Dangote Group", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian businessman, founded <a href=\"https://wikipedia.org/wiki/Dangote_Group\" title=\"Dangote Group\">Dangote Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian businessman, founded <a href=\"https://wikipedia.org/wiki/Dangote_Group\" title=\"Dangote Group\">Dangote Group</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Dangote Group", "link": "https://wikipedia.org/wiki/Dangote_Group"}]}, {"year": "1957", "text": "<PERSON>, American author and poet (d. 2006)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Spanish-American bass player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English historian and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rosemary Hill\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rosemary Hill\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Northern Irish engineer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorsport)\" title=\"<PERSON> (motorsport)\"><PERSON></a>, Northern Irish engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorsport)\" title=\"<PERSON> (motorsport)\"><PERSON></a>, Northern Irish engineer", "links": [{"title": "<PERSON> (motorsport)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorsport)"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Uzbek-American pianist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uzbek-American pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uzbek-American pianist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, German high jumper", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German high jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Canadian economist and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian economist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American businessman, co-founded Allegis Group", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Allegis_Group\" title=\"Allegis Group\">Allegis Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Allegis_Group\" title=\"Allegis Group\">Allegis Group</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Allegis Group", "link": "https://wikipedia.org/wiki/Allegis_Group"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American basketball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Scottish broadcaster and journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish broadcaster and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish broadcaster and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English computer scientist and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American basketball player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1961)\" title=\"<PERSON> (basketball, born 1961)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1961)\" title=\"<PERSON> (basketball, born 1961)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1961)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1961)"}]}, {"year": "1962", "text": "<PERSON>, American football player and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1963", "text": "<PERSON>, American guitarist and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1963)\" title=\"<PERSON> (baseball, born 1963)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1963)\" title=\"<PERSON> (baseball, born 1963)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball, born 1963)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1963)"}]}, {"year": "1963", "text": "<PERSON>, Swiss lawyer and politician, 162nd President of the Swiss Confederation", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 162nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 162nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1965", "text": "<PERSON>, American drummer and songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "Anna<PERSON><PERSON><PERSON>, Finnish author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>%C3%A4rk%C3%B6nen\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>%C3%A4rk%C3%B6nen\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_H%C3%A4rk%C3%B6nen"}]}, {"year": "1966", "text": "<PERSON>, American football player and actor (d. 2022)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English footballer, manager, and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Turkish photographer and journalist (d. 1996)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Metin_G%C3%B6ktepe\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish photographer and journalist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Metin_G%C3%B6ktepe\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish photographer and journalist (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Metin_G%C3%B6ktepe"}]}, {"year": "1968", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jones\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jones\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Jones"}]}, {"year": "1969", "text": "<PERSON>, Australian lawn bowler", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawn bowler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawn bowler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek sprinter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>kateri<PERSON>_<PERSON>\" title=\"Ekateri<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>kateri<PERSON>_<PERSON>\" title=\"Ekateri<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>kat<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1970", "text": "<PERSON>, Canadian ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Romanian-Canadian boxer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Canadian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Canadian boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper, producer, and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (musician)\"><PERSON>-<PERSON><PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (musician)\"><PERSON>-<PERSON><PERSON></a>, American rapper, producer, and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_(musician)"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Estonian footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/1971\" title=\"1971\">1971</a> - <a href=\"https://wikipedia.org/wiki/Indro_<PERSON>\" title=\"In<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1971\" title=\"1971\">1971</a> - <a href=\"https://wikipedia.org/wiki/Indro_<PERSON>\" title=\"In<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer and coach", "links": [{"title": "1971", "link": "https://wikipedia.org/wiki/1971"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indro_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Dominican-American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Reyes\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Reyes"}]}, {"year": "1972", "text": "<PERSON>, Australian cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Estonian computer programmer, co-created Skype", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian computer programmer, co-created <a href=\"https://wikipedia.org/wiki/Skype\" title=\"Skype\">Skype</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian computer programmer, co-created <a href=\"https://wikipedia.org/wiki/Skype\" title=\"Skype\">Skype</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Priit_<PERSON>u"}, {"title": "Skype", "link": "https://wikipedia.org/wiki/Skype"}]}, {"year": "1972", "text": "<PERSON>, Scottish film maker", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish film maker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish film maker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French actor and director", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Brazilian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Scottish singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian-American graphic designer, author, and academic", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American graphic designer, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American graphic designer, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American soldier, author and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, author and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, author and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Greek footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Passalis"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Indian dancer and choreographer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON><PERSON></a>, Indian dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON><PERSON></a>, Indian dancer and choreographer", "links": [{"title": "<PERSON> (choreographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/David_Harbour\" title=\"David Harbour\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/David_Harbour\" title=\"David Harbour\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/David_Harbour"}]}, {"year": "1976", "text": "<PERSON>, English actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese actress and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian skier", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ren<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ner"}]}, {"year": "1977", "text": "<PERSON>, Taiwanese-American voice actress, director, and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American voice actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American voice actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Finnish guitarist (d. 2017)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>\" title=\"Sir <PERSON>\">Sir <PERSON></a>, Finnish guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON><PERSON>\" title=\"Sir <PERSON><PERSON>\">Sir <PERSON></a>, Finnish guitarist (d. 2017)", "links": [{"title": "Sir <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Uruguayan footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Iv%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American author and activist (d. 2003)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter and actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>-<PERSON><PERSON><PERSON>, English singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Greek rapper (d. 2013)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek rapper (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek rapper (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Finnish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player and model", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Chinese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jiayi\"><PERSON><PERSON></a>, Chinese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jiayi\"><PERSON><PERSON></a>, Chinese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Israeli tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English singer and dancer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor, model and musician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, model and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, model and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Russian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hockey_player)\" class=\"mw-redirect\" title=\"<PERSON> (hockey player)\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hockey_player)\" class=\"mw-redirect\" title=\"<PERSON> (hockey player)\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON> (hockey player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hockey_player)"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American actress and singer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American guitarist and songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Icelandic footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Hannes_Sigur%C3%B<PERSON>son\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>nes_Sigur%C3%B<PERSON>son\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hannes_Sigur%C3%B<PERSON>son"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Australian television host", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American figure skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Ugandan footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, French-Polish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Polish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>r%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%ADguez"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Somali-American actor and director", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Somali-American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Somali-American actor and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bar<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Irish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Flood\"><PERSON><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Flood\"><PERSON><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_G%C3%A1mez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_G%C3%A1mez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_G%C3%A1mez"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Belgian sprinter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Belgian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Belgian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Olivia_Borl%C3%A9e"}]}, {"year": "1986", "text": "<PERSON>, Argentine footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball pitcher", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Belgian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Norwegian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Regini<PERSON>\" title=\"Tore Reginiussen\"><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Reg<PERSON>\" title=\"Tor<PERSON> Reginiussen\"><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>en"}]}, {"year": "1987", "text": "<PERSON>, Egyptian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>ea<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian actress and model", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, New Zealand soprano", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Hay<PERSON>_Westenra\" title=\"<PERSON><PERSON> Westenra\"><PERSON><PERSON></a>, New Zealand soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hay<PERSON>_Westenra\" title=\"<PERSON>ley Westenra\"><PERSON><PERSON></a>, New Zealand soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hayley_<PERSON>ra"}]}, {"year": "1988", "text": "<PERSON>, American baseball pitcher", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, South African footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/And<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/And<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/And<PERSON>_<PERSON>ali"}]}, {"year": "1990", "text": "<PERSON>, Australian-Samoan rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Samoan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Senegalese footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Sadio_Man%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sad<PERSON>_Man%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sadio_Man%C3%A9"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Australian racing driver", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American singer and actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Carson"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Scottish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1995)\" title=\"<PERSON> (actor, born 1995)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1995)\" title=\"<PERSON> (actor, born 1995)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor, born 1995)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor,_born_1995)"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Australian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American activist and author (d. 2018)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Russian figure skater", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_Pogorilaya"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Cypriot internet celebrity and politician", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot internet celebrity and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot internet celebrity and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Australian singer and actor", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Israeli singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}], "Deaths": [{"year": "879", "text": "<PERSON>, king of West Francia (b. 846)", "html": "879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer\" title=\"<PERSON> the Stammerer\"><PERSON> the <PERSON></a>, king of West Francia (b. 846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer\" title=\"<PERSON> the Stammerer\"><PERSON> the <PERSON></a>, king of West Francia (b. 846)", "links": [{"title": "<PERSON> Stammerer", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer"}]}, {"year": "943", "text": "<PERSON><PERSON>, prince of Benevento and Capua", "html": "943 - <a href=\"https://wikipedia.org/wiki/Landulf_I_of_Benevento\" title=\"Land<PERSON> I of Benevento\">Land<PERSON> I</a>, prince of Benevento and Capua", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Landulf_I_of_Benevento\" title=\"Landulf I of Benevento\">Landulf I</a>, prince of Benevento and Capua", "links": [{"title": "<PERSON><PERSON> I of Benevento", "link": "https://wikipedia.org/wiki/Landulf_I_of_Benevento"}]}, {"year": "948", "text": "<PERSON> of Arles, king of Italy", "html": "948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Italy\" title=\"<PERSON> of Italy\"><PERSON> of Arles</a>, king of Italy", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Italy\" title=\"<PERSON> of Italy\"><PERSON> of Arles</a>, king of Italy", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON>_of_Italy"}]}, {"year": "1008", "text": "<PERSON><PERSON> of Liège, French bishop (b. 940)", "html": "1008 - <a href=\"https://wikipedia.org/wiki/Notker_of_Li%C3%A8ge\" title=\"<PERSON><PERSON> of Liège\"><PERSON><PERSON> of Liège</a>, French bishop (b. 940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Notker_of_Li%C3%A8ge\" title=\"<PERSON><PERSON> of Liège\"><PERSON><PERSON> of Liège</a>, French bishop (b. 940)", "links": [{"title": "<PERSON><PERSON> of Liège", "link": "https://wikipedia.org/wiki/Notker_of_Li%C3%A8ge"}]}, {"year": "1216", "text": "<PERSON>, king of Sweden (b. 1180)", "html": "1216 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON></a>, king of Sweden (b. 1180)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON></a>, king of Sweden (b. 1180)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1282", "text": "<PERSON>, chief minister under <PERSON><PERSON><PERSON>", "html": "1282 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chief minister under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chief minister under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1309", "text": "<PERSON>, Swiss countess (b. 1261)", "html": "1309 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss countess (b. 1261)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss countess (b. 1261)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1362", "text": "<PERSON>, English noblewoman (b. 1339)", "html": "1362 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Leicester\" title=\"<PERSON>, Countess of Leicester\"><PERSON></a>, English noblewoman (b. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Leicester\" title=\"<PERSON>, Countess of Leicester\"><PERSON></a>, English noblewoman (b. 1339)", "links": [{"title": "<PERSON>, Countess of Leicester", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_of_Leicester"}]}, {"year": "1500", "text": "<PERSON>, Greek scholar and poet", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek scholar and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek scholar and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON>, king of Denmark and Norway (b. 1471)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON></a>, king of Denmark and Norway (b. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON></a>, king of Denmark and Norway (b. 1471)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark"}]}, {"year": "1545", "text": "<PERSON><PERSON><PERSON>, Italian composer", "html": "1545 - <a href=\"https://wikipedia.org/wiki/Costanzo_Festa\" title=\"Costanzo Festa\">Costanzo Festa</a>, Italian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Costanzo_Festa\" title=\"Costanzo Festa\">Costanz<PERSON> Festa</a>, Italian composer", "links": [{"title": "Costanzo Festa", "link": "https://wikipedia.org/wiki/Costanzo_Festa"}]}, {"year": "1585", "text": "<PERSON>, pope of the Catholic Church (b. 1502)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1502)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1502)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1598", "text": "<PERSON><PERSON><PERSON>, Italian philosopher (b. 1548)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian philosopher (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian philosopher (b. 1548)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON>, French mistress of <PERSON> of France (b. 1571)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_d%27Estr%C3%A9es\" title=\"<PERSON>\"><PERSON></a>, French mistress of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Estr%C3%A9es\" title=\"<PERSON>\"><PERSON></a>, French mistress of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (b. 1571)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gabrielle_d%27Estr%C3%A9es"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}]}, {"year": "1601", "text": "<PERSON>, Scottish soldier and poet (b. 1562)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and poet (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and poet (b. 1562)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON>, English-Irish archbishop and politician, Lord Chancellor of Ireland (b. 1550)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English-Irish archbishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Ireland\" title=\"Lord Chancellor of Ireland\">Lord Chancellor of Ireland</a> (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English-Irish archbishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Ireland\" title=\"Lord Chancellor of Ireland\">Lord Chancellor of Ireland</a> (b. 1550)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}, {"title": "Lord Chancellor of Ireland", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_Ireland"}]}, {"year": "1640", "text": "<PERSON><PERSON><PERSON>, Italian composer and theorist (b. 1578)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and theorist (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and theorist (b. 1578)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1644", "text": "<PERSON>, English official and pilgrim leader (b. 1566)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Mayflower_passenger)\" title=\"<PERSON> (Mayflower passenger)\"><PERSON></a>, English official and pilgrim leader (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(May<PERSON>_passenger)\" title=\"<PERSON> (Mayflower passenger)\"><PERSON></a>, English official and pilgrim leader (b. 1566)", "links": [{"title": "<PERSON> (Mayflower passenger)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON>_passenger)"}]}, {"year": "1646", "text": "<PERSON><PERSON>, Swiss architect and sculptor (b. 1576)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/Santino_Solari\" title=\"Santino Solari\"><PERSON><PERSON></a>, Swiss architect and sculptor (b. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santino_Solari\" title=\"Santino Solari\"><PERSON><PERSON></a>, Swiss architect and sculptor (b. 1576)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Santino_Solari"}]}, {"year": "1667", "text": "<PERSON>, Czech physician and author (b. 1595)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech physician and author (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech physician and author (b. 1595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, German cardinal (b. 1629)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCrstenberg\" title=\"<PERSON>\"><PERSON></a>, German cardinal (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCrstenberg\" title=\"<PERSON>\"><PERSON></a>, German cardinal (b. 1629)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>%C3%BCrstenberg"}]}, {"year": "1756", "text": "<PERSON>, Italian composer (b. 1661)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1661)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, French historian and author (b. 1687)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1687)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1786", "text": "<PERSON>, English admiral and politician, 24th Commodore Governor of Newfoundland (b. 1723)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, 24th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, 24th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of lieutenant governors of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador"}]}, {"year": "1806", "text": "<PERSON><PERSON><PERSON>, English-American general (b. 1727)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-American general (b. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-American general (b. 1727)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horat<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON>, Italian mathematician and astronomer (b. 1736)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and astronomer (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and astronomer (b. 1736)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1821", "text": "<PERSON> of Constantinople, Ecumenical Patriarch of Constantinople (b. 1746)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Gregory_V_of_Constantinople\" title=\"Gregory V of Constantinople\"><PERSON> of Constantinople</a>, Ecumenical Patriarch of Constantinople (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gregory_V_of_Constantinople\" title=\"<PERSON> V of Constantinople\"><PERSON> of Constantinople</a>, Ecumenical Patriarch of Constantinople (b. 1746)", "links": [{"title": "<PERSON> V of Constantinople", "link": "https://wikipedia.org/wiki/Gregory_V_of_Constantinople"}]}, {"year": "1823", "text": "<PERSON>, Austrian philosopher and academic (b. 1757)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher and academic (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher and academic (b. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON>, Argentinian general and politician (b. 1789)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian general and politician (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian general and politician (b. 1789)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Scottish engineer and shipbuilder (b. 1827)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, Scottish engineer and shipbuilder (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, Scottish engineer and shipbuilder (b. 1827)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_(engineer)"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, English poet, playwright, novelist, and critic (b. 1837)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet, playwright, novelist, and critic (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet, playwright, novelist, and critic (b. 1837)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Mexican general (b. 1879)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Emiliano_Zapata\" title=\"Emiliano Zapata\"><PERSON><PERSON></a>, Mexican general (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emiliano_Zapata\" title=\"Emiliano Zapata\"><PERSON><PERSON></a>, Mexican general (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emiliano_Zapata"}]}, {"year": "1920", "text": "<PERSON><PERSON>, German mathematician and historian (b. 1829)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and historian (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German mathematician and historian (b. 1829)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Lebanese-American poet, painter, and philosopher (b. 1883)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese-American poet, painter, and philosopher (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese-American poet, painter, and philosopher (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian novelist (b. 1851)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian novelist (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian novelist (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American cornet player and bandleader (b. 1885)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">King <PERSON></a>, American cornet player and bandleader (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">King <PERSON></a>, American cornet player and bandleader (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Danish actor and director (b. 1881)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8m\" title=\"<PERSON>\"><PERSON></a>, Danish actor and director (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8m\" title=\"<PERSON>\"><PERSON></a>, Danish actor and director (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%B8m"}]}, {"year": "1943", "text": "<PERSON>, Estonian-German sailor and engineer (b. 1898)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German sailor and engineer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German sailor and engineer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Dutch printer and typographer (b. 1882)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch printer and typographer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch printer and typographer (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English-American lieutenant and author (b. 1887)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lieutenant and author (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lieutenant and author (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Turkish field marshal and politician, 2nd Prime Minister of Turkey (b. 1876)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Fevzi_%C3%87akmak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish field marshal and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fe<PERSON><PERSON>_%C3%87akmak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish field marshal and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fevzi_%C3%87akmak"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "1954", "text": "<PERSON>, French director and producer (b. 1862)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, French director and producer (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, French director and producer (b. 1862)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1954", "text": "<PERSON>, Norwegian speed skater (b. 1888)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian speed skater (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian speed skater (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, French priest, theologian, and philosopher (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, theologian, and philosopher (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, theologian, and philosopher (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter (b. 1928)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, French director and screenwriter (b. 1903)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Hungarian-American director, producer, and screenwriter (b. 1886)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American director, producer, and screenwriter (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American director, producer, and screenwriter (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Scottish artist and musician (b. 1940)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish artist and musician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish artist and musician (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American race car driver, founded Casner Motor Racing Division (b. 1928)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ner_Motor_Racing_Division\" title=\"Casner Motor Racing Division\">Casner Motor Racing Division</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ner_Motor_Racing_Division\" title=\"Casner Motor Racing Division\">Casner Motor Racing Division</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Casner Motor Racing Division", "link": "https://wikipedia.org/wiki/<PERSON>asner_Motor_Racing_Division"}]}, {"year": "1965", "text": "<PERSON>, American actress (b. 1923)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English soldier, novelist, journalist and critic (b. 1903)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, novelist, journalist and critic (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, novelist, journalist and critic (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Latvian lieutenant and politician (b. 1899)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%86%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian lieutenant and politician (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%86%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian lieutenant and politician (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gustavs_Celmi%C5%86%C5%A1"}]}, {"year": "1969", "text": "<PERSON>, American businessman (b. 1893)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Earl\"><PERSON></a>, American businessman (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American photographer (b. 1903)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress (b. 1890)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian politician (b. 1901)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Hjalmar_M%C3%A4e\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hjalmar_M%C3%A4e\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hjalmar_M%C3%A4e"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Italian pianist, composer, and conductor (b. 1911)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, composer, and conductor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, composer, and conductor (b. 1911)", "links": [{"title": "<PERSON>no <PERSON>", "link": "https://wikipedia.org/wiki/Nino_Rota"}]}, {"year": "1980", "text": "<PERSON>, American actress and singer (b. 1919)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American author, philosopher and civil rights activist (b. 1899)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, philosopher and civil rights activist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, philosopher and civil rights activist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Palestinian activist (b. 1935)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Issa<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian activist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Issa<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian activist (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Issam_<PERSON>wi"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Greek chieftain of the Macedonian Struggle (b. 1880)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ver<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek chieftain of the Macedonian Struggle (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ver<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek chieftain of the Macedonian Struggle (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter (b. 1948)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek Cypriot politician (b. 1908)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek Cypriot politician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek Cypriot politician (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor (b. 1955)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English guitarist and producer (b. 1948)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and producer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and producer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress (b. 1900)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American comedian and actor (b. 1953)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, South African activist and politician (b. 1942)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist and politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Jr., American lawyer, judge, and politician (b. 1924)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer, judge, and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer, judge, and politician (b. 1924)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Indian politician, 4th Prime Minister of India (b. 1896)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1997", "text": "<PERSON>, American author and academic (b. 1945)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON> of Athens, Greek archbishop (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/1998\" title=\"1998\">1998</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Athens\" title=\"<PERSON><PERSON><PERSON> of Athens\"><PERSON><PERSON><PERSON> of Athens</a>, Greek archbishop (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1998\" title=\"1998\">1998</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Athens\" title=\"<PERSON><PERSON><PERSON> of Athens\"><PERSON><PERSON><PERSON> of Athens</a>, Greek archbishop (b. 1913)", "links": [{"title": "1998", "link": "https://wikipedia.org/wiki/1998"}, {"title": "<PERSON><PERSON><PERSON> of Athens", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Athens"}]}, {"year": "1999", "text": "<PERSON>, German-American biochemist and physician (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and physician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and physician (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actress and voice artist (b. 1919)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English actor and screenwriter (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter (b. 1920)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2000", "text": "<PERSON>, American actor (b. 1939)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American singer (b. 1943)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Eva\" title=\"<PERSON> Eva\"><PERSON> Eva</a>, American singer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Eva\" title=\"Little Eva\"><PERSON> Eva</a>, American singer (b. 1943)", "links": [{"title": "Little Eva", "link": "https://wikipedia.org/wiki/<PERSON>_Eva"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Polish singer-songwriter, guitarist, and poet (b. 1957)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish singer-songwriter, guitarist, and poet (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish singer-songwriter, guitarist, and poet (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish businessman and philanthropist, founded Sabancı Holding (b. 1933)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Sak%C4%B1p_Sabanc%C4%B1\" title=\"Sakıp Sabancı\"><PERSON><PERSON><PERSON><PERSON>bancı</a>, Turkish businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Sabanc%C4%B1_Holding\" title=\"Sabancı Holding\">Sabancı Holding</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sak%C4%B1p_Sabanc%C4%B1\" title=\"Sakıp Sabancı\"><PERSON><PERSON><PERSON><PERSON>bancı</a>, Turkish businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Sabanc%C4%B1_Holding\" title=\"Sabancı Holding\">Sabancı Holding</a> (b. 1933)", "links": [{"title": "Sakıp Sabancı", "link": "https://wikipedia.org/wiki/Sak%C4%B1p_Sabanc%C4%B1"}, {"title": "Sabancı Holding", "link": "https://wikipedia.org/wiki/Sabanc%C4%B1_Holding"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Austrian violinist (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian violinist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian violinist (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American drummer (b. 1970)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer (b. 1970)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2005", "text": "Archbishop <PERSON><PERSON><PERSON><PERSON> of America (b. 1911)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON><PERSON>_of_America\" title=\"Archbishop <PERSON><PERSON><PERSON><PERSON> of America\">Archbishop <PERSON><PERSON><PERSON><PERSON> of America</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON><PERSON>_of_America\" title=\"Archbishop <PERSON><PERSON><PERSON><PERSON> of America\">Archbishop <PERSON><PERSON><PERSON><PERSON> of America</a> (b. 1911)", "links": [{"title": "Archbishop <PERSON><PERSON><PERSON><PERSON> of America", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_of_America"}]}, {"year": "2005", "text": "<PERSON>, American football player (b. 1978)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1978)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2005", "text": "<PERSON>, Dutch singer-songwriter (b. 1948)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Wally_Tax\" title=\"Wally Tax\"><PERSON></a>, Dutch singer-songwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wally_Tax\" title=\"Wally Tax\"><PERSON></a>, Dutch singer-songwriter (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Tax"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek poet and translator (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek poet and translator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek poet and translator (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kle<PERSON>s_K<PERSON>ou"}]}, {"year": "2007", "text": "<PERSON>, French-Canadian biologist and academic (b. 1910)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian biologist and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian biologist and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Dakota_Staton\" title=\"Dakota Staton\"><PERSON></a>, American singer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dakota_Staton\" title=\"Dakota Staton\"><PERSON></a>, American singer (b. 1930)", "links": [{"title": "Dakota Staton", "link": "https://wikipedia.org/wiki/Dakota_Staton"}]}, {"year": "2009", "text": "<PERSON>, American poet and educator (b. 1950)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Greek politician (b. 1940)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "Casualties in the 2010 Polish Air Force Tu-154 crash included:\n<PERSON><PERSON><PERSON>, Polish soldier and politician, 6th President of the Republic of Poland (b. 1919)\n<PERSON>, Polish economist, First Lady of Poland (b. 1942)\n<PERSON><PERSON>, Polish lawyer and politician, 4th President of Poland (b. 1949)\n<PERSON>, Ukrainian-Polish journalist and activist (b. 1929)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/List_of_casualties_of_the_Smolensk_air_disaster\" title=\"List of casualties of the Smolensk air disaster\">Casualties in the 2010 Polish Air Force Tu-154 crash included</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"President of the Republic of Poland\">President of the Republic of Poland</a> (b. 1919)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Maria_<PERSON>%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish economist, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Poland\" title=\"First Lady of Poland\">First Lady of Poland</a> (b. 1942)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Lech_Ka<PERSON>y%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON>ch <PERSON>czy<PERSON></a>, Polish lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Anna_Walentynowicz\" title=\"Anna Walentynowicz\">Anna Walentynowicz</a>, Ukrainian-Polish journalist and activist (b. 1929)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_casualties_of_the_Smolensk_air_disaster\" title=\"List of casualties of the Smolensk air disaster\">Casualties in the 2010 Polish Air Force Tu-154 crash included</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"President of the Republic of Poland\">President of the Republic of Poland</a> (b. 1919)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Maria_<PERSON>y%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish economist, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Poland\" title=\"First Lady of Poland\">First Lady of Poland</a> (b. 1942)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Lech_Kaczy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON>ch <PERSON>czy<PERSON></a>, Polish lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Anna_Walentynowicz\" title=\"Anna Walentynowicz\">Anna Walentynowicz</a>, Ukrainian-Polish journalist and activist (b. 1929)</li>\n</ul>", "links": [{"title": "List of casualties of the Smolensk air disaster", "link": "https://wikipedia.org/wiki/List_of_casualties_of_the_Smolensk_air_disaster"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Republic of Poland", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Poland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Kaczy%C5%84ska"}, {"title": "First Lady of Poland", "link": "https://wikipedia.org/wiki/First_Lady_of_Poland"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lech_Kaczy%C5%84ski"}, {"title": "President of Poland", "link": "https://wikipedia.org/wiki/President_of_Poland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>owicz"}]}, {"year": "<PERSON><PERSON><PERSON>, Polish soldier and politician, 6th President of the Republic of Poland (b. 1919)", "text": null, "html": "<PERSON><PERSON><PERSON>, Polish soldier and politician, 6th President of the Republic of Poland (b. 1919) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier and politician, 6th <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/President_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"President of the Republic of Poland\">President of the Republic of Poland</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier and politician, 6th <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/President_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"President of the Republic of Poland\">President of the Republic of Poland</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Republic of Poland", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/President_of_the_Republic_of_Poland"}]}, {"year": "<PERSON>, Polish economist, First Lady of Poland (b. 1942)", "text": null, "html": "<PERSON>, Polish economist, First Lady of Poland (b. 1942) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish economist, <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/First_Lady_of_Poland\" title=\"First Lady of Poland\">First Lady of Poland</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish economist, <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/First_Lady_of_Poland\" title=\"First Lady of Poland\">First Lady of Poland</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Maria_<PERSON>%C5%84ska"}, {"title": "First Lady of Poland", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/First_Lady_of_Poland"}]}, {"year": "<PERSON><PERSON>, Polish lawyer and politician, 4th President of Poland (b. 1949)", "text": null, "html": "<PERSON><PERSON>, Polish lawyer and politician, 4th President of Poland (b. 1949) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Le<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish lawyer and politician, 4th <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Le<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish lawyer and politician, 4th <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Lech_Ka<PERSON>y%C5%84ski"}, {"title": "President of Poland", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/President_of_Poland"}]}, {"year": "<PERSON>, Ukrainian-Polish journalist and activist (b. 1929)", "text": null, "html": "<PERSON>, Ukrainian-Polish journalist and activist (b. 1929) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Polish journalist and activist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Polish journalist and activist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actress and singer (b. 1939)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Carter\"><PERSON></a>, American actress and singer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French engineer and activist (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer and activist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer and activist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, German theremin player and composer (b. 1959)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Theremin\" title=\"Theremin\">theremin</a> player and composer (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Theremin\" title=\"Theremin\">theremin</a> player and composer (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Theremin", "link": "https://wikipedia.org/wiki/Theremin"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Armenian-American operatic singer (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian-American operatic singer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian-American operatic singer (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Puerto Rican cardinal (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mart%C3%ADnez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican cardinal (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADnez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican cardinal (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Aponte_Mart%C3%ADnez"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Nigerian lawyer and politician (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and politician (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Omoboriowo"}]}, {"year": "2013", "text": "<PERSON>, Italian cardinal (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French sociologist and academic (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist and academic (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist and academic (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Bangladeshi activist (b. 1911)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Binod_Bihari_Chowdhury\" title=\"Binod Bihari Chowdhury\">Binod <PERSON><PERSON></a>, Bangladeshi activist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Binod_Bihari_Chowdhury\" title=\"Binod Bihari Chowdhury\"><PERSON><PERSON> Bihar<PERSON></a>, Bangladeshi activist (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Binod_Bihari_Chowdhury"}]}, {"year": "2013", "text": "<PERSON>, English physiologist and academic, Nobel Prize laureate (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physiologist)\" title=\"<PERSON> (physiologist)\"><PERSON></a>, English physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physiologist)\" title=\"<PERSON> (physiologist)\"><PERSON></a>, English physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1925)", "links": [{"title": "<PERSON> (physiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(physiologist)"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2013", "text": "<PERSON>, Jamaican anthropologist, musicologist, and author (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican anthropologist, musicologist, and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican anthropologist, musicologist, and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English cyclist (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist (b. 1921)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)"}]}, {"year": "2013", "text": "<PERSON>, German long jumper (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German long jumper (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German long jumper (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oigt"}]}, {"year": "2014", "text": "<PERSON>, French journalist and politician (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian lawyer and politician, 37th Canadian Minister of Finance (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Canada)\" title=\"Minister of Finance (Canada)\">Canadian Minister of Finance</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Canada)\" title=\"Minister of Finance (Canada)\">Canadian Minister of Finance</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Finance (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Canada)"}]}, {"year": "2014", "text": "<PERSON>, English author and academic (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English author and playwright (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian cricketer and sportscaster (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Mexican-American politician and diplomat, 14th Governor of Arizona (b. 1916)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American politician and diplomat, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Arizona\" class=\"mw-redirect\" title=\"Governor of Arizona\">Governor of Arizona</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American politician and diplomat, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Arizona\" class=\"mw-redirect\" title=\"Governor of Arizona\">Governor of Arizona</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_H%C3%A9ctor_Castro"}, {"title": "Governor of Arizona", "link": "https://wikipedia.org/wiki/Governor_of_Arizona"}]}, {"year": "2015", "text": "<PERSON>, German-American actress and director, co-founded The Living Theatre (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actress and director, co-founded <a href=\"https://wikipedia.org/wiki/The_Living_Theatre\" title=\"The Living Theatre\">The Living Theatre</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actress and director, co-founded <a href=\"https://wikipedia.org/wiki/The_Living_Theatre\" title=\"The Living Theatre\">The Living Theatre</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Living Theatre", "link": "https://wikipedia.org/wiki/The_Living_Theatre"}]}, {"year": "2015", "text": "<PERSON>, Gabonese lawyer and politician, President of Gabon (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"Rose Fr<PERSON>ogo<PERSON>\"><PERSON></a>, Gabonese lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Gabon\" class=\"mw-redirect\" title=\"List of heads of state of Gabon\">President of Gabon</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"Rose Fr<PERSON>\"><PERSON></a>, Gabonese lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Gabon\" class=\"mw-redirect\" title=\"List of heads of state of Gabon\">President of Gabon</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_Francine_Rogomb%C3%A9"}, {"title": "List of heads of state of Gabon", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Gabon"}]}, {"year": "2015", "text": "<PERSON>, Australian farmer and politician, 6th Australian Minister for Finance (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian farmer and politician, 6th <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Australia)\" title=\"Minister for Finance (Australia)\">Australian Minister for Finance</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian farmer and politician, 6th <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Australia)\" title=\"Minister for Finance (Australia)\">Australian Minister for Finance</a> (b. 1935)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_politician)"}, {"title": "Minister for Finance (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Finance_(Australia)"}]}, {"year": "2016", "text": "<PERSON>, Welsh cannabis smuggler, writer, and legalisation campaigner (b. 1945)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh cannabis smuggler, writer, and legalisation campaigner (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh cannabis smuggler, writer, and legalisation campaigner (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American cartoonist (b. 1921)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1921)", "links": [{"title": "Al Jaffee", "link": "https://wikipedia.org/wiki/Al_Jaffee"}]}, {"year": "2024", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player, actor, and broadcaster (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player, actor, and broadcaster (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player, actor, and broadcaster (b. 1947)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}]}}