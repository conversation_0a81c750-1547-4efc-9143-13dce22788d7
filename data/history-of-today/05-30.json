{"date": "May 30", "url": "https://wikipedia.org/wiki/May_30", "data": {"Events": [{"year": "70", "text": "Siege of Jerusalem: <PERSON> and his Roman legions breach the Second Wall of Jerusalem. Jewish defenders retreat to the First Wall. The Romans build a circumvallation, cutting down all trees within fifteen kilometres (9.3 mi).", "html": "70 - <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)\" class=\"mw-redirect\" title=\"Siege of Jerusalem (AD 70)\">Siege of Jerusalem</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his <a href=\"https://wikipedia.org/wiki/Roman_legion\" title=\"Roman legion\">Roman legions</a> breach the Second Wall of <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>. Jewish defenders retreat to the First Wall. The Romans build a <a href=\"https://wikipedia.org/wiki/Investment_(military)\" title=\"Investment (military)\">circumvallation</a>, cutting down all trees within fifteen kilometres (9.3 mi).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)\" class=\"mw-redirect\" title=\"Siege of Jerusalem (AD 70)\">Siege of Jerusalem</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his <a href=\"https://wikipedia.org/wiki/Roman_legion\" title=\"Roman legion\">Roman legions</a> breach the Second Wall of <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>. Jewish defenders retreat to the First Wall. The Romans build a <a href=\"https://wikipedia.org/wiki/Investment_(military)\" title=\"Investment (military)\">circumvallation</a>, cutting down all trees within fifteen kilometres (9.3 mi).", "links": [{"title": "Siege of Jerusalem (AD 70)", "link": "https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Titus"}, {"title": "Roman legion", "link": "https://wikipedia.org/wiki/Roman_legion"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}, {"title": "Investment (military)", "link": "https://wikipedia.org/wiki/Investment_(military)"}]}, {"year": "1381", "text": "Beginning of the Peasants' Revolt in England.", "html": "1381 - Beginning of the <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a> in England.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a> in England.", "links": [{"title": "Peasants' Revolt", "link": "https://wikipedia.org/wiki/Peasants%27_<PERSON>olt"}]}, {"year": "1416", "text": "The Council of Constance, called by Emperor <PERSON><PERSON><PERSON>, a supporter of Antipope <PERSON>, burns <PERSON> of Prague following a trial for heresy.", "html": "1416 - The <a href=\"https://wikipedia.org/wiki/Council_of_Constance\" title=\"Council of Constance\">Council of Constance</a>, called by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON><PERSON><PERSON>, Holy Roman Emperor\">Emperor <PERSON><PERSON><PERSON></a>, a supporter of <a href=\"https://wikipedia.org/wiki/Antipope_John_XXIII\" title=\"Antipope John XXIII\">Antipope John XXIII</a>, burns <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Prague\" title=\"<PERSON> of Prague\"><PERSON> of Prague</a> following a trial for <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heresy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Constance\" title=\"Council of Constance\">Council of Constance</a>, called by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON><PERSON><PERSON>, Holy Roman Emperor\">Emperor <PERSON><PERSON><PERSON></a>, a supporter of <a href=\"https://wikipedia.org/wiki/Antipope_John_XXIII\" title=\"Antipope John XXIII\">Antipope John XXIII</a>, burns <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Prague\" title=\"<PERSON> of Prague\"><PERSON> of Prague</a> following a trial for <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heresy</a>.", "links": [{"title": "Council of Constance", "link": "https://wikipedia.org/wiki/Council_of_Constance"}, {"title": "<PERSON><PERSON><PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Holy_Roman_Emperor"}, {"title": "Antipope John XXIII", "link": "https://wikipedia.org/wiki/Antipope_John_XXIII"}, {"title": "Jerome of Prague", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Prague"}, {"title": "Heresy in Christianity", "link": "https://wikipedia.org/wiki/Heresy_in_Christianity"}]}, {"year": "1431", "text": "Hundred Years' War: In Rouen, France, the 19-year-old <PERSON> Arc is burned at the stake by an English-dominated tribunal.", "html": "1431 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: In <a href=\"https://wikipedia.org/wiki/Rouen\" title=\"Rouen\">Rouen</a>, France, the 19-year-old <a href=\"https://wikipedia.org/wiki/Joan_<PERSON>_Arc\" title=\"Joan of Arc\">Joan of Arc</a> is <a href=\"https://wikipedia.org/wiki/Death_by_burning\" title=\"Death by burning\">burned at the stake</a> by an English-dominated tribunal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: In <a href=\"https://wikipedia.org/wiki/Rouen\" title=\"Rouen\">Rouen</a>, France, the 19-year-old <a href=\"https://wikipedia.org/wiki/Joan_of_Arc\" title=\"Joan of Arc\">Joan of Arc</a> is <a href=\"https://wikipedia.org/wiki/Death_by_burning\" title=\"Death by burning\">burned at the stake</a> by an English-dominated tribunal.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "Rouen", "link": "https://wikipedia.org/wiki/Rouen"}, {"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Death by burning", "link": "https://wikipedia.org/wiki/Death_by_burning"}]}, {"year": "1434", "text": "Hussite Wars: Battle of Lipany: Effectively ending the war, Utraquist forces led by <PERSON><PERSON><PERSON> of Miletínek defeat and almost annihilate Taborite forces led by <PERSON><PERSON><PERSON> the Great.", "html": "1434 - <a href=\"https://wikipedia.org/wiki/Hussite_Wars\" title=\"Hussite Wars\">Hussite Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Lipany\" title=\"Battle of Lipany\">Battle of Lipany</a>: Effectively ending the war, <a href=\"https://wikipedia.org/wiki/Utraquist\" class=\"mw-redirect\" title=\"Utraquist\">Utraquist</a> forces led by <PERSON><PERSON><PERSON> of Miletínek defeat and almost annihilate <a href=\"https://wikipedia.org/wiki/Taborite\" class=\"mw-redirect\" title=\"Taborite\">Taborite</a> forces led by <a href=\"https://wikipedia.org/wiki/Prokop_the_Great\" title=\"Proko<PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hussite_Wars\" title=\"Hussite Wars\">Hussite Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Lipany\" title=\"Battle of Lipany\">Battle of Lipany</a>: Effectively ending the war, <a href=\"https://wikipedia.org/wiki/Utraquist\" class=\"mw-redirect\" title=\"Utraquist\">Utraquist</a> forces led by <PERSON><PERSON><PERSON> of Miletínek defeat and almost annihilate <a href=\"https://wikipedia.org/wiki/Taborite\" class=\"mw-redirect\" title=\"Taborite\">Taborite</a> forces led by <a href=\"https://wikipedia.org/wiki/Prokop_the_Great\" title=\"Prokop the Great\"><PERSON>ko<PERSON> the Great</a>.", "links": [{"title": "Hussite Wars", "link": "https://wikipedia.org/wiki/Hussite_Wars"}, {"title": "Battle of Lipany", "link": "https://wikipedia.org/wiki/Battle_of_Lipany"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Taborite", "link": "https://wikipedia.org/wiki/Taborite"}, {"title": "Prokop the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great"}]}, {"year": "1510", "text": "During the reign of the <PERSON><PERSON> Emperor, Ming dynasty rebel leader <PERSON> is defeated by commander <PERSON><PERSON>, ending the Prince of Anhua rebellion.", "html": "1510 - During the reign of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor\" title=\"Zhengde Emperor\">Zhengde Emperor</a>, <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is defeated by commander <PERSON><PERSON>, ending the <a href=\"https://wikipedia.org/wiki/Prince_of_Anhua_rebellion\" title=\"Prince of Anhua rebellion\">Prince of Anhua rebellion</a>.", "no_year_html": "During the reign of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor\" title=\"Zhengde Emperor\">Zhengde Emperor</a>, <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is defeated by commander <PERSON><PERSON>, ending the <a href=\"https://wikipedia.org/wiki/Prince_of_Anhua_rebellion\" title=\"Prince of Anhua rebellion\">Prince of Anhua rebellion</a>.", "links": [{"title": "<PERSON>de Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prince of Anhua rebellion", "link": "https://wikipedia.org/wiki/Prince_of_Anhua_rebellion"}]}, {"year": "1536", "text": "King <PERSON> of England marries <PERSON>, a lady-in-waiting to his first two wives.", "html": "1536 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Lady-in-waiting\" title=\"Lady-in-waiting\">lady-in-waiting</a> to his first two wives.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Lady-in-waiting\" title=\"Lady-in-waiting\">lady-in-waiting</a> to his first two wives.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lady-in-waiting", "link": "https://wikipedia.org/wiki/Lady-in-waiting"}]}, {"year": "1539", "text": "In Florida, <PERSON><PERSON><PERSON> lands at Tampa Bay with 600 soldiers with the goal of finding gold.", "html": "1539 - In <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> lands at <a href=\"https://wikipedia.org/wiki/Tampa_Bay\" title=\"Tampa Bay\">Tampa Bay</a> with 600 soldiers with the goal of finding <a href=\"https://wikipedia.org/wiki/Gold\" title=\"Gold\">gold</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> lands at <a href=\"https://wikipedia.org/wiki/Tampa_Bay\" title=\"Tampa Bay\">Tampa Bay</a> with 600 soldiers with the goal of finding <a href=\"https://wikipedia.org/wiki/Gold\" title=\"Gold\">gold</a>.", "links": [{"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Tampa Bay", "link": "https://wikipedia.org/wiki/Tampa_Bay"}, {"title": "Gold", "link": "https://wikipedia.org/wiki/Gold"}]}, {"year": "1574", "text": "<PERSON> becomes King of France.", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON> III</a> becomes King of France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON> III</a> becomes King of France.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1588", "text": "The last ship of the Spanish Armada sets sail from Lisbon heading for the English Channel.", "html": "1588 - The last ship of the <a href=\"https://wikipedia.org/wiki/Spanish_Armada\" title=\"Spanish Armada\">Spanish Armada</a> sets sail from <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> heading for the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a>.", "no_year_html": "The last ship of the <a href=\"https://wikipedia.org/wiki/Spanish_Armada\" title=\"Spanish Armada\">Spanish Armada</a> sets sail from <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> heading for the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a>.", "links": [{"title": "Spanish Armada", "link": "https://wikipedia.org/wiki/Spanish_Armada"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}]}, {"year": "1631", "text": "Publication of Gazette de France, the first French newspaper.", "html": "1631 - Publication of <i><a href=\"https://wikipedia.org/wiki/Gazette_de_France\" class=\"mw-redirect\" title=\"Gazette de France\">Gazette de France</a></i>, the first French newspaper.", "no_year_html": "Publication of <i><a href=\"https://wikipedia.org/wiki/Gazette_de_France\" class=\"mw-redirect\" title=\"Gazette de France\">Gazette de France</a></i>, the first French newspaper.", "links": [{"title": "Gazette de France", "link": "https://wikipedia.org/wiki/Gazette_de_France"}]}, {"year": "1635", "text": "Thirty Years' War: The Peace of Prague is signed.", "html": "1635 - <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: The <a href=\"https://wikipedia.org/wiki/Peace_of_Prague_(1635)\" title=\"Peace of Prague (1635)\">Peace of Prague</a> is signed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: The <a href=\"https://wikipedia.org/wiki/Peace_of_Prague_(1635)\" title=\"Peace of Prague (1635)\">Peace of Prague</a> is signed.", "links": [{"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}, {"title": "Peace of Prague (1635)", "link": "https://wikipedia.org/wiki/Peace_of_Prague_(1635)"}]}, {"year": "1642", "text": "From this date all honors granted by <PERSON> of England are retroactively annulled by Parliament.", "html": "1642 - From this date all honors granted by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> of England</a> are retroactively <a href=\"https://wikipedia.org/wiki/List_of_Ordinances_and_Acts_of_the_Parliament_of_England,_1642%E2%80%9360\" class=\"mw-redirect\" title=\"List of Ordinances and Acts of the Parliament of England, 1642-60\">annulled by Parliament</a>.", "no_year_html": "From this date all honors granted by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> of England</a> are retroactively <a href=\"https://wikipedia.org/wiki/List_of_Ordinances_and_Acts_of_the_Parliament_of_England,_1642%E2%80%9360\" class=\"mw-redirect\" title=\"List of Ordinances and Acts of the Parliament of England, 1642-60\">annulled by Parliament</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "List of Ordinances and Acts of the Parliament of England, 1642-60", "link": "https://wikipedia.org/wiki/List_of_Ordinances_and_Acts_of_the_Parliament_of_England,_1642%E2%80%9360"}]}, {"year": "1723", "text": "<PERSON> assumed the office of <PERSON><PERSON><PERSON> in Leipzig, presenting his first new cantata, Die Elenden sollen essen, BWV 75, in the St. Nicholas Church on the first Sunday after Trinity.", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> assumed the office of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in Leipzig, presenting his first new cantata, <span title=\"German-language text\"><span lang=\"de\"><a href=\"https://wikipedia.org/wiki/Die_Elenden_sollen_essen,_BWV_75\" title=\"Die Elenden sollen essen, BWV 75\"><i>Die Elenden sollen essen</i>, BWV 75</a></span></span>, in the St. Nicholas Church on the first Sunday after <a href=\"https://wikipedia.org/wiki/Trinity_Sunday\" title=\"Trinity Sunday\">Trinity</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> assumed the office of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in Leipzig, presenting his first new cantata, <span title=\"German-language text\"><span lang=\"de\"><a href=\"https://wikipedia.org/wiki/Die_Elenden_sollen_essen,_BWV_75\" title=\"Die Elenden sollen essen, BWV 75\"><i>Die Elenden sollen essen</i>, BWV 75</a></span></span>, in the St. Nicholas Church on the first Sunday after <a href=\"https://wikipedia.org/wiki/Trinity_Sunday\" title=\"Trinity Sunday\">Trinity</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON> Elenden sollen essen, BWV 75", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_sollen_essen,_BWV_75"}, {"title": "Trinity Sunday", "link": "https://wikipedia.org/wiki/Trinity_Sunday"}]}, {"year": "1796", "text": "War of the First Coalition: In the Battle of Borghetto, <PERSON> manages to cross the Mincio River against the Austrian army. This crossing forces the Austrians to abandon Lombardy and retreat to the Tyrol, leaving the fortress of Mantua as the sole remaining stronghold in Northern Italy.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Borghetto\" title=\"Battle of Borghetto\">Battle of Borghetto</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Bonaparte\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> manages to cross the <a href=\"https://wikipedia.org/wiki/Mincio_River\" class=\"mw-redirect\" title=\"Mincio River\">Mincio River</a> against the Austrian army. This crossing forces the Austrians to abandon Lombardy and retreat to the Tyrol, leaving the fortress of Mantua as the sole remaining stronghold in Northern Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Borghetto\" title=\"Battle of Borghetto\">Battle of Borghetto</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> manages to cross the <a href=\"https://wikipedia.org/wiki/Mincio_River\" class=\"mw-redirect\" title=\"Mincio River\">Mincio River</a> against the Austrian army. This crossing forces the Austrians to abandon Lombardy and retreat to the Tyrol, leaving the fortress of Mantua as the sole remaining stronghold in Northern Italy.", "links": [{"title": "War of the First Coalition", "link": "https://wikipedia.org/wiki/War_of_the_First_Coalition"}, {"title": "Battle of Borghetto", "link": "https://wikipedia.org/wiki/Battle_of_Borghetto"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mincio River", "link": "https://wikipedia.org/wiki/Mincio_River"}]}, {"year": "1806", "text": "Future U.S. President <PERSON> kills <PERSON> in a duel.", "html": "1806 - Future U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> kills <a href=\"https://wikipedia.org/wiki/<PERSON>_(historical_figure)\" class=\"mw-redirect\" title=\"<PERSON> (historical figure)\"><PERSON></a> in a duel.", "no_year_html": "Future U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> kills <a href=\"https://wikipedia.org/wiki/<PERSON>_(historical_figure)\" class=\"mw-redirect\" title=\"<PERSON> (historical figure)\"><PERSON></a> in a duel.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (historical figure)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historical_figure)"}]}, {"year": "1814", "text": "The First Treaty of Paris is signed, returning the French frontiers to their 1792 extent, and restoring the House of Bourbon to power.", "html": "1814 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1814)\" title=\"Treaty of Paris (1814)\">First Treaty of Paris</a> is signed, returning the French frontiers to their 1792 extent, and restoring the <a href=\"https://wikipedia.org/wiki/House_of_Bourbon\" title=\"House of Bourbon\">House of Bourbon</a> to power.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1814)\" title=\"Treaty of Paris (1814)\">First Treaty of Paris</a> is signed, returning the French frontiers to their 1792 extent, and restoring the <a href=\"https://wikipedia.org/wiki/House_of_Bourbon\" title=\"House of Bourbon\">House of Bourbon</a> to power.", "links": [{"title": "Treaty of Paris (1814)", "link": "https://wikipedia.org/wiki/Treaty_of_Paris_(1814)"}, {"title": "House of Bourbon", "link": "https://wikipedia.org/wiki/House_of_Bourbon"}]}, {"year": "1815", "text": "The East Indiaman Arniston is wrecked during a storm at Waenhuiskrans, near Cape Agulhas, in present-day South Africa, with the loss of 372 lives.", "html": "1815 - The <a href=\"https://wikipedia.org/wiki/East_Indiaman\" title=\"East Indiaman\">East Indiaman</a> <i><a href=\"https://wikipedia.org/wiki/A<PERSON>ist<PERSON>_(East_Indiaman)\" title=\"<PERSON><PERSON><PERSON><PERSON> (East Indiaman)\"><PERSON><PERSON><PERSON><PERSON></a></i> is wrecked during a storm at <a href=\"https://wikipedia.org/wiki/Waenhuiskrans\" class=\"mw-redirect\" title=\"Waenhuiskrans\">Waenhuiskrans</a>, near <a href=\"https://wikipedia.org/wiki/Cape_Agulhas\" title=\"Cape Agulhas\">Cape Agulhas</a>, in present-day South Africa, with the loss of 372 lives.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/East_Indiaman\" title=\"East Indiaman\">East Indiaman</a> <i><a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_(East_Indiaman)\" title=\"<PERSON><PERSON><PERSON><PERSON> (East Indiaman)\"><PERSON><PERSON><PERSON><PERSON></a></i> is wrecked during a storm at <a href=\"https://wikipedia.org/wiki/Waenhuiskrans\" class=\"mw-redirect\" title=\"Waenhuiskrans\">Waenhuiskrans</a>, near <a href=\"https://wikipedia.org/wiki/Cape_Agulhas\" title=\"Cape Agulhas\">Cape Agulhas</a>, in present-day South Africa, with the loss of 372 lives.", "links": [{"title": "East Indiaman", "link": "https://wikipedia.org/wiki/East_Indiaman"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (East Indiaman)", "link": "https://wikipedia.org/wiki/Arnist<PERSON>_(East_Indiaman)"}, {"title": "Waenhuiskrans", "link": "https://wikipedia.org/wiki/Waenhuiskrans"}, {"title": "Cape Agulhas", "link": "https://wikipedia.org/wiki/Cape_Agulhas"}]}, {"year": "1834", "text": "Minister of Justice <PERSON><PERSON><PERSON><PERSON> issues a law seizing \"all convents, monasteries, colleges, hospices and any other houses\" from the Catholic religious orders in Portugal, earning him the nickname of \"The Friar-Killer\".", "html": "1834 - Minister of Justice <a href=\"https://wikipedia.org/wiki/Jo<PERSON>ui<PERSON>_Ant%C3%B3<PERSON>_de_Aguiar\" title=\"<PERSON><PERSON><PERSON><PERSON>ón<PERSON> Aguiar\"><PERSON><PERSON><PERSON><PERSON>iar</a> issues a law seizing \"all convents, monasteries, colleges, hospices and any other houses\" from the <a href=\"https://wikipedia.org/wiki/Catholic_religious_order\" class=\"mw-redirect\" title=\"Catholic religious order\">Catholic religious orders</a> in Portugal, earning him the nickname of \"The Friar-Killer\".", "no_year_html": "Minister of Justice <a href=\"https://wikipedia.org/wiki/Jo<PERSON>ui<PERSON>_Ant%C3%B3<PERSON>_de_Aguiar\" title=\"<PERSON><PERSON><PERSON><PERSON> An<PERSON>ón<PERSON> Aguiar\"><PERSON><PERSON><PERSON><PERSON>ón<PERSON> Aguiar</a> issues a law seizing \"all convents, monasteries, colleges, hospices and any other houses\" from the <a href=\"https://wikipedia.org/wiki/Catholic_religious_order\" class=\"mw-redirect\" title=\"Catholic religious order\">Catholic religious orders</a> in Portugal, earning him the nickname of \"The Friar-Killer\".", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaquim_Ant%C3%B3<PERSON>_de_A<PERSON>iar"}, {"title": "Catholic religious order", "link": "https://wikipedia.org/wiki/Catholic_religious_order"}]}, {"year": "1842", "text": "<PERSON> attempts to murder <PERSON> as she drives down Constitution Hill in London with <PERSON>.", "html": "1842 - <PERSON> attempts to murder <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> as she drives down <a href=\"https://wikipedia.org/wiki/Constitution_Hill,_London\" title=\"Constitution Hill, London\">Constitution Hill</a> in London with <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_Consort\" class=\"mw-redirect\" title=\"<PERSON>, Prince <PERSON>\">Prince <PERSON></a>.", "no_year_html": "<PERSON> attempts to murder <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> as she drives down <a href=\"https://wikipedia.org/wiki/Constitution_Hill,_London\" title=\"Constitution Hill, London\">Constitution Hill</a> in London with <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_Consort\" class=\"mw-redirect\" title=\"<PERSON>, Prince <PERSON>\">Prince <PERSON></a>.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "Constitution Hill, London", "link": "https://wikipedia.org/wiki/Constitution_Hill,_London"}, {"title": "<PERSON>, Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_Consort"}]}, {"year": "1845", "text": "The Fatel Razack coming from India, lands in the Gulf of Paria in Trinidad and Tobago carrying the first Indians to the country.", "html": "1845 - The <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ra<PERSON>k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> coming from <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">India</a>, lands in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Paria\" title=\"Gulf of Paria\">Gulf of Paria</a> in <a href=\"https://wikipedia.org/wiki/Trinidad_and_Tobago\" title=\"Trinidad and Tobago\">Trinidad and Tobago</a> carrying the first <a href=\"https://wikipedia.org/wiki/Indo-Trinidadians\" class=\"mw-redirect\" title=\"Indo-Trinidadians\">Indians</a> to the country.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> coming from <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">India</a>, lands in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Paria\" title=\"Gulf of Paria\">Gulf of Paria</a> in <a href=\"https://wikipedia.org/wiki/Trinidad_and_Tobago\" title=\"Trinidad and Tobago\">Trinidad and Tobago</a> carrying the first <a href=\"https://wikipedia.org/wiki/Indo-Trinidadians\" class=\"mw-redirect\" title=\"Indo-Trinidadians\">Indians</a> to the country.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k"}, {"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}, {"title": "Gulf of Paria", "link": "https://wikipedia.org/wiki/Gulf_of_Paria"}, {"title": "Trinidad and Tobago", "link": "https://wikipedia.org/wiki/Trinidad_and_Tobago"}, {"title": "Indo-Trinidadians", "link": "https://wikipedia.org/wiki/Indo-Trinidadians"}]}, {"year": "1854", "text": "The Kansas-Nebraska Act becomes law establishing the U.S. territories of Kansas and Nebraska.", "html": "1854 - The <a href=\"https://wikipedia.org/wiki/Kansas%E2%80%93Nebraska_Act\" title=\"Kansas-Nebraska Act\">Kansas-Nebraska Act</a> becomes law establishing the <a href=\"https://wikipedia.org/wiki/U.S._territories\" class=\"mw-redirect\" title=\"U.S. territories\">U.S. territories</a> of <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a> and <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kansas%E2%80%93Nebraska_Act\" title=\"Kansas-Nebraska Act\">Kansas-Nebraska Act</a> becomes law establishing the <a href=\"https://wikipedia.org/wiki/U.S._territories\" class=\"mw-redirect\" title=\"U.S. territories\">U.S. territories</a> of <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a> and <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>.", "links": [{"title": "Kansas-Nebraska Act", "link": "https://wikipedia.org/wiki/Kansas%E2%80%93Nebraska_Act"}, {"title": "U.S. territories", "link": "https://wikipedia.org/wiki/U.S._territories"}, {"title": "Kansas", "link": "https://wikipedia.org/wiki/Kansas"}, {"title": "Nebraska", "link": "https://wikipedia.org/wiki/Nebraska"}]}, {"year": "1866", "text": "<PERSON><PERSON>'s comic opera The Bartered Bride premiered in Prague.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Bedrich_Smetana\" class=\"mw-redirect\" title=\"Bedrich Smetana\">Bedrich Smetana</a>'s comic opera <i><a href=\"https://wikipedia.org/wiki/The_Bartered_Bride\" title=\"The Bartered Bride\">The Bartered Bride</a></i> premiered in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bedrich_Smetana\" class=\"mw-redirect\" title=\"Bedrich Smetana\">Bedrich Smetana</a>'s comic opera <i><a href=\"https://wikipedia.org/wiki/The_Bartered_Bride\" title=\"The Bartered Bride\">The Bartered Bride</a></i> premiered in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bedrich_Smetana"}, {"title": "The Bartered Bride", "link": "https://wikipedia.org/wiki/The_Bartered_Bride"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}]}, {"year": "1868", "text": "Decoration Day (the predecessor of the modern \"Memorial Day\") is observed in the United States for the first time after a proclamation by <PERSON>, head of the Grand Army of the Republic (a veterans group).", "html": "1868 - Decoration Day (the predecessor of the modern \"<a href=\"https://wikipedia.org/wiki/Memorial_Day\" title=\"Memorial Day\">Memorial Day</a>\") is observed in the United States for the first time after a proclamation by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head of the <a href=\"https://wikipedia.org/wiki/Grand_Army_of_the_Republic\" title=\"Grand Army of the Republic\">Grand Army of the Republic</a> (a veterans group).", "no_year_html": "Decoration Day (the predecessor of the modern \"<a href=\"https://wikipedia.org/wiki/Memorial_Day\" title=\"Memorial Day\">Memorial Day</a>\") is observed in the United States for the first time after a proclamation by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head of the <a href=\"https://wikipedia.org/wiki/Grand_Army_of_the_Republic\" title=\"Grand Army of the Republic\">Grand Army of the Republic</a> (a veterans group).", "links": [{"title": "Memorial Day", "link": "https://wikipedia.org/wiki/Memorial_Day"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Grand Army of the Republic", "link": "https://wikipedia.org/wiki/Grand_Army_of_the_Republic"}]}, {"year": "1876", "text": "Ottoman sultan <PERSON><PERSON><PERSON><PERSON><PERSON> is deposed and succeeded by his nephew <PERSON><PERSON>.", "html": "1876 - Ottoman sultan <a href=\"https://wikipedia.org/wiki/Abd%C3%BCla<PERSON>z\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is deposed and succeeded by his nephew <a href=\"https://wikipedia.org/wiki/Murad_V\" title=\"Murad V\"><PERSON><PERSON> V</a>.", "no_year_html": "Ottoman sultan <a href=\"https://wikipedia.org/wiki/Abd%C3%<PERSON><PERSON><PERSON>z\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is deposed and succeeded by his nephew <a href=\"https://wikipedia.org/wiki/Murad_V\" title=\"Murad V\"><PERSON><PERSON> V</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abd%C3%BClaziz"}, {"title": "<PERSON><PERSON> V", "link": "https://wikipedia.org/wiki/Murad_V"}]}, {"year": "1876", "text": "The secret decree of Ems Ukaz, issued by Russian Tsar <PERSON> in the German city of Bad Ems, was aimed at stopping the printing and distribution of Ukrainian-language publications in the Russian Empire.", "html": "1876 - The secret decree of <a href=\"https://wikipedia.org/wiki/Ems_Ukaz\" title=\"Ems Ukaz\">Ems Ukaz</a>, issued by Russian Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" title=\"Alexander II of Russia\"><PERSON> II</a> in the German city of <a href=\"https://wikipedia.org/wiki/Bad_Ems\" title=\"Bad Ems\">Bad Ems</a>, was aimed at stopping the printing and distribution of <a href=\"https://wikipedia.org/wiki/Ukrainian_language\" title=\"Ukrainian language\">Ukrainian-language</a> publications in the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>.", "no_year_html": "The secret decree of <a href=\"https://wikipedia.org/wiki/Ems_Ukaz\" title=\"Ems Ukaz\">Ems Ukaz</a>, issued by Russian Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" title=\"Alexander II of Russia\"><PERSON> II</a> in the German city of <a href=\"https://wikipedia.org/wiki/Bad_Ems\" title=\"Bad Ems\">Bad Ems</a>, was aimed at stopping the printing and distribution of <a href=\"https://wikipedia.org/wiki/Ukrainian_language\" title=\"Ukrainian language\">Ukrainian-language</a> publications in the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>.", "links": [{"title": "Ems Ukaz", "link": "https://wikipedia.org/wiki/Ems_U<PERSON>z"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "Bad Ems", "link": "https://wikipedia.org/wiki/Bad_Ems"}, {"title": "Ukrainian language", "link": "https://wikipedia.org/wiki/Ukrainian_language"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1883", "text": "In New York City, a stampede on the recently opened Brooklyn Bridge killed twelve people.", "html": "1883 - In New York City, a stampede on the recently opened <a href=\"https://wikipedia.org/wiki/Brooklyn_Bridge\" title=\"Brooklyn Bridge\">Brooklyn Bridge</a> killed twelve people.", "no_year_html": "In New York City, a stampede on the recently opened <a href=\"https://wikipedia.org/wiki/Brooklyn_Bridge\" title=\"Brooklyn Bridge\">Brooklyn Bridge</a> killed twelve people.", "links": [{"title": "Brooklyn Bridge", "link": "https://wikipedia.org/wiki/Brooklyn_Bridge"}]}, {"year": "1899", "text": "<PERSON>, a female outlaw of the Old West, robs a stage coach 30 miles southeast of Globe, Arizona.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a female outlaw of the <a href=\"https://wikipedia.org/wiki/American_frontier\" title=\"American frontier\">Old West</a>, robs a stage coach 30 miles southeast of <a href=\"https://wikipedia.org/wiki/Globe,_Arizona\" title=\"Globe, Arizona\">Globe, Arizona</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a female outlaw of the <a href=\"https://wikipedia.org/wiki/American_frontier\" title=\"American frontier\">Old West</a>, robs a stage coach 30 miles southeast of <a href=\"https://wikipedia.org/wiki/Globe,_Arizona\" title=\"Globe, Arizona\">Globe, Arizona</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American frontier", "link": "https://wikipedia.org/wiki/American_frontier"}, {"title": "Globe, Arizona", "link": "https://wikipedia.org/wiki/Globe,_Arizona"}]}, {"year": "1911", "text": "At the Indianapolis Motor Speedway, the first Indianapolis 500 ends with <PERSON> in his <PERSON><PERSON> becoming the first winner of the 500-mile auto race.", "html": "1911 - At the <a href=\"https://wikipedia.org/wiki/Indianapolis_Motor_Speedway\" title=\"Indianapolis Motor Speedway\">Indianapolis Motor Speedway</a>, the first <a href=\"https://wikipedia.org/wiki/Indianapolis_500\" title=\"Indianapolis 500\">Indianapolis 500</a> ends with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in his <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <i>Wasp</i></a> becoming the first winner of the 500-mile auto race.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Indianapolis_Motor_Speedway\" title=\"Indianapolis Motor Speedway\">Indianapolis Motor Speedway</a>, the first <a href=\"https://wikipedia.org/wiki/Indianapolis_500\" title=\"Indianapolis 500\">Indianapolis 500</a> ends with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in his <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <i><PERSON>p</i></a> becoming the first winner of the 500-mile auto race.", "links": [{"title": "Indianapolis Motor Speedway", "link": "https://wikipedia.org/wiki/Indianapolis_Motor_Speedway"}, {"title": "Indianapolis 500", "link": "https://wikipedia.org/wiki/Indianapolis_500"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "The Treaty of London is signed, ending the First Balkan War; Albania becomes an independent nation.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1913)\" title=\"Treaty of London (1913)\">Treaty of London</a> is signed, ending the <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>; <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a> becomes an independent nation.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1913)\" title=\"Treaty of London (1913)\">Treaty of London</a> is signed, ending the <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>; <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a> becomes an independent nation.", "links": [{"title": "Treaty of London (1913)", "link": "https://wikipedia.org/wiki/Treaty_of_London_(1913)"}, {"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Albania", "link": "https://wikipedia.org/wiki/Albania"}]}, {"year": "1914", "text": "The new, and then the largest, Cunard ocean liner RMS Aquitania, 45,647 tons, sets sails on her maiden voyage from Liverpool, England, to New York City.", "html": "1914 - The new, and then the largest, <a href=\"https://wikipedia.org/wiki/Cunard_Line\" title=\"Cunard Line\">Cunard</a> <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a> <a href=\"https://wikipedia.org/wiki/RMS_Aquitania\" title=\"RMS Aquitania\">RMS <i>Aquitania</i></a>, 45,647 tons, sets sails on her maiden voyage from <a href=\"https://wikipedia.org/wiki/Liverpool\" title=\"Liverpool\">Liverpool</a>, England, to New York City.", "no_year_html": "The new, and then the largest, <a href=\"https://wikipedia.org/wiki/Cunard_Line\" title=\"Cunard Line\">Cunard</a> <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a> <a href=\"https://wikipedia.org/wiki/RMS_Aquitania\" title=\"RMS Aquitania\">RMS <i>Aquitania</i></a>, 45,647 tons, sets sails on her maiden voyage from <a href=\"https://wikipedia.org/wiki/Liverpool\" title=\"Liverpool\">Liverpool</a>, England, to New York City.", "links": [{"title": "Cunard Line", "link": "https://wikipedia.org/wiki/Cunard_Line"}, {"title": "Ocean liner", "link": "https://wikipedia.org/wiki/Ocean_liner"}, {"title": "RMS Aquitania", "link": "https://wikipedia.org/wiki/RMS_Aquitania"}, {"title": "Liverpool", "link": "https://wikipedia.org/wiki/Liverpool"}]}, {"year": "1922", "text": "The Lincoln Memorial is dedicated in Washington, D.C..", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Lincoln_Memorial\" title=\"Lincoln Memorial\">Lincoln Memorial</a> is dedicated in Washington, D.C..", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lincoln_Memorial\" title=\"Lincoln Memorial\">Lincoln Memorial</a> is dedicated in Washington, D.C..", "links": [{"title": "Lincoln Memorial", "link": "https://wikipedia.org/wiki/Lincoln_Memorial"}]}, {"year": "1925", "text": "May Thirtieth Movement: Shanghai Municipal Police Force shoot and kill 13 protesting workers.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/May_Thirtieth_Movement\" title=\"May Thirtieth Movement\">May Thirtieth Movement</a>: <a href=\"https://wikipedia.org/wiki/Shanghai_Municipal_Police\" title=\"Shanghai Municipal Police\">Shanghai Municipal Police</a> Force shoot and kill 13 protesting workers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_Thirtieth_Movement\" title=\"May Thirtieth Movement\">May Thirtieth Movement</a>: <a href=\"https://wikipedia.org/wiki/Shanghai_Municipal_Police\" title=\"Shanghai Municipal Police\">Shanghai Municipal Police</a> Force shoot and kill 13 protesting workers.", "links": [{"title": "May Thirtieth Movement", "link": "https://wikipedia.org/wiki/May_Thirtieth_Movement"}, {"title": "Shanghai Municipal Police", "link": "https://wikipedia.org/wiki/Shanghai_Municipal_Police"}]}, {"year": "1937", "text": "Memorial Day massacre: Chicago police shoot and kill ten labor demonstrators.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Memorial_Day_massacre_of_1937\" class=\"mw-redirect\" title=\"Memorial Day massacre of 1937\">Memorial Day massacre</a>: Chicago police shoot and kill ten labor demonstrators.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Memorial_Day_massacre_of_1937\" class=\"mw-redirect\" title=\"Memorial Day massacre of 1937\">Memorial Day massacre</a>: Chicago police shoot and kill ten labor demonstrators.", "links": [{"title": "Memorial Day massacre of 1937", "link": "https://wikipedia.org/wiki/Memorial_Day_massacre_of_1937"}]}, {"year": "1941", "text": "World War II: <PERSON><PERSON> G<PERSON>s and <PERSON><PERSON><PERSON><PERSON> climb the Athenian Acropolis and tear down the German flag.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Manolis_Glezos\" title=\"Manolis Glezos\">Manolis Glezos</a> and <a href=\"https://wikipedia.org/wiki/Apostolos_Santas\" title=\"Apostolos Santas\">Apostolos Santas</a> climb the Athenian <a href=\"https://wikipedia.org/wiki/Acropolis\" title=\"Acropolis\">Acropolis</a> and tear down the German flag.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Manolis_Glezos\" title=\"Manolis Glezos\">Manolis Glezos</a> and <a href=\"https://wikipedia.org/wiki/Apostolos_Santas\" title=\"Apostolos Santas\">Apostolos Santas</a> climb the Athenian <a href=\"https://wikipedia.org/wiki/Acropolis\" title=\"Acropolis\">Acropolis</a> and tear down the German flag.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Manolis Glezos", "link": "https://wikipedia.org/wiki/Man<PERSON>_Glezos"}, {"title": "Apostolos Santas", "link": "https://wikipedia.org/wiki/Apostolos_Santas"}, {"title": "Acropolis", "link": "https://wikipedia.org/wiki/Acropolis"}]}, {"year": "1942", "text": "World War II: One thousand British bombers launch a 90-minute attack on Cologne, Germany.", "html": "1942 - World War II: One thousand British bombers launch a <a href=\"https://wikipedia.org/wiki/Bombing_of_Cologne_in_World_War_II\" title=\"Bombing of Cologne in World War II\">90-minute attack on Cologne</a>, Germany.", "no_year_html": "World War II: One thousand British bombers launch a <a href=\"https://wikipedia.org/wiki/Bombing_of_Cologne_in_World_War_II\" title=\"Bombing of Cologne in World War II\">90-minute attack on Cologne</a>, Germany.", "links": [{"title": "Bombing of Cologne in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Cologne_in_World_War_II"}]}, {"year": "1943", "text": "The Holocaust: <PERSON> becomes chief medical officer of the Zigeunerfamilienlager (Romani family camp) at Auschwitz concentration camp.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes chief medical officer of the <i>Zigeunerfamilienlager</i> (<a href=\"https://wikipedia.org/wiki/Romani_people\" title=\"Romani people\">Romani</a> family camp) at <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes chief medical officer of the <i>Zigeunerfamilienlager</i> (<a href=\"https://wikipedia.org/wiki/Romani_people\" title=\"Romani people\">Romani</a> family camp) at <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Romani people", "link": "https://wikipedia.org/wiki/Romani_people"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1948", "text": "A dike along the flooding Columbia River breaks, obliterating Vanport, Oregon within minutes. Fifteen people die and tens of thousands are left homeless.", "html": "1948 - A dike along the <a href=\"https://wikipedia.org/wiki/1948_Columbia_River_flood\" title=\"1948 Columbia River flood\">flooding</a> <a href=\"https://wikipedia.org/wiki/Columbia_River\" title=\"Columbia River\">Columbia River</a> breaks, obliterating <a href=\"https://wikipedia.org/wiki/Vanport,_Oregon\" title=\"Vanport, Oregon\">Vanport, Oregon</a> within minutes. Fifteen people die and tens of thousands are left homeless.", "no_year_html": "A dike along the <a href=\"https://wikipedia.org/wiki/1948_Columbia_River_flood\" title=\"1948 Columbia River flood\">flooding</a> <a href=\"https://wikipedia.org/wiki/Columbia_River\" title=\"Columbia River\">Columbia River</a> breaks, obliterating <a href=\"https://wikipedia.org/wiki/Vanport,_Oregon\" title=\"Vanport, Oregon\">Vanport, Oregon</a> within minutes. Fifteen people die and tens of thousands are left homeless.", "links": [{"title": "1948 Columbia River flood", "link": "https://wikipedia.org/wiki/1948_Columbia_River_flood"}, {"title": "Columbia River", "link": "https://wikipedia.org/wiki/Columbia_River"}, {"title": "Vanport, Oregon", "link": "https://wikipedia.org/wiki/Vanport,_Oregon"}]}, {"year": "1958", "text": "Memorial Day: The remains of two unidentified American servicemen, killed in action during World War II and the Korean War respectively, are buried at the Tomb of the Unknown Soldier in Arlington National Cemetery.", "html": "1958 - Memorial Day: The remains of two unidentified American servicemen, killed in action during World War II and the <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a> respectively, are buried at the <a href=\"https://wikipedia.org/wiki/Tomb_of_the_Unknowns\" class=\"mw-redirect\" title=\"Tomb of the Unknowns\">Tomb of the Unknown Soldier</a> in <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a>.", "no_year_html": "Memorial Day: The remains of two unidentified American servicemen, killed in action during World War II and the <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a> respectively, are buried at the <a href=\"https://wikipedia.org/wiki/Tomb_of_the_Unknowns\" class=\"mw-redirect\" title=\"Tomb of the Unknowns\">Tomb of the Unknown Soldier</a> in <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Tomb of the Unknowns", "link": "https://wikipedia.org/wiki/Tomb_of_the_Unknowns"}, {"title": "Arlington National Cemetery", "link": "https://wikipedia.org/wiki/Arlington_National_Cemetery"}]}, {"year": "1959", "text": "The Auckland Harbour Bridge, crossing the Waitemata Harbour in Auckland, New Zealand, is officially opened by Governor-General <PERSON>, 10th Viscount <PERSON>.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Auckland_Harbour_Bridge\" title=\"Auckland Harbour Bridge\">Auckland Harbour Bridge</a>, crossing the <a href=\"https://wikipedia.org/wiki/Waitemat%C4%81_Harbour\" title=\"Waitematā Harbour\">Waitemata Harbour</a> in <a href=\"https://wikipedia.org/wiki/Auckland\" title=\"Auckland\">Auckland</a>, New Zealand, is officially opened by <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Viscount_<PERSON>\" title=\"<PERSON>, 10th Viscount <PERSON>\"><PERSON>, 10th Viscount <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Auckland_Harbour_Bridge\" title=\"Auckland Harbour Bridge\">Auckland Harbour Bridge</a>, crossing the <a href=\"https://wikipedia.org/wiki/Waitemat%C4%81_Harbour\" title=\"Waitematā Harbour\">Waitemata Harbour</a> in <a href=\"https://wikipedia.org/wiki/Auckland\" title=\"Auckland\">Auckland</a>, New Zealand, is officially opened by <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Viscount_<PERSON>\" title=\"<PERSON>, 10th Viscount <PERSON>\"><PERSON>, 10th Viscount <PERSON></a>.", "links": [{"title": "Auckland Harbour Bridge", "link": "https://wikipedia.org/wiki/Auckland_Harbour_Bridge"}, {"title": "Waitematā Harbour", "link": "https://wikipedia.org/wiki/Waitemat%C4%81_Harbour"}, {"title": "Auckland", "link": "https://wikipedia.org/wiki/Auckland"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}, {"title": "<PERSON>, 10th Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_10th_Viscount_<PERSON>"}]}, {"year": "1961", "text": "The long-time Dominican dictator <PERSON> is assassinated in Santo Domingo, Dominican Republic.", "html": "1961 - The long-time Dominican dictator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Santo_Domingo\" title=\"Santo Domingo\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a>.", "no_year_html": "The long-time Dominican dictator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Santo_Domingo\" title=\"Santo Domingo\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Santo Domingo", "link": "https://wikipedia.org/wiki/Santo_Domingo"}, {"title": "Dominican Republic", "link": "https://wikipedia.org/wiki/Dominican_Republic"}]}, {"year": "1961", "text": "Viasa Flight 897 crashes after takeoff from Lisbon Airport, killing 61.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Viasa_Flight_897\" title=\"Viasa Flight 897\">Viasa Flight 897</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Lisbon_Airport\" title=\"Lisbon Airport\">Lisbon Airport</a>, killing 61.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viasa_Flight_897\" title=\"Viasa Flight 897\">Viasa Flight 897</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Lisbon_Airport\" title=\"Lisbon Airport\">Lisbon Airport</a>, killing 61.", "links": [{"title": "Viasa Flight 897", "link": "https://wikipedia.org/wiki/Viasa_Flight_897"}, {"title": "Lisbon Airport", "link": "https://wikipedia.org/wiki/Lisbon_Airport"}]}, {"year": "1963", "text": "A protest against pro-Catholic discrimination during the Buddhist crisis is held outside South Vietnam's National Assembly, the first open demonstration during the eight-year presidency of <PERSON><PERSON>.", "html": "1963 - A protest against pro-Catholic discrimination during the <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a> is held outside <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>'s National Assembly, the first open demonstration during the eight-year presidency of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "A protest against pro-Catholic discrimination during the <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a> is held outside <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>'s National Assembly, the first open demonstration during the eight-year presidency of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Buddhist crisis", "link": "https://wikipedia.org/wiki/Buddhist_crisis"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "Former Congolese Prime Minister, <PERSON><PERSON><PERSON><PERSON>, and several other politicians are publicly executed in Kinshasa on the orders of President <PERSON>.", "html": "1966 - Former <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Congolese</a> Prime Minister, <a href=\"https://wikipedia.org/wiki/%C3%89variste_<PERSON><PERSON>\" title=\"<PERSON>varist<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, and several other politicians are publicly executed in <a href=\"https://wikipedia.org/wiki/Kinshasa\" title=\"Kinshasa\">Kinshasa</a> on the orders of President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a>.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Congolese</a> Prime Minister, <a href=\"https://wikipedia.org/wiki/%C3%89variste_<PERSON><PERSON>\" title=\"<PERSON>var<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, and several other politicians are publicly executed in <a href=\"https://wikipedia.org/wiki/Kinshasa\" title=\"Kinshasa\">Kinshasa</a> on the orders of President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a>.", "links": [{"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89variste_<PERSON><PERSON>"}, {"title": "Kinshasa", "link": "https://wikipedia.org/wiki/Kinshasa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "The Nigerian Eastern Region declares independence as the Republic of Biafra, sparking a civil war.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigerian</a> Eastern Region declares independence as the <a href=\"https://wikipedia.org/wiki/Biafra\" title=\"Biafra\">Republic of Biafra</a>, sparking a <a href=\"https://wikipedia.org/wiki/Nigerian_Civil_War\" title=\"Nigerian Civil War\">civil war</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigerian</a> Eastern Region declares independence as the <a href=\"https://wikipedia.org/wiki/Biafra\" title=\"Biafra\">Republic of Biafra</a>, sparking a <a href=\"https://wikipedia.org/wiki/Nigerian_Civil_War\" title=\"Nigerian Civil War\">civil war</a>.", "links": [{"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "Biafra", "link": "https://wikipedia.org/wiki/Biafra"}, {"title": "Nigerian Civil War", "link": "https://wikipedia.org/wiki/Nigerian_Civil_War"}]}, {"year": "1968", "text": "<PERSON> reappears publicly after his flight to Baden-Baden, West Germany, and dissolves the French National Assembly by a radio appeal. Immediately after, less than one million of his supporters march on the Champs-Élysées in Paris. This is the turning point of May 1968 events in France.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reappears publicly after his flight to <a href=\"https://wikipedia.org/wiki/Baden-Baden\" title=\"Baden-Baden\">Baden-Baden</a>, West Germany, and dissolves the French National Assembly by a radio appeal. Immediately after, less than one million of his supporters march on the <a href=\"https://wikipedia.org/wiki/Cha<PERSON>-%C3%89lys%C3%A9es\" title=\"Champs-Élysées\">Champs-Élysées</a> in Paris. This is the turning point of <a href=\"https://wikipedia.org/wiki/May_1968_events_in_France\" class=\"mw-redirect\" title=\"May 1968 events in France\">May 1968 events in France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reappears publicly after his flight to <a href=\"https://wikipedia.org/wiki/Baden-Baden\" title=\"Baden-Baden\">Baden-Baden</a>, West Germany, and dissolves the French National Assembly by a radio appeal. Immediately after, less than one million of his supporters march on the <a href=\"https://wikipedia.org/wiki/Cha<PERSON>-%C3%89lys%C3%A9es\" title=\"Champs-Élysées\">Champs-Élysées</a> in Paris. This is the turning point of <a href=\"https://wikipedia.org/wiki/May_1968_events_in_France\" class=\"mw-redirect\" title=\"May 1968 events in France\">May 1968 events in France</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Baden-Baden", "link": "https://wikipedia.org/wiki/Baden-Baden"}, {"title": "Champs-Élysées", "link": "https://wikipedia.org/wiki/Champs-%C3%89lys%C3%A9es"}, {"title": "May 1968 events in France", "link": "https://wikipedia.org/wiki/May_1968_events_in_France"}]}, {"year": "1971", "text": "Mariner program: Mariner 9 is launched to map 70% of the surface, and to study temporal changes in the atmosphere and surface, of Mars.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Mariner_program\" title=\"Mariner program\">Mariner program</a>: <i><a href=\"https://wikipedia.org/wiki/Mariner_9\" title=\"Mariner 9\">Mariner 9</a></i> is launched to map 70% of the surface, and to study temporal changes in the atmosphere and surface, of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariner_program\" title=\"Mariner program\">Mariner program</a>: <i><a href=\"https://wikipedia.org/wiki/Mariner_9\" title=\"Mariner 9\">Mariner 9</a></i> is launched to map 70% of the surface, and to study temporal changes in the atmosphere and surface, of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "Mariner program", "link": "https://wikipedia.org/wiki/Mariner_program"}, {"title": "Mariner 9", "link": "https://wikipedia.org/wiki/Mariner_9"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "1972", "text": "The Angry Brigade goes on trial over a series of 25 bombings throughout the United Kingdom.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/The_Angry_Brigade\" title=\"The Angry Brigade\">The Angry Brigade</a> goes on trial over a series of 25 bombings throughout the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Angry_Brigade\" title=\"The Angry Brigade\">The Angry Brigade</a> goes on trial over a series of 25 bombings throughout the United Kingdom.", "links": [{"title": "The Angry Brigade", "link": "https://wikipedia.org/wiki/The_Angry_Brigade"}]}, {"year": "1972", "text": "In Ben Gurion Airport (at the time: Lod Airport), Israel, members of the Japanese Red Army carry out the Lod Airport massacre, killing 24 people and injuring 78 others.", "html": "1972 - In <a href=\"https://wikipedia.org/wiki/Ben_Gurion_Airport\" title=\"Ben Gurion Airport\">Ben Gurion Airport</a> (at the time: <a href=\"https://wikipedia.org/wiki/Lod\" title=\"Lod\">Lod</a> Airport), <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>, members of the <a href=\"https://wikipedia.org/wiki/Japanese_Red_Army\" title=\"Japanese Red Army\">Japanese Red Army</a> carry out the <a href=\"https://wikipedia.org/wiki/Lod_Airport_massacre\" title=\"Lod Airport massacre\">Lod Airport massacre</a>, killing 24 people and injuring 78 others.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Ben_Gurion_Airport\" title=\"Ben Gurion Airport\">Ben Gurion Airport</a> (at the time: <a href=\"https://wikipedia.org/wiki/Lod\" title=\"Lod\">Lod</a> Airport), <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>, members of the <a href=\"https://wikipedia.org/wiki/Japanese_Red_Army\" title=\"Japanese Red Army\">Japanese Red Army</a> carry out the <a href=\"https://wikipedia.org/wiki/Lod_Airport_massacre\" title=\"Lod Airport massacre\">Lod Airport massacre</a>, killing 24 people and injuring 78 others.", "links": [{"title": "Ben Gurion Airport", "link": "https://wikipedia.org/wiki/Ben_Gurion_Airport"}, {"title": "Lod", "link": "https://wikipedia.org/wiki/Lod"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Japanese Red Army", "link": "https://wikipedia.org/wiki/Japanese_Red_Army"}, {"title": "Lod Airport massacre", "link": "https://wikipedia.org/wiki/Lod_Airport_massacre"}]}, {"year": "1974", "text": "The Airbus A300 passenger aircraft first enters service.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Airbus_A300\" title=\"Airbus A300\">Airbus A300</a> passenger aircraft first enters service.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Airbus_A300\" title=\"Airbus A300\">Airbus A300</a> passenger aircraft first enters service.", "links": [{"title": "Airbus A300", "link": "https://wikipedia.org/wiki/Airbus_A300"}]}, {"year": "1975", "text": "European Space Agency is established.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a> is established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a> is established.", "links": [{"title": "European Space Agency", "link": "https://wikipedia.org/wiki/European_Space_Agency"}]}, {"year": "1979", "text": "Downeast Flight 46 crashes on approach to Knox County Regional Airport in Rockland, Maine, killing 17.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Downeast_Flight_46\" class=\"mw-redirect\" title=\"Downeast Flight 46\">Downeast Flight 46</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Knox_County_Regional_Airport\" title=\"Knox County Regional Airport\">Knox County Regional Airport</a> in <a href=\"https://wikipedia.org/wiki/Rockland,_Maine\" title=\"Rockland, Maine\">Rockland, Maine</a>, killing 17.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Downeast_Flight_46\" class=\"mw-redirect\" title=\"Downeast Flight 46\">Downeast Flight 46</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Knox_County_Regional_Airport\" title=\"Knox County Regional Airport\">Knox County Regional Airport</a> in <a href=\"https://wikipedia.org/wiki/Rockland,_Maine\" title=\"Rockland, Maine\">Rockland, Maine</a>, killing 17.", "links": [{"title": "Downeast Flight 46", "link": "https://wikipedia.org/wiki/Downeast_Flight_46"}, {"title": "Knox County Regional Airport", "link": "https://wikipedia.org/wiki/Knox_County_Regional_Airport"}, {"title": "Rockland, Maine", "link": "https://wikipedia.org/wiki/Rockland,_Maine"}]}, {"year": "1982", "text": "Cold War: Spain joins NATO.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> joins <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> joins <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "1989", "text": "Tiananmen Square protests of 1989: The 10-metre high \"Goddess of Democracy\" statue is unveiled in Tiananmen Square by student demonstrators.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">Tiananmen Square protests of 1989</a>: The 10-metre high \"<a href=\"https://wikipedia.org/wiki/Goddess_of_Democracy\" title=\"Goddess of Democracy\">Goddess of Democracy</a>\" <a href=\"https://wikipedia.org/wiki/Statue\" title=\"Statue\">statue</a> is unveiled in <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a> by student demonstrators.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">Tiananmen Square protests of 1989</a>: The 10-metre high \"<a href=\"https://wikipedia.org/wiki/Goddess_of_Democracy\" title=\"Goddess of Democracy\">Goddess of Democracy</a>\" <a href=\"https://wikipedia.org/wiki/Statue\" title=\"Statue\">statue</a> is unveiled in <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a> by student demonstrators.", "links": [{"title": "Tiananmen Square protests of 1989", "link": "https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989"}, {"title": "Goddess of Democracy", "link": "https://wikipedia.org/wiki/Goddess_of_Democracy"}, {"title": "Statue", "link": "https://wikipedia.org/wiki/Statue"}, {"title": "Tiananmen Square", "link": "https://wikipedia.org/wiki/Tiananmen_Square"}]}, {"year": "1990", "text": "Croatian Parliament is constituted after the first free, multi-party elections, today celebrated as the National Day of Croatia.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Croatian_Parliament\" title=\"Croatian Parliament\">Croatian Parliament</a> is constituted after the first free, multi-party elections, today celebrated as the <a href=\"https://wikipedia.org/wiki/National_Day\" class=\"mw-redirect\" title=\"National Day\">National Day</a> of <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Croatian_Parliament\" title=\"Croatian Parliament\">Croatian Parliament</a> is constituted after the first free, multi-party elections, today celebrated as the <a href=\"https://wikipedia.org/wiki/National_Day\" class=\"mw-redirect\" title=\"National Day\">National Day</a> of <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>.", "links": [{"title": "Croatian Parliament", "link": "https://wikipedia.org/wiki/Croatian_Parliament"}, {"title": "National Day", "link": "https://wikipedia.org/wiki/National_Day"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}]}, {"year": "1998", "text": "The 6.5 Mw  Afghanistan earthquake shook the Takhar Province of northern Afghanistan with a maximum Mercalli intensity of VII (Very strong), killing around 4,000-4,500.", "html": "1998 - The 6.5 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/May_1998_Afghanistan_earthquake\" title=\"May 1998 Afghanistan earthquake\">Afghanistan earthquake</a> shook the <a href=\"https://wikipedia.org/wiki/Takhar_Province\" title=\"Takhar Province\">Takhar Province</a> of northern <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VII (<i>Very strong</i>), killing around 4,000-4,500.", "no_year_html": "The 6.5 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/May_1998_Afghanistan_earthquake\" title=\"May 1998 Afghanistan earthquake\">Afghanistan earthquake</a> shook the <a href=\"https://wikipedia.org/wiki/Takhar_Province\" title=\"Takhar Province\">Takhar Province</a> of northern <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VII (<i>Very strong</i>), killing around 4,000-4,500.", "links": [{"title": "May 1998 Afghanistan earthquake", "link": "https://wikipedia.org/wiki/May_1998_Afghanistan_earthquake"}, {"title": "Takhar Province", "link": "https://wikipedia.org/wiki/Takhar_Province"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1998", "text": "Nuclear Testing: Pakistan conducts an underground test in the Kharan Desert. It is reported to be a plutonium device with yield of 20kt TNT equivalent.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear Testing</a>: Pakistan conducts an <a href=\"https://wikipedia.org/wiki/Chagai-II\" title=\"Chagai-II\">underground test</a> in the <a href=\"https://wikipedia.org/wiki/Kharan_Desert\" title=\"Kharan Desert\">Kharan Desert</a>. It is reported to be a <a href=\"https://wikipedia.org/wiki/Plutonium\" title=\"Plutonium\">plutonium</a> device with yield of 20kt <a href=\"https://wikipedia.org/wiki/TNT_equivalent\" title=\"TNT equivalent\">TNT equivalent</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear Testing</a>: Pakistan conducts an <a href=\"https://wikipedia.org/wiki/Chagai-II\" title=\"Chagai-II\">underground test</a> in the <a href=\"https://wikipedia.org/wiki/Kharan_Desert\" title=\"Kharan Desert\">Kharan Desert</a>. It is reported to be a <a href=\"https://wikipedia.org/wiki/Plutonium\" title=\"Plutonium\">plutonium</a> device with yield of 20kt <a href=\"https://wikipedia.org/wiki/TNT_equivalent\" title=\"TNT equivalent\">TNT equivalent</a>.", "links": [{"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "Chagai-II", "link": "https://wikipedia.org/wiki/Chagai-II"}, {"title": "Kharan Desert", "link": "https://wikipedia.org/wiki/Kharan_Desert"}, {"title": "Plutonium", "link": "https://wikipedia.org/wiki/Plutonium"}, {"title": "TNT equivalent", "link": "https://wikipedia.org/wiki/TNT_equivalent"}]}, {"year": "2003", "text": "Depayin massacre: At least 70 people associated with the National League for Democracy are killed by government-sponsored mob in Burma. <PERSON><PERSON> flees the scene, but is arrested soon afterwards.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Depayin_massacre\" title=\"Depayin massacre\">Depayin massacre</a>: At least 70 people associated with the <a href=\"https://wikipedia.org/wiki/National_League_for_Democracy\" title=\"National League for Democracy\">National League for Democracy</a> are killed by government-sponsored mob in <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a>. <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> flees the scene, but is arrested soon afterwards.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Depayin_massacre\" title=\"Depayin massacre\">Depayin massacre</a>: At least 70 people associated with the <a href=\"https://wikipedia.org/wiki/National_League_for_Democracy\" title=\"National League for Democracy\">National League for Democracy</a> are killed by government-sponsored mob in <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a>. <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> flees the scene, but is arrested soon afterwards.", "links": [{"title": "Depayin massacre", "link": "https://wikipedia.org/wiki/Depayin_massacre"}, {"title": "National League for Democracy", "link": "https://wikipedia.org/wiki/National_League_for_Democracy"}, {"title": "Burma", "link": "https://wikipedia.org/wiki/Burma"}, {"title": "Aung San Suu Kyi", "link": "https://wikipedia.org/wiki/Aung_San_Suu_K<PERSON>"}]}, {"year": "2008", "text": "Convention on Cluster Munitions is adopted.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Convention_on_Cluster_Munitions\" title=\"Convention on Cluster Munitions\">Convention on Cluster Munitions</a> is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Convention_on_Cluster_Munitions\" title=\"Convention on Cluster Munitions\">Convention on Cluster Munitions</a> is adopted.", "links": [{"title": "Convention on Cluster Munitions", "link": "https://wikipedia.org/wiki/Convention_on_Cluster_Munitions"}]}, {"year": "2008", "text": "TACA Flight 390 overshoots the runway at Toncontín International Airport in Tegucigalpa, Honduras and crashes, killing five people.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/TACA_Flight_390\" title=\"TACA Flight 390\">TACA Flight 390</a> overshoots the runway at <a href=\"https://wikipedia.org/wiki/Toncont%C3%ADn_International_Airport\" title=\"Toncontín International Airport\">Toncontín International Airport</a> in <a href=\"https://wikipedia.org/wiki/Tegucigalpa\" title=\"Tegucigalpa\">Tegucigalpa</a>, <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> and crashes, killing five people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TACA_Flight_390\" title=\"TACA Flight 390\">TACA Flight 390</a> overshoots the runway at <a href=\"https://wikipedia.org/wiki/Toncont%C3%ADn_International_Airport\" title=\"Toncontín International Airport\">Toncontín International Airport</a> in <a href=\"https://wikipedia.org/wiki/Tegucigalpa\" title=\"Tegucigalpa\">Tegucigalpa</a>, <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> and crashes, killing five people.", "links": [{"title": "TACA Flight 390", "link": "https://wikipedia.org/wiki/TACA_Flight_390"}, {"title": "Toncontín International Airport", "link": "https://wikipedia.org/wiki/Toncont%C3%ADn_International_Airport"}, {"title": "Tegucigalpa", "link": "https://wikipedia.org/wiki/Tegucigalpa"}, {"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}]}, {"year": "2012", "text": "Former Liberian president <PERSON> is sentenced to 50 years in prison for his role in atrocities committed during the Sierra Leone Civil War.", "html": "2012 - Former <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberian</a> president <a href=\"https://wikipedia.org/wiki/<PERSON>_(Liberian_politician)\" title=\"<PERSON> (Liberian politician)\"><PERSON></a> is sentenced to 50 years in prison for his role in atrocities committed during the <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Civil_War\" title=\"Sierra Leone Civil War\">Sierra Leone Civil War</a>.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberian</a> president <a href=\"https://wikipedia.org/wiki/<PERSON>_(Liberian_politician)\" title=\"<PERSON> (Liberian politician)\"><PERSON></a> is sentenced to 50 years in prison for his role in atrocities committed during the <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Civil_War\" title=\"Sierra Leone Civil War\">Sierra Leone Civil War</a>.", "links": [{"title": "Liberia", "link": "https://wikipedia.org/wiki/Liberia"}, {"title": "<PERSON> (Liberian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Liberian_politician)"}, {"title": "Sierra Leone Civil War", "link": "https://wikipedia.org/wiki/Sierra_Leone_Civil_War"}]}, {"year": "2013", "text": "Nigeria passes a law banning same-sex marriage.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> passes a law <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_Nigeria\" class=\"mw-redirect\" title=\"Same-sex marriage in Nigeria\">banning same-sex marriage</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> passes a law <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_Nigeria\" class=\"mw-redirect\" title=\"Same-sex marriage in Nigeria\">banning same-sex marriage</a>.", "links": [{"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "Same-sex marriage in Nigeria", "link": "https://wikipedia.org/wiki/Same-sex_marriage_in_Nigeria"}]}, {"year": "2020", "text": "The Crew Dragon Demo-2 launches from the Kennedy Space Center, becoming the first crewed orbital spacecraft to launch from the United States since 2011 and the first commercial flight to the International Space Station.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/Crew_Dragon_Demo-2\" title=\"Crew Dragon Demo-2\">Crew Dragon Demo-2</a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a>, becoming the first crewed orbital spacecraft to launch from the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> since 2011 and the first commercial flight to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Crew_Dragon_Demo-2\" title=\"Crew Dragon Demo-2\">Crew Dragon Demo-2</a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a>, becoming the first crewed orbital spacecraft to launch from the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> since 2011 and the first commercial flight to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Crew Dragon Demo-2", "link": "https://wikipedia.org/wiki/Crew_Dragon_Demo-2"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2024", "text": "<PERSON> is convicted of falsifying business records in his New York trial, the first time a former President of the United States has been found guilty in a criminal case.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Prosecution_of_<PERSON>_<PERSON>_in_New_York\" title=\"Prosecution of <PERSON> in New York\">convicted of falsifying business records in his New York trial</a>, the first time a former <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> has been found guilty in a criminal case.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Prosecution_of_<PERSON>_<PERSON>_in_New_York\" title=\"Prosecution of <PERSON> in New York\">convicted of falsifying business records in his New York trial</a>, the first time a former <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> has been found guilty in a criminal case.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prosecution of <PERSON> in New York", "link": "https://wikipedia.org/wiki/Prosecution_of_<PERSON>_<PERSON>_in_New_York"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}], "Births": [{"year": "1010", "text": "<PERSON>, Chinese emperor (d. 1063)", "html": "1010 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\"><PERSON></a>, Chinese emperor (d. 1063)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\"><PERSON></a>, Chinese emperor (d. 1063)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1201", "text": "<PERSON><PERSON><PERSON> <PERSON>, count of Champagne (d. 1253)", "html": "1201 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Navarre\" title=\"<PERSON>bal<PERSON> I of Navarre\"><PERSON><PERSON><PERSON> IV</a>, count of Champagne (d. 1253)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON>bal<PERSON> I of Navarre\"><PERSON><PERSON><PERSON> IV</a>, count of Champagne (d. 1253)", "links": [{"title": "<PERSON><PERSON><PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Navarre"}]}, {"year": "1423", "text": "<PERSON>, German mathematician and astronomer (d. 1461)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (d. 1461)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (d. 1461)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1464", "text": "<PERSON> of Brandenburg, Bohemian queen (d. 1515)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brandenburg_(1464%E2%80%931515)\" title=\"<PERSON> of Brandenburg (1464-1515)\"><PERSON> of Brandenburg</a>, Bohemian queen (d. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbara_of_Brandenburg_(1464%E2%80%931515)\" title=\"<PERSON> of Brandenburg (1464-1515)\"><PERSON> of Brandenburg</a>, Bohemian queen (d. 1515)", "links": [{"title": "<PERSON> of Brandenburg (1464-1515)", "link": "https://wikipedia.org/wiki/Barbara_of_Brandenburg_(1464%E2%80%931515)"}]}, {"year": "1580", "text": "<PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Villanueva de <PERSON>za (d. 1634)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Villanueva_de_Valdueza\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Villanueva de Valdueza\"><PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Villanueva de Valdueza</a> (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Villanueva_de_Valdueza\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Villanueva de Valdueza\"><PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Villanueva de Valdueza</a> (d. 1634)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Villanueva de Valdueza", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Villanueva_de_Valdueza"}]}, {"year": "1599", "text": "<PERSON>, French Protestant biblical scholar (d. 1667)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Protestant biblical scholar (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Protestant biblical scholar (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON>, 2nd Earl of Bridgewater, English politician, Lord Lieutenant of Buckinghamshire (d. 1686)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Bridgewater\" title=\"<PERSON>, 2nd Earl of Bridgewater\"><PERSON>, 2nd Earl of Bridgewater</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire\" title=\"Lord Lieutenant of Buckinghamshire\">Lord Lieutenant of Buckinghamshire</a> (d. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Bridgewater\" title=\"<PERSON>, 2nd Earl of Bridgewater\"><PERSON>, 2nd Earl of Bridgewater</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire\" title=\"Lord Lieutenant of Buckinghamshire\">Lord Lieutenant of Buckinghamshire</a> (d. 1686)", "links": [{"title": "<PERSON>, 2nd Earl of Bridgewater", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Bridgewater"}, {"title": "Lord Lieutenant of Buckinghamshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire"}]}, {"year": "1686", "text": "<PERSON><PERSON>, Dutch illustrator (d. 1736)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch illustrator (d. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch illustrator (d. 1736)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, 1st Marquess of Downshire, English politician, Secretary of State for the Colonies (d. 1793)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Downshire\" title=\"<PERSON>, 1st Marquess of Downshire\"><PERSON>, 1st Marquess of Downshire</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Downshire\" title=\"<PERSON>, 1st Marquess of Downshire\"><PERSON>, 1st Marquess of Downshire</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1793)", "links": [{"title": "<PERSON>, 1st Marquess of Downshire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Downshire"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1719", "text": "<PERSON>, English politician (d. 1806)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gate"}]}, {"year": "1757", "text": "<PERSON>, 1st Viscount Sidmouth, English politician, Prime Minister of the United Kingdom (d. 1844)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Sidmouth\" class=\"mw-redirect\" title=\"<PERSON>, 1st Viscount Sidmouth\"><PERSON>, 1st Viscount Sidmouth</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Sidmouth\" class=\"mw-redirect\" title=\"<PERSON>, 1st Viscount Sidmouth\"><PERSON>, 1st Viscount Sidmouth</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1844)", "links": [{"title": "<PERSON>, 1st Viscount Sidmouth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_Sidmouth"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1768", "text": "<PERSON>, French general (d. 1815)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>_<PERSON>_Champion_de_Nansouty\" title=\"<PERSON> de Nansouty\"><PERSON> Nansouty</a>, French general (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>_<PERSON>_Champion_de_Nansouty\" title=\"Étienne <PERSON> Champion de Nansouty\"><PERSON> Nansouty</a>, French general (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, German mineralogist and geologist (d. 1873)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German mineralogist and geologist (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German mineralogist and geologist (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French cardinal (d. 1883)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Bonnechose\" title=\"<PERSON><PERSON><PERSON><PERSON> Bonnechose\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French cardinal (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French cardinal (d. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, Russian philosopher and theorist  (d. 1876)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and theorist (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and theorist (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, Belgian-French mathematician and academic (d. 1894)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Charles_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French mathematician and academic (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Charles_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French mathematician and academic (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, English general (d. 1894)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, 1st Premier of Quebec (d. 1890)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1835", "text": "<PERSON>, English author, poet, and playwright (d. 1913)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alfred Austin\"><PERSON></a>, English author, poet, and playwright (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfred_<PERSON>\" title=\"Alfred Austin\"><PERSON></a>, English author, poet, and playwright (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_Austin"}]}, {"year": "1844", "text": "<PERSON>, French poet and photographer (d. 1921)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_A<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, French poet and photographer (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_A<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, French poet and photographer (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_A<PERSON><PERSON>n"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON>, Spanish king (d. 1890)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Amadeo_I_of_Spain\" title=\"Amadeo I of Spain\"><PERSON><PERSON><PERSON> <PERSON></a>, Spanish king (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amadeo_I_of_Spain\" title=\"Amadeo I of Spain\"><PERSON><PERSON><PERSON> <PERSON></a>, Spanish king (d. 1890)", "links": [{"title": "Amadeo I of Spain", "link": "https://wikipedia.org/wiki/Amadeo_I_of_Spain"}]}, {"year": "1846", "text": "<PERSON>, Russian goldsmith and jeweler (d. 1920)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Russian goldsmith and jeweler (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Russian goldsmith and jeweler (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1862", "text": "<PERSON>, Azerbaijani philosopher and poet (d. 1911)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Azerbaijani philosopher and poet (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Azerbaijani philosopher and poet (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mirza_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American mathematician (d. 1951)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, American mathematician (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, American mathematician (d. 1951)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)"}]}, {"year": "1874", "text": "<PERSON>, French physician (d. 1912)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Italian philosopher and academic (d. 1944)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and academic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and academic (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_Gentile"}]}, {"year": "1879", "text": "<PERSON>, English cricketer and soldier (d. 1917)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Estonian psychologist and academic (d. 1975)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian psychologist and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian psychologist and academic (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, German field marshal (d. 1968)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, English runner and soldier (d. 1915)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Wyndham_Halswelle\" title=\"Wyndham Halswelle\">W<PERSON><PERSON></a>, English runner and soldier (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wyndham_Halswelle\" title=\"Wyndham Halswelle\">W<PERSON><PERSON></a>, English runner and soldier (d. 1915)", "links": [{"title": "Wyndham Halswelle", "link": "https://wikipedia.org/wiki/Wyndham_Halswelle"}]}, {"year": "1883", "text": "<PERSON>, Australian rugby league player (d. 1930)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, German soldier and politician (d. 1942)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and politician (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and politician (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>egmund_Gl%C3%<PERSON><PERSON>mann"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Estonian poet and linguist (d. 1942)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Villem_Gr%C3%BCnthal-Ridal<PERSON>\" title=\"<PERSON><PERSON>rünthal-Ridal<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and linguist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Villem_Gr%C3%BCnthal-Ridala\" title=\"<PERSON><PERSON>nthal-Ridal<PERSON>\"><PERSON><PERSON></a>, Estonian poet and linguist (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Villem_Gr%C3%BCnthal-Ridala"}]}, {"year": "1886", "text": "<PERSON>, Canadian lawyer and politician (d. 1964)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1886", "text": "<PERSON>, American theorist and author (d. 1918)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and author (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and author (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Ukrainian-American sculptor and illustrator (d. 1964)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American sculptor and illustrator (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American sculptor and illustrator (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Danish pianist, composer, and conductor (d. 1964)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish pianist, composer, and conductor (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish pianist, composer, and conductor (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, French soldier and politician, French Minister of the Interior (d. 1936)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Interior Ministers of France\">French Minister of the Interior</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Interior Ministers of France\">French Minister of the Interior</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Interior Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France"}]}, {"year": "1892", "text": "<PERSON>, Filipino painter (d. 1972)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Dutch politician, Governor-General of the Dutch East Indies (d. 1965)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors-General_of_the_Dutch_East_Indies\" class=\"mw-redirect\" title=\"List of Governors-General of the Dutch East Indies\">Governor-General of the Dutch East Indies</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors-General_of_the_Dutch_East_Indies\" class=\"mw-redirect\" title=\"List of Governors-General of the Dutch East Indies\">Governor-General of the Dutch East Indies</a> (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Governors-General of the Dutch East Indies", "link": "https://wikipedia.org/wiki/List_of_Governors-General_of_the_Dutch_East_Indies"}]}, {"year": "1895", "text": "<PERSON>, English cricketer (d. 1956)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American director, producer, and screenwriter (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Howard_Hawks\" title=\"Howard Hawks\"><PERSON></a>, American director, producer, and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Howard_Hawks\" title=\"Howard Hawks\"><PERSON></a>, American director, producer, and screenwriter (d. 1977)", "links": [{"title": "Howard <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hawks"}]}, {"year": "1897", "text": "<PERSON>, Australian politician, 16th Premier of Western Australia (d. 1986)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1898", "text": "<PERSON>, English artist and illustrator (d. 1985)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English artist and illustrator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English artist and illustrator (d. 1985)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1899", "text": "<PERSON>, American screenwriter and producer (d. 1936)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Estonian pianist and composer (d. 1969)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian pianist and composer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian pianist and composer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, American actress and author (d. 1979)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and author (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Co<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and author (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, American actor and dancer (d. 1985)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and dancer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>tch<PERSON>\"><PERSON><PERSON></a>, American actor and dancer (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>it"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American poet and author (d. 1946)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American poet and author (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American poet and author (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ee_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German mystic and author (d. 1959)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%B6ning\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/German_mystic\" class=\"mw-redirect\" title=\"German mystic\">German mystic</a> and author (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ning\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/German_mystic\" class=\"mw-redirect\" title=\"German mystic\">German mystic</a> and author (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bruno_Gr%C3%B6ning"}, {"title": "German mystic", "link": "https://wikipedia.org/wiki/German_mystic"}]}, {"year": "1907", "text": "<PERSON><PERSON>, French anthropologist and academic (d. 2008)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French anthropologist and academic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French anthropologist and academic (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Swedish physicist and engineer, Nobel Prize laureate (d. 1995)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>nes_Alfv%C3%A9n\" title=\"Hannes Alfvén\"><PERSON><PERSON></a>, Swedish physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Alfv%C3%A9n\" title=\"<PERSON><PERSON> Alfvén\"><PERSON><PERSON></a>, Swedish physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hannes_Alfv%C3%A9n"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1908", "text": "<PERSON>, American voice actor (d. 1989)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Blanc\"><PERSON></a>, American voice actor (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, French music executive and talent agent (d. 1997)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French music executive and talent agent (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French music executive and talent agent (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, English motorcycle road racer (d. 1988)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle road racer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle road racer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American clarinet player, songwriter, and bandleader (d. 1986)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Goodman\" title=\"Benny Goodman\"><PERSON></a>, American clarinet player, songwriter, and bandleader (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Goodman\" title=\"Benny Goodman\"><PERSON></a>, American clarinet player, songwriter, and bandleader (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English-American journalist and author (d. 2011)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1912", "text": "<PERSON>, German physicist and academic (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Welsh actor (d. 1980)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, American author and academic (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Millicent_Selsam\" title=\"Millicent Selsam\"><PERSON>ice<PERSON> <PERSON><PERSON><PERSON></a>, American author and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Millicent_Selsam\" title=\"Millicent Selsam\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, American author and academic (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Millicent_<PERSON><PERSON>am"}]}, {"year": "1912", "text": "<PERSON>, American playwright and author (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 37th <PERSON><PERSON><PERSON><PERSON> (d. 1979)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 37th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 37th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>o"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1915", "text": "<PERSON>, English footballer and soldier (d. 1996)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and soldier (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and soldier (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, French soldier and politician (d. 1962)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ay%C3%A9e"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American illustrator (d. 1995)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American illustrator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American illustrator (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mort_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Mexican poet and author (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pita Amor\"><PERSON><PERSON></a>, Mexican poet and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pita Amor\"><PERSON><PERSON></a>, Mexican poet and author (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Amor"}]}, {"year": "1918", "text": "<PERSON>, American businessman, founded Bob Evans Restaurants (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(restaurateur)\" title=\"<PERSON> (restaurateur)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Restaurants\" title=\"Bob <PERSON> Restaurants\">Bob <PERSON> Restaurants</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(restaurateur)\" title=\"<PERSON> (restaurateur)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Restaurants\" title=\"Bob <PERSON> Restaurants\">Bob <PERSON> Restaurants</a> (d. 2007)", "links": [{"title": "<PERSON> (restaurateur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(restaurateur)"}, {"title": "<PERSON> Restaurants", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Restaurants"}]}, {"year": "1919", "text": "<PERSON>, Bolivian general and politician, 55th President of Bolivia (d. 1969)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Barr<PERSON>os\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 55th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 55th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Barrientos"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1920", "text": "<PERSON>, Japanese-American director and producer (d. 1989)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American director and producer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American director and producer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American author and educator (d. 2003)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American CIA officer and diplomat (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer and diplomat (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer and diplomat (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "CIA", "link": "https://wikipedia.org/wiki/CIA"}]}, {"year": "1925", "text": "<PERSON>, English physician and author (d. 2022)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physician and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physician and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American country/western swing musician (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>im<PERSON>\"><PERSON></a>, American country/western swing musician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gim<PERSON>\"><PERSON></a>, American country/western swing musician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ble"}]}, {"year": "1927", "text": "<PERSON>, American mathematician", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor and singer (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian rugby league player and coach (d. 1993)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rugby_league)\" title=\"<PERSON> (Australian rugby league)\"><PERSON></a>, Australian rugby league player and coach (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rugby_league)\" title=\"<PERSON> (Australian rugby league)\"><PERSON></a>, Australian rugby league player and coach (d. 1993)", "links": [{"title": "<PERSON> (Australian rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rugby_league)"}]}, {"year": "1928", "text": "<PERSON>, Australian painter (d. 2006)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Pro_Hart\" title=\"Pro Hart\"><PERSON> <PERSON></a>, Australian painter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pro_Hart\" title=\"Pro Hart\"><PERSON> <PERSON></a>, Australian painter (d. 2006)", "links": [{"title": "Pro Hart", "link": "https://wikipedia.org/wiki/Pro_Hart"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Belgian-French director, producer, and screenwriter (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Agn%C3%A8s_Varda\" title=\"<PERSON>gn<PERSON> V<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian-French director, producer, and screenwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agn%C3%A8s_Varda\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian-French director, producer, and screenwriter (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agn%C3%A8s_Varda"}]}, {"year": "1929", "text": "<PERSON>, French archbishop (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archbishop (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archbishop (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English businessman, founded Annabel's (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s\" title=\"<PERSON><PERSON>'s\"><PERSON><PERSON>'s</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s\" title=\"<PERSON><PERSON>'s\"><PERSON><PERSON>'s</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>'s", "link": "https://wikipedia.org/wiki/Annabel%27s"}]}, {"year": "1930", "text": "<PERSON>, American painter (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American real estate magnate", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate magnate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate magnate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English actor and playwright", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American accordion player and composer (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player and composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player and composer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Baron <PERSON>, Welsh politician and diplomat, British Ambassador to the United Nations (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, Welsh politician and diplomat, <a href=\"https://wikipedia.org/wiki/British_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"British Ambassador to the United Nations\">British Ambassador to the United Nations</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, Welsh politician and diplomat, <a href=\"https://wikipedia.org/wiki/British_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"British Ambassador to the United Nations\">British Ambassador to the United Nations</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>"}, {"title": "British Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/British_Ambassador_to_the_United_Nations"}]}, {"year": "1934", "text": "<PERSON>, Russian general, pilot, and cosmonaut (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general, pilot, and cosmonaut (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general, pilot, and cosmonaut (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>ket<PERSON>_<PERSON>agoulias\" title=\"Alketas Panagoulias\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ago<PERSON>as\" title=\"Alketas Panagoulias\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager (d. 2012)", "links": [{"title": "Alketas Panagoulias", "link": "https://wikipedia.org/wiki/Alketas_Panagoulias"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Canadian-American actress and dancer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian academic and politician (d. 2005)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American actor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1937", "text": "<PERSON>, Anglo-Irish businessman, life peer, and British politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish businessman, life peer, and British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish businessman, life peer, and British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American-English architect (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English architect (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English architect (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American author and educator (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actor (d. 2019)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Austrian race car driver", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Scottish businessman, founded Waterstones", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman, founded <a href=\"https://wikipedia.org/wiki/Waterstones\" title=\"Waterstones\">Waterstones</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman, founded <a href=\"https://wikipedia.org/wiki/Waterstones\" title=\"Waterstones\">Waterstones</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Waterstones", "link": "https://wikipedia.org/wiki/Waterstones"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricket administrator (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricket administrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricket administrator (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian-American ice hockey player", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English bishop", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English journalist and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American civil rights activist (d. 1964)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Swedish motorcycle racer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American football player and philanthropist (d. 2020)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and philanthropist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and philanthropist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sayers"}]}, {"year": "1944", "text": "<PERSON>, English guitarist and songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actress (d. 2000)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Greek-American engineer and businessman", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Stav_Prodromou\" title=\"Stav Prodromou\"><PERSON><PERSON> Prodromou</a>, Greek-American engineer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stav_Prodromou\" title=\"Stav Prodromou\"><PERSON><PERSON> Prodromou</a>, Greek-American engineer and businessman", "links": [{"title": "Stav Prodromou", "link": "https://wikipedia.org/wiki/Stav_Prodromou"}]}, {"year": "1945", "text": "<PERSON>, American singer (d. 2011)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English historian and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_(historian)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Serbian and Yugoslav footballer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C5%BEaji%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian and Yugoslav footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C5%BEaji%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian and Yugoslav footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dragan_D%C5%BEaji%C4%87"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Canadian golfer (d. 2021)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian golfer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian golfer (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Belgian former professional road racing cyclist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian former professional road racing cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian former professional road racing cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American screenwriter and producer (d. 2005)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian rules footballer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian rules footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/P.J._<PERSON>\" class=\"mw-redirect\" title=\"P.<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P.J<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>.<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P.J._<PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English lawyer and judge", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English cricketer and sportscaster (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, French politician, 14th Mayor of Paris", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AB\" title=\"<PERSON>\"><PERSON></a>, French politician, 14th <a href=\"https://wikipedia.org/wiki/Mayor_of_Paris\" title=\"Mayor of Paris\">Mayor of Paris</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AB\" title=\"<PERSON>\"><PERSON></a>, French politician, 14th <a href=\"https://wikipedia.org/wiki/Mayor_of_Paris\" title=\"Mayor of Paris\">Mayor of Paris</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bertrand_Delano%C3%AB"}, {"title": "Mayor of Paris", "link": "https://wikipedia.org/wiki/Mayor_of_Paris"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Indian actor, producer, and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON> Rawal\"><PERSON><PERSON></a>, Indian actor, producer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON>al\"><PERSON><PERSON></a>, Indian actor, producer, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Rawal"}]}, {"year": "1950", "text": "<PERSON>, English lawyer, journalist, and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, journalist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Bosnian Serb singer-songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Zdravko_%C4%8Coli%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bosnian Serb singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zdravko_%C4%8Coli%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bosnian Serb singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zdravko_%C4%8Coli%C4%87"}]}, {"year": "1951", "text": "<PERSON>, Paraguayan bishop and politician, President of Paraguay", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan bishop and politician, <a href=\"https://wikipedia.org/wiki/President_of_Paraguay\" title=\"President of Paraguay\">President of Paraguay</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan bishop and politician, <a href=\"https://wikipedia.org/wiki/President_of_Paraguay\" title=\"President of Paraguay\">President of Paraguay</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_<PERSON>"}, {"title": "President of Paraguay", "link": "https://wikipedia.org/wiki/President_of_Paraguay"}]}, {"year": "1951", "text": "<PERSON>, American actor, singer, and director", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American screenwriter and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player, referee, and sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, referee, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, referee, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian skier", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)\" title=\"<PERSON> (skier)\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)\" title=\"<PERSON> (skier)\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON> (skier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Irish actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Meaney\"><PERSON><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Meaney\"><PERSON><PERSON></a>, Irish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Colm_<PERSON>ey"}]}, {"year": "1955", "text": "<PERSON><PERSON>, English drummer and songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Topper Headon\"><PERSON><PERSON></a>, English drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Topper Headon\"><PERSON><PERSON></a>, English drummer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Top<PERSON>_<PERSON>on"}]}, {"year": "1955", "text": "<PERSON>, English-Canadian biologist, ecologist, and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian biologist, ecologist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian biologist, ecologist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English lawyer and judge", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Swift\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Irish novelist, poet, playwright, and critic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Colm_T%C3%B3ib%C3%ADn\" title=\"Colm Tóibín\"><PERSON><PERSON></a>, Irish novelist, poet, playwright, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colm_T%C3%B3ib%C3%ADn\" title=\"Colm Tóibín\"><PERSON><PERSON></a>, Irish novelist, poet, playwright, and critic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Colm_T%C3%B3ib%C3%ADn"}]}, {"year": "1955", "text": "<PERSON>, American professional wrestler", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American author, screenwriter, and critic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian golfer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1958", "text": "<PERSON>, Canadian football player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>au"}]}, {"year": "1958", "text": "<PERSON>, Swedish singer-songwriter and pianist (d. 2019)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and pianist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and pianist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American lawyer and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Steve_<PERSON>\" title=\"Steve Israel\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Steve_Israel\" title=\"Steve Israel\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Steve_Israel"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Spanish-American captain, pilot, and astronaut", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3pez-Alegr%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Spanish-American captain, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3pez-Alegr%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Spanish-American captain, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michael_L%C3%B3pez-Alegr%C3%ADa"}]}, {"year": "1958", "text": "<PERSON>, American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English footballer, coach, and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1959)\" title=\"<PERSON> (footballer, born 1959)\"><PERSON></a>, English footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1959)\" title=\"<PERSON> (footballer, born 1959)\"><PERSON></a>, English footballer, coach, and manager", "links": [{"title": "<PERSON> (footballer, born 1959)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1959)"}]}, {"year": "1959", "text": "<PERSON>, Canadian curler", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Belgian politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Iranian-American director and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian-American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian-American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American author and illustrator, co-created the Teenage Mutant Ninja Turtles", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, co-created the <i><a href=\"https://wikipedia.org/wiki/Teenage_Mutant_Ninja_Turtles\" title=\"Teenage Mutant Ninja Turtles\">Teenage Mutant Ninja Turtles</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, co-created the <i><a href=\"https://wikipedia.org/wiki/Teenage_Mutant_Ninja_Turtles\" title=\"Teenage Mutant Ninja Turtles\">Teenage Mutant Ninja Turtles</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Teenage Mutant Ninja Turtles", "link": "https://wikipedia.org/wiki/Teenage_Mutant_Ninja_Turtles"}]}, {"year": "1962", "text": "<PERSON>, English lawyer and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bedford_MP)\" class=\"mw-redirect\" title=\"<PERSON> (Bedford MP)\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bedford_MP)\" class=\"mw-redirect\" title=\"<PERSON> (Bedford MP)\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON> (Bedford MP)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bedford_MP)"}]}, {"year": "1962", "text": "<PERSON>, English businessman and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress and singer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian drummer and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, French journalist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/%C3%89lis<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lis<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1963", "text": "<PERSON>, English chemist and astronaut", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, American singer-songwriter, guitarist, and actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wyn<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wyn<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Italian race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Coker\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Troy_Coker"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Italian animator and producer, founded Rainbow S.r.l.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Iginio_Straffi\" title=\"Iginio Straffi\"><PERSON><PERSON><PERSON>raff<PERSON></a>, Italian animator and producer, founded <a href=\"https://wikipedia.org/wiki/Rainbow_S.r.l.\" class=\"mw-redirect\" title=\"Rainbow S.r.l.\">Rainbow S.r.l.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iginio_Straffi\" title=\"Iginio Straffi\"><PERSON><PERSON><PERSON> Straff<PERSON></a>, Italian animator and producer, founded <a href=\"https://wikipedia.org/wiki/Rainbow_S.r.l.\" class=\"mw-redirect\" title=\"Rainbow S.r.l.\">Rainbow S.r.l.</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iginio_Straffi"}, {"title": "Rainbow S.r.l.", "link": "https://wikipedia.org/wiki/Rainbow_S.r.l."}]}, {"year": "1966", "text": "<PERSON><PERSON>, mother of American basketball players", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, mother of American basketball players", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, mother of American basketball players", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4%C3%9Fler\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4%C3%9Fler\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_H%C3%A4%C3%9Fler"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English singer-songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Australian hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German-American bass player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian lawyer and politician, 18th Premier of Alberta", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Alberta", "link": "https://wikipedia.org/wiki/Premier_of_Alberta"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, French citizen, sentenced to life in prison related to September 11 attacks", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French citizen, sentenced to life in prison related to <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French citizen, sentenced to life in prison related to <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}]}, {"year": "1969", "text": "<PERSON>, Japanese director, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yu<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English rugby player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1971", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_%C5%A0l%C3%A9gr\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_%C5%A0l%C3%A9gr\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_%C5%A0l%C3%A9gr"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Dominican-American baseball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American rapper (d. 1999)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Big_L\" title=\"Big L\"><PERSON> <PERSON></a>, American rapper (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_L\" title=\"Big L\"><PERSON> <PERSON></a>, American rapper (d. 1999)", "links": [{"title": "Big L", "link": "https://wikipedia.org/wiki/Big_L"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Chalk<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Chalk<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, South Korean actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yun"}]}, {"year": "1974", "text": "<PERSON>, American ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English rugby player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>e<PERSON><PERSON>_Green\" title=\"CeeLo Green\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"CeeLo Green\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "CeeLo Green", "link": "https://wikipedia.org/wiki/CeeLo_Green"}]}, {"year": "1975", "text": "<PERSON>, American computer scientist and businesswoman", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Icelandic politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Arna_L%C3%A1ra_J%C3%B3nsd%C3%B3ttir\" title=\"Arna L<PERSON>dóttir\"><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arna_L%C3%A1ra_J%C3%B3nsd%C3%B3ttir\" title=\"<PERSON><PERSON>tti<PERSON>\"><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arna_L%C3%A1ra_J%C3%B3nsd%C3%B3ttir"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Rasho_Nesterovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rasho_Nesterovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rasho_Nesterovi%C4%87"}]}, {"year": "1976", "text": "<PERSON>, Swedish tennis player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Kenyan runner", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Argentinian-Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English international footballer and manager", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Russian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ilona_Korstin\" title=\"Il<PERSON> Korstin\"><PERSON><PERSON></a>, Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ilona_Korstin\" title=\"Ilona Korstin\"><PERSON><PERSON></a>, Russian basketball player", "links": [{"title": "<PERSON><PERSON> Korstin", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Japanese author", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ry%C5%8Dgo_Narita\" title=\"Ryōgo Narita\"><PERSON><PERSON><PERSON></a>, Japanese author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%8Dgo_Narita\" title=\"Ryōgo Narita\"><PERSON><PERSON><PERSON></a>, Japanese author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%8Dgo_Narita"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1981", "text": "<PERSON>, Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American rapper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Danish handball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish handball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8<PERSON>_<PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player (d. 2007)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2007)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1982", "text": "<PERSON>-<PERSON>, English rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English rugby player", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Hong Kong footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Sham_Kwok_Fai\" title=\"Sham Kwok Fai\"><PERSON><PERSON> Kwo<PERSON> Fai</a>, Hong Kong footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sham_Kwok_Fai\" title=\"Sham Kwok Fai\"><PERSON><PERSON> Kwo<PERSON> Fai</a>, Hong Kong footballer", "links": [{"title": "Sham Kwok Fai", "link": "https://wikipedia.org/wiki/Sham_Kwok_Fai"}]}, {"year": "1984", "text": "<PERSON>, Australian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Russian chess player (d. 2013)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Polish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Bulgarian international footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian international footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Korean-American singer and songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean-American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean-American singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lee"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Ukrainian tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, South Korean singer and actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Im_<PERSON><PERSON>-ah\" title=\"Im <PERSON>on-ah\"><PERSON><PERSON> <PERSON>-ah</a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON><PERSON>-ah\" title=\"Im <PERSON>on-ah\"><PERSON><PERSON> <PERSON>-ah</a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON>ah", "link": "https://wikipedia.org/wiki/Im_<PERSON><PERSON>-ah"}]}, {"year": "1990", "text": "<PERSON>, Russian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English swimmer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Brazilian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, South Korean singer and actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1997", "text": "<PERSON>, American actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1997)\" title=\"<PERSON> (actor, born 1997)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1997)\" title=\"<PERSON> (actor, born 1997)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor, born 1997)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1997)"}]}, {"year": "1997", "text": "<PERSON>, American actor ", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Chinese race car driver", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "531", "text": "<PERSON>, prince of the Liang dynasty (b. 501)", "html": "531 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tong\"><PERSON></a>, prince of the <a href=\"https://wikipedia.org/wiki/Liang_dynasty\" title=\"Liang dynasty\">Liang dynasty</a> (b. 501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Tong\" title=\"<PERSON> Tong\"><PERSON></a>, prince of the <a href=\"https://wikipedia.org/wiki/Liang_dynasty\" title=\"Liang dynasty\">Liang dynasty</a> (b. 501)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Liang dynasty", "link": "https://wikipedia.org/wiki/Liang_dynasty"}]}, {"year": "727", "text": "<PERSON><PERSON>, bishop <PERSON>", "html": "727 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(first_Bishop_of_Li%C3%A8ge)\" class=\"mw-redirect\" title=\"<PERSON> (first Bishop of Liège)\"><PERSON><PERSON></a>, bishop <a href=\"https://wikipedia.org/wiki/Prince-Bishopric_of_Li%C3%A8ge\" title=\"Prince-Bishopric of Liège\">Liège</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(first_Bishop_of_Li%C3%A8ge)\" class=\"mw-redirect\" title=\"<PERSON> (first Bishop of Liège)\"><PERSON><PERSON></a>, bishop <a href=\"https://wikipedia.org/wiki/Prince-Bishopric_of_Li%C3%A8ge\" title=\"Prince-Bishopric of Liège\">Liège</a>", "links": [{"title": "<PERSON> (first Bishop of Liège)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(first_Bishop_of_Li%C3%A8ge)"}, {"title": "Prince-Bishopric of Liège", "link": "https://wikipedia.org/wiki/Prince-Bishopric_of_Li%C3%A8ge"}]}, {"year": "947", "text": "<PERSON>, king of Chu (b. 899)", "html": "947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)\" class=\"mw-redirect\" title=\"<PERSON> (Ten Kingdoms)\">Chu</a> (b. 899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)\" class=\"mw-redirect\" title=\"<PERSON> (Ten Kingdoms)\">Chu</a> (b. 899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Chu (Ten Kingdoms)", "link": "https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)"}]}, {"year": "1035", "text": "<PERSON>, count of Flanders (b. 980)", "html": "1035 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Count_of_Flanders\" title=\"<PERSON> IV, Count of Flanders\"><PERSON> IV</a>, count of <a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flanders</a> (b. 980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Count_of_Flanders\" title=\"<PERSON> IV, Count of Flanders\"><PERSON> IV</a>, count of <a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flanders</a> (b. 980)", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders"}, {"title": "County of Flanders", "link": "https://wikipedia.org/wiki/County_of_Flanders"}]}, {"year": "1159", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON> the Exile, High Duke of Poland and Duke of Silesia (b. 1105)", "html": "1159 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_II_the_Exile\" title=\"<PERSON><PERSON><PERSON><PERSON> II the Exile\"><PERSON><PERSON><PERSON><PERSON> II the Exile</a>, <a href=\"https://wikipedia.org/wiki/List_of_Polish_monarchs\" title=\"List of Polish monarchs\">High Duke of Poland</a> and <a href=\"https://wikipedia.org/wiki/Duchy_of_Silesia\" title=\"Duchy of Silesia\">Duke of Silesia</a> (b. 1105)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_II_the_Exile\" title=\"<PERSON><PERSON><PERSON><PERSON> II the Exile\"><PERSON><PERSON><PERSON><PERSON> II the Exile</a>, <a href=\"https://wikipedia.org/wiki/List_of_Polish_monarchs\" title=\"List of Polish monarchs\">High Duke of Poland</a> and <a href=\"https://wikipedia.org/wiki/Duchy_of_Silesia\" title=\"Duchy of Silesia\">Duke of Silesia</a> (b. 1105)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> the Exile", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_II_the_Exile"}, {"title": "List of Polish monarchs", "link": "https://wikipedia.org/wiki/List_of_Polish_monarchs"}, {"title": "Duchy of Silesia", "link": "https://wikipedia.org/wiki/Duchy_of_Silesia"}]}, {"year": "1252", "text": "<PERSON>, king of Castile and León (b. 1199)", "html": "1252 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Castile\" title=\"<PERSON> III of Castile\"><PERSON> III</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> (b. 1199)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Castile\" title=\"<PERSON> III of Castile\"><PERSON> III</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> (b. 1199)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}]}, {"year": "1347", "text": "<PERSON>, 1st Baron <PERSON>, English peer (b. 1290)", "html": "1347 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Peerage\" title=\"Peerage\">peer</a> (b. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Peerage\" title=\"Peerage\">peer</a> (b. 1290)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Peerage", "link": "https://wikipedia.org/wiki/Peerage"}]}, {"year": "1376", "text": "<PERSON>, Dame of Epernon, French noblewoman", "html": "1376 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Dame_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Pont<PERSON>eu, Dame of Epernon\"><PERSON> Pont<PERSON>eu, Dame of Epernon</a>, French noblewoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Dame_<PERSON>_E<PERSON><PERSON>\" title=\"<PERSON>eu, Dame of Epernon\"><PERSON> Pont<PERSON>eu, Dame of Epernon</a>, French noblewoman", "links": [{"title": "<PERSON>eu, Dame of Epernon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1416", "text": "<PERSON> of Prague, Czech martyr and theologian (b. 1379)", "html": "1416 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Prague\" title=\"<PERSON> of Prague\"><PERSON> of Prague</a>, Czech martyr and theologian (b. 1379)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Prague\" title=\"<PERSON> of Prague\"><PERSON> of Prague</a>, Czech martyr and theologian (b. 1379)", "links": [{"title": "Jerome of Prague", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Prague"}]}, {"year": "1431", "text": "<PERSON> Arc, French martyr and saint (b. 1412)", "html": "1431 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Arc\"><PERSON> Arc</a>, French martyr and saint (b. 1412)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Joan of Arc\"><PERSON> Arc</a>, French martyr and saint (b. 1412)", "links": [{"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1434", "text": "<PERSON><PERSON><PERSON> the Great, Czech general (b. 1380)", "html": "1434 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a>, Czech general (b. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a>, Czech general (b. 1380)", "links": [{"title": "Prokop the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great"}]}, {"year": "1469", "text": "<PERSON><PERSON>, Castilian bishop (b. 1389)", "html": "1469 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Barrientos\" title=\"<PERSON><PERSON> de Barrientos\"><PERSON><PERSON> Barrientos</a>, <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a> <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a> (b. 1389)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Barrient<PERSON>\" title=\"<PERSON><PERSON> Barrientos\"><PERSON><PERSON> Barr<PERSON>os</a>, <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a> <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a> (b. 1389)", "links": [{"title": "Lope de Barrientos", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ientos"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bishop"}]}, {"year": "1472", "text": "<PERSON><PERSON><PERSON><PERSON> of Luxembourg, daughter of <PERSON> (b. 1416)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Luxembourg\" title=\"<PERSON><PERSON><PERSON><PERSON> of Luxembourg\"><PERSON><PERSON><PERSON><PERSON> of Luxembourg</a>, daughter of <PERSON> (b. 1416)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Luxembourg\" title=\"<PERSON><PERSON><PERSON><PERSON> of Luxembourg\"><PERSON><PERSON><PERSON><PERSON> of Luxembourg</a>, daughter of <PERSON> (b. 1416)", "links": [{"title": "Jacquetta of Luxembourg", "link": "https://wikipedia.org/wiki/Jacquetta_of_Luxembourg"}]}, {"year": "1574", "text": "<PERSON> of France (b. 1550)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> (b. 1550)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_IX_of_France"}]}, {"year": "1593", "text": "<PERSON>, English poet and playwright (b. 1564)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1564)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1606", "text": "Guru <PERSON><PERSON><PERSON>, fifth of the Sikh gurus (b. 1563)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\">Guru <PERSON><PERSON></a>, fifth of the <a href=\"https://wikipedia.org/wiki/Sikh_gurus\" title=\"Sikh gurus\">Sikh gurus</a> (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Guru <PERSON><PERSON>\">Guru <PERSON><PERSON></a>, fifth of the <a href=\"https://wikipedia.org/wiki/Sikh_gurus\" title=\"Sikh gurus\">Sikh gurus</a> (b. 1563)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Sikh gurus", "link": "https://wikipedia.org/wiki/Sikh_gurus"}]}, {"year": "1640", "text": "<PERSON>, German-Belgian painter (b. 1577)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Belgian painter (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Belgian painter (b. 1577)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1696", "text": "<PERSON>, 1st Baron <PERSON> of Tewkesbury, English politician, Lord Lieutenant of Ireland (b. 1638)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Tewkesbury\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON> of Tewkesbury\"><PERSON>, 1st Baron <PERSON> of Tewkesbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Tewkesbury\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON> of Tewkesbury\"><PERSON>, 1st Baron <PERSON> of Tewkesbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1638)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Tewkesbury", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Tewkesbury"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1670", "text": "<PERSON>, English minister, co-founded the New Haven Colony (b. 1597)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English minister, co-founded the <a href=\"https://wikipedia.org/wiki/New_Haven_Colony\" title=\"New Haven Colony\">New Haven Colony</a> (b. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English minister, co-founded the <a href=\"https://wikipedia.org/wiki/New_Haven_Colony\" title=\"New Haven Colony\">New Haven Colony</a> (b. 1597)", "links": [{"title": "<PERSON> (minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)"}, {"title": "New Haven Colony", "link": "https://wikipedia.org/wiki/New_Haven_Colony"}]}, {"year": "1712", "text": "<PERSON>, Italian painter (b. 1645)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, 1st Earl of Albemarle, Dutch-English general (b. 1670)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Albemarle\" title=\"<PERSON>, 1st Earl of Albemarle\"><PERSON>, 1st Earl of Albemarle</a>, Dutch-English general (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Albemarle\" title=\"<PERSON>, 1st Earl of Albemarle\"><PERSON>, 1st Earl of Albemarle</a>, Dutch-English general (b. 1670)", "links": [{"title": "<PERSON>, 1st Earl of Albemarle", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Albemarle"}]}, {"year": "1744", "text": "<PERSON>, English poet, essayist, and translator (b. 1688)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, essayist, and translator (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, essayist, and translator (b. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, French painter and set designer (b. 1703)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Boucher\" title=\"<PERSON>\"><PERSON></a>, French painter and set designer (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Boucher\" title=\"<PERSON>\"><PERSON></a>, French painter and set designer (b. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Boucher"}]}, {"year": "1778", "text": "<PERSON><PERSON>, French philosopher and author (b. 1694)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French philosopher and author (b. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vol<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French philosopher and author (b. 1694)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Voltaire"}]}, {"year": "1778", "text": "<PERSON>, French/Spanish mining magnate in colonial Mexico (b. ca. 1700)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_la_Borda\" title=\"<PERSON> Borda\"><PERSON></a>, French/Spanish mining magnate in colonial Mexico (b. ca. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_la_Borda\" title=\"<PERSON> Bo<PERSON>\"><PERSON></a>, French/Spanish mining magnate in colonial Mexico (b. ca. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON>, French general (b. 1774)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (b. 1774)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, Scottish historian, jurist, and politician (b. 1765)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian, jurist, and politician (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian, jurist, and politician (b. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, Australian businesswoman, (b. 1777)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businesswoman, (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businesswoman, (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON>, Indian Muslim scholar, (b. 1800)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Muslim scholar, (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Muslim scholar, (b. 1800)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>i"}]}, {"year": "1892", "text": "<PERSON>, American author, correspondent, and poet (b. 1835)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, correspondent, and poet (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, correspondent, and poet (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American lawyer and judge (b. 1786)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Peruvian military leader and politician, President of Peru (b. 1797)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Castilla\" title=\"<PERSON>\"><PERSON></a>, Peruvian military leader and politician, President of Peru (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Castilla\" title=\"<PERSON>\"><PERSON></a>, Peruvian military leader and politician, President of Peru (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Castilla"}]}, {"year": "1901", "text": "<PERSON>, Belgian mathematician, lawyer, and jurist (b. 1841)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hondt\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician, lawyer, and jurist (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hondt\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician, lawyer, and jurist (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_D%27Hondt"}]}, {"year": "1911", "text": "<PERSON>, American businessman, founded the Milton Bradley Company (b. 1836)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Milton Bradley\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Milton_Bradley_Company\" title=\"Milton Bradley Company\">Milton Bradley Company</a> (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bradley\" title=\"Milton Bradley\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Milton_Bradley_Company\" title=\"Milton Bradley Company\">Milton Bradley Company</a> (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Milton Bradley Company", "link": "https://wikipedia.org/wiki/Milton_Bradley_Company"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, American pilot and businessman, co-founded the Wright Company (b. 1867)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a>, American pilot and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Wright_Company\" title=\"Wright Company\">Wright Company</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pilot and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Wright_Company\" title=\"Wright Company\">Wright Company</a> (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}, {"title": "Wright Company", "link": "https://wikipedia.org/wiki/Wright_Company"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Russian philosopher and theorist (b. 1856)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian philosopher and theorist (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian philosopher and theorist (b. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Bengali writer and social activist (b. 1858)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengali writer and social activist (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengali writer and social activist (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, German historian and author (b. 1876)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and author (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and author (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Russian mathematician and physicist (b. 1864)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Russian mathematician and physicist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Russian mathematician and physicist (b. 1864)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese admiral (b. 1848)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/T%C5%8Dg%C5%8D_Heihachir%C5%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C5%8Dg%C5%8D_Heihachir%C5%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (b. 1848)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C5%8Dg%C5%8D_Heihachir%C5%8D"}]}, {"year": "1939", "text": "<PERSON>, American race car driver (b. 1904)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Thai king (b. 1893)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>raj<PERSON><PERSON><PERSON>\" title=\"<PERSON>raj<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Thai king (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Thai king (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Praj<PERSON>hipok"}]}, {"year": "1946", "text": "<PERSON>, Canadian physicist and chemist (b. 1910)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and chemist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and chemist (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Austrian captain (b. 1880)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian captain (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian captain (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovene-Hungarian priest and politician (b. 1874)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene-Hungarian priest and politician (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene-Hungarian priest and politician (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(politician)"}]}, {"year": "1949", "text": "<PERSON>, French cardinal (b. 1874)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emmanuel_C%C3%A9<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Austrian-American author (b. 1886)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American author (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American actor and singer (b. 1886)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American race car driver (b. 1918)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Italian race car driver (b. 1921)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Russian poet, novelist, and literary translator, Nobel Prize laureate (b. 1890)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet, novelist, and literary translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet, novelist, and literary translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1961", "text": "<PERSON>, Dominican soldier and politician, 36th President of the Dominican Republic (b. 1891)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Dominican_Republic\" class=\"mw-redirect\" title=\"List of Presidents of the Dominican Republic\">President of the Dominican Republic</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Dominican_Republic\" class=\"mw-redirect\" title=\"List of Presidents of the Dominican Republic\">President of the Dominican Republic</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the Dominican Republic", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Dominican_Republic"}]}, {"year": "1964", "text": "<PERSON>, Nigerian king (b. 1882)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian king (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian king (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American race car driver (b. 1927)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Hungarian-American physicist and engineer (b. 1898)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American physicist and engineer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American physicist and engineer (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd"}]}, {"year": "1965", "text": "<PERSON>, Danish linguist and academic (b. 1899)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish linguist and academic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish linguist and academic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English-American actor (b. 1889)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, French organist and composer (b. 1886)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Dupr%C3%A9"}]}, {"year": "1975", "text": "<PERSON>, American runner (b. 1951)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese martial artist, founded <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (b. 1908)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Isshin-ry%C5%AB\" title=\"Isshin-ryū\"><PERSON><PERSON>-ryū</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Isshin-ry%C5%AB\" title=\"Isshin-ryū\"><PERSON><PERSON>-ryū</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Isshin-ry<PERSON>", "link": "https://wikipedia.org/wiki/Isshin-ry%C5%AB"}]}, {"year": "1975", "text": "<PERSON>, Swiss-born French actor (b. 1895)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-born French actor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-born French actor (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player, coach, and manager (b. 1890)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese captain (b. 1902)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese captain (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese captain (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian violinist, composer, and conductor (b. 1909)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, and conductor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, and conductor (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American bass player and producer (b. 1942)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player (b. 1955)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Bangladeshi general and politician, 7th President of Bangladesh (b. 1936)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}]}, {"year": "1982", "text": "<PERSON>, German journalist and politician (b. 1904)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Mexican journalist and political columnist (b. 1926)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Buen<PERSON>%C3%ADa\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Mexican journalist and political columnist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>%C3%ADa\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Mexican journalist and political columnist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_Buend%C3%ADa"}]}, {"year": "1986", "text": "<PERSON>, American fashion designer, founded his own eponymous fashion brand (b. 1940)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(brand)\" title=\"<PERSON> (brand)\">his own eponymous fashion brand</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>(brand)\" title=\"<PERSON> (brand)\">his own eponymous fashion brand</a> (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (brand)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)"}]}, {"year": "1993", "text": "<PERSON>, American pianist, composer, and bandleader (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Sun_Ra\" title=\"Sun Ra\"><PERSON></a>, American pianist, composer, and bandleader (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sun_Ra\" title=\"Sun Ra\"><PERSON></a>, American pianist, composer, and bandleader (b. 1914)", "links": [{"title": "Sun Ra", "link": "https://wikipedia.org/wiki/Sun_Ra"}]}, {"year": "1994", "text": "<PERSON>, American religious leader, 13th President of The Church of Jesus Christ of Latter-day Saints (b. 1899)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 13th <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_the_Church_of_Jesus_Christ_of_Latter-day_Saints\" title=\"List of presidents of the Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 13th <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_the_Church_of_Jesus_Christ_of_Latter-day_Saints\" title=\"List of presidents of the Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of presidents of the Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/List_of_presidents_of_the_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1994", "text": "<PERSON>, Italian-French businessman, co-founded Société Bic (b. 1914)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French businessman, co-founded <a href=\"https://wikipedia.org/wiki/Soci%C3%A9t%C3%A9_Bic\" class=\"mw-redirect\" title=\"Société Bic\">Société Bic</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French businessman, co-founded <a href=\"https://wikipedia.org/wiki/Soci%C3%A9t%C3%A9_Bic\" class=\"mw-redirect\" title=\"Société Bic\">Société Bic</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Société Bic", "link": "https://wikipedia.org/wiki/Soci%C3%A9t%C3%A9_Bic"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Italian footballer (b. 1955)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Agostino Di Bartolomei\"><PERSON><PERSON><PERSON></a>, Italian footballer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Agostino Di Bartolomei\"><PERSON><PERSON><PERSON></a>, Italian footballer (b. 1955)", "links": [{"title": "Agostino Di <PERSON>olomei", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Di_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English footballer and manager (b. 1912)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, English-Austrian engineer (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Lofty_England\" title=\"Lofty England\">Lofty England</a>, English-Austrian engineer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lofty_England\" title=\"Lofty England\">Lofty England</a>, English-Austrian engineer (b. 1911)", "links": [{"title": "Lofty England", "link": "https://wikipedia.org/wiki/Lofty_England"}]}, {"year": "1995", "text": "<PERSON>, English footballer (b. 1951)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, French cardinal (b. 1903)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on-%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on-%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on-%C3%89tienne_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Estonian composer (b. 1961)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer (b. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Estonian poet and author (b. 1920)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and author (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American saxophonist and bandleader (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and bandleader (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and bandleader (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tex_Beneke"}]}, {"year": "2001", "text": "<PERSON>, Canadian general and historian (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general and historian (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general and historian (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Acadian poet (b. 1945)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Acadian poet (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Acadian poet (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Polish journalist and author (b. 1958)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pacy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and author (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pacy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and author (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tomasz_Pacy%C5%84ski"}]}, {"year": "2005", "text": "<PERSON>, American baseball player and stenographer (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and stenographer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and stenographer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON>, New Zealand biologist and academic (b. 1938)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, New Zealand biologist and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, New Zealand biologist and academic (b. 1938)", "links": [{"title": "<PERSON> (botanist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(botanist)"}]}, {"year": "2006", "text": "<PERSON>, American actor (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Algerian-French actor and director (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian-French actor and director (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian-French actor and director (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Norwegian politician (b. 1907)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Birgit_Dalland\" title=\"Birgit Dalland\"><PERSON><PERSON><PERSON></a>, Norwegian politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birgit_Dalland\" title=\"Birgit Dalland\"><PERSON><PERSON><PERSON></a>, Norwegian politician (b. 1907)", "links": [{"title": "Birgit Dalland", "link": "https://wikipedia.org/wiki/Birgit_Dalland"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Indian poet and critic (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and critic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and critic (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Swedish painter and illustrator (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish painter and illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish painter and illustrator (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Finnish politician (b. 1966)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli biophysicist and politician, 4th President of Israel (b. 1916)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli biophysicist and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli biophysicist and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "2010", "text": "<PERSON>, Russian volleyball player and coach (b. 1933)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(volleyball)\" title=\"<PERSON> (volleyball)\"><PERSON></a>, Russian volleyball player and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(volleyball)\" title=\"<PERSON> (volleyball)\"><PERSON></a>, Russian volleyball player and coach (b. 1933)", "links": [{"title": "<PERSON> (volleyball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(volleyball)"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Canadian commander and politician, 14th Premier of Manitoba (b. 1917)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian commander and politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian commander and politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Manitoba", "link": "https://wikipedia.org/wiki/Premier_of_Manitoba"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Fijian police officer and diplomat (b. 1952)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Is<PERSON><PERSON>_Savua\" title=\"<PERSON><PERSON><PERSON> Savua\"><PERSON><PERSON><PERSON></a>, Fijian police officer and diplomat (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Savua\"><PERSON><PERSON><PERSON></a>, Fijian police officer and diplomat (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isikia_Savua"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Pakistani journalist (b. 1970)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani journalist (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani journalist (b. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Polish philosopher and historian (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish philosopher and historian (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish philosopher and historian (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1917)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American physicist and academic, Nobel Prize laureate (b. 1921)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sussman Yalow\"><PERSON><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sussman Yalow\"><PERSON><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2012", "text": "<PERSON>, American comedian, actor, and screenwriter (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1957)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "2012", "text": "<PERSON>, English physiologist and biophysicist, Nobel Prize laureate (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2012", "text": "<PERSON>, German economist and politician (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German economist and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German economist and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American basketball player and sportscaster (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Sri Lankan physician and politician (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan physician and politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan physician and politician (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player and coach (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1933)", "links": [{"title": "<PERSON> (American football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian poet, journalist, and diplomat (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Hienadz_Buraukin\" title=\"Hienadz Buraukin\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian poet, journalist, and diplomat (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hienadz_Buraukin\" title=\"Hienadz Buraukin\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian poet, journalist, and diplomat (b. 1936)", "links": [{"title": "Hienadz <PERSON>", "link": "https://wikipedia.org/wiki/Hiena<PERSON>z_B<PERSON>ukin"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Danish director, producer, and screenwriter (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish director, producer, and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish director, producer, and screenwriter (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, British actress (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Greek admiral (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek admiral (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek admiral (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American soldier, lawyer, and politician, 44th Attorney General of Delaware (b. 1969)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 44th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Delaware\" title=\"Attorney General of Delaware\">Attorney General of Delaware</a> (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 44th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Delaware\" title=\"Attorney General of Delaware\">Attorney General of Delaware</a> (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney General of Delaware", "link": "https://wikipedia.org/wiki/Attorney_General_of_Delaware"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Canadian author and screenwriter (b. 1957)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Jo%C3%ABl_Champetier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author and screenwriter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%ABl_Champetier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author and screenwriter (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%ABl_Champetier"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON>, American religious leader and member of the Quorum of the Twelve Apostles of the Church of Jesus Christ of Latter-day Saints (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American religious leader and member of the <a href=\"https://wikipedia.org/wiki/Quorum_of_the_Twelve_Apostles_(LDS_Church)\" title=\"Quorum of the Twelve Apostles (LDS Church)\">Quorum of the Twelve Apostles</a> of <a href=\"https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints\" title=\"The Church of Jesus Christ of Latter-day Saints\">the Church of Jesus Christ of Latter-day Saints</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American religious leader and member of the <a href=\"https://wikipedia.org/wiki/Quorum_of_the_Twelve_Apostles_(LDS_Church)\" title=\"Quorum of the Twelve Apostles (LDS Church)\">Quorum of the Twelve Apostles</a> of <a href=\"https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints\" title=\"The Church of Jesus Christ of Latter-day Saints\">the Church of Jesus Christ of Latter-day Saints</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Quorum of the Twelve Apostles (LDS Church)", "link": "https://wikipedia.org/wiki/Quorum_of_the_Twelve_Apostles_(LDS_Church)"}, {"title": "The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "2016", "text": "<PERSON>, Polish-Canadian ice hockey player (b. 1953)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian ice hockey player (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian ice hockey player (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Canadian ice hockey player (b. 1950)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American lawyer and politician (b. 1937)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Thad_<PERSON>\" title=\"Thad <PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON>\" title=\"T<PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thad_<PERSON><PERSON>ran"}]}, {"year": "2019", "text": "<PERSON>, Trinidadian footballer (b. 1983)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian footballer (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian footballer (b. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, British actor (b. 1944)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Swiss motorcycle road racer (b. 2001)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss motorcycle road racer (b. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss motorcycle road racer (b. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON>, French nurse (b. 1925)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Genevi%C3%A8ve_de_<PERSON>\" title=\"<PERSON>vi<PERSON><PERSON> de <PERSON>rd\"><PERSON><PERSON><PERSON><PERSON></a>, French nurse (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Genevi%C3%A8ve_de_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> de <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French nurse (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Genevi%C3%A8ve_de_<PERSON>rd"}]}, {"year": "2024", "text": "<PERSON>, American professional basketball player (b. 1990)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional basketball player (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional basketball player (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}