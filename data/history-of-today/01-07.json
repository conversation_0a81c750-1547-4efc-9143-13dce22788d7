{"date": "January 7", "url": "https://wikipedia.org/wiki/January_7", "data": {"Events": [{"year": "49 BC", "text": "The Senate of Rome says that <PERSON> will be declared a public enemy unless he disbands his army, prompting the tribunes who support him to flee to where <PERSON> is waiting in Ravenna.", "html": "49 BC - 49 BC - The <a href=\"https://wikipedia.org/wiki/Senate_of_the_Roman_Republic\" title=\"Senate of the Roman Republic\">Senate of Rome</a> says that <a href=\"https://wikipedia.org/wiki/Caesar\" class=\"mw-redirect\" title=\"Caesar\"><PERSON></a> will be declared a public enemy unless he disbands his army, prompting the tribunes who support him to flee to where <PERSON> is waiting in <a href=\"https://wikipedia.org/wiki/Ravenna\" title=\"Ravenna\">Ravenna</a>.", "no_year_html": "49 BC - The <a href=\"https://wikipedia.org/wiki/Senate_of_the_Roman_Republic\" title=\"Senate of the Roman Republic\">Senate of Rome</a> says that <a href=\"https://wikipedia.org/wiki/Caesar\" class=\"mw-redirect\" title=\"Caesar\"><PERSON></a> will be declared a public enemy unless he disbands his army, prompting the tribunes who support him to flee to where <PERSON> is waiting in <a href=\"https://wikipedia.org/wiki/Ravenna\" title=\"Ravenna\">Ravenna</a>.", "links": [{"title": "Senate of the Roman Republic", "link": "https://wikipedia.org/wiki/Senate_of_the_Roman_Republic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Caesar"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ravenna"}]}, {"year": "1325", "text": "<PERSON><PERSON><PERSON> becomes King of Portugal.", "html": "1325 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Portugal\" title=\"<PERSON><PERSON><PERSON> IV of Portugal\"><PERSON><PERSON><PERSON> IV</a> becomes <a href=\"https://wikipedia.org/wiki/List_of_Portuguese_monarchs\" title=\"List of Portuguese monarchs\">King of Portugal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Portugal\" title=\"<PERSON><PERSON><PERSON> IV of Portugal\"><PERSON><PERSON><PERSON> IV</a> becomes <a href=\"https://wikipedia.org/wiki/List_of_Portuguese_monarchs\" title=\"List of Portuguese monarchs\">King of Portugal</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Afonso_IV_of_Portugal"}, {"title": "List of Portuguese monarchs", "link": "https://wikipedia.org/wiki/List_of_Portuguese_monarchs"}]}, {"year": "1558", "text": "French troops, led by <PERSON>, Duke of Guise, take Calais, the last continental possession of England.", "html": "1558 - French troops, led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a>, <a href=\"https://wikipedia.org/wiki/Siege_of_Calais_(1558)\" title=\"Siege of Calais (1558)\">take Calais</a>, the last continental possession of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "no_year_html": "French troops, led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a>, <a href=\"https://wikipedia.org/wiki/Siege_of_Calais_(1558)\" title=\"Siege of Calais (1558)\">take Calais</a>, the last continental possession of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "links": [{"title": "<PERSON>, Duke of Guise", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_Guise"}, {"title": "Siege of Calais (1558)", "link": "https://wikipedia.org/wiki/Siege_of_Calais_(1558)"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1608", "text": "Fire destroys Jamestown, Virginia.", "html": "1608 - Fire destroys <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown, Virginia</a>.", "no_year_html": "Fire destroys <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">Jamestown, Virginia</a>.", "links": [{"title": "Jamestown, Virginia", "link": "https://wikipedia.org/wiki/Jamestown,_Virginia"}]}, {"year": "1610", "text": "<PERSON> makes his first observation of the four Galilean moons: Ganymede, Callisto, Io and Europa, although he is not able to distinguish the last two until the following night.", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_G<PERSON>lei\" title=\"<PERSON>lei\"><PERSON></a> makes his first observation of the four <a href=\"https://wikipedia.org/wiki/Galilean_moons\" title=\"Galilean moons\">Galilean moons</a>: <a href=\"https://wikipedia.org/wiki/Ganymede_(moon)\" title=\"Ganymede (moon)\">Ganymede</a>, <a href=\"https://wikipedia.org/wiki/Callisto_(moon)\" title=\"Callisto (moon)\">Callisto</a>, <a href=\"https://wikipedia.org/wiki/Io_(moon)\" title=\"Io (moon)\">Io</a> and <a href=\"https://wikipedia.org/wiki/Europa_(moon)\" title=\"Europa (moon)\">Europa</a>, although he is not able to distinguish the last two until the following night.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Galilei\"><PERSON></a> makes his first observation of the four <a href=\"https://wikipedia.org/wiki/Galilean_moons\" title=\"Galilean moons\">Galilean moons</a>: <a href=\"https://wikipedia.org/wiki/Ganymede_(moon)\" title=\"Ganymede (moon)\">Ganymede</a>, <a href=\"https://wikipedia.org/wiki/Callisto_(moon)\" title=\"Callisto (moon)\">Callist<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Io_(moon)\" title=\"Io (moon)\">Io</a> and <a href=\"https://wikipedia.org/wiki/Europa_(moon)\" title=\"Europa (moon)\">Europa</a>, although he is not able to distinguish the last two until the following night.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gali<PERSON>i"}, {"title": "Galilean moons", "link": "https://wikipedia.org/wiki/Galilean_moons"}, {"title": "Ganymede (moon)", "link": "https://wikipedia.org/wiki/Ganymede_(moon)"}, {"title": "<PERSON><PERSON><PERSON> (moon)", "link": "https://wikipedia.org/wiki/Call<PERSON><PERSON>_(moon)"}, {"title": "<PERSON><PERSON> (moon)", "link": "https://wikipedia.org/wiki/Io_(moon)"}, {"title": "Europa (moon)", "link": "https://wikipedia.org/wiki/Europa_(moon)"}]}, {"year": "1708", "text": "Battle of Zlatoust: Battle between Bashkir and Tatar rebels and the government troops of the Tsardom of Russia. It is one of the events of the Bashkir rebellion of 1704-1711.", "html": "1708 - Battle of Zlatoust: Battle between Bashkir and Tatar rebels and the government troops of the <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Tsardom of Russia</a>. It is one of the events of the <a href=\"https://wikipedia.org/wiki/Bashkir_rebellion_of_1704%E2%80%931711\" title=\"Bashkir rebellion of 1704-1711\">Bashkir rebellion of 1704-1711</a>.", "no_year_html": "Battle of Zlatoust: Battle between Bashkir and Tatar rebels and the government troops of the <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Tsardom of Russia</a>. It is one of the events of the <a href=\"https://wikipedia.org/wiki/Bashkir_rebellion_of_1704%E2%80%931711\" title=\"Bashkir rebellion of 1704-1711\">Bashkir rebellion of 1704-1711</a>.", "links": [{"title": "Tsardom of Russia", "link": "https://wikipedia.org/wiki/Tsardom_of_Russia"}, {"title": "Bashkir rebellion of 1704-1711", "link": "https://wikipedia.org/wiki/Bashkir_rebellion_of_1704%E2%80%931711"}]}, {"year": "1708", "text": "Bashkir rebels besiege Yelabuga.", "html": "1708 - Bashkir rebels <a href=\"https://wikipedia.org/wiki/Siege_of_Yelabuga_(1708)\" title=\"Siege of Yelabuga (1708)\">besiege</a> <a href=\"https://wikipedia.org/wiki/Yelabuga\" title=\"Yelabuga\">Yelabuga</a>.", "no_year_html": "Bashkir rebels <a href=\"https://wikipedia.org/wiki/Siege_of_Yelabuga_(1708)\" title=\"Siege of Yelabuga (1708)\">besiege</a> <a href=\"https://wikipedia.org/wiki/Yelabuga\" title=\"Yelabuga\">Yelabuga</a>.", "links": [{"title": "Siege of Yelabuga (1708)", "link": "https://wikipedia.org/wiki/Siege_of_Yelabuga_(1708)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yelabuga"}]}, {"year": "1738", "text": "A peace treaty is signed between Peshwa Bajirao and <PERSON> following Maratha victory in the Battle of Bhopal.", "html": "1738 - A peace treaty is signed between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rao_I\" class=\"mw-redirect\" title=\"<PERSON><PERSON> I\"><PERSON><PERSON><PERSON> Baji<PERSON>o</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Singh II\"><PERSON> II</a> following <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha</a> victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Bhopal\" title=\"Battle of Bhopal\">Battle of Bhopal</a>.", "no_year_html": "A peace treaty is signed between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rao_I\" class=\"mw-redirect\" title=\"<PERSON><PERSON> I\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Singh II\"><PERSON> II</a> following <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha</a> victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Bhopal\" title=\"Battle of Bhopal\">Battle of Bhopal</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}, {"title": "Battle of Bhopal", "link": "https://wikipedia.org/wiki/Battle_of_Bhopal"}]}, {"year": "1782", "text": "The first American commercial bank, the Bank of North America, opens.", "html": "1782 - The first American commercial bank, the <a href=\"https://wikipedia.org/wiki/Bank_of_North_America\" title=\"Bank of North America\">Bank of North America</a>, opens.", "no_year_html": "The first American commercial bank, the <a href=\"https://wikipedia.org/wiki/Bank_of_North_America\" title=\"Bank of North America\">Bank of North America</a>, opens.", "links": [{"title": "Bank of North America", "link": "https://wikipedia.org/wiki/Bank_of_North_America"}]}, {"year": "1785", "text": "Frenchman <PERSON><PERSON><PERSON> and American <PERSON> travel from Dover, England, to Calais, France, in a gas balloon.", "html": "1785 - Frenchman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and American <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> travel from <a href=\"https://wikipedia.org/wiki/Dover\" title=\"Dover\">Dover, England</a>, to <a href=\"https://wikipedia.org/wiki/Calais\" title=\"Calais\">Calais, France</a>, in a gas <a href=\"https://wikipedia.org/wiki/Balloon\" title=\"Balloon\">balloon</a>.", "no_year_html": "Frenchman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and American <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> travel from <a href=\"https://wikipedia.org/wiki/Dover\" title=\"Dover\">Dover, England</a>, to <a href=\"https://wikipedia.org/wiki/Calais\" title=\"Calais\">Calais, France</a>, in a gas <a href=\"https://wikipedia.org/wiki/Balloon\" title=\"Balloon\">balloon</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dover", "link": "https://wikipedia.org/wiki/Dover"}, {"title": "Calais", "link": "https://wikipedia.org/wiki/Calais"}, {"title": "Balloon", "link": "https://wikipedia.org/wiki/<PERSON>oon"}]}, {"year": "1835", "text": "HMS Beagle, with <PERSON> on board, drops anchor off the Chonos Archipelago.", "html": "1835 - <a href=\"https://wikipedia.org/wiki/HMS_Beagle\" title=\"HMS Beagle\">HMS <i><PERSON>agle</i></a>, with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> on board, drops anchor off the <a href=\"https://wikipedia.org/wiki/Chonos_Archipelago\" title=\"Chonos Archipelago\">Chonos Archipelago</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Beagle\" title=\"HMS Beagle\">HMS <i><PERSON><PERSON><PERSON></i></a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> on board, drops anchor off the <a href=\"https://wikipedia.org/wiki/Chonos_Archipelago\" title=\"Chonos Archipelago\">Chonos Archipelago</a>.", "links": [{"title": "HMS Beagle", "link": "https://wikipedia.org/wiki/HMS_Beagle"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chonos Archipelago", "link": "https://wikipedia.org/wiki/Chonos_Archipelago"}]}, {"year": "1867", "text": "The Kingstree jail fire kills 22 freedmen in Reconstruction-era South Carolina.", "html": "1867 - The <a href=\"https://wikipedia.org/wiki/Kingstree_jail_fire\" title=\"Kingstree jail fire\">Kingstree jail fire</a> kills 22 freedmen in <a href=\"https://wikipedia.org/wiki/Reconstruction_era\" title=\"Reconstruction era\">Reconstruction</a>-era <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingstree_jail_fire\" title=\"Kingstree jail fire\">Kingstree jail fire</a> kills 22 freedmen in <a href=\"https://wikipedia.org/wiki/Reconstruction_era\" title=\"Reconstruction era\">Reconstruction</a>-era <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>.", "links": [{"title": "Kingstree jail fire", "link": "https://wikipedia.org/wiki/Kingstree_jail_fire"}, {"title": "Reconstruction era", "link": "https://wikipedia.org/wiki/Reconstruction_era"}, {"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}]}, {"year": "1894", "text": "<PERSON> makes a kinetoscopic film of someone sneezing. On the same day, his employee, <PERSON>, receives a patent for motion picture film.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas <PERSON>\"><PERSON></a> makes a kinetoscopic film of someone sneezing. On the same day, his employee, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, receives a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">motion picture film</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> makes a kinetoscopic film of someone sneezing. On the same day, his employee, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, receives a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">motion picture film</a>.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Film", "link": "https://wikipedia.org/wiki/Film"}]}, {"year": "1904", "text": "The distress signal \"CQD\" is established only to be replaced two years later by \"SOS\".", "html": "1904 - The <a href=\"https://wikipedia.org/wiki/Distress_signal\" title=\"Distress signal\">distress signal</a> \"<a href=\"https://wikipedia.org/wiki/CQD\" title=\"CQD\">CQD</a>\" is established only to be replaced two years later by \"<a href=\"https://wikipedia.org/wiki/SOS\" title=\"SOS\">SOS</a>\".", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Distress_signal\" title=\"Distress signal\">distress signal</a> \"<a href=\"https://wikipedia.org/wiki/CQD\" title=\"CQD\">CQD</a>\" is established only to be replaced two years later by \"<a href=\"https://wikipedia.org/wiki/SOS\" title=\"SOS\">SOS</a>\".", "links": [{"title": "Distress signal", "link": "https://wikipedia.org/wiki/Distress_signal"}, {"title": "CQD", "link": "https://wikipedia.org/wiki/CQD"}, {"title": "SOS", "link": "https://wikipedia.org/wiki/SOS"}]}, {"year": "1919", "text": "Montenegrin guerrilla fighters rebel against the planned annexation of Montenegro by Serbia, but fail.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegrin</a> guerrilla fighters <a href=\"https://wikipedia.org/wiki/Christmas_Uprising\" title=\"Christmas Uprising\">rebel</a> against the planned annexation of Montenegro by <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>, but fail.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegrin</a> guerrilla fighters <a href=\"https://wikipedia.org/wiki/Christmas_Uprising\" title=\"Christmas Uprising\">rebel</a> against the planned annexation of Montenegro by <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>, but fail.", "links": [{"title": "Montenegro", "link": "https://wikipedia.org/wiki/Montenegro"}, {"title": "Christmas Uprising", "link": "https://wikipedia.org/wiki/Christmas_Uprising"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}]}, {"year": "1920", "text": "The New York State Assembly refuses to seat five duly elected Socialist assemblymen.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/New_York_State_Assembly\" title=\"New York State Assembly\">New York State Assembly</a> refuses to seat five duly elected <a href=\"https://wikipedia.org/wiki/Socialist_Party_of_America\" title=\"Socialist Party of America\">Socialist</a> assemblymen.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_York_State_Assembly\" title=\"New York State Assembly\">New York State Assembly</a> refuses to seat five duly elected <a href=\"https://wikipedia.org/wiki/Socialist_Party_of_America\" title=\"Socialist Party of America\">Socialist</a> assemblymen.", "links": [{"title": "New York State Assembly", "link": "https://wikipedia.org/wiki/New_York_State_Assembly"}, {"title": "Socialist Party of America", "link": "https://wikipedia.org/wiki/Socialist_Party_of_America"}]}, {"year": "1922", "text": "Dáil Éireann ratifies the Anglo-Irish Treaty by a 64-57 vote.[citation needed]", "html": "1922 - <a href=\"https://wikipedia.org/wiki/D%C3%A1il_%C3%89ireann\" title=\"Dáil Éireann\">Dáil Éireann</a> ratifies the <a href=\"https://wikipedia.org/wiki/Anglo-Irish_Treaty\" title=\"Anglo-Irish Treaty\">Anglo-Irish Treaty</a> by a 64-57 vote.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A1il_%C3%89ireann\" title=\"Dáil Éireann\">Dáil Éireann</a> ratifies the <a href=\"https://wikipedia.org/wiki/Anglo-Irish_Treaty\" title=\"Anglo-Irish Treaty\">Anglo-Irish Treaty</a> by a 64-57 vote.", "links": [{"title": "Dáil Éireann", "link": "https://wikipedia.org/wiki/D%C3%A1il_%C3%89ireann"}, {"title": "Anglo-Irish Treaty", "link": "https://wikipedia.org/wiki/Anglo-Irish_Treaty"}]}, {"year": "1927", "text": "The first transatlantic commercial telephone service is established from New York City to London.", "html": "1927 - The first <a href=\"https://wikipedia.org/wiki/History_of_the_telephone\" title=\"History of the telephone\">transatlantic commercial telephone service</a> is established from New York City to London.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/History_of_the_telephone\" title=\"History of the telephone\">transatlantic commercial telephone service</a> is established from New York City to London.", "links": [{"title": "History of the telephone", "link": "https://wikipedia.org/wiki/History_of_the_telephone"}]}, {"year": "1928", "text": "A disastrous flood of the River Thames kills 14 people and causes extensive damage to much of riverside London.", "html": "1928 - A disastrous <a href=\"https://wikipedia.org/wiki/1928_Thames_flood\" title=\"1928 Thames flood\">flood of the River Thames</a> kills 14 people and causes extensive damage to much of riverside London.", "no_year_html": "A disastrous <a href=\"https://wikipedia.org/wiki/1928_Thames_flood\" title=\"1928 Thames flood\">flood of the River Thames</a> kills 14 people and causes extensive damage to much of riverside London.", "links": [{"title": "1928 Thames flood", "link": "https://wikipedia.org/wiki/1928_Thames_flood"}]}, {"year": "1931", "text": "<PERSON> flies the first solo non-stop trans-Tasman flight (from Australia to New Zealand) in 11 hours and 45 minutes, crash-landing on New Zealand's west coast.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies the first solo non-stop <a href=\"https://wikipedia.org/wiki/Trans-Tasman\" title=\"Trans-Tasman\">trans-Tasman</a> flight (from Australia to New Zealand) in 11 hours and 45 minutes, crash-landing on New Zealand's <a href=\"https://wikipedia.org/wiki/Westland_District\" title=\"Westland District\">west coast</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies the first solo non-stop <a href=\"https://wikipedia.org/wiki/Trans-Tasman\" title=\"Trans-Tasman\">trans-Tasman</a> flight (from Australia to New Zealand) in 11 hours and 45 minutes, crash-landing on New Zealand's <a href=\"https://wikipedia.org/wiki/Westland_District\" title=\"Westland District\">west coast</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Trans-Tasman", "link": "https://wikipedia.org/wiki/Trans-Tasman"}, {"title": "Westland District", "link": "https://wikipedia.org/wiki/Westland_District"}]}, {"year": "1935", "text": "<PERSON> and French Foreign Minister <PERSON> sign the Franco-Italian Agreement.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a> and French <a href=\"https://wikipedia.org/wiki/Foreign_Minister\" class=\"mw-redirect\" title=\"Foreign Minister\">Foreign Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Franco-Italian_Agreement\" class=\"mw-redirect\" title=\"Franco-Italian Agreement\">Franco-Italian Agreement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a> and French <a href=\"https://wikipedia.org/wiki/Foreign_Minister\" class=\"mw-redirect\" title=\"Foreign Minister\">Foreign Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Franco-Italian_Agreement\" class=\"mw-redirect\" title=\"Franco-Italian Agreement\">Franco-Italian Agreement</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Foreign Minister", "link": "https://wikipedia.org/wiki/Foreign_Minister"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Franco-Italian Agreement", "link": "https://wikipedia.org/wiki/Franco-Italian_Agreement"}]}, {"year": "1940", "text": "Winter War: Battle of Raate Road: The Finnish 9th Division finally defeat the numerically superior Soviet forces on the Raate-Suomussalmi road.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Raate_Road\" title=\"Battle of Raate Road\">Battle of Raate Road</a>: The Finnish 9th Division finally defeat the numerically superior Soviet forces on the Raate-Suomussalmi road.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Raate_Road\" title=\"Battle of Raate Road\">Battle of Raate Road</a>: The Finnish 9th Division finally defeat the numerically superior Soviet forces on the Raate-Suomussalmi road.", "links": [{"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}, {"title": "Battle of Raate Road", "link": "https://wikipedia.org/wiki/Battle_of_Raate_Road"}]}, {"year": "1948", "text": "Kentucky Air National Guard pilot <PERSON> crashes while in pursuit of a supposed UFO.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Kentucky_Air_National_Guard\" title=\"Kentucky Air National Guard\">Kentucky Air National Guard</a> pilot <PERSON> crashes while <a href=\"https://wikipedia.org/wiki/Mantell_UFO_incident\" title=\"Mantell UFO incident\">in pursuit of</a> a supposed <a href=\"https://wikipedia.org/wiki/Unidentified_flying_object\" title=\"Unidentified flying object\">UFO</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kentucky_Air_National_Guard\" title=\"Kentucky Air National Guard\">Kentucky Air National Guard</a> pilot <PERSON> crashes while <a href=\"https://wikipedia.org/wiki/Mantell_UFO_incident\" title=\"Mantell UFO incident\">in pursuit of</a> a supposed <a href=\"https://wikipedia.org/wiki/Unidentified_flying_object\" title=\"Unidentified flying object\">UFO</a>.", "links": [{"title": "Kentucky Air National Guard", "link": "https://wikipedia.org/wiki/Kentucky_Air_National_Guard"}, {"title": "Mantell UFO incident", "link": "https://wikipedia.org/wiki/Mantell_UFO_incident"}, {"title": "Unidentified flying object", "link": "https://wikipedia.org/wiki/Unidentified_flying_object"}]}, {"year": "1950", "text": "In the Sverdlovsk air disaster, all 19 of those on board are killed, including almost the entire national ice hockey team (VVS Moscow) of the Soviet Air Force - 11 players, as well as a team doctor and a masseur.", "html": "1950 - In the <a href=\"https://wikipedia.org/wiki/1950_Sverdlovsk_air_disaster\" class=\"mw-redirect\" title=\"1950 Sverdlovsk air disaster\">Sverdlovsk air disaster</a>, all 19 of those on board are killed, including almost the entire national ice hockey team (<a href=\"https://wikipedia.org/wiki/VVS_Moscow\" title=\"VVS Moscow\">VVS Moscow</a>) of the <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet Air Force</a> - 11 players, as well as a team doctor and a masseur.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/1950_Sverdlovsk_air_disaster\" class=\"mw-redirect\" title=\"1950 Sverdlovsk air disaster\">Sverdlovsk air disaster</a>, all 19 of those on board are killed, including almost the entire national ice hockey team (<a href=\"https://wikipedia.org/wiki/VVS_Moscow\" title=\"VVS Moscow\">VVS Moscow</a>) of the <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet Air Force</a> - 11 players, as well as a team doctor and a masseur.", "links": [{"title": "1950 Sverdlovsk air disaster", "link": "https://wikipedia.org/wiki/1950_Sverdlovsk_air_disaster"}, {"title": "VVS Moscow", "link": "https://wikipedia.org/wiki/VVS_Moscow"}, {"title": "Soviet Air Forces", "link": "https://wikipedia.org/wiki/Soviet_Air_Forces"}]}, {"year": "1954", "text": "Georgetown-IBM experiment: The first public demonstration of a machine translation system is held in New York at the head office of IBM.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Georgetown%E2%80%93IBM_experiment\" title=\"Georgetown-IBM experiment\">Georgetown-IBM experiment</a>: The first public demonstration of a <a href=\"https://wikipedia.org/wiki/Machine_translation\" title=\"Machine translation\">machine translation</a> system is held in New York at the head office of <a href=\"https://wikipedia.org/wiki/IBM\" title=\"IBM\">IBM</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgetown%E2%80%93IBM_experiment\" title=\"Georgetown-IBM experiment\">Georgetown-IBM experiment</a>: The first public demonstration of a <a href=\"https://wikipedia.org/wiki/Machine_translation\" title=\"Machine translation\">machine translation</a> system is held in New York at the head office of <a href=\"https://wikipedia.org/wiki/IBM\" title=\"IBM\">IBM</a>.", "links": [{"title": "Georgetown-IBM experiment", "link": "https://wikipedia.org/wiki/Georgetown%E2%80%93IBM_experiment"}, {"title": "Machine translation", "link": "https://wikipedia.org/wiki/Machine_translation"}, {"title": "IBM", "link": "https://wikipedia.org/wiki/IBM"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON> becomes the first person of color to perform at the Metropolitan Opera in Giuseppe Verdi's Un ballo in maschera.", "html": "1955 - <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person of color to perform at the <a href=\"https://wikipedia.org/wiki/Metropolitan_Opera\" title=\"Metropolitan Opera\">Metropolitan Opera</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>_Verdi\" title=\"Giuseppe Verdi\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Un_ballo_in_maschera\" title=\"Un ballo in maschera\">Un ballo in maschera</a></i>.", "no_year_html": "<PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person of color to perform at the <a href=\"https://wikipedia.org/wiki/Metropolitan_Opera\" title=\"Metropolitan Opera\">Metropolitan Opera</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Verdi\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Un_ballo_in_maschera\" title=\"Un ballo in maschera\">Un ballo in maschera</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Metropolitan Opera", "link": "https://wikipedia.org/wiki/Metropolitan_Opera"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Un ballo in maschera", "link": "https://wikipedia.org/wiki/Un_ballo_in_maschera"}]}, {"year": "1959", "text": "The United States recognizes the new Cuban government of <PERSON><PERSON>.", "html": "1959 - The United States recognizes the new <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuban</a> government of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fidel <PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "The United States recognizes the new <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuban</a> government of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fidel <PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "Surveyor program: Surveyor 7, the last spacecraft in the Surveyor series, lifts off from Cape Canaveral Launch Complex 36A.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Surveyor_program\" title=\"Surveyor program\">Surveyor program</a>: <i><a href=\"https://wikipedia.org/wiki/Surveyor_7\" title=\"Surveyor 7\">Surveyor 7</a></i>, the last spacecraft in the Surveyor series, lifts off from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Launch_Complex_36A\" class=\"mw-redirect\" title=\"Cape Canaveral Launch Complex 36A\">Cape Canaveral Launch Complex 36A</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Surveyor_program\" title=\"Surveyor program\">Surveyor program</a>: <i><a href=\"https://wikipedia.org/wiki/Surveyor_7\" title=\"Surveyor 7\">Surveyor 7</a></i>, the last spacecraft in the Surveyor series, lifts off from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Launch_Complex_36A\" class=\"mw-redirect\" title=\"Cape Canaveral Launch Complex 36A\">Cape Canaveral Launch Complex 36A</a>.", "links": [{"title": "Surveyor program", "link": "https://wikipedia.org/wiki/Surveyor_program"}, {"title": "Surveyor 7", "link": "https://wikipedia.org/wiki/Surveyor_7"}, {"title": "Cape Canaveral Launch Complex 36A", "link": "https://wikipedia.org/wiki/Cape_Canaveral_Launch_Complex_36A"}]}, {"year": "1972", "text": "Iberia Flight 602 crashes near Ibiza Airport, killing all 104 people on board.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Iberia_Flight_602\" title=\"Iberia Flight 602\">Iberia Flight 602</a> crashes near <a href=\"https://wikipedia.org/wiki/Ibiza_Airport\" title=\"Ibiza Airport\">Ibiza Airport</a>, killing all 104 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iberia_Flight_602\" title=\"Iberia Flight 602\">Iberia Flight 602</a> crashes near <a href=\"https://wikipedia.org/wiki/Ibiza_Airport\" title=\"Ibiza Airport\">Ibiza Airport</a>, killing all 104 people on board.", "links": [{"title": "Iberia Flight 602", "link": "https://wikipedia.org/wiki/Iberia_Flight_602"}, {"title": "Ibiza Airport", "link": "https://wikipedia.org/wiki/Ibiza_Airport"}]}, {"year": "1973", "text": "In his second shooting spree of the week, <PERSON> fatally shoots seven people and wounds five others at <PERSON>'s Hotel in New Orleans, before being shot to death by police officers.", "html": "1973 - In his second shooting spree of the week, <a href=\"https://wikipedia.org/wiki/Mark_Essex\" title=\"Mark Essex\"><PERSON></a> fatally shoots seven people and wounds five others at <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s\" title=\"<PERSON>'s\"><PERSON>'s</a> Hotel in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>, before being shot to death by police officers.", "no_year_html": "In his second shooting spree of the week, <a href=\"https://wikipedia.org/wiki/Mark_Essex\" title=\"Mark Essex\"><PERSON></a> fatally shoots seven people and wounds five others at <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s\" title=\"<PERSON>'s\"><PERSON>'s</a> Hotel in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>, before being shot to death by police officers.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mark_<PERSON>"}, {"title": "<PERSON>'s", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s"}, {"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}]}, {"year": "1979", "text": "Third Indochina War: Cambodian-Vietnamese War: Phnom Penh falls to the advancing Vietnamese troops, driving out Pol Pot and the Khmer Rouge.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Sino-Vietnamese_War\" title=\"Sino-Vietnamese War\">Third Indochina War</a>: <a href=\"https://wikipedia.org/wiki/Cambodian%E2%80%93Vietnamese_War\" title=\"Cambodian-Vietnamese War\">Cambodian-Vietnamese War</a>: <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> falls to the advancing <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> troops, driving out <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sino-Vietnamese_War\" title=\"Sino-Vietnamese War\">Third Indochina War</a>: <a href=\"https://wikipedia.org/wiki/Cambodian%E2%80%93Vietnamese_War\" title=\"Cambodian-Vietnamese War\">Cambodian-Vietnamese War</a>: <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> falls to the advancing <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> troops, driving out <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a>.", "links": [{"title": "Sino-Vietnamese War", "link": "https://wikipedia.org/wiki/Sino-Vietnamese_War"}, {"title": "Cambodian-Vietnamese War", "link": "https://wikipedia.org/wiki/Cambodian%E2%80%93Vietnamese_War"}, {"title": "Phnom Penh", "link": "https://wikipedia.org/wiki/Phnom_Penh"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "Pol Po<PERSON>", "link": "https://wikipedia.org/wiki/Pol_Pot"}, {"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}]}, {"year": "1980", "text": "U.S. President <PERSON> authorizes legislation giving $1.5 billion in loans to bail out the Chrysler Corporation.", "html": "1980 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> authorizes legislation giving $1.5 billion in loans to bail out the <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> Corporation.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> authorizes legislation giving $1.5 billion in loans to bail out the <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> Corporation.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chrysler", "link": "https://wikipedia.org/wiki/Chrysler"}]}, {"year": "1984", "text": "Brunei becomes the sixth member of the Association of Southeast Asian Nations (ASEAN).", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Brunei\" title=\"Brunei\">Brunei</a> becomes the sixth member of the <a href=\"https://wikipedia.org/wiki/Association_of_Southeast_Asian_Nations\" class=\"mw-redirect\" title=\"Association of Southeast Asian Nations\">Association of Southeast Asian Nations</a> (ASEAN).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brunei\" title=\"Brunei\">Brunei</a> becomes the sixth member of the <a href=\"https://wikipedia.org/wiki/Association_of_Southeast_Asian_Nations\" class=\"mw-redirect\" title=\"Association of Southeast Asian Nations\">Association of Southeast Asian Nations</a> (ASEAN).", "links": [{"title": "Brunei", "link": "https://wikipedia.org/wiki/Brunei"}, {"title": "Association of Southeast Asian Nations", "link": "https://wikipedia.org/wiki/Association_of_Southeast_Asian_Nations"}]}, {"year": "1985", "text": "Japan Aerospace Exploration Agency launches <PERSON><PERSON>gake, Japan's first interplanetary spacecraft and the first deep space probe to be launched by any country other than the United States or the Soviet Union.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Japan_Aerospace_Exploration_Agency\" class=\"mw-redirect\" title=\"Japan Aerospace Exploration Agency\">Japan Aerospace Exploration Agency</a> launches <i><a href=\"https://wikipedia.org/wiki/Sakigake\" title=\"Sakigake\">Sakigake</a></i>, Japan's first <a href=\"https://wikipedia.org/wiki/Robotic_spacecraft\" class=\"mw-redirect\" title=\"Robotic spacecraft\">interplanetary spacecraft</a> and the first deep <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> to be launched by any country other than the United States or the Soviet Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japan_Aerospace_Exploration_Agency\" class=\"mw-redirect\" title=\"Japan Aerospace Exploration Agency\">Japan Aerospace Exploration Agency</a> launches <i><a href=\"https://wikipedia.org/wiki/Sakigake\" title=\"Sakigake\">Sakigake</a></i>, Japan's first <a href=\"https://wikipedia.org/wiki/Robotic_spacecraft\" class=\"mw-redirect\" title=\"Robotic spacecraft\">interplanetary spacecraft</a> and the first deep <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> to be launched by any country other than the United States or the Soviet Union.", "links": [{"title": "Japan Aerospace Exploration Agency", "link": "https://wikipedia.org/wiki/Japan_Aerospace_Exploration_Agency"}, {"title": "Sakigake", "link": "https://wikipedia.org/wiki/Sakigake"}, {"title": "Robotic spacecraft", "link": "https://wikipedia.org/wiki/Robotic_spacecraft"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}]}, {"year": "1989", "text": "Sutton United, a team in the fifth tier of English league football, defeated top-tier Coventry City in one of the biggest upsets in FA Cup history.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Sutton_United_F.C.\" title=\"Sutton United F.C.\">Sutton United</a>, a team in the fifth tier of <a href=\"https://wikipedia.org/wiki/English_football_league_system\" title=\"English football league system\">English league football</a>, <a href=\"https://wikipedia.org/wiki/Sutton_United_2%E2%80%931_Coventry_City_(1989)\" title=\"Sutton United 2-1 Coventry City (1989)\">defeated</a> top-tier <a href=\"https://wikipedia.org/wiki/Coventry_City_F.C.\" title=\"Coventry City F.C.\">Coventry City</a> in one of the biggest upsets in <a href=\"https://wikipedia.org/wiki/FA_Cup\" title=\"FA Cup\">FA Cup</a> history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sutton_United_F.C.\" title=\"Sutton United F.C.\">Sutton United</a>, a team in the fifth tier of <a href=\"https://wikipedia.org/wiki/English_football_league_system\" title=\"English football league system\">English league football</a>, <a href=\"https://wikipedia.org/wiki/Sutton_United_2%E2%80%931_Coventry_City_(1989)\" title=\"Sutton United 2-1 Coventry City (1989)\">defeated</a> top-tier <a href=\"https://wikipedia.org/wiki/Coventry_City_F.C.\" title=\"Coventry City F.C.\">Coventry City</a> in one of the biggest upsets in <a href=\"https://wikipedia.org/wiki/FA_Cup\" title=\"FA Cup\">FA Cup</a> history.", "links": [{"title": "Sutton United F.C.", "link": "https://wikipedia.org/wiki/Sutton_United_F.C."}, {"title": "English football league system", "link": "https://wikipedia.org/wiki/English_football_league_system"}, {"title": "Sutton United 2-1 Coventry City (1989)", "link": "https://wikipedia.org/wiki/Sutton_United_2%E2%80%931_Coventry_City_(1989)"}, {"title": "Coventry City F.C.", "link": "https://wikipedia.org/wiki/Coventry_City_F.C."}, {"title": "FA Cup", "link": "https://wikipedia.org/wiki/FA_Cup"}]}, {"year": "1991", "text": "<PERSON>, former leader of the Tonton Macoute in Haiti under <PERSON>, attempts a coup d'état, which ends in his arrest.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former leader of the <a href=\"https://wikipedia.org/wiki/Tonton_Macoute\" title=\"Tonton Macoute\"><PERSON><PERSON> Macout<PERSON></a> in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> under <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, attempts a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>, which ends in his arrest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former leader of the <a href=\"https://wikipedia.org/wiki/Tonton_Macoute\" title=\"Tonton Macoute\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> under <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, attempts a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>, which ends in his arrest.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/To<PERSON>_<PERSON>e"}, {"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}]}, {"year": "1993", "text": "The Fourth Republic of Ghana is inaugurated with <PERSON> as president.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/History_of_Ghana#Fourth_Republic_(1993-present)\" title=\"History of Ghana\">Fourth Republic of Ghana</a> is inaugurated with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as president.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/History_of_Ghana#Fourth_Republic_(1993-present)\" title=\"History of Ghana\">Fourth Republic of Ghana</a> is inaugurated with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as president.", "links": [{"title": "History of Ghana", "link": "https://wikipedia.org/wiki/History_of_Ghana#Fourth_Republic_(1993-present)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "Bosnian War: The Bosnian Army executes a surprise attack at the village of Kravica in Srebrenica.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Bosnian_War\" title=\"Bosnian War\">Bosnian War</a>: The <a href=\"https://wikipedia.org/wiki/Armed_Forces_of_Bosnia_and_Herzegovina\" title=\"Armed Forces of Bosnia and Herzegovina\">Bosnian Army</a> executes a <a href=\"https://wikipedia.org/wiki/Kravica_attack_(1993)\" title=\"Kravica attack (1993)\">surprise attack</a> at the village of Kravica in <a href=\"https://wikipedia.org/wiki/Srebrenica\" title=\"Srebrenica\">Srebrenica</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bosnian_War\" title=\"Bosnian War\">Bosnian War</a>: The <a href=\"https://wikipedia.org/wiki/Armed_Forces_of_Bosnia_and_Herzegovina\" title=\"Armed Forces of Bosnia and Herzegovina\">Bosnian Army</a> executes a <a href=\"https://wikipedia.org/wiki/Kravica_attack_(1993)\" title=\"Kravica attack (1993)\">surprise attack</a> at the village of Kravica in <a href=\"https://wikipedia.org/wiki/Srebrenica\" title=\"Srebrenica\">Srebrenica</a>.", "links": [{"title": "Bosnian War", "link": "https://wikipedia.org/wiki/Bosnian_War"}, {"title": "Armed Forces of Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Armed_Forces_of_Bosnia_and_Herzegovina"}, {"title": "K<PERSON>vica attack (1993)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_attack_(1993)"}, {"title": "Srebrenica", "link": "https://wikipedia.org/wiki/Srebrenica"}]}, {"year": "1994", "text": "A British Aerospace Jetstream 41 operating as United Express Flight 6291 crashes in Gahanna, Ohio, killing five of the eight people on board.", "html": "1994 - A <a href=\"https://wikipedia.org/wiki/British_Aerospace_Jetstream_41\" title=\"British Aerospace Jetstream 41\">British Aerospace Jetstream 41</a> operating as <a href=\"https://wikipedia.org/wiki/United_Express_Flight_6291\" title=\"United Express Flight 6291\">United Express Flight 6291</a> crashes in <a href=\"https://wikipedia.org/wiki/Gahanna,_Ohio\" title=\"Gahanna, Ohio\">Gahanna, Ohio</a>, killing five of the eight people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/British_Aerospace_Jetstream_41\" title=\"British Aerospace Jetstream 41\">British Aerospace Jetstream 41</a> operating as <a href=\"https://wikipedia.org/wiki/United_Express_Flight_6291\" title=\"United Express Flight 6291\">United Express Flight 6291</a> crashes in <a href=\"https://wikipedia.org/wiki/Gahanna,_Ohio\" title=\"Gahanna, Ohio\">Gahanna, Ohio</a>, killing five of the eight people on board.", "links": [{"title": "British Aerospace Jetstream 41", "link": "https://wikipedia.org/wiki/British_Aerospace_Jetstream_41"}, {"title": "United Express Flight 6291", "link": "https://wikipedia.org/wiki/United_Express_Flight_6291"}, {"title": "Gahanna, Ohio", "link": "https://wikipedia.org/wiki/Gahanna,_Ohio"}]}, {"year": "1999", "text": "The Senate trial in the impeachment of U.S. President <PERSON> begins.", "html": "1999 - The Senate trial in the <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>\" title=\"Impeachment of <PERSON>\">impeachment</a> of U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins.", "no_year_html": "The Senate trial in the <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>\" title=\"Impeachment of <PERSON>\">impeachment</a> of U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins.", "links": [{"title": "Impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "A hot air balloon crashes near Carterton, New Zealand, killing all 11 people on board.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> <a href=\"https://wikipedia.org/wiki/2012_Carterton_hot_air_balloon_crash\" title=\"2012 Carterton hot air balloon crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Carterton,_New_Zealand\" title=\"Carterton, New Zealand\">Carterton, New Zealand</a>, killing all 11 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> <a href=\"https://wikipedia.org/wiki/2012_Carterton_hot_air_balloon_crash\" title=\"2012 Carterton hot air balloon crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Carterton,_New_Zealand\" title=\"Carterton, New Zealand\">Carterton, New Zealand</a>, killing all 11 people on board.", "links": [{"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}, {"title": "2012 Carterton hot air balloon crash", "link": "https://wikipedia.org/wiki/2012_Carterton_hot_air_balloon_crash"}, {"title": "Carterton, New Zealand", "link": "https://wikipedia.org/wiki/Carterton,_New_Zealand"}]}, {"year": "2015", "text": "Two gunmen commit mass murder at the offices of <PERSON> in Paris, executing twelve people and wounding eleven others.", "html": "2015 - Two gunmen commit <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_shooting\" title=\"<PERSON> shooting\">mass murder</a> at the offices of <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> He<PERSON>\"><PERSON></a></i> in Paris, executing twelve people and wounding eleven others.", "no_year_html": "Two gunmen commit <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_shooting\" title=\"<PERSON> shooting\">mass murder</a> at the offices of <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> in Paris, executing twelve people and wounding eleven others.", "links": [{"title": "<PERSON> shooting", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_shooting"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "A car bomb explodes outside a police college in the Yemeni capital Sana'a with at least 38 people reported dead and more than 63 injured.", "html": "2015 - A car bomb <a href=\"https://wikipedia.org/wiki/January_2015_Sana%27a_bombing\" class=\"mw-redirect\" title=\"January 2015 Sana'a bombing\">explodes</a> outside a police college in the <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemeni</a> capital <a href=\"https://wikipedia.org/wiki/Sana%27a\" class=\"mw-redirect\" title=\"Sana'a\">Sana'a</a> with at least 38 people reported dead and more than 63 injured.", "no_year_html": "A car bomb <a href=\"https://wikipedia.org/wiki/January_2015_Sana%27a_bombing\" class=\"mw-redirect\" title=\"January 2015 Sana'a bombing\">explodes</a> outside a police college in the <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemeni</a> capital <a href=\"https://wikipedia.org/wiki/Sana%27a\" class=\"mw-redirect\" title=\"Sana'a\">Sana'a</a> with at least 38 people reported dead and more than 63 injured.", "links": [{"title": "January 2015 Sana'a bombing", "link": "https://wikipedia.org/wiki/January_2015_Sana%27a_bombing"}, {"title": "Yemen", "link": "https://wikipedia.org/wiki/Yemen"}, {"title": "Sana'a", "link": "https://wikipedia.org/wiki/Sana%27a"}]}, {"year": "2020", "text": "The 6.4Mw  2019-20 Puerto Rico earthquakes kill four and injure nine in southern Puerto Rico.", "html": "2020 - The 6.4M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2019%E2%80%9320_Puerto_Rico_earthquakes\" title=\"2019-20 Puerto Rico earthquakes\">2019-20 Puerto Rico earthquakes</a> kill four and injure nine in southern <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a>.", "no_year_html": "The 6.4M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2019%E2%80%9320_Puerto_Rico_earthquakes\" title=\"2019-20 Puerto Rico earthquakes\">2019-20 Puerto Rico earthquakes</a> kill four and injure nine in southern <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a>.", "links": [{"title": "2019-20 Puerto Rico earthquakes", "link": "https://wikipedia.org/wiki/2019%E2%80%9320_Puerto_Rico_earthquakes"}, {"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}]}, {"year": "2023", "text": "The longest U.S. House of Representatives speaker election since the December 1859 - February 1860 U.S. speaker election concludes and <PERSON> is elected 55th Speaker of the United States House of Representatives.", "html": "2023 - The longest <a href=\"https://wikipedia.org/wiki/January_2023_Speaker_of_the_United_States_House_of_Representatives_election\" title=\"January 2023 Speaker of the United States House of Representatives election\">U.S. House of Representatives speaker election</a> since the December 1859 - February 1860 U.S. speaker election concludes and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected 55th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker</a> of the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a>.", "no_year_html": "The longest <a href=\"https://wikipedia.org/wiki/January_2023_Speaker_of_the_United_States_House_of_Representatives_election\" title=\"January 2023 Speaker of the United States House of Representatives election\">U.S. House of Representatives speaker election</a> since the December 1859 - February 1860 U.S. speaker election concludes and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected 55th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker</a> of the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a>.", "links": [{"title": "January 2023 Speaker of the United States House of Representatives election", "link": "https://wikipedia.org/wiki/January_2023_Speaker_of_the_United_States_House_of_Representatives_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}]}, {"year": "2025", "text": "A series of wildfires ravage the Greater Los Angeles area, resulting in at least 16 deaths and 13,401 structures destroyed.", "html": "2025 - A series of <a href=\"https://wikipedia.org/wiki/January_2025_Southern_California_wildfires\" title=\"January 2025 Southern California wildfires\">wildfires</a> ravage the <a href=\"https://wikipedia.org/wiki/Greater_Los_Angeles\" title=\"Greater Los Angeles\">Greater Los Angeles</a> area, resulting in at least 16 deaths and 13,401 structures destroyed.", "no_year_html": "A series of <a href=\"https://wikipedia.org/wiki/January_2025_Southern_California_wildfires\" title=\"January 2025 Southern California wildfires\">wildfires</a> ravage the <a href=\"https://wikipedia.org/wiki/Greater_Los_Angeles\" title=\"Greater Los Angeles\">Greater Los Angeles</a> area, resulting in at least 16 deaths and 13,401 structures destroyed.", "links": [{"title": "January 2025 Southern California wildfires", "link": "https://wikipedia.org/wiki/January_2025_Southern_California_wildfires"}, {"title": "Greater Los Angeles", "link": "https://wikipedia.org/wiki/Greater_Los_Angeles"}]}], "Births": [{"year": "889", "text": "<PERSON>, emperor of Southern Tang (d. 943)", "html": "889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>\" title=\"Li Bian\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Southern_Tang\" title=\"Southern Tang\">Southern Tang</a> (d. 943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_Bian\" title=\"Li Bian\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Southern_Tang\" title=\"Southern Tang\">Southern Tang</a> (d. 943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_Bian"}, {"title": "Southern Tang", "link": "https://wikipedia.org/wiki/Southern_Tang"}]}, {"year": "1355", "text": "<PERSON> of Woodstock, Duke of Gloucester, English politician, Lord High Constable of England (d. 1397)", "html": "1355 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock,_Duke_of_Gloucester\" title=\"<PERSON> of Woodstock, Duke of Gloucester\"><PERSON> of Woodstock, Duke of Gloucester</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (d. 1397)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock,_Duke_of_Gloucester\" title=\"<PERSON> of Woodstock, Duke of Gloucester\"><PERSON> of Woodstock, Duke of Gloucester</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (d. 1397)", "links": [{"title": "<PERSON> of Woodstock, Duke of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock,_<PERSON>_of_Gloucester"}, {"title": "Lord High Constable of England", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_England"}]}, {"year": "1414", "text": "<PERSON>, Count of Nassau-Siegen (d. 1451)", "html": "1414 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (d. 1451)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (d. 1451)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1502", "text": "<PERSON> (d. 1585)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory XIII\"><PERSON> <PERSON></a> (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory XIII\"><PERSON> <PERSON></a> (d. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1634", "text": "<PERSON>, German organist and composer (d. 1666)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "<PERSON>, Duke of Württemberg (d. 1677)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a> (d. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a> (d. 1677)", "links": [{"title": "<PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrt<PERSON>berg"}]}, {"year": "1685", "text": "<PERSON>, Swedish agronomist and businessman (d. 1761)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6mer\" title=\"<PERSON>\"><PERSON></a>, Swedish agronomist and businessman (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6mer\" title=\"<PERSON>\"><PERSON></a>, Swedish agronomist and businessman (d. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jonas_Alstr%C3%B6mer"}]}, {"year": "1706", "text": "<PERSON>, German publisher (d. 1751)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publisher (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publisher (d. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, Italian opera director and manager (d. 1785)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(opera_director)\" title=\"<PERSON> (opera director)\"><PERSON></a>, Italian opera director and manager (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(opera_director)\" title=\"<PERSON> (opera director)\"><PERSON></a>, Italian opera director and manager (d. 1785)", "links": [{"title": "<PERSON> (opera director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(opera_director)"}]}, {"year": "1718", "text": "<PERSON>, American general (d. 1790)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/Israel_Putnam\" title=\"Israel Putnam\"><PERSON></a>, American general (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Putnam\" title=\"Israel Putnam\"><PERSON></a>, American general (d. 1790)", "links": [{"title": "Israel Putnam", "link": "https://wikipedia.org/wiki/Israel_Putnam"}]}, {"year": "1746", "text": "<PERSON>, 1st Viscount <PERSON>, Scottish admiral and politician (d. 1823)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Scottish admiral and politician (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Scottish admiral and politician (d. 1823)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, Italian king (d. 1844)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian king (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian king (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Mexican general and 16th president (1845-1846) (d. 1849)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(President_of_Mexico)\" class=\"mw-redirect\" title=\"<PERSON> (President of Mexico)\"><PERSON></a>, Mexican general and 16th president (1845-1846) (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(President_of_Mexico)\" class=\"mw-redirect\" title=\"<PERSON> (President of Mexico)\"><PERSON></a>, Mexican general and 16th president (1845-1846) (d. 1849)", "links": [{"title": "<PERSON> (President of Mexico)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(President_of_Mexico)"}]}, {"year": "1800", "text": "<PERSON><PERSON>, American politician, 13th President of the United States (d. 1874)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/Millard_<PERSON>more\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Millard_Fillmore"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1814", "text": "<PERSON>, Scottish poet (d. 1837)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, American writer (d. 1882)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American writer (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American writer (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON><PERSON>, Scottish-Canadian engineer, created Universal Standard Time (d. 1915)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fleming\"><PERSON><PERSON></a>, Scottish-Canadian engineer, created <a href=\"https://wikipedia.org/wiki/Universal_Standard_Time\" class=\"mw-redirect\" title=\"Universal Standard Time\">Universal Standard Time</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fleming\"><PERSON><PERSON></a>, Scottish-Canadian engineer, created <a href=\"https://wikipedia.org/wiki/Universal_Standard_Time\" class=\"mw-redirect\" title=\"Universal Standard Time\">Universal Standard Time</a> (d. 1915)", "links": [{"title": "Sandford Fleming", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fleming"}, {"title": "Universal Standard Time", "link": "https://wikipedia.org/wiki/Universal_Standard_Time"}]}, {"year": "1830", "text": "<PERSON>, American painter (d. 1902)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Albert_<PERSON>\" title=\"Albert <PERSON>\"><PERSON></a>, American painter (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albert_<PERSON>\" title=\"Albert <PERSON>\"><PERSON></a>, American painter (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Albert_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, German postman, founded the Universal Postal Union (d. 1897)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German postman, founded the <a href=\"https://wikipedia.org/wiki/Universal_Postal_Union\" title=\"Universal Postal Union\">Universal Postal Union</a> (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German postman, founded the <a href=\"https://wikipedia.org/wiki/Universal_Postal_Union\" title=\"Universal Postal Union\">Universal Postal Union</a> (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Universal Postal Union", "link": "https://wikipedia.org/wiki/Universal_Postal_Union"}]}, {"year": "1832", "text": "<PERSON>, Scottish-Australian publisher and politician, 15th Premier of Victoria (d. 1908)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian publisher and politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian publisher and politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1908)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1834", "text": "<PERSON>, German physicist and academic, invented the Reis telephone (d. 1874)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, invented the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_telephone\" title=\"<PERSON><PERSON> telephone\"><PERSON><PERSON> telephone</a> (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, invented the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_telephone\" title=\"<PERSON><PERSON> telephone\"><PERSON><PERSON> telephone</a> (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Reis telephone", "link": "https://wikipedia.org/wiki/Re<PERSON>_telephone"}]}, {"year": "1837", "text": "<PERSON>, English businessman, founded the White Star Line Shipping Company (d. 1899)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/White_Star_Line\" title=\"White Star Line\">White Star Line Shipping Company</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/White_Star_Line\" title=\"White Star Line\">White Star Line Shipping Company</a> (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White Star Line", "link": "https://wikipedia.org/wiki/White_Star_Line"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON>, French nun and saint (d. 1879)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Bernadette_Soubirous\" title=\"Bernadette Soubirous\">Bernadette Soubirous</a>, French nun and saint (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bernadette_Soubirous\" title=\"Bernadette Soubirous\"><PERSON><PERSON><PERSON>ubi<PERSON></a>, French nun and saint (d. 1879)", "links": [{"title": "Bernadette Soubirous", "link": "https://wikipedia.org/wiki/Bernadette_Soubirous"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON>, New Mexican Congressman and political ally of <PERSON> (d. 1919)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Mexican Congressman and political ally of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Mexican Congressman and political ally of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON>, Belarusian lexicographer and journalist (d. 1922)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian lexicographer and journalist (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian lexicographer and journalist (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American botanist and first librarian of the New York Botanical Garden (d. 1955)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and first librarian of the <a href=\"https://wikipedia.org/wiki/New_York_Botanical_Garden\" title=\"New York Botanical Garden\">New York Botanical Garden</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and first librarian of the <a href=\"https://wikipedia.org/wiki/New_York_Botanical_Garden\" title=\"New York Botanical Garden\">New York Botanical Garden</a> (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "New York Botanical Garden", "link": "https://wikipedia.org/wiki/New_York_Botanical_Garden"}]}, {"year": "1871", "text": "<PERSON><PERSON>, French mathematician and politician (d. 1956)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Borel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French mathematician and politician (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Borel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French mathematician and politician (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Borel"}]}, {"year": "1873", "text": "<PERSON>, French poet and journalist (d. 1914)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9guy\" title=\"<PERSON>\"><PERSON></a>, French poet and journalist (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9guy\" title=\"<PERSON>\"><PERSON></a>, French poet and journalist (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_P%C3%A9guy"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Hungarian-American film producer, co-founded Paramount Pictures (d. 1976)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Paramount_Pictures\" title=\"Paramount Pictures\">Paramount Pictures</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Paramount_Pictures\" title=\"Paramount Pictures\">Paramount Pictures</a> (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Paramount Pictures", "link": "https://wikipedia.org/wiki/Paramount_Pictures"}]}, {"year": "1875", "text": "<PERSON>, German gymnast (d. 1945)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, English pianist and composer (d. 1906)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American baseball player, coach, and lawyer (d. 1928)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and lawyer (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and lawyer (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Russian-American ballerina (d. 1982)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American ballerina (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American ballerina (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>-<PERSON>, American soldier, pulp magazine writer, and pioneer of the American comic book (d. 1965)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pulp magazine writer, and pioneer of the American comic book (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pulp magazine writer, and pioneer of the American comic book (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, American novelist, short story writer, and folklorist (d. 1960)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist, short story writer, and folklorist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist, short story writer, and folklorist (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Australian pilot and businessman, co-founded Qantas Airways Limited (d. 1974)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Hudson_F<PERSON>\" title=\"<PERSON> F<PERSON>\"><PERSON></a>, Australian pilot and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas Airways Limited</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hudson_F<PERSON>\" title=\"<PERSON> F<PERSON>\"><PERSON></a>, Australian pilot and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas Airways Limited</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hudson_F<PERSON>h"}, {"title": "Qantas", "link": "https://wikipedia.org/wiki/Qantas"}]}, {"year": "1898", "text": "<PERSON>, Mozambican-English singer-songwriter (disputed; d. 1941)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mozambican-English singer-songwriter (disputed; d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ly\" title=\"<PERSON>\"><PERSON></a>, Mozambican-English singer-songwriter (disputed; d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_<PERSON>ly"}]}, {"year": "1899", "text": "<PERSON>, French pianist and composer (d. 1963)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, American magazine executive, writer, and magazine editor (Astounding Stories) (d. 1956)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Orlin_Tremaine\" title=\"F. Orlin Tremaine\"><PERSON><PERSON><PERSON></a>, American magazine executive, writer, and magazine editor (<i><a href=\"https://wikipedia.org/wiki/Astounding_Stories\" class=\"mw-redirect\" title=\"Astounding Stories\">Astounding Stories</a></i>) (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Or<PERSON>_Tremaine\" title=\"F. Orlin Tremaine\"><PERSON><PERSON> <PERSON></a>, American magazine executive, writer, and magazine editor (<i><a href=\"https://wikipedia.org/wiki/Astounding_Stories\" class=\"mw-redirect\" title=\"Astounding Stories\">Astounding Stories</a></i>) (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Tremaine"}, {"title": "Astounding Stories", "link": "https://wikipedia.org/wiki/Astounding_Stories"}]}, {"year": "1900", "text": "<PERSON>, Australian actor and singer (d. 1969)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baritone)\" title=\"<PERSON> (baritone)\"><PERSON></a>, Australian actor and singer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baritone)\" title=\"<PERSON> (baritone)\"><PERSON></a>, Australian actor and singer (d. 1969)", "links": [{"title": "<PERSON> (baritone)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baritone)"}]}, {"year": "1903", "text": "<PERSON>, English actor (d. 1988)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player (d. 1963)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Irish Republican died while on hunger strike at Curragh Internment camp (d.1923).", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> died while on <a href=\"https://wikipedia.org/wiki/Hunger_strike\" title=\"Hunger strike\">hunger strike</a> at <a href=\"https://wikipedia.org/wiki/Curragh_Camp\" title=\"Curragh Camp\">Curragh Internment camp</a> (d.1923).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> died while on <a href=\"https://wikipedia.org/wiki/Hunger_strike\" title=\"Hunger strike\">hunger strike</a> at <a href=\"https://wikipedia.org/wiki/Curragh_Camp\" title=\"Curragh Camp\">Curragh Internment camp</a> (d.1923).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}, {"title": "Hunger strike", "link": "https://wikipedia.org/wiki/Hunger_strike"}, {"title": "Curragh Camp", "link": "https://wikipedia.org/wiki/Curragh_Camp"}]}, {"year": "1908", "text": "<PERSON>, American trumpet player (d. 1967)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Allen\"><PERSON></a>, American trumpet player (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Allen"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American soldier and politician, 36th Governor of Arkansas (d. 1994)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> F<PERSON>\"><PERSON><PERSON></a>, American soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> F<PERSON>bus\"><PERSON><PERSON></a>, American soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aubus"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1912", "text": "<PERSON>, American cartoonist, created The Addams Family (d. 1988)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <a href=\"https://wikipedia.org/wiki/The_Addams_Family\" title=\"The Addams Family\">The Addams Family</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <a href=\"https://wikipedia.org/wiki/The_Addams_Family\" title=\"The Addams Family\">The Addams Family</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Addams Family", "link": "https://wikipedia.org/wiki/The_Addams_Family"}]}, {"year": "1913", "text": "<PERSON>, English actor (d. 1984)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American baseball player, coach, and sportscaster (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan geographer and academic (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan geographer and academic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan geographer and academic (d. 1989)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Canadian ice hockey player (d. 1988)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pratt\"><PERSON></a>, Canadian ice hockey player (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Italian-American actor (d. 1992)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vincent_Gardenia"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Colombian politician (d. 1997)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Esmeralda_Arboleda_Cadavid\" title=\"Esmeralda Arboleda Cadavid\"><PERSON><PERSON><PERSON><PERSON> Arboleda Cadavid</a>, Colombian politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esmeralda_Arboleda_Cadavid\" title=\"Esmeralda Arboleda Cadavid\"><PERSON><PERSON><PERSON><PERSON> Arboleda Cada<PERSON></a>, Colombian politician (d. 1997)", "links": [{"title": "Esmeralda Arboleda Cadavid", "link": "https://wikipedia.org/wiki/Esmeralda_Arboleda_Cadavid"}]}, {"year": "1921", "text": "<PERSON>, American poet and translator (d. 1975)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and translator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and translator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American baseball player and manager (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alvin Dark\"><PERSON></a>, American baseball player and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alvin Dark\"><PERSON></a>, American baseball player and manager (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, French flute player (d. 2000)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French flute player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French flute player (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Canadian scholar and critic (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian scholar and critic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian scholar and critic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Bulgarian theologian, educator, public figure and lecturer (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian theologian, educator, public figure and lecturer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian theologian, educator, public figure and lecturer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English actor (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Indian-English zookeeper, conservationist and author, founded Durrell Wildlife Park (d. 1995)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English zookeeper, conservationist and author, founded <a href=\"https://wikipedia.org/wiki/Durrell_Wildlife_Park\" class=\"mw-redirect\" title=\"Durrell Wildlife Park\">Durrell Wildlife Park</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English zookeeper, conservationist and author, founded <a href=\"https://wikipedia.org/wiki/Durrell_Wildlife_Park\" class=\"mw-redirect\" title=\"Durrell Wildlife Park\">Durrell Wildlife Park</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Durrell Wildlife Park", "link": "https://wikipedia.org/wiki/Durrell_Wildlife_Park"}]}, {"year": "1926", "text": "<PERSON>, South Korean lieutenant and politician, 11th Prime Minister of South Korea (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>il\" title=\"<PERSON>\"><PERSON></a>, South Korean lieutenant and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Korea\" title=\"Prime Minister of South Korea\">Prime Minister of South Korea</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>il\" title=\"<PERSON>\"><PERSON></a>, South Korean lieutenant and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Korea\" title=\"Prime Minister of South Korea\">Prime Minister of South Korea</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>il"}, {"title": "Prime Minister of South Korea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Korea"}]}, {"year": "1928", "text": "<PERSON>, American author and screenwriter (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian painter and sculptor (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and sculptor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and sculptor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actress", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>(actress)"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Finnish skier (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hietamies\" title=\"<PERSON><PERSON> Hietamies\"><PERSON><PERSON></a>, Finnish skier (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hietamie<PERSON>\" title=\"<PERSON><PERSON> Hietamies\"><PERSON><PERSON></a>, Finnish skier (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mirja_Hietamies"}]}, {"year": "1933", "text": "<PERSON>, American-English film producer (d. 2010)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English film producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English film producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian lawyer and politician, 29th Canadian Minister of Labour (d. 2002)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Labour (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Labour_(Canada)"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Cypriot lawyer and politician, 5th President of Cyprus (d. 2008)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Cyprus", "link": "https://wikipedia.org/wiki/President_of_Cyprus"}]}, {"year": "1935", "text": "<PERSON>, American clarinet player and saxophonist (d. 2006)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and saxophonist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and saxophonist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Russian engineer and astronaut (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Chinese diplomat and international jurist (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese diplomat and international jurist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sheng<PERSON>ao\"><PERSON></a>, Chinese diplomat and international jurist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Scottish author and journalist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian rugby league player and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American golfer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English singer-songwriter (d. 1972)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English violinist and conductor (d. 2004)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Brown\" title=\"<PERSON>\"><PERSON></a>, English violinist and conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Brown\" title=\"<PERSON> Brown\"><PERSON></a>, English violinist and conductor (d. 2004)", "links": [{"title": "Iona Brown", "link": "https://wikipedia.org/wiki/Iona_Brown"}]}, {"year": "1941", "text": "<PERSON>, English actor (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Russian-German weightlifter and coach (d. 2011)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-German weightlifter and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-German weightlifter and coach (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Japanese survivor of the atomic bombing of Hiroshima, known for one thousand origami cranes (d. 1955)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese survivor of the <a href=\"https://wikipedia.org/wiki/Atomic_bombings_of_Hiroshima_and_Nagasaki\" title=\"Atomic bombings of Hiroshima and Nagasaki\">atomic bombing of Hiroshima</a>, known for <a href=\"https://wikipedia.org/wiki/One_thousand_origami_cranes\" title=\"One thousand origami cranes\">one thousand origami cranes</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese survivor of the <a href=\"https://wikipedia.org/wiki/Atomic_bombings_of_Hiroshima_and_Nagasaki\" title=\"Atomic bombings of Hiroshima and Nagasaki\">atomic bombing of Hiroshima</a>, known for <a href=\"https://wikipedia.org/wiki/One_thousand_origami_cranes\" title=\"One thousand origami cranes\">one thousand origami cranes</a> (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Atomic bombings of Hiroshima and Nagasaki", "link": "https://wikipedia.org/wiki/Atomic_bombings_of_Hiroshima_and_Nagasaki"}, {"title": "One thousand origami cranes", "link": "https://wikipedia.org/wiki/One_thousand_origami_cranes"}]}, {"year": "1944", "text": "<PERSON>, British performing artist and rock photographer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, British performing artist and rock photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, British performing artist and rock photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Japanese economist and academic (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese economist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese economist and academic (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player (d. 1990)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Kenyan engineer and politician, 2nd Prime Minister of Kenya", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Raila_Odinga\" title=\"Raila Odinga\"><PERSON><PERSON></a>, Kenyan engineer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kenya\" title=\"Prime Minister of Kenya\">Prime Minister of Kenya</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raila_Odinga\" title=\"Raila Odinga\"><PERSON><PERSON></a>, Kenyan engineer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kenya\" title=\"Prime Minister of Kenya\">Prime Minister of Kenya</a>", "links": [{"title": "Raila Odinga", "link": "https://wikipedia.org/wiki/Raila_Odinga"}, {"title": "Prime Minister of Kenya", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Kenya"}]}, {"year": "1946", "text": "<PERSON>, author, psychologist and founder of child protection charity Kidscape", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, author, psychologist and founder of child protection charity <a href=\"https://wikipedia.org/wiki/Kidscape\" title=\"Kidscape\">Kidscape</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, author, psychologist and founder of child protection charity <a href=\"https://wikipedia.org/wiki/Kidscape\" title=\"Kidscape\">Kidscape</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kidscape", "link": "https://wikipedia.org/wiki/Kidscape"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American publisher, co-founded Rolling Stone", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Rolling_Stone\" title=\"Rolling Stone\">Rolling Stone</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Rolling_Stone\" title=\"Rolling Stone\">Rolling Stone</a></i>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Rolling Stone", "link": "https://wikipedia.org/wiki/Rolling_Stone"}]}, {"year": "1947", "text": "<PERSON>, English publisher, founded Time Out (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, English publisher, founded <i><a href=\"https://wikipedia.org/wiki/Time_Out_(magazine)\" title=\"Time Out (magazine)\">Time Out</a></i> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, English publisher, founded <i><a href=\"https://wikipedia.org/wiki/Time_Out_(magazine)\" title=\"Time Out (magazine)\">Time Out</a></i> (d. 2020)", "links": [{"title": "<PERSON> (publisher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)"}, {"title": "Time Out (magazine)", "link": "https://wikipedia.org/wiki/Time_Out_(magazine)"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Mexican singer-songwriter (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actress and model", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erin_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Hong Kong actor, director, producer, and martial artist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hung\" title=\"Sammo Hung\"><PERSON><PERSON></a>, Hong Kong actor, director, producer, and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sammo Hung\"><PERSON><PERSON></a>, Hong Kong actor, director, producer, and martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American painter and sculptor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English cricketer and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian-Bengali actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Bengali actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Bengali actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American novelist and essayist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Baker"}]}, {"year": "1957", "text": "<PERSON>, American television journalist, anchor, and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist, anchor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist, anchor, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, <PERSON> of Basildon, English accountant and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Basildon\" title=\"<PERSON>, Baroness <PERSON> of Basildon\"><PERSON>, Baroness <PERSON> of Basildon</a>, English accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>_of_Basildon\" title=\"<PERSON>, Baroness <PERSON> of Basildon\"><PERSON>, Baroness <PERSON> of Basildon</a>, English accountant and politician", "links": [{"title": "<PERSON>, <PERSON> of Basildon", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Basildon"}]}, {"year": "1959", "text": "<PERSON>, American bass player and songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Russian political analyst and strategist known for his fascist views", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian political analyst and strategist known for his <a href=\"https://wikipedia.org/wiki/Fascist\" class=\"mw-redirect\" title=\"Fascist\">fascist</a> views", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian political analyst and strategist known for his <a href=\"https://wikipedia.org/wiki/Fascist\" class=\"mw-redirect\" title=\"Fascist\">fascist</a> views", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fascist", "link": "https://wikipedia.org/wiki/Fascist"}]}, {"year": "1962", "text": "<PERSON>, American football player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American politician and physician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Paul\"><PERSON></a>, American politician and physician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Paul\" title=\"Rand Paul\"><PERSON></a>, American politician and physician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Paul"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cage\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "Five for Fighting, American singer-songwriter and pianist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Five_for_Fighting\" title=\"Five for Fighting\">Five for Fighting</a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Five_for_Fighting\" title=\"Five for Fighting\">Five for Fighting</a>, American singer-songwriter and pianist", "links": [{"title": "Five for Fighting", "link": "https://wikipedia.org/wiki/Five_for_Fighting"}]}, {"year": "1965", "text": "<PERSON>, Italian runner", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English academic and politician, Deputy Prime Minister of the United Kingdom", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Indian actor (d. 2020)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>rr<PERSON>_<PERSON>\" title=\"<PERSON>rr<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irr<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1969", "text": "<PERSON>, Italian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American-Canadian ice hockey player and mixed martial artist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player and mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Slovenian sprinter and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Alen<PERSON>_Bikar\" title=\"Alenka Bikar\"><PERSON><PERSON><PERSON></a>, Slovenian sprinter and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alen<PERSON>_Bikar\" title=\"Alenka Bikar\"><PERSON><PERSON><PERSON></a>, Slovenian sprinter and politician", "links": [{"title": "Alenka Bikar", "link": "https://wikipedia.org/wiki/Alenka_Bikar"}]}, {"year": "1976", "text": "<PERSON>, Armenian-Australian boxer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-Australian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-Australian boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Canadian baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C3%89ric_Gagn%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89ric_Gagn%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ric_Gagn%C3%A9"}]}, {"year": "1976", "text": "<PERSON>, Dominican baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor and comedian (d. 2021)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dustin Diamond\"><PERSON></a>, American actor and comedian (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dustin Diamond\"><PERSON></a>, American actor and comedian (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Finnish author and playwright", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>pel"}]}, {"year": "1978", "text": "<PERSON>, English cricketer and umpire", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American musician, singer, songwriter, record producer, actor, businessman and philanthropist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Aloe_Blacc\" title=\"Aloe Blacc\"><PERSON><PERSON></a>, American musician, singer, songwriter, record producer, actor, businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aloe_Blacc\" title=\"Aloe Blacc\"><PERSON><PERSON></a>, American musician, singer, songwriter, record producer, actor, businessman and philanthropist", "links": [{"title": "<PERSON>oe <PERSON>", "link": "https://wikipedia.org/wiki/Aloe_Blacc"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Zimbabwean cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Friend\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Friend\" title=\"<PERSON> Friend\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American-English actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Venezuelan baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Francisco_Rodr%C3%<PERSON><PERSON><PERSON>_(baseball,_born_1982)\" class=\"mw-redirect\" title=\"<PERSON> (baseball, born 1982)\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Rodr%C3%<PERSON><PERSON><PERSON>_(baseball,_born_1982)\" class=\"mw-redirect\" title=\"<PERSON> (baseball, born 1982)\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON> (baseball, born 1982)", "link": "https://wikipedia.org/wiki/Francisco_Rodr%C3%<PERSON><PERSON><PERSON>_(baseball,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, German swimmer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Dominican baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>nac<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edwin_Encarnaci%C3%B3n"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pondexter\" title=\"<PERSON><PERSON> Pondexter\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pondexter\" title=\"Cappie Pondexter\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pondexter"}]}, {"year": "1983", "text": "<PERSON>, American actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27chard\" title=\"<PERSON>'chard\"><PERSON>'chard</a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27chard\" title=\"<PERSON>'chard\"><PERSON>chard</a>, American actor", "links": [{"title": "<PERSON>ard", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i%27chard"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Italian footballer (d. 2018)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>i\"><PERSON><PERSON></a>, Italian footballer (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1987", "text": "<PERSON>, Serbian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stefan_Babovi%C4%87"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Lyndsy_Fonseca\" title=\"Lyndsy Fonseca\"><PERSON><PERSON><PERSON><PERSON> Fonseca</a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lyndsy_Fonseca\" title=\"Lyndsy Fonseca\"><PERSON><PERSON><PERSON><PERSON> Fonseca</a>, American actress", "links": [{"title": "Lyndsy Fonseca", "link": "https://wikipedia.org/wiki/Lyndsy_Fonseca"}]}, {"year": "1988", "text": "<PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dlebury"}]}, {"year": "1988", "text": "<PERSON>, Irish actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "Gentleman <PERSON>, English mixed martial artist and wrestler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Gentleman_<PERSON>_<PERSON>\" title=\"Gentleman <PERSON>\">Gentleman <PERSON></a>, English mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gentleman_<PERSON>_<PERSON>\" title=\"Gentleman <PERSON>\">Gentleman <PERSON></a>, English mixed martial artist and wrestler", "links": [{"title": "Gentleman <PERSON>", "link": "https://wikipedia.org/wiki/Gentleman_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Austrian ski jumper", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Belgian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Eden_Hazard\" title=\"Eden Hazard\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eden_Hazard\" title=\"Eden Hazard\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eden_Hazard"}]}, {"year": "1991", "text": "<PERSON><PERSON>, South African sprinter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Caster Se<PERSON>ya\"><PERSON><PERSON></a>, South African sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Caster Semenya\"><PERSON><PERSON></a>, South African sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>er_<PERSON><PERSON>ya"}]}, {"year": "1991", "text": "<PERSON>, Australian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean actress and singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> Sun-bin\"><PERSON></a>, South Korean actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lee_<PERSON>-bin"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bell\" title=\"<PERSON> Bell\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Bell\" title=\"Jordan Bell\"><PERSON></a>, American basketball player", "links": [{"title": "Jordan Bell", "link": "https://wikipedia.org/wiki/Jordan_Bell"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Kazakhstani tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Curaçaoan baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>zie_Albies\" title=\"Ozzie Albies\"><PERSON><PERSON></a>, Curaçaoan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>zie_Albies\" title=\"Ozzie Albies\"><PERSON><PERSON></a>, Curaçaoan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Albies"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American basketball player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2004", "text": "<PERSON>, American actress, singer, and dancer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Wylie\" title=\"Sofia Wylie\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Wylie\" title=\"<PERSON> Wylie\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON> Wylie", "link": "https://wikipedia.org/wiki/Sofia_Wylie"}]}, {"year": "2012", "text": "<PERSON>, American singer and actress", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Blue Ivy Carter\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Blue <PERSON> Carter\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "312", "text": "<PERSON> of Antioch, Christian martyr, saint, and theologian (b. 240)", "html": "312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Antioch\" title=\"<PERSON> of Antioch\"><PERSON> of Antioch</a>, Christian martyr, saint, and theologian (b. 240)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Antioch\" title=\"<PERSON> of Antioch\"><PERSON> of Antioch</a>, Christian martyr, saint, and theologian (b. 240)", "links": [{"title": "<PERSON> of Antioch", "link": "https://wikipedia.org/wiki/Lucian_of_Antioch"}]}, {"year": "838", "text": "<PERSON><PERSON>, Iranian leader of the Khurramite uprising against the Abbasid Caliphate", "html": "838 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian leader of the <a href=\"https://wikipedia.org/wiki/Khurramite\" class=\"mw-redirect\" title=\"Khurramite\">K<PERSON><PERSON>ite</a> uprising against the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON><PERSON> Caliphate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian leader of the <a href=\"https://wikipedia.org/wiki/Khurramite\" class=\"mw-redirect\" title=\"Khurramite\">Khurramite</a> uprising against the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON><PERSON> Calip<PERSON></a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Khurramite", "link": "https://wikipedia.org/wiki/K<PERSON>ramite"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbasid_Caliphate"}]}, {"year": "856", "text": "<PERSON><PERSON>, bishop of Le Mans[citation needed]", "html": "856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Le_Mans\" title=\"<PERSON><PERSON> of Le Mans\"><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Le_Mans\" title=\"Roman Catholic Diocese of Le Mans\">Le Mans</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Le_Mans\" title=\"<PERSON><PERSON> of Le Mans\"><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Le_Mans\" title=\"Roman Catholic Diocese of Le Mans\">Le Mans</a>", "links": [{"title": "<PERSON><PERSON> of Le Mans", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Roman Catholic Diocese of Le Mans", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Le_Mans"}]}, {"year": "1131", "text": "<PERSON><PERSON>, Danish prince and saint (b. 1096)", "html": "1131 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish prince and saint (b. 1096)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish prince and saint (b. 1096)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1285", "text": "<PERSON> of Naples (b. 1226)", "html": "1285 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" class=\"mw-redirect\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1226)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" class=\"mw-redirect\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1226)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Naples"}]}, {"year": "1325", "text": "<PERSON> of Portugal (b. 1261)", "html": "1325 - <a href=\"https://wikipedia.org/wiki/Denis_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1261)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denis_<PERSON>_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1261)", "links": [{"title": "Denis of Portugal", "link": "https://wikipedia.org/wiki/Denis_of_Portugal"}]}, {"year": "1355", "text": "<PERSON><PERSON><PERSON>, Castilian noblewoman (b. 1325)", "html": "1355 - <a href=\"https://wikipedia.org/wiki/In%C3%AAs_de_Castro\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Castilian noblewoman (b. 1325)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In%C3%AAs_de_Castro\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Castilian noblewoman (b. 1325)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In%C3%AAs_de_Castro"}]}, {"year": "1400", "text": "<PERSON>, 3rd Earl of Salisbury, English Earl (b. 1350)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Salisbury\" title=\"<PERSON>, 3rd Earl of Salisbury\"><PERSON>, 3rd Earl of Salisbury</a>, English Earl (b. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Salisbury\" title=\"<PERSON>, 3rd Earl of Salisbury\"><PERSON>, 3rd Earl of Salisbury</a>, English Earl (b. 1350)", "links": [{"title": "<PERSON>, 3rd Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Salisbury"}]}, {"year": "1451", "text": "<PERSON><PERSON><PERSON>, Duke of Savoy a.k.a. Antipope <PERSON> (b. 1383)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON>, Duke of Savoy</a> a.k.a. Antipope <PERSON> (b. 1383)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VIII,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON>, Duke of Savoy</a> a.k.a. Antipope <PERSON> (b. 1383)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1529", "text": "<PERSON> the Elder, German sculptor (b. 1455)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, German sculptor (b. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, German sculptor (b. 1455)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}]}, {"year": "1536", "text": "<PERSON> Aragon (b. 1485)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON> Aragon</a> (b. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON> Aragon</a> (b. 1485)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1566", "text": "<PERSON>, Flemish monk and author (b. 1506)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish monk and author (b. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish monk and author (b. 1506)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON>, English painter and goldsmith (b. 1547)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and goldsmith (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and goldsmith (b. 1547)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1625", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer and author (b. 1560)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/Ruggiero_Giovannelli\" title=\"Ruggiero Giovannelli\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and author (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ruggiero_Giovannelli\" title=\"Ruggiero Giovannelli\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and author (b. 1560)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruggiero_Giovannelli"}]}, {"year": "1655", "text": "<PERSON> (b. 1574)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_X\" title=\"Pope Innocent X\">Pope <PERSON></a> (b. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_X\" title=\"Pope Innocent X\"><PERSON></a> (b. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_<PERSON>"}]}, {"year": "1658", "text": "<PERSON><PERSON><PERSON>, American farmer and politician, 1st Governor of the New Haven Colony (b. 1590)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American farmer and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Connecticut\" title=\"List of colonial governors of Connecticut\">Governor of the New Haven Colony</a> (b. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American farmer and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Connecticut\" title=\"List of colonial governors of Connecticut\">Governor of the New Haven Colony</a> (b. 1590)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theophi<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Connecticut", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Connecticut"}]}, {"year": "1694", "text": "<PERSON>, 1st Earl of Macclesfield, English general and politician, Lord Lieutenant of Gloucestershire (b. 1618)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Macclesfield\" title=\"<PERSON>, 1st Earl of Macclesfield\"><PERSON>, 1st Earl of Macclesfield</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Gloucestershire\" title=\"Lord Lieutenant of Gloucestershire\">Lord Lieutenant of Gloucestershire</a> (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Macclesfield\" title=\"<PERSON>, 1st Earl of Macclesfield\"><PERSON>, 1st Earl of Macclesfield</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Gloucestershire\" title=\"Lord Lieutenant of Gloucestershire\">Lord Lieutenant of Gloucestershire</a> (b. 1618)", "links": [{"title": "<PERSON>, 1st Earl of Macclesfield", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Macclesfield"}, {"title": "Lord Lieutenant of Gloucestershire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Gloucestershire"}]}, {"year": "1700", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian scholar and author (b. 1618)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian scholar and author (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian scholar and author (b. 1618)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, French archbishop, theologian, and poet (b. 1651)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_F%C3%A9nelon\" title=\"<PERSON>\"><PERSON></a>, French archbishop, theologian, and poet (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_F%C3%A9nelon\" title=\"<PERSON>\"><PERSON></a>, French archbishop, theologian, and poet (b. 1651)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_F%C3%A9nelon"}]}, {"year": "1758", "text": "<PERSON>, Scottish poet and playwright (b. 1686)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and playwright (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and playwright (b. 1686)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1767", "text": "<PERSON>, American minister and academic (b. 1703)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and academic (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and academic (b. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, Swedish politician and diplomat (b. 1695)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician and diplomat (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician and diplomat (b. 1695)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, American journalist and author (b. 1768)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Irish-Australian public servant and politician (b. 1770)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian public servant and politician (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian public servant and politician (b. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, English painter and educator (b. 1769)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Ottoman politician, Grand Vizier of the Ottoman Empire (b. 1800)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_grand_viziers\" title=\"List of Ottoman grand viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9F<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_grand_viziers\" title=\"List of Ottoman grand viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mustafa_Re%C5%9Fid_<PERSON>"}, {"title": "List of Ottoman grand viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_grand_viziers"}]}, {"year": "1864", "text": "<PERSON>, American journalist and politician, 6th U.S. Secretary of the Interior (b. 1808)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 6th <a href=\"https://wikipedia.org/wiki/U.S._Secretary_of_the_Interior\" class=\"mw-redirect\" title=\"U.S. Secretary of the Interior\">U.S. Secretary of the Interior</a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 6th <a href=\"https://wikipedia.org/wiki/U.S._Secretary_of_the_Interior\" class=\"mw-redirect\" title=\"U.S. Secretary of the Interior\">U.S. Secretary of the Interior</a> (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "U.S. Secretary of the Interior", "link": "https://wikipedia.org/wiki/U.S._Secretary_of_the_Interior"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Bengali landlord and philanthropist (b. 1824)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Golam_<PERSON>_<PERSON>\" title=\"Golam Ali Chowdh<PERSON>\"><PERSON><PERSON></a>, Bengali landlord and philanthropist (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Golam_<PERSON>_<PERSON>\" title=\"Golam Ali Chowdh<PERSON>\"><PERSON><PERSON></a>, Bengali landlord and philanthropist (b. 1824)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian ruler (b. 1852)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian ruler (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian ruler (b. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>w<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Slovenian physicist and mathematician (b. 1835)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian physicist and mathematician (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian physicist and mathematician (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English physician and feminist (b. 1840)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and feminist (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and feminist (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American businessman and philanthropist, co-founded Washington University in St. Louis (b. 1843)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Washington_University_in_St._Louis\" title=\"Washington University in St. Louis\">Washington University in St. Louis</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Washington_University_in_St._Louis\" title=\"Washington University in St. Louis\">Washington University in St. Louis</a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Washington University in St. Louis", "link": "https://wikipedia.org/wiki/Washington_University_in_St._Louis"}]}, {"year": "1920", "text": "<PERSON>, Australian judge and politician, 1st Prime Minister of Australia (b. 1849)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian judge and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian judge and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Greek politician, 99th Prime Minister of Greece (b. 1851)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, 99th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, 99th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1851)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1931", "text": "<PERSON>, American historian and author (b. 1856)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, French sergeant and politician (b. 1877)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sergeant and politician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sergeant and politician (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Ma<PERSON>ot"}]}, {"year": "1936", "text": "<PERSON>, French pianist and composer (b. 1858)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hardelot\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hard<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON><PERSON>t"}]}, {"year": "1941", "text": "<PERSON>, English journalist and author (b. 1869)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Serbian-American inventor and engineer (b. 1856)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian-American inventor and engineer (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian-American inventor and engineer (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, French-Egyptian philosopher and author (b. 1886)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Gu%C3%A9non\" title=\"<PERSON>\"><PERSON></a>, French-Egyptian philosopher and author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Gu%C3%A9non\" title=\"<PERSON>\"><PERSON></a>, French-Egyptian philosopher and author (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Gu%C3%A9non"}]}, {"year": "1960", "text": "<PERSON>, English tennis player and coach (b. 1878)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English tennis player and coach (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English tennis player and coach (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, New Zealand-Australian farmer and politician, 23rd Premier of Queensland (b. 1876)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian farmer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian farmer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1964", "text": "<PERSON>, English racing driver and manager (b. 1911)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and manager (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and manager (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American author and screenwriter (b. 1917)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German-Swiss conductor (b. 1880)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss conductor (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss conductor (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, South African chemist and academic (b. 1897)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, South African chemist and academic (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, South African chemist and academic (b. 1897)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American poet and scholar (b. 1914)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and scholar (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and scholar (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, English journalist and radio announcer (b. 1908)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and radio announcer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and radio announcer (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian businessman and politician, 2nd Australian Minister for Finance (b. 1926)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian businessman and politician, 2nd <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Australia)\" title=\"Minister for Finance (Australia)\">Australian Minister for Finance</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian businessman and politician, 2nd <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Australia)\" title=\"Minister for Finance (Australia)\">Australian Minister for Finance</a> (b. 1926)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Minister for Finance (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Finance_(Australia)"}]}, {"year": "1984", "text": "<PERSON>, German-French physicist and academic, Nobel Prize laureate (b. 1902)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1986", "text": "<PERSON>, Mexican author, screenwriter, and photographer (b. 1917)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican author, screenwriter, and photographer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican author, screenwriter, and photographer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American Nipmuc Indian chief and fashion designer (b. 1919)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Zara_Cisco_Brough\" title=\"Zara Cisco Brough\">Zara Cisco Brough</a>, American <a href=\"https://wikipedia.org/wiki/Nipmuc\" title=\"Nipmuc\"><PERSON><PERSON><PERSON></a> Indian chief and fashion designer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zara_Cisco_Brough\" title=\"Zara Cisco Brough\">Zara Cisco Brough</a>, American <a href=\"https://wikipedia.org/wiki/Nipmuc\" title=\"Nipmuc\"><PERSON><PERSON><PERSON></a> Indian chief and fashion designer (b. 1919)", "links": [{"title": "Zara Cisco Brough", "link": "https://wikipedia.org/wiki/Zara_Cisco_Brough"}, {"title": "Nipmuc", "link": "https://wikipedia.org/wiki/Nipmuc"}]}, {"year": "1988", "text": "<PERSON>, English actor (b. 1913)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (b. 1901)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Hiro<PERSON>o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hiro<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hi<PERSON><PERSON>o"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Canadian-American football player and wrestler (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rski\" title=\"<PERSON><PERSON><PERSON>gurski\"><PERSON><PERSON><PERSON></a>, Canadian-American football player and wrestler (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American football player and wrestler (b. 1908)", "links": [{"title": "Bron<PERSON>", "link": "https://wikipedia.org/wiki/Bronko_Nagurski"}]}, {"year": "1992", "text": "<PERSON>, American puppeteer and voice actor (b. 1951)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)\" title=\"<PERSON> (puppeteer)\"><PERSON></a>, American puppeteer and voice actor (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)\" title=\"<PERSON> (puppeteer)\"><PERSON></a>, American puppeteer and voice actor (b. 1951)", "links": [{"title": "<PERSON> (puppeteer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)"}]}, {"year": "1995", "text": "<PERSON>, American economist, historian, and theorist (b. 1926)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, historian, and theorist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, historian, and theorist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian politician, 51st Prime Minister of Hungary (b. 1930)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Gr%C3%B3sz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Gr%C3%B3sz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A1roly_Gr%C3%B3sz"}, {"title": "List of Prime Ministers of Hungary", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary"}]}, {"year": "1998", "text": "<PERSON>, American record producer (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Croatian-Swiss chemist and academic, Nobel Prize laureate (b. 1906)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prelog\"><PERSON></a>, Croatian-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Vladimir Prelog\"><PERSON></a>, Croatian-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Prelog"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2000", "text": "<PERSON>, American wrestler (b. 1963)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American singer (b. 1942)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (b. 1942)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "2002", "text": "<PERSON>, American comedian and actor (b. 1935)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hreiber"}]}, {"year": "2004", "text": "<PERSON>, Swedish actress (b. 1926)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, French author (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Austrian mountaineer, geographer, and author (b. 1912)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer, geographer, and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer, geographer, and author (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American race car driver and businessman (b. 1957)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Icelandic journalist, author, and academic (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Icelandic journalist, author, and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Icelandic journalist, author, and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, South African academic and politician, Vice State President of South Africa (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African academic and politician, <a href=\"https://wikipedia.org/wiki/Vice_State_President_of_South_Africa\" title=\"Vice State President of South Africa\">Vice State President of South Africa</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African academic and politician, <a href=\"https://wikipedia.org/wiki/Vice_State_President_of_South_Africa\" title=\"Vice State President of South Africa\">Vice State President of South Africa</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Vice State President of South Africa", "link": "https://wikipedia.org/wiki/Vice_State_President_of_South_Africa"}]}, {"year": "2012", "text": "<PERSON>, British-born American child actor, journalist and pundit (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born American child actor, journalist and pundit (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born American child actor, journalist and pundit (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Chinese-Hong Kong businessman and philanthropist, founded Shaw Brothers Studio and TVB (b. 1907)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Run_<PERSON>\" title=\"Run Run Shaw\"><PERSON></a>, Chinese-Hong Kong businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Shaw_Brothers_Studio\" title=\"Shaw Brothers Studio\">Shaw Brothers Studio</a> and <a href=\"https://wikipedia.org/wiki/TVB\" title=\"TVB\">TVB</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Run_<PERSON>\" title=\"Run Run Shaw\"><PERSON></a>, Chinese-Hong Kong businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Shaw_Brothers_Studio\" title=\"Shaw Brothers Studio\">Shaw Brothers Studio</a> and <a href=\"https://wikipedia.org/wiki/TVB\" title=\"TVB\">TVB</a> (b. 1907)", "links": [{"title": "Run Run <PERSON>", "link": "https://wikipedia.org/wiki/Run_Run_Shaw"}, {"title": "Shaw Brothers Studio", "link": "https://wikipedia.org/wiki/Shaw_Brothers_Studio"}, {"title": "TVB", "link": "https://wikipedia.org/wiki/TVB"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Botswana general and politician, Vice-President of Botswana (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Botswana general and politician, <a href=\"https://wikipedia.org/wiki/Vice-President_of_Botswana\" title=\"Vice-President of Botswana\">Vice-President of Botswana</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Botswana general and politician, <a href=\"https://wikipedia.org/wiki/Vice-President_of_Botswana\" title=\"Vice-President of Botswana\">Vice-President of Botswana</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rafhe"}, {"title": "Vice-President of Botswana", "link": "https://wikipedia.org/wiki/Vice-President_of_Botswana"}]}, {"year": "2015", "text": "<PERSON>, Australian-American actor and screenwriter (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American actor and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American actor and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Tunisian-French cartoonist (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian-French cartoonist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian-French cartoonist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American basketball player and coach (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1929)\" title=\"<PERSON> (basketball, born 1929)\"><PERSON></a>, American basketball player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1929)\" title=\"<PERSON> (basketball, born 1929)\"><PERSON></a>, American basketball player and coach (b. 1929)", "links": [{"title": "<PERSON> (basketball, born 1929)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1929)"}]}, {"year": "2016", "text": "<PERSON>, American basketball player (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1947)\" title=\"<PERSON> (basketball, born 1947)\"><PERSON></a>, American basketball player (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1947)\" title=\"<PERSON> (basketball, born 1947)\"><PERSON></a>, American basketball player (b. 1947)", "links": [{"title": "<PERSON> (basketball, born 1947)", "link": "https://wikipedia.org/wiki/<PERSON>_(basketball,_born_1947)"}]}, {"year": "2016", "text": "<PERSON>, American singer (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American lawyer and jurist (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, Indian Minister of Home Affairs (b. 1936)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Home_Affairs_(India)\" title=\"Minister of Home Affairs (India)\">Indian Minister of Home Affairs</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Home_Affairs_(India)\" title=\"Minister of Home Affairs (India)\">Indian Minister of Home Affairs</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Home Affairs (India)", "link": "https://wikipedia.org/wiki/Minister_of_Home_Affairs_(India)"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Portuguese politician; 16th President of Portugal (b. 1924)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Soares\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese politician; 16th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese politician; 16th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rio_Soares"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "2018", "text": "<PERSON>, Former New Zealand Deputy Prime Minister (b. 1938)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Former New Zealand Deputy Prime Minister (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Former New Zealand Deputy Prime Minister (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, French singer (b. 1947)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/France_Gall\" title=\"France Gall\"><PERSON></a>, French singer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_Gall\" title=\"France Gall\"><PERSON> Gall</a>, French singer (b. 1947)", "links": [{"title": "France Gall", "link": "https://wikipedia.org/wiki/France_Gall"}]}, {"year": "2020", "text": "<PERSON>, Canadian drummer, songwriter, and producer (b. 1952)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer, songwriter, and producer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer, songwriter, and producer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, American screenwriter and television producer (b. 1974)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American screenwriter and television producer (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American screenwriter and television producer (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, author and feminist (b. 1967)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, author and feminist (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, author and feminist (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English filmmaker (b. 1941)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English filmmaker (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English filmmaker (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American baseball player, coach, and manager (b. 1927)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Swiss cardinal (b. 1932)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cardinal (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cardinal (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, German footballer and manager (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON>, French intelligence officer and politician (b. 1928)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French intelligence officer and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French intelligence officer and politician (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1938)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}