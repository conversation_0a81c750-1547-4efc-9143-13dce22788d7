{"date": "October 23", "url": "https://wikipedia.org/wiki/October_23", "data": {"Events": [{"year": "4004 BC", "text": "<PERSON>'s purported creation date of the world according to the Bible.", "html": "4004 BC - 4004 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> purported creation date of the world according to the Bible.", "no_year_html": "4004 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> purported creation date of the world according to the Bible.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "42 BC", "text": "Liberators' civil war: <PERSON> and <PERSON><PERSON><PERSON> decisively defeat an army under <PERSON><PERSON><PERSON> in the second part of the Battle of Philippi, with <PERSON><PERSON><PERSON> committing suicide and ending the civil war.", "html": "42 BC - 42 BC - <a href=\"https://wikipedia.org/wiki/Liberators%27_civil_war\" title=\"Liberators' civil war\">Liberators' civil war</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a> decisively defeat an army under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brutus\" title=\"<PERSON> Junius Brutus\"><PERSON><PERSON><PERSON></a> in the second part of the <a href=\"https://wikipedia.org/wiki/Battle_of_Philippi\" title=\"Battle of Philippi\">Battle of Philippi</a>, with <PERSON><PERSON><PERSON> committing suicide and ending the civil war.", "no_year_html": "42 BC - <a href=\"https://wikipedia.org/wiki/Liberators%27_civil_war\" title=\"Liberators' civil war\">Liberators' civil war</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\"><PERSON>avia<PERSON></a> decisively defeat an army under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brutus\" title=\"<PERSON>ius Brutus\"><PERSON><PERSON><PERSON></a> in the second part of the <a href=\"https://wikipedia.org/wiki/Battle_of_Philippi\" title=\"Battle of Philippi\">Battle of Philippi</a>, with <PERSON><PERSON><PERSON> committing suicide and ending the civil war.", "links": [{"title": "Liberators' civil war", "link": "https://wikipedia.org/wiki/Liberators%27_civil_war"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Philippi", "link": "https://wikipedia.org/wiki/Battle_of_Philippi"}]}, {"year": "425", "text": "<PERSON><PERSON><PERSON> <PERSON> is elevated as Roman emperor at the age of six.", "html": "425 - <a href=\"https://wikipedia.org/wiki/Valentinian_III\" title=\"Valentinian III\"><PERSON><PERSON><PERSON> III</a> is elevated as Roman emperor at the age of six.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentinian_III\" title=\"Valentinian III\"><PERSON><PERSON><PERSON> III</a> is elevated as Roman emperor at the age of six.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valentinian_III"}]}, {"year": "502", "text": "The Synodus Palmaris, called by Gothic king <PERSON><PERSON><PERSON>, absolves <PERSON> <PERSON><PERSON><PERSON><PERSON> of all charges, thus ending the schism of Antipope <PERSON>.", "html": "502 - The <i><PERSON><PERSON></i>, called by Gothic king <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON></a>, absolves <a href=\"https://wikipedia.org/wiki/Pope_Symmachus\" title=\"Pope Symmachus\"><PERSON></a> of all charges, thus ending the schism of <a href=\"https://wikipedia.org/wiki/Antipop<PERSON>_Laurent<PERSON>\" title=\"Antipop<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "The <i><PERSON><PERSON></i>, called by Gothic king <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON></a>, absolves <a href=\"https://wikipedia.org/wiki/Pope_Symmachus\" title=\"Pope Sym<PERSON>chus\"><PERSON></a> of all charges, thus ending the schism of <a href=\"https://wikipedia.org/wiki/Antipope_Laurentius\" title=\"Antipop<PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Symmachus"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anti<PERSON><PERSON>_<PERSON>"}]}, {"year": "1086", "text": "Spanish Reconquista: At the Battle of Sagrajas, the Almoravids defeats the Castilians, but are unable to take advantage of their victory.", "html": "1086 - <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Spanish <i>Reconquista</i></a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Sagrajas\" title=\"Battle of Sagrajas\">Battle of Sagrajas</a>, the <a href=\"https://wikipedia.org/wiki/Almoravid_dynasty\" title=\"Almoravid dynasty\">Almoravids</a> defeats the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castilians</a>, but are unable to take advantage of their victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Spanish <i>Reconquista</i></a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Sagrajas\" title=\"Battle of Sagrajas\">Battle of Sagrajas</a>, the <a href=\"https://wikipedia.org/wiki/Almoravid_dynasty\" title=\"Almoravid dynasty\">Almoravids</a> defeats the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castilians</a>, but are unable to take advantage of their victory.", "links": [{"title": "Reconquista", "link": "https://wikipedia.org/wiki/Reconquista"}, {"title": "Battle of Sagrajas", "link": "https://wikipedia.org/wiki/Battle_of_Sagrajas"}, {"title": "Almoravid dynasty", "link": "https://wikipedia.org/wiki/Almoravid_dynasty"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}]}, {"year": "1157", "text": "The Battle of Grathe Heath ends the Danish Civil War.", "html": "1157 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Grathe_Heath\" title=\"Battle of Grathe Heath\">Battle of Grathe Heath</a> ends the <a href=\"https://wikipedia.org/wiki/Danish_Civil_War\" class=\"mw-redirect\" title=\"Danish Civil War\">Danish Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Grathe_Heath\" title=\"Battle of Grathe Heath\">Battle of Grathe Heath</a> ends the <a href=\"https://wikipedia.org/wiki/Danish_Civil_War\" class=\"mw-redirect\" title=\"Danish Civil War\">Danish Civil War</a>.", "links": [{"title": "Battle of Grathe Heath", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Danish Civil War", "link": "https://wikipedia.org/wiki/Danish_Civil_War"}]}, {"year": "1295", "text": "The first treaty forming the Auld Alliance between Scotland and France against England is signed in Paris.", "html": "1295 - The first treaty forming the <a href=\"https://wikipedia.org/wiki/Auld_Alliance\" title=\"Auld Alliance\">Auld Alliance</a> between <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> against <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> is signed in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "no_year_html": "The first treaty forming the <a href=\"https://wikipedia.org/wiki/Auld_Alliance\" title=\"Auld Alliance\">Auld Alliance</a> between <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> against <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> is signed in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "links": [{"title": "Auld Alliance", "link": "https://wikipedia.org/wiki/Auld_Alliance"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1641", "text": "Irish Catholic gentry from Ulster attempt to seize control of Dublin Castle, the seat of English rule in Ireland, so as to force concessions.", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Irish_Catholics\" title=\"Irish Catholics\">Irish Catholic</a> gentry from <a href=\"https://wikipedia.org/wiki/Ulster\" title=\"Ulster\">Ulster</a> attempt to <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1641\" title=\"Irish Rebellion of 1641\">seize control</a> of <a href=\"https://wikipedia.org/wiki/Dublin_Castle\" title=\"Dublin Castle\">Dublin Castle</a>, the seat of <a href=\"https://wikipedia.org/wiki/English_rule_in_Ireland\" class=\"mw-redirect\" title=\"English rule in Ireland\">English rule in Ireland</a>, so as to force concessions.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irish_Catholics\" title=\"Irish Catholics\">Irish Catholic</a> gentry from <a href=\"https://wikipedia.org/wiki/Ulster\" title=\"Ulster\">Ulster</a> attempt to <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1641\" title=\"Irish Rebellion of 1641\">seize control</a> of <a href=\"https://wikipedia.org/wiki/Dublin_Castle\" title=\"Dublin Castle\">Dublin Castle</a>, the seat of <a href=\"https://wikipedia.org/wiki/English_rule_in_Ireland\" class=\"mw-redirect\" title=\"English rule in Ireland\">English rule in Ireland</a>, so as to force concessions.", "links": [{"title": "Irish Catholics", "link": "https://wikipedia.org/wiki/Irish_Catholics"}, {"title": "Ulster", "link": "https://wikipedia.org/wiki/Ulster"}, {"title": "Irish Rebellion of 1641", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1641"}, {"title": "Dublin Castle", "link": "https://wikipedia.org/wiki/Dublin_Castle"}, {"title": "English rule in Ireland", "link": "https://wikipedia.org/wiki/English_rule_in_Ireland"}]}, {"year": "1642", "text": "The Battle of Edgehill is the first major battle of the English Civil War.", "html": "1642 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Edgehill\" title=\"Battle of Edgehill\">Battle of Edgehill</a> is the first major battle of the <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Edgehill\" title=\"Battle of Edgehill\">Battle of Edgehill</a> is the first major battle of the <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>.", "links": [{"title": "Battle of Edgehill", "link": "https://wikipedia.org/wiki/Battle_of_Edgehill"}, {"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}]}, {"year": "1666", "text": "The most intense tornado on record in English history, an F4 storm on the Fujita scale or T8 on the TORRO scale, strikes the county of Lincolnshire, with winds of more than 213 miles per hour (343 km/h).", "html": "1666 - The most intense tornado on record in English history, an F4 storm on the <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">Fujita scale</a> or T8 on the <a href=\"https://wikipedia.org/wiki/TORRO_scale\" title=\"TORRO scale\">TORRO scale</a>, strikes the county of <a href=\"https://wikipedia.org/wiki/Lincolnshire\" title=\"Lincolnshire\">Lincolnshire</a>, with winds of more than 213 miles per hour (343 km/h).", "no_year_html": "The most intense tornado on record in English history, an F4 storm on the <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">Fujita scale</a> or T8 on the <a href=\"https://wikipedia.org/wiki/TORRO_scale\" title=\"TORRO scale\">TORRO scale</a>, strikes the county of <a href=\"https://wikipedia.org/wiki/Lincolnshire\" title=\"Lincolnshire\">Lincolnshire</a>, with winds of more than 213 miles per hour (343 km/h).", "links": [{"title": "Fujita scale", "link": "https://wikipedia.org/wiki/Fujita_scale"}, {"title": "TORRO scale", "link": "https://wikipedia.org/wiki/TORRO_scale"}, {"title": "Lincolnshire", "link": "https://wikipedia.org/wiki/Lincolnshire"}]}, {"year": "1707", "text": "The First Parliament of the Kingdom of Great Britain convenes.", "html": "1707 - The <a href=\"https://wikipedia.org/wiki/First_Parliament_of_Great_Britain\" title=\"First Parliament of Great Britain\">First Parliament</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> convenes.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Parliament_of_Great_Britain\" title=\"First Parliament of Great Britain\">First Parliament</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> convenes.", "links": [{"title": "First Parliament of Great Britain", "link": "https://wikipedia.org/wiki/First_Parliament_of_Great_Britain"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1798", "text": "The forces of <PERSON> of Janina defeat the French and capture the town of Preveza in the Battle of Nicopolis.", "html": "1798 - The forces of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Janina\" class=\"mw-redirect\" title=\"<PERSON> of Janina\"><PERSON> of Janina</a> defeat the <a href=\"https://wikipedia.org/wiki/First_French_Republic\" class=\"mw-redirect\" title=\"First French Republic\">French</a> and capture the town of <a href=\"https://wikipedia.org/wiki/Preveza\" title=\"Preveza\">Preveza</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Nicopolis_(1798)\" title=\"Battle of Nicopolis (1798)\">Battle of Nicopolis</a>.", "no_year_html": "The forces of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Janina\" class=\"mw-redirect\" title=\"<PERSON> of Janina\"><PERSON> of Janina</a> defeat the <a href=\"https://wikipedia.org/wiki/First_French_Republic\" class=\"mw-redirect\" title=\"First French Republic\">French</a> and capture the town of <a href=\"https://wikipedia.org/wiki/Preveza\" title=\"Preveza\">Preveza</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Nicopolis_(1798)\" title=\"Battle of Nicopolis (1798)\">Battle of Nicopolis</a>.", "links": [{"title": "<PERSON> of Janina", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Jan<PERSON>"}, {"title": "First French Republic", "link": "https://wikipedia.org/wiki/First_French_Republic"}, {"title": "Preveza", "link": "https://wikipedia.org/wiki/Preveza"}, {"title": "Battle of Nicopolis (1798)", "link": "https://wikipedia.org/wiki/Battle_of_Nicopolis_(1798)"}]}, {"year": "1812", "text": "General <PERSON> begins a conspiracy to overthrow <PERSON>, claiming that the Emperor died in the Russian campaign.", "html": "1812 - General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Malet_coup_of_1812\" title=\"Malet coup of 1812\">begins a conspiracy</a> to overthrow <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a>, claiming that the Emperor died in the <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\">Russian campaign</a>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Malet_coup_of_1812\" title=\"Malet coup of 1812\">begins a conspiracy</a> to overthrow <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a>, claiming that the Emperor died in the <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\">Russian campaign</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Malet coup of 1812", "link": "https://wikipedia.org/wiki/Malet_coup_of_1812"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "French invasion of Russia", "link": "https://wikipedia.org/wiki/French_invasion_of_Russia"}]}, {"year": "1850", "text": "The first National Women's Rights Convention begins in Worcester, Massachusetts.", "html": "1850 - The first <a href=\"https://wikipedia.org/wiki/National_Women%27s_Rights_Convention\" title=\"National Women's Rights Convention\">National Women's Rights Convention</a> begins in <a href=\"https://wikipedia.org/wiki/Worcester,_Massachusetts\" title=\"Worcester, Massachusetts\">Worcester, Massachusetts</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/National_Women%27s_Rights_Convention\" title=\"National Women's Rights Convention\">National Women's Rights Convention</a> begins in <a href=\"https://wikipedia.org/wiki/Worcester,_Massachusetts\" title=\"Worcester, Massachusetts\">Worcester, Massachusetts</a>.", "links": [{"title": "National Women's Rights Convention", "link": "https://wikipedia.org/wiki/National_Women%27s_Rights_Convention"}, {"title": "Worcester, Massachusetts", "link": "https://wikipedia.org/wiki/Worcester,_Massachusetts"}]}, {"year": "1856", "text": "Second Opium War: Dissatisfied with imperial commissioner <PERSON>'s reparations for the alleged slighting of a British-owned vessel and at Consul <PERSON>'s urging, British Rear-Admiral <PERSON> launches an assault on the Barrier Forts outside Canton in the first military engagement of the Second Opium War.", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a>: Dissatisfied with imperial commissioner <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s reparations for the alleged slighting of a British-owned vessel and at Consul <a href=\"https://wikipedia.org/wiki/<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON>'s</a> urging, <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> Rear-Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer,_born_1802)\" title=\"<PERSON> (Royal Navy officer, born 1802)\"><PERSON></a> launches <a href=\"https://wikipedia.org/wiki/Battle_of_Canton_(1856)\" title=\"Battle of Canton (1856)\">an assault on the Barrier Forts outside Canton</a> in the first military engagement of the <a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a>: Dissatisfied with imperial commissioner <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s reparations for the alleged slighting of a British-owned vessel and at Consul <a href=\"https://wikipedia.org/wiki/<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON>'s</a> urging, <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> Rear-Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Royal_Navy_officer,_born_1802)\" title=\"<PERSON> (Royal Navy officer, born 1802)\"><PERSON></a> launches <a href=\"https://wikipedia.org/wiki/Battle_of_Canton_(1856)\" title=\"Battle of Canton (1856)\">an assault on the Barrier Forts outside Canton</a> in the first military engagement of the <a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a>.", "links": [{"title": "Second Opium War", "link": "https://wikipedia.org/wiki/Second_Opium_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "<PERSON> (Royal Navy officer, born 1802)", "link": "https://wikipedia.org/wiki/<PERSON>(Royal_Navy_officer,_born_1802)"}, {"title": "Battle of Canton (1856)", "link": "https://wikipedia.org/wiki/Battle_of_Canton_(1856)"}, {"title": "Second Opium War", "link": "https://wikipedia.org/wiki/Second_Opium_War"}]}, {"year": "1864", "text": "American Civil War: The Battle of Westport is the last significant engagement west of the Mississippi River, ending in a Union victory.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Westport\" title=\"Battle of Westport\">Battle of Westport</a> is the last significant engagement west of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>, ending in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Westport\" title=\"Battle of Westport\">Battle of Westport</a> is the last significant engagement west of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>, ending in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> victory.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Westport", "link": "https://wikipedia.org/wiki/Battle_of_Westport"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1868", "text": "Meiji Restoration: Having taken the shogunate's seat of power at Edo and declared it his new capital as Tokyo, <PERSON><PERSON><PERSON><PERSON> proclaims the start of the new Meiji era.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Meiji_Restoration\" title=\"Meiji Restoration\">Meiji Restoration</a>: Having taken the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">shogunate's</a> seat of power at <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a> and declared it his new capital as <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo</a>, <a href=\"https://wikipedia.org/wiki/Emperor_Meiji\" title=\"Emperor Meiji\"><PERSON><PERSON><PERSON><PERSON></a> proclaims the start of the new <a href=\"https://wikipedia.org/wiki/Meiji_era\" title=\"Meiji era\">Meiji era</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meiji_Restoration\" title=\"Meiji Restoration\">Meiji Restoration</a>: Having taken the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">shogunate's</a> seat of power at <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a> and declared it his new capital as <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo</a>, <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Meiji\"><PERSON><PERSON><PERSON><PERSON></a> proclaims the start of the new <a href=\"https://wikipedia.org/wiki/Meiji_era\" title=\"Meiji era\">Meiji era</a>.", "links": [{"title": "Meiji Restoration", "link": "https://wikipedia.org/wiki/Meiji_Restoration"}, {"title": "Tokugawa shogunate", "link": "https://wikipedia.org/wiki/Tokugawa_shogunate"}, {"title": "Edo (Tokyo)", "link": "https://wikipedia.org/wiki/Edo_(Tokyo)"}, {"title": "Tokyo", "link": "https://wikipedia.org/wiki/Tokyo"}, {"title": "Emperor <PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>"}, {"title": "Meiji era", "link": "https://wikipedia.org/wiki/Meiji_era"}]}, {"year": "1906", "text": "<PERSON> flies an airplane in the first heavier-than-air flight in Europe.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies an <a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">airplane</a> in the first <a href=\"https://wikipedia.org/wiki/Heavier-than-air_flight\" class=\"mw-redirect\" title=\"Heavier-than-air flight\">heavier-than-air flight</a> in Europe.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies an <a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">airplane</a> in the first <a href=\"https://wikipedia.org/wiki/Heavier-than-air_flight\" class=\"mw-redirect\" title=\"Heavier-than-air flight\">heavier-than-air flight</a> in Europe.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Airplane", "link": "https://wikipedia.org/wiki/Airplane"}, {"title": "Heavier-than-air flight", "link": "https://wikipedia.org/wiki/Heavier-than-air_flight"}]}, {"year": "1911", "text": "The Italo-Turkish War sees the first use of an airplane in combat when an Italian pilot makes a reconnaissance flight.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/Italo-Turkish_War\" title=\"Italo-Turkish War\">Italo-Turkish War</a> sees the first use of an airplane in combat when an Italian pilot makes a reconnaissance flight.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Italo-Turkish_War\" title=\"Italo-Turkish War\">Italo-Turkish War</a> sees the first use of an airplane in combat when an Italian pilot makes a reconnaissance flight.", "links": [{"title": "Italo-Turkish War", "link": "https://wikipedia.org/wiki/Italo-Turkish_War"}]}, {"year": "1912", "text": "First Balkan War: The Battle of Kumanovo between the Serbian and Ottoman armies begins.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kumanovo\" title=\"Battle of Kumanovo\">Battle of Kumanovo</a> between the Serbian and Ottoman armies begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kumanovo\" title=\"Battle of Kumanovo\">Battle of Kumanovo</a> between the Serbian and Ottoman armies begins.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Battle of Kumanovo", "link": "https://wikipedia.org/wiki/Battle_of_Kumanovo"}]}, {"year": "1923", "text": "German October: Due to a miscommunication with the party leadership, a militant section of the Communist Party of Germany launches an insurrection in Hamburg.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/German_October\" title=\"German October\">German October</a>: Due to a miscommunication with the party leadership, a militant section of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Germany\" title=\"Communist Party of Germany\">Communist Party of Germany</a> launches <a href=\"https://wikipedia.org/wiki/Hamburg_Uprising\" title=\"Hamburg Uprising\">an insurrection in Hamburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/German_October\" title=\"German October\">German October</a>: Due to a miscommunication with the party leadership, a militant section of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Germany\" title=\"Communist Party of Germany\">Communist Party of Germany</a> launches <a href=\"https://wikipedia.org/wiki/Hamburg_Uprising\" title=\"Hamburg Uprising\">an insurrection in Hamburg</a>.", "links": [{"title": "German October", "link": "https://wikipedia.org/wiki/German_October"}, {"title": "Communist Party of Germany", "link": "https://wikipedia.org/wiki/Communist_Party_of_Germany"}, {"title": "Hamburg Uprising", "link": "https://wikipedia.org/wiki/Hamburg_Uprising"}]}, {"year": "1924", "text": "Second Zhili-Fengtian War: Warlord <PERSON>, with the covert support of the Empire of Japan, stages a coup in Beijing against his erstwhile superiors in the Zhili clique, crippling their nearly victorious war effort against the Fengtian clique and forcing them to withdraw from northern China.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Second_Zhili%E2%80%93Fengtian_War\" title=\"Second Zhili-Fengtian War\">Second Zhili-Fengtian War</a>: Warlord <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Feng Yuxiang\"><PERSON></a>, with the covert support of the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a>, <a href=\"https://wikipedia.org/wiki/1924_Beijing_Coup\" title=\"1924 Beijing Coup\">stages a coup in Beijing</a> against his erstwhile superiors in the <a href=\"https://wikipedia.org/wiki/Zhili_clique\" title=\"Zhili clique\">Zhili clique</a>, crippling their nearly victorious war effort against the <a href=\"https://wikipedia.org/wiki/Fengtian_clique\" title=\"Fengtian clique\">Fengtian clique</a> and forcing them to withdraw from northern China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Zhili%E2%80%93Fengtian_War\" title=\"Second Zhili-Fengtian War\">Second Zhili-Fengtian War</a>: Warlord <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Feng Yuxiang\"><PERSON></a>, with the covert support of the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a>, <a href=\"https://wikipedia.org/wiki/1924_Beijing_Coup\" title=\"1924 Beijing Coup\">stages a coup in Beijing</a> against his erstwhile superiors in the <a href=\"https://wikipedia.org/wiki/Zhili_clique\" title=\"Zhili clique\">Zhili clique</a>, crippling their nearly victorious war effort against the <a href=\"https://wikipedia.org/wiki/Fengtian_clique\" title=\"Fengtian clique\">Fengtian clique</a> and forcing them to withdraw from northern China.", "links": [{"title": "Second Zhili-Fengtian War", "link": "https://wikipedia.org/wiki/Second_Zhili%E2%80%93Fengtian_War"}, {"title": "Feng Yu<PERSON>", "link": "https://wikipedia.org/wiki/Feng_<PERSON>"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "1924 Beijing Coup", "link": "https://wikipedia.org/wiki/1924_Beijing_Coup"}, {"title": "Zhili clique", "link": "https://wikipedia.org/wiki/Zhili_clique"}, {"title": "Fengtian clique", "link": "https://wikipedia.org/wiki/Fengtian_clique"}]}, {"year": "1927", "text": "The Imatra Cinema is destroyed in a fire in Tampere, Finland, during showing the 1924 film Wages of Virtue; 21 people die in the fire and almost 30 are injured.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Imatra_(cinema)\" title=\"Imatra (cinema)\">Imatra Cinema</a> is destroyed in a fire in <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, during showing the 1924 film <i><a href=\"https://wikipedia.org/wiki/Wages_of_Virtue\" title=\"Wages of Virtue\">Wages of Virtue</a></i>; 21 people die in the fire and almost 30 are injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Imatra_(cinema)\" title=\"Imatra (cinema)\">Imatra Cinema</a> is destroyed in a fire in <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, during showing the 1924 film <i><a href=\"https://wikipedia.org/wiki/Wages_of_Virtue\" title=\"Wages of Virtue\">Wages of Virtue</a></i>; 21 people die in the fire and almost 30 are injured.", "links": [{"title": "<PERSON><PERSON><PERSON> (cinema)", "link": "https://wikipedia.org/wiki/<PERSON>matra_(cinema)"}, {"title": "Tampere", "link": "https://wikipedia.org/wiki/Tampere"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Wages of Virtue", "link": "https://wikipedia.org/wiki/Wages_of_Virtue"}]}, {"year": "1940", "text": "<PERSON> and <PERSON> meet at Hendaye to discuss the possibility of Spain entering the Second World War.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Meeting_at_Hendaye\" title=\"Meeting at Hendaye\">meet at Hendaye</a> to discuss the possibility of <a href=\"https://wikipedia.org/wiki/Spain_during_World_War_II\" title=\"Spain during World War II\">Spain entering the Second World War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Meeting_at_Hendaye\" title=\"Meeting at Hendaye\">meet at Hendaye</a> to discuss the possibility of <a href=\"https://wikipedia.org/wiki/Spain_during_World_War_II\" title=\"Spain during World War II\">Spain entering the Second World War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}, {"title": "Meeting at Hendaye", "link": "https://wikipedia.org/wiki/Meeting_at_He<PERSON><PERSON>"}, {"title": "Spain during World War II", "link": "https://wikipedia.org/wiki/Spain_during_World_War_II"}]}, {"year": "1941", "text": "The Holocaust: Nazi Germany prohibits Jews from emigrating, including in its occupied territories.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> prohibits <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> from emigrating, including in its <a href=\"https://wikipedia.org/wiki/German-occupied_Europe\" title=\"German-occupied Europe\">occupied territories</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> prohibits <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> from emigrating, including in its <a href=\"https://wikipedia.org/wiki/German-occupied_Europe\" title=\"German-occupied Europe\">occupied territories</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}, {"title": "German-occupied Europe", "link": "https://wikipedia.org/wiki/German-occupied_Europe"}]}, {"year": "1942", "text": "World War II: Allied forces commence the Second Battle of El Alamein, which proves to be the key turning point in the North African campaign.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Allied forces commence the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_El_Alamein\" title=\"Second Battle of El Alamein\">Second Battle of El Alamein</a>, which proves to be the key turning point in the North African campaign.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Allied forces commence the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_El_Alamein\" title=\"Second Battle of El Alamein\">Second Battle of El Alamein</a>, which proves to be the key turning point in the North African campaign.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Second Battle of El Alamein", "link": "https://wikipedia.org/wiki/Second_Battle_of_El_Alamein"}]}, {"year": "1942", "text": "All 12 passengers and crewmen aboard American Airlines Flight 28 are killed when it collides with a U.S. Army Air Force bomber near Palm Springs, California.", "html": "1942 - All 12 passengers and crewmen aboard <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_28\" title=\"American Airlines Flight 28\">American Airlines Flight 28</a> are killed when it collides with a <a href=\"https://wikipedia.org/wiki/United_States_Army_Air_Forces\" title=\"United States Army Air Forces\">U.S. Army Air Force</a> bomber near <a href=\"https://wikipedia.org/wiki/Palm_Springs,_California\" title=\"Palm Springs, California\">Palm Springs, California</a>.", "no_year_html": "All 12 passengers and crewmen aboard <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_28\" title=\"American Airlines Flight 28\">American Airlines Flight 28</a> are killed when it collides with a <a href=\"https://wikipedia.org/wiki/United_States_Army_Air_Forces\" title=\"United States Army Air Forces\">U.S. Army Air Force</a> bomber near <a href=\"https://wikipedia.org/wiki/Palm_Springs,_California\" title=\"Palm Springs, California\">Palm Springs, California</a>.", "links": [{"title": "American Airlines Flight 28", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_28"}, {"title": "United States Army Air Forces", "link": "https://wikipedia.org/wiki/United_States_Army_Air_Forces"}, {"title": "Palm Springs, California", "link": "https://wikipedia.org/wiki/Palm_Springs,_California"}]}, {"year": "1942", "text": "World War II: The Battle for Henderson Field begins on Guadalcanal.", "html": "1942 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_for_Henderson_Field\" title=\"Battle for Henderson Field\">Battle for Henderson Field</a> begins on Guadalcanal.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_for_Henderson_Field\" title=\"Battle for Henderson Field\">Battle for Henderson Field</a> begins on Guadalcanal.", "links": [{"title": "Battle for Henderson Field", "link": "https://wikipedia.org/wiki/Battle_for_Henderson_Field"}]}, {"year": "1944", "text": "World War II: The Battle of Leyte Gulf begins.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Leyte_Gulf\" title=\"Battle of Leyte Gulf\">Battle of Leyte Gulf</a> begins.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Leyte_Gulf\" title=\"Battle of Leyte Gulf\">Battle of Leyte Gulf</a> begins.", "links": [{"title": "Battle of Leyte Gulf", "link": "https://wikipedia.org/wiki/Battle_of_Leyte_Gulf"}]}, {"year": "1955", "text": "Prime Minister <PERSON><PERSON> defeats former emperor <PERSON><PERSON><PERSON> in a referendum and founds the Republic of Vietnam.", "html": "1955 - Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> defeats former emperor <a href=\"https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i\" title=\"Bảo Đại\"><PERSON><PERSON><PERSON> Đạ<PERSON></a> in a <a href=\"https://wikipedia.org/wiki/1955_State_of_Vietnam_referendum\" title=\"1955 State of Vietnam referendum\">referendum</a> and founds the <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">Republic of Vietnam</a>.", "no_year_html": "Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> defeats former emperor <a href=\"https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i\" title=\"Bảo Đại\"><PERSON><PERSON><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/1955_State_of_Vietnam_referendum\" title=\"1955 State of Vietnam referendum\">referendum</a> and founds the <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">Republic of Vietnam</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i"}, {"title": "1955 State of Vietnam referendum", "link": "https://wikipedia.org/wiki/1955_State_of_Vietnam_referendum"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1955", "text": "The people of the Saar region vote in a referendum to unite with West Germany instead of France.", "html": "1955 - The people of the <a href=\"https://wikipedia.org/wiki/Saarland\" title=\"Saarland\">Saar region</a> vote in a <a href=\"https://wikipedia.org/wiki/1955_Saar_Statute_referendum\" title=\"1955 Saar Statute referendum\">referendum</a> to unite with <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> instead of <a href=\"https://wikipedia.org/wiki/French_Fourth_Republic\" title=\"French Fourth Republic\">France</a>.", "no_year_html": "The people of the <a href=\"https://wikipedia.org/wiki/Saarland\" title=\"Saarland\">Saar region</a> vote in a <a href=\"https://wikipedia.org/wiki/1955_Saar_Statute_referendum\" title=\"1955 Saar Statute referendum\">referendum</a> to unite with <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> instead of <a href=\"https://wikipedia.org/wiki/French_Fourth_Republic\" title=\"French Fourth Republic\">France</a>.", "links": [{"title": "Saarland", "link": "https://wikipedia.org/wiki/Saarland"}, {"title": "1955 Saar Statute referendum", "link": "https://wikipedia.org/wiki/1955_Saar_Statute_referendum"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}, {"title": "French Fourth Republic", "link": "https://wikipedia.org/wiki/French_Fourth_Republic"}]}, {"year": "1956", "text": "Secret police shoot several anti-communist protesters, igniting the Hungarian Revolution.", "html": "1956 - Secret police shoot several anti-communist protesters, igniting the <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>.", "no_year_html": "Secret police shoot several anti-communist protesters, igniting the <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>.", "links": [{"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}]}, {"year": "1958", "text": "Canada's Springhill mining disaster kills seventy-five miners, while ninety-nine others are rescued.", "html": "1958 - Canada's <a href=\"https://wikipedia.org/wiki/Springhill_mining_disasters#1958_bump\" title=\"Springhill mining disasters\">Springhill mining disaster</a> kills seventy-five miners, while ninety-nine others are rescued.", "no_year_html": "Canada's <a href=\"https://wikipedia.org/wiki/Springhill_mining_disasters#1958_bump\" title=\"Springhill mining disasters\">Springhill mining disaster</a> kills seventy-five miners, while ninety-nine others are rescued.", "links": [{"title": "Springhill mining disasters", "link": "https://wikipedia.org/wiki/Springhill_mining_disasters#1958_bump"}]}, {"year": "1958", "text": "Belgian artist <PERSON><PERSON><PERSON>'s comic characters, the Smurfs, make their debut in Spirou magazine.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgian</a> artist <a href=\"https://wikipedia.org/wiki/Peyo\" title=\"<PERSON>ey<PERSON>\"><PERSON><PERSON><PERSON></a>'s comic characters, <a href=\"https://wikipedia.org/wiki/The_Smurfs\" title=\"The Smurfs\">the Smurfs</a>, make their debut in <a href=\"https://wikipedia.org/wiki/Spirou_(magazine)\" title=\"Spirou (magazine)\">Spirou</a> magazine.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgian</a> artist <a href=\"https://wikipedia.org/wiki/<PERSON>eyo\" title=\"<PERSON>ey<PERSON>\"><PERSON><PERSON><PERSON></a>'s comic characters, <a href=\"https://wikipedia.org/wiki/The_Smurfs\" title=\"The Smurfs\">the Smurfs</a>, make their debut in <a href=\"https://wikipedia.org/wiki/Spirou_(magazine)\" title=\"Spirou (magazine)\">Spirou</a> magazine.", "links": [{"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eyo"}, {"title": "The Smurfs", "link": "https://wikipedia.org/wiki/The_Smurfs"}, {"title": "Spirou (magazine)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>rou_(magazine)"}]}, {"year": "1959", "text": "Aeroflot Flight 200 crashes while attempting to land at Vnukovo International Airport, killing 28.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_200\" title=\"Aeroflot Flight 200\">Aeroflot Flight 200</a> crashes while attempting to land at <a href=\"https://wikipedia.org/wiki/Vnukovo_International_Airport\" title=\"Vnukovo International Airport\">Vnukovo International Airport</a>, killing 28.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_200\" title=\"Aeroflot Flight 200\">Aeroflot Flight 200</a> crashes while attempting to land at <a href=\"https://wikipedia.org/wiki/Vnukovo_International_Airport\" title=\"Vnukovo International Airport\">Vnukovo International Airport</a>, killing 28.", "links": [{"title": "Aeroflot Flight 200", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_200"}, {"title": "Vnukovo International Airport", "link": "https://wikipedia.org/wiki/Vnukovo_International_Airport"}]}, {"year": "1965", "text": "Vietnam War: The 1st Cavalry Division (Airmobile), in conjunction with the Army of the Republic of Vietnam, launches an operation seeking to destroy Communist forces during the siege of Plei Me.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/1st_Cavalry_Division_(Airmobile)\" class=\"mw-redirect\" title=\"1st Cavalry Division (Airmobile)\">1st Cavalry Division (Airmobile)</a>, in conjunction with the <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">Army of the Republic of Vietnam</a>, launches an operation seeking to destroy Communist forces during the <a href=\"https://wikipedia.org/wiki/Siege_of_Plei_Me\" title=\"Siege of Plei Me\">siege of Plei Me</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/1st_Cavalry_Division_(Airmobile)\" class=\"mw-redirect\" title=\"1st Cavalry Division (Airmobile)\">1st Cavalry Division (Airmobile)</a>, in conjunction with the <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">Army of the Republic of Vietnam</a>, launches an operation seeking to destroy Communist forces during the <a href=\"https://wikipedia.org/wiki/Siege_of_Plei_Me\" title=\"Siege of Plei Me\">siege of Plei Me</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "1st Cavalry Division (Airmobile)", "link": "https://wikipedia.org/wiki/1st_Cavalry_Division_(Airmobile)"}, {"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "Siege of Plei Me", "link": "https://wikipedia.org/wiki/Siege_of_Plei_Me"}]}, {"year": "1970", "text": "<PERSON> sets a land speed record in a rocket-powered automobile called the Blue Flame, fueled with natural gas.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets a land speed record in a rocket-powered automobile called the <a href=\"https://wikipedia.org/wiki/Blue_Flame\" title=\"Blue Flame\">Blue Flame</a>, fueled with <a href=\"https://wikipedia.org/wiki/Natural_gas\" title=\"Natural gas\">natural gas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets a land speed record in a rocket-powered automobile called the <a href=\"https://wikipedia.org/wiki/Blue_Flame\" title=\"Blue Flame\">Blue Flame</a>, fueled with <a href=\"https://wikipedia.org/wiki/Natural_gas\" title=\"Natural gas\">natural gas</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Blue Flame", "link": "https://wikipedia.org/wiki/Blue_Flame"}, {"title": "Natural gas", "link": "https://wikipedia.org/wiki/Natural_gas"}]}, {"year": "1972", "text": "Vietnam War: Operation Linebacker, a US bombing campaign against North Vietnam in response to its Easter Offensive, ends after five months.", "html": "1972 - Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Linebacker\" title=\"Operation Linebacker\">Operation Linebacker</a>, a US bombing campaign against <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> in response to its <a href=\"https://wikipedia.org/wiki/Easter_Offensive\" title=\"Easter Offensive\">Easter Offensive</a>, ends after five months.", "no_year_html": "Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Linebacker\" title=\"Operation Linebacker\">Operation Linebacker</a>, a US bombing campaign against <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> in response to its <a href=\"https://wikipedia.org/wiki/Easter_Offensive\" title=\"Easter Offensive\">Easter Offensive</a>, ends after five months.", "links": [{"title": "Operation Linebacker", "link": "https://wikipedia.org/wiki/Operation_Linebacker"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "Easter Offensive", "link": "https://wikipedia.org/wiki/Easter_Offensive"}]}, {"year": "1978", "text": "Aeroflot Flight 6515 crashes off Syvash, killing all 26 people aboard.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_6515\" title=\"Aeroflot Flight 6515\">Aeroflot Flight 6515</a> crashes off <a href=\"https://wikipedia.org/wiki/Syvash\" title=\"Syvash\">Syvash</a>, killing all 26 people aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_6515\" title=\"Aeroflot Flight 6515\">Aeroflot Flight 6515</a> crashes off <a href=\"https://wikipedia.org/wiki/Syvash\" title=\"Syvash\">Syvash</a>, killing all 26 people aboard.", "links": [{"title": "Aeroflot Flight 6515", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_6515"}, {"title": "Syvash", "link": "https://wikipedia.org/wiki/Syvash"}]}, {"year": "1982", "text": "A gunfight breaks out between police officers and members of a religious cult in Arizona. The shootout leaves two cultists dead and dozens of cultists and police officers injured.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Miracle_Valley_shootout\" title=\"Miracle Valley shootout\">A gunfight breaks out</a> between police officers and members of a religious cult in Arizona. The shootout leaves two cultists dead and dozens of cultists and police officers injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miracle_Valley_shootout\" title=\"Miracle Valley shootout\">A gunfight breaks out</a> between police officers and members of a religious cult in Arizona. The shootout leaves two cultists dead and dozens of cultists and police officers injured.", "links": [{"title": "Miracle Valley shootout", "link": "https://wikipedia.org/wiki/Miracle_Valley_shootout"}]}, {"year": "1983", "text": "Lebanese Civil War: The U.S. Marines Corps barracks in Beirut is hit by a truck bomb, killing 241 U.S. military personnel.  A French Army barracks in Lebanon is also hit that same morning, killing 58 troops.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War\" title=\"Lebanese Civil War\">Lebanese Civil War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines Corps</a> barracks in Beirut is <a href=\"https://wikipedia.org/wiki/1983_Beirut_barracks_bombing\" class=\"mw-redirect\" title=\"1983 Beirut barracks bombing\">hit by a truck bomb</a>, killing 241 U.S. military personnel. A <a href=\"https://wikipedia.org/wiki/French_Army\" title=\"French Army\">French Army</a> barracks in Lebanon is also hit that same morning, killing 58 troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War\" title=\"Lebanese Civil War\">Lebanese Civil War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines Corps</a> barracks in Beirut is <a href=\"https://wikipedia.org/wiki/1983_Beirut_barracks_bombing\" class=\"mw-redirect\" title=\"1983 Beirut barracks bombing\">hit by a truck bomb</a>, killing 241 U.S. military personnel. A <a href=\"https://wikipedia.org/wiki/French_Army\" title=\"French Army\">French Army</a> barracks in Lebanon is also hit that same morning, killing 58 troops.", "links": [{"title": "Lebanese Civil War", "link": "https://wikipedia.org/wiki/Lebanese_Civil_War"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "1983 Beirut barracks bombing", "link": "https://wikipedia.org/wiki/1983_Beirut_barracks_bombing"}, {"title": "French Army", "link": "https://wikipedia.org/wiki/French_Army"}]}, {"year": "1989", "text": "The Hungarian Republic officially replaces the communist Hungarian People's Republic.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/End_of_communism_in_Hungary_(1989)\" class=\"mw-redirect\" title=\"End of communism in Hungary (1989)\">The Hungarian Republic officially replaces</a> the communist <a href=\"https://wikipedia.org/wiki/Hungarian_People%27s_Republic\" title=\"Hungarian People's Republic\">Hungarian People's Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/End_of_communism_in_Hungary_(1989)\" class=\"mw-redirect\" title=\"End of communism in Hungary (1989)\">The Hungarian Republic officially replaces</a> the communist <a href=\"https://wikipedia.org/wiki/Hungarian_People%27s_Republic\" title=\"Hungarian People's Republic\">Hungarian People's Republic</a>.", "links": [{"title": "End of communism in Hungary (1989)", "link": "https://wikipedia.org/wiki/End_of_communism_in_Hungary_(1989)"}, {"title": "Hungarian People's Republic", "link": "https://wikipedia.org/wiki/Hungarian_People%27s_Republic"}]}, {"year": "1989", "text": "Bankruptcy of Wärtsilä Marine, the biggest bankruptcy in the Nordic countries up until then.", "html": "1989 - Bankruptcy of <a href=\"https://wikipedia.org/wiki/W%C3%A4rtsil%C3%A4_Marine\" title=\"Wärtsilä Marine\">Wärtsilä Marine</a>, the biggest bankruptcy in the Nordic countries up until then.", "no_year_html": "Bankruptcy of <a href=\"https://wikipedia.org/wiki/W%C3%A4rtsil%C3%A4_Marine\" title=\"Wärtsilä Marine\">Wärtsilä Marine</a>, the biggest bankruptcy in the Nordic countries up until then.", "links": [{"title": "Wärtsilä Marine", "link": "https://wikipedia.org/wiki/W%C3%A4rtsil%C3%A4_Marine"}]}, {"year": "1989", "text": "An explosion at the Houston Chemical Complex in Pasadena, Texas, which registered a 3.5 on the Richter magnitude scale, kills 23 and injures 314.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_disaster_of_1989\" title=\"Phillips disaster of 1989\">An explosion at the Houston Chemical Complex</a> in <a href=\"https://wikipedia.org/wiki/Pasadena,_Texas\" title=\"Pasadena, Texas\">Pasadena, Texas</a>, which registered a 3.5 on the <a href=\"https://wikipedia.org/wiki/Richter_magnitude_scale\" class=\"mw-redirect\" title=\"Richter magnitude scale\">Richter magnitude scale</a>, kills 23 and injures 314.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_disaster_of_1989\" title=\"Phillips disaster of 1989\">An explosion at the Houston Chemical Complex</a> in <a href=\"https://wikipedia.org/wiki/Pasadena,_Texas\" title=\"Pasadena, Texas\">Pasadena, Texas</a>, which registered a 3.5 on the <a href=\"https://wikipedia.org/wiki/Richter_magnitude_scale\" class=\"mw-redirect\" title=\"Richter magnitude scale\">Richter magnitude scale</a>, kills 23 and injures 314.", "links": [{"title": "Phillips disaster of 1989", "link": "https://wikipedia.org/wiki/<PERSON>_disaster_of_1989"}, {"title": "Pasadena, Texas", "link": "https://wikipedia.org/wiki/Pasadena,_Texas"}, {"title": "Richter magnitude scale", "link": "https://wikipedia.org/wiki/Richter_magnitude_scale"}]}, {"year": "1991", "text": "Signing of the Paris Peace Accords which ends the Cambodian-Vietnamese War.", "html": "1991 - Signing of the <a href=\"https://wikipedia.org/wiki/1991_Paris_Peace_Accords\" class=\"mw-redirect\" title=\"1991 Paris Peace Accords\">Paris Peace Accords</a> which ends the <a href=\"https://wikipedia.org/wiki/Cambodian%E2%80%93Vietnamese_War\" title=\"Cambodian-Vietnamese War\">Cambodian-Vietnamese War</a>.", "no_year_html": "Signing of the <a href=\"https://wikipedia.org/wiki/1991_Paris_Peace_Accords\" class=\"mw-redirect\" title=\"1991 Paris Peace Accords\">Paris Peace Accords</a> which ends the <a href=\"https://wikipedia.org/wiki/Cambodian%E2%80%93Vietnamese_War\" title=\"Cambodian-Vietnamese War\">Cambodian-Vietnamese War</a>.", "links": [{"title": "1991 Paris Peace Accords", "link": "https://wikipedia.org/wiki/1991_Paris_Peace_Accords"}, {"title": "Cambodian-Vietnamese War", "link": "https://wikipedia.org/wiki/Cambodian%E2%80%93Vietnamese_War"}]}, {"year": "1993", "text": "The Troubles: A Provisional IRA bomb prematurely detonates in Belfast, killing the bomber and nine civilians.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: A <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> bomb <a href=\"https://wikipedia.org/wiki/Shankill_Road_bombing\" title=\"Shankill Road bombing\">prematurely detonates</a> in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, killing the bomber and nine civilians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: A <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> bomb <a href=\"https://wikipedia.org/wiki/Shankill_Road_bombing\" title=\"Shankill Road bombing\">prematurely detonates</a> in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, killing the bomber and nine civilians.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Shankill Road bombing", "link": "https://wikipedia.org/wiki/Shankill_Road_bombing"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON> is found guilty of first-degree murder in the shooting death of popular Latin singer <PERSON>.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d%C3%ADvar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is found guilty of first-degree murder in the <a href=\"https://wikipedia.org/wiki/Murder_of_Selena\" title=\"Murder of Selena\">shooting death</a> of popular Latin singer <a href=\"https://wikipedia.org/wiki/Selena\" title=\"Selena\">Selena</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%ADvar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is found guilty of first-degree murder in the <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of Selena\">shooting death</a> of popular Latin singer <a href=\"https://wikipedia.org/wiki/Selena\" title=\"Selena\">Selena</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a_Sald%C3%ADvar"}, {"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Selena"}]}, {"year": "1998", "text": "Israel and the Palestinian Authority sign the Wye River Memorandum.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> and the <a href=\"https://wikipedia.org/wiki/Palestinian_National_Authority\" class=\"mw-redirect\" title=\"Palestinian National Authority\">Palestinian Authority</a> sign the <a href=\"https://wikipedia.org/wiki/Wye_River_Memorandum\" title=\"Wye River Memorandum\">Wye River Memorandum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> and the <a href=\"https://wikipedia.org/wiki/Palestinian_National_Authority\" class=\"mw-redirect\" title=\"Palestinian National Authority\">Palestinian Authority</a> sign the <a href=\"https://wikipedia.org/wiki/Wye_River_Memorandum\" title=\"Wye River Memorandum\">Wye River Memorandum</a>.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Palestinian National Authority", "link": "https://wikipedia.org/wiki/Palestinian_National_Authority"}, {"title": "Wye River Memorandum", "link": "https://wikipedia.org/wiki/Wye_River_Memorandum"}]}, {"year": "2001", "text": "Apple Computer releases the iPod.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Apple_Computer\" class=\"mw-redirect\" title=\"Apple Computer\">Apple Computer</a> releases the <a href=\"https://wikipedia.org/wiki/IPod\" title=\"IPod\">iPod</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apple_Computer\" class=\"mw-redirect\" title=\"Apple Computer\">Apple Computer</a> releases the <a href=\"https://wikipedia.org/wiki/IPod\" title=\"IPod\">iPod</a>.", "links": [{"title": "Apple Computer", "link": "https://wikipedia.org/wiki/Apple_Computer"}, {"title": "IPod", "link": "https://wikipedia.org/wiki/IPod"}]}, {"year": "2002", "text": "Second Chechen War: Chechen separatist terrorists seize the House of Culture theater in Moscow and take approximately 700 theater-goers hostage.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Second_Chechen_War\" title=\"Second Chechen War\">Second Chechen War</a>: <a href=\"https://wikipedia.org/wiki/Chechen_Republic_of_Ichkeria\" title=\"Chechen Republic of Ichkeria\">Chechen separatist</a> terrorists <a href=\"https://wikipedia.org/wiki/Moscow_theater_hostage_crisis\" title=\"Moscow theater hostage crisis\">seize</a> the House of Culture theater in Moscow and take approximately 700 theater-goers hostage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Chechen_War\" title=\"Second Chechen War\">Second Chechen War</a>: <a href=\"https://wikipedia.org/wiki/Chechen_Republic_of_Ichkeria\" title=\"Chechen Republic of Ichkeria\">Chechen separatist</a> terrorists <a href=\"https://wikipedia.org/wiki/Moscow_theater_hostage_crisis\" title=\"Moscow theater hostage crisis\">seize</a> the House of Culture theater in Moscow and take approximately 700 theater-goers hostage.", "links": [{"title": "Second Chechen War", "link": "https://wikipedia.org/wiki/Second_Chechen_War"}, {"title": "Chechen Republic of Ichkeria", "link": "https://wikipedia.org/wiki/Chechen_Republic_of_Ichkeria"}, {"title": "Moscow theater hostage crisis", "link": "https://wikipedia.org/wiki/Moscow_theater_hostage_crisis"}]}, {"year": "2004", "text": "A powerful earthquake and its aftershocks hit Niigata Prefecture in northern Japan, killing 35 people, injuring 2,200, and leaving 85,000 homeless or evacuated.", "html": "2004 - A powerful <a href=\"https://wikipedia.org/wiki/2004_Ch%C5%ABetsu_earthquake\" title=\"2004 Chūetsu earthquake\">earthquake</a> and its aftershocks hit <a href=\"https://wikipedia.org/wiki/Niigata_Prefecture\" title=\"Niigata Prefecture\">Niigata Prefecture</a> in northern Japan, killing 35 people, injuring 2,200, and leaving 85,000 homeless or evacuated.", "no_year_html": "A powerful <a href=\"https://wikipedia.org/wiki/2004_Ch%C5%ABetsu_earthquake\" title=\"2004 Chūetsu earthquake\">earthquake</a> and its aftershocks hit <a href=\"https://wikipedia.org/wiki/Niigata_Prefecture\" title=\"Niigata Prefecture\">Niigata Prefecture</a> in northern Japan, killing 35 people, injuring 2,200, and leaving 85,000 homeless or evacuated.", "links": [{"title": "2004 Chūetsu earthquake", "link": "https://wikipedia.org/wiki/2004_Ch%C5%ABetsu_earthquake"}, {"title": "Niigata Prefecture", "link": "https://wikipedia.org/wiki/Niigata_Prefecture"}]}, {"year": "2007", "text": "A storm causes the Mexican Kab 101 oil platform to collide with a wellhead, leading to the death and drowning of 22 people during rescue operations after evacuation of the platform.", "html": "2007 - A storm causes the Mexican <a href=\"https://wikipedia.org/wiki/Kab_101\" title=\"Kab 101\">Kab 101</a> oil platform to collide with a wellhead, leading to the death and drowning of 22 people during rescue operations after evacuation of the platform.", "no_year_html": "A storm causes the Mexican <a href=\"https://wikipedia.org/wiki/Kab_101\" title=\"Kab 101\">Kab 101</a> oil platform to collide with a wellhead, leading to the death and drowning of 22 people during rescue operations after evacuation of the platform.", "links": [{"title": "Kab 101", "link": "https://wikipedia.org/wiki/Kab_101"}]}, {"year": "2007", "text": "Space Shuttle Discovery is launched on STS-120, with <PERSON> becoming the second female space shuttle commander.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-120\" title=\"STS-120\">STS-120</a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becoming the second female space shuttle commander.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-120\" title=\"STS-120\">STS-120</a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becoming the second female space shuttle commander.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-120", "link": "https://wikipedia.org/wiki/STS-120"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "A powerful 7.2 magnitude earthquake strikes Van Province, Turkey, killing 582 people and injuring thousands.", "html": "2011 - A powerful 7.2 magnitude <a href=\"https://wikipedia.org/wiki/2011_Van_earthquakes\" title=\"2011 Van earthquakes\">earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Van_Province\" title=\"Van Province\">Van Province, Turkey</a>, killing 582 people and injuring thousands.", "no_year_html": "A powerful 7.2 magnitude <a href=\"https://wikipedia.org/wiki/2011_Van_earthquakes\" title=\"2011 Van earthquakes\">earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Van_Province\" title=\"Van Province\">Van Province, Turkey</a>, killing 582 people and injuring thousands.", "links": [{"title": "2011 Van earthquakes", "link": "https://wikipedia.org/wiki/2011_Van_earthquakes"}, {"title": "Van Province", "link": "https://wikipedia.org/wiki/Van_Province"}]}, {"year": "2011", "text": "The Libyan National Transitional Council deems the Libyan Civil War over.", "html": "2011 - The Libyan <a href=\"https://wikipedia.org/wiki/National_Transitional_Council\" title=\"National Transitional Council\">National Transitional Council</a> deems the <a href=\"https://wikipedia.org/wiki/Libyan_civil_war_(2011)\" title=\"Libyan civil war (2011)\">Libyan Civil War</a> over.", "no_year_html": "The Libyan <a href=\"https://wikipedia.org/wiki/National_Transitional_Council\" title=\"National Transitional Council\">National Transitional Council</a> deems the <a href=\"https://wikipedia.org/wiki/Libyan_civil_war_(2011)\" title=\"Libyan civil war (2011)\">Libyan Civil War</a> over.", "links": [{"title": "National Transitional Council", "link": "https://wikipedia.org/wiki/National_Transitional_Council"}, {"title": "Libyan civil war (2011)", "link": "https://wikipedia.org/wiki/Libyan_civil_war_(2011)"}]}, {"year": "2015", "text": "The lowest sea-level pressure in the Western Hemisphere, and the highest reliably-measured non-tornadic sustained winds, are recorded in Hurricane <PERSON>, which strikes Mexico hours later, killing at least 13 and causing over $280 million in damages.", "html": "2015 - The lowest <a href=\"https://wikipedia.org/wiki/Sea-level_pressure\" class=\"mw-redirect\" title=\"Sea-level pressure\">sea-level pressure</a> in the <a href=\"https://wikipedia.org/wiki/Western_Hemisphere\" title=\"Western Hemisphere\">Western Hemisphere</a>, and the highest reliably-measured non-tornadic sustained winds, are recorded in <a href=\"https://wikipedia.org/wiki/Hurricane_Patricia\" title=\"Hurricane Patricia\">Hurricane Patricia</a>, which strikes Mexico hours later, killing at least 13 and causing over $280 million in damages.", "no_year_html": "The lowest <a href=\"https://wikipedia.org/wiki/Sea-level_pressure\" class=\"mw-redirect\" title=\"Sea-level pressure\">sea-level pressure</a> in the <a href=\"https://wikipedia.org/wiki/Western_Hemisphere\" title=\"Western Hemisphere\">Western Hemisphere</a>, and the highest reliably-measured non-tornadic sustained winds, are recorded in <a href=\"https://wikipedia.org/wiki/Hurricane_Patricia\" title=\"Hurricane Patricia\">Hurricane Patricia</a>, which strikes Mexico hours later, killing at least 13 and causing over $280 million in damages.", "links": [{"title": "Sea-level pressure", "link": "https://wikipedia.org/wiki/Sea-level_pressure"}, {"title": "Western Hemisphere", "link": "https://wikipedia.org/wiki/Western_Hemisphere"}, {"title": "Hurricane Patricia", "link": "https://wikipedia.org/wiki/Hurricane_Patricia"}]}, {"year": "2017", "text": "War against the Islamic State: Philippine defense secretary <PERSON><PERSON> declares the end of the Siege of Marawi.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/War_against_the_Islamic_State\" title=\"War against the Islamic State\">War against the Islamic State</a>: <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippine</a> defense secretary <a href=\"https://wikipedia.org/wiki/Delfin_Lorenzana\" title=\"Delfin Lorenzana\"><PERSON><PERSON> Loren<PERSON></a> declares the end of the <a href=\"https://wikipedia.org/wiki/Siege_of_Marawi\" title=\"Siege of Marawi\">Siege of Marawi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_against_the_Islamic_State\" title=\"War against the Islamic State\">War against the Islamic State</a>: <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippine</a> defense secretary <a href=\"https://wikipedia.org/wiki/Delfin_Lorenzana\" title=\"Delfin Lorenzana\"><PERSON><PERSON> Loren<PERSON></a> declares the end of the <a href=\"https://wikipedia.org/wiki/Siege_of_Marawi\" title=\"Siege of Marawi\">Siege of Marawi</a>.", "links": [{"title": "War against the Islamic State", "link": "https://wikipedia.org/wiki/War_against_the_Islamic_State"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Delfin_<PERSON>zana"}, {"title": "Siege of Marawi", "link": "https://wikipedia.org/wiki/Siege_of_Marawi"}]}, {"year": "2020", "text": "Second Libyan Civil War: The Second Libyan Civil War comes to an end as all parties to the 5+5 Joint Libyan Military Commission agree to a ceasefire.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Libyan_civil_war_(2014%E2%80%932020)\" title=\"Libyan civil war (2014-2020)\">Second Libyan Civil War</a>: The Second Libyan Civil War comes to an end as all parties to the 5+5 Joint Libyan Military Commission agree to a ceasefire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Libyan_civil_war_(2014%E2%80%932020)\" title=\"Libyan civil war (2014-2020)\">Second Libyan Civil War</a>: The Second Libyan Civil War comes to an end as all parties to the 5+5 Joint Libyan Military Commission agree to a ceasefire.", "links": [{"title": "Libyan civil war (2014-2020)", "link": "https://wikipedia.org/wiki/Libyan_civil_war_(2014%E2%80%932020)"}]}, {"year": "2022", "text": "<PERSON> is elected as General Secretary of the Chinese Communist Party by the Central Committee, beginning a third term of the paramount leader of China.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Xi_Jinping\" title=\"Xi <PERSON>ping\"><PERSON></a> is elected as <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> by the <a href=\"https://wikipedia.org/wiki/Central_Committee_of_the_Chinese_Communist_Party\" title=\"Central Committee of the Chinese Communist Party\">Central Committee</a>, beginning a third term of the <a href=\"https://wikipedia.org/wiki/Paramount_leader\" title=\"Paramount leader\">paramount leader</a> of <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xi_Jinping\" title=\"Xi Jinping\"><PERSON></a> is elected as <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> by the <a href=\"https://wikipedia.org/wiki/Central_Committee_of_the_Chinese_Communist_Party\" title=\"Central Committee of the Chinese Communist Party\">Central Committee</a>, beginning a third term of the <a href=\"https://wikipedia.org/wiki/Paramount_leader\" title=\"Paramount leader\">paramount leader</a> of <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>.", "links": [{"title": "Xi <PERSON>ping", "link": "https://wikipedia.org/wiki/Xi_<PERSON>"}, {"title": "General Secretary of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party"}, {"title": "Central Committee of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/Central_Committee_of_the_Chinese_Communist_Party"}, {"title": "Paramount leader", "link": "https://wikipedia.org/wiki/Paramount_leader"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}]}, {"year": "2022", "text": "Myanmar Air Force airstrikes a concert in Hpakant Township, Kachin state killing at least 80 people, including senior Kachin Independence Organisation officials, in the Hpakant massacre.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Myanmar_Air_Force\" title=\"Myanmar Air Force\">Myanmar Air Force</a> airstrikes a concert in <a href=\"https://wikipedia.org/wiki/Hpakant_Township\" title=\"Hpakant Township\">Hpakant Township</a>, Kachin state killing at least 80 people, including senior <a href=\"https://wikipedia.org/wiki/Kachin_Independence_Organisation\" title=\"Kachin Independence Organisation\">Kachin Independence Organisation</a> officials, in the <a href=\"https://wikipedia.org/wiki/Hpakant_massacre\" title=\"Hpakant massacre\">Hpakant massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Myanmar_Air_Force\" title=\"Myanmar Air Force\">Myanmar Air Force</a> airstrikes a concert in <a href=\"https://wikipedia.org/wiki/Hpakant_Township\" title=\"Hpakant Township\">Hpakant Township</a>, Kachin state killing at least 80 people, including senior <a href=\"https://wikipedia.org/wiki/Kachin_Independence_Organisation\" title=\"Kachin Independence Organisation\">Kachin Independence Organisation</a> officials, in the <a href=\"https://wikipedia.org/wiki/Hpakant_massacre\" title=\"Hpakant massacre\">Hpakant massacre</a>.", "links": [{"title": "Myanmar Air Force", "link": "https://wikipedia.org/wiki/Myanmar_Air_Force"}, {"title": "Hpakant Township", "link": "https://wikipedia.org/wiki/Hpakant_Township"}, {"title": "Kachin Independence Organisation", "link": "https://wikipedia.org/wiki/Kachin_Independence_Organisation"}, {"title": "Hpakant massacre", "link": "https://wikipedia.org/wiki/Hpakant_massacre"}]}], "Births": [{"year": "1006", "text": "<PERSON>, Chinese grand chancellor (d. 1097)", "html": "1006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Song_dynasty)\" title=\"<PERSON> (Song dynasty)\"><PERSON></a>, Chinese grand chancellor (d. 1097)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Song_dynasty)\" title=\"<PERSON> (Song dynasty)\"><PERSON></a>, Chinese grand chancellor (d. 1097)", "links": [{"title": "<PERSON> (Song dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Song_dynasty)"}]}, {"year": "1255", "text": "<PERSON>, Spanish noble (d. 1275)", "html": "1255 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish noble (d. 1275)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>rda\"><PERSON></a>, Spanish noble (d. 1275)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1491 (estimated)", "text": "<PERSON><PERSON><PERSON> <PERSON> Loyola, Catholic priest (d. 1556)", "html": "1491 (estimated) - <a href=\"https://wikipedia.org/wiki/1491\" title=\"1491\">1491</a> (estimated) - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_of_Loyola\" title=\"<PERSON><PERSON>tius of Loyola\"><PERSON><PERSON><PERSON> of Loyola</a>, Catholic priest (d. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1491\" title=\"1491\">1491</a> (estimated) - <a href=\"https://wikipedia.org/wiki/I<PERSON>tius_of_Loyola\" title=\"<PERSON><PERSON><PERSON> of Loyola\"><PERSON><PERSON><PERSON> of Loyola</a>, Catholic priest (d. 1556)", "links": [{"title": "1491", "link": "https://wikipedia.org/wiki/1491"}, {"title": "<PERSON><PERSON><PERSON> of Loyola", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Loyola"}]}, {"year": "1516", "text": "<PERSON> of Valois, French princess (d. 1524)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Valois\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, French princess (d. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Valois\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, French princess (d. 1524)", "links": [{"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois"}]}, {"year": "1634", "text": "<PERSON><PERSON><PERSON> of Holstein-Gottorp, Swedish queen (d. 1715)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON><PERSON><PERSON>_of_Holstein-Gottorp\" title=\"<PERSON><PERSON><PERSON> of Holstein-Gottorp\"><PERSON><PERSON><PERSON> of Holstein-Gottorp</a>, Swedish queen (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON><PERSON><PERSON>_of_Holstein-Gottorp\" title=\"<PERSON><PERSON><PERSON> of Holstein-Gottorp\"><PERSON><PERSON><PERSON> of Holstein-Gottorp</a>, Swedish queen (d. 1715)", "links": [{"title": "<PERSON><PERSON><PERSON> of Holstein-Gottorp", "link": "https://wikipedia.org/wiki/Hed<PERSON>_<PERSON><PERSON><PERSON>_of_Holstein-<PERSON>"}]}, {"year": "1654", "text": "<PERSON>, Austrian composer (d. 1712)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1698", "text": "<PERSON><PERSON><PERSON><PERSON>, French architect, designed the École Militaire (d. 1782)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French architect, designed the <a href=\"https://wikipedia.org/wiki/%C3%89cole_Militaire\" class=\"mw-redirect\" title=\"École Militaire\">École Militaire</a> (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French architect, designed the <a href=\"https://wikipedia.org/wiki/%C3%89cole_Militaire\" class=\"mw-redirect\" title=\"École Militaire\">École Militaire</a> (d. 1782)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}, {"title": "École Militaire", "link": "https://wikipedia.org/wiki/%C3%89cole_Militaire"}]}, {"year": "1705", "text": "<PERSON>, Austrian field marshal (d. 1757)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON> the Younger, Dutch philologist, poet, and educator (d. 1778)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Dutch philologist, poet, and educator (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Dutch philologist, poet, and educator (d. 1778)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, American engineer (d. 1843)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, <PERSON>, French general (d. 1847)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, French general (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, French general (d. 1847)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON><PERSON><PERSON>, American minister, lexicographer, and educator (d. 1860)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American minister, lexicographer, and educator (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American minister, lexicographer, and educator (d. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, Swiss statistician and politician (d. 1857)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss statistician and politician (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss statistician and politician (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, German singer-songwriter and actor (d. 1851)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and actor (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and actor (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, American linguist and historian (d. 1886)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and historian (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and historian (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, German-Australian explorer (d. 1848)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian explorer (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian explorer (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Baron of Cotejipe, Brazilian politician (d. 1889)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Maur%C3%<PERSON><PERSON>_<PERSON>,_Baron_of_Cotejipe\" class=\"mw-redirect\" title=\"<PERSON>, Baron of Cotejipe\"><PERSON>, Baron of Cotejipe</a>, Brazilian politician (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Maur%C3%<PERSON><PERSON>_<PERSON>,_Baron_of_Cotejipe\" class=\"mw-redirect\" title=\"<PERSON>, Baron of Cotejipe\"><PERSON>, Baron of Cotejipe</a>, Brazilian politician (d. 1889)", "links": [{"title": "<PERSON>, Baron of Cotejipe", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>ur%C3%<PERSON><PERSON>_<PERSON>,_Baron_of_Cotejipe"}]}, {"year": "1817", "text": "<PERSON>, French lexicographer and author (d. 1875)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lexicographer and author (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lexicographer and author (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, German astronomer (d. 1895)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sp%C3%B6rer\" title=\"<PERSON>\"><PERSON></a>, German astronomer (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>p%C3%B6rer\" title=\"<PERSON>\"><PERSON></a>, German astronomer (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gustav_Sp%C3%B6rer"}]}, {"year": "1832", "text": "<PERSON>, Finnish priest and father of <PERSON><PERSON> <PERSON><PERSON>, the first President of Finland (d. 1873)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON>\"><PERSON></a>, Finnish priest and father of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON><PERSON><PERSON>\">K. J<PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON>\"><PERSON></a>, Finnish priest and father of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A5<PERSON>berg"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>_Juho_St%C3%A5hlberg"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1835", "text": "<PERSON><PERSON>, American lawyer and politician, 23rd Vice President of the United States (d. 1914)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> I</a>, American lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> I</a>, American lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1837", "text": "<PERSON><PERSON>, Hungarian dermatologist (d. 1902)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian dermatologist (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian dermatologist (d. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, French actress (d. 1923)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, English poet and playwright (d. 1930)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Filipino painter and sculptor (d. 1899)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Juan Luna\"><PERSON></a>, Filipino painter and sculptor (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Juan Luna\"><PERSON></a>, Filipino painter and sculptor (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON><PERSON>, Croatian writer, bibliographer, antiquarian, and one of the notable alleged and false victims of the Stara Gradiška concentration camp (d. 1946)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian writer, bibliographer, antiquarian, and one of the notable alleged and false victims of the <a href=\"https://wikipedia.org/wiki/Stara_Gradi%C5%A1ka_concentration_camp\" title=\"Stara Gradiška concentration camp\">Stara Gradiška concentration camp</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian writer, bibliographer, antiquarian, and one of the notable alleged and false victims of the <a href=\"https://wikipedia.org/wiki/Stara_Gradi%C5%A1ka_concentration_camp\" title=\"Stara Gradiška concentration camp\">Stara Gradiška concentration camp</a> (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Stara Gradiška concentration camp", "link": "https://wikipedia.org/wiki/Stara_Gradi%C5%A1ka_concentration_camp"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON><PERSON>, American historian and author (d. 1918)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American historian and author (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American historian and author (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American football player and coach (d. 1936)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Canadian-American bishop (d. 1948)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American bishop (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American bishop (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, American physicist and engineer (d. 1975)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American runner and educator (d. 1921)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and educator (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and educator (d. 1921)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1875", "text": "<PERSON>, American chemist and academic (d. 1946)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, German judge and politician, Reich Ministry of Justice (d. 1970)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German judge and politician, <a href=\"https://wikipedia.org/wiki/Reich_Ministry_of_Justice\" title=\"Reich Ministry of Justice\">Reich Ministry of Justice</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German judge and politician, <a href=\"https://wikipedia.org/wiki/Reich_Ministry_of_Justice\" title=\"Reich Ministry of Justice\">Reich Ministry of Justice</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reich Ministry of Justice", "link": "https://wikipedia.org/wiki/Reich_Ministry_of_Justice"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Estonian pastor and politician, 9th Estonian Minister of Foreign Affairs (d. 1967)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pastor and politician, 9th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pastor and politician, 9th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a> (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, German architect (d. 1955)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Dominikus_B%C3%B6hm\" title=\"Domini<PERSON> Böhm\"><PERSON><PERSON><PERSON></a>, German architect (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dominikus_B%C3%B6hm\" title=\"Domini<PERSON> Böhm\"><PERSON><PERSON><PERSON></a>, German architect (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dominikus_B%C3%B6hm"}]}, {"year": "1880", "text": "<PERSON>, Irish-American actress and singer (d. 1959)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Irish-American actress and singer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Irish-American actress and singer (d. 1959)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actress)"}]}, {"year": "1883", "text": "<PERSON>, Argentine writer (d. 1962)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine writer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine writer (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Canadian painter and educator (d. 1970)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter and educator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter and educator (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Canadian scholar and politician, 20th Lieutenant Governor of Quebec (d. 1961)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/On%C3%A9sime_<PERSON>\" title=\"Onés<PERSON> Gagnon\"><PERSON><PERSON><PERSON></a>, Canadian scholar and politician, 20th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/On%C3%A9sime_<PERSON>\" title=\"Onés<PERSON> Gagnon\"><PERSON><PERSON><PERSON></a>, Canadian scholar and politician, 20th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/On%C3%A9sime_<PERSON>non"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1892", "text": "Speckled <PERSON>, American blues/boogie-woogie piano player and singer-songwriter (d. 1973)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Speckled_Red\" title=\"Speckled Red\">Speckled Red</a>, American blues/boogie-woogie piano player and singer-songwriter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Speckled_Red\" title=\"Speckled Red\">Speckled Red</a>, American blues/boogie-woogie piano player and singer-songwriter (d. 1973)", "links": [{"title": "Speckled Red", "link": "https://wikipedia.org/wiki/Speckled_Red"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American baseball player (d. 1966)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ler\" title=\"<PERSON><PERSON> Bressler\"><PERSON><PERSON></a>, American baseball player (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bressler\"><PERSON><PERSON></a>, American baseball player (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American astronomer and academic (d. 1975)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Filipino politician (d. 1945)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lar<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, American actress (d. 1934)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English air marshal (d. 1978)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal (d. 1978)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>(RAF_officer)"}]}, {"year": "1897", "text": "<PERSON>, Spanish writer (d. 1975)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Marquis_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, 2nd Marquis <PERSON>\"><PERSON></a>, Spanish writer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_2nd_Marquis_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, 2nd Marquis <PERSON>\"><PERSON></a>, Spanish writer (d. 1975)", "links": [{"title": "<PERSON>, 2nd Marquis of Luca <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_2nd_Marquis_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Norwegian aviator (d. 1973)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Bernt_<PERSON>chen\" title=\"Bernt Balchen\"><PERSON><PERSON></a>, Norwegian aviator (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bernt_<PERSON>chen\" title=\"Bernt Balchen\"><PERSON><PERSON></a>, Norwegian aviator (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Balchen"}]}, {"year": "1900", "text": "<PERSON>, Indian-English cricketer and lawyer (d. 1958)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Douglas J<PERSON>ine\"><PERSON></a>, Indian-English cricketer and lawyer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Douglas J<PERSON>\"><PERSON></a>, Indian-English cricketer and lawyer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Douglas_J<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Austrian engineer (d. 1982)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian engineer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian engineer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American political scientist and politician (d. 1981)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and politician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and politician (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American golfer and coach (d. 1995)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Swiss physicist and academic, Nobel Prize laureate (d. 1983)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Chinese lawyer and politician, President of the Republic of China (d. 1993)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Yen_<PERSON><PERSON>-kan\" title=\"Yen <PERSON>-kan\">Ye<PERSON><PERSON></a>, Chinese lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yen_<PERSON><PERSON>-kan\" title=\"Yen <PERSON>-kan\">Ye<PERSON></a>, Chinese lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (d. 1993)", "links": [{"title": "Yen <PERSON>kan", "link": "https://wikipedia.org/wiki/Yen_<PERSON><PERSON>-kan"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1905", "text": "<PERSON>, American swimmer (d. 2003)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech shot putter (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Do<PERSON>\" title=\"Františ<PERSON> Do<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech shot putter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Do<PERSON>\" title=\"František Do<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech shot putter (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_Douda"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Russian physicist and academic, Nobel Prize laureate (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, American linguist and methodologist (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American linguist and methodologist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American linguist and methodologist (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Danish painter and educator (d. 1993)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and educator (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and educator (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actor (d. 1987)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American hurdler (d. 1978)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (d. 1978)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Finnish writer (d. 1967)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish writer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish writer (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actress (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Augusta_Dabney\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Dabney\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augusta_Da<PERSON>ney"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 1978)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1978)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1918", "text": "<PERSON>, American architect and academic, designed the Lippo Centre (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, American architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Lippo_Centre,_Hong_Kong\" class=\"mw-redirect\" title=\"Lippo Centre, Hong Kong\">Lippo Centre</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, American architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Lippo_Centre,_Hong_Kong\" class=\"mw-redirect\" title=\"Lippo Centre, Hong Kong\">Lippo Centre</a> (d. 1997)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>(architect)"}, {"title": "Lippo Centre, Hong Kong", "link": "https://wikipedia.org/wiki/Lippo_Centre,_Hong_Kong"}]}, {"year": "1919", "text": "Man<PERSON> Andronikos, Greek archaeologist and academic (d. 1992)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Manolis_Andronikos\" title=\"Manolis Andronikos\">Manolis Andronikos</a>, Greek archaeologist and academic (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manolis_Andronikos\" title=\"Manolis Andronikos\">Manolis Andronikos</a>, Greek archaeologist and academic (d. 1992)", "links": [{"title": "Manolis Andronikos", "link": "https://wikipedia.org/wiki/Manolis_Andronikos"}]}, {"year": "1920", "text": "<PERSON>, Japanese-American meteorologist and academic (d. 1998)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American meteorologist and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American meteorologist and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American illustrator (d. 1975)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Bob_<PERSON>\" title=\"Bob Montana\"><PERSON></a>, American illustrator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bob_<PERSON>\" title=\"Bob Montana\"><PERSON></a>, American illustrator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bob_Montana"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Italian writer (d. 1980)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian writer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian writer (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American baseball player (d. 1968)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Baroness <PERSON>, English politician (d. 2018)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness Trump<PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician (d. 2018)", "links": [{"title": "<PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American actress (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>en_Gray"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Indian-Pakistani linguist, author, and scholar (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Farruk<PERSON>\" title=\"<PERSON><PERSON> Farrukhi\"><PERSON><PERSON></a>, Indian-Pakistani linguist, author, and scholar (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Farruk<PERSON>\" title=\"<PERSON><PERSON> Farrukhi\"><PERSON><PERSON></a>, Indian-Pakistani linguist, author, and scholar (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hi"}]}, {"year": "1923", "text": "<PERSON>, American composer and author (d. 2022)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ned_R<PERSON>m"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 1974)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English journalist (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian-American ice hockey player and coach (d. 1990)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American saxophonist and composer (d. 1977)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian water polo player and coach (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Gyarmati\" title=\"<PERSON><PERSON><PERSON><PERSON> Gyarmati\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian water polo player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Gyarmati\" title=\"<PERSON><PERSON><PERSON><PERSON> Gyarmat<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian water polo player and coach (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dezs%C5%91_Gyarmati"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Polish-English historian and philosopher (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>k_Ko%C5%82akowski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English historian and philosopher (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>k_Ko%C5%82akowski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English historian and philosopher (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leszek_Ko%C5%82akowski"}]}, {"year": "1929", "text": "<PERSON>, Chilean actor (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Chilean actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Chilean actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Alarc%C3%B3n"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi poet and journalist (d. 2006)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)\" title=\"<PERSON><PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi poet and journalist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)\" title=\"<PERSON><PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi poet and journalist (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Finnish musician (d. 1968)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Unto_Mononen\" title=\"Unto Mononen\"><PERSON><PERSON></a>, Finnish musician (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Unto_Mononen\" title=\"Unto Mononen\"><PERSON><PERSON></a>, Finnish musician (d. 1968)", "links": [{"title": "Unto Mononen", "link": "https://wikipedia.org/wiki/Unto_Mononen"}]}, {"year": "1931", "text": "<PERSON>, American baseball player and politician (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and politician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Jr., American judge and politician, 12th United States National Security Advisor (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American judge and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_National_Security_Advisor\" class=\"mw-redirect\" title=\"United States National Security Advisor\">United States National Security Advisor</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American judge and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_National_Security_Advisor\" class=\"mw-redirect\" title=\"United States National Security Advisor\">United States National Security Advisor</a> (d. 2013)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}, {"title": "United States National Security Advisor", "link": "https://wikipedia.org/wiki/United_States_National_Security_Advisor"}]}, {"year": "1931", "text": "<PERSON>, English actress (d. 1984)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Japanese businessman, and talent manager (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese businessman, and talent manager (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese businessman, and talent manager (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Russian novelist, poet and playwright (d. 2012)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian novelist, poet and playwright (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian novelist, poet and playwright (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and pianist (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, sixth Vice President of Columbia (d. 2003)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simmond<PERSON>\" title=\"<PERSON> Simmond<PERSON>\"><PERSON></a>, sixth <a href=\"https://wikipedia.org/wiki/Vice_President_of_Colombia\" title=\"Vice President of Colombia\">Vice President of Columbia</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Simmond<PERSON>\" title=\"<PERSON>mmond<PERSON>\"><PERSON>mm<PERSON></a>, sixth <a href=\"https://wikipedia.org/wiki/Vice_President_of_Colombia\" title=\"Vice President of Colombia\">Vice President of Columbia</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>mmond<PERSON>"}, {"title": "Vice President of Colombia", "link": "https://wikipedia.org/wiki/Vice_President_of_Colombia"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Afro-Peruvian musician (d. 2004)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Afro-Peruvian musician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Afro-Peruvian musician (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, American neuroscientist  (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American neuroscientist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American neuroscientist (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English economist and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American rockabilly musician (d. 1995)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rockabilly musician (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rockabilly musician (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Brazilian captain (d. 1971)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian captain (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian captain (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Indian actor, director, and producer (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ma\" title=\"Deven Verma\"><PERSON><PERSON></a>, Indian actor, director, and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ma\" title=\"Deven Verma\"><PERSON><PERSON></a>, Indian actor, director, and producer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Verma"}]}, {"year": "1938", "text": "<PERSON>, Scottish footballer and manager (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American R&B/soul singer and guitarist (d. 1998)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON></a>, American R&amp;B/soul singer and guitarist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON></a>, American R&amp;B/soul singer and guitarist (d. 1998)", "links": [{"title": "<PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer, judge, and politician, 1st Chief Minister of the Northern Province", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON><PERSON>waran\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer, judge, and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_the_Northern_Province\" class=\"mw-redirect\" title=\"Chief Minister of the Northern Province\">Chief Minister of the Northern Province</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>waran\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer, judge, and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_the_Northern_Province\" class=\"mw-redirect\" title=\"Chief Minister of the Northern Province\">Chief Minister of the Northern Province</a>", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Chief Minister of the Northern Province", "link": "https://wikipedia.org/wiki/Chief_Minister_of_the_Northern_Province"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter and producer (d. 2009)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American model, actress, producer, and art collector", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, producer, and art collector", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, producer, and art collector", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and actor (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Pel%C3%A9\" title=\"<PERSON>el<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pel%C3%A9\" title=\"Pel<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and actor (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pel%C3%A9"}]}, {"year": "1941", "text": "<PERSON>, French rally driver (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rally driver (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rally driver (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Metge"}]}, {"year": "1941", "text": "<PERSON>, English cricketer (d. 1990)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Moldovan engineer and politician, 1st President of Transnistria", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Moldovan engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Transnistria\" title=\"President of Transnistria\">President of Transnistria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Moldovan engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Transnistria\" title=\"President of Transnistria\">President of Transnistria</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "President of Transnistria", "link": "https://wikipedia.org/wiki/President_of_Transnistria"}]}, {"year": "1942", "text": "<PERSON>, Scottish poet, critic, and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet, critic, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet, critic, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English businesswoman and activist, founded The Body Shop (d. 2007)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and activist, founded <a href=\"https://wikipedia.org/wiki/The_Body_Shop\" title=\"The Body Shop\">The Body Shop</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and activist, founded <a href=\"https://wikipedia.org/wiki/The_Body_Shop\" title=\"The Body Shop\">The Body Shop</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Body Shop", "link": "https://wikipedia.org/wiki/The_Body_Shop"}]}, {"year": "1942", "text": "<PERSON>, American author and screenwriter (d. 2008)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Italian actress and singer (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress and singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress and singer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and comedian", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, English sculptor and painter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English sculptor and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English sculptor and painter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Danish singer-songwriter and guitarist (d. 2018)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish singer-songwriter and guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish singer-songwriter and guitarist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Mozambican politician and humanitarian", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Gra%C3%A7a_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mozambican politician and humanitarian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gra%C3%A7a_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mozambican politician and humanitarian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gra%C3%A7a_<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American saxophonist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American composer, lyricist, and music theorist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer, lyricist, and music theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer, lyricist, and music theorist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English archaeologist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Argentine writer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American lawyer and politician, 12th United States Secretary of Housing and Urban Development", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Mel_Mart%C3%ADnez\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mel_Mart%C3%ADnez\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mel_Mart%C3%ADnez"}, {"title": "United States Secretary of Housing and Urban Development", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian javelin thrower", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_N%C3%A9meth_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_N%C3%A9meth_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_N%C3%A9meth_(athlete)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, co-founder of the Palestinian movement Hamas (d. 2004)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, co-founder of the <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> movement <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, co-founder of the <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> movement <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}, {"title": "Hamas", "link": "https://wikipedia.org/wiki/Hamas"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer (d. 1989)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English bass player (d. 2003)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Austrian-English businessman, co-founded Acorn Computers and Olivetti Research Laboratory", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Acorn_Computers\" title=\"Acorn Computers\">Acorn Computers</a> and <a href=\"https://wikipedia.org/wiki/Olivetti_Research_Laboratory\" title=\"Olivetti Research Laboratory\">Olivetti Research Laboratory</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Acorn_Computers\" title=\"Acorn Computers\">Acorn Computers</a> and <a href=\"https://wikipedia.org/wiki/Olivetti_Research_Laboratory\" title=\"Olivetti Research Laboratory\">Olivetti Research Laboratory</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Acorn Computers", "link": "https://wikipedia.org/wiki/Acorn_Computers"}, {"title": "Olivetti Research Laboratory", "link": "https://wikipedia.org/wiki/Olivetti_Research_Laboratory"}]}, {"year": "1948", "text": "<PERSON>, Irish-born British businessman, arts patron and television personality (d. 2021)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born British businessman, arts patron and television personality (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born British businessman, arts patron and television personality (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American journalist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Spanish musician (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9s\" title=\"<PERSON><PERSON> Sabatés\"><PERSON><PERSON></a>, Spanish musician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9s\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish musician (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo<PERSON>_<PERSON>bat%C3%A9s"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Croatian soldier and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Kre%C5%<PERSON><PERSON><PERSON>_%C4%86osi%C4%87_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kre%C5%<PERSON><PERSON><PERSON>_%C4%86osi%C4%87_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> soldier and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/Kre%C5%<PERSON><PERSON><PERSON>_%C4%86osi%C4%87_(politician)"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}]}, {"year": "1949", "text": "<PERSON>, Argentine theater actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Oscar_Mart%C3%<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Argentine theater actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_Mart%C3%<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Argentine theater actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/Oscar_Mart%C3%<PERSON><PERSON><PERSON>_(actor)"}]}, {"year": "1949", "text": "<PERSON>, American journalist, author, and poet (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and poet (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and poet (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, English singer and guitarist (d. 2011)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/W%C3%BCrz<PERSON>\" title=\"Würz<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English singer and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C3%BCrzel\" title=\"Würz<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English singer and guitarist (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C3%BCrzel"}]}, {"year": "1950", "text": "<PERSON><PERSON> <PERSON><PERSON>, Swedish businessman (d. 2012)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swedish businessman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swedish businessman (d. 2012)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Spanish actor (d. 2016)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_de_Andr%C3%A9s_L%C3%B3pez\" title=\"<PERSON>\"><PERSON></a>, Spanish actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ngel_de_Andr%C3%A9s_L%C3%B3pez\" title=\"<PERSON>\"><PERSON></a>, Spanish actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_de_Andr%C3%A9s_L%C3%B3pez"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Argentine singer-songwriter and keyboard player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Cha<PERSON>_Garc%C3%ADa\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Argentine singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha<PERSON>_Garc%C3%ADa\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Argentine singer-songwriter and keyboard player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Charly_Garc%C3%ADa"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Kosovan academic and politician, 2nd President of Kosovo", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kosovan academic and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Kosovo\" title=\"President of Kosovo\">President of Kosovo</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kosovan academic and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Kosovo\" title=\"President of Kosovo\">President of Kosovo</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}, {"title": "President of Kosovo", "link": "https://wikipedia.org/wiki/President_of_Kosovo"}]}, {"year": "1951", "text": "<PERSON>, American country music singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country music singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country music singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1952", "text": "<PERSON>, French drummer (d. 2005)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French drummer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French drummer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Turkish sociologist and historian", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Taner_Ak%C3%A7am\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish sociologist and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taner_Ak%C3%A7am\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish sociologist and historian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taner_Ak%C3%A7am"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Chilean politician and economist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Lav%C3%ADn\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean politician and economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Lav%C3%ADn\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean politician and economist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_Lav%C3%ADn"}]}, {"year": "1953", "text": "<PERSON>, English singer, actress and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, English singer, actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Taiwanese-American director, producer, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Polish football player and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%82ka\" title=\"<PERSON>\"><PERSON></a>, Polish football player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%82ka\" title=\"<PERSON>\"><PERSON></a>, Polish football player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_Nawa%C5%82ka"}]}, {"year": "1956", "text": "<PERSON>, American archer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pace\"><PERSON></a>, American archer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American singer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Rwandan soldier and politician, 6th President of Rwanda", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rwandan soldier and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Rwanda\" class=\"mw-redirect\" title=\"President of Rwanda\">President of Rwanda</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rwandan soldier and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Rwanda\" class=\"mw-redirect\" title=\"President of Rwanda\">President of Rwanda</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Rwanda", "link": "https://wikipedia.org/wiki/President_of_Rwanda"}]}, {"year": "1957", "text": "<PERSON>, English footballer and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rix\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Graham Rix\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Graham_R<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American human rights activist, philanthropist and advocate", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American human rights activist, philanthropist and advocate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American human rights activist, philanthropist and advocate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American activist, author, and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, German singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, German sprinter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "\"Weird Al\" <PERSON>, American comedy musician, writer, and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/%22Weird_<PERSON>%22_<PERSON><PERSON>\" title='\"Weird Al\" <PERSON><PERSON>'>\"Weird Al\" <PERSON></a>, American comedy musician, writer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%22Weird_Al%22_<PERSON><PERSON>\" title='\"Weird Al\" <PERSON>kovic'>\"Weird Al\" <PERSON><PERSON></a>, American comedy musician, writer, and actor", "links": [{"title": "\"Weird Al\" <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%22Weird_Al%22_<PERSON><PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American director, screenwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Swiss-French keyboard player, songwriter, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-French keyboard player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-French keyboard player, songwriter, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AF"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, French model and actress (d. 2008)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model and actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model and actress (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author and academic (d. 2008)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American motorcycle racer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Guatemalan politician (d. 2008)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Vinicio_G%C3%B3mez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guatemalan politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vinicio_G%C3%B3mez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guatemalan politician (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vinicio_G%C3%B3mez"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Spanish footballer and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/And<PERSON>_Zubizarreta\" title=\"<PERSON><PERSON> Zubizarreta\"><PERSON><PERSON></a>, Spanish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/And<PERSON>_Zubizarreta\" title=\"<PERSON><PERSON> Zubizarreta\"><PERSON><PERSON></a>, Spanish footballer and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian-American author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Nigerian footballer (d. 2012)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American computer scientist and businessman", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American bass player and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American author and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "Al Leiter", "link": "https://wikipedia.org/wiki/Al_Leiter"}]}, {"year": "1966", "text": "<PERSON>, Italian racing driver and cyclist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver and cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver and cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American musician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Cuban baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Peruvian tennis player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Czech film producer and director, actress and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Buster\"><PERSON></a>, Czech film producer and director, actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Buster\" title=\"<PERSON> Buster\"><PERSON></a>, Czech film producer and director, actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Australian author and illustrator", "html": "1969 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian author and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American football player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien_(American_football)"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American diplomat, United States Ambassador to the United Kingdom", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "1970", "text": "<PERSON>, American television presenter and engineer (d. 2020)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television presenter and engineer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television presenter and engineer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Grant_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Japanese voice actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American model and photographer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian-Japanese composer and scholar", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Japanese composer and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Japanese composer and scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American cyclist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, American soccer player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Tiff<PERSON><PERSON>_<PERSON>\" title=\"Tiff<PERSON><PERSON>\">T<PERSON><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiff<PERSON><PERSON>_<PERSON>\" title=\"Tiff<PERSON><PERSON>\">T<PERSON><PERSON><PERSON></a>, American soccer player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiffen<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Polish-Mexican actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Palet<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Mexican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Palet<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Mexican actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dominika_Paleta"}]}, {"year": "1972", "text": "<PERSON>, Cuban baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American lawyer and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Virgin Islander-American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jas<PERSON>_St._Claire\" title=\"Jasmin St. Claire\">J<PERSON><PERSON> St. Claire</a>, Virgin Islander-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jas<PERSON>_<PERSON>._Claire\" title=\"Jasmin St. Claire\"><PERSON><PERSON><PERSON> St. Claire</a>, Virgin Islander-American actress", "links": [{"title": "<PERSON><PERSON>min St. Claire", "link": "https://wikipedia.org/wiki/Jas<PERSON>_St._Claire"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Scottish footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Indian journalist and author", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ara<PERSON><PERSON>_<PERSON>\" title=\"Ara<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ara<PERSON><PERSON>_<PERSON>\" title=\"Ara<PERSON><PERSON> Ad<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author", "links": [{"title": "Aravind Adiga", "link": "https://wikipedia.org/wiki/Aravind_Adiga"}]}, {"year": "1974", "text": "<PERSON>, New Zealand discus thrower", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand discus thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Westerveld\" title=\"<PERSON><PERSON> Westerveld\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Westerveld\" title=\"Sand<PERSON> Westerveld\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Westerveld"}]}, {"year": "1974", "text": "<PERSON>, Canadian pianist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Cuban actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Odalys_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odalys_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odalys_Garc%C3%ADa"}]}, {"year": "1975", "text": "<PERSON>, Australian cricket umpire", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricket umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricket umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}]}, {"year": "1975", "text": "<PERSON><PERSON>, South Korean actress and singer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> Son-ha\"><PERSON><PERSON></a>, South Korean actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-ha\"><PERSON><PERSON></a>, South Korean actress and singer", "links": [{"title": "<PERSON><PERSON>-ha", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ha"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Spanish actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English model, actress, and television host", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model, actress, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model, actress, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Argentine footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sergio <PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian-American actor and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English cricketer and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English cricketer and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, New Zealand-Australian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Venezuelan baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_<PERSON>_(third_baseman)\" title=\"<PERSON> (third baseman)\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_<PERSON>_(third_baseman)\" title=\"<PERSON> (third baseman)\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON> (third baseman)", "link": "https://wikipedia.org/wiki/Ram%C3%B3<PERSON>_<PERSON>_(third_baseman)"}]}, {"year": "1979", "text": "<PERSON>, Welsh footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON> (footballer, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Telugu film actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Telugu film actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Telugu film actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rabhas"}]}, {"year": "1979", "text": "<PERSON>, Mexican boxer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs\" title=\"<PERSON>\"><PERSON></a>, Mexican boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs\" title=\"<PERSON>\"><PERSON></a>, Mexican boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jorge_Sol%C3%ADs"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mate_Bili%C4%87"}]}, {"year": "1980", "text": "<PERSON>, Dominican baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Venezuelan actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Dutch racing driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>len"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Spanish actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Letici<PERSON>_<PERSON>a\" title=\"Letici<PERSON> Dolera\"><PERSON><PERSON><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Let<PERSON><PERSON>_<PERSON>\" title=\"Letici<PERSON> Doler<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Letici<PERSON>_<PERSON><PERSON>a"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Ben_<PERSON>\" title=\"Ben Francisco\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ben_Francisco\" title=\"Ben Francisco\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ben_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo\" title=\"<PERSON>woo\"><PERSON>oo</a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo\" title=\"<PERSON>woo\"><PERSON>oo</a>, South Korean actor", "links": [{"title": "<PERSON>woo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Romanian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_R%C4%83doi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_R%C4%83doi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mirel_R%C4%83doi"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Romanian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Estonian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Aleksandar_Lukovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_Lukovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksandar_Lukovi%C4%87"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1982)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1982)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1982)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1982)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1982)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Ukrainian-born Azerbaijani canoeist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-born Azerbaijani canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-born Azerbaijani canoeist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Nigerian singer-songwriter (d. 2013)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian singer-songwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Brazilian model", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Go<PERSON>t\" title=\"<PERSON>za<PERSON> Goulart\"><PERSON><PERSON><PERSON></a>, Brazilian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>za<PERSON> Goulart\"><PERSON><PERSON><PERSON></a>, Brazilian model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}]}, {"year": "1984", "text": "<PERSON>, Dutch footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American journalist, author, and television personality", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, author, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, author, and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian golfer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Norwegian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Albanian-American actress, poet, and humanitarian", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian-American actress, poet, and humanitarian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian-American actress, poet, and humanitarian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American singer-songwriter and producer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>agiot<PERSON>_<PERSON>\" title=\"Panagiot<PERSON> V<PERSON>is\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>agiot<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress and dancer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Israeli actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Inbar_<PERSON><PERSON>\" title=\"Inbar Lavi\"><PERSON><PERSON></a>, Israeli actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inbar_<PERSON><PERSON>\" title=\"Inbar Lavi\"><PERSON><PERSON></a>, Israeli actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inbar_<PERSON>vi"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Montenegrin handball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Radi%C4%8Devi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Radi%C4%8Devi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin handball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jovanka_Radi%C4%8Devi%C4%87"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1987", "text": "<PERSON>, Irish rugby player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Venezuelan baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Doubront\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Doubront\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Doubront"}]}, {"year": "1987", "text": "<PERSON>, Swedish singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-guk\" title=\"<PERSON>o In-guk\"><PERSON><PERSON>-<PERSON>uk</a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-guk\" title=\"<PERSON>o In-guk\"><PERSON><PERSON>-<PERSON>uk</a>, South Korean singer and actor", "links": [{"title": "<PERSON><PERSON>uk", "link": "https://wikipedia.org/wiki/Se<PERSON>_In-guk"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Japanese model and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Japanese actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Crawford"}]}, {"year": "1988", "text": "<PERSON>, Russian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Swedish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Venezuelan footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Indo-Canadian singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indo-Canadian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indo-Canadian singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Russian race walker", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian race walker", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Swedish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "Princess <PERSON><PERSON> of Akishino, member of the Japanese Imperial Family", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_of_Akishino\" class=\"mw-redirect\" title=\"Princess <PERSON><PERSON> of Akishino\">Princess <PERSON><PERSON> of Akishino</a>, member of the <a href=\"https://wikipedia.org/wiki/Japanese_imperial_family\" class=\"mw-redirect\" title=\"Japanese imperial family\">Japanese Imperial Family</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_of_Akishino\" class=\"mw-redirect\" title=\"Princess <PERSON><PERSON> of Akishino\">Princess <PERSON><PERSON> of Akishino</a>, member of the <a href=\"https://wikipedia.org/wiki/Japanese_imperial_family\" class=\"mw-redirect\" title=\"Japanese imperial family\">Japanese Imperial Family</a>", "links": [{"title": "Princess <PERSON><PERSON> of Akishino", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_Akishino"}, {"title": "Japanese imperial family", "link": "https://wikipedia.org/wiki/Japanese_imperial_family"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American model", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Ireland_<PERSON>\" title=\"Ireland Baldwin\">Ireland Baldwin</a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ireland_<PERSON>\" title=\"Ireland Baldwin\">Ireland Baldwin</a>, American model", "links": [{"title": "Ireland Baldwin", "link": "https://wikipedia.org/wiki/Ireland_Baldwin"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, French basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/%C3%89lie_<PERSON>ob<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lie_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lie_<PERSON><PERSON>o"}]}, {"year": "1997", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27A\" title=\"<PERSON>dn Su'A\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27A\" title=\"<PERSON>dn Su'A\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaydn_Su%27A"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Thai singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Thai singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Thai singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Goodwin"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Japanese idol", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese idol", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese idol", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Chinese singer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Ningning\" title=\"Ning<PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ningning\" title=\"Ning<PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ningning"}]}], "Deaths": [{"year": "42 BC", "text": "<PERSON> the Younger, Roman general and politician (b. 85 BC)", "html": "42 BC - 42 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_the_Younger\" class=\"mw-redirect\" title=\"<PERSON>tus the Younger\"><PERSON> the Younger</a>, Roman general and politician (b. 85 BC)", "no_year_html": "42 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Younger\" class=\"mw-redirect\" title=\"<PERSON>tus the Younger\"><PERSON> the Younger</a>, Roman general and politician (b. 85 BC)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_the_Younger"}]}, {"year": "877", "text": "<PERSON><PERSON><PERSON><PERSON> of Constantinople, Byzantine patriarch (b. 797)", "html": "877 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON><PERSON> of Constantinople</a>, Byzantine patriarch (b. 797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON><PERSON> of Constantinople</a>, Byzantine patriarch (b. 797)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Ignatios_of_Constantinople"}]}, {"year": "891", "text": "<PERSON><PERSON><PERSON>, Abbasid general and politician", "html": "891 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Abbasid general and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Abbasid general and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "902", "text": "<PERSON> of Ifriqiya, Aghlabid emir (b. 850)", "html": "902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Ifriqiya\" title=\"<PERSON> of Ifriqiya\"><PERSON> of Ifriqiya</a>, Aghlabid emir (b. 850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Ifriqiya\" title=\"<PERSON> of Ifriqiya\"><PERSON> of Ifriqiya</a>, <PERSON><PERSON><PERSON><PERSON> emir (b. 850)", "links": [{"title": "<PERSON> of Ifriqiya", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Ifriqiya"}]}, {"year": "930", "text": "<PERSON><PERSON>, Japanese emperor (b. 885)", "html": "930 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese emperor (b. 885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese emperor (b. 885)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}]}, {"year": "945", "text": "<PERSON><PERSON><PERSON> of Goryeo, Korean king (b. 912)", "html": "945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean king (b. 912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean king (b. 912)", "links": [{"title": "<PERSON><PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Hyejong_of_Goryeo"}]}, {"year": "949", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (b. 869)", "html": "949 - <a href=\"https://wikipedia.org/wiki/Emperor_Y%C5%8Dzei\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Y%C5%8Dzei\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 869)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Y%C5%8Dzei"}]}, {"year": "1134", "text": "<PERSON>, Andalusian polymath", "html": "1134 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Abu al-Salt\"><PERSON></a>, Andalusian polymath", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Abu al-Salt\"><PERSON></a>, Andalusian polymath", "links": [{"title": "Abu al-Salt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1157", "text": "<PERSON><PERSON><PERSON>, Danish king (b. c. 1125)", "html": "1157 - <a href=\"https://wikipedia.org/wiki/S<PERSON>yn_III_of_Denmark\" title=\"Sweyn III of Denmark\"><PERSON><PERSON><PERSON> III</a>, Danish king (b. c. 1125)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S<PERSON>yn_III_of_Denmark\" title=\"Sweyn III of Denmark\"><PERSON><PERSON><PERSON> III</a>, Danish king (b. c. 1125)", "links": [{"title": "Sweyn III of Denmark", "link": "https://wikipedia.org/wiki/Sweyn_III_of_Denmark"}]}, {"year": "1456", "text": "<PERSON> of Capistrano, Italian priest and saint (b. 1386)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Capistrano\" title=\"<PERSON> of Capistrano\"><PERSON> of Capistrano</a>, Italian priest and saint (b. 1386)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Capist<PERSON>\" title=\"<PERSON> of Capistrano\"><PERSON> of Capistrano</a>, Italian priest and saint (b. 1386)", "links": [{"title": "<PERSON> of Capistrano", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1550", "text": "<PERSON><PERSON><PERSON>, Polish bishop (b. 1480)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/Tied<PERSON>_Giese\" title=\"Tiedemann Giese\"><PERSON><PERSON><PERSON></a>, Polish bishop (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tied<PERSON>_Giese\" title=\"Tiedemann Giese\"><PERSON><PERSON><PERSON></a>, Polish bishop (b. 1480)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1581", "text": "<PERSON>, German mathematician and astronomer (b. 1529)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (b. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (b. 1529)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1616", "text": "<PERSON><PERSON>, German theologian and academic (b. 1563)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German theologian and academic (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German theologian and academic (b. 1563)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON>, <PERSON><PERSON> <PERSON>, French philologist and historian (b. 1610)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_sieur_du_<PERSON>\" title=\"<PERSON>, sieur du <PERSON>\"><PERSON>, sieur du <PERSON></a>, French philologist and historian (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_sieur_<PERSON>_<PERSON>\" title=\"<PERSON>, sieur du <PERSON>\"><PERSON>, sieur du <PERSON></a>, French philologist and historian (b. 1610)", "links": [{"title": "<PERSON>, sieur du <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, English actress (b. 1683)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, French admiral (b. 1683)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></a>, French admiral (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></a>, French admiral (b. 1683)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>tte", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, French missionary and astronomer (b. 1715)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and astronomer (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and astronomer (b. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Finnish explorer, orientalist, and professor (b. 1811)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish explorer, <a href=\"https://wikipedia.org/wiki/Orientalism\" title=\"Orientalism\">orientalist</a>, and professor (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish explorer, <a href=\"https://wikipedia.org/wiki/Orientalism\" title=\"Orientalism\">orientalist</a>, and professor (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_August_Wallin"}, {"title": "Orientalism", "link": "https://wikipedia.org/wiki/Orientalism"}]}, {"year": "1867", "text": "<PERSON>, German linguist and academic (b. 1791)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist and academic (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist and academic (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, 14th Earl of Derby, English lawyer and politician, Prime Minister of the United Kingdom (b. 1799)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Derby\" title=\"<PERSON>, 14th Earl of Derby\"><PERSON>, 14th Earl of Derby</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Derby\" title=\"<PERSON>, 14th Earl of Derby\"><PERSON>, 14th Earl of Derby</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1799)", "links": [{"title": "<PERSON>, 14th Earl of Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_Earl_of_Derby"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French journalist, author, and poet (b. 1811)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9ophil<PERSON>_<PERSON>autier\" title=\"Théophile Gautier\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French journalist, author, and poet (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_Gautier\" title=\"Théophile Gautier\">T<PERSON><PERSON><PERSON><PERSON><PERSON></a>, French journalist, author, and poet (b. 1811)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9ophile_Gautier"}]}, {"year": "1885", "text": "<PERSON>, American lawyer, jurist, and politician, Secretary of State of Texas (b. 1829)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>._West\" title=\"Charles <PERSON> West\"><PERSON></a>, American lawyer, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Texas\" title=\"Secretary of State of Texas\">Secretary of State of Texas</a> (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_S._West\" title=\"Charles <PERSON> West\"><PERSON></a>, American lawyer, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Texas\" title=\"Secretary of State of Texas\">Secretary of State of Texas</a> (b. 1829)", "links": [{"title": "Charles <PERSON>", "link": "https://wikipedia.org/wiki/Charles_S._West"}, {"title": "Secretary of State of Texas", "link": "https://wikipedia.org/wiki/Secretary_of_State_of_Texas"}]}, {"year": "1893", "text": "<PERSON> Battenberg (b. 1857)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Battenberg\" title=\"<PERSON> of Battenberg\"><PERSON> of Battenberg</a> (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bat<PERSON>berg\" title=\"<PERSON> of Battenberg\"><PERSON> of Battenberg</a> (b. 1857)", "links": [{"title": "<PERSON> of Battenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Thai king (b. 1853)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Chulalongkorn\" title=\"Chulalongkorn\"><PERSON><PERSON>ongkorn</a>, Thai king (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chulalongkorn\" title=\"Chulalongkorn\"><PERSON><PERSON>ongkorn</a>, Thai king (b. 1853)", "links": [{"title": "Chulalongkorn", "link": "https://wikipedia.org/wiki/Chulalongkorn"}]}, {"year": "1915", "text": "<PERSON><PERSON> <PERSON><PERSON>, English cricketer and physician (b. 1848)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and physician (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and physician (b. 1848)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Scottish footballer and soldier (b. 1889)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and soldier (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and soldier (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Swiss illustrator (b. 1845)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Grasset\" title=\"<PERSON>\"><PERSON></a>, Swiss illustrator (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Grasset\" title=\"<PERSON> Grass<PERSON>\"><PERSON></a>, Swiss illustrator (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Grasset"}]}, {"year": "1921", "text": "<PERSON>, Scottish businessman, founded <PERSON><PERSON><PERSON> (b. 1840)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman, founded <a href=\"https://wikipedia.org/wiki/Dun<PERSON>_Rubber\" title=\"Dunlop Rubber\">Du<PERSON><PERSON> Rubber</a> (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman, founded <a href=\"https://wikipedia.org/wiki/Dun<PERSON>_Rubber\" title=\"Dun<PERSON> Rubber\">Du<PERSON><PERSON> Rubber</a> (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ber"}]}, {"year": "1935", "text": "<PERSON>, American painter and educator (b. 1883)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American dentist and author (b. 1872)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and author (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and author (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American pianist and composer (b. 1901)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 21st <PERSON><PERSON><PERSON><PERSON> (b. 1876)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Wakashima_Gonshir%C5%8D\" title=\"Wakashima Gonshirō\"><PERSON><PERSON><PERSON> Gonshirō</a>, Japanese sumo wrestler, the 21st <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wakashima_Gonshir%C5%8D\" title=\"Wakashima Gonshirō\"><PERSON><PERSON><PERSON> Gonshirō</a>, Japanese sumo wrestler, the 21st <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1876)", "links": [{"title": "Wakashima Gonshirō", "link": "https://wikipedia.org/wiki/Wakashima_Gonshir%C5%8D"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1944", "text": "<PERSON>, English-Scottish physicist and academic, Nobel Prize laureate (b. 1877)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Czech holocaust victim (b. 1931)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">holocaust</a> victim (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">holocaust</a> victim (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Holocaust", "link": "https://wikipedia.org/wiki/Holocaust"}]}, {"year": "1950", "text": "<PERSON>, Lithuanian-American actor and singer (b. 1886)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American actor and singer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American actor and singer (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, French son of <PERSON> (b. 1869)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Greek painter (b. 1885)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek painter (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek painter (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Swedish actress (b. 1871)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actress (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actress (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American historian and journalist (b. 1886)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and journalist (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and journalist (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter (b. 1922)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON> artist and farmer (b. 1891)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> British artist and farmer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> British artist and farmer (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American singer and autoharp player (Carter Family) (b. 1909)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and autoharp player (<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carter Family\"><PERSON> Family</a>) (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and autoharp player (<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carter Family\"><PERSON> Family</a>) (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Carter Family", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Hungarian-born Swiss rabbi and businessman (b. 1923)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-born Swiss rabbi and businessman (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-born Swiss rabbi and businessman (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American journalist (b. 1947)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Austrian-German actor (b. 1922)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German actor (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (b. 1893)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 46th <PERSON><PERSON><PERSON><PERSON> (b. 1929)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Asashio_Tar%C5%8D_III\" title=\"<PERSON><PERSON><PERSON> Tarō III\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 46th <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asashio_Tar%C5%8D_III\" title=\"<PERSON><PERSON><PERSON> Tarō III\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 46th <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asashio_Tar%C5%8D_III"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Mexican-American actress, singer, and dancer (b. 1911)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)\" title=\"<PERSON><PERSON> (actress)\"><PERSON><PERSON></a>, Mexican-American actress, singer, and dancer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)\" title=\"<PERSON><PERSON> (actress)\"><PERSON><PERSON></a>, Mexican-American actress, singer, and dancer (b. 1911)", "links": [{"title": "<PERSON><PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)"}]}, {"year": "1990", "text": "<PERSON>, American author and academic (b. 1926)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and academic (b. 1926)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1928)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1996", "text": "<PERSON>, American baseball player (b. 1930)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1930)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1997", "text": "<PERSON>, Dutch director, producer, and screenwriter (b. 1916)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director, producer, and screenwriter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director, producer, and screenwriter (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American physician (b. 1946)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Slepian\"><PERSON></a>, American physician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Slepian\"><PERSON></a>, American physician (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English author, screenwriter, and producer (b. 1909)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, screenwriter, and producer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, screenwriter, and producer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Australian politician, 32nd Premier of Tasmania (b. 1909)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, American wrestler (b. 1966)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON><PERSON></a>, American wrestler (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON><PERSON></a>, American wrestler (b. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)"}]}, {"year": "2001", "text": "<PERSON>, English illustrator (b. 1928)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, French art dealer and historian (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French art dealer and historian (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French art dealer and historian (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, American playwright and songwriter (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American playwright and songwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American playwright and songwriter (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English actor and singer (b. 1944)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tony Capstick\"><PERSON></a>, English actor and singer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cap<PERSON>\" title=\"Tony Capstick\"><PERSON></a>, English actor and singer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Chinese wife of <PERSON>, 2nd First Lady of the Republic of China (b. 1898)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ling\" title=\"<PERSON><PERSON>-ling\"><PERSON><PERSON></a>, Chinese wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_Republic_of_China\" title=\"First Lady of the Republic of China\">First Lady of the Republic of China</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ling\" title=\"<PERSON><PERSON>-ling\"><PERSON><PERSON></a>, Chinese wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_Republic_of_China\" title=\"First Lady of the Republic of China\">First Lady of the Republic of China</a> (b. 1898)", "links": [{"title": "<PERSON><PERSON>ling", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ling"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "First Lady of the Republic of China", "link": "https://wikipedia.org/wiki/First_Lady_of_the_Republic_of_China"}]}, {"year": "2004", "text": "<PERSON>, American actor and singer (b. 1919)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English footballer, coach, and manager (b. 1919)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer, coach, and manager (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer, coach, and manager (b. 1919)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "2005", "text": "<PERSON>, American actor (b. 1948)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American economist and academic (b. 1930)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Nigerian wife of <PERSON><PERSON><PERSON><PERSON>, 10th First Lady of Nigeria (b. 1945)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>luseg<PERSON>_Obasanjo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, 10th <a href=\"https://wikipedia.org/wiki/First_Lady_of_Nigeria\" title=\"First Lady of Nigeria\">First Lady of Nigeria</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>eg<PERSON>_Obasanjo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, 10th <a href=\"https://wikipedia.org/wiki/First_Lady_of_Nigeria\" title=\"First Lady of Nigeria\">First Lady of Nigeria</a> (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stella_<PERSON>jo"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>eg<PERSON>_O<PERSON>anjo"}, {"title": "First Lady of Nigeria", "link": "https://wikipedia.org/wiki/First_Lady_of_Nigeria"}]}, {"year": "2006", "text": "<PERSON><PERSON>, South African singer (<PERSON>) (b. 1977)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African singer (<a href=\"https://wikipedia.org/wiki/Boom_Shaka\" title=\"Boom Shaka\"><PERSON></a>) (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African singer (<a href=\"https://wikipedia.org/wiki/Boom_Shaka\" title=\"Boom Shaka\"><PERSON></a>) (b. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boom_Shaka"}]}, {"year": "2007", "text": "<PERSON>, Turkish-Australian businessman, founded Crazy John's (b. 1965)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-Australian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_John%27s\" title=\"Crazy John's\"><PERSON>'s</a> (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-Australian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_John%27s\" title=\"Crazy John's\"><PERSON>'s</a> (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Crazy John's", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s"}]}, {"year": "2007", "text": "<PERSON>, Malaysian-Chinese businessman (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian-Chinese businessman (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian-Chinese businessman (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English boxer (b. 1948)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian-American actor (b. 1913)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American swimmer (b. 1984)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer (b. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American businessman and philanthropist, founded the Tanger Factory Outlet Centers (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Tanger_Factory_Outlet_Centers\" class=\"mw-redirect\" title=\"Tanger Factory Outlet Centers\">Tanger Factory Outlet Centers</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Tanger_Factory_Outlet_Centers\" class=\"mw-redirect\" title=\"Tanger Factory Outlet Centers\">Tanger Factory Outlet Centers</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tanger Factory Outlet Centers", "link": "https://wikipedia.org/wiki/Tanger_Factory_Outlet_Centers"}]}, {"year": "2011", "text": "<PERSON>, American chemist and mathematician, Nobel Prize laureate (b. 1917)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2011", "text": "<PERSON>, Italian motorcycle racer (b. 1987)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Polish photographer (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish photographer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish photographer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French soldier and pilot (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Poype\"><PERSON></a>, French soldier and pilot (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Poype\"><PERSON></a>, French soldier and pilot (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian author and poet (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and poet (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and poet (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "2012", "text": "<PERSON>, Scottish singer-songwriter (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American basketball player (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American basketball player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American basketball player (b. 1945)", "links": [{"title": "Wes <PERSON>", "link": "https://wikipedia.org/wiki/Wes_Bialosuknia"}]}, {"year": "2013", "text": "<PERSON>, English sculptor and academic (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Irish hurler (b. 1990)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurler (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurler (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, English guitarist and songwriter (Dr<PERSON> and The Yardbirds) (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mayo\"><PERSON><PERSON><PERSON></a>, English guitarist and songwriter (<a href=\"https://wikipedia.org/wiki/Dr<PERSON>_<PERSON>_(band)\" title=\"Dr<PERSON> <PERSON> (band)\">Dr. <PERSON></a> and <a href=\"https://wikipedia.org/wiki/The_Yardbirds\" title=\"The Yardbirds\">The Yardbirds</a>) (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English guitarist and songwriter (<a href=\"https://wikipedia.org/wiki/Dr<PERSON>_<PERSON>_(band)\" title=\"<PERSON><PERSON> <PERSON> (band)\">Dr<PERSON></a> and <a href=\"https://wikipedia.org/wiki/The_Yardbirds\" title=\"The Yardbirds\">The Yardbirds</a>) (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mayo"}, {"title": "<PERSON><PERSON> (band)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>good_(band)"}, {"title": "The Yardbirds", "link": "https://wikipedia.org/wiki/The_Yardbirds"}]}, {"year": "2013", "text": "<PERSON>, Ukrainian-American journalist and sportscaster (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American journalist and sportscaster (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American journalist and sportscaster (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>hul<PERSON>_Azam\" title=\"<PERSON><PERSON><PERSON> Azam\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Azam\" title=\"<PERSON><PERSON><PERSON> Azam\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English-American journalist and academic (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American astrologer and author (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrologer and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrologer and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Italian physicist and academic (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tulli<PERSON> Regge\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic (b. 1931)", "links": [{"title": "<PERSON><PERSON>o <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ge"}]}, {"year": "2014", "text": "<PERSON>, English singer and actor (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alvin Stardust\"><PERSON></a>, English singer and actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alvin Stardust\"><PERSON></a>, English singer and actor (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alvin_Stardust"}]}, {"year": "2015", "text": "<PERSON>, American-Canadian singer (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-Canadian singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-Canadian singer (b. 1922)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2015", "text": "<PERSON>, Belgian businessman (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1940)\" title=\"<PERSON> (ice hockey, born 1940)\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1940)\" title=\"<PERSON> (ice hockey, born 1940)\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1940)", "links": [{"title": "<PERSON> (ice hockey, born 1940)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1940)"}]}, {"year": "2015", "text": "<PERSON>, American businessman and philanthropist, co-founded the Museum of Contemporary Art (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded the <a href=\"https://wikipedia.org/wiki/Museum_of_Contemporary_Art,_Los_Angeles\" title=\"Museum of Contemporary Art, Los Angeles\">Museum of Contemporary Art</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded the <a href=\"https://wikipedia.org/wiki/Museum_of_Contemporary_Art,_Los_Angeles\" title=\"Museum of Contemporary Art, Los Angeles\">Museum of Contemporary Art</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Museum of Contemporary Art, Los Angeles", "link": "https://wikipedia.org/wiki/Museum_of_Contemporary_Art,_Los_Angeles"}]}, {"year": "2016", "text": "<PERSON>, American cartoonist and publisher (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chi<PERSON>\"><PERSON></a>, American cartoonist and publisher (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Chick\"><PERSON></a>, American cartoonist and publisher (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Dutch speed skater (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Dutch speed skater (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Dutch speed skater (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English singer-songwriter (b. 1959)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, German cinematographer (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cinematographer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cinematographer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Australian tennis player (b. 1984)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American singer-songwriter (b. 1942)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Portuguese politician, Minister of the Overseas Provinces, President of the CDS - People's Party (b. 1922)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese politician, Minister of the Overseas Provinces, President of the <a href=\"https://wikipedia.org/wiki/CDS_%E2%80%93_People%27s_Party\" title=\"CDS - People's Party\">CDS - People's Party</a> (b. <a href=\"https://wikipedia.org/wiki/1922\" title=\"1922\">1922</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese politician, Minister of the Overseas Provinces, President of the <a href=\"https://wikipedia.org/wiki/CDS_%E2%80%93_People%27s_Party\" title=\"CDS - People's Party\">CDS - People's Party</a> (b. <a href=\"https://wikipedia.org/wiki/1922\" title=\"1922\">1922</a>)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "CDS - People's Party", "link": "https://wikipedia.org/wiki/CDS_%E2%80%93_People%27s_Party"}, {"title": "1922", "link": "https://wikipedia.org/wiki/1922"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Finnish entrepreneur and dance teacher (b. 1927)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish entrepreneur and dance teacher (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish entrepreneur and dance teacher (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aira_Samulin"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (b. 1946)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, British shot putter and strongman (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Geoff_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British shot putter and strongman (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Geoff <PERSON>\"><PERSON></a>, British shot putter and strongman (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Geoff_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American writer, playwright and poet (b. 1950)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Gary_Indiana\" title=\"Gary Indiana\"><PERSON></a>, American writer, playwright and poet (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gary_Indiana\" title=\"Gary Indiana\"><PERSON></a>, American writer, playwright and poet (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gary_Indiana"}]}, {"year": "2024", "text": "<PERSON>, American singer and actor (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer and actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer and actor (b. 1938)", "links": [{"title": "<PERSON> (American singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)"}]}]}}