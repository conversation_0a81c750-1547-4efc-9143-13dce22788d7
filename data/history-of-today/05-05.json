{"date": "May 5", "url": "https://wikipedia.org/wiki/May_5", "data": {"Events": [{"year": "553", "text": "The Second Council of Constantinople begins.", "html": "553 - The <a href=\"https://wikipedia.org/wiki/Second_Council_of_Constantinople\" title=\"Second Council of Constantinople\">Second Council of Constantinople</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Council_of_Constantinople\" title=\"Second Council of Constantinople\">Second Council of Constantinople</a> begins.", "links": [{"title": "Second Council of Constantinople", "link": "https://wikipedia.org/wiki/Second_Council_of_Constantinople"}]}, {"year": "1215", "text": "Rebel barons renounce their allegiance to King <PERSON> of England — part of a chain of events leading to the signing of the Magna Carta.", "html": "1215 - Rebel barons renounce their allegiance to King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a> — part of a chain of events leading to the signing of the <a href=\"https://wikipedia.org/wiki/Magna_Carta\" title=\"Magna Carta\">Magna Carta</a>.", "no_year_html": "Rebel barons renounce their allegiance to King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a> — part of a chain of events leading to the signing of the <a href=\"https://wikipedia.org/wiki/Magna_Carta\" title=\"Magna Carta\">Magna Carta</a>.", "links": [{"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "Magna Carta", "link": "https://wikipedia.org/wiki/Magna_Carta"}]}, {"year": "1260", "text": "<PERSON><PERSON><PERSON> Khan becomes ruler of the Mongol Empire.", "html": "1260 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Khan\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a> becomes ruler of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Khan\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a> becomes ruler of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}]}, {"year": "1494", "text": "On his second voyage to the New World, <PERSON> sights Jamaica, landing at Discovery Bay and declares Jamaica the property of the Spanish crown.", "html": "1494 - On his second voyage to the New World, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sights <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a>, landing at Discovery Bay and declares Jamaica the property of the Spanish crown.", "no_year_html": "On his second voyage to the New World, <a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sights <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a>, landing at Discovery Bay and declares Jamaica the property of the Spanish crown.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jamaica", "link": "https://wikipedia.org/wiki/Jamaica"}]}, {"year": "1609", "text": "<PERSON><PERSON><PERSON> (Lord) <PERSON><PERSON><PERSON> of the Satsuma Domain in southern Kyūshū, Japan, completes his successful invasion of the Ryūkyū Kingdom in Okinawa.", "html": "1609 - <i><PERSON><PERSON><PERSON></i> (Lord) <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tadatsune\" title=\"<PERSON><PERSON><PERSON> Tadatsune\"><PERSON><PERSON><PERSON>ats<PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Satsuma_Domain\" title=\"Satsuma Domain\">Satsuma Domain</a> in southern <a href=\"https://wikipedia.org/wiki/Ky%C5%ABsh%C5%AB\" class=\"mw-redirect\" title=\"Kyūshū\">Kyūshū</a>, <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Japan</a>, completes <a href=\"https://wikipedia.org/wiki/Invasion_of_Ryukyu\" title=\"Invasion of Ryukyu\">his successful invasion</a> of the <a href=\"https://wikipedia.org/wiki/Ry%C5%ABky%C5%AB_Kingdom\" class=\"mw-redirect\" title=\"Ryūkyū Kingdom\">Ryūkyū Kingdom</a> in <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa</a>.", "no_year_html": "<i><PERSON><PERSON><PERSON></i> (Lord) <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tadatsune\" title=\"<PERSON><PERSON><PERSON> Tadatsune\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Satsuma_Domain\" title=\"Satsuma Domain\">Satsuma Domain</a> in southern <a href=\"https://wikipedia.org/wiki/Ky%C5%ABsh%C5%AB\" class=\"mw-redirect\" title=\"Kyūshū\">Kyūshū</a>, <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Japan</a>, completes <a href=\"https://wikipedia.org/wiki/Invasion_of_Ryukyu\" title=\"Invasion of Ryukyu\">his successful invasion</a> of the <a href=\"https://wikipedia.org/wiki/Ry%C5%ABky%C5%AB_Kingdom\" class=\"mw-redirect\" title=\"Ryūkyū Kingdom\">Ryūkyū Kingdom</a> in <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>une"}, {"title": "Satsuma Domain", "link": "https://wikipedia.org/wiki/Satsuma_Domain"}, {"title": "Kyūshū", "link": "https://wikipedia.org/wiki/Ky%C5%ABsh%C5%AB"}, {"title": "Tokugawa shogunate", "link": "https://wikipedia.org/wiki/Tokugawa_shogunate"}, {"title": "Invasion of Ryukyu", "link": "https://wikipedia.org/wiki/Invasion_of_Ryukyu"}, {"title": "Ryūkyū Kingdom", "link": "https://wikipedia.org/wiki/Ry%C5%ABky%C5%AB_Kingdom"}, {"title": "Okinawa Prefecture", "link": "https://wikipedia.org/wiki/Okinawa_Prefecture"}]}, {"year": "1640", "text": "King <PERSON> of England dissolves the Short Parliament.", "html": "1640 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> of England</a> dissolves the <a href=\"https://wikipedia.org/wiki/Short_Parliament\" title=\"Short Parliament\">Short Parliament</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a> dissolves the <a href=\"https://wikipedia.org/wiki/Short_Parliament\" title=\"Short Parliament\">Short Parliament</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Short Parliament", "link": "https://wikipedia.org/wiki/Short_Parliament"}]}, {"year": "1654", "text": "<PERSON>'s Act of Grace, aimed at reconciliation with the Scots, proclaimed in Edinburgh.", "html": "1654 - <a href=\"https://wikipedia.org/wiki/Cromwell%27s_Act_of_Grace\" title=\"Cromwell's Act of Grace\"><PERSON>'s Act of Grace</a>, aimed at reconciliation with the Scots, proclaimed in <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cromwell%27s_Act_of_Grace\" title=\"Cromwell's Act of Grace\"><PERSON>'s Act of Grace</a>, aimed at reconciliation with the Scots, proclaimed in <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>.", "links": [{"title": "<PERSON>'s Act of Grace", "link": "https://wikipedia.org/wiki/Cromwell%27s_Act_of_Grace"}, {"title": "Edinburgh", "link": "https://wikipedia.org/wiki/Edinburgh"}]}, {"year": "1762", "text": "Russia and Prussia sign the Treaty of St. Petersburg.", "html": "1762 - <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint_Petersburg_(1762)\" title=\"Treaty of Saint Petersburg (1762)\">Treaty of St. Petersburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint_Petersburg_(1762)\" title=\"Treaty of Saint Petersburg (1762)\">Treaty of St. Petersburg</a>.", "links": [{"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Treaty of Saint Petersburg (1762)", "link": "https://wikipedia.org/wiki/Treaty_of_Saint_Petersburg_(1762)"}]}, {"year": "1789", "text": "In France, the Estates-General convenes for the first time since 1614.", "html": "1789 - In <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>, the <a href=\"https://wikipedia.org/wiki/Estates-General_of_1789\" class=\"mw-redirect\" title=\"Estates-General of 1789\">Estates-General</a> convenes for the first time since 1614.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>, the <a href=\"https://wikipedia.org/wiki/Estates-General_of_1789\" class=\"mw-redirect\" title=\"Estates-General of 1789\">Estates-General</a> convenes for the first time since 1614.", "links": [{"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Estates-General of 1789", "link": "https://wikipedia.org/wiki/Estates-General_of_1789"}]}, {"year": "1809", "text": "<PERSON> becomes the first woman awarded a U.S. patent, for a technique of weaving straw with silk and thread.", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the first woman awarded a U.S. <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a>, for a technique of <a href=\"https://wikipedia.org/wiki/Weaving\" title=\"Weaving\">weaving</a> <a href=\"https://wikipedia.org/wiki/Straw\" title=\"Straw\">straw</a> with <a href=\"https://wikipedia.org/wiki/Silk\" title=\"Silk\">silk</a> and <a href=\"https://wikipedia.org/wiki/Yarn\" title=\"Yarn\">thread</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the first woman awarded a U.S. <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a>, for a technique of <a href=\"https://wikipedia.org/wiki/Weaving\" title=\"Weaving\">weaving</a> <a href=\"https://wikipedia.org/wiki/Straw\" title=\"Straw\">straw</a> with <a href=\"https://wikipedia.org/wiki/Silk\" title=\"Silk\">silk</a> and <a href=\"https://wikipedia.org/wiki/Yarn\" title=\"Yarn\">thread</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Weaving", "link": "https://wikipedia.org/wiki/Weaving"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Straw"}, {"title": "Silk", "link": "https://wikipedia.org/wiki/Silk"}, {"title": "Yarn", "link": "https://wikipedia.org/wiki/Yarn"}]}, {"year": "1821", "text": "Emperor <PERSON> dies in exile on the island of Saint Helena in the South Atlantic Ocean.", "html": "1821 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in exile on the island of <a href=\"https://wikipedia.org/wiki/Saint_Helena\" title=\"Saint Helena\">Saint Helena</a> in the South Atlantic Ocean.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in exile on the island of <a href=\"https://wikipedia.org/wiki/Saint_Helena\" title=\"Saint Helena\">Saint Helena</a> in the South Atlantic Ocean.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Saint Helena", "link": "https://wikipedia.org/wiki/Saint_Helena"}]}, {"year": "1821", "text": "The first edition of The Manchester Guardian, now The Guardian, is published.", "html": "1821 - The first edition of <i>The Manchester Guardian</i>, now <i><a href=\"https://wikipedia.org/wiki/The_Guardian\" title=\"The Guardian\">The Guardian</a></i>, is published.", "no_year_html": "The first edition of <i>The Manchester Guardian</i>, now <i><a href=\"https://wikipedia.org/wiki/The_Guardian\" title=\"The Guardian\">The Guardian</a></i>, is published.", "links": [{"title": "The Guardian", "link": "https://wikipedia.org/wiki/The_Guardian"}]}, {"year": "1835", "text": "The first railway in continental Europe opens between Brussels and Mechelen.", "html": "1835 - The <a href=\"https://wikipedia.org/wiki/History_of_rail_transport_in_Belgium\" title=\"History of rail transport in Belgium\">first railway in continental Europe</a> opens between <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a> and <a href=\"https://wikipedia.org/wiki/Mechelen\" title=\"Mechelen\">Mechelen</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/History_of_rail_transport_in_Belgium\" title=\"History of rail transport in Belgium\">first railway in continental Europe</a> opens between <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a> and <a href=\"https://wikipedia.org/wiki/Mechelen\" title=\"Mechelen\">Mechelen</a>.", "links": [{"title": "History of rail transport in Belgium", "link": "https://wikipedia.org/wiki/History_of_rail_transport_in_Belgium"}, {"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}, {"title": "Mechelen", "link": "https://wikipedia.org/wiki/Mechelen"}]}, {"year": "1862", "text": "Cinco de Mayo: Troops led by <PERSON> halt a French invasion in the Battle of Puebla in Mexico.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Cinco_de_Mayo\" title=\"Cinco de Mayo\">Cinco de Mayo</a>: Troops led by <a href=\"https://wikipedia.org/wiki/Ignacio_<PERSON>\" title=\"<PERSON>\"><PERSON></a> halt a French invasion in the <a href=\"https://wikipedia.org/wiki/Battle_of_Puebla\" title=\"Battle of Puebla\">Battle of Puebla</a> in <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cinco_de_Mayo\" title=\"Cinco de Mayo\">Cinco de Mayo</a>: Troops led by <a href=\"https://wikipedia.org/wiki/Ignacio_<PERSON>\" title=\"<PERSON>\"><PERSON></a> halt a French invasion in the <a href=\"https://wikipedia.org/wiki/Battle_of_Puebla\" title=\"Battle of Puebla\">Battle of Puebla</a> in <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>.", "links": [{"title": "Cinco de Mayo", "link": "https://wikipedia.org/wiki/Cinco_de_Mayo"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ignacio_<PERSON>go<PERSON>"}, {"title": "Battle of Puebla", "link": "https://wikipedia.org/wiki/Battle_of_Puebla"}, {"title": "Mexico", "link": "https://wikipedia.org/wiki/Mexico"}]}, {"year": "1864", "text": "American Civil War: The Battle of the Wilderness begins in Spotsylvania County.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Wilderness\" title=\"Battle of the Wilderness\">Battle of the Wilderness</a> begins in <a href=\"https://wikipedia.org/wiki/Spotsylvania_County,_Virginia\" title=\"Spotsylvania County, Virginia\">Spotsylvania County</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Wilderness\" title=\"Battle of the Wilderness\">Battle of the Wilderness</a> begins in <a href=\"https://wikipedia.org/wiki/Spotsylvania_County,_Virginia\" title=\"Spotsylvania County, Virginia\">Spotsylvania County</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of the Wilderness", "link": "https://wikipedia.org/wiki/Battle_of_the_Wilderness"}, {"title": "Spotsylvania County, Virginia", "link": "https://wikipedia.org/wiki/Spotsylvania_County,_Virginia"}]}, {"year": "1865", "text": "American Civil War: The Confederate government was declared dissolved at Washington, Georgia.", "html": "1865 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War\" title=\"Conclusion of the American Civil War\">Confederate government</a> was declared dissolved at <a href=\"https://wikipedia.org/wiki/Washington,_Georgia\" title=\"Washington, Georgia\">Washington, Georgia</a>.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War\" title=\"Conclusion of the American Civil War\">Confederate government</a> was declared dissolved at <a href=\"https://wikipedia.org/wiki/Washington,_Georgia\" title=\"Washington, Georgia\">Washington, Georgia</a>.", "links": [{"title": "Conclusion of the American Civil War", "link": "https://wikipedia.org/wiki/Conclusion_of_the_American_Civil_War"}, {"title": "Washington, Georgia", "link": "https://wikipedia.org/wiki/Washington,_Georgia"}]}, {"year": "1866", "text": "Memorial Day first celebrated in United States at Waterloo, New York.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Memorial_Day\" title=\"Memorial Day\">Memorial Day</a> first celebrated in United States at <a href=\"https://wikipedia.org/wiki/Waterloo,_New_York\" title=\"Waterloo, New York\">Waterloo, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Memorial_Day\" title=\"Memorial Day\">Memorial Day</a> first celebrated in United States at <a href=\"https://wikipedia.org/wiki/Waterloo,_New_York\" title=\"Waterloo, New York\">Waterloo, New York</a>.", "links": [{"title": "Memorial Day", "link": "https://wikipedia.org/wiki/Memorial_Day"}, {"title": "Waterloo, New York", "link": "https://wikipedia.org/wiki/Waterloo,_New_York"}]}, {"year": "1877", "text": "American Indian Wars: <PERSON> leads his band of Lakota into Canada to avoid harassment by the United States Army under Colonel <PERSON>.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Sitting_Bull\" title=\"Sitting Bull\">Sitting Bull</a> leads his band of <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> into Canada to avoid harassment by the <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> under <a href=\"https://wikipedia.org/wiki/Colonel_(United_States)\" title=\"Colonel (United States)\">Colonel</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Sitting_Bull\" title=\"Sitting Bull\"><PERSON> Bull</a> leads his band of <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> into Canada to avoid harassment by the <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> under <a href=\"https://wikipedia.org/wiki/Colonel_(United_States)\" title=\"Colonel (United States)\">Colonel</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_Miles\" class=\"mw-redirect\" title=\"Nelson Miles\"><PERSON></a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Sitting Bull", "link": "https://wikipedia.org/wiki/Sitting_Bull"}, {"title": "Lakota people", "link": "https://wikipedia.org/wiki/Lakota_people"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Colonel (United States)", "link": "https://wikipedia.org/wiki/Colonel_(United_States)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "Workers marching for the Eight-hour day in Milwaukee, Wisconsin were shot at by Wisconsin National Guardsmen in what became known as the Bay View Massacre.", "html": "1886 - Workers marching for the <a href=\"https://wikipedia.org/wiki/Eight-hour_day\" class=\"mw-redirect\" title=\"Eight-hour day\">Eight-hour day</a> in <a href=\"https://wikipedia.org/wiki/Milwaukee\" title=\"Milwaukee\">Milwaukee</a>, <a href=\"https://wikipedia.org/wiki/Wisconsin\" title=\"Wisconsin\">Wisconsin</a> were shot at by <a href=\"https://wikipedia.org/wiki/Wisconsin_National_Guard\" title=\"Wisconsin National Guard\">Wisconsin National Guardsmen</a> in what became known as the <a href=\"https://wikipedia.org/wiki/Bay_View_Massacre\" class=\"mw-redirect\" title=\"Bay View Massacre\">Bay View Massacre</a>.", "no_year_html": "Workers marching for the <a href=\"https://wikipedia.org/wiki/Eight-hour_day\" class=\"mw-redirect\" title=\"Eight-hour day\">Eight-hour day</a> in <a href=\"https://wikipedia.org/wiki/Milwaukee\" title=\"Milwaukee\">Milwaukee</a>, <a href=\"https://wikipedia.org/wiki/Wisconsin\" title=\"Wisconsin\">Wisconsin</a> were shot at by <a href=\"https://wikipedia.org/wiki/Wisconsin_National_Guard\" title=\"Wisconsin National Guard\">Wisconsin National Guardsmen</a> in what became known as the <a href=\"https://wikipedia.org/wiki/Bay_View_Massacre\" class=\"mw-redirect\" title=\"Bay View Massacre\">Bay View Massacre</a>.", "links": [{"title": "Eight-hour day", "link": "https://wikipedia.org/wiki/Eight-hour_day"}, {"title": "Milwaukee", "link": "https://wikipedia.org/wiki/Milwaukee"}, {"title": "Wisconsin", "link": "https://wikipedia.org/wiki/Wisconsin"}, {"title": "Wisconsin National Guard", "link": "https://wikipedia.org/wiki/Wisconsin_National_Guard"}, {"title": "Bay View Massacre", "link": "https://wikipedia.org/wiki/Bay_View_Massacre"}]}, {"year": "1887", "text": "The Peruvian Academy of Language is founded.", "html": "1887 - The <a href=\"https://wikipedia.org/wiki/Peruvian_Academy_of_Language\" class=\"mw-redirect\" title=\"Peruvian Academy of Language\">Peruvian Academy of Language</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peruvian_Academy_of_Language\" class=\"mw-redirect\" title=\"Peruvian Academy of Language\">Peruvian Academy of Language</a> is founded.", "links": [{"title": "Peruvian Academy of Language", "link": "https://wikipedia.org/wiki/Peruvian_Academy_of_Language"}]}, {"year": "1891", "text": "The Music Hall in New York City (later known as Carnegie Hall) has its grand opening and first public performance, with <PERSON><PERSON><PERSON><PERSON> as the guest conductor.", "html": "1891 - The Music Hall in New York City (later known as <a href=\"https://wikipedia.org/wiki/Carnegie_Hall\" title=\"Carnegie Hall\">Carnegie Hall</a>) has its grand opening and first public performance, with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as the guest conductor.", "no_year_html": "The Music Hall in New York City (later known as <a href=\"https://wikipedia.org/wiki/Carnegie_Hall\" title=\"Carnegie Hall\">Carnegie Hall</a>) has its grand opening and first public performance, with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as the guest conductor.", "links": [{"title": "Carnegie Hall", "link": "https://wikipedia.org/wiki/Carnegie_Hall"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1904", "text": "Pitching against the Philadelphia Athletics at the Huntington Avenue Grounds, <PERSON> of the Boston Americans throws the first perfect game in the modern era of baseball.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Pitcher\" title=\"Pitcher\">Pitching</a> against the <a href=\"https://wikipedia.org/wiki/Philadelphia_Athletics\" title=\"Philadelphia Athletics\">Philadelphia Athletics</a> at the <a href=\"https://wikipedia.org/wiki/Huntington_Avenue_Grounds\" title=\"Huntington Avenue Grounds\">Huntington Avenue Grounds</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Boston_Red_Sox\" title=\"Boston Red Sox\">Boston Americans</a> throws the first <a href=\"https://wikipedia.org/wiki/Perfect_game_(baseball)\" title=\"Perfect game (baseball)\">perfect game</a> in the modern era of <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pitcher\" title=\"Pitcher\">Pitching</a> against the <a href=\"https://wikipedia.org/wiki/Philadelphia_Athletics\" title=\"Philadelphia Athletics\">Philadelphia Athletics</a> at the <a href=\"https://wikipedia.org/wiki/Huntington_Avenue_Grounds\" title=\"Huntington Avenue Grounds\">Huntington Avenue Grounds</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Boston_Red_Sox\" title=\"Boston Red Sox\">Boston Americans</a> throws the first <a href=\"https://wikipedia.org/wiki/Perfect_game_(baseball)\" title=\"Perfect game (baseball)\">perfect game</a> in the modern era of <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a>.", "links": [{"title": "Pitcher", "link": "https://wikipedia.org/wiki/Pitcher"}, {"title": "Philadelphia Athletics", "link": "https://wikipedia.org/wiki/Philadelphia_Athletics"}, {"title": "Huntington Avenue Grounds", "link": "https://wikipedia.org/wiki/Huntington_Avenue_Grounds"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Boston Red Sox", "link": "https://wikipedia.org/wiki/Boston_Red_Sox"}, {"title": "Perfect game (baseball)", "link": "https://wikipedia.org/wiki/Perfect_game_(baseball)"}, {"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}]}, {"year": "1905", "text": "The trial in the Strat<PERSON> Brothers case begins in London, England; it marks the first time that fingerprint evidence is used to gain a conviction for murder.", "html": "1905 - The trial in the <a href=\"https://wikipedia.org/wiki/<PERSON>ratton_Brothers_case\" title=\"Stratton Brothers case\">Stratton Brothers case</a> begins in London, England; it marks the first time that <a href=\"https://wikipedia.org/wiki/Fingerprint\" title=\"Fingerprint\">fingerprint</a> evidence is used to gain a conviction for murder.", "no_year_html": "The trial in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ton_Brothers_case\" title=\"Stratton Brothers case\">Stratton Brothers case</a> begins in London, England; it marks the first time that <a href=\"https://wikipedia.org/wiki/Fingerprint\" title=\"Fingerprint\">fingerprint</a> evidence is used to gain a conviction for murder.", "links": [{"title": "Stratton Brothers case", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Brothers_case"}, {"title": "Fingerprint", "link": "https://wikipedia.org/wiki/Fingerprint"}]}, {"year": "1912", "text": "The first issue of the Bolshevik newspaper Pravda was published.", "html": "1912 - The first issue of the <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> newspaper <i><a href=\"https://wikipedia.org/wiki/Pravda\" title=\"Pravda\">Pravda</a></i> was published.", "no_year_html": "The first issue of the <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> newspaper <i><a href=\"https://wikipedia.org/wiki/Pravda\" title=\"Pravda\">Pravda</a></i> was published.", "links": [{"title": "Bolshevik", "link": "https://wikipedia.org/wiki/Bolshevik"}, {"title": "Pravda", "link": "https://wikipedia.org/wiki/Pravda"}]}, {"year": "1920", "text": "Authorities arrest <PERSON> and <PERSON><PERSON><PERSON><PERSON> for alleged robbery and murder.", "html": "1920 - Authorities arrest <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON> and <PERSON><PERSON><PERSON><PERSON></a> for alleged robbery and murder.", "no_year_html": "Authorities arrest <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON> and <PERSON><PERSON><PERSON><PERSON></a> for alleged robbery and murder.", "links": [{"title": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>"}]}, {"year": "1930", "text": "The 1930 Bago earthquake, the former of two major earthquakes in southern Burma kills as many as 7,000 in Yangon and Bago.", "html": "1930 - The <a href=\"https://wikipedia.org/wiki/1930_Bago_earthquake\" title=\"1930 Bago earthquake\">1930 Bago earthquake</a>, the former of two major earthquakes in southern <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a> kills as many as 7,000 in Yangon and Bago.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1930_Bago_earthquake\" title=\"1930 Bago earthquake\">1930 Bago earthquake</a>, the former of two major earthquakes in southern <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a> kills as many as 7,000 in Yangon and Bago.", "links": [{"title": "1930 Bago earthquake", "link": "https://wikipedia.org/wiki/1930_Bago_earthquake"}, {"title": "Burma", "link": "https://wikipedia.org/wiki/Burma"}]}, {"year": "1936", "text": "Italian troops occupy Addis Ababa, Ethiopia.", "html": "1936 - Italian troops <a href=\"https://wikipedia.org/wiki/March_of_the_Iron_Will\" title=\"March of the Iron Will\">occupy</a> <a href=\"https://wikipedia.org/wiki/Addis_Ababa,_Ethiopia\" class=\"mw-redirect\" title=\"Addis Ababa, Ethiopia\">Addis Ababa, Ethiopia</a>.", "no_year_html": "Italian troops <a href=\"https://wikipedia.org/wiki/March_of_the_Iron_Will\" title=\"March of the Iron Will\">occupy</a> <a href=\"https://wikipedia.org/wiki/Addis_Ababa,_Ethiopia\" class=\"mw-redirect\" title=\"Addis Ababa, Ethiopia\">Addis Ababa, Ethiopia</a>.", "links": [{"title": "March of the Iron Will", "link": "https://wikipedia.org/wiki/March_of_the_Iron_Will"}, {"title": "Addis Ababa, Ethiopia", "link": "https://wikipedia.org/wiki/Addis_Ababa,_Ethiopia"}]}, {"year": "1940", "text": "World War II: Norwegian Campaign: Norwegian squads in Hegra Fortress and Vinjesvingen capitulate to German forces after all other Norwegian forces in southern Norway had laid down their arms.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/1940\" title=\"1940\">1940</a> - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Norwegian_Campaign\" class=\"mw-redirect\" title=\"Norwegian Campaign\">Norwegian Campaign</a>: Norwegian squads in <a href=\"https://wikipedia.org/wiki/Battle_of_Hegra_Fortress\" title=\"Battle of Hegra Fortress\">Hegra Fortress</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Vinjesvingen\" title=\"Battle of Vinjesvingen\">Vinjesvingen</a> capitulate to <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German forces</a> after all other Norwegian forces in southern Norway had laid down their arms.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1940\" title=\"1940\">1940</a> - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Norwegian_Campaign\" class=\"mw-redirect\" title=\"Norwegian Campaign\">Norwegian Campaign</a>: Norwegian squads in <a href=\"https://wikipedia.org/wiki/Battle_of_Hegra_Fortress\" title=\"Battle of Hegra Fortress\">Hegra Fortress</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Vinjesvingen\" title=\"Battle of Vinjesvingen\">Vinjesvingen</a> capitulate to <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German forces</a> after all other Norwegian forces in southern Norway had laid down their arms.", "links": [{"title": "1940", "link": "https://wikipedia.org/wiki/1940"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Norwegian Campaign", "link": "https://wikipedia.org/wiki/Norwegian_Campaign"}, {"title": "Battle of Hegra Fortress", "link": "https://wikipedia.org/wiki/Battle_of_Hegra_Fortress"}, {"title": "Battle of Vinjesvingen", "link": "https://wikipedia.org/wiki/Battle_of_Vinjesvingen"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1941", "text": "Emperor <PERSON><PERSON> returns to Addis Ababa; the country commemorates the date as Liberation Day or Patriots' Victory Day.", "html": "1941 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Selassie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON></a> returns to Addis Ababa; the country commemorates the date as <a href=\"https://wikipedia.org/wiki/Liberation_Day\" title=\"Liberation Day\">Liberation Day</a> or Patriots' Victory Day.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ie\" title=\"<PERSON><PERSON> Selassie\"><PERSON><PERSON></a> returns to Addis Ababa; the country commemorates the date as <a href=\"https://wikipedia.org/wiki/Liberation_Day\" title=\"Liberation Day\">Liberation Day</a> or Patriots' Victory Day.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Liberation Day", "link": "https://wikipedia.org/wiki/Liberation_Day"}]}, {"year": "1945", "text": "World War II: The Prague uprising begins as an attempt by the Czech resistance to free the city from German occupation.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Prague_uprising\" title=\"Prague uprising\">Prague uprising</a> begins as an attempt by the <a href=\"https://wikipedia.org/wiki/Czech_resistance\" class=\"mw-redirect\" title=\"Czech resistance\">Czech resistance</a> to free the city from <a href=\"https://wikipedia.org/wiki/German_occupation_of_Czechoslovakia\" class=\"mw-redirect\" title=\"German occupation of Czechoslovakia\">German occupation</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Prague_uprising\" title=\"Prague uprising\">Prague uprising</a> begins as an attempt by the <a href=\"https://wikipedia.org/wiki/Czech_resistance\" class=\"mw-redirect\" title=\"Czech resistance\">Czech resistance</a> to free the city from <a href=\"https://wikipedia.org/wiki/German_occupation_of_Czechoslovakia\" class=\"mw-redirect\" title=\"German occupation of Czechoslovakia\">German occupation</a>.", "links": [{"title": "Prague uprising", "link": "https://wikipedia.org/wiki/Prague_uprising"}, {"title": "Czech resistance", "link": "https://wikipedia.org/wiki/Czech_resistance"}, {"title": "German occupation of Czechoslovakia", "link": "https://wikipedia.org/wiki/German_occupation_of_Czechoslovakia"}]}, {"year": "1945", "text": "World War II: A Fu-Go balloon bomb launched by the Japanese Army kills six people near Bly, Oregon.", "html": "1945 - World War II: A <a href=\"https://wikipedia.org/wiki/Fu-Go_balloon_bomb\" title=\"Fu-Go balloon bomb\">Fu-Go balloon bomb</a> launched by the Japanese Army kills six people near <a href=\"https://wikipedia.org/wiki/Bly,_Oregon\" title=\"Bly, Oregon\">Bly, Oregon</a>.", "no_year_html": "World War II: A <a href=\"https://wikipedia.org/wiki/Fu-Go_balloon_bomb\" title=\"Fu-Go balloon bomb\">Fu-Go balloon bomb</a> launched by the Japanese Army kills six people near <a href=\"https://wikipedia.org/wiki/Bly,_Oregon\" title=\"Bly, Oregon\">Bly, Oregon</a>.", "links": [{"title": "Fu-Go balloon bomb", "link": "https://wikipedia.org/wiki/Fu-Go_balloon_bomb"}, {"title": "Bly, Oregon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Oregon"}]}, {"year": "1945", "text": "World War II: Battle of Castle Itter, one of only two battles in that war in which American and German troops fought cooperatively.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Castle_Itter\" title=\"Battle of Castle Itter\">Battle of Castle Itter</a>, one of only two battles in that war in which American and German troops fought cooperatively.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Castle_Itter\" title=\"Battle of Castle Itter\">Battle of Castle Itter</a>, one of only two battles in that war in which American and German troops fought cooperatively.", "links": [{"title": "Battle of Castle Itter", "link": "https://wikipedia.org/wiki/Battle_of_Castle_Itter"}]}, {"year": "1946", "text": "The International Military Tribunal for the Far East begins in Tokyo with twenty-eight Japanese military and government officials accused of war crimes and crimes against humanity.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> begins in Tokyo with twenty-eight Japanese military and government officials accused of <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a> and <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> begins in Tokyo with twenty-eight Japanese military and government officials accused of <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a> and <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>.", "links": [{"title": "International Military Tribunal for the Far East", "link": "https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East"}, {"title": "War crime", "link": "https://wikipedia.org/wiki/War_crime"}, {"title": "Crimes against humanity", "link": "https://wikipedia.org/wiki/Crimes_against_humanity"}]}, {"year": "1955", "text": "The General Treaty, by which France, Britain and the United States recognize the sovereignty of West Germany, comes into effect.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/General_Treaty\" class=\"mw-redirect\" title=\"General Treaty\">General Treaty</a>, by which France, Britain and the United States recognize the sovereignty of West Germany, comes into effect.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/General_Treaty\" class=\"mw-redirect\" title=\"General Treaty\">General Treaty</a>, by which France, Britain and the United States recognize the sovereignty of West Germany, comes into effect.", "links": [{"title": "General Treaty", "link": "https://wikipedia.org/wiki/General_Treaty"}]}, {"year": "1961", "text": "Project Mercury: <PERSON> becomes the first American to travel into outer space, on a sub-orbital flight.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first American to travel into outer space, on a sub-orbital <a href=\"https://wikipedia.org/wiki/Mercury-Redstone_3\" title=\"Mercury-Redstone 3\">flight</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first American to travel into outer space, on a sub-orbital <a href=\"https://wikipedia.org/wiki/Mercury-Redstone_3\" title=\"Mercury-Redstone 3\">flight</a>.", "links": [{"title": "Project Mercury", "link": "https://wikipedia.org/wiki/Project_Mercury"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mercury-Redstone 3", "link": "https://wikipedia.org/wiki/Mercury-Redstone_3"}]}, {"year": "1964", "text": "The Council of Europe declares May 5 as Europe Day.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Council_of_Europe\" title=\"Council of Europe\">Council of Europe</a> declares May 5 as <a href=\"https://wikipedia.org/wiki/Europe_Day\" title=\"Europe Day\">Europe Day</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Europe\" title=\"Council of Europe\">Council of Europe</a> declares May 5 as <a href=\"https://wikipedia.org/wiki/Europe_Day\" title=\"Europe Day\">Europe Day</a>.", "links": [{"title": "Council of Europe", "link": "https://wikipedia.org/wiki/Council_of_Europe"}, {"title": "Europe Day", "link": "https://wikipedia.org/wiki/Europe_Day"}]}, {"year": "1972", "text": "Alitalia Flight 112 crashes into Mount Longa near Palermo, Sicily, killing all 115 aboard, making it the deadliest single-aircraft disaster in Italy.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Alitalia_Flight_112\" title=\"Alitalia Flight 112\">Alitalia Flight 112</a> crashes into Mount Longa near <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a>, <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>, killing all 115 aboard, making it the deadliest single-aircraft disaster in Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alitalia_Flight_112\" title=\"Alitalia Flight 112\">Alitalia Flight 112</a> crashes into Mount Longa near <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a>, <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>, killing all 115 aboard, making it the deadliest single-aircraft disaster in Italy.", "links": [{"title": "Alitalia Flight 112", "link": "https://wikipedia.org/wiki/Alitalia_Flight_112"}, {"title": "Palermo", "link": "https://wikipedia.org/wiki/Palermo"}, {"title": "Sicily", "link": "https://wikipedia.org/wiki/Sicily"}]}, {"year": "1973", "text": "<PERSON> wins the 1973 Kentucky Derby in 1:59.4, an as-yet-unbeaten record.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Secretariat_(horse)\" title=\"<PERSON> (horse)\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1973_Kentucky_Derby\" title=\"1973 Kentucky Derby\">1973 Kentucky Derby</a> in 1:59.4, an as-yet-unbeaten record.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Secretariat_(horse)\" title=\"<PERSON> (horse)\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1973_Kentucky_Derby\" title=\"1973 Kentucky Derby\">1973 Kentucky Derby</a> in 1:59.4, an as-yet-unbeaten record.", "links": [{"title": "Secretariat (horse)", "link": "https://wikipedia.org/wiki/Secretariat_(horse)"}, {"title": "1973 Kentucky Derby", "link": "https://wikipedia.org/wiki/1973_Kentucky_Derby"}]}, {"year": "1980", "text": "Operation Nimrod: The British Special Air Service storms the Iranian embassy in London after a six-day siege.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Operation_Nimrod\" class=\"mw-redirect\" title=\"Operation Nimrod\">Operation Nimrod</a>: The British <a href=\"https://wikipedia.org/wiki/Special_Air_Service\" title=\"Special Air Service\">Special Air Service</a> storms the <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> <a href=\"https://wikipedia.org/wiki/Embassy\" class=\"mw-redirect\" title=\"Embassy\">embassy</a> in London after a six-day siege.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Nimrod\" class=\"mw-redirect\" title=\"Operation Nimrod\">Operation Nimrod</a>: The British <a href=\"https://wikipedia.org/wiki/Special_Air_Service\" title=\"Special Air Service\">Special Air Service</a> storms the <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> <a href=\"https://wikipedia.org/wiki/Embassy\" class=\"mw-redirect\" title=\"Embassy\">embassy</a> in London after a six-day siege.", "links": [{"title": "Operation Nimrod", "link": "https://wikipedia.org/wiki/Operation_Nimrod"}, {"title": "Special Air Service", "link": "https://wikipedia.org/wiki/Special_Air_Service"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Embassy", "link": "https://wikipedia.org/wiki/Embassy"}]}, {"year": "1981", "text": "<PERSON> dies in the Long Kesh prison hospital after 66 days of hunger-striking, aged 27.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in the <a href=\"https://wikipedia.org/wiki/Long_Kesh\" class=\"mw-redirect\" title=\"Long Kesh\"><PERSON></a> prison hospital after 66 days of <a href=\"https://wikipedia.org/wiki/Hunger_strike\" title=\"Hunger strike\">hunger-striking</a>, aged 27.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in the <a href=\"https://wikipedia.org/wiki/Long_Kesh\" class=\"mw-redirect\" title=\"Long Kesh\"><PERSON></a> prison hospital after 66 days of <a href=\"https://wikipedia.org/wiki/Hunger_strike\" title=\"Hunger strike\">hunger-striking</a>, aged 27.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>sh"}, {"title": "Hunger strike", "link": "https://wikipedia.org/wiki/Hunger_strike"}]}, {"year": "1985", "text": "<PERSON> visits the military cemetery at Bitburg and the site of the Bergen-Belsen concentration camp, where he makes a speech.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Bitburg_controversy\" title=\"Bitburg controversy\">visits</a> the military cemetery at <a href=\"https://wikipedia.org/wiki/Bitburg\" title=\"Bitburg\">Bitburg</a> and the site of the <a href=\"https://wikipedia.org/wiki/Bergen-Belsen_concentration_camp\" title=\"Bergen-Belsen concentration camp\">Bergen-Belsen concentration camp</a>, where he makes a speech.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Bitburg_controversy\" title=\"Bitburg controversy\">visits</a> the military cemetery at <a href=\"https://wikipedia.org/wiki/Bitburg\" title=\"Bitburg\">Bitburg</a> and the site of the <a href=\"https://wikipedia.org/wiki/Bergen-Belsen_concentration_camp\" title=\"Bergen-Belsen concentration camp\">Bergen-Belsen concentration camp</a>, where he makes a speech.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bitburg controversy", "link": "https://wikipedia.org/wiki/Bitburg_controversy"}, {"title": "Bitburg", "link": "https://wikipedia.org/wiki/Bitburg"}, {"title": "Bergen-Belsen concentration camp", "link": "https://wikipedia.org/wiki/Bergen-Belsen_concentration_camp"}]}, {"year": "1987", "text": "Iran-Contra affair: Start of Congressional televised hearings in the United States of America", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: Start of <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congressional</a> televised hearings in the United States of America", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: Start of <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congressional</a> televised hearings in the United States of America", "links": [{"title": "Iran-Contra affair", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1991", "text": "A riot breaks out in the Mt. Pleasant section of Washington, D.C. after police shoot a Salvadoran man.", "html": "1991 - A <a href=\"https://wikipedia.org/wiki/1991_Washington,_D.C._riot\" class=\"mw-redirect\" title=\"1991 Washington, D.C. riot\">riot</a> breaks out in the <a href=\"https://wikipedia.org/wiki/Mount_Pleasant,_Washington,_D.C.\" class=\"mw-redirect\" title=\"Mount Pleasant, Washington, D.C.\">Mt. Pleasant</a> section of Washington, D.C. after police shoot a <a href=\"https://wikipedia.org/wiki/Salvadoran\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> man.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1991_Washington,_D.C._riot\" class=\"mw-redirect\" title=\"1991 Washington, D.C. riot\">riot</a> breaks out in the <a href=\"https://wikipedia.org/wiki/Mount_Pleasant,_Washington,_D.C.\" class=\"mw-redirect\" title=\"Mount Pleasant, Washington, D.C.\">Mt. Pleasant</a> section of Washington, D.C. after police shoot a <a href=\"https://wikipedia.org/wiki/Salvadoran\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> man.", "links": [{"title": "1991 Washington, D.C. riot", "link": "https://wikipedia.org/wiki/1991_Washington,_D.C._riot"}, {"title": "Mount Pleasant, Washington, D.C.", "link": "https://wikipedia.org/wiki/Mount_Pleasant,_Washington,_D.C."}, {"title": "Salvadoran", "link": "https://wikipedia.org/wiki/Salvadoran"}]}, {"year": "1994", "text": "The signing of the Bishkek Protocol between Armenia and Azerbaijan effectively freezes the Nagorno-Karabakh conflict.", "html": "1994 - The signing of the <a href=\"https://wikipedia.org/wiki/Bishkek_Protocol\" title=\"Bishkek Protocol\">Bishkek Protocol</a> between <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a> and <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> effectively freezes the <a href=\"https://wikipedia.org/wiki/Nagorno-Karabakh_conflict\" title=\"Nagorno-Karabakh conflict\">Nagorno-Karabakh conflict</a>.", "no_year_html": "The signing of the <a href=\"https://wikipedia.org/wiki/Bishkek_Protocol\" title=\"Bishkek Protocol\">Bishkek Protocol</a> between <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a> and <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> effectively freezes the <a href=\"https://wikipedia.org/wiki/Nagorno-Karabakh_conflict\" title=\"Nagorno-Karabakh conflict\">Nagorno-Karabakh conflict</a>.", "links": [{"title": "Bishkek Protocol", "link": "https://wikipedia.org/wiki/Bishkek_Protocol"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}, {"title": "Nagorno-Karabakh conflict", "link": "https://wikipedia.org/wiki/Nagorno-Karabakh_conflict"}]}, {"year": "1994", "text": "American teenager <PERSON> is caned in Singapore for theft and vandalism.", "html": "1994 - American teenager <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Caning_in_Singapore\" title=\"Caning in Singapore\">caned</a> in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> for theft and vandalism.", "no_year_html": "American teenager <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Caning_in_Singapore\" title=\"Caning in Singapore\">caned</a> in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> for theft and vandalism.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Caning in Singapore", "link": "https://wikipedia.org/wiki/Caning_in_Singapore"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "2006", "text": "The government of Sudan signs an accord with the Sudan Liberation Army.", "html": "2006 - The government of <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a> signs an accord with the <a href=\"https://wikipedia.org/wiki/Sudan_Liberation_Army\" class=\"mw-redirect\" title=\"Sudan Liberation Army\">Sudan Liberation Army</a>.", "no_year_html": "The government of <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a> signs an accord with the <a href=\"https://wikipedia.org/wiki/Sudan_Liberation_Army\" class=\"mw-redirect\" title=\"Sudan Liberation Army\">Sudan Liberation Army</a>.", "links": [{"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}, {"title": "Sudan Liberation Army", "link": "https://wikipedia.org/wiki/Sudan_Liberation_Army"}]}, {"year": "2007", "text": "Kenya Airways Flight 507 crashes after takeoff from Douala International Airport in Douala, Cameroon, killing all 114 aboard, making it the deadliest aircraft disaster in Cameroon.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Kenya_Airways_Flight_507\" title=\"Kenya Airways Flight 507\">Kenya Airways Flight 507</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Douala_International_Airport\" title=\"Douala International Airport\">Douala International Airport</a> in <a href=\"https://wikipedia.org/wiki/Douala\" title=\"Douala\">Douala</a>, Cameroon, killing all 114 aboard, making it the deadliest aircraft disaster in Cameroon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenya_Airways_Flight_507\" title=\"Kenya Airways Flight 507\">Kenya Airways Flight 507</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Douala_International_Airport\" title=\"Douala International Airport\">Douala International Airport</a> in <a href=\"https://wikipedia.org/wiki/Douala\" title=\"Douala\">Douala</a>, Cameroon, killing all 114 aboard, making it the deadliest aircraft disaster in Cameroon.", "links": [{"title": "Kenya Airways Flight 507", "link": "https://wikipedia.org/wiki/Kenya_Airways_Flight_507"}, {"title": "Douala International Airport", "link": "https://wikipedia.org/wiki/Douala_International_Airport"}, {"title": "Douala", "link": "https://wikipedia.org/wiki/Douala"}]}, {"year": "2010", "text": "Mass protests in Greece erupt in response to austerity measures imposed by the government as a result of the Greek government-debt crisis.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Anti-austerity_movement_in_Greece\" title=\"Anti-austerity movement in Greece\">Mass protests</a> in Greece erupt in response to <a href=\"https://wikipedia.org/wiki/Austerity\" title=\"Austerity\">austerity</a> measures imposed by the government as a result of the <a href=\"https://wikipedia.org/wiki/Greek_government-debt_crisis\" title=\"Greek government-debt crisis\">Greek government-debt crisis</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anti-austerity_movement_in_Greece\" title=\"Anti-austerity movement in Greece\">Mass protests</a> in Greece erupt in response to <a href=\"https://wikipedia.org/wiki/Austerity\" title=\"Austerity\">austerity</a> measures imposed by the government as a result of the <a href=\"https://wikipedia.org/wiki/Greek_government-debt_crisis\" title=\"Greek government-debt crisis\">Greek government-debt crisis</a>.", "links": [{"title": "Anti-austerity movement in Greece", "link": "https://wikipedia.org/wiki/Anti-austerity_movement_in_Greece"}, {"title": "Austerity", "link": "https://wikipedia.org/wiki/Austerity"}, {"title": "Greek government-debt crisis", "link": "https://wikipedia.org/wiki/Greek_government-debt_crisis"}]}, {"year": "2023", "text": "The World Health Organization declares the end of the COVID-19 pandemic as a global health emergency.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> declares the end of the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a> as a global health emergency.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> declares the end of the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a> as a global health emergency.", "links": [{"title": "World Health Organization", "link": "https://wikipedia.org/wiki/World_Health_Organization"}, {"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}]}], "Births": [{"year": "1210", "text": "<PERSON><PERSON><PERSON> of Portugal (d. 1279)", "html": "1210 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Portugal\" title=\"<PERSON><PERSON><PERSON> III of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (d. 1279)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Portugal\" title=\"<PERSON><PERSON><PERSON> III of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (d. 1279)", "links": [{"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/A<PERSON>nso_III_of_Portugal"}]}, {"year": "1282", "text": "<PERSON>, Prince of Villena (d. 1348)", "html": "1282 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Villena\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Villena\"><PERSON>, Prince of Villena</a> (d. 1348)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Villena\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Villena\"><PERSON>, Prince of Villena</a> (d. 1348)", "links": [{"title": "<PERSON>, Prince of Villena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Villena"}]}, {"year": "1310", "text": "<PERSON><PERSON><PERSON> of Pogarell, Cardinal and Bishop of Wrocław (d. 1376)", "html": "1310 - <a href=\"https://wikipedia.org/wiki/Precz<PERSON>_of_Pogarell\" class=\"mw-redirect\" title=\"Precz<PERSON> of Pogarell\"><PERSON><PERSON><PERSON> of Pogarell</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_(Catholicism)\" class=\"mw-redirect\" title=\"<PERSON> (Catholicism)\">Cardinal</a> and <a href=\"https://wikipedia.org/wiki/Bishop_of_Wroc%C5%82aw\" title=\"Bishop of Wrocław\">Bishop of Wrocław</a> (d. 1376)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Precz<PERSON>_of_Pogarell\" class=\"mw-redirect\" title=\"Precz<PERSON> of Pogarell\"><PERSON><PERSON><PERSON> of Pogarell</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_(Catholicism)\" class=\"mw-redirect\" title=\"<PERSON> (Catholicism)\">Cardinal</a> and <a href=\"https://wikipedia.org/wiki/Bishop_of_Wroc%C5%82aw\" title=\"Bishop of Wrocław\">Bishop of Wrocław</a> (d. 1376)", "links": [{"title": "Preczlaw of Pogarell", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Pogarell"}, {"title": "<PERSON> (Catholicism)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholicism)"}, {"title": "Bishop of Wrocław", "link": "https://wikipedia.org/wiki/Bishop_of_Wroc%C5%82aw"}]}, {"year": "1352", "text": "<PERSON> of Germany, Count <PERSON><PERSON> of the Rhine (d. 1410)", "html": "1352 - <a href=\"https://wikipedia.org/wiki/Rupert_of_Germany\" class=\"mw-redirect\" title=\"Rupert of Germany\"><PERSON> of Germany</a>, Count <PERSON> of the Rhine (d. 1410)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rupert_of_Germany\" class=\"mw-redirect\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a>, Count <PERSON> of the Rhine (d. 1410)", "links": [{"title": "Rupert of Germany", "link": "https://wikipedia.org/wiki/Rupert_of_Germany"}]}, {"year": "1479", "text": "<PERSON>, Indian 3rd Sikh Guru (d. 1574)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Indian 3rd <a href=\"https://wikipedia.org/wiki/Sikh_Guru\" class=\"mw-redirect\" title=\"Sikh Guru\">Sikh Guru</a> (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Indian 3rd <a href=\"https://wikipedia.org/wiki/Sikh_Guru\" class=\"mw-redirect\" title=\"Sikh Guru\">Sikh Guru</a> (d. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sikh Guru", "link": "https://wikipedia.org/wiki/Sikh_Guru"}]}, {"year": "1504", "text": "<PERSON><PERSON><PERSON>, Polish cardinal (d. 1579)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (d. 1579)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1530", "text": "<PERSON>, comte de <PERSON>, French nobleman (d. 1574)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte de <PERSON>\"><PERSON>, comte de <PERSON></a>, French nobleman (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte de <PERSON>\"><PERSON>, comte de <PERSON></a>, French nobleman (d. 1574)", "links": [{"title": "<PERSON>, comte de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>"}]}, {"year": "1542", "text": "<PERSON>, 1st Earl of Exeter, English soldier and politician, Lord Lieutenant of Northamptonshire (d. 1623)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Exeter\" title=\"<PERSON>, 1st Earl of Exeter\"><PERSON>, 1st Earl of Exeter</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Northamptonshire\" title=\"Lord Lieutenant of Northamptonshire\">Lord Lieutenant of Northamptonshire</a> (d. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Exeter\" title=\"<PERSON>, 1st Earl of Exeter\"><PERSON>, 1st Earl of Exeter</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Northamptonshire\" title=\"Lord Lieutenant of Northamptonshire\">Lord Lieutenant of Northamptonshire</a> (d. 1623)", "links": [{"title": "<PERSON>, 1st Earl of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Exeter"}, {"title": "Lord Lieutenant of Northamptonshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Northamptonshire"}]}, {"year": "1582", "text": "<PERSON>, Duke of Württemberg (d. 1628)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a> (d. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON>, Duke of Württemberg\"><PERSON>, Duke of Württemberg</a> (d. 1628)", "links": [{"title": "<PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg"}]}, {"year": "1684", "text": "<PERSON><PERSON>, French wife of <PERSON><PERSON> (d. 1739)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>_d%27Aubign%C3%A9\" title=\"<PERSON><PERSON>Aubign<PERSON>\"><PERSON><PERSON></a>, French wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>%27Aubign%C3%A9\" title=\"<PERSON><PERSON>Aubign<PERSON>\"><PERSON><PERSON></a>, French wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1739)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_Charlotte_d%27Aubign%C3%A9"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON>, Holy Roman Emperor (d. 1792)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1792)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1749", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French pianist and composer (d. 1794)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French pianist and composer (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French pianist and composer (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, Scottish general and politician (d. 1812)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish general and politician (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish general and politician (d. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, French publisher (d. 1864)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Hachette\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French publisher (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Hachette\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French publisher (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON>ois_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON>, Danish philosopher and author (d. 1855)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish philosopher and author (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish philosopher and author (d. 1855)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1818", "text": "<PERSON>, German philosopher, sociologist, and journalist (d. 1883)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, sociologist, and journalist (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, sociologist, and journalist (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON><PERSON><PERSON><PERSON>, French wife of <PERSON> (d. 1920)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A9<PERSON>_de_Montijo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French wife of <a href=\"https://wikipedia.org/wiki/Napoleon_III\" title=\"Napoleon III\"><PERSON> III</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A9<PERSON>_de_Montijo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French wife of <a href=\"https://wikipedia.org/wiki/Napoleon_III\" title=\"Napoleon III\"><PERSON> III</a> (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A9nie_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_III"}]}, {"year": "1830", "text": "<PERSON>, American businessman, founded the John B. Stetson Company (d. 1906)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stetson\" class=\"mw-redirect\" title=\"<PERSON> Stetson\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/John_B._Stetson_Company\" title=\"John B. Stetson Company\">John B. Stetson Company</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>on\" class=\"mw-redirect\" title=\"<PERSON> Stetson\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/John_<PERSON>_Stetson_Company\" title=\"John B. Stetson Company\">John B. St<PERSON>on Company</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "John B. <PERSON>on Company", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>on_Company"}]}, {"year": "1832", "text": "<PERSON>, American ethnologist and historian (d. 1918)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ethnologist and historian (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ethnologist and historian (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, German geographer and academic (d. 1905)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and academic (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and academic (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, Russian painter and architect (d. 1873)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and architect (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and architect (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, Canadian dentist and patriot (d. 1900)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian dentist and patriot (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian dentist and patriot (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON><PERSON>, Polish journalist and author, Nobel Prize laureate (d. 1916)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1858", "text": "<PERSON>, American physician (d. 1914)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, American Shakespearean actor (d. 1926)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Shakespearean actor (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Shakespearean actor (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON>, American journalist and author (d. 1922)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American litterateur and poet (d. 1943)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American litterateur and poet (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American litterateur and poet (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Danish businessman (d. 1938)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hrige\" title=\"<PERSON>\"><PERSON></a>, Danish businessman (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Thrige\" title=\"<PERSON>hrige\"><PERSON></a>, Danish businessman (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hrige"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Filipino painter and educator (d. 1937)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_de_la_Rosa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino painter and educator (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_de_la_Rosa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino painter and educator (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabi%C3%A1<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, German composer and conductor (d. 1949)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": " <PERSON>, American assassin of <PERSON> (d. 1901)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, New Zealand-Australian politician, 24th Premier of New South Wales (d. 1941)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1882", "text": "<PERSON>, English women's suffrage movement leader and socialist activist (d. 1960)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English women's suffrage movement leader and socialist activist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English women's suffrage movement leader and socialist activist (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, 1st <PERSON>, English general and politician, 43rd Governor-General of India (d. 1950)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st Earl <PERSON></a>, English general and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English general and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (d. 1950)", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>"}, {"title": "Governor-General of India", "link": "https://wikipedia.org/wiki/Governor-General_of_India"}]}, {"year": "1883", "text": "<PERSON>, American mathematician (d. 1966)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "Chief <PERSON>, American baseball player and coach (d. 1954)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>\" title=\"Chief <PERSON>er\">Chief <PERSON><PERSON></a>, American baseball player and coach (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>\" title=\"Chief <PERSON>er\">Chief <PERSON><PERSON></a>, American baseball player and coach (d. 1954)", "links": [{"title": "Chief <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON><PERSON>"}]}, {"year": "1885", "text": "<PERSON>, South African-Australian scholar and politician  (d. 1924)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian scholar and politician (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian scholar and politician (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, American captain, Medal of Honor recipient (d. 1941)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American captain, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1889", "text": "<PERSON><PERSON>, South African cricketer and soldier (d. 1973)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and soldier (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and soldier (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American journalist and author (d. 1957)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, British archaeologist (d. 1968)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British archaeologist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British archaeologist (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American engineer (d. 1983)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON> <PERSON>, American Piedmont blues singer and guitar player (d. 1959)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\">Blind <PERSON></a>, American Piedmont blues singer and guitar player (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, American Piedmont blues singer and guitar player (d. 1959)", "links": [{"title": "Blind <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American actor and screenwriter (d. 1982)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Freeman_Go<PERSON>den"}]}, {"year": "1900", "text": "<PERSON>, American geneticist (d. 1988)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Finnish politician (d. 1964)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American chef and author (d. 1985)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American author and illustrator (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (Ukrainian) journalist and author (d. 1982)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON><PERSON><PERSON><PERSON> (Ukrainian) journalist and author (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON><PERSON><PERSON><PERSON> (Ukrainian) journalist and author (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American author and illustrator (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Russian-Hungarian chess player (d. 2010)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Hungarian chess player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Hungarian chess player (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian educator and activist (d. 1932)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Pritilata_Waddedar\" title=\"<PERSON>rit<PERSON><PERSON> Waddedar\"><PERSON><PERSON><PERSON><PERSON></a>, Indian educator and activist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prit<PERSON><PERSON>_Waddedar\" title=\"<PERSON>rit<PERSON><PERSON> Waddedar\"><PERSON><PERSON><PERSON><PERSON></a>, Indian educator and activist (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pritilata_Waddedar"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American race car driver (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 1958)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Power\"><PERSON></a>, American actor (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Power\" title=\"Tyrone Power\"><PERSON></a>, American actor (d. 1958)", "links": [{"title": "Tyrone Power", "link": "https://wikipedia.org/wiki/Tyrone_Power"}]}, {"year": "1915", "text": "<PERSON>, American actress and singer (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Indian politician, 7th President of India (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Greek colonel and politician, 169th Prime Minister of Greece (d. 1999)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek colonel and politician, 169th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek colonel and politician, 169th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1921", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1922", "text": "<PERSON>, Polish nurse and humanitarian (d. 2003)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish nurse and humanitarian (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish nurse and humanitarian (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American golfer (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2013)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(golfer)"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Canadian mathematician (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Cathleen_Synge_Morawetz\" title=\"Cathleen Synge Morawetz\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian mathematician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cathleen_Synge_Morawetz\" title=\"Cathleen Synge Morawetz\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian mathematician (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cathleen_Synge_Moraw<PERSON>z"}]}, {"year": "1925", "text": "<PERSON>, American soldier, educator, and politician (d. 1978)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, educator, and politician (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, educator, and politician (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actress (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 2022)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American actress (d. 2010)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Woods"}]}, {"year": "1932", "text": "<PERSON>, American illustrator (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Jamaican cricketer (d. 1959)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican cricketer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican cricketer (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Ivorian politician, 2nd President of Côte d'Ivoire (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9di%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Ivorian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire\" class=\"mw-redirect\" title=\"President of Côte d'Ivoire\">President of Côte d'Ivoire</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9di%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Ivorian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire\" class=\"mw-redirect\" title=\"President of Côte d'Ivoire\">President of Côte d'Ivoire</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_B%C3%A9di%C3%A9"}, {"title": "President of Côte d'Ivoire", "link": "https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire"}]}, {"year": "1934", "text": "<PERSON>, Australian accountant and politician, 26th Australian Minister for Veterans' Affairs (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian accountant and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_for_Veterans%27_Affairs\" title=\"Minister for Veterans' Affairs\">Australian Minister for Veterans' Affairs</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian accountant and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_for_Veterans%27_Affairs\" title=\"Minister for Veterans' Affairs\">Australian Minister for Veterans' Affairs</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Veterans' Affairs", "link": "https://wikipedia.org/wiki/Minister_for_Veterans%27_Affairs"}]}, {"year": "1935", "text": "<PERSON>, Scottish poet and magazine editor (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and magazine editor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and magazine editor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, French journalist, talk show host, and producer (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, talk show host, and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, talk show host, and producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor and comedian (d. 2001)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, English racing cyclist (d. 1996) ", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English racing cyclist (d. 1996) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English racing cyclist (d. 1996) ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, English musician, arranger and composer (d. 2001)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Delia_<PERSON>\" title=\"Delia Derbyshire\"><PERSON><PERSON></a>, English musician, arranger and composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delia_Derbyshire\" title=\"Delia Derbyshire\"><PERSON><PERSON></a>, English musician, arranger and composer (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Delia_Derbyshire"}]}, {"year": "1938", "text": "<PERSON>, American actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1939", "text": "<PERSON>, English journalist, author, and activist (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and activist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, <PERSON>, English lawyer and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON><PERSON><PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1998)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English actor and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Spanish journalist and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Swedish footballer (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Welsh actor and screenwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Welsh-American actor and director (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American actor and director (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American actor and director (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American journalist, author, and critic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor, athlete, and martial artist (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/1946\" title=\"1946\">1946</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martial_artist)\" title=\"<PERSON> (martial artist)\"><PERSON></a>, American actor, athlete, and martial artist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1946\" title=\"1946\">1946</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martial_artist)\" title=\"<PERSON> (martial artist)\"><PERSON></a>, American actor, athlete, and martial artist (d. 2013)", "links": [{"title": "1946", "link": "https://wikipedia.org/wiki/1946"}, {"title": "<PERSON> (martial artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martial_artist)"}]}, {"year": "1948", "text": "<PERSON>, English drummer and songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1950", "text": "<PERSON>, Dutch singer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American politician and attorney, 43rd Mayor of San Francisco (d. 2017)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician and attorney, 43rd <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician and attorney, 43rd <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (d. 2017)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Mayor of San Francisco", "link": "https://wikipedia.org/wiki/Mayor_of_San_Francisco"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter, guitarist, and freelance multimedia producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and freelance multimedia producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and freelance multimedia producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American runner and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and coach", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1957", "text": "<PERSON>, Swazi-English actor, director, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swazi-English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swazi-English actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Australian footballer and sportscaster", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ierdomenico"}]}, {"year": "1959", "text": "<PERSON>, American singer and bass player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1959", "text": "<PERSON>, American journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian footballer and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Australian actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marg_<PERSON><PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1963", "text": "<PERSON>, Canadian singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English chef and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American author and composer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, French politician, French Minister of Budget", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7ois_Cop%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, French Minister of Budget", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7ois_Cop%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, French Minister of Budget", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_Cop%C3%A9"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German high jumper", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"He<PERSON> Henkel\"><PERSON><PERSON></a>, German high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"He<PERSON> Henkel\"><PERSON><PERSON></a>, German high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American screenwriter and producer (d. 2013)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (d. 2013)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian drummer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Bulgarian politician, 46th Prime Minister of Bulgaria", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bulgarian politician, 46th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bulgarian politician, 46th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Bulgaria", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria"}]}, {"year": "1966", "text": "<PERSON>, American screenwriter and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American author and illustrator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Burundian journalist and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burundian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burundian journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English rower", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Slovakian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/%C5%BDigmund_P%C3%A1lffy\" title=\"Žig<PERSON> Pálffy\"><PERSON><PERSON><PERSON></a>, Slovakian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%BDigmund_P%C3%A1lffy\" title=\"Žig<PERSON> Pálffy\"><PERSON><PERSON><PERSON></a>, Slovakian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%BDigmund_P%C3%A1lffy"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Swedish ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American runner", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Australian actor (d. 2021)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actor (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Argentinian footballer and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADn"}]}, {"year": "1977", "text": "<PERSON>, American footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American entrepreneur, educator, and vlogger", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, educator, and vlogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Green\"><PERSON></a>, American entrepreneur, educator, and vlogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English singer-songwriter, musician and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, musician and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, musician and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American philanthropist and model", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian rugby league footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league footballer", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1983", "text": "<PERSON>, English actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Japanese actress and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Scottish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adele"}]}, {"year": "1989", "text": "<PERSON>, American singer-songwriter, dancer, and actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Swedish curler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Jim%C3%A9nez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Jim%C3%A9nez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_Jim%C3%A9nez"}]}, {"year": "1994", "text": "<PERSON>, British singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1996", "text": "<PERSON>, American tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Egyptian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Belarusian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American figure skater", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Dutch footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Spanish tennis player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Scottish freestyle skier", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish freestyle skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish freestyle skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "465", "text": "<PERSON><PERSON><PERSON>, Archbishop of Milan", "html": "465 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Milan)\" title=\"<PERSON><PERSON><PERSON> (bishop of Milan)\"><PERSON><PERSON><PERSON></a>, Archbishop of Milan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Milan)\" title=\"<PERSON><PERSON><PERSON> (bishop of Milan)\"><PERSON><PERSON><PERSON></a>, Archbishop of Milan", "links": [{"title": "<PERSON><PERSON><PERSON> (bishop of Milan)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Milan)"}]}, {"year": "1194", "text": "<PERSON> the <PERSON>, Polish son of <PERSON><PERSON><PERSON> (b. 1138)", "html": "1194 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_the_Just\" title=\"<PERSON> II the Just\"><PERSON> II the Just</a>, Polish son of <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_III_Wrymouth\" title=\"<PERSON><PERSON><PERSON> III Wrymouth\"><PERSON><PERSON><PERSON> III Wrymouth</a> (b. 1138)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_the_Just\" title=\"Casimir II the Just\"><PERSON> II the Just</a>, Polish son of <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_III_Wrymouth\" title=\"<PERSON><PERSON><PERSON> III Wrymouth\"><PERSON><PERSON><PERSON> III Wrymouth</a> (b. 1138)", "links": [{"title": "<PERSON> the Just", "link": "https://wikipedia.org/wiki/<PERSON>_II_the_Just"}, {"title": "<PERSON><PERSON><PERSON> III Wrymouth", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_III_Wrymouth"}]}, {"year": "1243", "text": "<PERSON>, 1st Earl of Kent, English justiciar  (b. c. 1160)", "html": "1243 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Kent\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl <PERSON> Kent\"><PERSON>, 1st Earl <PERSON> Kent</a>, English <a href=\"https://wikipedia.org/wiki/Justiciar\" title=\"Justiciar\">justiciar</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1160</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Kent\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl <PERSON> Kent\"><PERSON>, 1st Earl of Kent</a>, English <a href=\"https://wikipedia.org/wiki/Justiciar\" title=\"Justiciar\">justiciar</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1160</span>)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Kent"}, {"title": "Just<PERSON>ar", "link": "https://wikipedia.org/wiki/Justiciar"}]}, {"year": "1306", "text": "<PERSON>, Byzantine general (b. 1261)", "html": "1306 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(son_of_<PERSON>_<PERSON>)\" title=\"<PERSON> (son of <PERSON>)\"><PERSON></a>, Byzantine general (b. 1261)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(son_of_<PERSON>_<PERSON>)\" title=\"<PERSON> (son of <PERSON>)\"><PERSON></a>, Byzantine general (b. 1261)", "links": [{"title": "<PERSON> (son of <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(son_of_<PERSON>_<PERSON>)"}]}, {"year": "1309", "text": "<PERSON> of Naples (b. 1254)", "html": "1309 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1254)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1254)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples"}]}, {"year": "1316", "text": "<PERSON> of Rhuddlan, daughter of King <PERSON> of England (b. 1282)", "html": "1316 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Rhuddlan\" title=\"<PERSON> of Rhuddlan\"><PERSON> of Rhuddlan</a>, daughter of King <PERSON> of England (b. 1282)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rhuddlan\" title=\"<PERSON> of Rhuddlan\"><PERSON> of Rhuddlan</a>, daughter of King <PERSON> of England (b. 1282)", "links": [{"title": "<PERSON> of Rhuddlan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1338", "text": "Prince <PERSON><PERSON><PERSON><PERSON>, son of the Japanese Emperor (b. 1324)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON><PERSON></a>, son of the Japanese Emperor (b. 1324)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, son of the Japanese Emperor (b. 1324)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1380", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, Coptic martyr", "html": "1380 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Philotheos\" title=\"Saint Philotheos\"><PERSON></a>, Coptic martyr", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Philotheos\" title=\"Saint Philotheos\"><PERSON> Phil<PERSON>eo<PERSON></a>, Coptic martyr", "links": [{"title": "Saint Philotheos", "link": "https://wikipedia.org/wiki/Saint_Philotheos"}]}, {"year": "1432", "text": "<PERSON> Carmagnola, Italian adventurer", "html": "1432 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_da_Carmagnola\" title=\"<PERSON> Bus<PERSON>e da Carmagnola\"><PERSON> Carmagnola</a>, Italian adventurer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_da_Carmagnola\" title=\"<PERSON> Bus<PERSON>e da Carmagnola\"><PERSON> Carmagnola</a>, Italian adventurer", "links": [{"title": "<PERSON> Carmagnola", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1525", "text": "<PERSON>, Elector of Saxony (b. 1463)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1463)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1582", "text": "<PERSON> of Bourbon, Princess consort of Orange, married to <PERSON> Orange (b. 1547)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bourbon\" title=\"<PERSON> of Bourbon\"><PERSON> of Bourbon</a>, Princess consort of Orange, married to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange\" class=\"mw-redirect\" title=\"<PERSON> I of Orange\"><PERSON> of Orange</a> (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bourbon\" title=\"<PERSON> of Bourbon\"><PERSON> of Bourbon</a>, Princess consort of Orange, married to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange\" class=\"mw-redirect\" title=\"<PERSON> I of Orange\"><PERSON> of Orange</a> (b. 1547)", "links": [{"title": "<PERSON> of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bourbon"}, {"title": "<PERSON> Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange"}]}, {"year": "1586", "text": "<PERSON>, Irish politician, Lord Deputy of Ireland (b. 1529)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Deputy_of_Ireland\" title=\"Lord Deputy of Ireland\">Lord Deputy of Ireland</a> (b. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Deputy_of_Ireland\" title=\"Lord Deputy of Ireland\">Lord Deputy of Ireland</a> (b. 1529)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Deputy of Ireland", "link": "https://wikipedia.org/wiki/Lord_Deputy_of_Ireland"}]}, {"year": "1671", "text": "<PERSON>, 2nd Earl of Manchester, English general and politician, Lord Chamberlain of the United Kingdom (b. 1602)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Manchester\" title=\"<PERSON>, 2nd Earl of Manchester\"><PERSON>, 2nd Earl of Manchester</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chamberlain\" title=\"Lord Chamberlain\">Lord Chamberlain of the United Kingdom</a> (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Manchester\" title=\"<PERSON>, 2nd Earl of Manchester\"><PERSON>, 2nd Earl of Manchester</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chamberlain\" title=\"Lord Chamberlain\">Lord Chamberlain of the United Kingdom</a> (b. 1602)", "links": [{"title": "<PERSON>, 2nd Earl of Manchester", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Manchester"}, {"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, English painter and linguist (b. 1609)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter and linguist (b. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter and linguist (b. 1609)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_(painter)"}]}, {"year": "1700", "text": "<PERSON>, Italian architect (b. 1628)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/Angelo_Italia\" title=\"Angelo Italia\"><PERSON></a>, Italian architect (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Angelo_Italia\" title=\"Angelo Italia\"><PERSON></a>, Italian architect (b. 1628)", "links": [{"title": "Angelo Italia", "link": "https://wikipedia.org/wiki/Angelo_Italia"}]}, {"year": "1705", "text": "<PERSON>, Holy Roman Emperor (b. 1640)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> I, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1640)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1760", "text": "<PERSON>, 4th Earl <PERSON>, English politician (b. 1720)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th Earl <PERSON></a>, English politician (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th <PERSON></a>, English politician (b. 1720)", "links": [{"title": "<PERSON>, 4th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, French physician and scholar (b. 1684)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and scholar (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and scholar (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, French physiologist and philosopher (b. 1757)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French physiologist and philosopher (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French physiologist and philosopher (b. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, French general and emperor (b. 1769)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and emperor (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and emperor (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1827", "text": "<PERSON> of Saxony (b. 1750)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a> (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a> (b. 1750)", "links": [{"title": "<PERSON> of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony"}]}, {"year": "1833", "text": "<PERSON>, English-Australian painter (b. 1777)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian painter (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian painter (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "Sir <PERSON>, 2nd Baronet, English politician (b. 1786)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politician (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politician (b. 1786)", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet"}]}, {"year": "1859", "text": "<PERSON>, German mathematician and academic (b. 1805)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Canadian bishop (b. 1804)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (b. 1804)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Irish-Australian politician, 2nd Premier of Victoria (b. 1818)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shanassy\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shanassy\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_O%27Shanassy"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1892", "text": "<PERSON> <PERSON>, German chemist and academic (b. 1818)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German chemist and academic (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German chemist and academic (b. 1818)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American lawyer and politician (b. 1839)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Peruvian general, twice President of Peru (b. 1825)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian general, twice President of Peru (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian general, twice President of Peru (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, American short story writer and poet (b. 1836)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer and poet (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer and poet (b. 1836)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Turkish soldier and painter (b. 1841)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/%C5%9<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish soldier and painter (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish soldier and painter (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%9<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, French painter (b. 1856)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Irish soldier and rebel (b. 1865)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish soldier and rebel (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish soldier and rebel (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, French polo player (b. 1866)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French polo player (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French polo player (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Austrian journalist and publicist, Nobel Prize laureate (b. 1864)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist and publicist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist and publicist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Sri Lankan journalist and politician (b. 1853)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist and politician (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist and politician (b. 1853)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English pilot and race car driver (b. 1899)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ton\" title=\"Glen Kidston\"><PERSON></a>, English pilot and race car driver (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glen_<PERSON>ton\" title=\"Glen Kidston\"><PERSON></a>, English pilot and race car driver (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON> of Banja Luka, Serbian Orthodox bishop (b. 1874)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Platon_of_Banja_Luka\" title=\"Platon of Banja Luka\">Platon of Banja Luka</a>, Serbian Orthodox bishop (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Platon_of_Banja_Luka\" title=\"Platon of Banja Luka\">Platon of Banja Luka</a>, Serbian Orthodox bishop (b. 1874)", "links": [{"title": "Platon of Banja Luka", "link": "https://wikipedia.org/wiki/Platon_of_Banja_Luka"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Albanian politician (b. 1920)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Q<PERSON>l_Stafa\" title=\"<PERSON><PERSON><PERSON> Stafa\"><PERSON><PERSON><PERSON></a>, Albanian politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Q<PERSON><PERSON>_Stafa\" title=\"<PERSON><PERSON><PERSON> Stafa\"><PERSON><PERSON><PERSON></a>, Albanian politician (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Q<PERSON>l_Stafa"}]}, {"year": "1947", "text": "<PERSON>, Canadian-American baseball player (b. 1917)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball player (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball player (b. 1917)", "links": [{"title": "Ty <PERSON>", "link": "https://wikipedia.org/wiki/Ty_LaForest"}]}, {"year": "1957", "text": "<PERSON>, German mathematician and logician (b. 1878)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Leopold_L%C3%B6wen<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and logician (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopold_L%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and logician (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_L%C3%B6wen<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Argentinian academic and politician, Nobel Prize laureate (b. 1878)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1962", "text": "<PERSON>, English cricketer (b. 1889)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Greek tenor and composer (b. 1915)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek tenor and composer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek tenor and composer (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American director and screenwriter (b. 1893)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director_born_1893)\" class=\"mw-redirect\" title=\"<PERSON> (director born 1893)\"><PERSON></a>, American director and screenwriter (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director_born_1893)\" class=\"mw-redirect\" title=\"<PERSON> (director born 1893)\"><PERSON></a>, American director and screenwriter (b. 1893)", "links": [{"title": "<PERSON> (director born 1893)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director_born_1893)"}]}, {"year": "1971", "text": "<PERSON>, Argentinean-English nurse (b. 1887)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jessop\"><PERSON></a>, Argentinean-English nurse (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jessop\"><PERSON></a>, Argentinean-English nurse (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Turkish poet and academic (b. 1948)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Zekai_%C3%96zger\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and academic (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zekai_%C3%96zger\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and academic (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zekai_%C3%96zger"}]}, {"year": "1977", "text": "<PERSON>, German economist and politician, Chancellor of Germany (b. 1897)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German economist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic)\">Chancellor of Germany</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German economist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic)\">Chancellor of Germany</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (Federal Republic)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)"}]}, {"year": "1981", "text": "<PERSON>, PIRA volunteer and hunger striker (b. 1954)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">PIRA volunteer</a> and hunger striker (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">PIRA volunteer</a> and hunger striker (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}]}, {"year": "1983", "text": "<PERSON><PERSON>, German physician (b. 1901)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ho<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English-American actor (b. 1903)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-American actor (b. 1903)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1985", "text": "<PERSON>, English engineer, designed the Bailey bridge (b. 1901)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(civil_engineer)\" title=\"<PERSON> (civil engineer)\"><PERSON></a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/<PERSON>_bridge\" title=\"<PERSON> bridge\"><PERSON> bridge</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(civil_engineer)\" title=\"<PERSON> (civil engineer)\"><PERSON></a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/<PERSON>_bridge\" title=\"<PERSON> bridge\"><PERSON> bridge</a> (b. 1901)", "links": [{"title": "<PERSON> (civil engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_(civil_engineer)"}, {"title": "<PERSON> bridge", "link": "https://wikipedia.org/wiki/<PERSON>_bridge"}]}, {"year": "1988", "text": "<PERSON>, American author and academic (b. 1928)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>,  American literary and social critic (b. 1920)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary and social critic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary and social critic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Brazilian poet and translator (b. 1906)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Quintana\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian poet and translator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Quintana\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian poet and translator (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rio_Quintana"}]}, {"year": "1995", "text": "<PERSON>, Russian chess player and coach (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and coach (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and coach (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Greek actor, director, and screenwriter (b. 1920)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor, director, and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor, director, and screenwriter (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Italian cyclist (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American basketball player and coach (b. 1940)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American painter and educator (b. 1910)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Graves\"><PERSON></a>, American painter and educator (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American publisher, created <PERSON><PERSON><PERSON><PERSON> (b. 1918)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Clifton_Hillegass\" title=\"Clifton Hillegass\"><PERSON> Hillegass</a>, American publisher, created <i><a href=\"https://wikipedia.org/wiki/CliffsNotes\" title=\"CliffsNotes\">CliffsNotes</a></i> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clifton_Hillegass\" title=\"Clifton Hillegass\"><PERSON></a>, American publisher, created <i><a href=\"https://wikipedia.org/wiki/CliffsNotes\" title=\"CliffsNotes\">CliffsNotes</a></i> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clifton_Hillegass"}, {"title": "CliffsNotes", "link": "https://wikipedia.org/wiki/CliffsNotes"}]}, {"year": "2002", "text": "<PERSON>, Bolivian general and politician, 62nd President of Bolivia (b. 1926)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 62nd <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 62nd <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "2002", "text": "<PERSON>, American engineer, founded Klipsch Audio Technologies (b. 1904)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, founded <a href=\"https://wikipedia.org/wiki/Klipsch_Audio_Technologies\" title=\"Klipsch Audio Technologies\">Klipsch Audio Technologies</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, founded <a href=\"https://wikipedia.org/wiki/Klipsch_Audio_Technologies\" title=\"Klipsch Audio Technologies\">Klipsch Audio Technologies</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Klipsch Audio Technologies", "link": "https://wikipedia.org/wiki/Klipsch_Audio_Technologies"}]}, {"year": "2002", "text": "<PERSON>, American director and producer (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American lawyer and politician (b. 1917)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>yman\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>._Wyman"}]}, {"year": "2003", "text": "<PERSON>, Sierra Leonean commander (b. 1964)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean commander (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean commander (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, South African activist and politician (b. 1912)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist and politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Indian composer and producer (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian composer and producer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian composer and producer (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Turkish director, producer, and screenwriter (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/At%C4%B1f_Y%C4%B1lmaz\" title=\"At<PERSON><PERSON> Yılmaz\"><PERSON><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/At%C4%B1f_Y%C4%B1lmaz\" title=\"At<PERSON><PERSON> Yılmaz\"><PERSON><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/At%C4%B1f_Y%C4%B1lmaz"}]}, {"year": "2007", "text": "<PERSON>, American-Canadian physicist and engineer, created the laser (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American-Canadian physicist and engineer, created the <a href=\"https://wikipedia.org/wiki/Laser\" title=\"Laser\">laser</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American-Canadian physicist and engineer, created the <a href=\"https://wikipedia.org/wiki/Laser\" title=\"Laser\">laser</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Laser", "link": "https://wikipedia.org/wiki/Laser"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Canadian-American businessman, co-founded <PERSON><PERSON><PERSON><PERSON> (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON>\" title=\"<PERSON>r<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON>\" title=\"<PERSON>r<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irv_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American singer and guitarist (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian soprano (b. 1910)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Giulietta_Simionato\" title=\"Giulietta Simionato\">Giulie<PERSON> Si<PERSON></a>, Italian soprano (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giulietta_Simionato\" title=\"Giulietta Simionato\">Giulie<PERSON></a>, Italian soprano (b. 1910)", "links": [{"title": "Giulietta Simionato", "link": "https://wikipedia.org/wiki/Giulietta_Simionato"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Nigerian academic and politician, 13th President of Nigeria (b. 1951)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Umaru_Musa_Yar%27Adua\" title=\"Um<PERSON> Musa Yar'Adua\"><PERSON><PERSON></a>, Nigerian academic and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umaru_Musa_Yar%27Adua\" title=\"Umaru Musa Yar'Adua\"><PERSON><PERSON></a>, Nigerian academic and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a> (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umaru_Musa_Yar%27Adua"}, {"title": "President of Nigeria", "link": "https://wikipedia.org/wiki/President_of_Nigeria"}]}, {"year": "2011", "text": "<PERSON>, English-Australian soldier (b. 1901)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian soldier (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian soldier (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Israeli footballer and manager (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, British actress (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(cricketer)\" title=\"<PERSON><PERSON><PERSON> (cricketer)\"><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(cricketer)\" title=\"<PERSON><PERSON><PERSON> (cricketer)\"><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(cricketer)"}]}, {"year": "2012", "text": "<PERSON>, Count of Wisborg (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Count of Wisborg (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Count of Wisborg (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Finnish journalist and publisher (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>s_E<PERSON>\" title=\"<PERSON><PERSON>s Erkko\"><PERSON><PERSON><PERSON></a>, Finnish journalist and publisher (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>s_<PERSON>\" title=\"<PERSON><PERSON>s Erkko\"><PERSON><PERSON><PERSON></a>, Finnish journalist and publisher (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aatos_E<PERSON>ko"}]}, {"year": "2012", "text": "<PERSON>, Dutch footballer, coach, and manager (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer, coach, and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer, coach, and manager (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, South African lawyer and politician, South African Minister of Communications (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, South African Minister of Communications (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, South African Minister of Communications (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German poet and author (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" class=\"mw-redirect\" title=\"<PERSON> (poet)\"><PERSON></a>, German poet and author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" class=\"mw-redirect\" title=\"<PERSON> (poet)\"><PERSON></a>, German poet and author (b. 1935)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "2013", "text": "<PERSON>, American FBI agent and author (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agent and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agent and author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}]}, {"year": "2014", "text": "<PERSON>, Nigerian journalist and politician, 9th Governor of Lagos State (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_Lagos_State\" title=\"Governor of Lagos State\">Governor of Lagos State</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_Lagos_State\" title=\"Governor of Lagos State\">Governor of Lagos State</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Lagos State", "link": "https://wikipedia.org/wiki/Governor_of_Lagos_State"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American cyclist, engineer, and author (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brandt\"><PERSON><PERSON></a>, American cyclist, engineer, and author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brandt\"><PERSON><PERSON></a>, American cyclist, engineer, and author (b. 1935)", "links": [{"title": "Job<PERSON> Brandt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Dutch linguist, academic, and politician (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch linguist, academic, and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch linguist, academic, and politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Israeli Orthodox rabbi and politician (b. 1954)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli Orthodox rabbi and politician (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli Orthodox rabbi and politician (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Mauritanian politician (b. 1953)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mauritanian politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mauritanian politician (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Jamaican singer-songwriter (b. 1947)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American stuntwoman and actress (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American stuntwoman and actress (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American stuntwoman and actress (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, English actor (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Argentine footballer and manager (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>_<PERSON>"}]}]}}