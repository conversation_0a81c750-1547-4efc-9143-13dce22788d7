{"date": "August 14", "url": "https://wikipedia.org/wiki/August_14", "data": {"Events": [{"year": "74 BC", "text": "A group of officials, led by the Western Han minister <PERSON><PERSON>, present articles of impeachment against the new emperor, <PERSON>, to the imperial regent, Empress <PERSON><PERSON>.", "html": "74 BC - 74 BC - A group of officials, led by the <a href=\"https://wikipedia.org/wiki/Han_dynasty#Western_Han\" title=\"Han dynasty\">Western Han</a> minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, present articles of impeachment against the new emperor, <a href=\"https://wikipedia.org/wiki/Marquis_of_Haihun\" title=\"Marquis of Haihun\"><PERSON></a>, to the imperial regent, <a href=\"https://wikipedia.org/wiki/Grand_Empress_<PERSON><PERSON>_<PERSON>\" title=\"Grand Empress <PERSON><PERSON>\">Empress <PERSON><PERSON></a>.", "no_year_html": "74 BC - A group of officials, led by the <a href=\"https://wikipedia.org/wiki/Han_dynasty#Western_Han\" title=\"Han dynasty\">Western Han</a> minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, present articles of impeachment against the new emperor, <a href=\"https://wikipedia.org/wiki/Marquis_of_Haihun\" title=\"Marquis of Haihun\"><PERSON></a>, to the imperial regent, <a href=\"https://wikipedia.org/wiki/Grand_Empress_<PERSON><PERSON>_<PERSON>\" title=\"Grand Empress <PERSON><PERSON>\">Empress <PERSON><PERSON></a>.", "links": [{"title": "Han dynasty", "link": "https://wikipedia.org/wiki/Han_dynasty#Western_Han"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Marquis of Haihun", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hai<PERSON>"}, {"title": "Grand Empress <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grand_Empress_<PERSON><PERSON>_<PERSON>"}]}, {"year": "29 BC", "text": "<PERSON><PERSON><PERSON> holds the second of three consecutive triumphs in Rome to celebrate the victory over the Dalmatian tribes.", "html": "29 BC - 29 BC - <a href=\"https://wikipedia.org/wiki/Octavian\" class=\"mw-redirect\" title=\"Octavian\">Octavian</a> holds the second of three consecutive <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumphs</a> in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> to celebrate the victory over the <a href=\"https://wikipedia.org/wiki/Dalmatian_tribes\" class=\"mw-redirect\" title=\"Dalmatian tribes\">Dalmatian tribes</a>.", "no_year_html": "29 BC - <a href=\"https://wikipedia.org/wiki/Octavian\" class=\"mw-redirect\" title=\"Octavian\">Octavian</a> holds the second of three consecutive <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumphs</a> in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> to celebrate the victory over the <a href=\"https://wikipedia.org/wiki/Dalmatian_tribes\" class=\"mw-redirect\" title=\"Dalmatian tribes\">Dalmatian tribes</a>.", "links": [{"title": "Octavian", "link": "https://wikipedia.org/wiki/Octavian"}, {"title": "Roman triumph", "link": "https://wikipedia.org/wiki/Roman_triumph"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "Dalmatian tribes", "link": "https://wikipedia.org/wiki/Dalmatian_tribes"}]}, {"year": "1040", "text": "King <PERSON> is killed in battle against his first cousin and rival <PERSON><PERSON>. The latter succeeds him as King of Scotland.", "html": "1040 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Scotland\" title=\"<PERSON> I of Scotland\"><PERSON> I</a> is killed in battle against his first cousin and rival <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland\" title=\"<PERSON><PERSON>, King of Scotland\"><PERSON><PERSON></a>. The latter succeeds him as <a href=\"https://wikipedia.org/wiki/List_of_Scottish_monarchs\" title=\"List of Scottish monarchs\">King of Scotland</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> I of Scotland\"><PERSON> I</a> is killed in battle against his first cousin and rival <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland\" title=\"<PERSON><PERSON>, King of Scotland\"><PERSON><PERSON></a>. The latter succeeds him as <a href=\"https://wikipedia.org/wiki/List_of_Scottish_monarchs\" title=\"List of Scottish monarchs\">King of Scotland</a>.", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}, {"title": "<PERSON><PERSON>, King of Scotland", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland"}, {"title": "List of Scottish monarchs", "link": "https://wikipedia.org/wiki/List_of_Scottish_monarchs"}]}, {"year": "1183", "text": "<PERSON><PERSON> no Munemori and the Taira clan take the young Emperor <PERSON><PERSON><PERSON> and the three sacred treasures and flee to western Japan to escape pursuit by the Minamoto clan.", "html": "1183 - <a href=\"https://wikipedia.org/wiki/Taira_no_Munemori\" title=\"Taira no Munemori\">Taira no Munemori</a> and the <a href=\"https://wikipedia.org/wiki/Taira_clan\" title=\"Taira clan\">Taira clan</a> take the young <a href=\"https://wikipedia.org/wiki/Emperor_Antoku\" title=\"Emperor Antoku\">Emperor <PERSON><PERSON><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Imperial_Regalia_of_Japan\" title=\"Imperial Regalia of Japan\">three sacred treasures</a> and flee to western Japan to escape pursuit by the <a href=\"https://wikipedia.org/wiki/Minamoto_clan\" title=\"<PERSON><PERSON> clan\">Minamoto clan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taira_no_Munemori\" title=\"Taira no Munemori\">Taira no Munemori</a> and the <a href=\"https://wikipedia.org/wiki/Taira_clan\" title=\"Taira clan\">Taira clan</a> take the young <a href=\"https://wikipedia.org/wiki/Emperor_Antoku\" title=\"Emperor <PERSON>toku\">Emperor <PERSON><PERSON><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Imperial_Regalia_of_Japan\" title=\"Imperial Regalia of Japan\">three sacred treasures</a> and flee to western Japan to escape pursuit by the <a href=\"https://wikipedia.org/wiki/Minamoto_clan\" title=\"<PERSON><PERSON> clan\"><PERSON><PERSON> clan</a>.", "links": [{"title": "<PERSON>ra no Munemori", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>mori"}, {"title": "Taira clan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_clan"}, {"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Imperial Regalia of Japan", "link": "https://wikipedia.org/wiki/Imperial_Regalia_of_Japan"}, {"title": "Minamoto clan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_clan"}]}, {"year": "1264", "text": "After tricking the Venetian galley fleet into sailing east to the Levant, the Genoese capture an entire Venetian trade convoy at the Battle of Saseno.", "html": "1264 - After tricking the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> <a href=\"https://wikipedia.org/wiki/Galley\" title=\"Galley\">galley</a> fleet into sailing east to the <a href=\"https://wikipedia.org/wiki/Levant\" title=\"Levant\">Levant</a>, the <a href=\"https://wikipedia.org/wiki/Republic_of_Genoa\" title=\"Republic of Genoa\">Genoese</a> capture an entire Venetian trade convoy at the <a href=\"https://wikipedia.org/wiki/Battle_of_Saseno\" title=\"Battle of Saseno\">Battle of Saseno</a>.", "no_year_html": "After tricking the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> <a href=\"https://wikipedia.org/wiki/Galley\" title=\"Galley\">galley</a> fleet into sailing east to the <a href=\"https://wikipedia.org/wiki/Levant\" title=\"Levant\">Levant</a>, the <a href=\"https://wikipedia.org/wiki/Republic_of_Genoa\" title=\"Republic of Genoa\">Genoese</a> capture an entire Venetian trade convoy at the <a href=\"https://wikipedia.org/wiki/Battle_of_Saseno\" title=\"Battle of Saseno\">Battle of Saseno</a>.", "links": [{"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "Galley", "link": "https://wikipedia.org/wiki/Galley"}, {"title": "Levant", "link": "https://wikipedia.org/wiki/Levant"}, {"title": "Republic of Genoa", "link": "https://wikipedia.org/wiki/Republic_of_Genoa"}, {"title": "Battle of Saseno", "link": "https://wikipedia.org/wiki/Battle_of_Saseno"}]}, {"year": "1352", "text": "War of the Breton Succession: Anglo-Bretons defeat the French in the Battle of Mauron.", "html": "1352 - <a href=\"https://wikipedia.org/wiki/War_of_the_Breton_Succession\" title=\"War of the Breton Succession\">War of the Breton Succession</a>: Anglo-Bretons defeat the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Mauron\" title=\"Battle of Mauron\">Battle of Mauron</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Breton_Succession\" title=\"War of the Breton Succession\">War of the Breton Succession</a>: Anglo-Bretons defeat the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Mauron\" title=\"Battle of Mauron\">Battle of Mauron</a>.", "links": [{"title": "War of the Breton Succession", "link": "https://wikipedia.org/wiki/War_of_the_Breton_Succession"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Battle of Mauron", "link": "https://wikipedia.org/wiki/Battle_of_Mauron"}]}, {"year": "1370", "text": "<PERSON>, Holy Roman Emperor, grants city privileges to Karlovy Vary.", "html": "1370 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, grants <a href=\"https://wikipedia.org/wiki/City_privileges\" class=\"mw-redirect\" title=\"City privileges\">city privileges</a> to <a href=\"https://wikipedia.org/wiki/Karlovy_Vary\" title=\"Karlovy Vary\">Karlovy Vary</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, grants <a href=\"https://wikipedia.org/wiki/City_privileges\" class=\"mw-redirect\" title=\"City privileges\">city privileges</a> to <a href=\"https://wikipedia.org/wiki/Karlovy_Vary\" title=\"Karlovy Vary\">Karlovy Vary</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "City privileges", "link": "https://wikipedia.org/wiki/City_privileges"}, {"title": "Karlovy Vary", "link": "https://wikipedia.org/wiki/Karlovy_Vary"}]}, {"year": "1385", "text": "Portuguese Crisis of 1383-85: Battle of Aljubarrota: Portuguese forces commanded by <PERSON> of Portugal defeat the Castilian army of <PERSON> of Castile.", "html": "1385 - <a href=\"https://wikipedia.org/wiki/1383%E2%80%9385_Portuguese_interregnum\" class=\"mw-redirect\" title=\"1383-85 Portuguese interregnum\">Portuguese Crisis of 1383-85</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Aljubarrota\" title=\"Battle of Aljubarrota\">Battle of Aljubarrota</a>: Portuguese forces commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> defeat the <a href=\"https://wikipedia.org/wiki/Castile_(historical_region)\" title=\"Castile (historical region)\">Castilian</a> army of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON> of Castile</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1383%E2%80%9385_Portuguese_interregnum\" class=\"mw-redirect\" title=\"1383-85 Portuguese interregnum\">Portuguese Crisis of 1383-85</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Aljubarrota\" title=\"Battle of Aljubarrota\">Battle of Aljubarrota</a>: Portuguese forces commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> defeat the <a href=\"https://wikipedia.org/wiki/Castile_(historical_region)\" title=\"Castile (historical region)\">Castilian</a> army of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"John I of Castile\"><PERSON> of Castile</a>.", "links": [{"title": "1383-85 Portuguese interregnum", "link": "https://wikipedia.org/wiki/1383%E2%80%9385_Portuguese_interregnum"}, {"title": "Battle of Aljubarrota", "link": "https://wikipedia.org/wiki/Battle_of_Aljubarrota"}, {"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}, {"title": "Castile (historical region)", "link": "https://wikipedia.org/wiki/Castile_(historical_region)"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile"}]}, {"year": "1592", "text": "The first sighting of the Falkland Islands by <PERSON>.", "html": "1592 - The first sighting of the <a href=\"https://wikipedia.org/wiki/Falkland_Islands\" title=\"Falkland Islands\">Falkland Islands</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_(English_explorer)\" class=\"mw-redirect\" title=\"<PERSON> (English explorer)\"><PERSON></a>.", "no_year_html": "The first sighting of the <a href=\"https://wikipedia.org/wiki/Falkland_Islands\" title=\"Falkland Islands\">Falkland Islands</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_explorer)\" class=\"mw-redirect\" title=\"<PERSON> (English explorer)\"><PERSON></a>.", "links": [{"title": "Falkland Islands", "link": "https://wikipedia.org/wiki/Falkland_Islands"}, {"title": "<PERSON> (English explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_explorer)"}]}, {"year": "1598", "text": "Nine Years' War: Battle of the Yellow Ford: Irish forces under <PERSON>, Earl of Tyrone, defeat an English expeditionary force under <PERSON>.", "html": "1598 - <a href=\"https://wikipedia.org/wiki/Nine_Years%27_War_(Ireland)\" title=\"Nine Years' War (Ireland)\">Nine Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Yellow_Ford\" title=\"Battle of the Yellow Ford\">Battle of the Yellow Ford</a>: Irish forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, Earl of <PERSON>\"><PERSON>, Earl of Tyrone</a>, defeat an English expeditionary force under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nine_Years%27_War_(Ireland)\" title=\"Nine Years' War (Ireland)\">Nine Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Yellow_Ford\" title=\"Battle of the Yellow Ford\">Battle of the Yellow Ford</a>: Irish forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>,_<PERSON>_<PERSON>_Tyrone\" title=\"<PERSON>, Earl of Tyrone\"><PERSON>, Earl of Tyrone</a>, defeat an English expeditionary force under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Nine Years' War (Ireland)", "link": "https://wikipedia.org/wiki/Nine_Years%27_War_(Ireland)"}, {"title": "Battle of the Yellow Ford", "link": "https://wikipedia.org/wiki/Battle_of_the_Yellow_Ford"}, {"title": "<PERSON>, Earl of Tyrone", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>,_<PERSON>_of_Tyrone"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "The Spanish military Villasur expedition is defeated by Pawnee and Otoe warriors near present-day Columbus, Nebraska.", "html": "1720 - The Spanish military <a href=\"https://wikipedia.org/wiki/Villasur_expedition\" title=\"Villasur expedition\">Villasur expedition</a> is defeated by <a href=\"https://wikipedia.org/wiki/Pawnee_people\" title=\"Pawnee people\">Pawnee</a> and <a href=\"https://wikipedia.org/wiki/Otoe_tribe\" class=\"mw-redirect\" title=\"Otoe tribe\">Otoe</a> warriors near present-day <a href=\"https://wikipedia.org/wiki/Columbus,_Nebraska\" title=\"Columbus, Nebraska\">Columbus, Nebraska</a>.", "no_year_html": "The Spanish military <a href=\"https://wikipedia.org/wiki/Villasur_expedition\" title=\"Villasur expedition\">Villasur expedition</a> is defeated by <a href=\"https://wikipedia.org/wiki/Pawnee_people\" title=\"Pawnee people\">Pawnee</a> and <a href=\"https://wikipedia.org/wiki/Otoe_tribe\" class=\"mw-redirect\" title=\"Otoe tribe\">Otoe</a> warriors near present-day <a href=\"https://wikipedia.org/wiki/Columbus,_Nebraska\" title=\"Columbus, Nebraska\">Columbus, Nebraska</a>.", "links": [{"title": "Villasur expedition", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_expedition"}, {"title": "Pawnee people", "link": "https://wikipedia.org/wiki/Pawnee_people"}, {"title": "Otoe tribe", "link": "https://wikipedia.org/wiki/Otoe_tribe"}, {"title": "Columbus, Nebraska", "link": "https://wikipedia.org/wiki/Columbus,_Nebraska"}]}, {"year": "1784", "text": "Russian colonization of North America: Awa'uq Massacre: The Russian fur trader <PERSON><PERSON><PERSON> storms a Kodiak Island Alutiit refuge rock on Sitkalidak Island, killing 500+ Alutiit.", "html": "1784 - <a href=\"https://wikipedia.org/wiki/Russian_colonization_of_North_America\" title=\"Russian colonization of North America\">Russian colonization of North America</a>: <a href=\"https://wikipedia.org/wiki/Awa%27uq_Massacre\" title=\"Awa'uq Massacre\">Awa'uq Massacre</a>: The Russian fur trader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>hov\"><PERSON><PERSON><PERSON></a> storms a <a href=\"https://wikipedia.org/wiki/Kodiak_Island\" title=\"Kodiak Island\">Kodiak Island</a> <a href=\"https://wikipedia.org/wiki/Alutiiq\" title=\"Alutiiq\">Alutiit</a> refuge rock on <a href=\"https://wikipedia.org/wiki/Sitkalidak_Island\" title=\"Sitkalidak Island\">Sitkalidak Island</a>, killing 500+ Alutiit.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_colonization_of_North_America\" title=\"Russian colonization of North America\">Russian colonization of North America</a>: <a href=\"https://wikipedia.org/wiki/Awa%27uq_Massacre\" title=\"Awa'uq Massacre\">Awa'uq Massacre</a>: The Russian fur trader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hov\" title=\"G<PERSON><PERSON> She<PERSON>hov\"><PERSON><PERSON><PERSON></a> storms a <a href=\"https://wikipedia.org/wiki/Kodiak_Island\" title=\"Kodiak Island\">Kodiak Island</a> <a href=\"https://wikipedia.org/wiki/Alutiiq\" title=\"Alutiiq\">Alutiit</a> refuge rock on <a href=\"https://wikipedia.org/wiki/Sitkalidak_Island\" title=\"Sitkalidak Island\">Sitkalidak Island</a>, killing 500+ Alutiit.", "links": [{"title": "Russian colonization of North America", "link": "https://wikipedia.org/wiki/Russian_colonization_of_North_America"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Awa%27uq_Massacre"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Kodiak Island", "link": "https://wikipedia.org/wiki/Kodiak_Island"}, {"title": "Alutiiq", "link": "https://wikipedia.org/wiki/Alutiiq"}, {"title": "Sitkalidak Island", "link": "https://wikipedia.org/wiki/Sitkalidak_Island"}]}, {"year": "1790", "text": "The Treaty of Wereloe ended the 1788-1790 Russo-Swedish War.", "html": "1790 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Wereloe\" class=\"mw-redirect\" title=\"Treaty of Wereloe\">Treaty of Wereloe</a> ended the <a href=\"https://wikipedia.org/wiki/Russo-Swedish_War_(1788%E2%80%931790)\" title=\"Russo-Swedish War (1788-1790)\">1788-1790 Russo-Swedish War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Wereloe\" class=\"mw-redirect\" title=\"Treaty of Wereloe\">Treaty of Wereloe</a> ended the <a href=\"https://wikipedia.org/wiki/Russo-Swedish_War_(1788%E2%80%931790)\" title=\"Russo-Swedish War (1788-1790)\">1788-1790 Russo-Swedish War</a>.", "links": [{"title": "Treaty of Wereloe", "link": "https://wikipedia.org/wiki/Treaty_of_Wereloe"}, {"title": "Russo-Swedish War (1788-1790)", "link": "https://wikipedia.org/wiki/Russo-Swedish_War_(1788%E2%80%931790)"}]}, {"year": "1791", "text": "Slaves from plantations in Saint-Domingue hold a Vodou ceremony led by <PERSON><PERSON><PERSON> at Bois Caïman, marking the start of the Haitian Revolution.", "html": "1791 - Slaves from plantations in <a href=\"https://wikipedia.org/wiki/Saint-Domingue\" title=\"Saint-Domingue\">Saint-Domingue</a> hold a <a href=\"https://wikipedia.org/wiki/Haitian_Vodou\" title=\"Haitian Vodou\">Vodou</a> ceremony led by <a href=\"https://wikipedia.org/wiki/Houngan\" class=\"mw-redirect\" title=\"Houngan\">ho<PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Bois_Ca%C3%AFman\" title=\"Bois Caïman\"><PERSON> C<PERSON>ïman</a>, marking the start of the <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>.", "no_year_html": "Slaves from plantations in <a href=\"https://wikipedia.org/wiki/Saint-Domingue\" title=\"Saint-Domingue\">Saint-Domingue</a> hold a <a href=\"https://wikipedia.org/wiki/Haitian_Vodou\" title=\"Haitian Vodou\">Vodou</a> ceremony led by <a href=\"https://wikipedia.org/wiki/Houngan\" class=\"mw-redirect\" title=\"Houngan\">houng<PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Bois_Ca%C3%AFman\" title=\"<PERSON> C<PERSON>ïman\"><PERSON></a>, marking the start of the <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>.", "links": [{"title": "Saint-Domingue", "link": "https://wikipedia.org/wiki/Saint-Domingue"}, {"title": "Haitian Vodou", "link": "https://wikipedia.org/wiki/Haitian_Vodou"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>an"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bois_Ca%C3%AFman"}, {"title": "Haitian Revolution", "link": "https://wikipedia.org/wiki/Haitian_Revolution"}]}, {"year": "1814", "text": " A cease fire agreement, called the Convention of Moss, ended the Swedish-Norwegian War.", "html": "1814 - A cease fire agreement, called the <a href=\"https://wikipedia.org/wiki/Convention_of_Moss\" title=\"Convention of Moss\">Convention of Moss</a>, ended the <a href=\"https://wikipedia.org/wiki/Swedish%E2%80%93Norwegian_War_(1814)\" class=\"mw-redirect\" title=\"Swedish-Norwegian War (1814)\">Swedish-Norwegian War</a>.", "no_year_html": "A cease fire agreement, called the <a href=\"https://wikipedia.org/wiki/Convention_of_Moss\" title=\"Convention of Moss\">Convention of Moss</a>, ended the <a href=\"https://wikipedia.org/wiki/Swedish%E2%80%93Norwegian_War_(1814)\" class=\"mw-redirect\" title=\"Swedish-Norwegian War (1814)\">Swedish-Norwegian War</a>.", "links": [{"title": "Convention of Moss", "link": "https://wikipedia.org/wiki/Convention_of_Moss"}, {"title": "Swedish-Norwegian War (1814)", "link": "https://wikipedia.org/wiki/Swedish%E2%80%93Norwegian_War_(1814)"}]}, {"year": "1816", "text": "The United Kingdom formally annexes the Tristan da Cunha archipelago, administering the islands from the Cape Colony in South Africa.", "html": "1816 - The United Kingdom formally annexes the <a href=\"https://wikipedia.org/wiki/Tristan_da_Cunha\" title=\"Tristan da Cunha\"><PERSON> C<PERSON>ha</a> archipelago, administering the islands from the <a href=\"https://wikipedia.org/wiki/Cape_Colony\" title=\"Cape Colony\">Cape Colony</a> in South Africa.", "no_year_html": "The United Kingdom formally annexes the <a href=\"https://wikipedia.org/wiki/Tristan_da_Cunha\" title=\"<PERSON> da Cunha\"><PERSON> C<PERSON>ha</a> archipelago, administering the islands from the <a href=\"https://wikipedia.org/wiki/Cape_Colony\" title=\"Cape Colony\">Cape Colony</a> in South Africa.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cape Colony", "link": "https://wikipedia.org/wiki/Cape_Colony"}]}, {"year": "1842", "text": "American Indian Wars: Second Seminole War ends, with the Seminoles forced from Florida.", "html": "1842 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Seminole_Wars\" title=\"Seminole Wars\">Second Seminole War</a> ends, with the <a href=\"https://wikipedia.org/wiki/Seminole\" title=\"Seminole\">Seminoles</a> forced from <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Seminole_Wars\" title=\"Seminole Wars\">Second Seminole War</a> ends, with the <a href=\"https://wikipedia.org/wiki/Seminole\" title=\"Seminole\">Seminoles</a> forced from <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Seminole Wars", "link": "https://wikipedia.org/wiki/Seminole_Wars"}, {"title": "Seminole", "link": "https://wikipedia.org/wiki/Seminole"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}]}, {"year": "1848", "text": "Oregon Territory is organized by act of Congress.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Oregon_Territory\" title=\"Oregon Territory\">Oregon Territory</a> is organized by <a href=\"https://wikipedia.org/wiki/Act_of_Congress\" title=\"Act of Congress\">act of Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oregon_Territory\" title=\"Oregon Territory\">Oregon Territory</a> is organized by <a href=\"https://wikipedia.org/wiki/Act_of_Congress\" title=\"Act of Congress\">act of Congress</a>.", "links": [{"title": "Oregon Territory", "link": "https://wikipedia.org/wiki/Oregon_Territory"}, {"title": "Act of Congress", "link": "https://wikipedia.org/wiki/Act_of_Congress"}]}, {"year": "1880", "text": "Construction of Cologne Cathedral, the most famous landmark in Cologne, Germany, is completed.", "html": "1880 - Construction of <a href=\"https://wikipedia.org/wiki/Cologne_Cathedral\" title=\"Cologne Cathedral\">Cologne Cathedral</a>, the most famous landmark in <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>, Germany, is completed.", "no_year_html": "Construction of <a href=\"https://wikipedia.org/wiki/Cologne_Cathedral\" title=\"Cologne Cathedral\">Cologne Cathedral</a>, the most famous landmark in <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>, Germany, is completed.", "links": [{"title": "Cologne Cathedral", "link": "https://wikipedia.org/wiki/Cologne_Cathedral"}, {"title": "Cologne", "link": "https://wikipedia.org/wiki/Cologne"}]}, {"year": "1885", "text": "Japan's first patent is issued to the inventor of a rust-proof paint.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Japanese_patent_law\" title=\"Japanese patent law\">Japan's first patent</a> is issued to the inventor of a rust-proof paint.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japanese_patent_law\" title=\"Japanese patent law\">Japan's first patent</a> is issued to the inventor of a rust-proof paint.", "links": [{"title": "Japanese patent law", "link": "https://wikipedia.org/wiki/Japanese_patent_law"}]}, {"year": "1893", "text": "France becomes the first country to introduce motor vehicle registration.", "html": "1893 - France becomes the first country to introduce <a href=\"https://wikipedia.org/wiki/Vehicle_registration_plate\" title=\"Vehicle registration plate\">motor vehicle registration</a>.", "no_year_html": "France becomes the first country to introduce <a href=\"https://wikipedia.org/wiki/Vehicle_registration_plate\" title=\"Vehicle registration plate\">motor vehicle registration</a>.", "links": [{"title": "Vehicle registration plate", "link": "https://wikipedia.org/wiki/Vehicle_registration_plate"}]}, {"year": "1900", "text": "Battle of Peking: The Eight-Nation Alliance occupies Beijing, China, in a campaign to end the bloody Boxer Rebellion in China.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Battle_of_Peking_(1900)\" title=\"Battle of Peking (1900)\">Battle of Peking</a>: The <a href=\"https://wikipedia.org/wiki/Eight-Nation_Alliance\" title=\"Eight-Nation Alliance\">Eight-Nation Alliance</a> occupies Beijing, China, in a campaign to end the bloody <a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a> in China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Peking_(1900)\" title=\"Battle of Peking (1900)\">Battle of Peking</a>: The <a href=\"https://wikipedia.org/wiki/Eight-Nation_Alliance\" title=\"Eight-Nation Alliance\">Eight-Nation Alliance</a> occupies Beijing, China, in a campaign to end the bloody <a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a> in China.", "links": [{"title": "Battle of Peking (1900)", "link": "https://wikipedia.org/wiki/Battle_of_Peking_(1900)"}, {"title": "Eight-Nation Alliance", "link": "https://wikipedia.org/wiki/Eight-Nation_Alliance"}, {"title": "Boxer Rebellion", "link": "https://wikipedia.org/wiki/Boxer_Rebellion"}]}, {"year": "1901", "text": "The first claimed powered flight, by <PERSON><PERSON> in his Number 21.", "html": "1901 - The first claimed <a href=\"https://wikipedia.org/wiki/Aviation\" title=\"Aviation\">powered flight</a>, by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in his <a href=\"https://wikipedia.org/wiki/Whitehead_No._21\" title=\"Whitehead No. 21\">Number 21</a>.", "no_year_html": "The first claimed <a href=\"https://wikipedia.org/wiki/Aviation\" title=\"Aviation\">powered flight</a>, by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in his <a href=\"https://wikipedia.org/wiki/Whitehead_No._21\" title=\"Whitehead No. 21\">Number 21</a>.", "links": [{"title": "Aviation", "link": "https://wikipedia.org/wiki/Aviation"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Whitehead No. 21", "link": "https://wikipedia.org/wiki/Whitehead_No._21"}]}, {"year": "1914", "text": "World War I: Start of the Battle of Lorraine, an unsuccessful French offensive.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Start of the <a href=\"https://wikipedia.org/wiki/Battle_of_Lorraine\" title=\"Battle of Lorraine\">Battle of Lorraine</a>, an unsuccessful French offensive.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Start of the <a href=\"https://wikipedia.org/wiki/Battle_of_Lorraine\" title=\"Battle of Lorraine\">Battle of Lorraine</a>, an unsuccessful French offensive.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Lorraine", "link": "https://wikipedia.org/wiki/Battle_of_Lorraine"}]}, {"year": "1917", "text": "World War I: The Republic of China, which had heretofore been shipping labourers to Europe to assist in the war effort, officially declares war on the Central Powers, although it will continue to send to Europe labourers instead of combatants for the remaining duration of the war.", "html": "1917 - World War I: The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic of China</a>, which had heretofore been <a href=\"https://wikipedia.org/wiki/Chinese_Labour_Corps\" title=\"Chinese Labour Corps\">shipping labourers</a> to Europe to assist in the war effort, officially declares war on the <a href=\"https://wikipedia.org/wiki/Central_Powers\" title=\"Central Powers\">Central Powers</a>, although it will continue to send to Europe labourers instead of combatants for the remaining duration of the war.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic of China</a>, which had heretofore been <a href=\"https://wikipedia.org/wiki/Chinese_Labour_Corps\" title=\"Chinese Labour Corps\">shipping labourers</a> to Europe to assist in the war effort, officially declares war on the <a href=\"https://wikipedia.org/wiki/Central_Powers\" title=\"Central Powers\">Central Powers</a>, although it will continue to send to Europe labourers instead of combatants for the remaining duration of the war.", "links": [{"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}, {"title": "Chinese Labour Corps", "link": "https://wikipedia.org/wiki/Chinese_Labour_Corps"}, {"title": "Central Powers", "link": "https://wikipedia.org/wiki/Central_Powers"}]}, {"year": "1920", "text": "The 1920 Summer Olympics, having started four months earlier, officially open in Antwerp, Belgium, with the newly adopted Olympic flag and the Olympic oath being raised and taken at the Opening Ceremony for the first time in Olympic history.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/1920_Summer_Olympics\" title=\"1920 Summer Olympics\">1920 Summer Olympics</a>, having started four months earlier, officially open in <a href=\"https://wikipedia.org/wiki/Antwerp\" title=\"Antwerp\">Antwerp</a>, <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>, with the newly adopted <a href=\"https://wikipedia.org/wiki/Olympic_symbols#Different_types_of_flags\" title=\"Olympic symbols\">Olympic flag</a> and the Olympic oath being raised and taken at the Opening Ceremony for the first time in Olympic history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1920_Summer_Olympics\" title=\"1920 Summer Olympics\">1920 Summer Olympics</a>, having started four months earlier, officially open in <a href=\"https://wikipedia.org/wiki/Antwerp\" title=\"Antwerp\">Antwerp</a>, <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>, with the newly adopted <a href=\"https://wikipedia.org/wiki/Olympic_symbols#Different_types_of_flags\" title=\"Olympic symbols\">Olympic flag</a> and the Olympic oath being raised and taken at the Opening Ceremony for the first time in Olympic history.", "links": [{"title": "1920 Summer Olympics", "link": "https://wikipedia.org/wiki/1920_Summer_Olympics"}, {"title": "Antwerp", "link": "https://wikipedia.org/wiki/Antwerp"}, {"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}, {"title": "Olympic symbols", "link": "https://wikipedia.org/wiki/Olympic_symbols#Different_types_of_flags"}]}, {"year": "1921", "text": "Tannu <PERSON>, later Tuvan People's Republic is established as a completely independent country (which is supported by Soviet Russia).", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Tannu_Uriankhai\" title=\"Tannu Uriankhai\"><PERSON><PERSON></a>, later <a href=\"https://wikipedia.org/wiki/Tuvan_People%27s_Republic\" title=\"Tuvan People's Republic\">Tuvan People's Republic</a> is established as a completely independent country (which is supported by <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Soviet Russia</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tannu_Uriankhai\" title=\"Tannu Uriankhai\"><PERSON><PERSON></a>, later <a href=\"https://wikipedia.org/wiki/Tuvan_People%27s_Republic\" title=\"Tuvan People's Republic\">Tuvan People's Republic</a> is established as a completely independent country (which is supported by <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Soviet Russia</a>).", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "Tuvan People's Republic", "link": "https://wikipedia.org/wiki/Tuvan_People%27s_Republic"}, {"title": "Russian Soviet Federative Socialist Republic", "link": "https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic"}]}, {"year": "1933", "text": "Loggers cause a forest fire in the Coast Range of Oregon, later known as the first forest fire of the Tillamook Burn;  destroying 240,000 acres (970 km2) of land.", "html": "1933 - Loggers cause a <a href=\"https://wikipedia.org/wiki/Wildfire\" title=\"Wildfire\">forest fire</a> in the <a href=\"https://wikipedia.org/wiki/Pacific_Coast_Ranges\" title=\"Pacific Coast Ranges\">Coast Range</a> of <a href=\"https://wikipedia.org/wiki/Oregon\" title=\"Oregon\">Oregon</a>, later known as the first forest fire of the <a href=\"https://wikipedia.org/wiki/Tillamook_Burn\" title=\"Tillamook Burn\">Tillamook Burn</a>; destroying 240,000 acres (970 km) of land.", "no_year_html": "Loggers cause a <a href=\"https://wikipedia.org/wiki/Wildfire\" title=\"Wildfire\">forest fire</a> in the <a href=\"https://wikipedia.org/wiki/Pacific_Coast_Ranges\" title=\"Pacific Coast Ranges\">Coast Range</a> of <a href=\"https://wikipedia.org/wiki/Oregon\" title=\"Oregon\">Oregon</a>, later known as the first forest fire of the <a href=\"https://wikipedia.org/wiki/Tillamook_Burn\" title=\"Tillamook Burn\">Tillamook Burn</a>; destroying 240,000 acres (970 km) of land.", "links": [{"title": "Wildfire", "link": "https://wikipedia.org/wiki/Wildfire"}, {"title": "Pacific Coast Ranges", "link": "https://wikipedia.org/wiki/Pacific_Coast_Ranges"}, {"title": "Oregon", "link": "https://wikipedia.org/wiki/Oregon"}, {"title": "Tillamook Burn", "link": "https://wikipedia.org/wiki/Tillamook_Burn"}]}, {"year": "1935", "text": "<PERSON> signs the Social Security Act, creating a government pension system for the retired.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Social_Security_Act\" title=\"Social Security Act\">Social Security Act</a>, creating a government pension system for the retired.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Social_Security_Act\" title=\"Social Security Act\">Social Security Act</a>, creating a government pension system for the retired.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Social Security Act", "link": "https://wikipedia.org/wiki/Social_Security_Act"}]}, {"year": "1936", "text": "<PERSON><PERSON> is hanged in Owensboro, Kentucky in the last known public execution in the United States.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is hanged in <a href=\"https://wikipedia.org/wiki/Owensboro,_Kentucky\" title=\"Owensboro, Kentucky\">Owensboro, Kentucky</a> in the last known public <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_States\" title=\"Capital punishment in the United States\">execution in the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is hanged in <a href=\"https://wikipedia.org/wiki/Owensboro,_Kentucky\" title=\"Owensboro, Kentucky\">Owensboro, Kentucky</a> in the last known public <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_States\" title=\"Capital punishment in the United States\">execution in the United States</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Owensboro, Kentucky", "link": "https://wikipedia.org/wiki/Owensboro,_Kentucky"}, {"title": "Capital punishment in the United States", "link": "https://wikipedia.org/wiki/Capital_punishment_in_the_United_States"}]}, {"year": "1941", "text": "World War II: <PERSON> and <PERSON> sign the Atlantic Charter of war stating postwar aims.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Atlantic_Charter\" title=\"Atlantic Charter\">Atlantic Charter</a> of war stating postwar aims.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Atlantic_Charter\" title=\"Atlantic Charter\">Atlantic Charter</a> of war stating postwar aims.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Atlantic Charter", "link": "https://wikipedia.org/wiki/Atlantic_Charter"}]}, {"year": "1947", "text": "Pakistan gains independence from the British Empire as the Dominion of Pakistan, due to the partition of India.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> gains <a href=\"https://wikipedia.org/wiki/Pakistan_Movement\" title=\"Pakistan Movement\">independence</a> from the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> as the <a href=\"https://wikipedia.org/wiki/Dominion_of_Pakistan\" title=\"Dominion of Pakistan\">Dominion of Pakistan</a>, due to the <a href=\"https://wikipedia.org/wiki/Partition_of_India\" title=\"Partition of India\">partition of India</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> gains <a href=\"https://wikipedia.org/wiki/Pakistan_Movement\" title=\"Pakistan Movement\">independence</a> from the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> as the <a href=\"https://wikipedia.org/wiki/Dominion_of_Pakistan\" title=\"Dominion of Pakistan\">Dominion of Pakistan</a>, due to the <a href=\"https://wikipedia.org/wiki/Partition_of_India\" title=\"Partition of India\">partition of India</a>.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Pakistan Movement", "link": "https://wikipedia.org/wiki/Pakistan_Movement"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "Dominion of Pakistan", "link": "https://wikipedia.org/wiki/Dominion_of_Pakistan"}, {"title": "Partition of India", "link": "https://wikipedia.org/wiki/Partition_of_India"}]}, {"year": "1948", "text": "An Idaho Department of Fish and Game program to relocate beavers known as Beaver drop occurred. This program relocated beavers from Northwestern Idaho to Central Idaho by airplane and then parachuting the beavers into the Chamberlain Basin .", "html": "1948 - An <a href=\"https://wikipedia.org/wiki/Idaho_Department_of_Fish_and_Game\" title=\"Idaho Department of Fish and Game\">Idaho Department of Fish and Game</a> program to relocate <a href=\"https://wikipedia.org/wiki/Beavers\" class=\"mw-redirect\" title=\"Beavers\">beavers</a> known as <a href=\"https://wikipedia.org/wiki/Beaver_drop\" title=\"Beaver drop\">Beaver drop</a> occurred. This program relocated beavers from Northwestern Idaho to Central Idaho by airplane and then parachuting the beavers into the <a href=\"https://wikipedia.org/wiki/Chamberlain_Basin\" title=\"Chamberlain Basin\">Chamberlain Basin</a> .", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Idaho_Department_of_Fish_and_Game\" title=\"Idaho Department of Fish and Game\">Idaho Department of Fish and Game</a> program to relocate <a href=\"https://wikipedia.org/wiki/Beavers\" class=\"mw-redirect\" title=\"Beavers\">beavers</a> known as <a href=\"https://wikipedia.org/wiki/Beaver_drop\" title=\"Beaver drop\">Beaver drop</a> occurred. This program relocated beavers from Northwestern Idaho to Central Idaho by airplane and then parachuting the beavers into the <a href=\"https://wikipedia.org/wiki/Chamberlain_Basin\" title=\"Chamberlain Basin\">Chamberlain Basin</a> .", "links": [{"title": "Idaho Department of Fish and Game", "link": "https://wikipedia.org/wiki/Idaho_Department_of_Fish_and_Game"}, {"title": "Beavers", "link": "https://wikipedia.org/wiki/Beavers"}, {"title": "Beaver drop", "link": "https://wikipedia.org/wiki/<PERSON>_drop"}, {"title": "Chamberlain Basin", "link": "https://wikipedia.org/wiki/Chamberlain_Basin"}]}, {"year": "1959", "text": "Founding and first official meeting of the American Football League.", "html": "1959 - Founding and first official meeting of the <a href=\"https://wikipedia.org/wiki/American_Football_League\" title=\"American Football League\">American Football League</a>.", "no_year_html": "Founding and first official meeting of the <a href=\"https://wikipedia.org/wiki/American_Football_League\" title=\"American Football League\">American Football League</a>.", "links": [{"title": "American Football League", "link": "https://wikipedia.org/wiki/American_Football_League"}]}, {"year": "1967", "text": "UK Marine Broadcasting Offences Act 1967 declares participation in offshore pirate radio illegal.", "html": "1967 - UK <a href=\"https://wikipedia.org/wiki/Marine,_%26c.,_Broadcasting_(Offences)_Act_1967\" title=\"Marine, &amp;c., Broadcasting (Offences) Act 1967\">Marine Broadcasting Offences Act 1967</a> declares participation in offshore <a href=\"https://wikipedia.org/wiki/Pirate_radio\" title=\"Pirate radio\">pirate radio</a> illegal.", "no_year_html": "UK <a href=\"https://wikipedia.org/wiki/Marine,_%26c.,_Broadcasting_(Offences)_Act_1967\" title=\"Marine, &amp;c., Broadcasting (Offences) Act 1967\">Marine Broadcasting Offences Act 1967</a> declares participation in offshore <a href=\"https://wikipedia.org/wiki/Pirate_radio\" title=\"Pirate radio\">pirate radio</a> illegal.", "links": [{"title": "Marine, &c., Broadcasting (Offences) Act 1967", "link": "https://wikipedia.org/wiki/Marine,_%26c.,_Broadcasting_(Offences)_Act_1967"}, {"title": "Pirate radio", "link": "https://wikipedia.org/wiki/Pirate_radio"}]}, {"year": "1969", "text": "The Troubles: British troops are deployed in Northern Ireland as political and sectarian violence breaks out, marking the start of the 37-year Operation Banner.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: British troops are deployed in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> as <a href=\"https://wikipedia.org/wiki/1969_Northern_Ireland_riots\" title=\"1969 Northern Ireland riots\">political and sectarian violence breaks out</a>, marking the start of the 37-year <a href=\"https://wikipedia.org/wiki/Operation_Banner\" title=\"Operation Banner\">Operation Banner</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: British troops are deployed in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> as <a href=\"https://wikipedia.org/wiki/1969_Northern_Ireland_riots\" title=\"1969 Northern Ireland riots\">political and sectarian violence breaks out</a>, marking the start of the 37-year <a href=\"https://wikipedia.org/wiki/Operation_Banner\" title=\"Operation Banner\">Operation Banner</a>.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "1969 Northern Ireland riots", "link": "https://wikipedia.org/wiki/1969_Northern_Ireland_riots"}, {"title": "Operation Banner", "link": "https://wikipedia.org/wiki/Operation_Banner"}]}, {"year": "1971", "text": "Bahrain declares independence from Britain.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a> declares independence from Britain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a> declares independence from Britain.", "links": [{"title": "Bahrain", "link": "https://wikipedia.org/wiki/Bahrain"}]}, {"year": "1972", "text": "An Ilyushin Il-62 airliner crashes near Königs Wusterhausen, East Germany killing 156 people.", "html": "1972 - An Ilyushin Il-62 airliner <a href=\"https://wikipedia.org/wiki/1972_K%C3%B6nigs_Wusterhausen_air_disaster\" class=\"mw-redirect\" title=\"1972 Königs Wusterhausen air disaster\">crashes near Königs Wusterhausen</a>, East Germany killing 156 people.", "no_year_html": "An Ilyushin Il-62 airliner <a href=\"https://wikipedia.org/wiki/1972_K%C3%B6nigs_Wusterhausen_air_disaster\" class=\"mw-redirect\" title=\"1972 Königs Wusterhausen air disaster\">crashes near Königs Wusterhausen</a>, East Germany killing 156 people.", "links": [{"title": "1972 Königs Wusterhausen air disaster", "link": "https://wikipedia.org/wiki/1972_K%C3%B6ni<PERSON>_Wusterhausen_air_disaster"}]}, {"year": "1980", "text": "Lech Wałęsa leads strikes at the Gdańsk, Poland shipyards.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa\" title=\"Lech Wałęsa\">Lech Wałęsa</a> leads strikes at the <a href=\"https://wikipedia.org/wiki/Gda%C5%84sk\" title=\"Gdańsk\">Gdańsk</a>, Poland shipyards.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa\" title=\"Lech Wałęsa\">Lech Wałęsa</a> leads strikes at the <a href=\"https://wikipedia.org/wiki/Gda%C5%84sk\" title=\"Gdańsk\">Gdańsk</a>, Poland shipyards.", "links": [{"title": "Lech Wałęsa", "link": "https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa"}, {"title": "Gdańsk", "link": "https://wikipedia.org/wiki/Gda%C5%84sk"}]}, {"year": "1994", "text": "<PERSON><PERSON>, also known as \"<PERSON> the Jack<PERSON>\", is captured.", "html": "1994 - <PERSON><PERSON>, also known as \"<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Jack<PERSON>\" title=\"<PERSON> the Jackal\"><PERSON> the Jackal</a>\", is captured.", "no_year_html": "<PERSON><PERSON>, also known as \"<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Jackal\" title=\"<PERSON> the Jackal\"><PERSON> the Jackal</a>\", is captured.", "links": [{"title": "<PERSON> the Jackal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "Greek Cypriot refugee <PERSON><PERSON> is shot and killed by a Turkish security officer while trying to climb a flagpole in order to remove a Turkish flag from its mast in the United Nations Buffer Zone in Cyprus.", "html": "1996 - Greek Cypriot refugee <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Solomou\" class=\"mw-redirect\" title=\"Solomos Solomou\"><PERSON><PERSON></a> is shot and killed by a Turkish security officer while trying to climb a flagpole in order to remove a Turkish flag from its mast in the <a href=\"https://wikipedia.org/wiki/United_Nations_Buffer_Zone_in_Cyprus\" title=\"United Nations Buffer Zone in Cyprus\">United Nations Buffer Zone in Cyprus</a>.", "no_year_html": "Greek Cypriot refugee <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Solomou\" class=\"mw-redirect\" title=\"Solomos Solomou\"><PERSON><PERSON></a> is shot and killed by a Turkish security officer while trying to climb a flagpole in order to remove a Turkish flag from its mast in the <a href=\"https://wikipedia.org/wiki/United_Nations_Buffer_Zone_in_Cyprus\" title=\"United Nations Buffer Zone in Cyprus\">United Nations Buffer Zone in Cyprus</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Solomou"}, {"title": "United Nations Buffer Zone in Cyprus", "link": "https://wikipedia.org/wiki/United_Nations_Buffer_Zone_in_Cyprus"}]}, {"year": "2003", "text": "A widescale power blackout affects the northeast United States and Canada.", "html": "2003 - A <a href=\"https://wikipedia.org/wiki/Northeast_blackout_of_2003\" title=\"Northeast blackout of 2003\">widescale power blackout</a> affects the northeast United States and Canada.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Northeast_blackout_of_2003\" title=\"Northeast blackout of 2003\">widescale power blackout</a> affects the northeast United States and Canada.", "links": [{"title": "Northeast blackout of 2003", "link": "https://wikipedia.org/wiki/Northeast_blackout_of_2003"}]}, {"year": "2005", "text": "Helios Airways Flight 522, en route from Larnaca, Cyprus to Prague, Czech Republic via Athens, crashes in the hills near Grammatiko, Greece, killing 121 passengers and crew.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Helios_Airways_Flight_522\" title=\"Helios Airways Flight 522\">Helios Airways Flight 522</a>, en route from Larnaca, <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> to Prague, Czech Republic via Athens, crashes in the hills near <a href=\"https://wikipedia.org/wiki/Grammatiko\" title=\"Grammatiko\">Grammatiko</a>, Greece, killing 121 passengers and crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Helios_Airways_Flight_522\" title=\"Helios Airways Flight 522\">Helios Airways Flight 522</a>, en route from Larnaca, <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> to Prague, Czech Republic via Athens, crashes in the hills near <a href=\"https://wikipedia.org/wiki/Grammatiko\" title=\"Grammatiko\">Grammatiko</a>, Greece, killing 121 passengers and crew.", "links": [{"title": "Helios Airways Flight 522", "link": "https://wikipedia.org/wiki/Helios_Airways_Flight_522"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mat<PERSON>"}]}, {"year": "2006", "text": "Lebanon War: A ceasefire takes effect three days after the United Nations Security Council's approval of United Nations Security Council Resolution 1701, formally ending hostilities between Lebanon and Israel.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/2006_Lebanon_War\" title=\"2006 Lebanon War\">Lebanon War</a>: A ceasefire takes effect three days after the <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a>'s approval of <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_1701\" title=\"United Nations Security Council Resolution 1701\">United Nations Security Council Resolution 1701</a>, formally ending hostilities between <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> and <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2006_Lebanon_War\" title=\"2006 Lebanon War\">Lebanon War</a>: A ceasefire takes effect three days after the <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a>'s approval of <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_1701\" title=\"United Nations Security Council Resolution 1701\">United Nations Security Council Resolution 1701</a>, formally ending hostilities between <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> and <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "links": [{"title": "2006 Lebanon War", "link": "https://wikipedia.org/wiki/2006_Lebanon_War"}, {"title": "United Nations Security Council", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council"}, {"title": "United Nations Security Council Resolution 1701", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_1701"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "2006", "text": "Sri Lankan Civil War: Sixty-one schoolgirls killed in Chencholai bombing by Sri Lankan Air Force air strike.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: Sixty-one schoolgirls killed in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bombing\" title=\"Chencholai bombing\">Chen<PERSON>lai bombing</a> by <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Air_force\" class=\"mw-redirect\" title=\"Sri Lanka Air force\">Sri Lankan Air Force</a> air strike.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: Sixty-one schoolgirls killed in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bombing\" title=\"Chencholai bombing\"><PERSON><PERSON><PERSON> bombing</a> by <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Air_force\" class=\"mw-redirect\" title=\"Sri Lanka Air force\">Sri Lankan Air Force</a> air strike.", "links": [{"title": "Sri Lankan civil war", "link": "https://wikipedia.org/wiki/Sri_Lankan_civil_war"}, {"title": "<PERSON><PERSON><PERSON> bombing", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bombing"}, {"title": "Sri Lanka Air force", "link": "https://wikipedia.org/wiki/Sri_Lanka_Air_force"}]}, {"year": "2007", "text": "The Kahtaniya bombings kill at least 500 people.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/2007_Yazidi_communities_bombings\" class=\"mw-redirect\" title=\"2007 Yazidi communities bombings\">Kahta<PERSON>ya bombings</a> kill at least 500 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2007_Yazidi_communities_bombings\" class=\"mw-redirect\" title=\"2007 Yazidi communities bombings\">Kahtaniya bombings</a> kill at least 500 people.", "links": [{"title": "2007 Yazidi communities bombings", "link": "https://wikipedia.org/wiki/2007_Yazidi_communities_bombings"}]}, {"year": "2013", "text": "Egypt declares a state of emergency as security forces kill hundreds of demonstrators supporting former president <PERSON>.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> declares a <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> as security forces <a href=\"https://wikipedia.org/wiki/August_2013_Rabaa_massacre\" class=\"mw-redirect\" title=\"August 2013 Rabaa massacre\">kill hundreds of demonstrators</a> supporting former president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> declares a <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> as security forces <a href=\"https://wikipedia.org/wiki/August_2013_Rabaa_massacre\" class=\"mw-redirect\" title=\"August 2013 Rabaa massacre\">kill hundreds of demonstrators</a> supporting former president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "State of emergency", "link": "https://wikipedia.org/wiki/State_of_emergency"}, {"title": "August 2013 Rabaa massacre", "link": "https://wikipedia.org/wiki/August_2013_Rabaa_massacre"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "UPS Airlines Flight 1354 crashes short of the runway at Birmingham-Shuttlesworth International Airport, killing both crew members on board.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/UPS_Airlines_Flight_1354\" title=\"UPS Airlines Flight 1354\">UPS Airlines Flight 1354</a> crashes short of the runway at <a href=\"https://wikipedia.org/wiki/Birmingham%E2%80%93Shuttlesworth_International_Airport\" title=\"Birmingham-Shuttlesworth International Airport\">Birmingham-Shuttlesworth International Airport</a>, killing both crew members on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/UPS_Airlines_Flight_1354\" title=\"UPS Airlines Flight 1354\">UPS Airlines Flight 1354</a> crashes short of the runway at <a href=\"https://wikipedia.org/wiki/Birmingham%E2%80%93Shuttlesworth_International_Airport\" title=\"Birmingham-Shuttlesworth International Airport\">Birmingham-Shuttlesworth International Airport</a>, killing both crew members on board.", "links": [{"title": "UPS Airlines Flight 1354", "link": "https://wikipedia.org/wiki/UPS_Airlines_Flight_1354"}, {"title": "Birmingham-Shuttlesworth International Airport", "link": "https://wikipedia.org/wiki/Birmingham%E2%80%93Shuttlesworth_International_Airport"}]}, {"year": "2015", "text": "The U.S. Embassy in Havana, Cuba re-opens after 54 years of being closed when Cuba-United States relations were broken off.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/Embassy_of_the_United_States,_Havana\" title=\"Embassy of the United States, Havana\">U.S. Embassy</a> in <a href=\"https://wikipedia.org/wiki/Havana,_Cuba\" class=\"mw-redirect\" title=\"Havana, Cuba\">Havana, Cuba</a> re-opens after 54 years of being closed when <a href=\"https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations\" title=\"Cuba-United States relations\">Cuba-United States relations</a> were broken off.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Embassy_of_the_United_States,_Havana\" title=\"Embassy of the United States, Havana\">U.S. Embassy</a> in <a href=\"https://wikipedia.org/wiki/Havana,_Cuba\" class=\"mw-redirect\" title=\"Havana, Cuba\">Havana, Cuba</a> re-opens after 54 years of being closed when <a href=\"https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations\" title=\"Cuba-United States relations\">Cuba-United States relations</a> were broken off.", "links": [{"title": "Embassy of the United States, Havana", "link": "https://wikipedia.org/wiki/Embassy_of_the_United_States,_Havana"}, {"title": "Havana, Cuba", "link": "https://wikipedia.org/wiki/Havana,_Cuba"}, {"title": "Cuba-United States relations", "link": "https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations"}]}, {"year": "2018", "text": "The collapse of the Ponte Morandi bridge in Genoa, Italy, left 16 people injured and 43 people killed.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/2018_Ponte_Morandi_collapse\" class=\"mw-redirect\" title=\"2018 Ponte Morandi collapse\">collapse of the Ponte Morandi bridge</a> in <a href=\"https://wikipedia.org/wiki/Genoa\" title=\"Genoa\">Genoa</a>, Italy, left 16 people injured and 43 people killed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2018_Pont<PERSON>_Morandi_collapse\" class=\"mw-redirect\" title=\"2018 Ponte Morandi collapse\">collapse of the Ponte Morandi bridge</a> in <a href=\"https://wikipedia.org/wiki/Genoa\" title=\"Genoa\">Genoa</a>, Italy, left 16 people injured and 43 people killed.", "links": [{"title": "2018 Ponte Morandi collapse", "link": "https://wikipedia.org/wiki/2018_<PERSON><PERSON>_<PERSON>_collapse"}, {"title": "Genoa", "link": "https://wikipedia.org/wiki/Genoa"}]}, {"year": "2021", "text": "A magnitude 7.2 earthquake strikes southwestern Haiti, killing at least 2,248 people and causing a humanitarian crisis.", "html": "2021 - A <a href=\"https://wikipedia.org/wiki/2021_Haiti_earthquake\" title=\"2021 Haiti earthquake\">magnitude 7.2 earthquake</a> strikes southwestern <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>, killing at least 2,248 people and causing a <a href=\"https://wikipedia.org/wiki/Humanitarian_crisis\" title=\"Humanitarian crisis\">humanitarian crisis</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2021_Haiti_earthquake\" title=\"2021 Haiti earthquake\">magnitude 7.2 earthquake</a> strikes southwestern <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>, killing at least 2,248 people and causing a <a href=\"https://wikipedia.org/wiki/Humanitarian_crisis\" title=\"Humanitarian crisis\">humanitarian crisis</a>.", "links": [{"title": "2021 Haiti earthquake", "link": "https://wikipedia.org/wiki/2021_Haiti_earthquake"}, {"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "Humanitarian crisis", "link": "https://wikipedia.org/wiki/Humanitarian_crisis"}]}, {"year": "2022", "text": "An explosion destroys a market in Armenia, killing six people and injuring dozens.", "html": "2022 - An <a href=\"https://wikipedia.org/wiki/2022_Yerevan_explosion\" title=\"2022 Yerevan explosion\">explosion destroys a market in Armenia, killing six people and injuring dozens</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2022_Yerevan_explosion\" title=\"2022 Yerevan explosion\">explosion destroys a market in Armenia, killing six people and injuring dozens</a>.", "links": [{"title": "2022 Yerevan explosion", "link": "https://wikipedia.org/wiki/2022_Yerevan_explosion"}]}, {"year": "2023", "text": "Former U.S. President <PERSON> is charged in Georgia along with 18 others in attempting to overturn the results of the 2020 election in that state, his fourth indictment of 2023.", "html": "2023 - Former U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Georgia_election_racketeering_prosecution\" title=\"Georgia election racketeering prosecution\">charged</a> in <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> along with 18 others in attempting to overturn the results of the <a href=\"https://wikipedia.org/wiki/2020_United_States_presidential_election\" title=\"2020 United States presidential election\">2020 election</a> in that state, his fourth indictment of 2023.", "no_year_html": "Former U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Georgia_election_racketeering_prosecution\" title=\"Georgia election racketeering prosecution\">charged</a> in <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> along with 18 others in attempting to overturn the results of the <a href=\"https://wikipedia.org/wiki/2020_United_States_presidential_election\" title=\"2020 United States presidential election\">2020 election</a> in that state, his fourth indictment of 2023.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Georgia election racketeering prosecution", "link": "https://wikipedia.org/wiki/Georgia_election_racketeering_prosecution"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}, {"title": "2020 United States presidential election", "link": "https://wikipedia.org/wiki/2020_United_States_presidential_election"}]}], "Births": [{"year": "1479", "text": "<PERSON> York (d. 1527)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/Catherine_<PERSON>_York\" title=\"<PERSON> of York\"><PERSON> York</a> (d. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catherine_<PERSON>_<PERSON>\" title=\"Catherine of York\"><PERSON> York</a> (d. 1527)", "links": [{"title": "Catherine of York", "link": "https://wikipedia.org/wiki/Catherine_<PERSON>_York"}]}, {"year": "1499", "text": "<PERSON>, 14th Earl of Oxford, English politician (d. 1526)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_Earl_of_Oxford\" title=\"<PERSON>, 14th Earl of Oxford\"><PERSON>, 14th Earl of Oxford</a>, English politician (d. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_Earl_of_Oxford\" title=\"<PERSON>, 14th Earl of Oxford\"><PERSON>, 14th Earl of Oxford</a>, English politician (d. 1526)", "links": [{"title": "<PERSON>, 14th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Oxford"}]}, {"year": "1502", "text": "<PERSON>, Flemish painter (d. 1550)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1530", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian mathematician and physicist (d. 1590)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mathematician and physicist (d. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mathematician and physicist (d. 1590)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1552", "text": "<PERSON>, Italian writer (d. 1623)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian writer (d. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian writer (d. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON><PERSON><PERSON>, Swiss-English scholar and author (d. 1671)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/M%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-English scholar and author (d. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-English scholar and author (d. 1671)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9ric_<PERSON>on"}]}, {"year": "1642", "text": "<PERSON><PERSON><PERSON>, Grand Duke of Tuscany (d. 1723)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON><PERSON>, Grand Duke of Tuscany</a> (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON><PERSON>, Grand Duke of Tuscany</a> (d. 1723)", "links": [{"title": "<PERSON><PERSON><PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON>si<PERSON>_III_de%27_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1653", "text": "<PERSON>, 2nd Duke of Albemarle, English colonel and politician, Lieutenant Governor of Jamaica (d. 1688)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Albemarle\" title=\"<PERSON>, 2nd Duke of Albemarle\"><PERSON>, 2nd Duke of Albemarle</a>, English colonel and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica\" class=\"mw-redirect\" title=\"Lieutenant Governor of Jamaica\">Lieutenant Governor of Jamaica</a> (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Albemarle\" title=\"<PERSON>, 2nd Duke of Albemarle\"><PERSON>, 2nd Duke of Albemarle</a>, English colonel and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica\" class=\"mw-redirect\" title=\"Lieutenant Governor of Jamaica\">Lieutenant Governor of Jamaica</a> (d. 1688)", "links": [{"title": "<PERSON>, 2nd Duke of Albemarle", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Albemarle"}, {"title": "Lieutenant Governor of Jamaica", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica"}]}, {"year": "1688", "text": "<PERSON> of Prussia (d. 1740)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (d. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (d. 1740)", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Prussia"}]}, {"year": "1714", "text": "<PERSON>, French painter (d. 1789)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, Austrian composer and conductor (d. 1793)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON> (d. 1823)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VII\" title=\"Pope Pius VII\"><PERSON> <PERSON></a> (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VII\" title=\"Pope Pius VII\"><PERSON> <PERSON></a> (d. 1823)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON><PERSON>, French painter and lithographer (d. 1836)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and lithographer (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and lithographer (d. 1836)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, Danish physicist and chemist (d. 1851)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%98rsted\" title=\"<PERSON>\"><PERSON></a>, Danish physicist and chemist (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%98rsted\" title=\"<PERSON>\"><PERSON></a>, Danish physicist and chemist (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Christian_%C3%98rsted"}]}, {"year": "1802", "text": "<PERSON><PERSON><PERSON>, English poet and novelist (d. 1838)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Letitia_<PERSON>_<PERSON>\" title=\"Letiti<PERSON>\">Letitia <PERSON></a>, English poet and novelist (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>_<PERSON>\" title=\"Letiti<PERSON>\">Letitia <PERSON></a>, English poet and novelist (d. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, American phrenologist and publisher (d. 1901)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American phrenologist and publisher (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American phrenologist and publisher (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, American lawyer, judge, and politician (d. 1874)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, German-Austrian psychologist and author (d. 1902)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian psychologist and author (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian psychologist and author (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Swiss lawyer and politician (d. 1922)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Anglo-Irish astronomer and author (d. 1915)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish astronomer and author (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish astronomer and author (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, American dentist and gambler (d. 1887)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and gambler (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and gambler (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ho<PERSON>day"}]}, {"year": "1860", "text": "<PERSON>, American author, artist, and naturalist (d. 1946)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, artist, and naturalist (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, artist, and naturalist (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American poet and author (d. 1940)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Italian mathematician and academic (d. 1952)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and academic (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and academic (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian mathematician and academic (d. 1962)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician and academic (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Belgian mathematician and academic (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON><PERSON><PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, American baseball player (d. 1912)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Childs\" title=\"<PERSON><PERSON> Childs\"><PERSON><PERSON></a>, American baseball player (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Childs\" title=\"<PERSON><PERSON> Childs\"><PERSON><PERSON></a>, American baseball player (d. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>id_Childs"}]}, {"year": "1867", "text": "<PERSON>,  English novelist and playwright, Nobel Prize laureate (d. 1933)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>u Emperor of China (d. 1908)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Guangxu_Emperor\" title=\"Guangxu Emperor\">Guangxu Emperor</a> of China (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guangxu_Emperor\" title=\"Guangxu Emperor\">Guangxu Emperor</a> of China (d. 1908)", "links": [{"title": "Guangxu Emperor", "link": "https://wikipedia.org/wiki/Guangxu_Emperor"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Russian-Lithuanian painter and illustrator (d. 1957)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Lithuanian painter and illustrator (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Lithuanian painter and illustrator (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON> of Serbia (d. 1903)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia\" title=\"<PERSON> of Serbia\"><PERSON> of Serbia</a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia\" title=\"<PERSON> of Serbia\"><PERSON> of Serbia</a> (d. 1903)", "links": [{"title": "<PERSON> of Serbia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia"}]}, {"year": "1881", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1953)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1953)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1883", "text": "<PERSON>, American biologist and academic (d. 1941)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Canadian-American physicist and academic (d. 1950)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American physicist and academic (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American physicist and academic (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Estonian lawyer and politician, Prime Minister of Estonia (d. 1976)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Estonia\" title=\"List of heads of government of Estonia\">Prime Minister of Estonia</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Estonia\" title=\"List of heads of government of Estonia\">Prime Minister of Estonia</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of government of Estonia", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Estonia"}]}, {"year": "1890", "text": "<PERSON>, German chemist and businessman (d. 1946)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and businessman (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and businessman (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON>, English pianist, composer, and critic (d. 1988)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Kai<PERSON><PERSON><PERSON>_<PERSON>ji_Sorabji\" title=\"<PERSON><PERSON><PERSON><PERSON>ji Sorabji\"><PERSON><PERSON><PERSON><PERSON></a>, English pianist, composer, and critic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Sorabji\" title=\"<PERSON><PERSON><PERSON><PERSON> Sorabji\"><PERSON><PERSON><PERSON><PERSON></a>, English pianist, composer, and critic (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>ji"}]}, {"year": "1894", "text": "<PERSON>, Australian rugby league player and coach (d. 1958)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Australian cricketer (d. 1973)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1973)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, American architect (d. 1984)[citation needed]", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American architect (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American architect (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, English fighter pilot (d. 1917)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert Ball\"><PERSON></a>, English fighter pilot (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert Ball\"><PERSON></a>, English fighter pilot (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Estonian director and cinematographer (d. 1980)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian director and cinematographer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian director and cinematographer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, German journalist (d. 1975)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Margret_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marg<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marg<PERSON>_<PERSON>veri"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish astronomer and mathematician (d. 2003)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/N%C3%BCzhet_G%C3%B6kdo%C4%9Fan\" title=\"Nüzhe<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish astronomer and mathematician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%BCzhet_G%C3%B6kdo%C4%9Fan\" title=\"Nüzhet <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish astronomer and mathematician (d. 2003)", "links": [{"title": "Nüzhet Gökdoğan", "link": "https://wikipedia.org/wiki/N%C3%BCzhet_G%C3%B6kdo%C4%9Fan"}]}, {"year": "1910", "text": "<PERSON>, French photographer (d. 2009)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French composer and producer (d. 1995)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and producer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and producer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American physicist and academic (d. 1985)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Australian director and producer (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American baseball player (d. 1981)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 1981)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1914", "text": "<PERSON>, American physicist, chemist, and academic (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, chemist, and academic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, chemist, and academic (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON> <PERSON><PERSON>, Australian political activist and publisher (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/B._A._Santamaria\" title=\"B. A. Santamaria\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian political activist and publisher (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B._A._Santamaria\" title=\"B. A. Santamaria\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian political activist and publisher (d. 1998)", "links": [{"title": "B. A. Santamaria", "link": "https://wikipedia.org/wiki/B._<PERSON>._Santamaria"}]}, {"year": "1916", "text": "<PERSON> and <PERSON>, American naturalists (twins, <PERSON> d. 2001, <PERSON> d. 2016)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a>, American naturalists (twins, <PERSON> d. 2001, <PERSON> d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a>, American naturalists (twins, <PERSON> d. 2001, <PERSON> d. 2016)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American businessman (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Wellington_Mara\" title=\"Wellington Mara\"><PERSON></a>, American businessman (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wellington_Mara\" title=\"Wellington Mara\"><PERSON></a>, American businessman (d. 2005)", "links": [{"title": "Wellington Mara", "link": "https://wikipedia.org/wiki/Wellington_Mara"}]}, {"year": "1923", "text": "<PERSON>, American actress (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Norwegian architect, designed the Hedmark Museum (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian architect, designed the <a href=\"https://wikipedia.org/wiki/Hedmark_Museum\" class=\"mw-redirect\" title=\"Hedmark Museum\">Hedmark Museum</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian architect, designed the <a href=\"https://wikipedia.org/wiki/Hedmark_Museum\" class=\"mw-redirect\" title=\"Hedmark Museum\">Hedmark Museum</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Hedmark Museum", "link": "https://wikipedia.org/wiki/Hedmark_Museum"}]}, {"year": "1924", "text": "<PERSON>, French conductor (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%AAtre\" title=\"<PERSON>\"><PERSON></a>, French conductor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%AAtre\" title=\"<PERSON>\"><PERSON></a>, French conductor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_Pr%C3%AAtre"}]}, {"year": "1925", "text": "<PERSON>, American critic and essayist (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic and essayist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic and essayist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, French author and illustrator (d. 1977)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goscinny\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goscinny\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Goscinny"}]}, {"year": "1926", "text": "<PERSON>, American singer and pianist (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Greco\"><PERSON></a>, American singer and pianist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Greco\"><PERSON></a>, American singer and pianist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Italian director and screenwriter (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rtm%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rtm%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lina_Wertm%C3%BCller"}]}, {"year": "1929", "text": "<PERSON>, Italian Roman Catholic prelate, bishop of the Roman Catholic Diocese of Lodi from 1989 to 2005 (d. 2021).", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Roman Catholic prelate, bishop of the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Lodi\" title=\"Roman Catholic Diocese of Lodi\">Roman Catholic Diocese of Lodi</a> from 1989 to 2005 (d. 2021).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Roman Catholic prelate, bishop of the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Lodi\" title=\"Roman Catholic Diocese of Lodi\">Roman Catholic Diocese of Lodi</a> from 1989 to 2005 (d. 2021).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roman Catholic Diocese of Lodi", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Lodi"}]}, {"year": "1929", "text": "<PERSON>, Nigerian boxer (d. 1971)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian boxer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tiger\"><PERSON></a>, Nigerian boxer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, British politician and Member of Parliament (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician and <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician and <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Member of Parliament (United Kingdom)", "link": "https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)"}]}, {"year": "1930", "text": "<PERSON>, American baseball player and manager (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American journalist, author, and screenwriter", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American author (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Swiss chemist and academic, Nobel Prize laureate (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1935", "text": "<PERSON>, American football player", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Dutch footballer (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American country music singer-songwriter and guitarist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian singer-songwriter and producer (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English miner and politician (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English miner and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English miner and politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American jazz and rock keyboardist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz and rock keyboardist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz and rock keyboardist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor, comedian, musician, producer, and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, musician, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, musician, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German director, producer, and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"Wim Wen<PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"Wim Wen<PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American soul/funk bass player and singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul/funk bass player and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul/funk bass player and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Susan Saint <PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Susan Saint <PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Saint_James"}]}, {"year": "1946", "text": "<PERSON>, Scottish race car driver and businessman (d. 2010)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver and businessman (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver and businessman (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English folk singer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English folk singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prior\"><PERSON></a>, English folk singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American wrestler", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Danish footballer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American cartoonist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2024)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du<PERSON>lap\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du<PERSON>lap\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American swimmer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American composer and conductor (d. 2015)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player and sportscaster (d. 2009)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American general", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American actress and television personality", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Jack%C3%A9e_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack%C3%A9e_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jack%C3%A9e_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English footballer and manager (d. 2015)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1956)\" title=\"<PERSON> (footballer, born 1956)\"><PERSON></a>, English footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1956)\" title=\"<PERSON> (footballer, born 1956)\"><PERSON></a>, English footballer and manager (d. 2015)", "links": [{"title": "<PERSON> (footballer, born 1956)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1956)"}]}, {"year": "1956", "text": "<PERSON>, American race car driver", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian lawyer and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American basketball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Johnson\" title=\"<PERSON> Johnson\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Johnson\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Johnson"}]}, {"year": "1960", "text": "<PERSON>, English singer and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American basketball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress and radio host", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Argentinian golfer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_C%C3%B3ceres\" title=\"<PERSON>\"><PERSON></a>, Argentinian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_C%C3%B3ceres\" title=\"<PERSON>\"><PERSON></a>, Argentinian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_C%C3%B3ceres"}]}, {"year": "1964", "text": "<PERSON>, American football player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English golfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American model, actress, and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Berry"}]}, {"year": "1966", "text": "<PERSON>, Swedish-Norwegian footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B8ken\" title=\"<PERSON>\"><PERSON></a>, Swedish-Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B8ken\" title=\"<PERSON>\"><PERSON></a>, Swedish-Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_L%C3%B8ken"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1968", "text": "<PERSON>, English-American actress and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English-American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English-American actress and producer", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1968", "text": "<PERSON>, Northern Irish golfer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English rugby player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American chemist and astronaut", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Danish footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Stig_T%C3%B8fting\" title=\"<PERSON>ig Tøfting\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stig_T%C3%B8fting\" title=\"<PERSON>ig Tøfting\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stig_T%C3%B8fting"}]}, {"year": "1970", "text": "<PERSON>, American rock guitarist: 2005 ", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian actor, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Finnish actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Finnish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Finnish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n"}]}, {"year": "1971", "text": "<PERSON>, American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Haitian businessman and politician, Prime Minister of Haiti", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Haiti", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Haiti"}]}, {"year": "1973", "text": "<PERSON>, Mexican footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Australian swimmer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian triple jumper", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian triple jumper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ato"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Ana<PERSON>sios_Kyriakos\" title=\"<PERSON><PERSON>sios Kyriakos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana<PERSON>sio<PERSON>_Kyriakos\" title=\"<PERSON><PERSON>sios Kyriakos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anastasios_Kyriakos"}]}, {"year": "1978", "text": "<PERSON>, New Zealand rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian pole vaulter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Australian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Australian pole vaulter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1980", "text": "<PERSON>, Australian politician, 47th Premier of South Australia", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 47th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 47th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian rugby league player, boxer, and sportscaster", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, boxer, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, boxer, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Ghanaian-American wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian-American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian-American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kingston"}]}, {"year": "1981", "text": "<PERSON>, American tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Ukrainian-Scottish tennis player (d. 2014)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Scottish tennis player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Scottish tennis player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Ukrainian-American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American actor and comedian", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American television personality", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Czech tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>erov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Birnerov%C3%A1"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Buchholz\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gorges\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josh_<PERSON>s"}]}, {"year": "1984", "text": "<PERSON>, English radio and television host", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Scottish tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Swedish tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B6derling\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B6<PERSON>ling\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robin_S%C3%B6derling"}]}, {"year": "1985", "text": "<PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>r%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%ADguez"}]}, {"year": "1987", "text": "<PERSON>, American wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Venezuelan baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football and baseball player and sportscaster", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football and baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football and baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, German ski jumper", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican baseball player ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Giovanny_Gallegos\" title=\"Giovanny Gallegos\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican baseball player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giovan<PERSON>_Gallegos\" title=\"Giovanny Gallegos\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican baseball player ", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giovanny_Gallegos"}]}, {"year": "1994", "text": "<PERSON>, British TV presenter.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British TV presenter.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jam<PERSON>\"><PERSON></a>, British TV presenter.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, French tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Belgian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American actress and producer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Martin\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Martin\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Martin"}]}], "Deaths": [{"year": "582", "text": "<PERSON><PERSON><PERSON> <PERSON>, Byzantine emperor", "html": "582 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_II_<PERSON>\" title=\"<PERSON><PERSON><PERSON> II Constantine\">T<PERSON><PERSON> <PERSON></a>, Byzantine emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_II_<PERSON>\" title=\"<PERSON><PERSON><PERSON> II Constantine\">T<PERSON><PERSON> <PERSON></a>, Byzantine emperor", "links": [{"title": "<PERSON><PERSON><PERSON> II <PERSON>", "link": "https://wikipedia.org/wiki/Tiberius_II_Constantine"}]}, {"year": "1040", "text": "<PERSON> of Scotland", "html": "1040 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a>", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1167", "text": "<PERSON><PERSON> of Dassel, Italian archbishop", "html": "1167 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Dassel\" title=\"<PERSON><PERSON> of Dassel\"><PERSON><PERSON> of Dassel</a>, Italian archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Das<PERSON>\" title=\"<PERSON><PERSON> of Dassel\"><PERSON><PERSON> of Dassel</a>, Italian archbishop", "links": [{"title": "<PERSON><PERSON> of Dassel", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Das<PERSON>"}]}, {"year": "1204", "text": "<PERSON><PERSON>, second S<PERSON><PERSON><PERSON> of the Kamakura shogunate", "html": "1204 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoriie\" title=\"Minamoto no Yoriie\"><PERSON><PERSON> no Yoriie</a>, second Shōgun of the <a href=\"https://wikipedia.org/wiki/Kamakura_shogunate\" title=\"Kamakura shogunate\">Kamakura shogunate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoriie\" title=\"Minamoto no Yoriie\"><PERSON><PERSON> no Yoriie</a>, second Shōgun of the <a href=\"https://wikipedia.org/wiki/Kamakura_shogunate\" title=\"Kamakura shogunate\">Kamakura shogunate</a>", "links": [{"title": "<PERSON><PERSON> no Yoriie", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Kamakura shogunate", "link": "https://wikipedia.org/wiki/Kamakura_shogunate"}]}, {"year": "1433", "text": "<PERSON> of Portugal (b. 1357)", "html": "1433 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> I of Portugal\"><PERSON> of Portugal</a> (b. 1357)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1357)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1464", "text": "<PERSON> (b. 1405)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_II\" title=\"Pope Pius II\"><PERSON> <PERSON> II</a> (b. 1405)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"Pope Pius II\"><PERSON> <PERSON> II</a> (b. 1405)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1573", "text": "<PERSON><PERSON>, Japanese daimyō (b. 1548)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/Sait%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sait%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (b. 1548)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sait%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "1691", "text": "<PERSON>, 1st Earl of Tyrconnell, Irish soldier and politician (b. 1630)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Tyrconnell\" title=\"<PERSON>, 1st Earl of Tyrconnell\"><PERSON>, 1st Earl of Tyrconnell</a>, Irish soldier and politician (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Tyrconnell\" title=\"<PERSON>, 1st Earl of Tyrconnell\"><PERSON>, 1st Earl of Tyrconnell</a>, Irish soldier and politician (b. 1630)", "links": [{"title": "<PERSON>, 1st Earl of Tyrconnell", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Tyrconnell"}]}, {"year": "1716", "text": "<PERSON><PERSON>, Capuchin nun from Spain, to Peru (b. 1660)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/Madre_Mar%C3%AD<PERSON>_Rosa\" title=\"<PERSON>re María <PERSON>\"><PERSON><PERSON></a>, Capuchin nun from Spain, to Peru (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madre_Mar%C3%ADa_Rosa\" title=\"<PERSON>re María <PERSON>\"><PERSON><PERSON></a>, Capuchin nun from Spain, to Peru (b. 1660)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Madre_Mar%C3%ADa_Rosa"}]}, {"year": "1727", "text": "<PERSON>, English organist and composer (b. 1678)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, German physician and scholar (b. 1716)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and scholar (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and scholar (b. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1784", "text": "<PERSON> the Elder, Irish-born English painter and academic (b. 1718)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Irish-born English painter and academic (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Irish-born English painter and academic (b. 1718)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}]}, {"year": "1852", "text": "<PERSON>, First Lady of the United States (b. 1788)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, First Lady of the United States (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, First Lady of the United States (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Polish-born actor and theatre director (b. 1787)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born actor and theatre director (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born actor and theatre director (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, French zoologist and entomologist (b. 1774)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>_Du<PERSON>%C3%A9ril\" title=\"<PERSON>\"><PERSON></a>, French zoologist and entomologist (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>_Du<PERSON>%C3%A9ril\" title=\"<PERSON>\"><PERSON></a>, French zoologist and entomologist (b. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>_Dum%C3%A9ril"}]}, {"year": "1870", "text": "<PERSON>, American admiral (b. 1801)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American priest, founded the Knights of Columbus (b. 1852)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, founded the <a href=\"https://wikipedia.org/wiki/Knights_of_Columbus\" title=\"Knights of Columbus\">Knights of Columbus</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, founded the <a href=\"https://wikipedia.org/wiki/Knights_of_Columbus\" title=\"Knights of Columbus\">Knights of Columbus</a> (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Knights of Columbus", "link": "https://wikipedia.org/wiki/Knights_of_Columbus"}]}, {"year": "1891", "text": "<PERSON>, First Lady of the United States (b. 1803)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, First Lady of the United States (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, First Lady of the United States (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, English soldier and painter (b. 1840)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Solomon\" title=\"Sime<PERSON> Solomon\"><PERSON><PERSON><PERSON></a>, English soldier and painter (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Solomon\" title=\"Sime<PERSON> Solomon\"><PERSON><PERSON><PERSON></a>, English soldier and painter (b. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>on_Solomon"}]}, {"year": "1909", "text": "<PERSON>, British engineer and author (b. 1829)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, British engineer and author (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, British engineer and author (b. 1829)", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(inventor)"}]}, {"year": "1922", "text": "<PERSON>, American physician and social reformer (b. 1846)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and social reformer (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and social reformer (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, German author and poet (b. 1890)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Klabund\" title=\"Klabund\"><PERSON><PERSON><PERSON><PERSON></a>, German author and poet (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Klabund\" title=\"Klabund\"><PERSON><PERSON><PERSON><PERSON></a>, German author and poet (b. 1890)", "links": [{"title": "Klabund", "link": "https://wikipedia.org/wiki/Klabund"}]}, {"year": "1938", "text": "<PERSON>, Australian cricketer and accountant (b. 1876)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and accountant (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and accountant (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rumble"}]}, {"year": "1941", "text": "<PERSON>, Polish martyr and saint (b. 1894)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish martyr and saint (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish martyr and saint (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, French chemist and academic, Nobel Prize laureate (b. 1854)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1854)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_(chemist)"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1943", "text": "<PERSON>, American baseball player and manager (b. 1871)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Czech gymnast (b. 1926)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Eli%C5%A1ka_Mis%C3%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech gymnast (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eli%C5%A1ka_Mis%C3%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech gymnast (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eli%C5%A1ka_Mis%C3%A1kov%C3%A1"}]}, {"year": "1951", "text": "<PERSON>, American publisher and politician, founded the Hearst Corporation (b. 1863)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, founded the <a href=\"https://wikipedia.org/wiki/Hearst_Corporation\" class=\"mw-redirect\" title=\"Hearst Corporation\">Hearst Corporation</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, founded the <a href=\"https://wikipedia.org/wiki/Hearst_Corporation\" class=\"mw-redirect\" title=\"Hearst Corporation\">Hearst Corporation</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Hearst Corporation", "link": "https://wikipedia.org/wiki/Hearst_Corporation"}]}, {"year": "1954", "text": "<PERSON>, German pilot and designer (b. 1868)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and designer (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and designer (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and publisher, Librarian of Congress (b. 1861)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and publisher, <a href=\"https://wikipedia.org/wiki/Li<PERSON><PERSON>_of_Congress\" title=\"Li<PERSON>rian of Congress\"><PERSON><PERSON><PERSON> of Congress</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and publisher, <a href=\"https://wikipedia.org/wiki/Li<PERSON><PERSON>_of_Congress\" title=\"<PERSON><PERSON>rian of Congress\"><PERSON><PERSON>rian of Congress</a> (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Librarian of Congress", "link": "https://wikipedia.org/wiki/Librarian_of_Congress"}]}, {"year": "1956", "text": "<PERSON><PERSON>, German poet, playwright, and director (b. 1898)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brecht\"><PERSON><PERSON></a>, German poet, playwright, and director (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brecht\"><PERSON><PERSON></a>, German poet, playwright, and director (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>recht"}]}, {"year": "1956", "text": "<PERSON>, German lawyer and politician, Reich Minister of Foreign Affairs (b. 1873)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)\" title=\"Minister for Foreign Affairs (Germany)\">Reich Minister of Foreign Affairs</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)\" title=\"Minister for Foreign Affairs (Germany)\">Reich Minister of Foreign Affairs</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Germany)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French physicist and chemist, Nobel Prize laureate (b. 1900)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_<PERSON><PERSON><PERSON>-<PERSON><PERSON>e"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1963", "text": "<PERSON>, American director, playwright, and screenwriter (b. 1906)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, playwright, and screenwriter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, playwright, and screenwriter (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter (b. 1934)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Estonian skier (b. 1911)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English motorcycle racer and race car driver (b. 1931)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English motorcycle racer and race car driver (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English motorcycle racer and race car driver (b. 1931)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1972", "text": "<PERSON>, American actor, pianist, and composer (b. 1906)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Oscar_Levant\" title=\"Oscar Levant\"><PERSON></a>, American actor, pianist, and composer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_Levant\" title=\"Oscar Levant\"><PERSON></a>, American actor, pianist, and composer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oscar_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, French author and poet (b. 1885)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American journalist and author (b. 1908)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English author and illustrator (b. 1907)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian-American model and actress (b. 1960)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American model and actress (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American model and actress (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Austrian conductor and director (b. 1894)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hm\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and director (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hm\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and director (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_B%C3%B6hm"}]}, {"year": "1981", "text": "<PERSON>, South African cricketer (b. 1910)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Burmese monk and philosopher (b. 1904)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Burmese monk and philosopher (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Burmese monk and philosopher (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>adaw"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American baseball player, coach, and manager (b. 1904)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist and playwright (b. 1894)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist and playwright (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist and playwright (b. 1894)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress (b. 1899)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Son<PERSON>gaard"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1939)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, South African-English singer-songwriter and playwright (b. 1945): 1712 ", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English singer-songwriter and playwright (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English singer-songwriter and playwright (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Italian race car driver and businessman, founded <PERSON> (b. 1898)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/En<PERSON>_Ferrari\" title=\"Enzo Ferrari\"><PERSON><PERSON></a>, Italian race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/Ferrari\" title=\"Ferrari\">Ferrari</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/En<PERSON>_Ferrari\" title=\"Enzo Ferrari\"><PERSON><PERSON></a>, Italian race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/Ferrari\" title=\"Ferrari\">Ferrari</a> (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ferrari"}, {"title": "Ferrari", "link": "https://wikipedia.org/wiki/Ferrari"}]}, {"year": "1991", "text": "<PERSON>, Argentinian race car driver (b. 1920)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American lawyer and judge (b. 1904)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Bulgarian-Swiss author, Nobel Prize laureate (b. 1905)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-Swiss author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-Swiss author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1994", "text": "<PERSON>, American actress, playwright, and author (b. 1912)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, playwright, and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, playwright, and author (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Romanian conductor and composer (b. 1912)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian conductor and composer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian conductor and composer (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ache"}]}, {"year": "1999", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player and sportscaster (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ee We<PERSON> Reese\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player and sportscaster (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ee We<PERSON> Reese\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player and sportscaster (b. 1918)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American painter and sculptor (b. 1923)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, German footballer (b. 1929)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Polish-born American novelist, essayist, and poet, Nobel Prize laureate (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_Mi%C5%82osz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-born American novelist, essayist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_Mi%C5%82osz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-born American novelist, essayist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Czes%C5%82aw_Mi%C5%82osz"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2004", "text": "<PERSON>, New Zealand-English lawyer and politician (b. 1918)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, New Zealand-English lawyer and politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, New Zealand-English lawyer and politician (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor (b. 1949)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Russian pianist and composer (b. 1913)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American photographer (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, Chief Minister of Maharashtra (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian chess player (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Svetozar_Gligori%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian chess player (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Svetozar_Gligori%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian chess player (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Svetozar_Gligori%C4%87"}]}, {"year": "2012", "text": "<PERSON>, American actress (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist and author (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American journalist and academic, co-founded Moment Magazine (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic, co-founded <i><a href=\"https://wikipedia.org/wiki/Moment_(magazine)\" title=\"Moment (magazine)\">Moment Magazine</a></i> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic, co-founded <i><a href=\"https://wikipedia.org/wiki/Moment_(magazine)\" title=\"Moment (magazine)\">Moment Magazine</a></i> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Moment (magazine)", "link": "https://wikipedia.org/wiki/Moment_(magazine)"}]}, {"year": "2014", "text": "<PERSON>, American politician (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American songwriter and producer (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, American actor (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>yvu<PERSON>_<PERSON>\" title=\"<PERSON>yvu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yvu<PERSON>_<PERSON>\" title=\"<PERSON>yvu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yvush_Finkel"}]}, {"year": "2018", "text": "<PERSON>, American singer (b. 1975)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Australian footballer and coach (b. 1935)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English classical guitarist and lutenist (b. 1933)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English classical guitarist and lutenist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English classical guitarist and lutenist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, British tennis player (b. 1934)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American politician, Governor of Illinois (b. 1936)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician, Governor of Illinois (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician, Governor of Illinois (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American historian and scholar of Burmese and Southeast Asian history (b. 1946)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar of Burmese and Southeast Asian history (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar of Burmese and Southeast Asian history (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>hwin"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Bangladeshi Islamic lecturer, politician (b. 1940)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi Islamic lecturer, politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi Islamic lecturer, politician (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American actress (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}