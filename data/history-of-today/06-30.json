{"date": "June 30", "url": "https://wikipedia.org/wiki/June_30", "data": {"Events": [{"year": "296", "text": "Pope <PERSON><PERSON><PERSON> begins his papacy.", "html": "296 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\">Pope <PERSON><PERSON><PERSON></a> begins his papacy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\">Pope <PERSON><PERSON><PERSON></a> begins his papacy.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "763", "text": "The Byzantine army of emperor <PERSON> defeats the Bulgarian forces in the Battle of Anchialus.", "html": "763 - The <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army of emperor <a href=\"https://wikipedia.org/wiki/Constantine_V\" title=\"Constantine V\"><PERSON> V</a> defeats the <a href=\"https://wikipedia.org/wiki/First_Bulgarian_Empire\" title=\"First Bulgarian Empire\">Bulgarian</a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Anchialus_(763)\" title=\"Battle of Anchialus (763)\">Battle of Anchialus</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army of emperor <a href=\"https://wikipedia.org/wiki/Constantine_V\" title=\"Constantine V\"><PERSON> V</a> defeats the <a href=\"https://wikipedia.org/wiki/First_Bulgarian_Empire\" title=\"First Bulgarian Empire\">Bulgarian</a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Anchialus_(763)\" title=\"Battle of Anchialus (763)\">Battle of Anchialus</a>.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Constantine V", "link": "https://wikipedia.org/wiki/Constantine_V"}, {"title": "First Bulgarian Empire", "link": "https://wikipedia.org/wiki/First_Bulgarian_Empire"}, {"title": "Battle of Anchialus (763)", "link": "https://wikipedia.org/wiki/Battle_of_Anchialus_(763)"}]}, {"year": "1422", "text": "Battle of Arbedo between the duke of Milan and the Swiss cantons.", "html": "1422 - <a href=\"https://wikipedia.org/wiki/Battle_of_Arbedo\" title=\"Battle of Arbedo\">Battle of Arbedo</a> between the duke of <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> and the <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Swiss</a> <a href=\"https://wikipedia.org/wiki/Cantons_of_Switzerland\" title=\"Cantons of Switzerland\">cantons</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Arbedo\" title=\"Battle of Arbedo\">Battle of Arbedo</a> between the duke of <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> and the <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Swiss</a> <a href=\"https://wikipedia.org/wiki/Cantons_of_Switzerland\" title=\"Cantons of Switzerland\">cantons</a>.", "links": [{"title": "Battle of Arbedo", "link": "https://wikipedia.org/wiki/Battle_of_Arbedo"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}, {"title": "Switzerland", "link": "https://wikipedia.org/wiki/Switzerland"}, {"title": "Cantons of Switzerland", "link": "https://wikipedia.org/wiki/Cantons_of_Switzerland"}]}, {"year": "1521", "text": "Spanish forces defeat a combined French and Navarrese army at the Battle of Noáin during the Spanish conquest of Iberian Navarre.", "html": "1521 - Spanish forces defeat a combined <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Navarre\" title=\"Kingdom of Navarre\">Navarrese</a> army at the <a href=\"https://wikipedia.org/wiki/Battle_of_No%C3%A1in\" title=\"Battle of Noáin\">Battle of Noáin</a> during the <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_Iberian_Navarre\" title=\"Spanish conquest of Iberian Navarre\">Spanish conquest of Iberian Navarre</a>.", "no_year_html": "Spanish forces defeat a combined <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Navarre\" title=\"Kingdom of Navarre\">Navarrese</a> army at the <a href=\"https://wikipedia.org/wiki/Battle_of_No%C3%A1in\" title=\"Battle of Noáin\">Battle of Noáin</a> during the <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_Iberian_Navarre\" title=\"Spanish conquest of Iberian Navarre\">Spanish conquest of Iberian Navarre</a>.", "links": [{"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Kingdom of Navarre", "link": "https://wikipedia.org/wiki/Kingdom_of_Navarre"}, {"title": "Battle of Noáin", "link": "https://wikipedia.org/wiki/Battle_of_No%C3%A1in"}, {"title": "Spanish conquest of Iberian Navarre", "link": "https://wikipedia.org/wiki/Spanish_conquest_of_Iberian_Navarre"}]}, {"year": "1559", "text": "King <PERSON> of France is mortally wounded in a jousting match against <PERSON>, comte <PERSON>.", "html": "1559 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> is mortally wounded in a <a href=\"https://wikipedia.org/wiki/Jousting\" title=\"Jousting\">jousting</a> match against <a href=\"https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte de <PERSON>\"><PERSON>, comte de <PERSON></a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> is mortally wounded in a <a href=\"https://wikipedia.org/wiki/Jousting\" title=\"Jousting\">jousting</a> match against <a href=\"https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte de <PERSON>\"><PERSON>, comte de <PERSON></a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Jousting", "link": "https://wikipedia.org/wiki/Jousting"}, {"title": "<PERSON>, comte de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>"}]}, {"year": "1598", "text": "The Spanish-held Castillo San Felipe del Morro in San Juan, Puerto Rico having been besieged for fifteen days, surrenders to an English force under Sir <PERSON>, Earl of Cumberland.", "html": "1598 - The Spanish-held <a href=\"https://wikipedia.org/wiki/Castillo_San_Felipe_del_Morro\" title=\"Castillo San Felipe del Morro\">Castillo San Felipe del Morro</a> in <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan</a>, <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a> having <a href=\"https://wikipedia.org/wiki/Battle_of_San_Juan_(1598)\" title=\"Battle of San Juan (1598)\">been besieged for fifteen days</a>, surrenders to an English force under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 3rd Earl of Cumberland\">Sir <PERSON>, Earl of Cumberland</a>.", "no_year_html": "The Spanish-held <a href=\"https://wikipedia.org/wiki/Castillo_San_Felipe_del_Morro\" title=\"Castillo San Felipe del Morro\">Castillo San Felipe del Morro</a> in <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan</a>, <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a> having <a href=\"https://wikipedia.org/wiki/Battle_of_San_Juan_(1598)\" title=\"Battle of San Juan (1598)\">been besieged for fifteen days</a>, surrenders to an English force under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 3rd Earl of <PERSON>\">Sir <PERSON>, Earl of Cumberland</a>.", "links": [{"title": "Castillo San Felipe del Morro", "link": "https://wikipedia.org/wiki/Castillo_San_Felipe_<PERSON>_<PERSON>rro"}, {"title": "San Juan, Puerto Rico", "link": "https://wikipedia.org/wiki/San_Juan,_Puerto_Rico"}, {"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}, {"title": "Battle of San Juan (1598)", "link": "https://wikipedia.org/wiki/Battle_of_San_Juan_(1598)"}, {"title": "<PERSON>, 3rd Earl of Cumberland", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Cumberland"}]}, {"year": "1632", "text": "The University of Tartu is founded.", "html": "1632 - The <a href=\"https://wikipedia.org/wiki/University_of_Tartu\" title=\"University of Tartu\">University of Tartu</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/University_of_Tartu\" title=\"University of Tartu\">University of Tartu</a> is founded.", "links": [{"title": "University of Tartu", "link": "https://wikipedia.org/wiki/University_of_Tartu"}]}, {"year": "1651", "text": "The Deluge: Khmelnytsky Uprising: The Battle of Berestechko ends with a Polish victory.", "html": "1651 - <a href=\"https://wikipedia.org/wiki/Deluge_(history)\" title=\"Deluge (history)\">The Deluge</a>: <a href=\"https://wikipedia.org/wiki/Khmelnytsky_Uprising\" title=\"Khmelnytsky Uprising\">Khmelnytsky Uprising</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Berestechko\" title=\"Battle of Berestechko\">Battle of Berestechko</a> ends with a Polish victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deluge_(history)\" title=\"Deluge (history)\">The Deluge</a>: <a href=\"https://wikipedia.org/wiki/Khmelnytsky_Uprising\" title=\"Khmelnytsky Uprising\">Khmelnytsky Uprising</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Berestechko\" title=\"Battle of Berestechko\">Battle of Berestechko</a> ends with a Polish victory.", "links": [{"title": "Deluge (history)", "link": "https://wikipedia.org/wiki/Deluge_(history)"}, {"title": "Khmelnytsky Uprising", "link": "https://wikipedia.org/wiki/Khmelnytsky_Uprising"}, {"title": "Battle of Berestechko", "link": "https://wikipedia.org/wiki/Battle_of_Berestechko"}]}, {"year": "1688", "text": "The Immortal Seven issue the Invitation to <PERSON>, which would culminate in the Glorious Revolution.", "html": "1688 - The <a href=\"https://wikipedia.org/wiki/Immortal_Seven\" class=\"mw-redirect\" title=\"Immortal Seven\">Immortal Seven</a> issue the <a href=\"https://wikipedia.org/wiki/Invitation_to_<PERSON>\" title=\"Invitation to <PERSON>\">Invitation to <PERSON></a>, which would culminate in the <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Immortal_Seven\" class=\"mw-redirect\" title=\"Immortal Seven\">Immortal Seven</a> issue the <a href=\"https://wikipedia.org/wiki/Invitation_to_<PERSON>\" title=\"Invitation to <PERSON>\">Invitation to <PERSON></a>, which would culminate in the <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>.", "links": [{"title": "Immortal Seven", "link": "https://wikipedia.org/wiki/Immortal_Seven"}, {"title": "Invitation to <PERSON>", "link": "https://wikipedia.org/wiki/Invitation_to_<PERSON>"}, {"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}]}, {"year": "1703", "text": "The Battle of Ekeren between a Dutch force and a French force.", "html": "1703 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Ekeren\" title=\"Battle of Ekeren\">Battle of Ekeren</a> between a Dutch force and a French force.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Ekeren\" title=\"Battle of Ekeren\">Battle of Ekeren</a> between a Dutch force and a French force.", "links": [{"title": "Battle of Ekeren", "link": "https://wikipedia.org/wiki/Battle_of_Ekeren"}]}, {"year": "1758", "text": "Seven Years' War: Habsburg Austrian forces destroy a Prussian reinforcement and supply convoy in the Battle of Domstadtl, helping to expel Prussian King <PERSON> the <PERSON> from Moravia.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Habsburg Austrian</a> forces destroy a <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> reinforcement and supply convoy in the <a href=\"https://wikipedia.org/wiki/Battle_of_Domstadtl\" title=\"Battle of Domstadtl\">Battle of Domstadtl</a>, helping to expel Prussian King <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> from <a href=\"https://wikipedia.org/wiki/Moravia\" title=\"Moravia\">Moravia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Habsburg Austrian</a> forces destroy a <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> reinforcement and supply convoy in the <a href=\"https://wikipedia.org/wiki/Battle_of_Domstadtl\" title=\"Battle of Domstadtl\">Battle of Domstadtl</a>, helping to expel Prussian King <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> from <a href=\"https://wikipedia.org/wiki/Moravia\" title=\"Moravia\">Moravia</a>.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "Habsburg monarchy", "link": "https://wikipedia.org/wiki/Habsburg_monarchy"}, {"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}, {"title": "Battle of Domstadtl", "link": "https://wikipedia.org/wiki/Battle_of_Dom<PERSON>l"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Moravia", "link": "https://wikipedia.org/wiki/Moravia"}]}, {"year": "1794", "text": "Northwest Indian War: Native American forces under Blue Jacket attack Fort Recovery.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>: Native American forces under <a href=\"https://wikipedia.org/wiki/Blue_Jacket\" title=\"Blue Jacket\">Blue Jacket</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Recovery\" class=\"mw-redirect\" title=\"Siege of Fort Recovery\">attack Fort Recovery</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>: Native American forces under <a href=\"https://wikipedia.org/wiki/Blue_Jacket\" title=\"Blue Jacket\">Blue Jacket</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Recovery\" class=\"mw-redirect\" title=\"Siege of Fort Recovery\">attack Fort Recovery</a>.", "links": [{"title": "Northwest Indian War", "link": "https://wikipedia.org/wiki/Northwest_Indian_War"}, {"title": "Blue Jacket", "link": "https://wikipedia.org/wiki/Blue_Jacket"}, {"title": "Siege of Fort Recovery", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Recovery"}]}, {"year": "1805", "text": "Under An act to divide the Indiana Territory into two separate governments, adopted by the U.S. Congress on January 11, 1805, the Michigan Territory is organized.", "html": "1805 - Under <i>An act to divide the Indiana Territory into two separate governments</i>, adopted by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> on January 11, 1805, the <a href=\"https://wikipedia.org/wiki/Michigan_Territory\" title=\"Michigan Territory\">Michigan Territory</a> is organized.", "no_year_html": "Under <i>An act to divide the Indiana Territory into two separate governments</i>, adopted by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> on January 11, 1805, the <a href=\"https://wikipedia.org/wiki/Michigan_Territory\" title=\"Michigan Territory\">Michigan Territory</a> is organized.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Michigan Territory", "link": "https://wikipedia.org/wiki/Michigan_Territory"}]}, {"year": "1859", "text": "French acrobat <PERSON> crosses Niagara Falls on a tightrope.", "html": "1859 - French <a href=\"https://wikipedia.org/wiki/Acrobatics\" title=\"Acrobatics\">acrobat</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> crosses <a href=\"https://wikipedia.org/wiki/Niagara_Falls\" title=\"Niagara Falls\">Niagara Falls</a> on a <a href=\"https://wikipedia.org/wiki/Tightrope_walking\" title=\"Tightrope walking\">tightrope</a>.", "no_year_html": "French <a href=\"https://wikipedia.org/wiki/Acrobatics\" title=\"Acrobatics\">acrobat</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> crosses <a href=\"https://wikipedia.org/wiki/Niagara_Falls\" title=\"Niagara Falls\">Niagara Falls</a> on a <a href=\"https://wikipedia.org/wiki/Tightrope_walking\" title=\"Tightrope walking\">tightrope</a>.", "links": [{"title": "Acrobatics", "link": "https://wikipedia.org/wiki/Acrobatics"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Niagara Falls", "link": "https://wikipedia.org/wiki/Niagara_Falls"}, {"title": "Tightrope walking", "link": "https://wikipedia.org/wiki/Tightrope_walking"}]}, {"year": "1860", "text": "The 1860 Oxford evolution debate at the Oxford University Museum of Natural History takes place.", "html": "1860 - The <a href=\"https://wikipedia.org/wiki/1860_Oxford_evolution_debate\" title=\"1860 Oxford evolution debate\">1860 Oxford evolution debate</a> at the <a href=\"https://wikipedia.org/wiki/Oxford_University_Museum_of_Natural_History\" title=\"Oxford University Museum of Natural History\">Oxford University Museum of Natural History</a> takes place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1860_Oxford_evolution_debate\" title=\"1860 Oxford evolution debate\">1860 Oxford evolution debate</a> at the <a href=\"https://wikipedia.org/wiki/Oxford_University_Museum_of_Natural_History\" title=\"Oxford University Museum of Natural History\">Oxford University Museum of Natural History</a> takes place.", "links": [{"title": "1860 Oxford evolution debate", "link": "https://wikipedia.org/wiki/1860_Oxford_evolution_debate"}, {"title": "Oxford University Museum of Natural History", "link": "https://wikipedia.org/wiki/Oxford_University_Museum_of_Natural_History"}]}, {"year": "1864", "text": "U.S. President <PERSON> grants Yosemite Valley to California for \"public use, resort and recreation\".", "html": "1864 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> grants <a href=\"https://wikipedia.org/wiki/Yosemite_Valley\" title=\"Yosemite Valley\">Yosemite Valley</a> to <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> for \"public use, resort and recreation\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> grants <a href=\"https://wikipedia.org/wiki/Yosemite_Valley\" title=\"Yosemite Valley\">Yosemite Valley</a> to <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> for \"public use, resort and recreation\".", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Yosemite Valley", "link": "https://wikipedia.org/wiki/Yosemite_Valley"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1882", "text": "<PERSON> is hanged in Washington, D.C. for the assassination of U.S. President <PERSON>.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hanged in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a> for the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassination</a> of <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hanged in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a> for the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassination</a> of <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "The first transcontinental train trip across Canada departs from Montreal, Quebec. It arrives in Port Moody, British Columbia on July 4.", "html": "1886 - The first transcontinental train trip across Canada departs from <a href=\"https://wikipedia.org/wiki/Montreal,_Quebec\" class=\"mw-redirect\" title=\"Montreal, Quebec\">Montreal, Quebec</a>. It arrives in <a href=\"https://wikipedia.org/wiki/Port_Moody,_British_Columbia\" class=\"mw-redirect\" title=\"Port Moody, British Columbia\">Port Moody, British Columbia</a> on <a href=\"https://wikipedia.org/wiki/July_4\" title=\"July 4\">July 4</a>.", "no_year_html": "The first transcontinental train trip across Canada departs from <a href=\"https://wikipedia.org/wiki/Montreal,_Quebec\" class=\"mw-redirect\" title=\"Montreal, Quebec\">Montreal, Quebec</a>. It arrives in <a href=\"https://wikipedia.org/wiki/Port_Moody,_British_Columbia\" class=\"mw-redirect\" title=\"Port Moody, British Columbia\">Port Moody, British Columbia</a> on <a href=\"https://wikipedia.org/wiki/July_4\" title=\"July 4\">July 4</a>.", "links": [{"title": "Montreal, Quebec", "link": "https://wikipedia.org/wiki/Montreal,_Quebec"}, {"title": "Port Moody, British Columbia", "link": "https://wikipedia.org/wiki/Port_Moody,_British_Columbia"}, {"title": "July 4", "link": "https://wikipedia.org/wiki/July_4"}]}, {"year": "1892", "text": "The Homestead Strike begins near Pittsburgh, Pennsylvania.", "html": "1892 - The <a href=\"https://wikipedia.org/wiki/Homestead_Strike\" class=\"mw-redirect\" title=\"Homestead Strike\">Homestead Strike</a> begins near <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>, <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Homestead_Strike\" class=\"mw-redirect\" title=\"Homestead Strike\">Homestead Strike</a> begins near <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>, <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>.", "links": [{"title": "Homestead Strike", "link": "https://wikipedia.org/wiki/Homestead_Strike"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}]}, {"year": "1900", "text": "A savage fire wrecked three steamships docked at a pier in Hoboken, New Jersey. Over 200 crew members and passengers are killed, and hundreds injured.", "html": "1900 - A savage fire wrecked three steamships docked at a pier in <a href=\"https://wikipedia.org/wiki/Hoboken\" class=\"mw-redirect\" title=\"Hoboken\">Hoboken</a>, New Jersey. Over 200 crew members and passengers are killed, and hundreds injured.", "no_year_html": "A savage fire wrecked three steamships docked at a pier in <a href=\"https://wikipedia.org/wiki/Hoboken\" class=\"mw-redirect\" title=\"Hoboken\">Hoboken</a>, New Jersey. Over 200 crew members and passengers are killed, and hundreds injured.", "links": [{"title": "Hoboken", "link": "https://wikipedia.org/wiki/Hoboken"}]}, {"year": "1905", "text": "<PERSON> sends the article On the Electrodynamics of Moving Bodies, in which he introduces special relativity, for publication in Annalen der Physik.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Einstein\"><PERSON></a> sends the article <i><a href=\"https://wikipedia.org/wiki/On_the_Electrodynamics_of_Moving_Bodies\" class=\"mw-redirect\" title=\"On the Electrodynamics of Moving Bodies\">On the Electrodynamics of Moving Bodies</a></i>, in which he introduces <a href=\"https://wikipedia.org/wiki/Special_relativity\" title=\"Special relativity\">special relativity</a>, for publication in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_der_Physik\" title=\"Annalen der Physik\"><PERSON><PERSON> der Physik</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Albert Einstein\"><PERSON></a> sends the article <i><a href=\"https://wikipedia.org/wiki/On_the_Electrodynamics_of_Moving_Bodies\" class=\"mw-redirect\" title=\"On the Electrodynamics of Moving Bodies\">On the Electrodynamics of Moving Bodies</a></i>, in which he introduces <a href=\"https://wikipedia.org/wiki/Special_relativity\" title=\"Special relativity\">special relativity</a>, for publication in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_der_Physik\" title=\"<PERSON>len der Physik\"><PERSON><PERSON> der Physik</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "On the Electrodynamics of Moving Bodies", "link": "https://wikipedia.org/wiki/On_the_Electrodynamics_of_Moving_Bodies"}, {"title": "Special relativity", "link": "https://wikipedia.org/wiki/Special_relativity"}, {"title": "Annalen der Physik", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Physik"}]}, {"year": "1906", "text": "The United States Congress passes the Meat Inspection Act and Pure Food and Drug Act.", "html": "1906 - The United States Congress passes the <a href=\"https://wikipedia.org/wiki/Meat_Inspection_Act\" class=\"mw-redirect\" title=\"Meat Inspection Act\">Meat Inspection Act</a> and <a href=\"https://wikipedia.org/wiki/Pure_Food_and_Drug_Act\" title=\"Pure Food and Drug Act\">Pure Food and Drug Act</a>.", "no_year_html": "The United States Congress passes the <a href=\"https://wikipedia.org/wiki/Meat_Inspection_Act\" class=\"mw-redirect\" title=\"Meat Inspection Act\">Meat Inspection Act</a> and <a href=\"https://wikipedia.org/wiki/Pure_Food_and_Drug_Act\" title=\"Pure Food and Drug Act\">Pure Food and Drug Act</a>.", "links": [{"title": "Meat Inspection Act", "link": "https://wikipedia.org/wiki/Meat_Inspection_Act"}, {"title": "Pure Food and Drug Act", "link": "https://wikipedia.org/wiki/Pure_Food_and_Drug_Act"}]}, {"year": "1908", "text": "The Tunguska Event, the largest impact event on Earth in human recorded history, resulting in a massive explosion over Eastern Siberia.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/Tunguska_Event\" class=\"mw-redirect\" title=\"Tunguska Event\">Tunguska Event</a>, the largest <a href=\"https://wikipedia.org/wiki/Impact_event\" title=\"Impact event\">impact event</a> on <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> in human recorded history, resulting in a massive explosion over <a href=\"https://wikipedia.org/wiki/Eastern_Siberia\" class=\"mw-redirect\" title=\"Eastern Siberia\">Eastern Siberia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tunguska_Event\" class=\"mw-redirect\" title=\"Tunguska Event\">Tunguska Event</a>, the largest <a href=\"https://wikipedia.org/wiki/Impact_event\" title=\"Impact event\">impact event</a> on <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> in human recorded history, resulting in a massive explosion over <a href=\"https://wikipedia.org/wiki/Eastern_Siberia\" class=\"mw-redirect\" title=\"Eastern Siberia\">Eastern Siberia</a>.", "links": [{"title": "Tunguska Event", "link": "https://wikipedia.org/wiki/Tunguska_Event"}, {"title": "Impact event", "link": "https://wikipedia.org/wiki/Impact_event"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}, {"title": "Eastern Siberia", "link": "https://wikipedia.org/wiki/Eastern_Siberia"}]}, {"year": "1912", "text": "The Regina Cyclone, Canada's deadliest tornado event, kills 28 people in Regina, Saskatchewan.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/Regina_Cyclone\" title=\"Regina Cyclone\">Regina Cyclone</a>, Canada's deadliest tornado event, kills 28 people in <a href=\"https://wikipedia.org/wiki/Regina,_Saskatchewan\" title=\"Regina, Saskatchewan\">Regina, Saskatchewan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Regina_Cyclone\" title=\"Regina Cyclone\">Regina Cyclone</a>, Canada's deadliest tornado event, kills 28 people in <a href=\"https://wikipedia.org/wiki/Regina,_Saskatchewan\" title=\"Regina, Saskatchewan\">Regina, Saskatchewan</a>.", "links": [{"title": "Regina Cyclone", "link": "https://wikipedia.org/wiki/Regina_Cyclone"}, {"title": "Regina, Saskatchewan", "link": "https://wikipedia.org/wiki/Regina,_Saskatchewan"}]}, {"year": "1916", "text": "World War I: In \"the day Sussex died\", elements of the Royal Sussex Regiment take heavy casualties in the Battle of the Boar's Head at Richebourg-l'Avoué in France.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: In \"the day Sussex died\", elements of the <a href=\"https://wikipedia.org/wiki/Royal_Sussex_Regiment\" title=\"Royal Sussex Regiment\">Royal Sussex Regiment</a> take heavy casualties in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Boar%27s_Head\" title=\"Battle of the Boar's Head\">Battle of the Boar's Head</a> at <a href=\"https://wikipedia.org/wiki/Richebourg-l%27Avou%C3%A9\" title=\"Richebourg-l'Avoué\">Richebourg-l'Avoué</a> in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: In \"the day Sussex died\", elements of the <a href=\"https://wikipedia.org/wiki/Royal_Sussex_Regiment\" title=\"Royal Sussex Regiment\">Royal Sussex Regiment</a> take heavy casualties in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Boar%27s_Head\" title=\"Battle of the Boar's Head\">Battle of the Boar's Head</a> at <a href=\"https://wikipedia.org/wiki/Richebourg-l%27Avou%C3%A9\" title=\"Richebourg-l'Avoué\">Richebourg-l'Avoué</a> in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Royal Sussex Regiment", "link": "https://wikipedia.org/wiki/Royal_Sussex_Regiment"}, {"title": "Battle of the Boar's Head", "link": "https://wikipedia.org/wiki/Battle_of_the_Boar%27s_Head"}, {"title": "Richebourg-l'Avoué", "link": "https://wikipedia.org/wiki/Richebourg-l%27Avou%C3%A9"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}]}, {"year": "1921", "text": "U.S. President <PERSON> appoints former President <PERSON> as Chief Justice of the United States.", "html": "1921 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints former President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints former President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1922", "text": "In Washington D.C., U.S. Secretary of State <PERSON> and Dominican Ambassador <PERSON> sign the Hughes-Peynado agreement, which ends the United States occupation of the Dominican Republic.", "html": "1922 - In Washington D.C., U.S. Secretary of State <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Dominican Ambassador <PERSON> sign the Hughes-Peynado agreement, which ends the <a href=\"https://wikipedia.org/wiki/United_States_occupation_of_the_Dominican_Republic_(1916%E2%80%9324)\" class=\"mw-redirect\" title=\"United States occupation of the Dominican Republic (1916-24)\">United States occupation of the Dominican Republic</a>.", "no_year_html": "In Washington D.C., U.S. Secretary of State <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Dominican Ambassador <PERSON> sign the Hughes-Peynado agreement, which ends the <a href=\"https://wikipedia.org/wiki/United_States_occupation_of_the_Dominican_Republic_(1916%E2%80%9324)\" class=\"mw-redirect\" title=\"United States occupation of the Dominican Republic (1916-24)\">United States occupation of the Dominican Republic</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States occupation of the Dominican Republic (1916-24)", "link": "https://wikipedia.org/wiki/United_States_occupation_of_the_Dominican_Republic_(1916%E2%80%9324)"}]}, {"year": "1934", "text": "The Night of the Long Knives, <PERSON>'s violent purge of his political rivals in Germany, takes place.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Night_of_the_Long_Knives\" title=\"Night of the Long Knives\">Night of the Long Knives</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Adolf Hitler\"><PERSON></a>'s violent purge of his political rivals in Germany, takes place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Night_of_the_Long_Knives\" title=\"Night of the Long Knives\">Night of the Long Knives</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a>'s violent purge of his political rivals in Germany, takes place.", "links": [{"title": "Night of the Long Knives", "link": "https://wikipedia.org/wiki/Night_of_the_<PERSON>_<PERSON>ves"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "Emperor <PERSON><PERSON> of Abyssinia appeals for aid to the League of Nations against Italy's invasion of his country.", "html": "1936 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>le_Selassie\" title=\"Haile Selassie\"><PERSON><PERSON> Se<PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ethiopian_Empire\" title=\"Ethiopian Empire\">Abyssinia</a> appeals for aid to the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> against Italy's invasion of his country.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Se<PERSON>ie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ethiopian_Empire\" title=\"Ethiopian Empire\">Abyssinia</a> appeals for aid to the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> against Italy's invasion of his country.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ethiopian Empire", "link": "https://wikipedia.org/wiki/Ethiopian_Empire"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1937", "text": "The world's first emergency telephone number, 999, is introduced in London.", "html": "1937 - The world's first <a href=\"https://wikipedia.org/wiki/Emergency_telephone_number\" title=\"Emergency telephone number\">emergency telephone number</a>, <a href=\"https://wikipedia.org/wiki/999_(emergency_telephone_number)\" title=\"999 (emergency telephone number)\">999</a>, is introduced in London.", "no_year_html": "The world's first <a href=\"https://wikipedia.org/wiki/Emergency_telephone_number\" title=\"Emergency telephone number\">emergency telephone number</a>, <a href=\"https://wikipedia.org/wiki/999_(emergency_telephone_number)\" title=\"999 (emergency telephone number)\">999</a>, is introduced in London.", "links": [{"title": "Emergency telephone number", "link": "https://wikipedia.org/wiki/Emergency_telephone_number"}, {"title": "999 (emergency telephone number)", "link": "https://wikipedia.org/wiki/999_(emergency_telephone_number)"}]}, {"year": "1944", "text": "World War II: The Battle of Cherbourg ends with the fall of the strategically valuable port to American forces.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Cherbourg\" title=\"Battle of Cherbourg\">Battle of Cherbourg</a> ends with the fall of the strategically valuable port to American forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Cherbourg\" title=\"Battle of Cherbourg\">Battle of Cherbourg</a> ends with the fall of the strategically valuable port to American forces.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Cherbourg", "link": "https://wikipedia.org/wiki/Battle_of_Cherbourg"}]}, {"year": "1953", "text": "The first Chevrolet Corvette rolls off the assembly line in Flint, Michigan.", "html": "1953 - The first <a href=\"https://wikipedia.org/wiki/Chevrolet_Corvette\" title=\"Chevrolet Corvette\">Chevrolet Corvette</a> rolls off the assembly line in <a href=\"https://wikipedia.org/wiki/Flint,_Michigan\" title=\"Flint, Michigan\">Flint, Michigan</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Chevrolet_Corvette\" title=\"Chevrolet Corvette\">Chevrolet Corvette</a> rolls off the assembly line in <a href=\"https://wikipedia.org/wiki/Flint,_Michigan\" title=\"Flint, Michigan\">Flint, Michigan</a>.", "links": [{"title": "Chevrolet Corvette", "link": "https://wikipedia.org/wiki/Chevrolet_Corvette"}, {"title": "Flint, Michigan", "link": "https://wikipedia.org/wiki/Flint,_Michigan"}]}, {"year": "1956", "text": "A TWA Super Constellation and a United Airlines DC-7 collide above the Grand Canyon in Arizona and crash, killing all 128 on board both airliners.", "html": "1956 - A <a href=\"https://wikipedia.org/wiki/Trans_World_Airlines\" title=\"Trans World Airlines\">TWA</a> <a href=\"https://wikipedia.org/wiki/Lockheed_Constellation\" title=\"Lockheed Constellation\">Super Constellation</a> and a <a href=\"https://wikipedia.org/wiki/United_Airlines\" title=\"United Airlines\">United Airlines</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-7\" title=\"Douglas DC-7\">DC-7</a> <a href=\"https://wikipedia.org/wiki/1956_Grand_Canyon_mid-air_collision\" title=\"1956 Grand Canyon mid-air collision\">collide</a> above the <a href=\"https://wikipedia.org/wiki/Grand_Canyon\" title=\"Grand Canyon\">Grand Canyon</a> in <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a> and crash, killing all 128 on board both airliners.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Trans_World_Airlines\" title=\"Trans World Airlines\">TWA</a> <a href=\"https://wikipedia.org/wiki/Lockheed_Constellation\" title=\"Lockheed Constellation\">Super Constellation</a> and a <a href=\"https://wikipedia.org/wiki/United_Airlines\" title=\"United Airlines\">United Airlines</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-7\" title=\"Douglas DC-7\">DC-7</a> <a href=\"https://wikipedia.org/wiki/1956_Grand_Canyon_mid-air_collision\" title=\"1956 Grand Canyon mid-air collision\">collide</a> above the <a href=\"https://wikipedia.org/wiki/Grand_Canyon\" title=\"Grand Canyon\">Grand Canyon</a> in <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a> and crash, killing all 128 on board both airliners.", "links": [{"title": "Trans World Airlines", "link": "https://wikipedia.org/wiki/Trans_World_Airlines"}, {"title": "Lockheed Constellation", "link": "https://wikipedia.org/wiki/Lockheed_Constellation"}, {"title": "United Airlines", "link": "https://wikipedia.org/wiki/United_Airlines"}, {"title": "Douglas DC-7", "link": "https://wikipedia.org/wiki/Douglas_DC-7"}, {"title": "1956 Grand Canyon mid-air collision", "link": "https://wikipedia.org/wiki/1956_Grand_Canyon_mid-air_collision"}, {"title": "Grand Canyon", "link": "https://wikipedia.org/wiki/Grand_Canyon"}, {"title": "Arizona", "link": "https://wikipedia.org/wiki/Arizona"}]}, {"year": "1959", "text": "A United States Air Force F-100 Super Sabre from Kadena Air Base, Okinawa, crashes into a nearby elementary school, killing 11 students plus six residents from the local neighborhood.", "html": "1959 - A United States Air Force <a href=\"https://wikipedia.org/wiki/F-100_Super_Sabre\" class=\"mw-redirect\" title=\"F-100 Super Sabre\">F-100 Super Sabre</a> from <a href=\"https://wikipedia.org/wiki/Kadena_Air_Base\" title=\"Kadena Air Base\">Kadena Air Base</a>, <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa</a>, <a href=\"https://wikipedia.org/wiki/1959_Okinawa_F-100_crash\" title=\"1959 Okinawa F-100 crash\">crashes</a> into a nearby elementary school, killing 11 students plus six residents from the local neighborhood.", "no_year_html": "A United States Air Force <a href=\"https://wikipedia.org/wiki/F-100_Super_Sabre\" class=\"mw-redirect\" title=\"F-100 Super Sabre\">F-100 Super Sabre</a> from <a href=\"https://wikipedia.org/wiki/Kadena_Air_Base\" title=\"Kadena Air Base\">Kadena Air Base</a>, <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa</a>, <a href=\"https://wikipedia.org/wiki/1959_Okinawa_F-100_crash\" title=\"1959 Okinawa F-100 crash\">crashes</a> into a nearby elementary school, killing 11 students plus six residents from the local neighborhood.", "links": [{"title": "F-100 Super Sabre", "link": "https://wikipedia.org/wiki/F-100_Super_Sabre"}, {"title": "Kadena Air Base", "link": "https://wikipedia.org/wiki/Kadena_Air_Base"}, {"title": "Okinawa Prefecture", "link": "https://wikipedia.org/wiki/Okinawa_Prefecture"}, {"title": "1959 Okinawa F-100 crash", "link": "https://wikipedia.org/wiki/1959_Okinawa_F-100_crash"}]}, {"year": "1960", "text": "Belgian Congo gains independence as Republic of the Congo (Léopoldville).", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Belgian_Congo\" title=\"Belgian Congo\">Belgian Congo</a> gains independence as <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)\" title=\"Republic of the Congo (Léopoldville)\">Republic of the Congo (Léopoldville)</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Belgian_Congo\" title=\"Belgian Congo\">Belgian Congo</a> gains independence as <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)\" title=\"Republic of the Congo (Léopoldville)\">Republic of the Congo (Léopoldville)</a>.", "links": [{"title": "Belgian Congo", "link": "https://wikipedia.org/wiki/Belgian_Congo"}, {"title": "Republic of the Congo (Léopoldville)", "link": "https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON> bombing: a car bomb, intended for Mafia boss <PERSON>, kills seven police officers and military personnel near Palermo.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Ciaculli_bombing\" class=\"mw-redirect\" title=\"Ciaculli bombing\">Ciaculli bombing</a>: a <a href=\"https://wikipedia.org/wiki/Car_bomb\" title=\"Car bomb\">car bomb</a>, intended for <a href=\"https://wikipedia.org/wiki/Sicilian_Mafia\" title=\"Sicilian Mafia\">Mafia</a> boss <a href=\"https://wikipedia.org/wiki/Salvatore_%22Ciaschiteddu%22_Greco\" title='<PERSON> \"<PERSON><PERSON>ted<PERSON>\" Greco'><PERSON></a>, kills seven <a href=\"https://wikipedia.org/wiki/Polizia_di_Stato\" title=\"Polizia di Stato\">police</a> officers and <a href=\"https://wikipedia.org/wiki/Italian_Army\" title=\"Italian Army\">military</a> personnel near <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ciaculli_bombing\" class=\"mw-redirect\" title=\"Ciaculli bombing\">Ciaculli bombing</a>: a <a href=\"https://wikipedia.org/wiki/Car_bomb\" title=\"Car bomb\">car bomb</a>, intended for <a href=\"https://wikipedia.org/wiki/Sicilian_Mafia\" title=\"Sicilian Mafia\">Mafia</a> boss <a href=\"https://wikipedia.org/wiki/Salvatore_%22Ciaschiteddu%22_Greco\" title='<PERSON> \"<PERSON><PERSON>\" Greco'><PERSON></a>, kills seven <a href=\"https://wikipedia.org/wiki/Polizia_di_Stato\" title=\"Polizia di Stato\">police</a> officers and <a href=\"https://wikipedia.org/wiki/Italian_Army\" title=\"Italian Army\">military</a> personnel near <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a>.", "links": [{"title": "Ciaculli bombing", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_bombing"}, {"title": "Car bomb", "link": "https://wikipedia.org/wiki/Car_bomb"}, {"title": "Sicilian Mafia", "link": "https://wikipedia.org/wiki/Sicilian_Mafia"}, {"title": "<PERSON> \"Ciaschiteddu\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Ciaschiteddu%22_Greco"}, {"title": "Polizia di Stato", "link": "https://wikipedia.org/wiki/Polizia_di_Stato"}, {"title": "Italian Army", "link": "https://wikipedia.org/wiki/Italian_Army"}, {"title": "Palermo", "link": "https://wikipedia.org/wiki/Palermo"}]}, {"year": "1966", "text": "The National Organization for Women, the United States' largest feminist organization, is founded.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/National_Organization_for_Women\" title=\"National Organization for Women\">National Organization for Women</a>, the United States' largest <a href=\"https://wikipedia.org/wiki/Feminism\" title=\"Feminism\">feminist</a> organization, is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Organization_for_Women\" title=\"National Organization for Women\">National Organization for Women</a>, the United States' largest <a href=\"https://wikipedia.org/wiki/Feminism\" title=\"Feminism\">feminist</a> organization, is founded.", "links": [{"title": "National Organization for Women", "link": "https://wikipedia.org/wiki/National_Organization_for_Women"}, {"title": "Feminism", "link": "https://wikipedia.org/wiki/Feminism"}]}, {"year": "1968", "text": "<PERSON> <PERSON> issues the Credo of the People of God.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\">Pope <PERSON> VI</a> issues the <i><a href=\"https://wikipedia.org/wiki/C<PERSON>_of_the_People_of_God\" class=\"mw-redirect\" title=\"Credo of the People of God\"><PERSON><PERSON> of the People of God</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\">Pope <PERSON> VI</a> issues the <i><a href=\"https://wikipedia.org/wiki/C<PERSON>_of_the_People_of_God\" class=\"mw-redirect\" title=\"Credo of the People of God\"><PERSON><PERSON> of the People of God</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> of the People of God", "link": "https://wikipedia.org/wiki/Credo_of_the_People_of_God"}]}, {"year": "1971", "text": "The crew of the Soviet Soyuz 11 spacecraft are killed when their air supply escapes through a faulty valve.", "html": "1971 - The crew of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <i><a href=\"https://wikipedia.org/wiki/Soyuz_11\" title=\"Soyuz 11\">Soyuz 11</a></i> spacecraft are killed when their air supply escapes through a faulty valve.", "no_year_html": "The crew of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <i><a href=\"https://wikipedia.org/wiki/Soyuz_11\" title=\"Soyuz 11\">Soyuz 11</a></i> spacecraft are killed when their air supply escapes through a faulty valve.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Soyuz 11", "link": "https://wikipedia.org/wiki/Soyuz_11"}]}, {"year": "1972", "text": "The first leap second is added to the UTC time system.", "html": "1972 - The first <a href=\"https://wikipedia.org/wiki/Leap_second\" title=\"Leap second\">leap second</a> is added to the <a href=\"https://wikipedia.org/wiki/UTC\" class=\"mw-redirect\" title=\"UTC\">UTC</a> time system.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Leap_second\" title=\"Leap second\">leap second</a> is added to the <a href=\"https://wikipedia.org/wiki/UTC\" class=\"mw-redirect\" title=\"UTC\">UTC</a> time system.", "links": [{"title": "<PERSON><PERSON> second", "link": "https://wikipedia.org/wiki/Leap_second"}, {"title": "UTC", "link": "https://wikipedia.org/wiki/UTC"}]}, {"year": "1973", "text": "Concorde 001 intercepts the path of a total solar eclipse and follows the moon's shadow, experiencing the longest total eclipse observation.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/1973_Eclipse_Flight_of_Concorde_001\" class=\"mw-redirect\" title=\"1973 Eclipse Flight of Concorde 001\">Concorde 001 intercepts the path of a total solar eclipse</a> and follows the moon's shadow, experiencing the longest total eclipse observation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1973_Eclipse_Flight_of_Concorde_001\" class=\"mw-redirect\" title=\"1973 Eclipse Flight of Concorde 001\">Concorde 001 intercepts the path of a total solar eclipse</a> and follows the moon's shadow, experiencing the longest total eclipse observation.", "links": [{"title": "1973 Eclipse Flight of Concorde 001", "link": "https://wikipedia.org/wiki/1973_Eclipse_Flight_of_Concorde_001"}]}, {"year": "1974", "text": "The Baltimore municipal strike of 1974 begins.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Baltimore_municipal_strike_of_1974\" class=\"mw-redirect\" title=\"Baltimore municipal strike of 1974\">Baltimore municipal strike of 1974</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Baltimore_municipal_strike_of_1974\" class=\"mw-redirect\" title=\"Baltimore municipal strike of 1974\">Baltimore municipal strike of 1974</a> begins.", "links": [{"title": "Baltimore municipal strike of 1974", "link": "https://wikipedia.org/wiki/Baltimore_municipal_strike_of_1974"}]}, {"year": "1977", "text": "The Southeast Asia Treaty Organization disbands.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Southeast_Asia_Treaty_Organization\" title=\"Southeast Asia Treaty Organization\">Southeast Asia Treaty Organization</a> disbands.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Southeast_Asia_Treaty_Organization\" title=\"Southeast Asia Treaty Organization\">Southeast Asia Treaty Organization</a> disbands.", "links": [{"title": "Southeast Asia Treaty Organization", "link": "https://wikipedia.org/wiki/Southeast_Asia_Treaty_Organization"}]}, {"year": "1985", "text": "Thirty-nine American hostages from the hijacked TWA Flight 847 are freed in Beirut after being held for 17 days.", "html": "1985 - Thirty-nine American hostages from the hijacked <a href=\"https://wikipedia.org/wiki/TWA_Flight_847\" title=\"TWA Flight 847\">TWA Flight 847</a> are freed in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> after being held for 17 days.", "no_year_html": "Thirty-nine American hostages from the hijacked <a href=\"https://wikipedia.org/wiki/TWA_Flight_847\" title=\"TWA Flight 847\">TWA Flight 847</a> are freed in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> after being held for 17 days.", "links": [{"title": "TWA Flight 847", "link": "https://wikipedia.org/wiki/TWA_Flight_847"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}]}, {"year": "1986", "text": "The U.S. Supreme Court rules in <PERSON><PERSON> v<PERSON> that states can outlaw homosexual acts between consenting adults.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v._<PERSON>wick\" title=\"<PERSON><PERSON> v. <PERSON>wick\">Bowers v. <PERSON></a></i> that states can outlaw <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexual</a> acts between consenting adults.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/Bow<PERSON>_v._<PERSON>wick\" title=\"<PERSON><PERSON> v. <PERSON>wick\">Bowers v. <PERSON></a></i> that states can outlaw <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexual</a> acts between consenting adults.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Bowers v. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_v._<PERSON>"}, {"title": "Homosexuality", "link": "https://wikipedia.org/wiki/Homosexuality"}]}, {"year": "1989", "text": "A coup d'état in Sudan deposes the democratically elected government of Prime Minister <PERSON><PERSON> and President <PERSON>.", "html": "1989 - A <a href=\"https://wikipedia.org/wiki/1989_Sudanese_coup_d%27%C3%A9tat\" title=\"1989 Sudanese coup d'état\">coup d'état in Sudan</a> deposes the democratically elected government of Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1989_Sudanese_coup_d%27%C3%A9tat\" title=\"1989 Sudanese coup d'état\">coup d'état in Sudan</a> deposes the democratically elected government of Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "1989 Sudanese coup d'état", "link": "https://wikipedia.org/wiki/1989_Sudanese_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "East and West Germany merge their economies.", "html": "1990 - East and West Germany <a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">merge their economies</a>.", "no_year_html": "East and West Germany <a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">merge their economies</a>.", "links": [{"title": "German reunification", "link": "https://wikipedia.org/wiki/German_reunification"}]}, {"year": "1993", "text": "Malta is officially subdivided into 68 local councils by the Local Councils Act.", "html": "1993 - Malta is officially subdivided into 68 <a href=\"https://wikipedia.org/wiki/Local_councils_of_Malta\" title=\"Local councils of Malta\">local councils</a> by the Local Councils Act.", "no_year_html": "Malta is officially subdivided into 68 <a href=\"https://wikipedia.org/wiki/Local_councils_of_Malta\" title=\"Local councils of Malta\">local councils</a> by the Local Councils Act.", "links": [{"title": "Local councils of Malta", "link": "https://wikipedia.org/wiki/Local_councils_of_Malta"}]}, {"year": "1994", "text": "During a test flight of an Airbus A330-300 at Toulouse-Blagnac Airport, the aircraft crashes killing all seven people on board.", "html": "1994 - During a test flight of an <a href=\"https://wikipedia.org/wiki/Airbus_A330\" title=\"Airbus A330\">Airbus A330-300</a> at <a href=\"https://wikipedia.org/wiki/Toulouse%E2%80%93Blagnac_Airport\" title=\"Toulouse-Blagnac Airport\">Toulouse-Blagnac Airport</a>, the aircraft <a href=\"https://wikipedia.org/wiki/Airbus_Industrie_Flight_129\" title=\"Airbus Industrie Flight 129\">crashes</a> killing all seven people on board.", "no_year_html": "During a test flight of an <a href=\"https://wikipedia.org/wiki/Airbus_A330\" title=\"Airbus A330\">Airbus A330-300</a> at <a href=\"https://wikipedia.org/wiki/Toulouse%E2%80%93Blagnac_Airport\" title=\"Toulouse-Blagnac Airport\">Toulouse-Blagnac Airport</a>, the aircraft <a href=\"https://wikipedia.org/wiki/Airbus_Industrie_Flight_129\" title=\"Airbus Industrie Flight 129\">crashes</a> killing all seven people on board.", "links": [{"title": "Airbus A330", "link": "https://wikipedia.org/wiki/Airbus_A330"}, {"title": "Toulouse-Blagnac Airport", "link": "https://wikipedia.org/wiki/Toulouse%E2%80%93Blagnac_Airport"}, {"title": "Airbus Industrie Flight 129", "link": "https://wikipedia.org/wiki/Airbus_Industrie_Flight_129"}]}, {"year": "2007", "text": "A Jeep Cherokee filled with propane canisters drives into the entrance of Glasgow Airport, Scotland in a failed terrorist attack. This was linked to the 2007 London car bombs that had taken place the day before.", "html": "2007 - A <a href=\"https://wikipedia.org/wiki/Jeep_Cherokee\" title=\"Jeep Cherokee\">Jeep Cherokee</a> filled with propane canisters drives into the entrance of <a href=\"https://wikipedia.org/wiki/Glasgow_Airport\" title=\"Glasgow Airport\">Glasgow Airport</a>, <a href=\"https://wikipedia.org/wiki/Scotland\" title=\"Scotland\">Scotland</a> in a <a href=\"https://wikipedia.org/wiki/2007_Glasgow_Airport_attack\" title=\"2007 Glasgow Airport attack\">failed terrorist attack</a>. This was linked to the <a href=\"https://wikipedia.org/wiki/2007_London_car_bombs\" title=\"2007 London car bombs\">2007 London car bombs</a> that had taken place the day before.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Jeep_Cherokee\" title=\"Jeep Cherokee\">Jeep Cherokee</a> filled with propane canisters drives into the entrance of <a href=\"https://wikipedia.org/wiki/Glasgow_Airport\" title=\"Glasgow Airport\">Glasgow Airport</a>, <a href=\"https://wikipedia.org/wiki/Scotland\" title=\"Scotland\">Scotland</a> in a <a href=\"https://wikipedia.org/wiki/2007_Glasgow_Airport_attack\" title=\"2007 Glasgow Airport attack\">failed terrorist attack</a>. This was linked to the <a href=\"https://wikipedia.org/wiki/2007_London_car_bombs\" title=\"2007 London car bombs\">2007 London car bombs</a> that had taken place the day before.", "links": [{"title": "Jeep Cherokee", "link": "https://wikipedia.org/wiki/Jeep_Cherokee"}, {"title": "Glasgow Airport", "link": "https://wikipedia.org/wiki/Glasgow_Airport"}, {"title": "Scotland", "link": "https://wikipedia.org/wiki/Scotland"}, {"title": "2007 Glasgow Airport attack", "link": "https://wikipedia.org/wiki/2007_Glasgow_Airport_attack"}, {"title": "2007 London car bombs", "link": "https://wikipedia.org/wiki/2007_London_car_bombs"}]}, {"year": "2009", "text": "Yemenia Flight 626, an Airbus A310-300, crashes into the Indian Ocean near Comoros, killing 152 of the 153 people on board. A 14-year-old girl named <PERSON><PERSON> survives the crash.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Yemenia_Flight_626\" title=\"Yemenia Flight 626\">Yemenia Flight 626</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A310\" title=\"Airbus A310\">Airbus A310-300</a>, crashes into the <a href=\"https://wikipedia.org/wiki/Indian_Ocean\" title=\"Indian Ocean\">Indian Ocean</a> near <a href=\"https://wikipedia.org/wiki/Comoros\" title=\"Comoros\">Comoros</a>, killing 152 of the 153 people on board. A 14-year-old girl named <a href=\"https://wikipedia.org/wiki/Bahia_Bakari\" title=\"Bahia Bakari\">Bahia Bakari</a> survives the crash.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yemenia_Flight_626\" title=\"Yemenia Flight 626\">Yemenia Flight 626</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A310\" title=\"Airbus A310\">Airbus A310-300</a>, crashes into the <a href=\"https://wikipedia.org/wiki/Indian_Ocean\" title=\"Indian Ocean\">Indian Ocean</a> near <a href=\"https://wikipedia.org/wiki/Comoros\" title=\"Comoros\">Comoros</a>, killing 152 of the 153 people on board. A 14-year-old girl named <a href=\"https://wikipedia.org/wiki/Bahia_Bakari\" title=\"Bahia Bakari\">Bahia Bakari</a> survives the crash.", "links": [{"title": "Yemenia Flight 626", "link": "https://wikipedia.org/wiki/Yemenia_Flight_626"}, {"title": "Airbus A310", "link": "https://wikipedia.org/wiki/Airbus_A310"}, {"title": "Indian Ocean", "link": "https://wikipedia.org/wiki/Indian_Ocean"}, {"title": "Comoros", "link": "https://wikipedia.org/wiki/Comoros"}, {"title": "Bahia Bakari", "link": "https://wikipedia.org/wiki/Bahia_Bakari"}]}, {"year": "2013", "text": "Nineteen firefighters die controlling a wildfire near Yarnell, Arizona.", "html": "2013 - Nineteen firefighters die controlling a <a href=\"https://wikipedia.org/wiki/Yarnell_Hill_Fire\" title=\"Yarnell Hill Fire\">wildfire</a> near <a href=\"https://wikipedia.org/wiki/Yarnell,_Arizona\" title=\"Yarnell, Arizona\">Yarnell, Arizona</a>.", "no_year_html": "Nineteen firefighters die controlling a <a href=\"https://wikipedia.org/wiki/Yarnell_Hill_Fire\" title=\"Yarnell Hill Fire\">wildfire</a> near <a href=\"https://wikipedia.org/wiki/Yarnell,_Arizona\" title=\"Yarnell, Arizona\">Yarnell, Arizona</a>.", "links": [{"title": "Yarnell Hill Fire", "link": "https://wikipedia.org/wiki/Yarnell_Hill_Fire"}, {"title": "Yarnell, Arizona", "link": "https://wikipedia.org/wiki/Yarnell,_Arizona"}]}, {"year": "2013", "text": "Protests begin around Egypt against President <PERSON> and the ruling Freedom and Justice Party, leading to their overthrow during the 2013 Egyptian coup d'état.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/June_2013_Egyptian_protests\" title=\"June 2013 Egyptian protests\">Protests</a> begin around Egypt against President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the ruling <a href=\"https://wikipedia.org/wiki/Freedom_and_Justice_Party_(Egypt)\" title=\"Freedom and Justice Party (Egypt)\">Freedom and Justice Party</a>, leading to their overthrow during the <a href=\"https://wikipedia.org/wiki/2013_Egyptian_coup_d%27%C3%A9tat\" title=\"2013 Egyptian coup d'état\">2013 Egyptian coup d'état</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_2013_Egyptian_protests\" title=\"June 2013 Egyptian protests\">Protests</a> begin around Egypt against President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the ruling <a href=\"https://wikipedia.org/wiki/Freedom_and_Justice_Party_(Egypt)\" title=\"Freedom and Justice Party (Egypt)\">Freedom and Justice Party</a>, leading to their overthrow during the <a href=\"https://wikipedia.org/wiki/2013_Egyptian_coup_d%27%C3%A9tat\" title=\"2013 Egyptian coup d'état\">2013 Egyptian coup d'état</a>.", "links": [{"title": "June 2013 Egyptian protests", "link": "https://wikipedia.org/wiki/June_2013_Egyptian_protests"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Freedom and Justice Party (Egypt)", "link": "https://wikipedia.org/wiki/Freedom_and_Justice_Party_(Egypt)"}, {"title": "2013 Egyptian coup d'état", "link": "https://wikipedia.org/wiki/2013_Egyptian_coup_d%27%C3%A9tat"}]}, {"year": "2015", "text": "A Hercules C-130 military aircraft with 113 people on board crashes in a residential area in Medan, Indonesia, resulting in at least 116 deaths.", "html": "2015 - A <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">Hercules C-130</a> military aircraft with 113 people on board <a href=\"https://wikipedia.org/wiki/2015_Sumatra_Indonesian_Air_Force_C-130_crash\" title=\"2015 Sumatra Indonesian Air Force C-130 crash\">crashes</a> in a residential area in <a href=\"https://wikipedia.org/wiki/Medan\" title=\"Medan\">Medan, Indonesia</a>, resulting in at least 116 deaths.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">Hercules C-130</a> military aircraft with 113 people on board <a href=\"https://wikipedia.org/wiki/2015_Sumatra_Indonesian_Air_Force_C-130_crash\" title=\"2015 Sumatra Indonesian Air Force C-130 crash\">crashes</a> in a residential area in <a href=\"https://wikipedia.org/wiki/Medan\" title=\"Medan\">Medan, Indonesia</a>, resulting in at least 116 deaths.", "links": [{"title": "Lockheed C-130 Hercules", "link": "https://wikipedia.org/wiki/Lockheed_C-130_Hercules"}, {"title": "2015 Sumatra Indonesian Air Force C-130 crash", "link": "https://wikipedia.org/wiki/2015_Sumatra_Indonesian_Air_Force_C-130_crash"}, {"title": "Medan", "link": "https://wikipedia.org/wiki/Medan"}]}, {"year": "2019", "text": "<PERSON> becomes the first sitting US President to visit the Democratic People's Republic of Korea (North Korea).", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first sitting US President to <a href=\"https://wikipedia.org/wiki/2019_Koreas%E2%80%93United_States_DMZ_Summit\" title=\"2019 Koreas-United States DMZ Summit\">visit</a> the <a href=\"https://wikipedia.org/wiki/Democratic_People%E2%80%99s_Republic_of_Korea\" class=\"mw-redirect\" title=\"Democratic People’s Republic of Korea\">Democratic People's Republic of Korea</a> (North Korea).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first sitting US President to <a href=\"https://wikipedia.org/wiki/2019_Koreas%E2%80%93United_States_DMZ_Summit\" title=\"2019 Koreas-United States DMZ Summit\">visit</a> the <a href=\"https://wikipedia.org/wiki/Democratic_People%E2%80%99s_Republic_of_Korea\" class=\"mw-redirect\" title=\"Democratic People’s Republic of Korea\">Democratic People's Republic of Korea</a> (North Korea).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "2019 Koreas-United States DMZ Summit", "link": "https://wikipedia.org/wiki/2019_Koreas%E2%80%93United_States_DMZ_Summit"}, {"title": "Democratic People’s Republic of Korea", "link": "https://wikipedia.org/wiki/Democratic_People%E2%80%99s_Republic_of_Korea"}]}, {"year": "2021", "text": "The Tiger Fire ignites near Black Canyon City, Arizona, and goes on to burn 16,278 acres (6,587 ha) of land before being fully contained on July 30.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/Tiger_Fire\" title=\"Tiger Fire\">Tiger Fire</a> ignites near <a href=\"https://wikipedia.org/wiki/Black_Canyon_City,_Arizona\" title=\"Black Canyon City, Arizona\">Black Canyon City, Arizona</a>, and goes on to burn 16,278 acres (6,587 ha) of land before being fully contained on July 30.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tiger_Fire\" title=\"Tiger Fire\">Tiger Fire</a> ignites near <a href=\"https://wikipedia.org/wiki/Black_Canyon_City,_Arizona\" title=\"Black Canyon City, Arizona\">Black Canyon City, Arizona</a>, and goes on to burn 16,278 acres (6,587 ha) of land before being fully contained on July 30.", "links": [{"title": "Tiger Fire", "link": "https://wikipedia.org/wiki/Tiger_Fire"}, {"title": "Black Canyon City, Arizona", "link": "https://wikipedia.org/wiki/Black_Canyon_City,_Arizona"}]}, {"year": "2023", "text": "A Tajik citizen with ISIS connections, wanted in Tajikistan for murder and kidnapping, kills two people at Chișinău International Airport in Moldova, after being denied entry to the country.", "html": "2023 - A <a href=\"https://wikipedia.org/wiki/Tajikistan\" title=\"Tajikistan\">Tajik citizen</a> with <a href=\"https://wikipedia.org/wiki/Islamic_State\" title=\"Islamic State\">ISIS</a> connections, wanted in Tajikistan for murder and kidnapping, <a href=\"https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport_shooting\" title=\"Chișinău International Airport shooting\">kills two people</a> at <a href=\"https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport\" title=\"Chișinău International Airport\">Chișinău International Airport</a> in <a href=\"https://wikipedia.org/wiki/Moldova\" title=\"Moldova\">Moldova</a>, after being denied entry to the country.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tajikistan\" title=\"Tajikistan\">Tajik citizen</a> with <a href=\"https://wikipedia.org/wiki/Islamic_State\" title=\"Islamic State\">ISIS</a> connections, wanted in Tajikistan for murder and kidnapping, <a href=\"https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport_shooting\" title=\"Chișinău International Airport shooting\">kills two people</a> at <a href=\"https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport\" title=\"Chișinău International Airport\">Chișinău International Airport</a> in <a href=\"https://wikipedia.org/wiki/Moldova\" title=\"Moldova\">Moldova</a>, after being denied entry to the country.", "links": [{"title": "Tajikistan", "link": "https://wikipedia.org/wiki/Tajikistan"}, {"title": "Islamic State", "link": "https://wikipedia.org/wiki/Islamic_State"}, {"title": "Chișinău International Airport shooting", "link": "https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport_shooting"}, {"title": "Chișinău International Airport", "link": "https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport"}, {"title": "Moldova", "link": "https://wikipedia.org/wiki/Moldova"}]}], "Births": [{"year": "1286", "text": "<PERSON>, 7th Earl of Surrey, English magnate (d. 1347)", "html": "1286 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Surrey\" title=\"<PERSON>, 7th Earl of Surrey\"><PERSON>, 7th Earl of Surrey</a>, English magnate (d. 1347)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Surrey\" title=\"<PERSON>, 7th Earl of Surrey\"><PERSON>, 7th Earl of Surrey</a>, English magnate (d. 1347)", "links": [{"title": "<PERSON>, 7th Earl of Surrey", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Surrey"}]}, {"year": "1468", "text": "<PERSON>, Elector of Saxony (d. 1532)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1532)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony"}]}, {"year": "1470", "text": "<PERSON> of France (d. 1498)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VIII of France\"><PERSON> of France</a> (d. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VIII of France\"><PERSON> of France</a> (d. 1498)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}]}, {"year": "1478", "text": "<PERSON>, Prince of Asturias, Son of <PERSON> of Aragon and <PERSON> of Castile (d. 1497)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias\" title=\"<PERSON>, Prince of Asturias\"><PERSON>, Prince of Asturias</a>, Son of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> (d. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias\" title=\"<PERSON>, Prince of Asturias\"><PERSON>, Prince of Asturias</a>, Son of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> (d. 1497)", "links": [{"title": "<PERSON>, Prince of Asturias", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}]}, {"year": "1503", "text": "<PERSON>, Elector of Saxony (d. 1554)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1554)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1533", "text": "<PERSON>, Spanish missionary (d. 1578)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_de_Rada\" title=\"<PERSON>\"><PERSON></a>, Spanish missionary (d. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_de_Rada\" title=\"<PERSON>\"><PERSON></a>, Spanish missionary (d. 1578)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_de_Rada"}]}, {"year": "1588", "text": "<PERSON>, Italian organist, composer, and educator (d. 1649)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist, composer, and educator (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist, composer, and educator (d. 1649)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1641", "text": "<PERSON><PERSON><PERSON>, 3rd Duke of Schomberg, German-English general (d. 1719)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_3rd_Duke_of_Schomberg\" title=\"<PERSON><PERSON><PERSON>, 3rd Duke of Schomberg\"><PERSON><PERSON><PERSON>, 3rd Duke of Schomberg</a>, German-English general (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_3rd_Duke_of_Schomberg\" title=\"<PERSON><PERSON><PERSON>, 3rd Duke of Schomberg\"><PERSON><PERSON><PERSON>, 3rd Duke of Schomberg</a>, German-English general (d. 1719)", "links": [{"title": "<PERSON><PERSON><PERSON>, 3rd Duke of Schomberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_3rd_Duke_<PERSON>_Schomberg"}]}, {"year": "1685", "text": "<PERSON>, English poet and playwright (d. 1732)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (d. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (d. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON><PERSON><PERSON>, ruler of Tunisia (d. 1756)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> l-Hasan <PERSON>\"><PERSON><PERSON><PERSON></a>, ruler of Tunisia (d. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> l-Hasan <PERSON>\"><PERSON><PERSON><PERSON></a>, ruler of Tunisia (d. 1756)", "links": [{"title": "<PERSON><PERSON><PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_I"}]}, {"year": "1722", "text": "<PERSON><PERSON><PERSON>, Czech composer, violinist and <PERSON><PERSON>lmeister (d. 1795)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Anton%C3%ADn_Benda\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech composer, violinist and <PERSON><PERSON><PERSON>eister (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Anton%C3%ADn_Benda\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech composer, violinist and <PERSON><PERSON><PERSON>eister (d. 1795)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Anton%C3%ADn_Benda"}]}, {"year": "1755", "text": "<PERSON>, French soldier and politician (d. 1829)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (d. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, French painter and academic (d. 1863)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and academic (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and academic (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, French physicist and psychologist (d. 1841)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Sa<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, French physicist and psychologist (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Sa<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, French physicist and psychologist (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Savart"}]}, {"year": "1803", "text": "<PERSON>, English poet, playwright, and physician (d. 1849)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and physician (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and physician (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, German author, poet, and playwright (d. 1887)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, English botanist and explorer (d. 1911)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and explorer (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and explorer (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, English orientalist and diplomat (d. 1929)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English orientalist and diplomat (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English orientalist and diplomat (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English architect and archaeologist (d. 1945)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and archaeologist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and archaeologist (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, French author and critic (d. 1966)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>-<PERSON>, English motor car designer, engineer and founder of Frazer Nash (d. 1965)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motor car designer, engineer and founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Nash\"><PERSON><PERSON><PERSON></a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motor car designer, engineer and founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Nash\"><PERSON><PERSON><PERSON></a> (d. 1965)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Maltese physician and politician, 5th Prime Minister of Malta (d. 1962)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese physician and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese physician and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1891", "text": "<PERSON>, American wrestler and sergeant (d. 1953)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Man_Mountain_Dean\" title=\"Man Mountain Dean\"><PERSON></a>, American wrestler and sergeant (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man_Mountain_Dean\" title=\"Man Mountain Dean\"><PERSON></a>, American wrestler and sergeant (d. 1953)", "links": [{"title": "Man Mountain Dean", "link": "https://wikipedia.org/wiki/Man_Mountain_Dean"}]}, {"year": "1891", "text": "<PERSON>, American wrestler and manager (d. 1966)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and manager (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and manager (d. 1966)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1891", "text": "<PERSON>, English painter (d. 1959)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Algerian-French actor and director (d. 1963)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French actor and director (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French actor and director (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, American politician and librarian (d. 1956)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and librarian (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and librarian (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, German soldier and politician (d. 1973)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, German-American sculptor and educator (d. 1983)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sculptor and educator (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sculptor and educator (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, American actress (d. 1990)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Madge Bellamy\"><PERSON><PERSON></a>, American actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Madge Bellamy\"><PERSON><PERSON></a>, American actress (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>my"}]}, {"year": "1905", "text": "<PERSON>, American tennis player (d. 1999)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American actor and director (d. 1967)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Ukrainian general and politician (d. 1950)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian general and politician (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian general and politician (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English author (d. 2003)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Italian film producer (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian film producer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian film producer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Dutch writer (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch writer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch writer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, 43rd President of the Dominican Republic (d. 2001)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, 43rd President of the Dominican Republic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, 43rd President of the Dominican Republic (d. 2001)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Polish novelist, essayist, and poet, Nobel Prize laureate (d. 2004)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_Mi%C5%82osz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish novelist, essayist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_Mi%C5%82osz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish novelist, essayist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Czes%C5%82aw_Mi%C5%82osz"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Indian poet (d. 1998)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>jun\" title=\"Nagar<PERSON>\"><PERSON><PERSON></a>, Indian poet (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>jun\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nagarjun"}]}, {"year": "1912", "text": "<PERSON>, German engineer (d. 2003)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Ludwig_B%C3%B6lkow\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludwig_B%C3%B6lkow\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_B%C3%B6lkow"}]}, {"year": "1912", "text": "<PERSON>, American businessman and philanthropist (d. 1971)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_executive)\" title=\"<PERSON> (American football executive)\"><PERSON></a>, American businessman and philanthropist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_executive)\" title=\"<PERSON> (American football executive)\"><PERSON></a>, American businessman and philanthropist (d. 1971)", "links": [{"title": "<PERSON> (American football executive)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_executive)"}]}, {"year": "1912", "text": "<PERSON>, Mexican architect (d. 2009)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Luisa_Dehesa_G%C3%B3mez_Far%C3%ADas\" title=\"María Luisa Dehes<PERSON>\"><PERSON></a>, Mexican architect (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Luisa_Dehesa_G%C3%B3mez_Far%C3%ADas\" title=\"María Luis<PERSON>\"><PERSON></a>, Mexican architect (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%AD<PERSON>_Luis<PERSON>_Dehesa_G%C3%B3mez_Far%C3%ADas"}]}, {"year": "1913", "text": "<PERSON>, Colombian lawyer and politician, 24th President of Colombia (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Alfonso_L%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_L%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfonso_L%C3%B3<PERSON><PERSON>_<PERSON>"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "1913", "text": "<PERSON>, American sportscaster (d. 1967)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Portuguese general and politician, 15th President of Portugal (d. 2001)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese general and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese general and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1914", "text": "<PERSON>, American sculptor and painter (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress (d. 1975)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress, singer, and activist (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and activist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and activist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American costume designer (d. 2016)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American costume designer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American costume designer (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American inventor of the modern hot air balloon (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor of the modern <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor of the modern <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "1920", "text": "<PERSON>, American poet and educator (d. 2011)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American-Filipino accountant (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Washington_SyCip\" title=\"Washington SyCip\">Washington SyCip</a>, American-Filipino accountant (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Washington_SyCip\" title=\"Washington SyCip\">Washington SyCip</a>, American-Filipino accountant (d. 2017)", "links": [{"title": "Washington SyCip", "link": "https://wikipedia.org/wiki/Washington_SyCip"}]}, {"year": "1924", "text": "<PERSON>, Swiss sprinter (d. 1990)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Trepp\" title=\"Max Trepp\"><PERSON></a>, Swiss sprinter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T<PERSON>pp\" title=\"Max Trepp\"><PERSON></a>, Swiss sprinter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Trepp"}]}, {"year": "1925", "text": "<PERSON>, American basketball player and coach (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Iranian politician (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian politician (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ini"}]}, {"year": "1926", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 2023)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1927", "text": "<PERSON>, American tennis player (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American screenwriter and playwright (d. 1998)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and playwright (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and playwright (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Italian director, screenwriter, producer, collector and actor (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director, screenwriter, producer, collector and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director, screenwriter, producer, collector and actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American basketball player (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2021)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1928", "text": "<PERSON>, Islamic philosopher, theologian, mathematician and mystic (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Islamic philosopher, theologian, mathematician and mystic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Islamic philosopher, theologian, mathematician and mystic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American poet, essayist, anthropologist, and translator", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, anthropologist, and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, anthropologist, and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Chinese judge (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-liang\" title=\"<PERSON> Ti-liang\"><PERSON>liang</a>, Chinese judge (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i-liang\" title=\"<PERSON> Ti-liang\"><PERSON>liang</a>, Chinese judge (d. 2023)", "links": [{"title": "<PERSON>iang", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i-liang"}]}, {"year": "1930", "text": "<PERSON>, American politician (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Saudi Arabian politician (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Syrian bishop (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Ignatius_<PERSON>_<PERSON>_<PERSON>\" title=\"Ignatius <PERSON>\">Igna<PERSON></a>, Syrian bishop (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignatius_<PERSON>_<PERSON>_<PERSON>\" title=\"Ignatius <PERSON>\">Igna<PERSON></a>, Syrian bishop (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player and manager (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American pianist and composer (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" class=\"mw-redirect\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, American pianist and composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" class=\"mw-redirect\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, American pianist and composer (d. 2007)", "links": [{"title": "<PERSON> (jazz musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)"}]}, {"year": "1931", "text": "<PERSON>, American judge (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American football player (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Croatian football coach and manager (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian football coach and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian football coach and manager (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "1933", "text": "<PERSON>, American educator and marine biologist (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and marine biologist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and marine biologist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, English cricketer and rugby player", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and rugby player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Australian Major <PERSON>'s cockatoo, oldest recorded parrot (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cockatoo)\" title=\"<PERSON><PERSON> (cockatoo)\"><PERSON><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_cockatoo\" class=\"mw-redirect\" title=\"<PERSON>'s cockatoo\"><PERSON>'s cockatoo</a>, oldest recorded parrot (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cockatoo)\" title=\"<PERSON><PERSON> (cockatoo)\"><PERSON><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_cockatoo\" class=\"mw-redirect\" title=\"<PERSON>'s cockatoo\"><PERSON>'s cockatoo</a>, oldest recorded parrot (d. 2016)", "links": [{"title": "<PERSON><PERSON> (cockatoo)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(cockatoo)"}, {"title": "<PERSON>'s cockatoo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_cockatoo"}]}, {"year": "1934", "text": "<PERSON>, American magician and author (d. 1997)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American magician and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American magician and author (d. 1997)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1935", "text": "<PERSON>, American pilot and mountaineer (d. 1966)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and mountaineer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and mountaineer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Algerian-French author and translator (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Assia_D<PERSON>bar\" title=\"<PERSON><PERSON> Djebar\"><PERSON><PERSON></a>, Algerian-French author and translator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Assia_D<PERSON>bar\" title=\"<PERSON>sia Djebar\"><PERSON><PERSON></a>, Algerian-French author and translator (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assia_D<PERSON>bar"}]}, {"year": "1936", "text": "<PERSON>, American actress and singer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor and screenwriter (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2002)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American sprinter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American screenwriter (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English pianist, composer, and producer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English author and screenwriter (d. 2016)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Mexican poet and author (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, South African cricketer and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American lieutenant and oceanographer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and oceanographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and oceanographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1943", "text": "<PERSON>, American pop/soul singer (d. 1976)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ballard\" title=\"<PERSON>\"><PERSON></a>, American pop/soul singer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ballard\" title=\"<PERSON> Ballard\"><PERSON></a>, American pop/soul singer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Ballard"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Indian director and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American wrestler (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American parapsychologist and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American parapsychologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American parapsychologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English-Australian singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American baseball player and sportscaster", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, German footballer, coach, and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Welsh singer-songwriter, guitarist, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, Welsh singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, Welsh singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(guitarist)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Vietnamese cosmonaut (d. 1981)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/B%C3%B9i_Thanh_Li%C3%AAm\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese cosmonaut (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%B9i_<PERSON>h_Li%C3%AAm\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese cosmonaut (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%B9i_Thanh_Li%C3%AAm"}]}, {"year": "1951", "text": "<PERSON>, American bass player and composer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek mathematician and academic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Athanassios_S._Fokas\" class=\"mw-redirect\" title=\"Athanassios S. Fokas\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Greek mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Athanassios_S._Fokas\" class=\"mw-redirect\" title=\"Athanassios S. Fokas\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Greek mathematician and academic", "links": [{"title": "Athanassios S. F<PERSON>s", "link": "https://wikipedia.org/wiki/Athanassios_S._F<PERSON>s"}]}, {"year": "1952", "text": "<PERSON>, American actor and singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American-English guitarist and film score composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English guitarist and film score composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English guitarist and film score composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English organist, composer, and conductor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" class=\"mw-redirect\" title=\"<PERSON> (conductor)\"><PERSON></a>, English organist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" class=\"mw-redirect\" title=\"<PERSON> (conductor)\"><PERSON></a>, English organist, composer, and conductor", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_(conductor)"}]}, {"year": "1954", "text": "<PERSON>, Dominican educator and politician, 5th Prime Minister of Dominica (d. 2004)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dominican_politician)\" title=\"<PERSON> (Dominican politician)\"><PERSON></a>, Dominican educator and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Dominica\" title=\"Prime Minister of Dominica\">Prime Minister of Dominica</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dominican_politician)\" title=\"<PERSON> (Dominican politician)\"><PERSON></a>, Dominican educator and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Dominica\" title=\"Prime Minister of Dominica\">Prime Minister of Dominica</a> (d. 2004)", "links": [{"title": "<PERSON> (Dominican politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dominican_politician)"}, {"title": "Prime Minister of Dominica", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Dominica"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Armenian politician, 3rd President of Armenia", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Armenia\" title=\"President of Armenia\">President of Armenia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Armenia\" title=\"President of Armenia\">President of Armenia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "President of Armenia", "link": "https://wikipedia.org/wiki/President_of_Armenia"}]}, {"year": "1954", "text": "<PERSON>, Australian academic and politician, 14th Deputy Prime Minister of Australia", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian academic and politician, 14th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian academic and politician, 14th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia"}]}, {"year": "1955", "text": "<PERSON>, Canadian singer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Latvian judge, jurist, 10th President of Latvia", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvian</a> judge, jurist, 10th <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvian</a> judge, jurist, 10th <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "President of Latvia", "link": "https://wikipedia.org/wiki/President_of_Latvia"}]}, {"year": "1956", "text": "<PERSON><PERSON>, German hurdler and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(athlete)\" title=\"<PERSON><PERSON> (athlete)\"><PERSON><PERSON></a>, German hurdler and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(athlete)\" title=\"<PERSON><PERSON> (athlete)\"><PERSON><PERSON></a>, German hurdler and coach", "links": [{"title": "<PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(athlete)"}]}, {"year": "1956", "text": "<PERSON>, English historian, academic, and politician, Minister of State for Europe", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, academic, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, academic, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of State for Europe", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Europe"}]}, {"year": "1956", "text": "<PERSON>, American actor, singer, and comedian", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American baseball player and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Bud_Black\" title=\"Bud Black\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bud_Black\" title=\"Bud Black\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bud_Black"}]}, {"year": "1957", "text": "<PERSON>, American race car driver", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sterling_Marlin"}]}, {"year": "1958", "text": "<PERSON>, British television presenter, journalist and voice coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British television presenter, journalist and voice coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British television presenter, journalist and voice coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish conductor and composer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Esa-Pekka_Salonen\" title=\"Esa-Pekka Salonen\"><PERSON>sa-<PERSON><PERSON><PERSON></a>, Finnish conductor and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esa-Pekka_<PERSON>en\" title=\"Esa-Pekka Salonen\"><PERSON>sa-Pek<PERSON></a>, Finnish conductor and composer", "links": [{"title": "Esa-Pekka <PERSON>", "link": "https://wikipedia.org/wiki/Esa-Pekka_Salonen"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Onofrio\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Onofrio\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vincent_D%27Onofrio"}]}, {"year": "1959", "text": "<PERSON>, American political scientist, author, and academic", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Greek footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Baroness <PERSON>, Indian-English businesswoman and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, Indian-English businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, Indian-English businesswoman and politician", "links": [{"title": "<PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Scottish educator and politician, 3rd First Minister of Scotland", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Minister of Scotland", "link": "https://wikipedia.org/wiki/First_Minister_of_Scotland"}]}, {"year": "1960", "text": "<PERSON>, Australian musician, actor, songwriter and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian musician, actor, songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian musician, actor, songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American computer scientist and programmer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and programmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and programmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English musician, composer and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician, composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician, composer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Dominican baseball player  (d. 2020)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez"}]}, {"year": "1962", "text": "<PERSON><PERSON>, English singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Ukrainian sprinter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>na"}]}, {"year": "1963", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish guitarist and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Yng<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yng<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ng<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish guitarist and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yng<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Countess of Frederiksborg", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Frederiksborg\" title=\"<PERSON>, Countess of Frederiksborg\"><PERSON>, Countess of Frederiksborg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Frederiksborg\" title=\"<PERSON>, Countess of Frederiksborg\"><PERSON>, Countess of Frederiksborg</a>", "links": [{"title": "<PERSON>, Countess of Frederiksborg", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_<PERSON>_Frederiksborg"}]}, {"year": "1964", "text": "<PERSON>, American director and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1965", "text": "<PERSON>, Canadian-American ice hockey player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, South Korean actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyun\" title=\"<PERSON>yun\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yun\" title=\"<PERSON>hyun\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-hyun"}]}, {"year": "1965", "text": "<PERSON>, Russian figure skater and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English footballer and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American boxer and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Swedish javelin thrower", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Pat<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pat<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish javelin thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patrik_Bod%C3%A9n"}]}, {"year": "1967", "text": "<PERSON>, English footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American-Canadian astrophysicist and academic", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Victoria_Kaspi\" title=\"Victoria Kaspi\"><PERSON></a>, American-Canadian astrophysicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Kaspi\" title=\"Victoria Kaspi\"><PERSON></a>, American-Canadian astrophysicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Kaspi"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Sri Lankan cricketer and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan cricketer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan cricketer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, German sprinter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Uta_Rohl%C3%A4nder\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uta_Rohl%C3%A4nder\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uta_Rohl%C3%A4nder"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian director and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Italian footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American baseball player and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Potter\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Belgian swimmer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, South Korean baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Chan <PERSON> Park\"><PERSON></a>, South Korean baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Chan <PERSON> Park\"><PERSON></a>, South Korean baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Austrian politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, South African runner", "html": "1974 - <a href=\"https://wikipedia.org/wiki/He<PERSON>ki%C3%A9l_Sepeng\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South African runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON>ki%C3%A9l_Sepeng\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South African runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hezeki%C3%A9l_Sepeng"}]}, {"year": "1975", "text": "<PERSON>, New Zealand footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_Bannatyne"}]}, {"year": "1975", "text": "<PERSON><PERSON>, German race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Kenyan runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Italian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, French cyclist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>yl<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French cyclist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Rade_P<PERSON>\" title=\"Rade Prica\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rade_Prica\" title=\"Rade Prica\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "Rade P<PERSON>", "link": "https://wikipedia.org/wiki/Rade_Prica"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Nigerian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Olofinjana\"><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Olofinjana\"><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Dutch cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Does<PERSON>\" title=\"Ryan ten Doeschate\"><PERSON></a>, Dutch cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Does<PERSON>\" title=\"Ryan ten Doeschate\"><PERSON></a>, Dutch cricketer", "links": [{"title": "Ryan <PERSON> Doeschate", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Does<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Turkish race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Can_Artam\" title=\"Can Artam\"><PERSON></a>, Turkish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Can_Artam\" title=\"Can Artam\"><PERSON></a>, Turkish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Artam"}]}, {"year": "1981", "text": "<PERSON>, Canadian football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Czech javelin thrower", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Barbora_%C5%A0pot%C3%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbora_%C5%A0pot%C3%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barbora_%C5%A0pot%C3%A1kov%C3%A1"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Mexican footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German cyclist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, UK-based Canadian comedian and presenter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, UK-based Canadian comedian and presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, UK-based Canadian comedian and presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English singer and TV personality", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer and TV personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer and TV personality", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Fantasia_Barrino\" class=\"mw-redirect\" title=\"<PERSON>tas<PERSON> Barrino\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fantasia_Barrino\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Barr<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "Fantasia <PERSON>ino", "link": "https://wikipedia.org/wiki/Fantasia_Barrino"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Da<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Da<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Crown Prince of Johor, Malaysia", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>nk<PERSON>_<PERSON>_<PERSON>\" title=\"Tunku Ismail Idr<PERSON>\"><PERSON><PERSON><PERSON></a>, Crown Prince of Johor, Malaysia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>nk<PERSON>_<PERSON>_<PERSON>\" title=\"Tunku Ismail Idris\"><PERSON><PERSON><PERSON></a>, Crown Prince of Johor, Malaysia", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>is"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabiana_<PERSON>jos"}]}, {"year": "1986", "text": "<PERSON>, American wrestler, model, and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, model, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fox\"><PERSON></a>, American wrestler, model, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Colombian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fredy_Guar%C3%ADn"}]}, {"year": "1986", "text": "<PERSON>, Italian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Italian-American businesswoman", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Allegra_Versace\" title=\"Allegra Versace\">All<PERSON>ra Versace</a>, Italian-American businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Allegra_Versace\" title=\"Allegra Versace\"><PERSON><PERSON>ra Versace</a>, Italian-American businesswoman", "links": [{"title": "Allegra V<PERSON>ace", "link": "https://wikipedia.org/wiki/Allegra_Versace"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1987", "text": "<PERSON>, New Zealand runner", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "Jack<PERSON><PERSON><PERSON>, American YouTuber", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jacksfilms\" title=\"Jacksfilms\">Jacksfilms</a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jacksfilms\" title=\"Jacksfilms\">Jacksfilms</a>, American YouTuber", "links": [{"title": "Jacksfilms", "link": "https://wikipedia.org/wiki/Jacksfilms"}]}, {"year": "1988", "text": "<PERSON>, American basketball coach", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Kenyan runner", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kip<PERSON>\"><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kiprop\"><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, German rugby player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Steffen_Liebig\" title=\"Steffen Liebig\"><PERSON><PERSON><PERSON> Lie<PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Steffen_Liebig\" title=\"Steffen Liebig\"><PERSON><PERSON><PERSON></a>, German rugby player", "links": [{"title": "Steffen Liebig", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lie<PERSON>g"}]}, {"year": "1989", "text": "<PERSON>, Australian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_footballer)"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "bb<PERSON><PERSON>, Canadian singer-songwriter", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Bbno$\" title=\"Bbno$\">bbno$</a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bbno$\" title=\"Bbno$\">bbno$</a>, Canadian singer-songwriter", "links": [{"title": "Bbno$", "link": "https://wikipedia.org/wiki/Bbno$"}]}, {"year": "1997", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1998)\" title=\"<PERSON> (footballer, born 1998)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1998)\" title=\"<PERSON> (footballer, born 1998)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1998)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1998)"}]}], "Deaths": [{"year": "350", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Roman ruler", "html": "350 - <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>ianus\" title=\"Nepotian<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Roman ruler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>ianus\" title=\"N<PERSON>otian<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Roman ruler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nepotianus"}]}, {"year": "710", "text": "<PERSON><PERSON><PERSON><PERSON>, Frankish abbess", "html": "710 - <a href=\"https://wikipedia.org/wiki/Saint_Erentrude\" class=\"mw-redirect\" title=\"Saint Erentrude\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Abbess\" title=\"Abbess\">abbess</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Erentrude\" class=\"mw-redirect\" title=\"Saint Erentrude\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Abbess\" title=\"Abbess\">abbess</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Abbess", "link": "https://wikipedia.org/wiki/Abbess"}]}, {"year": "888", "text": "<PERSON><PERSON><PERSON><PERSON>, archbishop of Canterbury", "html": "888 - <a href=\"https://wikipedia.org/wiki/%C3%86thel<PERSON>_(archbishop)\" title=\"<PERSON><PERSON><PERSON><PERSON> (archbishop)\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_Canterbury\" title=\"Diocese of Canterbury\">Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86thel<PERSON>_(archbishop)\" title=\"<PERSON><PERSON><PERSON><PERSON> (archbishop)\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_Canterbury\" title=\"Diocese of Canterbury\">Canterbury</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (archbishop)", "link": "https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON>_(archbishop)"}, {"title": "Diocese of Canterbury", "link": "https://wikipedia.org/wiki/Diocese_of_Canterbury"}]}, {"year": "1066", "text": "<PERSON><PERSON>Theo<PERSON>d Of Provins", "html": "1066 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Pro<PERSON>s\" title=\"<PERSON><PERSON><PERSON> of Provins\"><PERSON><PERSON>d Of Provins</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_<PERSON>\" title=\"<PERSON><PERSON><PERSON> of Provins\"><PERSON><PERSON>d Of Provins</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Provins", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1181", "text": "<PERSON>, 5th Earl of Chester, Welsh politician (b. 1147)", "html": "1181 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Chester\" class=\"mw-redirect\" title=\"<PERSON>, 5th Earl of Chester\"><PERSON>, 5th Earl <PERSON> Chester</a>, Welsh politician (b. 1147)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Chester\" class=\"mw-redirect\" title=\"<PERSON>, 5th Earl <PERSON> Chester\"><PERSON>, 5th Earl <PERSON> Chester</a>, Welsh politician (b. 1147)", "links": [{"title": "<PERSON>, 5th Earl of Chester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Chester"}]}, {"year": "1224", "text": "<PERSON> of Osnabrück, German monk and bishop (b. 1185)", "html": "1224 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Osnabr%C3%BCck\" title=\"<PERSON> of Osnabrück\"><PERSON> of Osnabrück</a>, German monk and bishop (b. 1185)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Osnabr%C3%BCck\" title=\"<PERSON> of Osnabrück\"><PERSON> of Osnabrück</a>, German monk and bishop (b. 1185)", "links": [{"title": "Adolf of Osnabrück", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Osnabr%C3%BCck"}]}, {"year": "1278", "text": "<PERSON>, French courtier", "html": "1278 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French courtier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French courtier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1337", "text": "<PERSON>, English noblewoman (b. 1290)", "html": "1337 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1290)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1364", "text": "<PERSON><PERSON><PERSON><PERSON> of Pardubice, Czech archbishop (b. 1297)", "html": "1364 - <a href=\"https://wikipedia.org/wiki/Arno%C5%A1t_of_Pardubice\" title=\"<PERSON><PERSON><PERSON><PERSON> of Pardubice\"><PERSON><PERSON><PERSON><PERSON> of Pardubice</a>, Czech archbishop (b. 1297)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arno%C5%A1t_of_Pardubice\" title=\"<PERSON><PERSON><PERSON><PERSON> of Pardubice\"><PERSON><PERSON><PERSON><PERSON> of Pardubice</a>, Czech archbishop (b. 1297)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Pardubice", "link": "https://wikipedia.org/wiki/Arno%C5%A1t_of_Pardubice"}]}, {"year": "1538", "text": "<PERSON>, Duke of Guelders (b. 1467)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guelders\" title=\"<PERSON>, Duke of Guelders\"><PERSON>, Duke of Guelders</a> (b. 1467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guelders\" title=\"<PERSON>, Duke of Guelders\"><PERSON>, Duke of Guelders</a> (b. 1467)", "links": [{"title": "<PERSON>, Duke of Guelders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guelders"}]}, {"year": "1522", "text": "<PERSON>, German humanist and Hebrew scholar (b. 1455)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and Hebrew scholar (b. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and Hebrew scholar (b. 1455)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, Italian cardinal and historian (b. 1538)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Baron<PERSON>\" title=\"Caesar Baronius\"><PERSON></a>, Italian cardinal and historian (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Baron<PERSON>\" title=\"Caesar Baron<PERSON>\"><PERSON></a>, Italian cardinal and historian (b. 1538)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON>, French painter (b. 1590)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1590)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1660", "text": "<PERSON>, English minister and mathematician (b. 1575)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and mathematician (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and mathematician (b. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON>, English poet and playwright (b. 1620)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1620)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1670", "text": "<PERSON> of England (b. 1644)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/Henrietta_of_England\" title=\"Henrietta of England\"><PERSON> of England</a> (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henrietta_of_England\" title=\"Henrietta of England\"><PERSON> of England</a> (b. 1644)", "links": [{"title": "Henrietta of England", "link": "https://wikipedia.org/wiki/Henrietta_of_England"}]}, {"year": "1704", "text": "<PERSON>, English pirate (b. 1665)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pirate)\" title=\"<PERSON> (pirate)\"><PERSON></a>, English pirate (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pirate)\" title=\"<PERSON> (pirate)\"><PERSON></a>, English pirate (b. 1665)", "links": [{"title": "<PERSON> (pirate)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pirate)"}]}, {"year": "1708", "text": "<PERSON><PERSON> of Ethiopia (b. 1684)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Haymanot_I\" title=\"Tekle Haymanot I\"><PERSON><PERSON> Haymanot I</a> of Ethiopia (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ot_I\" title=\"Tekle Haymanot I\"><PERSON><PERSON> Haymanot I</a> of Ethiopia (b. 1684)", "links": [{"title": "<PERSON><PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_I"}]}, {"year": "1709", "text": "<PERSON>, Welsh botanist, linguist, and geographer (b. 1660)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh botanist, linguist, and geographer (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh botanist, linguist, and geographer (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, English general and politician, 1st Colonial Governor of Georgia (b. 1696)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Georgia\" title=\"List of colonial governors of Georgia\">Colonial Governor of Georgia</a> (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Georgia\" title=\"List of colonial governors of Georgia\">Colonial Governor of Georgia</a> (b. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Georgia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Georgia"}]}, {"year": "1796", "text": "<PERSON>, American lawyer and politician (b. 1724)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician (b. 1724)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1857", "text": "<PERSON><PERSON><PERSON>, French zoologist and paleontologist (b. 1802)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Alcide_d%27Orbigny\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, French zoologist and paleontologist (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alcide_d%27Orbigny\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, French zoologist and paleontologist (b. 1802)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alcide_d%27Orbigny"}]}, {"year": "1882", "text": "<PERSON>, American preacher and lawyer, assassin of <PERSON> (b. 1841)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American preacher and lawyer, assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American preacher and lawyer, assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, German-Brazilian photographer and businessman (b. 1827)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian photographer and businessman (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian photographer and businessman (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American organist and composer (b. 1819)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tuckerman\"><PERSON></a>, American organist and composer (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American painter (b. 1829)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter (b. 1829)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, French fencer (b. 1873)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French fencer (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French fencer (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, American correspondent, author, and poet (b. 1847)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American correspondent, author, and poet (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American correspondent, author, and poet (b. 1847)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French painter and illustrator (b. 1861)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A1ndara\" title=\"<PERSON> Gándara\"><PERSON></a>, French painter and illustrator (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A1ndara\" title=\"<PERSON> Gándar<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_La_G%C3%A1ndara"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Parsi intellectual, educator, cotton trader, and an early Indian political and social leader (b. 1825)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Parsi intellectual, educator, cotton trader, and an early Indian political and social leader (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Parsi intellectual, educator, cotton trader, and an early Indian political and social leader (b. 1825)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, 3rd Baron <PERSON>, English physicist and academic, Nobel Prize laureate (b. 1842)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1842)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1932", "text": "<PERSON>, German actor, producer, and screenwriter (b. 1890)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor, producer, and screenwriter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor, producer, and screenwriter (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German soldier (b. 1904)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German soldier and politician (b. 1885)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German lawyer and politician, Minister-President of Bavaria (b. 1862)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers-President_of_Bavaria\" class=\"mw-redirect\" title=\"List of Ministers-President of Bavaria\">Minister-President of Bavaria</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers-President_of_Bavaria\" class=\"mw-redirect\" title=\"List of Ministers-President of Bavaria\">Minister-President of Bavaria</a> (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ministers-President of Bavaria", "link": "https://wikipedia.org/wiki/List_of_Ministers-President_of_Bavaria"}]}, {"year": "1934", "text": "<PERSON>, German lieutenant and politician (b. 1892)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German general and politician, 23rd Chancellor of Germany (b. 1882)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and politician, 23rd <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and politician, 23rd <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (German Reich)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Belarusian politician (b. 1909)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>omin\" title=\"<PERSON><PERSON><PERSON>omin\"><PERSON><PERSON><PERSON></a>, Belarusian politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>omin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian politician (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>m_Fomin"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian general and politician, 5th Estonian Minister of War (b. 1875)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Aleksander_T%C3%B5<PERSON>son\" title=\"Aleksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian general and politician, 5th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_T%C3%B5<PERSON>son\" title=\"<PERSON>eksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian general and politician, 5th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (b. 1875)", "links": [{"title": "Aleksander <PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_T%C3%B5<PERSON>son"}, {"title": "Estonian Minister of War", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_War"}]}, {"year": "1948", "text": "<PERSON>, Turkish-Swiss sociologist and academic (b. 1879)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" class=\"mw-redirect\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Turkish-Swiss sociologist and academic (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" class=\"mw-redirect\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Turkish-Swiss sociologist and academic (b. 1879)", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, French financier and polo player (b. 1868)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>uard <PERSON>\"><PERSON><PERSON><PERSON></a>, French financier and polo player (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"É<PERSON>uard <PERSON>\"><PERSON><PERSON><PERSON></a>, French financier and polo player (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish wrestler and coach (b. 1884)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Yrj%C3%B6_<PERSON>arel<PERSON>\" title=\"<PERSON>r<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish wrestler and coach (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yrj%C3%B6_<PERSON><PERSON>\" title=\"<PERSON>r<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish wrestler and coach (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yrj%C3%B6_<PERSON><PERSON>a"}]}, {"year": "1953", "text": "<PERSON>, Swedish author and illustrator (b. 1874)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author and illustrator (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author and illustrator (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elsa_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Brazilian footballer and civil servant (b. 1874)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and civil servant (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and civil servant (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Faroese politician, 1st Prime Minister of the Faroe Islands (b. 1873)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Norwegian actor (b. 1880)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian actor (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian actor (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Mexican philosopher and politician (b. 1882)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Vasco<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican philosopher and politician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Vasco<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican philosopher and politician (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Vasconcelos"}]}, {"year": "1961", "text": "<PERSON>, American inventor, invented the audion tube (b. 1873)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lee <PERSON>\"><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Audion_tube\" class=\"mw-redirect\" title=\"Audion tube\">audion tube</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lee <PERSON>\"><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Audion_tube\" class=\"mw-redirect\" title=\"Audion tube\">audion tube</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Audion tube", "link": "https://wikipedia.org/wiki/Audion_tube"}]}, {"year": "1966", "text": "<PERSON>, Italian race car driver (b. 1906)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, English author of detective fiction (b. 1904)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author of detective fiction (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author of detective fiction (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ingham"}]}, {"year": "1968", "text": "<PERSON>, German zoologist (b. 1893)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(zoologist)\" title=\"<PERSON> (zoologist)\"><PERSON></a>, German zoologist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(zoologist)\" title=\"<PERSON> (zoologist)\"><PERSON></a>, German zoologist (b. 1893)", "links": [{"title": "<PERSON> (zoologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(zoologist)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Bulgarian footballer (b. 1943)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American director and screenwriter (b. 1900)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON> Ukrainian pilot and astronaut (b. 1928)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Ukrainian pilot and astronaut (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Ukrainian pilot and astronaut (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ky"}]}, {"year": "1971", "text": "<PERSON>, Bulgarian footballer (b. 1938)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian footballer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian footballer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Kazakh engineer and astronaut (b. 1933)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakh engineer and astronaut (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakh engineer and astronaut (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Russian engineer and astronaut (b. 1935)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English journalist and author (b. 1904)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Canadian bishop and martyr (b. 1903)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Canadian bishop and martyr (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Canadian bishop and martyr (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Civil rights activist (b. 1904)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Alberta_Williams_King\" title=\"Alberta Williams King\"><PERSON></a>, Civil rights activist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alberta_Williams_<PERSON>\" title=\"Alberta Williams King\"><PERSON></a>, Civil rights activist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberta_Williams_King"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American baseball player and umpire (b. 1898)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Firpo_<PERSON>\" title=\"Firpo Marberry\"><PERSON><PERSON><PERSON></a>, American baseball player and umpire (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Firpo_<PERSON>\" title=\"Firpo Marberry\"><PERSON><PERSON><PERSON></a>, American baseball player and umpire (b. 1898)", "links": [{"title": "Firpo Marberry", "link": "https://wikipedia.org/wiki/Firpo_<PERSON>berry"}]}, {"year": "1984", "text": "<PERSON>, American author and playwright (b. 1905)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Palauan politician, 1st President of Palau (b. 1933)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palauan politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Palau\" title=\"President of Palau\">President of Palau</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palauan politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Palau\" title=\"President of Palau\">President of Palau</a> (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>iik"}, {"title": "President of Palau", "link": "https://wikipedia.org/wiki/President_of_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Ukrainian general and astronaut (b. 1921)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian general and astronaut (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian general and astronaut (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actor and voice artist (b. 1906)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Greek footballer and manager (b. 1932)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American saxophonist and composer (b. 1937)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Brazilian medium and author (b. 1910)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Xavier\"><PERSON></a>, Brazilian medium and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chico_Xavier\" title=\"Chico Xavier\"><PERSON></a>, Brazilian medium and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chico_Xavier"}]}, {"year": "2003", "text": "<PERSON>, American actor and comedian (b. 1924)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American author and illustrator (b. 1915)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Australian rugby league player (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Indian librarian and politician, 4th Chief Minister of Delhi (b. 1943)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian librarian and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Delhi\" class=\"mw-redirect\" title=\"List of Chief Ministers of Delhi\">Chief Minister of Delhi</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian librarian and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Delhi\" class=\"mw-redirect\" title=\"List of Chief Ministers of Delhi\">Chief Minister of Delhi</a> (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Chief Ministers of Delhi", "link": "https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Delhi"}]}, {"year": "2009", "text": "<PERSON><PERSON>, German dancer, choreographer, and director (b. 1940)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German dancer, choreographer, and director (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German dancer, choreographer, and director (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American actor and singer (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nell\"><PERSON><PERSON></a>, American actor and singer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>-<PERSON>, 14th Earl of Loudoun, English-Australian politician (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_Earl_of_Loudoun\" title=\"<PERSON>, 14th Earl of Loudoun\"><PERSON>, 14th Earl of Loudoun</a>, English-Australian politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Loudoun\" title=\"<PERSON>, 14th Earl of Loudoun\"><PERSON>, 14th Earl of Loudoun</a>, English-Australian politician (b. 1942)", "links": [{"title": "<PERSON>-<PERSON>, 14th Earl of Loudoun", "link": "https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Loudoun"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Israeli politician, 7th Prime Minister of Israel (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Israel\" class=\"mw-redirect\" title=\"List of Prime Ministers of Israel\">Prime Minister of Israel</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Israel\" class=\"mw-redirect\" title=\"List of Prime Ministers of Israel\">Prime Minister of Israel</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Israel", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Israel"}]}, {"year": "2012", "text": "<PERSON>, American journalist and author (b. 1966)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Baron <PERSON> of Alloway, English lawyer and judge (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Alloway\" title=\"<PERSON>, Baron <PERSON> of Alloway\"><PERSON>, Baron <PERSON> of Alloway</a>, English lawyer and judge (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Alloway\" title=\"<PERSON>, Baron <PERSON> of Alloway\"><PERSON>, Baron <PERSON> of Alloway</a>, English lawyer and judge (b. 1917)", "links": [{"title": "<PERSON>, Baron <PERSON> of Alloway", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Alloway"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Nigerian politician (b. 1963)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ido\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian politician (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>her<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian politician (b. 1963)", "links": [{"title": "Akpor <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ido"}]}, {"year": "2013", "text": "<PERSON>, American educator and politician (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(legislator)\" class=\"mw-redirect\" title=\"<PERSON> (legislator)\"><PERSON></a>, American educator and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(legislator)\" class=\"mw-redirect\" title=\"<PERSON> (legislator)\"><PERSON></a>, American educator and politician (b. 1942)", "links": [{"title": "<PERSON> (legislator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(legislator)"}]}, {"year": "2013", "text": "<PERSON>, Nigerian footballer (b. 1968)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian politician, 29th Governor of South Australia (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of South Australia", "link": "https://wikipedia.org/wiki/Governor_of_South_Australia"}]}, {"year": "2014", "text": "<PERSON>, American businessman (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Montenegrin lawyer and politician, 31st Prime Minister of Montenegro (b. 1960)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/%C5%BDeljko_%C5%A0turanovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Montenegrin lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Montenegro\" title=\"Prime Minister of Montenegro\">Prime Minister of Montenegro</a> (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%B<PERSON><PERSON>jko_%C5%A0turanovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Montenegrin lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Montenegro\" title=\"Prime Minister of Montenegro\">Prime Minister of Montenegro</a> (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%BDeljko_%C5%A0turanovi%C4%87"}, {"title": "Prime Minister of Montenegro", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Montenegro"}]}, {"year": "2015", "text": "<PERSON>, American general (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English-American computer scientist and academic (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American computer scientist and academic (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American computer scientist and academic (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian physician and academic (b. 1956)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Canadian physician and academic (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Canadian physician and academic (b. 1956)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "2015", "text": "<PERSON>, American author and illustrator (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, English television presenter (b. 1933)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, French lawyer and politician (b. 1927)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Canadian rapper (b. 1996)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Smoke_Dawg\" title=\"Smoke Dawg\"><PERSON> Dawg</a>, Canadian rapper (b. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Smoke_Dawg\" title=\"Smoke Dawg\"><PERSON> Dawg</a>, Canadian rapper (b. 1996)", "links": [{"title": "Smoke Dawg", "link": "https://wikipedia.org/wiki/Smoke_Dawg"}]}, {"year": "2021", "text": "<PERSON>, Indian Film Director and Producer (b. 1971)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Film Director and Producer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Film Director and Producer (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "Technoblade, American YouTuber and streamer (b. 1999)[a]", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Technoblade\" title=\"Technoblade\">Technoblade</a>, American YouTuber and streamer (b. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Technoblade\" title=\"Technoblade\">Technoblade</a>, American YouTuber and streamer (b. 1999)", "links": [{"title": "Technoblade", "link": "https://wikipedia.org/wiki/Technoblade"}]}]}}