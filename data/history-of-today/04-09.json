{"date": "April 9", "url": "https://wikipedia.org/wiki/April_9", "data": {"Events": [{"year": "193", "text": "The distinguished soldier <PERSON><PERSON><PERSON> is proclaimed emperor by the army in Illyricum.", "html": "193 - The distinguished soldier <a href=\"https://wikipedia.org/wiki/Septimius_Severus\" title=\"Septimius Severus\">Sept<PERSON><PERSON></a> is proclaimed emperor by the army in <a href=\"https://wikipedia.org/wiki/Illyricum_(Roman_province)\" title=\"Illyricum (Roman province)\">Illyricum</a>.", "no_year_html": "The distinguished soldier <a href=\"https://wikipedia.org/wiki/Septimius_Severus\" title=\"Septimius Severus\">Sept<PERSON><PERSON></a> is proclaimed emperor by the army in <a href=\"https://wikipedia.org/wiki/Illyricum_(Roman_province)\" title=\"Illyricum (Roman province)\">Il<PERSON>ricum</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Septimius_Severus"}, {"title": "Illyricum (Roman province)", "link": "https://wikipedia.org/wiki/Illyricum_(Roman_province)"}]}, {"year": "475", "text": "Byzantine Emperor <PERSON><PERSON><PERSON> issues a circular letter (Enkyklikon) to the bishops of his empire, supporting the Monophysite christological position.", "html": "475 - <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/Basiliscus\" title=\"Basiliscus\">Basiliscus</a> issues a circular letter (<i>Enkyklikon</i>) to the bishops of his empire, supporting the <a href=\"https://wikipedia.org/wiki/Monophysitism\" title=\"Monophysitism\">Monophysite</a> <a href=\"https://wikipedia.org/wiki/Christological\" class=\"mw-redirect\" title=\"Christological\">christological</a> position.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/Basiliscus\" title=\"Basiliscus\">Basiliscus</a> issues a circular letter (<i>Enkyklik<PERSON></i>) to the bishops of his empire, supporting the <a href=\"https://wikipedia.org/wiki/Monophysitism\" title=\"Monophysitism\">Monophysite</a> <a href=\"https://wikipedia.org/wiki/Christological\" class=\"mw-redirect\" title=\"Christological\">christological</a> position.", "links": [{"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}, {"title": "Basiliscus", "link": "https://wikipedia.org/wiki/Basiliscus"}, {"title": "Monophysitism", "link": "https://wikipedia.org/wiki/Monophysitism"}, {"title": "Christological", "link": "https://wikipedia.org/wiki/Christological"}]}, {"year": "537", "text": "Siege of Rome: The Byzantine general <PERSON><PERSON><PERSON> receives his promised reinforcements, 1,600 cavalry, mostly of Hunnic or Slavic origin and expert bowmen. He starts, despite shortages, raids against the Gothic camps and Vitiges but is forced into a stalemate.", "html": "537 - <a href=\"https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%93538)\" title=\"Siege of Rome (537-538)\">Siege of Rome</a>: The Byzantine general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> receives his promised reinforcements, 1,600 cavalry, mostly of <a href=\"https://wikipedia.org/wiki/Huns\" title=\"Huns\"><PERSON><PERSON><PERSON></a> or <a href=\"https://wikipedia.org/wiki/Slavs\" title=\"Slavs\">Slavic</a> origin and expert <a href=\"https://wikipedia.org/wiki/Archery\" title=\"Archery\">bowmen</a>. He starts, despite shortages, raids against the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Gothic</a> camps and <a href=\"https://wikipedia.org/wiki/Vitiges\" title=\"Vitiges\">Vitiges</a> but is forced into a stalemate.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%93538)\" title=\"Siege of Rome (537-538)\">Siege of Rome</a>: The Byzantine general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> receives his promised reinforcements, 1,600 cavalry, mostly of <a href=\"https://wikipedia.org/wiki/Huns\" title=\"Huns\"><PERSON><PERSON><PERSON></a> or <a href=\"https://wikipedia.org/wiki/Slavs\" title=\"Slavs\">Slavic</a> origin and expert <a href=\"https://wikipedia.org/wiki/Archery\" title=\"Archer<PERSON>\">bowmen</a>. He starts, despite shortages, raids against the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Gothic</a> camps and <a href=\"https://wikipedia.org/wiki/Vitiges\" title=\"Vitiges\">Vitiges</a> but is forced into a stalemate.", "links": [{"title": "Siege of Rome (537-538)", "link": "https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%93538)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belisarius"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Huns"}, {"title": "Slavs", "link": "https://wikipedia.org/wiki/Slavs"}, {"title": "Archery", "link": "https://wikipedia.org/wiki/Archery"}, {"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}, {"title": "Vitiges", "link": "https://wikipedia.org/wiki/Vitiges"}]}, {"year": "1241", "text": "Battle of Liegnitz: Mongol forces defeat the Polish and German armies.", "html": "1241 - <a href=\"https://wikipedia.org/wiki/Battle_of_Legnica\" title=\"Battle of Legnica\">Battle of Liegnitz</a>: <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol</a> forces defeat the Polish and German armies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Legnica\" title=\"Battle of Legnica\">Battle of Liegnitz</a>: <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol</a> forces defeat the Polish and German armies.", "links": [{"title": "Battle of Legnica", "link": "https://wikipedia.org/wiki/Battle_of_Legnica"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}]}, {"year": "1288", "text": "Mongol invasions of Vietnam: Yuan forces are defeated by Trần forces in the Battle of Bach Dang in present-day northern Vietnam.", "html": "1288 - <a href=\"https://wikipedia.org/wiki/Mongol_invasions_of_Vietnam\" title=\"Mongol invasions of Vietnam\">Mongol invasions of Vietnam</a>: <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Yuan</a> forces are defeated by <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần</a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_B%E1%BA%A1ch_%C4%90%E1%BA%B1ng_(1288)\" title=\"Battle of Bạch Đằng (1288)\">Battle of Bach Dang</a> in present-day northern Vietnam.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongol_invasions_of_Vietnam\" title=\"Mongol invasions of Vietnam\">Mongol invasions of Vietnam</a>: <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Yuan</a> forces are defeated by <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần</a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_B%E1%BA%A1ch_%C4%90%E1%BA%B1ng_(1288)\" title=\"Battle of Bạch Đằng (1288)\">Battle of Bach Dang</a> in present-day northern Vietnam.", "links": [{"title": "Mongol invasions of Vietnam", "link": "https://wikipedia.org/wiki/Mongol_invasions_of_Vietnam"}, {"title": "Yuan dynasty", "link": "https://wikipedia.org/wiki/Yuan_dynasty"}, {"title": "Trần dynasty", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty"}, {"title": "Battle of Bạch Đằng (1288)", "link": "https://wikipedia.org/wiki/Battle_of_B%E1%BA%A1ch_%C4%90%E1%BA%B1ng_(1288)"}]}, {"year": "1388", "text": "Despite being outnumbered 16:1, forces of the Old Swiss Confederacy are victorious over the Archduchy of Austria in the Battle of Näfels.", "html": "1388 - Despite being outnumbered 16:1, forces of the <a href=\"https://wikipedia.org/wiki/Old_Swiss_Confederacy\" title=\"Old Swiss Confederacy\">Old Swiss Confederacy</a> are victorious over the <a href=\"https://wikipedia.org/wiki/Archduchy_of_Austria\" title=\"Archduchy of Austria\">Archduchy of Austria</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_N%C3%A4fels\" title=\"Battle of Näfels\">Battle of Näfels</a>.", "no_year_html": "Despite being outnumbered 16:1, forces of the <a href=\"https://wikipedia.org/wiki/Old_Swiss_Confederacy\" title=\"Old Swiss Confederacy\">Old Swiss Confederacy</a> are victorious over the <a href=\"https://wikipedia.org/wiki/Archduchy_of_Austria\" title=\"Archduchy of Austria\">Archduchy of Austria</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_N%C3%A4fels\" title=\"Battle of Näfels\">Battle of Näfels</a>.", "links": [{"title": "Old Swiss Confederacy", "link": "https://wikipedia.org/wiki/Old_Swiss_Confederacy"}, {"title": "Archduchy of Austria", "link": "https://wikipedia.org/wiki/Archduchy_of_Austria"}, {"title": "Battle of Näfels", "link": "https://wikipedia.org/wiki/Battle_of_N%C3%A4fels"}]}, {"year": "1454", "text": "The Treaty of Lodi is signed, establishing a balance of power among northern Italian city-states for almost 50 years.", "html": "1454 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lodi\" title=\"Treaty of Lodi\">Treaty of Lodi</a> is signed, establishing a balance of power among northern Italian city-states for almost 50 years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lodi\" title=\"Treaty of Lodi\">Treaty of Lodi</a> is signed, establishing a balance of power among northern Italian city-states for almost 50 years.", "links": [{"title": "Treaty of Lodi", "link": "https://wikipedia.org/wiki/Treaty_of_Lodi"}]}, {"year": "1609", "text": "Eighty Years' War: Spain and the Dutch Republic sign the Treaty of Antwerp to initiate twelve years of truce.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spain</a> and the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Antwerp_(1609)\" title=\"Treaty of Antwerp (1609)\">Treaty of Antwerp</a> to initiate twelve years of truce.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spain</a> and the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Antwerp_(1609)\" title=\"Treaty of Antwerp (1609)\">Treaty of Antwerp</a> to initiate twelve years of truce.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}, {"title": "Treaty of Antwerp (1609)", "link": "https://wikipedia.org/wiki/Treaty_of_Antwerp_(1609)"}]}, {"year": "1609", "text": "<PERSON> of Spain issues the decree of the \"Expulsion of the Moriscos\".", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> III of Spain\"><PERSON> of Spain</a> issues <a href=\"https://wikipedia.org/wiki/Expulsion_of_the_Moriscos\" title=\"Expulsion of the Moriscos\">the decree of the \"Expulsion of the Moriscos\"</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> issues <a href=\"https://wikipedia.org/wiki/Expulsion_of_the_Moriscos\" title=\"Expulsion of the Moriscos\">the decree of the \"Expulsion of the Moriscos\"</a>.", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}, {"title": "Expulsion of the Moriscos", "link": "https://wikipedia.org/wiki/Expulsion_of_the_Moriscos"}]}, {"year": "1682", "text": "<PERSON> discovers the mouth of the Mississippi River, claims it for France and names it Louisiana.", "html": "1682 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\"><PERSON></a> discovers the mouth of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>, claims it for France and names it <a href=\"https://wikipedia.org/wiki/Louisiana_(New_France)\" title=\"Louisiana (New France)\">Louisiana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\"><PERSON></a> discovers the mouth of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>, claims it for France and names it <a href=\"https://wikipedia.org/wiki/Louisiana_(New_France)\" title=\"Louisiana (New France)\">Louisiana</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "Louisiana (New France)", "link": "https://wikipedia.org/wiki/Louisiana_(New_France)"}]}, {"year": "1784", "text": "The Treaty of Paris, ratified by the United States Congress on January 14, 1784, is ratified by King <PERSON> of the Kingdom of Great Britain, ending the American Revolutionary War. Copies of the ratified documents are exchanged on May 12, 1784.", "html": "1784 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1783)\" title=\"Treaty of Paris (1783)\">Treaty of Paris</a>, ratified by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> on January 14, 1784, is ratified by <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> III of the United Kingdom\">King <PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a>, ending the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>. Copies of the ratified documents are exchanged on May 12, 1784.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1783)\" title=\"Treaty of Paris (1783)\">Treaty of Paris</a>, ratified by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> on January 14, 1784, is ratified by <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> III of the United Kingdom\">King <PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a>, ending the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>. Copies of the ratified documents are exchanged on May 12, 1784.", "links": [{"title": "Treaty of Paris (1783)", "link": "https://wikipedia.org/wiki/Treaty_of_Paris_(1783)"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1860", "text": "On his phonautograph machine, <PERSON><PERSON><PERSON><PERSON><PERSON> makes the first known recording of an audible human voice.", "html": "1860 - On his <a href=\"https://wikipedia.org/wiki/Phonautograph\" title=\"Phonautograph\">phonautograph</a> machine, <a href=\"https://wikipedia.org/wiki/%C3%89douard-<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Édouard<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> makes the <a href=\"https://wikipedia.org/wiki/History_of_sound_recording\" title=\"History of sound recording\">first known recording</a> of an audible human voice.", "no_year_html": "On his <a href=\"https://wikipedia.org/wiki/Phonautograph\" title=\"Phonautograph\">phonautograph</a> machine, <a href=\"https://wikipedia.org/wiki/%C3%89douard-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>uard<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> makes the <a href=\"https://wikipedia.org/wiki/History_of_sound_recording\" title=\"History of sound recording\">first known recording</a> of an audible human voice.", "links": [{"title": "Phonautograph", "link": "https://wikipedia.org/wiki/Phonautograph"}, {"title": "Édouard<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89douard-L%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "History of sound recording", "link": "https://wikipedia.org/wiki/History_of_sound_recording"}]}, {"year": "1865", "text": "American Civil War: <PERSON> surrenders the Army of Northern Virginia (26,765 troops) to <PERSON> at Appomattox Court House, Virginia, effectively ending the war.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Appomattox_Court_House#Surrender\" title=\"Battle of Appomattox Court House\">surrenders</a> the <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> (26,765 troops) to <a href=\"https://wikipedia.org/wiki/Ulysses_<PERSON>._Grant\" title=\"Ulysses S. Grant\">Ulysses <PERSON> Grant</a> at <a href=\"https://wikipedia.org/wiki/Appomattox_Court_House_National_Historical_Park\" title=\"Appomattox Court House National Historical Park\">Appomattox Court House, Virginia</a>, effectively ending the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Appomattox_Court_House#Surrender\" title=\"Battle of Appomattox Court House\">surrenders</a> the <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> (26,765 troops) to <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses S. Grant\">Ulysses <PERSON> Grant</a> at <a href=\"https://wikipedia.org/wiki/Appomattox_Court_House_National_Historical_Park\" title=\"Appomattox Court House National Historical Park\">Appomattox Court House, Virginia</a>, effectively ending the war.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Appomattox Court House", "link": "https://wikipedia.org/wiki/Battle_of_Appomattox_Court_House#Surrender"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Appomattox Court House National Historical Park", "link": "https://wikipedia.org/wiki/Appomattox_Court_House_National_Historical_Park"}]}, {"year": "1909", "text": "The U.S. Congress passes the Payne-Aldrich Tariff Act.", "html": "1909 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Payne%E2%80%93Aldrich_Tariff_Act\" title=\"Payne-Aldrich Tariff Act\">Payne-Aldrich Tariff Act</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Payne%E2%80%93Aldrich_Tariff_Act\" title=\"Payne-Aldrich Tariff Act\">Payne-Aldrich Tariff Act</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Payne-Aldrich Tariff Act", "link": "https://wikipedia.org/wiki/Payne%E2%80%93Aldrich_Tariff_Act"}]}, {"year": "1917", "text": "World War I: The Battle of Arras: The battle begins with Canadian Corps executing a massive assault on Vimy Ridge.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Arras_(1917)\" title=\"Battle of Arras (1917)\">Battle of Arras</a>: The battle begins with <a href=\"https://wikipedia.org/wiki/Canadian_Corps\" title=\"Canadian Corps\">Canadian Corps</a> executing a <a href=\"https://wikipedia.org/wiki/Battle_of_Vimy_Ridge\" title=\"Battle of Vimy Ridge\">massive assault on Vimy Ridge</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Arras_(1917)\" title=\"Battle of Arras (1917)\">Battle of Arras</a>: The battle begins with <a href=\"https://wikipedia.org/wiki/Canadian_Corps\" title=\"Canadian Corps\">Canadian Corps</a> executing a <a href=\"https://wikipedia.org/wiki/Battle_of_Vimy_Ridge\" title=\"Battle of Vimy Ridge\">massive assault on Vimy Ridge</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Arras (1917)", "link": "https://wikipedia.org/wiki/Battle_of_Arras_(1917)"}, {"title": "Canadian Corps", "link": "https://wikipedia.org/wiki/Canadian_Corps"}, {"title": "Battle of Vimy Ridge", "link": "https://wikipedia.org/wiki/Battle_of_Vimy_Ridge"}]}, {"year": "1918", "text": "World War I: The Battle of the Lys: The Portuguese Expeditionary Corps is crushed by the German forces during what is called the Spring Offensive on the Belgian region of Flanders.", "html": "1918 - World War I: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Lys_(1918)\" title=\"Battle of the Lys (1918)\">Battle of the Lys</a>: The <a href=\"https://wikipedia.org/wiki/Portuguese_Expeditionary_Corps\" title=\"Portuguese Expeditionary Corps\">Portuguese Expeditionary Corps</a> is crushed by the German forces during what is called the <a href=\"https://wikipedia.org/wiki/German_spring_offensive\" title=\"German spring offensive\">Spring Offensive</a> on the Belgian region of <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flanders</a>.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Lys_(1918)\" title=\"Battle of the Lys (1918)\">Battle of the Lys</a>: The <a href=\"https://wikipedia.org/wiki/Portuguese_Expeditionary_Corps\" title=\"Portuguese Expeditionary Corps\">Portuguese Expeditionary Corps</a> is crushed by the German forces during what is called the <a href=\"https://wikipedia.org/wiki/German_spring_offensive\" title=\"German spring offensive\">Spring Offensive</a> on the Belgian region of <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flanders</a>.", "links": [{"title": "Battle of the Lys (1918)", "link": "https://wikipedia.org/wiki/Battle_of_the_Lys_(1918)"}, {"title": "Portuguese Expeditionary Corps", "link": "https://wikipedia.org/wiki/Portuguese_Expeditionary_Corps"}, {"title": "German spring offensive", "link": "https://wikipedia.org/wiki/German_spring_offensive"}, {"title": "Flanders", "link": "https://wikipedia.org/wiki/Flanders"}]}, {"year": "1937", "text": "The Kamikaze arrives at Croydon Airport in London. It is the first Japanese-built aircraft to fly to Europe.", "html": "1937 - The <i><a href=\"https://wikipedia.org/wiki/Kamikaze_(aircraft)\" class=\"mw-redirect\" title=\"Kamikaze (aircraft)\"><PERSON><PERSON>ka<PERSON></a></i> arrives at <a href=\"https://wikipedia.org/wiki/Croydon_Airport\" title=\"Croydon Airport\">Croydon Airport</a> in London. It is the first Japanese-built aircraft to fly to Europe.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Kamika<PERSON>_(aircraft)\" class=\"mw-redirect\" title=\"Kamikaze (aircraft)\"><PERSON><PERSON><PERSON><PERSON></a></i> arrives at <a href=\"https://wikipedia.org/wiki/Croydon_Airport\" title=\"Croydon Airport\">Croydon Airport</a> in London. It is the first Japanese-built aircraft to fly to Europe.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (aircraft)", "link": "https://wikipedia.org/wiki/Kamikaze_(aircraft)"}, {"title": "Croydon Airport", "link": "https://wikipedia.org/wiki/Croydon_Airport"}]}, {"year": "1939", "text": "African-American singer <PERSON> gives a concert at the Lincoln Memorial after being denied the use of Constitution Hall by the Daughters of the American Revolution.", "html": "1939 - African-American singer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives a concert at the <a href=\"https://wikipedia.org/wiki/Lincoln_Memorial\" title=\"Lincoln Memorial\">Lincoln Memorial</a> after being denied the use of <a href=\"https://wikipedia.org/wiki/Constitution_Hall\" class=\"mw-redirect\" title=\"Constitution Hall\">Constitution Hall</a> by the <a href=\"https://wikipedia.org/wiki/Daughters_of_the_American_Revolution\" title=\"Daughters of the American Revolution\">Daughters of the American Revolution</a>.", "no_year_html": "African-American singer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives a concert at the <a href=\"https://wikipedia.org/wiki/Lincoln_Memorial\" title=\"Lincoln Memorial\">Lincoln Memorial</a> after being denied the use of <a href=\"https://wikipedia.org/wiki/Constitution_Hall\" class=\"mw-redirect\" title=\"Constitution Hall\">Constitution Hall</a> by the <a href=\"https://wikipedia.org/wiki/Daughters_of_the_American_Revolution\" title=\"Daughters of the American Revolution\">Daughters of the American Revolution</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lincoln Memorial", "link": "https://wikipedia.org/wiki/Lincoln_Memorial"}, {"title": "Constitution Hall", "link": "https://wikipedia.org/wiki/Constitution_Hall"}, {"title": "Daughters of the American Revolution", "link": "https://wikipedia.org/wiki/Daughters_of_the_American_Revolution"}]}, {"year": "1940", "text": "World War II: Operation Weserübung: Germany invades Denmark and Norway.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Weser%C3%BCbung\" title=\"Operation Weserübung\">Operation Weserübung</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> invades Denmark and Norway.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Weser%C3%BCbung\" title=\"Operation Weserübung\">Operation Weserübung</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> invades Denmark and Norway.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Weserübung", "link": "https://wikipedia.org/wiki/Operation_Weser%C3%BCbung"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON> seizes power in Norway.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Vidkun_<PERSON>ui<PERSON>ling\" title=\"Vidku<PERSON> Qui<PERSON>ling\">Vid<PERSON><PERSON></a> seizes power in Norway.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vidkun_Quisling\" title=\"Vidku<PERSON> Quisling\">Vidku<PERSON>ui<PERSON>ling</a> seizes power in Norway.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vidku<PERSON>_<PERSON>ui<PERSON>ling"}]}, {"year": "1942", "text": "World War II: The Battle of Bataan ends. An Indian Ocean raid by Japan's 1st Air Fleet sinks the British aircraft carrier HMS Hermes and the Australian destroyer HMAS Vampire.", "html": "1942 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Bataan\" title=\"Battle of Bataan\">Battle of Bataan</a> ends. An <a href=\"https://wikipedia.org/wiki/Indian_Ocean_raid\" title=\"Indian Ocean raid\">Indian Ocean raid</a> by Japan's <a href=\"https://wikipedia.org/wiki/1st_Air_Fleet\" title=\"1st Air Fleet\">1st Air Fleet</a> sinks the British aircraft carrier <a href=\"https://wikipedia.org/wiki/HMS_Hermes_(95)\" title=\"HMS Hermes (95)\">HMS <i>Hermes</i></a> and the Australian destroyer <a href=\"https://wikipedia.org/wiki/HMAS_Vampire_(D68)\" title=\"HMAS Vampire (D68)\">HMAS <i>Vampire</i></a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Bataan\" title=\"Battle of Bataan\">Battle of Bataan</a> ends. An <a href=\"https://wikipedia.org/wiki/Indian_Ocean_raid\" title=\"Indian Ocean raid\">Indian Ocean raid</a> by Japan's <a href=\"https://wikipedia.org/wiki/1st_Air_Fleet\" title=\"1st Air Fleet\">1st Air Fleet</a> sinks the British aircraft carrier <a href=\"https://wikipedia.org/wiki/HMS_Hermes_(95)\" title=\"HMS Hermes (95)\">HMS <i>Hermes</i></a> and the Australian destroyer <a href=\"https://wikipedia.org/wiki/HMAS_Vampire_(D68)\" title=\"HMAS Vampire (D68)\">HMAS <i>Vampire</i></a>.", "links": [{"title": "Battle of Bataan", "link": "https://wikipedia.org/wiki/Battle_of_Bataan"}, {"title": "Indian Ocean raid", "link": "https://wikipedia.org/wiki/Indian_Ocean_raid"}, {"title": "1st Air Fleet", "link": "https://wikipedia.org/wiki/1st_Air_Fleet"}, {"title": "HMS Hermes (95)", "link": "https://wikipedia.org/wiki/HMS_Hermes_(95)"}, {"title": "HMAS Vampire (D68)", "link": "https://wikipedia.org/wiki/HMAS_Vampire_(D68)"}]}, {"year": "1945", "text": "<PERSON>, Lutheran pastor and anti-Nazi dissident, is executed by the Nazi regime.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lutheran pastor and anti-Nazi dissident, is executed by the Nazi regime.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lutheran pastor and anti-Nazi dissident, is executed by the Nazi regime.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "World War II: The German heavy cruiser Admiral <PERSON><PERSON><PERSON> is sunk by the Royal Air Force.", "html": "1945 - World War II: The German heavy cruiser <a href=\"https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>\" title=\"German cruiser Admiral <PERSON>\"><i>Admiral <PERSON></i></a> is sunk by the <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a>.", "no_year_html": "World War II: The German heavy cruiser <a href=\"https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>\" title=\"German cruiser Admiral <PERSON>\"><i>Admiral <PERSON><PERSON><PERSON></i></a> is sunk by the <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a>.", "links": [{"title": "German cruiser Admiral <PERSON>", "link": "https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}]}, {"year": "1945", "text": "World War II: The Battle of Königsberg, in East Prussia, ends.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_K%C3%B6nigsberg\" title=\"Battle of Königsberg\">Battle of Königsberg</a>, in <a href=\"https://wikipedia.org/wiki/East_Prussia\" title=\"East Prussia\">East Prussia</a>, ends.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_K%C3%B6nigsberg\" title=\"Battle of Königsberg\">Battle of Königsberg</a>, in <a href=\"https://wikipedia.org/wiki/East_Prussia\" title=\"East Prussia\">East Prussia</a>, ends.", "links": [{"title": "Battle of Königsberg", "link": "https://wikipedia.org/wiki/Battle_of_K%C3%B6nigsberg"}, {"title": "East Prussia", "link": "https://wikipedia.org/wiki/East_Prussia"}]}, {"year": "1945", "text": "The United States Atomic Energy Commission is formed.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/United_States_Atomic_Energy_Commission\" title=\"United States Atomic Energy Commission\">United States Atomic Energy Commission</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Atomic_Energy_Commission\" title=\"United States Atomic Energy Commission\">United States Atomic Energy Commission</a> is formed.", "links": [{"title": "United States Atomic Energy Commission", "link": "https://wikipedia.org/wiki/United_States_Atomic_Energy_Commission"}]}, {"year": "1947", "text": "The Glazier-Higgins-Woodward tornadoes kill 181 and injure 970 in Texas, Oklahoma, and Kansas.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Glazier%E2%80%93Higgins%E2%80%93Woodward_tornadoes\" class=\"mw-redirect\" title=\"Glazier-<PERSON>-<PERSON> tornadoes\"><PERSON><PERSON><PERSON>-<PERSON>-<PERSON> tornadoes</a> kill 181 and injure 970 in <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>, <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>, and <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Glazier%E2%80%93Higgins%E2%80%93Woodward_tornadoes\" class=\"mw-redirect\" title=\"Gla<PERSON>-<PERSON>-<PERSON> tornadoes\"><PERSON><PERSON><PERSON>-<PERSON>-<PERSON> tornadoes</a> kill 181 and injure 970 in <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>, <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>, and <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a>.", "links": [{"title": "Glazier-Higgins-Woodward tornadoes", "link": "https://wikipedia.org/wiki/Glazier%E2%80%93Higgins%E2%80%93Woodward_tornadoes"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Oklahoma", "link": "https://wikipedia.org/wiki/Oklahoma"}, {"title": "Kansas", "link": "https://wikipedia.org/wiki/Kansas"}]}, {"year": "1947", "text": "The Journey of Reconciliation, the first interracial Freedom Ride begins through the upper South in violation of Jim Crow laws. The riders wanted enforcement of the United States Supreme Court's 1946 <PERSON> decision that banned racial segregation in interstate travel.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Journey_of_Reconciliation\" title=\"Journey of Reconciliation\">Journey of Reconciliation</a>, the first interracial <a href=\"https://wikipedia.org/wiki/Freedom_Riders\" title=\"Freedom Riders\">Freedom Ride</a> begins through the upper <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">South</a> in violation of <a href=\"https://wikipedia.org/wiki/Jim_Crow_laws\" title=\"Jim Crow laws\">Jim Crow laws</a>. The riders wanted enforcement of the <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a>'s <a href=\"https://wikipedia.org/wiki/1946\" title=\"1946\">1946</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> decision that banned <a href=\"https://wikipedia.org/wiki/Racial_segregation\" title=\"Racial segregation\">racial segregation</a> in interstate travel.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Journey_of_Reconciliation\" title=\"Journey of Reconciliation\">Journey of Reconciliation</a>, the first interracial <a href=\"https://wikipedia.org/wiki/Freedom_Riders\" title=\"Freedom Riders\">Freedom Ride</a> begins through the upper <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">South</a> in violation of <a href=\"https://wikipedia.org/wiki/Jim_Crow_laws\" title=\"Jim Crow laws\">Jim Crow laws</a>. The riders wanted enforcement of the <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a>'s <a href=\"https://wikipedia.org/wiki/1946\" title=\"1946\">1946</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> decision that banned <a href=\"https://wikipedia.org/wiki/Racial_segregation\" title=\"Racial segregation\">racial segregation</a> in interstate travel.", "links": [{"title": "Journey of Reconciliation", "link": "https://wikipedia.org/wiki/Journey_of_Reconciliation"}, {"title": "Freedom Riders", "link": "https://wikipedia.org/wiki/Freedom_Riders"}, {"title": "Southern United States", "link": "https://wikipedia.org/wiki/Southern_United_States"}, {"title": "<PERSON> laws", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_laws"}, {"title": "United States Supreme Court", "link": "https://wikipedia.org/wiki/United_States_Supreme_Court"}, {"title": "1946", "link": "https://wikipedia.org/wiki/1946"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Racial segregation", "link": "https://wikipedia.org/wiki/Racial_segregation"}]}, {"year": "1947", "text": "United Nations Security Council Resolution 22 relating to Corfu Channel incident is adopted.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_22\" title=\"United Nations Security Council Resolution 22\">United Nations Security Council Resolution 22</a> relating to <a href=\"https://wikipedia.org/wiki/Corfu_Channel_incident\" title=\"Corfu Channel incident\">Corfu Channel incident</a> is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_22\" title=\"United Nations Security Council Resolution 22\">United Nations Security Council Resolution 22</a> relating to <a href=\"https://wikipedia.org/wiki/Corfu_Channel_incident\" title=\"Corfu Channel incident\">Corfu Channel incident</a> is adopted.", "links": [{"title": "United Nations Security Council Resolution 22", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_22"}, {"title": "Corfu Channel incident", "link": "https://wikipedia.org/wiki/Corfu_Channel_incident"}]}, {"year": "1948", "text": "<PERSON>'s assassination provokes a violent riot in Bogotá (the Bogotazo), and a further ten years of violence in Colombia.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9cer_<PERSON>ait%C3%A1n\" title=\"<PERSON>\"><PERSON></a>'s assassination provokes a violent riot in <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a> (the <i><a href=\"https://wikipedia.org/wiki/Bogotazo\" title=\"Bogotazo\">Bogotazo</a></i>), and a further <a href=\"https://wikipedia.org/wiki/La_Violencia\" title=\"La Violencia\">ten years of violence</a> in <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9cer_<PERSON>ait%C3%A1n\" title=\"<PERSON>\"><PERSON></a>'s assassination provokes a violent riot in <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a> (the <i><a href=\"https://wikipedia.org/wiki/Bogotazo\" title=\"Bogotazo\">Bogotazo</a></i>), and a further <a href=\"https://wikipedia.org/wiki/La_Violencia\" title=\"La Violencia\">ten years of violence</a> in <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jorge_Eli%C3%A9cer_Gait%C3%A1n"}, {"title": "Bogotá", "link": "https://wikipedia.org/wiki/Bogot%C3%A1"}, {"title": "Bogotazo", "link": "https://wikipedia.org/wiki/Bogotazo"}, {"title": "La Violencia", "link": "https://wikipedia.org/wiki/La_Violencia"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}]}, {"year": "1948", "text": "Fighters from the Irgun and Lehi Zionist paramilitary groups attacked Deir Yassin near Jerusalem, killing over 100.", "html": "1948 - Fighters from the <a href=\"https://wikipedia.org/wiki/Irgun\" title=\"Irgun\">Irgun</a> and <a href=\"https://wikipedia.org/wiki/Lehi_(militant_group)\" title=\"<PERSON><PERSON> (militant group)\">Lehi</a> <a href=\"https://wikipedia.org/wiki/Zionist\" class=\"mw-redirect\" title=\"Zionist\">Zionist</a> paramilitary groups attacked <a href=\"https://wikipedia.org/wiki/Deir_Yassin\" title=\"Deir Yassin\"><PERSON><PERSON></a> near <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>, <a href=\"https://wikipedia.org/wiki/Deir_Yassin_massacre\" title=\"Deir Yassin massacre\">killing over 100</a>.", "no_year_html": "Fighters from the <a href=\"https://wikipedia.org/wiki/Irgun\" title=\"Irgun\">Irgun</a> and <a href=\"https://wikipedia.org/wiki/Lehi_(militant_group)\" title=\"<PERSON><PERSON> (militant group)\">Lehi</a> <a href=\"https://wikipedia.org/wiki/Zionist\" class=\"mw-redirect\" title=\"Zionist\">Zionist</a> paramilitary groups attacked <a href=\"https://wikipedia.org/wiki/Deir_Yassin\" title=\"Deir Yassin\"><PERSON><PERSON></a> near <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>, <a href=\"https://wikipedia.org/wiki/Deir_Yassin_massacre\" title=\"Deir Yassin massacre\">killing over 100</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irgun"}, {"title": "Lehi (militant group)", "link": "https://wikipedia.org/wiki/Lehi_(militant_group)"}, {"title": "Zionist", "link": "https://wikipedia.org/wiki/Zionist"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}, {"title": "<PERSON><PERSON> massacre", "link": "https://wikipedia.org/wiki/De<PERSON>_<PERSON><PERSON>_massacre"}]}, {"year": "1952", "text": "<PERSON>'s government is overthrown by the Bolivian National Revolution, starting a period of agrarian reform, universal suffrage and the nationalization of tin mines", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>'s government is overthrown by the <a href=\"https://wikipedia.org/wiki/History_of_Bolivia_(1920%E2%80%9364)\" class=\"mw-redirect\" title=\"History of Bolivia (1920-64)\">Bolivian National Revolution</a>, starting a period of <a href=\"https://wikipedia.org/wiki/Agrarian_reform\" class=\"mw-redirect\" title=\"Agrarian reform\">agrarian reform</a>, <a href=\"https://wikipedia.org/wiki/Universal_suffrage\" title=\"Universal suffrage\">universal suffrage</a> and the <a href=\"https://wikipedia.org/wiki/Nationalization\" title=\"Nationalization\">nationalization</a> of tin mines", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>'s government is overthrown by the <a href=\"https://wikipedia.org/wiki/History_of_Bolivia_(1920%E2%80%9364)\" class=\"mw-redirect\" title=\"History of Bolivia (1920-64)\">Bolivian National Revolution</a>, starting a period of <a href=\"https://wikipedia.org/wiki/Agrarian_reform\" class=\"mw-redirect\" title=\"Agrarian reform\">agrarian reform</a>, <a href=\"https://wikipedia.org/wiki/Universal_suffrage\" title=\"Universal suffrage\">universal suffrage</a> and the <a href=\"https://wikipedia.org/wiki/Nationalization\" title=\"Nationalization\">nationalization</a> of tin mines", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ballivi%C3%A1n"}, {"title": "History of Bolivia (1920-64)", "link": "https://wikipedia.org/wiki/History_of_Bolivia_(1920%E2%80%9364)"}, {"title": "Agrarian reform", "link": "https://wikipedia.org/wiki/Agrarian_reform"}, {"title": "Universal suffrage", "link": "https://wikipedia.org/wiki/Universal_suffrage"}, {"title": "Nationalization", "link": "https://wikipedia.org/wiki/Nationalization"}]}, {"year": "1952", "text": "Japan Air Lines Flight 301 crashes into Mount Mihara, Izu Ōshima, Japan, killing 37.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Japan_Air_Lines_Flight_301\" title=\"Japan Air Lines Flight 301\">Japan Air Lines Flight 301</a> crashes into <a href=\"https://wikipedia.org/wiki/Mount_Mihara\" title=\"Mount Mihara\">Mount Mihara</a>, <a href=\"https://wikipedia.org/wiki/Izu_%C5%8Cshima\" title=\"Izu Ōshima\"><PERSON><PERSON></a>, Japan, killing 37.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japan_Air_Lines_Flight_301\" title=\"Japan Air Lines Flight 301\">Japan Air Lines Flight 301</a> crashes into <a href=\"https://wikipedia.org/wiki/Mount_Mihara\" title=\"Mount Mihara\">Mount Mihara</a>, <a href=\"https://wikipedia.org/wiki/Izu_%C5%8Cshima\" title=\"<PERSON><PERSON>shima\"><PERSON><PERSON></a>, Japan, killing 37.", "links": [{"title": "Japan Air Lines Flight 301", "link": "https://wikipedia.org/wiki/Japan_Air_Lines_Flight_301"}, {"title": "Mount Mihara", "link": "https://wikipedia.org/wiki/Mount_Mihara"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Izu_%C5%8Cshima"}]}, {"year": "1957", "text": "The Suez Canal in Egypt is cleared and opens to shipping following the Suez Crisis.", "html": "1957 - The <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> is cleared and opens to shipping following the <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> is cleared and opens to shipping following the <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>.", "links": [{"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}]}, {"year": "1959", "text": "Project Mercury: NASA announces the selection of the United States' first seven astronauts, whom the news media quickly dub the \"Mercury Seven\".", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> announces the selection of the United States' first seven <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronauts</a>, whom the news media quickly dub the \"<a href=\"https://wikipedia.org/wiki/Mercury_Seven\" title=\"Mercury Seven\">Mercury Seven</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> announces the selection of the United States' first seven <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronauts</a>, whom the news media quickly dub the \"<a href=\"https://wikipedia.org/wiki/Mercury_Seven\" title=\"Mercury Seven\">Mercury Seven</a>\".", "links": [{"title": "Project Mercury", "link": "https://wikipedia.org/wiki/Project_Mercury"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}, {"title": "Mercury Seven", "link": "https://wikipedia.org/wiki/Mercury_Seven"}]}, {"year": "1960", "text": "Dr. <PERSON><PERSON><PERSON>, Prime Minister of South Africa and architect of apartheid, narrowly survives an assassination attempt by a white farmer, <PERSON> in Johannesburg.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Dr. <PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> and architect of <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a>, narrowly survives an assassination attempt by a white farmer, <a href=\"https://wikipedia.org/wiki/<PERSON>_(assassin)\" class=\"mw-redirect\" title=\"<PERSON> (assassin)\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Johannesburg\" title=\"Johannesburg\">Johannesburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Dr. <PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> and architect of <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a>, narrowly survives an assassination attempt by a white farmer, <a href=\"https://wikipedia.org/wiki/<PERSON>_(assassin)\" class=\"mw-redirect\" title=\"<PERSON> (assassin)\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Johannesburg\" title=\"Johannesburg\">Johannesburg</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "<PERSON> (assassin)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(assassin)"}, {"title": "Johannesburg", "link": "https://wikipedia.org/wiki/Johannesburg"}]}, {"year": "1967", "text": "The first Boeing 737 (a 100 series) makes its maiden flight.", "html": "1967 - The first <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737</a> (a 100 series) makes its maiden flight.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737</a> (a 100 series) makes its maiden flight.", "links": [{"title": "Boeing 737", "link": "https://wikipedia.org/wiki/Boeing_737"}]}, {"year": "1969", "text": "The first British-built Concorde 002 makes its maiden flight from Filton to RAF Fairford with <PERSON> as the test pilot.", "html": "1969 - The first British-built <i><a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a></i> 002 makes its maiden flight from <a href=\"https://wikipedia.org/wiki/Filton\" title=\"<PERSON>lton\"><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/RAF_Fairford\" title=\"RAF Fairford\">RAF Fairford</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the test pilot.", "no_year_html": "The first British-built <i><a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a></i> 002 makes its maiden flight from <a href=\"https://wikipedia.org/wiki/Filton\" title=\"<PERSON>lton\"><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/RAF_Fairford\" title=\"RAF Fairford\">RAF Fairford</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the test pilot.", "links": [{"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Filton"}, {"title": "RAF Fairford", "link": "https://wikipedia.org/wiki/RAF_Fairford"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "The Iraqi regime of <PERSON> kills philosopher <PERSON> and his sister <PERSON><PERSON> after three days of torture.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraqi</a> regime of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> kills philosopher <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his sister <a href=\"https://wikipedia.org/wiki/Bint_al-Huda\" class=\"mw-redirect\" title=\"<PERSON><PERSON> al-Huda\"><PERSON><PERSON> al-Huda</a> after three days of torture.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraqi</a> regime of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> kills philosopher <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his sister <a href=\"https://wikipedia.org/wiki/Bint_al-Huda\" class=\"mw-redirect\" title=\"<PERSON><PERSON> al-Huda\"><PERSON><PERSON> al-Huda</a> after three days of torture.", "links": [{"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> al-Huda", "link": "https://wikipedia.org/wiki/<PERSON>t_<PERSON>-<PERSON>"}]}, {"year": "1981", "text": "The U.S. Navy nuclear submarine USS <PERSON> accidentally collides with the Nissho Maru, a Japanese cargo ship, sinking it and killing two Japanese sailors.", "html": "1981 - The U.S. Navy nuclear submarine <a href=\"https://wikipedia.org/wiki/USS_George_Washington_(SSBN-598)\" title=\"USS George Washington (SSBN-598)\">USS <i><PERSON></i></a> accidentally collides with the <i><PERSON><PERSON><PERSON></i>, a Japanese cargo ship, sinking it and killing two Japanese sailors.", "no_year_html": "The U.S. Navy nuclear submarine <a href=\"https://wikipedia.org/wiki/USS_George_Washington_(SSBN-598)\" title=\"USS George Washington (SSBN-598)\">USS <i><PERSON></i></a> accidentally collides with the <i><PERSON><PERSON><PERSON></i>, a Japanese cargo ship, sinking it and killing two Japanese sailors.", "links": [{"title": "USS George Washington (SSBN-598)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(SSBN-598)"}]}, {"year": "1989", "text": "Tbilisi massacre: An anti-Soviet peaceful demonstration and hunger strike in Tbilisi, demanding restoration of Georgian independence, is dispersed by the Soviet Army, resulting in 20 deaths and hundreds of injuries.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/April_9_tragedy\" title=\"April 9 tragedy\">Tbilisi massacre</a>: An <a href=\"https://wikipedia.org/wiki/Anti-Sovietism\" title=\"Anti-Sovietism\">anti-Soviet</a> peaceful demonstration and hunger strike in <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a>, demanding restoration of Georgian independence, is <a href=\"https://wikipedia.org/wiki/April_9_tragedy\" title=\"April 9 tragedy\">dispersed by the Soviet Army</a>, resulting in 20 deaths and hundreds of injuries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/April_9_tragedy\" title=\"April 9 tragedy\">Tbilisi massacre</a>: An <a href=\"https://wikipedia.org/wiki/Anti-Sovietism\" title=\"Anti-Sovietism\">anti-Soviet</a> peaceful demonstration and hunger strike in <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a>, demanding restoration of Georgian independence, is <a href=\"https://wikipedia.org/wiki/April_9_tragedy\" title=\"April 9 tragedy\">dispersed by the Soviet Army</a>, resulting in 20 deaths and hundreds of injuries.", "links": [{"title": "April 9 tragedy", "link": "https://wikipedia.org/wiki/April_9_tragedy"}, {"title": "Anti-Sovietism", "link": "https://wikipedia.org/wiki/Anti-Sovietism"}, {"title": "Tbilisi", "link": "https://wikipedia.org/wiki/Tbilisi"}, {"title": "April 9 tragedy", "link": "https://wikipedia.org/wiki/April_9_tragedy"}]}, {"year": "1990", "text": "An IRA bombing in County Down, Northern Ireland, kills three members of the UDR.", "html": "1990 - An <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">IRA</a> bombing in County Down, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, <a href=\"https://wikipedia.org/wiki/1990_Downpatrick_roadside_bomb\" class=\"mw-redirect\" title=\"1990 Downpatrick roadside bomb\">kills</a> three members of the <a href=\"https://wikipedia.org/wiki/Ulster_Defence_Regiment\" title=\"Ulster Defence Regiment\">UDR</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">IRA</a> bombing in County Down, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, <a href=\"https://wikipedia.org/wiki/1990_Downpatrick_roadside_bomb\" class=\"mw-redirect\" title=\"1990 Downpatrick roadside bomb\">kills</a> three members of the <a href=\"https://wikipedia.org/wiki/Ulster_Defence_Regiment\" title=\"Ulster Defence Regiment\">UDR</a>.", "links": [{"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "1990 Downpatrick roadside bomb", "link": "https://wikipedia.org/wiki/1990_Downpatrick_roadside_bomb"}, {"title": "Ulster Defence Regiment", "link": "https://wikipedia.org/wiki/Ulster_Defence_Regiment"}]}, {"year": "1990", "text": "The Sahtu Dene and Metis Comprehensive Land Claim Agreement is signed for 180,000 square kilometres (69,000 sq mi) in the Mackenzie Valley of the western Arctic.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Sahtu_Dene_and_Metis_Comprehensive_Land_Claim_Agreement\" title=\"Sahtu Dene and Metis Comprehensive Land Claim Agreement\">Sahtu Dene and Metis Comprehensive Land Claim Agreement</a> is signed for 180,000 square kilometres (69,000 sq mi) in the <a href=\"https://wikipedia.org/wiki/Mackenzie_Valley\" class=\"mw-redirect\" title=\"Mackenzie Valley\">Mackenzie Valley</a> of the western <a href=\"https://wikipedia.org/wiki/Arctic\" title=\"Arctic\">Arctic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sahtu_Dene_and_Metis_Comprehensive_Land_Claim_Agreement\" title=\"Sahtu Dene and Metis Comprehensive Land Claim Agreement\">Sahtu Dene and Metis Comprehensive Land Claim Agreement</a> is signed for 180,000 square kilometres (69,000 sq mi) in the <a href=\"https://wikipedia.org/wiki/Mackenzie_Valley\" class=\"mw-redirect\" title=\"Mackenzie Valley\">Mackenzie Valley</a> of the western <a href=\"https://wikipedia.org/wiki/Arctic\" title=\"Arctic\">Arctic</a>.", "links": [{"title": "Sahtu Dene and Metis Comprehensive Land Claim Agreement", "link": "https://wikipedia.org/wiki/Saht<PERSON>_<PERSON><PERSON>_and_Metis_Comprehensive_Land_Claim_Agreement"}, {"title": "Mackenzie Valley", "link": "https://wikipedia.org/wiki/Mackenzie_Valley"}, {"title": "Arctic", "link": "https://wikipedia.org/wiki/Arctic"}]}, {"year": "1990", "text": "An Embraer EMB 120 Brasilia collides in mid-air with a Cessna 172 over Gadsden, Alabama, killing both of the Cessna's occupants.", "html": "1990 - An <a href=\"https://wikipedia.org/wiki/Embraer_EMB_120_Brasilia\" title=\"Embraer EMB 120 Brasilia\">Embraer EMB 120 Brasilia</a> <a href=\"https://wikipedia.org/wiki/Atlantic_Southeast_Airlines_Flight_2254\" title=\"Atlantic Southeast Airlines Flight 2254\">collides</a> in mid-air with a <a href=\"https://wikipedia.org/wiki/Cessna_172\" title=\"Cessna 172\">Cessna 172</a> over <a href=\"https://wikipedia.org/wiki/Gadsden,_Alabama\" title=\"Gadsden, Alabama\">Gadsden, Alabama</a>, killing both of the Cessna's occupants.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Embraer_EMB_120_Brasilia\" title=\"Embraer EMB 120 Brasilia\">Embraer EMB 120 Brasilia</a> <a href=\"https://wikipedia.org/wiki/Atlantic_Southeast_Airlines_Flight_2254\" title=\"Atlantic Southeast Airlines Flight 2254\">collides</a> in mid-air with a <a href=\"https://wikipedia.org/wiki/Cessna_172\" title=\"Cessna 172\">Cessna 172</a> over <a href=\"https://wikipedia.org/wiki/Gadsden,_Alabama\" title=\"Gadsden, Alabama\">Gadsden, Alabama</a>, killing both of the Cessna's occupants.", "links": [{"title": "Embraer EMB 120 Brasilia", "link": "https://wikipedia.org/wiki/Embraer_EMB_120_Brasilia"}, {"title": "Atlantic Southeast Airlines Flight 2254", "link": "https://wikipedia.org/wiki/Atlantic_Southeast_Airlines_Flight_2254"}, {"title": "Cessna 172", "link": "https://wikipedia.org/wiki/Cessna_172"}, {"title": "Gadsden, Alabama", "link": "https://wikipedia.org/wiki/Gadsden,_Alabama"}]}, {"year": "1991", "text": "Georgia declares independence from the Soviet Union.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a> declares independence from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a> declares independence from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1992", "text": "A U.S. Federal Court finds former Panamanian dictator <PERSON> guilty of drug and racketeering charges. He is sentenced to 30 years in prison.", "html": "1992 - A <a href=\"https://wikipedia.org/wiki/United_States_federal_courts\" class=\"mw-redirect\" title=\"United States federal courts\">U.S. Federal Court</a> finds former <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panamanian</a> dictator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> guilty of drug and racketeering charges. He is sentenced to 30 years in prison.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_federal_courts\" class=\"mw-redirect\" title=\"United States federal courts\">U.S. Federal Court</a> finds former <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panamanian</a> dictator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> guilty of drug and racketeering charges. He is sentenced to 30 years in prison.", "links": [{"title": "United States federal courts", "link": "https://wikipedia.org/wiki/United_States_federal_courts"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "Space Shuttle program: Space Shuttle Endeavour is launched on STS-59.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-59\" title=\"STS-59\">STS-59</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-59\" title=\"STS-59\">STS-59</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-59", "link": "https://wikipedia.org/wiki/STS-59"}]}, {"year": "2003", "text": "Iraq War: Baghdad falls to American forces.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Baghdad_(2003)\" title=\"Battle of Baghdad (2003)\">Baghdad falls</a> to American forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Baghdad_(2003)\" title=\"Battle of Baghdad (2003)\">Baghdad falls</a> to American forces.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "Battle of Baghdad (2003)", "link": "https://wikipedia.org/wiki/Battle_of_Baghdad_(2003)"}]}, {"year": "2009", "text": "In Tbilisi, Georgia, up to 60,000 people protest against the government of <PERSON><PERSON><PERSON>.", "html": "2009 - In <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a>, <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>, up to 60,000 people <a href=\"https://wikipedia.org/wiki/2009_Georgian_demonstrations\" title=\"2009 Georgian demonstrations\">protest</a> against the government of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a>, <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>, up to 60,000 people <a href=\"https://wikipedia.org/wiki/2009_Georgian_demonstrations\" title=\"2009 Georgian demonstrations\">protest</a> against the government of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Tbilisi", "link": "https://wikipedia.org/wiki/Tbilisi"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "2009 Georgian demonstrations", "link": "https://wikipedia.org/wiki/2009_Georgian_demonstrations"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "A 6.1-magnitude earthquake strikes Iran killing 32 people and injuring over 850 people.", "html": "2013 - A 6.1-magnitude <a href=\"https://wikipedia.org/wiki/2013_Bushehr_earthquake\" title=\"2013 Bushehr earthquake\">earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> killing 32 people and injuring over 850 people.", "no_year_html": "A 6.1-magnitude <a href=\"https://wikipedia.org/wiki/2013_Bushehr_earthquake\" title=\"2013 Bushehr earthquake\">earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> killing 32 people and injuring over 850 people.", "links": [{"title": "2013 Bushehr earthquake", "link": "https://wikipedia.org/wiki/2013_Bushehr_earthquake"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "2013", "text": "At least 13 people are killed and another three injured after a man goes on a spree shooting in the Serbian village of Velika Ivanča.", "html": "2013 - At least 13 people are killed and another three injured after a man goes on a <a href=\"https://wikipedia.org/wiki/Velika_Ivan%C4%8Da_shooting\" title=\"Veli<PERSON> shooting\">spree shooting</a> in the Serbian village of <a href=\"https://wikipedia.org/wiki/Velika_Ivan%C4%8Da\" title=\"Velika <PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "At least 13 people are killed and another three injured after a man goes on a <a href=\"https://wikipedia.org/wiki/Velika_Ivan%C4%8Da_shooting\" title=\"Veli<PERSON> shooting\">spree shooting</a> in the Serbian village of <a href=\"https://wikipedia.org/wiki/Velika_Ivan%C4%8Da\" title=\"Velika <PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON> shooting", "link": "https://wikipedia.org/wiki/Velika_Ivan%C4%8Da_shooting"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Velika_Ivan%C4%8Da"}]}, {"year": "2014", "text": "A student stabs 20 people at Franklin Regional High School in Murrysville, Pennsylvania.", "html": "2014 - A student <a href=\"https://wikipedia.org/wiki/Franklin_Regional_High_School_stabbing\" title=\"Franklin Regional High School stabbing\">stabs</a> 20 people at <a href=\"https://wikipedia.org/wiki/Franklin_Regional_High_School\" title=\"Franklin Regional High School\">Franklin Regional High School</a> in <a href=\"https://wikipedia.org/wiki/Murrysville,_Pennsylvania\" title=\"Murrysville, Pennsylvania\">Murrysville, Pennsylvania</a>.", "no_year_html": "A student <a href=\"https://wikipedia.org/wiki/Franklin_Regional_High_School_stabbing\" title=\"Franklin Regional High School stabbing\">stabs</a> 20 people at <a href=\"https://wikipedia.org/wiki/Franklin_Regional_High_School\" title=\"Franklin Regional High School\">Franklin Regional High School</a> in <a href=\"https://wikipedia.org/wiki/Murrysville,_Pennsylvania\" title=\"Murrysville, Pennsylvania\">Murrysville, Pennsylvania</a>.", "links": [{"title": "Franklin Regional High School stabbing", "link": "https://wikipedia.org/wiki/Franklin_Regional_High_School_stabbing"}, {"title": "Franklin Regional High School", "link": "https://wikipedia.org/wiki/Franklin_Regional_High_School"}, {"title": "Murrysville, Pennsylvania", "link": "https://wikipedia.org/wiki/Murrysville,_Pennsylvania"}]}, {"year": "2017", "text": "The Palm Sunday church bombings at Coptic churches in Tanta and Alexandria, Egypt, take place.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/Palm_Sunday_church_bombings\" title=\"Palm Sunday church bombings\">Palm Sunday church bombings</a> at Coptic churches in <a href=\"https://wikipedia.org/wiki/Tanta\" title=\"Tanta\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>, Egypt, take place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Palm_Sunday_church_bombings\" title=\"Palm Sunday church bombings\">Palm Sunday church bombings</a> at Coptic churches in <a href=\"https://wikipedia.org/wiki/Tanta\" title=\"Tanta\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>, Egypt, take place.", "links": [{"title": "Palm Sunday church bombings", "link": "https://wikipedia.org/wiki/Palm_Sunday_church_bombings"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ta"}, {"title": "Alexandria", "link": "https://wikipedia.org/wiki/Alexandria"}]}, {"year": "2017", "text": "After refusing to give up his seat on an overbooked United Express flight, Dr. <PERSON> is forcibly dragged off the flight by aviation security officers, leading to major criticism of United Airlines.", "html": "2017 - After refusing to give up his seat on an overbooked <a href=\"https://wikipedia.org/wiki/United_Express\" title=\"United Express\">United Express</a> flight, Dr. <PERSON> is <a href=\"https://wikipedia.org/wiki/2017_United_Express_passenger_removal\" title=\"2017 United Express passenger removal\">forcibly dragged off</a> the flight by aviation security officers, leading to major criticism of <a href=\"https://wikipedia.org/wiki/United_Airlines\" title=\"United Airlines\">United Airlines</a>.", "no_year_html": "After refusing to give up his seat on an overbooked <a href=\"https://wikipedia.org/wiki/United_Express\" title=\"United Express\">United Express</a> flight, Dr. <PERSON> is <a href=\"https://wikipedia.org/wiki/2017_United_Express_passenger_removal\" title=\"2017 United Express passenger removal\">forcibly dragged off</a> the flight by aviation security officers, leading to major criticism of <a href=\"https://wikipedia.org/wiki/United_Airlines\" title=\"United Airlines\">United Airlines</a>.", "links": [{"title": "United Express", "link": "https://wikipedia.org/wiki/United_Express"}, {"title": "2017 United Express passenger removal", "link": "https://wikipedia.org/wiki/2017_United_Express_passenger_removal"}, {"title": "United Airlines", "link": "https://wikipedia.org/wiki/United_Airlines"}]}, {"year": "2021", "text": "Burmese military and security forces commit the Bago massacre, during which at least 82 civilians are killed.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Tatmadaw\" title=\"Tatmadaw\">Burmese military</a> and security forces commit the <a href=\"https://wikipedia.org/wiki/Bago_massacre\" title=\"Bago massacre\">Bago massacre</a>, during which at least 82 civilians are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tatmadaw\" title=\"Tatmadaw\">Burmese military</a> and security forces commit the <a href=\"https://wikipedia.org/wiki/Bago_massacre\" title=\"Bago massacre\">Bago massacre</a>, during which at least 82 civilians are killed.", "links": [{"title": "Tatmadaw", "link": "https://wikipedia.org/wiki/Tatmadaw"}, {"title": "Bago massacre", "link": "https://wikipedia.org/wiki/Bago_massacre"}]}], "Births": [{"year": "1096", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, caliph of the Abbasid Caliphate (d. 1160)", "html": "1096 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>Mu<PERSON>\" title=\"Al<PERSON>Muqta<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, caliph of the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON><PERSON> Caliphate</a> (d. 1160)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Muq<PERSON>\" title=\"<PERSON><PERSON><PERSON>q<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, caliph of the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON>id Caliphate</a> (d. 1160)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbasid_Caliphate"}]}, {"year": "1285", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan (d. 1320)", "html": "1285 - <a href=\"https://wikipedia.org/wiki/Ayurb<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan</a> (d. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ayurb<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan</a> (d. 1320)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan"}]}, {"year": "1458", "text": "<PERSON><PERSON>, Italian saint (d. 1524)", "html": "1458 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_Varano\" title=\"<PERSON><PERSON> da Varano\"><PERSON><PERSON> Varano</a>, Italian saint (d. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>arano\" title=\"<PERSON><PERSON> da Varano\"><PERSON><PERSON> Varano</a>, Italian saint (d. 1524)", "links": [{"title": "<PERSON><PERSON> Battista da Varano", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1498", "text": "<PERSON>, <PERSON> of Lorraine (d. 1550)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_of_Lorraine\" title=\"<PERSON>, <PERSON> of Lorraine\"><PERSON>, <PERSON> of Lorraine</a> (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_of_Lorraine\" title=\"<PERSON>, <PERSON> of Lorraine\"><PERSON>, <PERSON> of Lorraine</a> (d. 1550)", "links": [{"title": "<PERSON>, <PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_Lorraine"}]}, {"year": "1586", "text": "<PERSON>, Duke of Saxe-Lauenburg (d. 1665)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg\" title=\"<PERSON>, Duke of Saxe-Lauenburg\"><PERSON>, Duke of Saxe-Lauenburg</a> (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg\" title=\"<PERSON>, Duke of Saxe-Lauenburg\"><PERSON>, Duke of Saxe-Lauenburg</a> (d. 1665)", "links": [{"title": "<PERSON>, Duke of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg"}]}, {"year": "1597", "text": "<PERSON>, English minister, co-founded the New Haven Colony (d. 1670)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English minister, co-founded the <a href=\"https://wikipedia.org/wiki/New_Haven_Colony\" title=\"New Haven Colony\">New Haven Colony</a> (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English minister, co-founded the <a href=\"https://wikipedia.org/wiki/New_Haven_Colony\" title=\"New Haven Colony\">New Haven Colony</a> (d. 1670)", "links": [{"title": "<PERSON> (minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)"}, {"title": "New Haven Colony", "link": "https://wikipedia.org/wiki/New_Haven_Colony"}]}, {"year": "1598", "text": "<PERSON>, Sorbian-German composer and theorist (d. 1662)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCger\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sorbs\" title=\"Sorbs\">Sorbian</a>-German composer and theorist (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>ger\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sorbs\" title=\"Sorbs\">Sorbian</a>-German composer and theorist (d. 1662)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johann_<PERSON>r%C3%BCger"}, {"title": "Sorbs", "link": "https://wikipedia.org/wiki/Sorbs"}]}, {"year": "1624", "text": "<PERSON>, Dutch military engineer (d. 1679)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch military engineer (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch military engineer (d. 1679)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1627", "text": "<PERSON>, German organist and composer (d. 1693)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>l"}]}, {"year": "1634", "text": "Countess <PERSON><PERSON> of Nassau (d. 1696)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_<PERSON>_Nassau\" class=\"mw-redirect\" title=\"Countess <PERSON><PERSON> of Nassau\">Countess <PERSON><PERSON> Nassau</a> (d. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_of_Nassau\" class=\"mw-redirect\" title=\"Countess <PERSON><PERSON> of Nassau\">Countess <PERSON><PERSON> of Nassau</a> (d. 1696)", "links": [{"title": "Countess <PERSON><PERSON> of Nassau", "link": "https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_<PERSON>_Nassau"}]}, {"year": "1648", "text": "<PERSON>, Earl of Galway, French soldier and diplomat (d. 1720)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Galway\" title=\"<PERSON>, Earl of Galway\"><PERSON>, Earl of Galway</a>, French soldier and diplomat (d. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Galway\" title=\"<PERSON>, Earl of Galway\"><PERSON>, Earl of Galway</a>, French soldier and diplomat (d. 1720)", "links": [{"title": "<PERSON>, Earl of Galway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Earl_of_Galway"}]}, {"year": "1649", "text": "<PERSON>, 1st Duke of Monmouth, English general and politician, Lord Lieutenant of Staffordshire (d. 1685)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Monmouth\" title=\"<PERSON>, 1st Duke of Monmouth\"><PERSON>, 1st Duke of Monmouth</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Staffordshire\" title=\"Lord Lieutenant of Staffordshire\">Lord Lieutenant of Staffordshire</a> (d. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Monmouth\" title=\"<PERSON>, 1st Duke of Monmouth\"><PERSON>, 1st Duke of Monmouth</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Staffordshire\" title=\"Lord Lieutenant of Staffordshire\">Lord Lieutenant of Staffordshire</a> (d. 1685)", "links": [{"title": "<PERSON>, 1st Duke of Monmouth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Monmouth"}, {"title": "Lord Lieutenant of Staffordshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Staffordshire"}]}, {"year": "1654", "text": "<PERSON>, Czech Jesuit missionary to South America (d. 1725?)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech Jesuit missionary to South America (d. 1725?)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech Jesuit missionary to South America (d. 1725?)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, French playwright (d. 1754)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ricault_Destouches\" title=\"<PERSON>\"><PERSON></a>, French playwright (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ricault_Destouches\" title=\"<PERSON>\"><PERSON></a>, French playwright (d. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Philippe_N%C3%A9<PERSON>ult_Destouches"}]}, {"year": "1686", "text": "<PERSON> the Younger, English politician, Secretary of State for the Southern Department (d. 1721)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (d. 1721)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1691", "text": "<PERSON>, German scholar and academic (d. 1761)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and academic (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and academic (d. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, Austrian organist, composer, and educator (d. 1750)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist, composer, and educator (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist, composer, and educator (d. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, German physicist and academic (d. 1831)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, French author and academic (d. 1824)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and academic (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and academic (d. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_Aignan"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON>, German flute player and composer (d. 1881)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German flute player and composer (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German flute player and composer (d. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON>, Norwegian priest and botanist (d. 1838)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>_<PERSON>mmerfelt\" title=\"<PERSON><PERSON><PERSON>mmerfelt\"><PERSON><PERSON><PERSON></a>, Norwegian priest and botanist (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>_<PERSON>mmerfelt\" title=\"<PERSON><PERSON><PERSON>mmerfelt\"><PERSON><PERSON><PERSON></a>, Norwegian priest and botanist (d. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>_<PERSON>lt"}]}, {"year": "1802", "text": "<PERSON>, Finnish physician and philologist (d. 1884)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6nnrot\" title=\"<PERSON>\"><PERSON></a>, Finnish physician and philologist (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6nnrot\" title=\"<PERSON>\"><PERSON></a>, Finnish physician and philologist (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elias_L%C3%B6nnrot"}]}, {"year": "1806", "text": "Isambard <PERSON> B<PERSON>, English engineer, designed the Clifton Suspension Bridge (d. 1859)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Isambard_Kingdom_Brunel\" title=\"Isambard Kingdom Brunel\">Isambard Kingdom Brunel</a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Clifton_Suspension_Bridge\" title=\"Clifton Suspension Bridge\">Clifton Suspension Bridge</a> (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isambard_Kingdom_Brunel\" title=\"Isambard Kingdom Brunel\">Isambard Kingdom Brunel</a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Clifton_Suspension_Bridge\" title=\"Clifton Suspension Bridge\">Clifton Suspension Bridge</a> (d. 1859)", "links": [{"title": "Isambard Kingdom Brunel", "link": "https://wikipedia.org/wiki/Isambard_Kingdom_Brunel"}, {"title": "Clifton Suspension Bridge", "link": "https://wikipedia.org/wiki/Clifton_Suspension_Bridge"}]}, {"year": "1807", "text": "<PERSON>, Scottish theologian and academic (d. 1868)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, Scottish theologian and academic (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, Scottish theologian and academic (d. 1868)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)"}]}, {"year": "1821", "text": "<PERSON>, French poet and critic (d. 1867)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON><PERSON>, English photographer and cinematographer (d. 1904)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English photographer and cinematographer (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English photographer and cinematographer (d. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON> of Belgium (d. 1909)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Leopold_II_of_Belgium\" title=\"Leopold II of Belgium\">Leopold II of Belgium</a> (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopold_II_of_Belgium\" title=\"Leopold II of Belgium\"><PERSON> II of Belgium</a> (d. 1909)", "links": [{"title": "Leopold II of Belgium", "link": "https://wikipedia.org/wiki/Leopold_II_of_Belgium"}]}, {"year": "1835", "text": "<PERSON> Lowry-<PERSON><PERSON>, 4th Earl <PERSON> (d. 1913)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Somerset_Lowry-<PERSON>,_4th_Earl_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th Earl <PERSON></a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Somerset_Lowry-<PERSON>,_4th_Earl_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th <PERSON></a> (d. 1913)", "links": [{"title": "Somerset Lowry-<PERSON><PERSON>, 4th Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>,_4th_Earl_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Italian-English composer and educator (d. 1916)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English composer and educator (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English composer and educator (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Spanish Augustinian Recollect priest and saint (d. 1906)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Ezequi%C3%A9l_<PERSON>_y_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> y <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Spanish Augustinian Recollect priest and saint (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ezequi%C3%A9l_<PERSON>_y_D%C3%ADaz\" title=\"Ezequi<PERSON><PERSON> y <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Spanish Augustinian Recollect priest and saint (d. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> y <PERSON>", "link": "https://wikipedia.org/wiki/Ezequi%C3%A9l_<PERSON>_y_D%C3%ADaz"}]}, {"year": "1865", "text": "<PERSON>, German general and politician (d. 1937)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and politician (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and politician (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Polish-American mathematician and engineer (d. 1923)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American mathematician and engineer (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American mathematician and engineer (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Chilean-Australian journalist and politician, 3rd Prime Minister of Australia (d. 1941)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-Australian journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-Australian journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1867", "text": "<PERSON>, Danish tug of war competitor, discus thrower, and shot putter (d. 1932)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor, discus thrower, and shot putter (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor, discus thrower, and shot putter (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1872", "text": "<PERSON>, French lawyer and politician, Prime Minister of France (d. 1950)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_Blu<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Blum"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1875", "text": "<PERSON>, American journalist and author (d. 1912)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Czech architect (d. 1925)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech architect (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech architect (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Grand Duke of Mecklenburg-Schwerin (d. 1946)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Schwerin\" class=\"mw-redirect\" title=\"<PERSON>, Grand Duke of Mecklenburg-Schwerin\"><PERSON>, Grand Duke of Mecklenburg-Schwerin</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Schwerin\" class=\"mw-redirect\" title=\"<PERSON>, Grand Duke of Mecklenburg-Schwerin\"><PERSON>, Grand Duke of Mecklenburg-Schwerin</a> (d. 1946)", "links": [{"title": "<PERSON>, Grand Duke of Mecklenburg-Schwerin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Schwerin"}]}, {"year": "1882", "text": "<PERSON><PERSON>, German actor (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>llen"}]}, {"year": "1883", "text": "<PERSON>, American cartoonist (d. 1969)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (d. 1969)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)"}]}, {"year": "1887", "text": "<PERSON>, Polish actor, writer, singer, and director (d. 1957)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish actor, writer, singer, and director (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Konrad Tom\"><PERSON></a>, Polish actor, writer, singer, and director (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Ukrainian-American talent manager (d. 1974)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hurok\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American talent manager (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sol_Hurok\" title=\"Sol Hurok\"><PERSON></a>, Ukrainian-American talent manager (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sol_<PERSON>rok"}]}, {"year": "1893", "text": "<PERSON>, American painter (d. 1967)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English publisher, founded Victor Gollancz Ltd (d. 1967)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher, founded <a href=\"https://wikipedia.org/wiki/Victor_<PERSON>_Ltd\" title=\"Victor Gollancz Ltd\">Victor Go<PERSON> Ltd</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher, founded <a href=\"https://wikipedia.org/wiki/Victor_<PERSON>_Ltd\" title=\"Victor Gollancz Ltd\">Victor Go<PERSON> Ltd</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victor Gollancz Ltd", "link": "https://wikipedia.org/wiki/Victor_Gollancz_Ltd"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Indian linguist, author, and scholar (d. 1963)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian linguist, author, and scholar (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian linguist, author, and scholar (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 1976)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Swiss-French actor (d. 1975)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French actor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French actor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American radio host (d. 1974)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Gambling\" title=\"<PERSON> Gam<PERSON>\"><PERSON></a>, American radio host (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ambling\" title=\"<PERSON> Gambling\"><PERSON></a>, American radio host (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bling"}]}, {"year": "1898", "text": "<PERSON><PERSON>, American football player and coach (d. 1965)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (d. 1965)", "links": [{"title": "Curly <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American singer, actor, and activist (d. 1976)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and activist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and activist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American actor and singer (d. 1974)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Canadian historian and author (d. 1979)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9si\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and author (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9si\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and author (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9si"}]}, {"year": "1901", "text": "<PERSON>, American actor and director (d. 1960)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 1960)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French explorer and scholar (d. 2000)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9odore_Monod\" title=\"Théod<PERSON> Monod\">T<PERSON><PERSON><PERSON><PERSON></a>, French explorer and scholar (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9odore_Monod\" title=\"Théod<PERSON> Monod\">T<PERSON><PERSON><PERSON><PERSON></a>, French explorer and scholar (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9odore_Monod"}]}, {"year": "1903", "text": "<PERSON>, American actor (d. 1960)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bond\" title=\"<PERSON> Bond\"><PERSON></a>, American actor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ward_Bond"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American singer, trumpet player, and bandleader (d. 1972)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, trumpet player, and bandleader (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, trumpet player, and bandleader (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, American lawyer and politician (d. 1995)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Spanish actress (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Hungarian-American conductor and composer (d. 1988)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Antal_Dor%C3%<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American conductor and composer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An<PERSON>_<PERSON>r%C3%<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American conductor and composer (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antal_Dor%C3%A1ti"}]}, {"year": "1906", "text": "<PERSON>, British politician and leader of the Labour Party (d. 1963)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician and leader of the Labour Party (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician and leader of the Labour Party (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Hungarian-French painter (d. 1997)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-French painter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-French painter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vasarely"}]}, {"year": "1908", "text": "<PERSON>, American author and screenwriter (d. 1980)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, French composer, pianist and lyricist (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, pianist and lyricist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, pianist and lyricist (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Australian dancer, actor, and choreographer (d. 1986)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian dancer, actor, and choreographer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian dancer, actor, and choreographer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American lawyer and politician, 4th United States Secretary of Health and Human Services (d. 1998)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of Health and Human Services", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services"}]}, {"year": "1912", "text": "<PERSON>, Ukrainian-German author and academic (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German author and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German author and academic (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Canadian lawyer and politician, 20th Premier of Quebec (d. 1968)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1968)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1916", "text": "<PERSON>, American swing music jazz tenor saxophonist (d. 1974)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swing music jazz tenor saxophonist (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swing music jazz tenor saxophonist (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, <PERSON> (paratrooper) during World War II (d. 1987)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (paratrooper) during World War II (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (paratrooper) during World War II (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American journalist (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (d. 1994)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "1917", "text": "<PERSON>, German songwriter and poet (d. 1965)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German songwriter and poet (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German songwriter and poet (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Welsh international footballer and manager (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh international footballer and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh international footballer and manager (d. 2005)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1917", "text": "<PERSON>, American actor (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American theater writer (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)\" title=\"<PERSON> (critic)\"><PERSON></a>, American theater writer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)\" title=\"<PERSON> (critic)\"><PERSON></a>, American theater writer (d. 2006)", "links": [{"title": "<PERSON> (critic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Danish architect, designed the Sydney Opera House (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish architect, designed the <a href=\"https://wikipedia.org/wiki/Sydney_Opera_House\" title=\"Sydney Opera House\">Sydney Opera House</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish architect, designed the <a href=\"https://wikipedia.org/wiki/Sydney_Opera_House\" title=\"Sydney Opera House\">Sydney Opera House</a> (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B8rn_<PERSON><PERSON>on"}, {"title": "Sydney Opera House", "link": "https://wikipedia.org/wiki/Sydney_Opera_House"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American engineer, invented the ENIAC (d. 1995)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/ENIAC\" title=\"ENIAC\">ENIAC</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/ENIAC\" title=\"ENIAC\">ENIAC</a> (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "ENIAC", "link": "https://wikipedia.org/wiki/ENIAC"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, French businessman (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French businessman (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Israeli politician (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli politician (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, African-American mathematician and aerospace engineer (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, African-American mathematician and aerospace engineer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, African-American mathematician and aerospace engineer (d. 2005)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)"}]}, {"year": "1922", "text": "<PERSON>, German author and activist (d. 2005)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and activist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and activist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American historian and author (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English professional footballer (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1924)\" title=\"<PERSON> (footballer, born 1924)\"><PERSON></a>, English professional footballer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1924)\" title=\"<PERSON> (footballer, born 1924)\"><PERSON></a>, English professional footballer (d. 2015)", "links": [{"title": "<PERSON> (footballer, born 1924)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1924)"}]}, {"year": "1925", "text": "<PERSON>, American actress, singer, and dancer (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Virginia_Gibson\" title=\"Virginia Gibson\"><PERSON></a>, American actress, singer, and dancer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Gibson\" title=\"Virginia Gibson\"><PERSON></a>, American actress, singer, and dancer (d. 2013)", "links": [{"title": "Virginia Gibson", "link": "https://wikipedia.org/wiki/Virginia_Gibson"}]}, {"year": "1925", "text": "<PERSON>, American photographer (d. 1995)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Kane\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Kane\" title=\"<PERSON> Kane\"><PERSON></a>, American photographer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kane"}]}, {"year": "1926", "text": "<PERSON>, Northern Irish soldier and politician; British life peer (d. 2005)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish soldier and politician; British life peer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish soldier and politician; British life peer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American publisher, founded Playboy Enterprises (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/Playboy_Enterprises\" class=\"mw-redirect\" title=\"Playboy Enterprises\">Playboy Enterprises</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/Playboy_Enterprises\" class=\"mw-redirect\" title=\"Playboy Enterprises\">Playboy Enterprises</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Playboy Enterprises", "link": "https://wikipedia.org/wiki/Playboy_Enterprises"}]}, {"year": "1926", "text": "<PERSON>, American politician, author, and civil rights activist (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, author, and civil rights activist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, author, and civil rights activist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, New Zealand rugby player (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 2019)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_Hill_(rugby_union)"}]}, {"year": "1928", "text": "<PERSON>, American basketball player (d. 2006)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter, pianist, and mathematician", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Indian sarod player and scholar (d. 2008)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>n <PERSON>\"><PERSON>haran <PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Sarod\" title=\"<PERSON>rod\">sarod</a> player and scholar (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON>haran <PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Sarod\" title=\"<PERSON>rod\">sarod</a> player and scholar (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rod"}]}, {"year": "1929", "text": "<PERSON>, New Zealand-Australian ophthalmologist (d. 1993)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fred <PERSON>\"><PERSON></a>, New Zealand-Australian ophthalmologist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Fred Hollows\"><PERSON></a>, New Zealand-Australian ophthalmologist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American author and academic (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and academic (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian-American psychotherapist and author (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychotherapist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychotherapist and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American chemist and academic (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American chemist and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American chemist and academic (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American zoologist and television host (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist and television host (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist and television host (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian businessman, founded McCain Foods (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/McCain_Foods\" title=\"McCain Foods\">McCain Foods</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/McCain_Foods\" title=\"McCain Foods\">McCain Foods</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McCain Foods", "link": "https://wikipedia.org/wiki/McCain_Foods"}]}, {"year": "1931", "text": "<PERSON>, Canadian lawyer and politician, 26th Premier of New Brunswick (d. 1991)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New Brunswick", "link": "https://wikipedia.org/wiki/Premier_of_New_Brunswick"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Swiss conductor (d. 2006)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss conductor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss conductor (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>in_Jordan"}]}, {"year": "1932", "text": "<PERSON>, English businessman and philanthropist (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman and philanthropist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman and philanthropist (d. 2016)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_(businessman)"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1998)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, French actor and producer (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and producer (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Swiss photographer and journalist (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss photographer and journalist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss photographer and journalist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>i"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American author", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American political scientist and academic", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_scientist)\" title=\"<PERSON> (political scientist)\"><PERSON></a>, American political scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_scientist)\" title=\"<PERSON> (political scientist)\"><PERSON></a>, American political scientist and academic", "links": [{"title": "<PERSON> (political scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_scientist)"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Italian actor (d. 1994)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Volont%C3%A9"}]}, {"year": "1934", "text": "<PERSON>, New Zealand surveyor and politician, 38th New Zealand Minister of Finance", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand surveyor and politician, 38th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(New_Zealand)\" title=\"Minister of Finance (New Zealand)\">New Zealand Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand surveyor and politician, 38th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(New_Zealand)\" title=\"Minister of Finance (New Zealand)\">New Zealand Minister of Finance</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Finance (New Zealand)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(New_Zealand)"}]}, {"year": "1934", "text": "<PERSON>, Australian motorcycle racer (d. 1962)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Russian high jumper (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian high jumper (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian high jumper (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Finnish composer and academic", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1935", "text": "<PERSON>, American actor and comedian (d. 2002)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hreiber"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Polish pianist, composer, and conductor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pianist, composer, and conductor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American LGBT rights activist from Missouri (d. 1989)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT rights activist from Missouri (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT rights activist from Missouri (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American radical feminist author, attempted murderer (d. 1988)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radical feminist author, attempted murderer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radical feminist author, attempted murderer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Baron <PERSON> of Eaton-under-Heywood, English lieutenant, lawyer, and judge (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Eaton-under-Heywood\" title=\"<PERSON>, Baron <PERSON> of Eaton-under-Heywood\"><PERSON>, Baron <PERSON> of Eaton-under-Heywood</a>, English lieutenant, lawyer, and judge (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Eaton-under-Heywood\" title=\"<PERSON>, Baron <PERSON> of Eaton-under-Heywood\"><PERSON>, Baron <PERSON> of Eaton-under-Heywood</a>, English lieutenant, lawyer, and judge (d. 2023)", "links": [{"title": "<PERSON>, <PERSON> of Eaton-under-Heywood", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Eaton-under-Heywood"}]}, {"year": "1937", "text": "<PERSON>, Canadian screenwriter and producer (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, Canadian screenwriter and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, Canadian screenwriter and producer (d. 2023)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English television and radio host", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Russian businessman and politician, 30th Prime Minister of Russia (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman and politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman and politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Russia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Russia"}]}, {"year": "1939", "text": "<PERSON>, American actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American singer-songwriter (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, German sprinter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian-American ice hockey player and coach (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1940)\" title=\"<PERSON> (ice hockey, born 1940)\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1940)\" title=\"<PERSON> (ice hockey, born 1940)\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 2015)", "links": [{"title": "<PERSON> (ice hockey, born 1940)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1940)"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1941", "text": "<PERSON>, Scottish actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor (d. 1972)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> deW<PERSON>\"><PERSON></a>, American actor (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> de<PERSON>\"><PERSON></a>, American actor (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Palestinian activist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and producer (d. 2004)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Welsh rugby league player (d. 1985)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby league player (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby league player (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American baseball player and umpire", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, German shot putter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German shot putter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American drummer and percussionist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and percussionist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and percussionist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American baseball player (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English cricketer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish activist and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish activist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_<PERSON>in"}]}, {"year": "1946", "text": "<PERSON>, English footballer, coach, and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer, coach, and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1947", "text": "<PERSON>, Italian economist and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Indian actress and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actress and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actress and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Puerto Rican salsa singer (d. 2007)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3me<PERSON>_(Puerto_Rican_singer)\" title=\"<PERSON> (Puerto Rican singer)\"><PERSON></a>, Puerto Rican salsa singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3<PERSON><PERSON>_(Puerto_Rican_singer)\" title=\"<PERSON> (Puerto Rican singer)\"><PERSON></a>, Puerto Rican salsa singer (d. 2007)", "links": [{"title": "<PERSON> (Puerto Rican singer)", "link": "https://wikipedia.org/wiki/Tito_<PERSON>%C3%B3me<PERSON>_(Puerto_Rican_singer)"}]}, {"year": "1948", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Italian singer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pravo\"><PERSON></a>, Italian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Pravo\"><PERSON></a>, Italian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English sculptor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1952", "text": "<PERSON>, New Zealand rugby player (d. 2023)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" class=\"mw-redirect\" title=\"<PERSON> (rugby)\"><PERSON></a>, New Zealand rugby player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" class=\"mw-redirect\" title=\"<PERSON> (rugby)\"><PERSON></a>, New Zealand rugby player (d. 2023)", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Greek singer and actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English singer-songwriter and pianist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer-songwriter)\" title=\"<PERSON> (singer-songwriter)\"><PERSON></a>, English singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer-songwriter)\" title=\"<PERSON> (singer-songwriter)\"><PERSON></a>, English singer-songwriter and pianist", "links": [{"title": "<PERSON> (singer-songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>(singer-songwriter)"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2020)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American mass murderer responsible for the 2017 Las Vegas shooting (d. 2017)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer responsible for the <a href=\"https://wikipedia.org/wiki/2017_Las_Vegas_shooting\" title=\"2017 Las Vegas shooting\">2017 Las Vegas shooting</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer responsible for the <a href=\"https://wikipedia.org/wiki/2017_Las_Vegas_shooting\" title=\"2017 Las Vegas shooting\">2017 Las Vegas shooting</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "2017 Las Vegas shooting", "link": "https://wikipedia.org/wiki/2017_Las_Vegas_shooting"}]}, {"year": "1954", "text": "<PERSON>, American journalist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, British soldier and politician, Secretary of State for Work and Pensions", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions\" title=\"Secretary of State for Work and Pensions\">Secretary of State for Work and Pensions</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions\" title=\"Secretary of State for Work and Pensions\">Secretary of State for Work and Pensions</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Secretary of State for Work and Pensions", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Algerian-French director and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"Ya<PERSON> Benguig<PERSON>\"><PERSON><PERSON></a>, Algerian-French director and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"Ya<PERSON> Benguig<PERSON>\"><PERSON><PERSON></a>, Algerian-French director and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, English poet and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Argentinian footballer and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English computer scientist and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Russian ice dancer and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Spanish golfer and architect (d. 2011)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish golfer and architect (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish golfer and architect (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Belgian fashion designer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English-born Australian television presenter and pop singer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Australian television presenter and pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Australian television presenter and pop singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, British-Lebanese surgeon and sculptor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British-Lebanese surgeon and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British-Lebanese surgeon and sculptor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English boxer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English food writer and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English food writer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English food writer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English businessman and politician, Shadow Secretary of State for Defence", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Estonian educator and politician, Minister of Social Affairs of Estonia", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian educator and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_of_Estonia\" class=\"mw-redirect\" title=\"Minister of Social Affairs of Estonia\">Minister of Social Affairs of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian educator and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_of_Estonia\" class=\"mw-redirect\" title=\"Minister of Social Affairs of Estonia\">Minister of Social Affairs of Estonia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaak_<PERSON>ab"}, {"title": "Minister of Social Affairs of Estonia", "link": "https://wikipedia.org/wiki/Minister_of_Social_Affairs_of_Estonia"}]}, {"year": "1961", "text": "<PERSON>, Irish keyboard player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(keyboardist)\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, Irish keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(keyboardist)\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, Irish keyboard player", "links": [{"title": "<PERSON> (keyboardist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(keyboardist)"}]}, {"year": "1961", "text": "<PERSON>, Canadian-American baseball and hockey player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball and hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball and hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American production designer and illustrator", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American production designer and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American production designer and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Ukrainian director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Podolchak"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, English field hockey player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English field hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player, coach, and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American-French fashion designer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American journalist, lawyer, and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German-American football player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Jamaican sprinter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American politician and businessman, 23rd Governor of Arizona", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and businessman, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_Arizona\" class=\"mw-redirect\" title=\"Governor of Arizona\">Governor of Arizona</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and businessman, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_Arizona\" class=\"mw-redirect\" title=\"Governor of Arizona\">Governor of Arizona</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Arizona", "link": "https://wikipedia.org/wiki/Governor_of_Arizona"}]}, {"year": "1964", "text": "<PERSON>, Canadian businessman and politician, 9th Canadian Minister of Intergovernmental Affairs", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 9th <a href=\"https://wikipedia.org/wiki/Minister_of_Intergovernmental_Affairs_(Canada)\" class=\"mw-redirect\" title=\"Minister of Intergovernmental Affairs (Canada)\">Canadian Minister of Intergovernmental Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 9th <a href=\"https://wikipedia.org/wiki/Minister_of_Intergovernmental_Affairs_(Canada)\" class=\"mw-redirect\" title=\"Minister of Intergovernmental Affairs (Canada)\">Canadian Minister of Intergovernmental Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Intergovernmental Affairs (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Intergovernmental_Affairs_(Canada)"}]}, {"year": "1964", "text": "<PERSON>, American author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian-American ice hockey player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Swedish golfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Czech-born Swedish-American model and actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-born Swedish-American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-born Swedish-American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American businessman", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English weather forecaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weather_forecaster)\" title=\"<PERSON> (weather forecaster)\"><PERSON></a>, English weather forecaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weather_forecaster)\" title=\"<PERSON> (weather forecaster)\"><PERSON></a>, English weather forecaster", "links": [{"title": "<PERSON> (weather forecaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weather_forecaster)"}]}, {"year": "1966", "text": "<PERSON>, American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, German-English translator and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Natas<PERSON>gel\"><PERSON><PERSON><PERSON></a>, German-English translator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Natas<PERSON> Engel\"><PERSON><PERSON><PERSON></a>, German-English translator and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Engel"}]}, {"year": "1967", "text": "<PERSON>, American author, philosopher, and neuroscientist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, philosopher, and neuroscientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, philosopher, and neuroscientist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor, comedian, writer and director", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, writer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, writer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, English actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German runner", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Brazilian singer-songwriter (d. 2013)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Chor%C3%A3o\" title=\"Chor<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chor%C3%A3o\" title=\"Chor<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter (d. 2013)", "links": [{"title": "<PERSON>r<PERSON>", "link": "https://wikipedia.org/wiki/Chor%C3%A3o"}]}, {"year": "1971", "text": "<PERSON>, Irish footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Leo_Fortune-West\" title=\"Leo Fortune-West\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leo_Fortune-West\" title=\"Leo Fortune-West\"><PERSON></a>, English footballer and manager", "links": [{"title": "Leo Fortune-West", "link": "https://wikipedia.org/wiki/Leo_Fortune-West"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Peck"}]}, {"year": "1971", "text": "<PERSON>, Canadian race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, German-Japanese martial artist and kick-boxer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Japanese martial artist and kick-boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Japanese martial artist and kick-boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Estonian architect", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian actress (d. 2001)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Australian actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Australian actress (d. 2001)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1974", "text": "<PERSON>, American actress and pornographic performer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and pornographic performer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and pornographic performer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American director and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter and comic book writer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gerard Way\"><PERSON></a>, American singer-songwriter and comic book writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gerard Way\"><PERSON></a>, American singer-songwriter and comic book writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gerard_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Japanese actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Portuguese footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English singer-songwriter, dancer, and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ull<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pulliam\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pulliam\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English sailor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Argentinian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1981", "text": "<PERSON>, Slovak ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Milan_Bartovi%C4%8D\" title=\"<PERSON>č\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Bartovi%C4%8D\" title=\"<PERSON> Bart<PERSON>č\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_Bartovi%C4%8D"}]}, {"year": "1981", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Je<PERSON>%C5%84"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American mass murderer, responsible for the Columbine High School massacre  (d. 1999)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American mass murderer, responsible for the <a href=\"https://wikipedia.org/wiki/Columbine_High_School_massacre\" title=\"Columbine High School massacre\">Columbine High School massacre</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American mass murderer, responsible for the <a href=\"https://wikipedia.org/wiki/Columbine_High_School_massacre\" title=\"Columbine High School massacre\">Columbine High School massacre</a> (d. 1999)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "Columbine High School massacre", "link": "https://wikipedia.org/wiki/Columbine_High_School_massacre"}]}, {"year": "1982", "text": "<PERSON>, Canadian actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Costa Rican footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1982)\" title=\"<PERSON> (footballer, born 1982)\"><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1982)\" title=\"<PERSON> (footballer, born 1982)\"><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON> (footballer, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, Canadian-American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Tunisian runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Habiba_Ghribi\" title=\"Habiba Ghribi\">Habi<PERSON> Ghribi</a>, Tunisian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Habiba_Ghribi\" title=\"Habiba Ghribi\">Habi<PERSON> Ghribi</a>, Tunisian runner", "links": [{"title": "Habiba Ghribi", "link": "https://wikipedia.org/wiki/Habiba_Ghribi"}]}, {"year": "1984", "text": "<PERSON>, Canadian baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_<PERSON><PERSON>\" title=\"Ósca<PERSON> Razo\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_<PERSON><PERSON>\" title=\"<PERSON>sca<PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_<PERSON>zo"}]}, {"year": "1985", "text": "<PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_(baseball)"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Leigh<PERSON>_Meester\" title=\"<PERSON><PERSON> Meester\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leighton_Meester\" title=\"<PERSON><PERSON> Meester\"><PERSON><PERSON></a>, American actress", "links": [{"title": "Leighton Meester", "link": "https://wikipedia.org/wiki/Leighton_Meester"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, French-Comorian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Comorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Comorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter and actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "Jazmine Sullivan", "link": "https://wikipedia.org/wiki/Jazmine_Sullivan"}]}, {"year": "1988", "text": "<PERSON>, English race car driver", "html": "1988 - <a href=\"https://wikipedia.org/wiki/1988\" title=\"1988\">1988</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1988\" title=\"1988\">1988</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "1988", "link": "https://wikipedia.org/wiki/1988"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American wrestler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American figure skater", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>in"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1991", "text": "<PERSON>, American synchronized swimmer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American synchronized swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American synchronized swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American politician", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Croatian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Domagoj_Bo%C5%A1njak\" title=\"<PERSON><PERSON>j Bošnjak\"><PERSON><PERSON><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Domagoj_Bo%C5%A1njak\" title=\"Domagoj Bošnjak\"><PERSON><PERSON><PERSON></a>, Croatian basketball player", "links": [{"title": "Domagoj <PERSON>", "link": "https://wikipedia.org/wiki/Domagoj_Bo%C5%A1njak"}]}, {"year": "1995", "text": "<PERSON>, German-Kazakhstani footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German-Kazakhstani footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German-Kazakhstani footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Dutch Paralympic equestrian", "html": "1995 - <a href=\"https://wikipedia.org/wiki/De<PERSON>_Vermeulen\" title=\"<PERSON><PERSON> Vermeulen\"><PERSON><PERSON></a>, Dutch Paralympic equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON>_Vermeulen\" title=\"De<PERSON> Vermeulen\"><PERSON><PERSON></a>, Dutch Paralympic equestrian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON>_Vermeulen"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brailey\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brailey\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Argentinian international footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>iovani <PERSON>lso\"><PERSON><PERSON><PERSON></a>, Argentinian international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian international footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lo_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Venezuelan baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rr%C3%A1ez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rr%C3%A1ez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Arr%C3%A1ez"}]}, {"year": "1998", "text": "<PERSON>, American actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fanning\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fanning\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> Fanning", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ning"}]}, {"year": "1999", "text": "<PERSON>, American rapper", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Lil_Nas_X\" title=\"Lil Nas X\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lil_Nas_X\" title=\"Lil Nas X\"><PERSON></a>, American rapper", "links": [{"title": "Lil Nas X", "link": "https://wikipedia.org/wiki/<PERSON>_Na<PERSON>_X"}]}, {"year": "2000", "text": "<PERSON>, American singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, British YouTuber and Twitch streamer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/TommyInnit\" title=\"TommyInnit\"><PERSON>I<PERSON><PERSON></a>, British YouTuber and Twitch streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TommyInn<PERSON>\" title=\"TommyInnit\"><PERSON><PERSON><PERSON><PERSON></a>, British YouTuber and Twitch streamer", "links": [{"title": "TommyInnit", "link": "https://wikipedia.org/wiki/<PERSON>Innit"}]}], "Deaths": [{"year": "585 BC", "text": "<PERSON><PERSON>, emperor of Japan (b. 711 BC)", "html": "585 BC - 585 BC - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Japan (b. 711 BC)", "no_year_html": "585 BC - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Japan (b. 711 BC)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "436", "text": "<PERSON>, Chinese general and politician", "html": "436 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Da<PERSON>ji\" title=\"Tan Da<PERSON>\"><PERSON></a>, Chinese general and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tan Da<PERSON>ji\"><PERSON></a>, Chinese general and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "491", "text": "<PERSON><PERSON>, emperor of the Byzantine Empire (b. 425)", "html": "491 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, emperor of the Byzantine Empire (b. 425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, emperor of the Byzantine Empire (b. 425)", "links": [{"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}]}, {"year": "682", "text": "<PERSON><PERSON><PERSON> <PERSON>, Egyptian politician, Governor of Egypt (b. 616)", "html": "682 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ibn_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>An<PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, Egyptian politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt\" class=\"mw-redirect\" title=\"List of governors of Islamic Egypt\">Governor of Egypt</a> (b. 616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ibn_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>An<PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, Egyptian politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt\" class=\"mw-redirect\" title=\"List of governors of Islamic Egypt\">Governor of Egypt</a> (b. 616)", "links": [{"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of governors of Islamic Egypt", "link": "https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt"}]}, {"year": "715", "text": "<PERSON>, pope of the Catholic Church (b. 664)", "html": "715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1024", "text": "<PERSON>, pope of the Catholic Church (b. 980)", "html": "1024 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VIII\" title=\"Pope Benedict VIII\"><PERSON></a>, pope of the Catholic Church (b. 980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict VIII\"><PERSON></a>, pope of the Catholic Church (b. 980)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1137", "text": "<PERSON>, duke of Aquitaine (b. 1099)", "html": "1137 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 1099)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 1099)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1241", "text": "<PERSON>, High Duke of Poland (b. 1196)", "html": "1241 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Pious\" title=\"<PERSON> II the Pious\"><PERSON></a>, High Duke of Poland (b. 1196)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Pious\" title=\"<PERSON> II the Pious\"><PERSON></a>, High Duke of Poland (b. 1196)", "links": [{"title": "<PERSON> the Pious", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Pi<PERSON>"}]}, {"year": "1283", "text": "<PERSON> of Scotland, queen of Norway (b. 1261)", "html": "1283 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Scotland,_Queen_of_Norway\" title=\"<PERSON> of Scotland, Queen of Norway\"><PERSON> of Scotland</a>, queen of Norway (b. 1261)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Scotland,_Queen_of_Norway\" title=\"<PERSON> of Scotland, Queen of Norway\"><PERSON> of Scotland</a>, queen of Norway (b. 1261)", "links": [{"title": "<PERSON> of Scotland, Queen of Norway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Scotland,_Queen_of_Norway"}]}, {"year": "1327", "text": "<PERSON>, 6th High Steward of Scotland, Scottish nobleman (ca. 1296)", "html": "1327 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_High_Steward_of_Scotland\" title=\"<PERSON>, 6th High Steward of Scotland\"><PERSON>, 6th High Steward of Scotland</a>, Scottish nobleman (ca. 1296)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_High_Steward_of_Scotland\" title=\"<PERSON>, 6th High Steward of Scotland\"><PERSON>, 6th High Steward of Scotland</a>, Scottish nobleman (ca. 1296)", "links": [{"title": "<PERSON>, 6th High Steward of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_High_<PERSON>eward_of_Scotland"}]}, {"year": "1483", "text": "<PERSON>, king of England (b. 1442)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> IV</a>, king of England (b. 1442)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> IV</a>, king of England (b. 1442)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1484", "text": "<PERSON> of Middleheim, prince of Wales (b. 1473)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Middleham,_Prince_of_Wales\" title=\"<PERSON> of Middleham, Prince of Wales\"><PERSON> of Middleheim</a>, prince of Wales (b. 1473)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Middleham,_Prince_of_Wales\" title=\"<PERSON> of Middleham, Prince of Wales\"><PERSON> of Middleheim</a>, prince of Wales (b. 1473)", "links": [{"title": "<PERSON> Middleham, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Prince_of_Wales"}]}, {"year": "1550", "text": "<PERSON><PERSON><PERSON>, Safavid prince (b. 1516)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Safavid prince (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Safavid prince (b. 1516)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1553", "text": "<PERSON>, French monk and scholar (b. 1494)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_Ra<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French monk and scholar (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Ra<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French monk and scholar (b. 1494)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1557", "text": "<PERSON><PERSON><PERSON>, Finnish priest and scholar (b. 1510)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish priest and scholar (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish priest and scholar (b. 1510)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1561", "text": "<PERSON>, French priest, knight and writer (b. 1500)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, knight and writer (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, knight and writer (b. 1500)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON>, English jurist and politician, Attorney General for England and Wales (b. 1561)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jurist and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jurist and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (b. 1561)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1654", "text": "<PERSON><PERSON>, Romanian prince (b. 1588)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian prince (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian prince (b. 1588)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1693", "text": "<PERSON>, <PERSON><PERSON><PERSON>, French author (b. 1618)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON><PERSON>\"><PERSON>, <PERSON><PERSON><PERSON></a>, French author (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON><PERSON>\"><PERSON>, <PERSON><PERSON><PERSON></a>, French author (b. 1618)", "links": [{"title": "<PERSON>, <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON>, 11th <PERSON>, Scottish soldier and politician (b. 1667)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Lord_<PERSON>\" title=\"<PERSON>, 11th Lord <PERSON>\"><PERSON>, 11th Lord <PERSON></a>, Scottish soldier and politician (b. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_11th_Lord_<PERSON>\" title=\"<PERSON>, 11th Lord <PERSON>\"><PERSON>, 11th Lord <PERSON></a>, Scottish soldier and politician (b. 1667)", "links": [{"title": "<PERSON>, 11th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Lord_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, German philosopher and academic (b. 1679)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, German philosopher and academic (b. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, German philosopher and academic (b. 1679)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)"}]}, {"year": "1761", "text": "<PERSON>, English priest and theologian (b. 1686)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Law\"><PERSON></a>, English priest and theologian (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Law\"><PERSON></a>, English priest and theologian (b. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, English author (b. 1710)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Swiss-French politician, Chief Minister to the French Monarch (b. 1732)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Chief Minister to the French Monarch</a> (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Chief Minister to the French Monarch</a> (b. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Prime Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France"}]}, {"year": "1806", "text": "<PERSON>, stadtholder of the Dutch Republic (b. 1748)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON></a>, stadtholder of the Dutch Republic (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON></a>, stadtholder of the Dutch Republic (b. 1748)", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, American businessman and politician (b. 1794)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rn<PERSON>\" title=\"<PERSON><PERSON><PERSON> Corn<PERSON>\"><PERSON><PERSON><PERSON></a>, American businessman and politician (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Corn<PERSON>\"><PERSON><PERSON><PERSON></a>, American businessman and politician (b. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>st<PERSON>_Corning"}]}, {"year": "1876", "text": "<PERSON>, American lawyer, judge, and politician (b. 1804)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer, judge, and politician (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer, judge, and politician (b. 1804)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1882", "text": "<PERSON>, English poet and painter (b. 1828)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and painter (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and painter (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, French chemist and academic (b. 1786)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8ne_Chevreul\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8ne_Chevreul\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A8ne_Chevreul"}]}, {"year": "1904", "text": "<PERSON>, Spanish queen (b. 1830)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Spain\" class=\"mw-redirect\" title=\"Isabella II of Spain\"><PERSON> II</a>, Spanish queen (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Spain\" class=\"mw-redirect\" title=\"Isabella II of Spain\"><PERSON> II</a>, Spanish queen (b. 1830)", "links": [{"title": "<PERSON> II of Spain", "link": "https://wikipedia.org/wiki/Isabella_II_of_Spain"}]}, {"year": "1909", "text": "<PERSON>, Polish-American actress (b. 1840)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American actress (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American actress (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Helena_<PERSON>d<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English rugby player (b. 1883)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English philologist and scholar (b. 1863)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philologist and scholar (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philologist and scholar (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, German entomologist and explorer (b. 1866)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entomologist and explorer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entomologist and explorer (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, American freak show performer (b. 1857)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Zip_the_Pinhead\" title=\"Z<PERSON> the Pinhead\">Zip the Pinhead</a>, American <a href=\"https://wikipedia.org/wiki/Freak_show\" title=\"Freak show\">freak show</a> performer (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zip_the_Pinhead\" title=\"Z<PERSON> the Pinhead\"><PERSON>ip the Pinhead</a>, American <a href=\"https://wikipedia.org/wiki/Freak_show\" title=\"Freak show\">freak show</a> performer (b. 1857)", "links": [{"title": "Zip the Pinhead", "link": "https://wikipedia.org/wiki/Zip_the_Pinhead"}, {"title": "Freak show", "link": "https://wikipedia.org/wiki/Freak_show"}]}, {"year": "1936", "text": "<PERSON>, German sociologist and philosopher (b. 1855)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%B6nnies\" title=\"Ferdinand Tönnies\"><PERSON></a>, German sociologist and philosopher (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%B6nnies\" title=\"Ferdinand T<PERSON>\"><PERSON></a>, German sociologist and philosopher (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_T%C3%B6nnies"}]}, {"year": "1940", "text": "Mrs <PERSON>, English actress (b. 1865)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Mrs_<PERSON>_<PERSON>\" title=\"Mrs <PERSON>\">Mrs <PERSON></a>, English actress (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mrs_<PERSON>_<PERSON>\" title=\"Mrs <PERSON>\">Mrs <PERSON></a>, English actress (b. 1865)", "links": [{"title": "Mrs <PERSON>", "link": "https://wikipedia.org/wiki/Mrs_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian lieutenant and pilot (b. 1920)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Yevgen<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian lieutenant and pilot (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian lieutenant and pilot (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>ud<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German pastor and theologian (b. 1906)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and theologian (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and theologian (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German admiral (b. 1887)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German carpenter (b. 1903)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German carpenter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German carpenter (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German general (b. 1887)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German lawyer and jurist (b. 1896)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and jurist (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and jurist (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Austrian-German lawyer and jurist (b. 1902)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian-German lawyer and jurist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian-German lawyer and jurist (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi"}]}, {"year": "1948", "text": "<PERSON>, Australian 5th General of The Salvation Army (b. 1872)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Salvation_Army)\" title=\"<PERSON> (Salvation Army)\"><PERSON></a>, Australian 5th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Salvation_Army)\" title=\"<PERSON> (Salvation Army)\"><PERSON></a>, Australian 5th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1872)", "links": [{"title": "<PERSON> (Salvation Army)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Salvation_Army)"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1948", "text": "<PERSON>, Colombian lawyer and politician, 16th Colombian Minister of National Education (b. 1903)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9cer_<PERSON>ait%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Ministry_of_National_Education_(Colombia)\" title=\"Ministry of National Education (Colombia)\">Colombian Minister of National Education</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9cer_<PERSON>ait%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Ministry_of_National_Education_(Colombia)\" title=\"Ministry of National Education (Colombia)\">Colombian Minister of National Education</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jorge_Eli%C3%A9cer_Gait%C3%A1n"}, {"title": "Ministry of National Education (Colombia)", "link": "https://wikipedia.org/wiki/Ministry_of_National_Education_(Colombia)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian physicist and meteorologist (b. 1862)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Vilhel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hel<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian physicist and meteorologist (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>hel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian physicist and meteorologist (b. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vilhel<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American football player and coach (b. 1877)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English philosopher and television host (b. 1891)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/C._E._<PERSON><PERSON>_Joad\" title=\"C. E. M. Joad\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and television host (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._E._<PERSON>._Joad\" title=\"C. E. M. Joad\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and television host (b. 1891)", "links": [{"title": "C. <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>_Joad"}]}, {"year": "1953", "text": "<PERSON>, German philosopher from the Vienna Circle (b. 1891)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher from the Vienna Circle (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher from the Vienna Circle (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American architect, designed the Price Tower and Fallingwater (b. 1867)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Price_Tower\" title=\"Price Tower\">Price Tower</a> and <a href=\"https://wikipedia.org/wiki/Fallingwater\" title=\"Fallingwater\">Fallingwater</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Price_Tower\" title=\"Price Tower\">Price Tower</a> and <a href=\"https://wikipedia.org/wiki/Fallingwater\" title=\"Fallingwater\">Fallingwater</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Price Tower", "link": "https://wikipedia.org/wiki/Price_Tower"}, {"title": "Fallingwater", "link": "https://wikipedia.org/wiki/Fallingwater"}]}, {"year": "1961", "text": "<PERSON><PERSON> <PERSON> of Albania (b. 1895)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Zog_I_of_Albania\" class=\"mw-redirect\" title=\"Zog I of Albania\"><PERSON><PERSON> <PERSON> of Albania</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zog_I_of_Albania\" class=\"mw-redirect\" title=\"Zog I of Albania\"><PERSON><PERSON> <PERSON> of Albania</a> (b. 1895)", "links": [{"title": "Zog I of Albania", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_Albania"}]}, {"year": "1963", "text": "<PERSON>, American trombonist (b. 1891)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trombonist (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trombonist (b. 1891)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Argentinian painter and sculptor (b. 1887)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Xul_Solar\" title=\"Xul Solar\"><PERSON><PERSON></a>, Argentinian painter and sculptor (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xul_Solar\" title=\"Xul Solar\"><PERSON><PERSON></a>, Argentinian painter and sculptor (b. 1887)", "links": [{"title": "Xul Solar", "link": "https://wikipedia.org/wiki/Xul_Solar"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Swedish-American illustrator and animator (b. 1896)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American illustrator and animator (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American illustrator and animator (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>taf_<PERSON>ggren"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and pianist (b. 1903)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1940)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Italian rower (b. 1891)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Petronio\" title=\"Renato Petronio\"><PERSON><PERSON></a>, Italian rower (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Petronio\" title=\"Renato Petronio\"><PERSON><PERSON></a>, Italian rower (b. 1891)", "links": [{"title": "<PERSON>ato Petronio", "link": "https://wikipedia.org/wiki/Renato_Petronio"}]}, {"year": "1978", "text": "<PERSON><PERSON>-<PERSON>, English-Welsh architect, designed <PERSON><PERSON><PERSON> (b. 1883)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, English-Welsh architect, designed <a href=\"https://wikipedia.org/wiki/Portmeirion\" title=\"Portmeirion\">Portmeirion</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, English-Welsh architect, designed <a href=\"https://wikipedia.org/wiki/Portmeirion\" title=\"Portmeirion\">Portmeirion</a> (b. 1883)", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "Portmeirion", "link": "https://wikipedia.org/wiki/Portmeirion"}]}, {"year": "1980", "text": "<PERSON>, Iraqi cleric and philosopher (b. 1935)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi cleric and philosopher (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi cleric and philosopher (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian pianist, composer, and conductor (b. 1896)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian pianist, composer, and conductor (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian pianist, composer, and conductor (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter and actor (b. 1931)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Brook_Benton\" title=\"Brook Benton\"><PERSON></a>, American singer-songwriter and actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brook_Benton\" title=\"Brook Benton\"><PERSON></a>, American singer-songwriter and actor (b. 1931)", "links": [{"title": "<PERSON> Benton", "link": "https://wikipedia.org/wiki/<PERSON>_Benton"}]}, {"year": "1988", "text": "<PERSON>, German footballer (b. 1913)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American singer (b. 1937)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American hurdler and coach (b. 1914)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Forrest_Towns\" title=\"Forrest Towns\"><PERSON></a>, American hurdler and coach (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Forrest_Towns\" title=\"Forrest Towns\"><PERSON></a>, American hurdler and coach (b. 1914)", "links": [{"title": "Forrest Towns", "link": "https://wikipedia.org/wiki/Forrest_Towns"}]}, {"year": "1993", "text": "<PERSON>, American rabbi and philosopher (b. 1903)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rabbi and philosopher (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rabbi and philosopher (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American author and publicist (b. 1915)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and publicist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and publicist (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter (b. 1914)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Boren <PERSON>xton\"><PERSON></a>, American singer-songwriter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Mae Boren <PERSON>xton\"><PERSON></a>, American singer-songwriter (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American author and screenwriter (b. 1916)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American cellist and composer (b. 1953)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist and composer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cora\"><PERSON></a>, American cellist and composer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Nigerien general and politician, President of Niger (b. 1949)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Ma%C3%AFnassara\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Niger\" title=\"Niger\">Nigerien</a> general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Niger\" class=\"mw-redirect\" title=\"President of Niger\">President of Niger</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Ma%C3%AFnassara\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Niger\" title=\"Niger\">Nigerien</a> general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Niger\" class=\"mw-redirect\" title=\"President of Niger\">President of Niger</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ibrahim_Bar%C3%A9_Ma%C3%AFnassara"}, {"title": "Niger", "link": "https://wikipedia.org/wiki/Niger"}, {"title": "President of Niger", "link": "https://wikipedia.org/wiki/President_of_Niger"}]}, {"year": "2000", "text": "<PERSON>, Trotskyist activist and founder of the Socialist Workers Party (b. 1917)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trotskyist activist and founder of the <a href=\"https://wikipedia.org/wiki/Socialist_Workers_Party_(UK)\" title=\"Socialist Workers Party (UK)\">Socialist Workers Party</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tony Cliff\"><PERSON></a>, Trotskyist activist and founder of the <a href=\"https://wikipedia.org/wiki/Socialist_Workers_Party_(UK)\" title=\"Socialist Workers Party (UK)\">Socialist Workers Party</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Socialist Workers Party (UK)", "link": "https://wikipedia.org/wiki/Socialist_Workers_Party_(UK)"}]}, {"year": "2001", "text": "<PERSON>, American baseball player and coach (b. 1940)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American race car driver (b. 1926)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1926)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2002", "text": "<PERSON>, Austrian soldier, mathematician, and academic (b. 1891)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian soldier, mathematician, and academic (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian soldier, mathematician, and academic (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_V<PERSON>oris"}]}, {"year": "2003", "text": "<PERSON>, American cartoonist (b. 1949)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American baseball player, coach, manager (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, manager (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, manager (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Swedish director and screenwriter (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Vilgot_Sj%C3%B6man\" title=\"Vil<PERSON>\">V<PERSON><PERSON></a>, Swedish director and screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vilgot_Sj%C3%B6man\" title=\"Vil<PERSON>\">V<PERSON><PERSON></a>, Swedish director and screenwriter (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vilgot_Sj%C3%B6man"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Czech philosopher and poet (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech philosopher and poet (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech philosopher and poet (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Egon_Bondy"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, American astronomer and academic (b. 1907)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>it\" title=\"<PERSON><PERSON><PERSON> Hoffleit\"><PERSON><PERSON><PERSON></a>, American astronomer and academic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ffleit\"><PERSON><PERSON><PERSON></a>, American astronomer and academic (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>it"}]}, {"year": "2009", "text": "<PERSON>, American baseball player (b. 1986)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer and manager (b. 1945)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(footballer,_born_1945)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1945)\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(footballer,_born_1945)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1945)\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1945)", "link": "https://wikipedia.org/wiki/Zolt%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(footballer,_born_1945)"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Bahraini journalist (b. 1971)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bahraini journalist (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bahraini journalist (b. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American director, producer, and screenwriter (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Welsh rugby player and cricketer (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Welsh rugby player and cricketer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Welsh rugby player and cricketer (b. 1929)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rugby_union)"}]}, {"year": "2012", "text": "<PERSON>, Soviet philosopher, psychologist, and author (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet philosopher, psychologist, and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet philosopher, psychologist, and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American sculptor and painter (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor and painter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor and painter (b. 1931)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_(sculptor)"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli lawyer and politician (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Mo<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Morde<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli lawyer and politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli lawyer and politician (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American journalist and author (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Mc<PERSON>and<PERSON>_<PERSON>\" title=\"McCand<PERSON> <PERSON>\"><PERSON><PERSON><PERSON>and<PERSON> <PERSON></a>, American journalist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mc<PERSON>and<PERSON>_<PERSON>\" title=\"Mc<PERSON>and<PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American journalist and author (b. 1927)", "links": [{"title": "McCandlish <PERSON>", "link": "https://wikipedia.org/wiki/McCandlish_Phillips"}]}, {"year": "2013", "text": "<PERSON>, Italian-American architect, designed the <PERSON><PERSON><PERSON> (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American trumpet player, composer, and producer (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and producer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and producer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player (b. 1973)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1973)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Jamaican economist, academic, and politician (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican economist, academic, and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican economist, academic, and politician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Indian politician (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Na<PERSON>dra"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Trinbagonian politician, 3rd President of Trinidad and Tobago (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Trinbagonian politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago\" title=\"President of Trinidad and Tobago\">President of Trinidad and Tobago</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Trinbagonian politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago\" title=\"President of Trinidad and Tobago\">President of Trinidad and Tobago</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian author (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian author (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-Jan<PERSON>i%C4%87"}]}, {"year": "2015", "text": "<PERSON>, Canadian-American director, producer, and screenwriter (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director, producer, and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director, producer, and screenwriter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, British marine archaeologist (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Margaret Rule\"><PERSON></a>, British marine archaeologist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Margaret Rule\"><PERSON></a>, British marine archaeologist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French director and screenwriter (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English physicist and academic (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American journalist and author (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Chinese-American academic (b. 1909)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Tsien_Tsuen-hsuin\" title=\"Tsien Tsuen-hsuin\"><PERSON>sie<PERSON>-hsuin</a>, Chinese-American academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsien_Tsuen-hsuin\" title=\"Tsien Tsuen-hsuin\"><PERSON><PERSON><PERSON>-hsuin</a>, Chinese-American academic (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsien_T<PERSON><PERSON>-hsuin"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American spy (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American spy (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American spy (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American football player (b. 1981)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player (b. 1981)", "links": [{"title": "<PERSON> (defensive end)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)"}]}, {"year": "2017", "text": "<PERSON>, New Zealand-Australian comedian, writer, and satirist (b. 1948)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, New Zealand-Australian comedian, writer, and satirist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, New Zealand-Australian comedian, writer, and satirist (b. 1948)", "links": [{"title": "<PERSON> (satirist)", "link": "https://wikipedia.org/wiki/<PERSON>_(satirist)"}]}, {"year": "2019", "text": "<PERSON>, American writer and editor (b. 1926)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and editor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and editor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Duke of Edinburgh (b. 1921)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh\" title=\"Prince <PERSON>, Duke of Edinburgh\">Prince <PERSON>, Duke of Edinburgh</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh\" title=\"Prince <PERSON>, Duke of Edinburgh\">Prince <PERSON>, Duke of Edinburgh</a> (b. 1921)", "links": [{"title": "<PERSON>, Duke of Edinburgh", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh"}]}, {"year": "2021", "text": "<PERSON><PERSON>, American rapper and actor (b. 1970)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/DMX\" title=\"DMX\"><PERSON><PERSON></a>, American rapper and actor (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DMX\" title=\"DMX\"><PERSON><PERSON></a>, American rapper and actor (b. 1970)", "links": [{"title": "DMX", "link": "https://wikipedia.org/wiki/DMX"}]}, {"year": "2021", "text": "<PERSON>, British reality-TV icon (b. 1982)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British reality-TV icon (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British reality-TV icon (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, British scientist and Labour Party politician (b. 1938)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, British scientist and Labour Party politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, British scientist and Labour Party politician (b. 1938)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "2021", "text": "<PERSON>, American lawyer (b. 1927)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, American football player (b. 1997)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, German-American jazz pianist (b. 1935)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American jazz pianist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American jazz pianist (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}