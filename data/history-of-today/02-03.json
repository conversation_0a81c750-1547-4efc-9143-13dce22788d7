{"date": "February 3", "url": "https://wikipedia.org/wiki/February_3", "data": {"Events": [{"year": "1047", "text": "<PERSON><PERSON> of Hauteville is elected as count of the Apulian Normans during the Norman conquest of Southern Italy.", "html": "1047 - <a href=\"https://wikipedia.org/wiki/Drogo_of_Hauteville\" title=\"Drogo of Hauteville\"><PERSON><PERSON> of Hauteville</a> is <a href=\"https://wikipedia.org/wiki/County_of_Apulia_and_Calabria\" title=\"County of Apulia and Calabria\">elected as count of the Apulian Normans</a> during the <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_southern_Italy\" title=\"Norman conquest of southern Italy\">Norman conquest of Southern Italy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Drogo_of_Hauteville\" title=\"Drogo of Hauteville\"><PERSON><PERSON> of Hauteville</a> is <a href=\"https://wikipedia.org/wiki/County_of_Apulia_and_Calabria\" title=\"County of Apulia and Calabria\">elected as count of the Apulian Normans</a> during the <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_southern_Italy\" title=\"Norman conquest of southern Italy\">Norman conquest of Southern Italy</a>.", "links": [{"title": "<PERSON><PERSON> of Hauteville", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hauteville"}, {"title": "County of Apulia and Calabria", "link": "https://wikipedia.org/wiki/County_of_Apulia_and_Calabria"}, {"title": "Norman conquest of southern Italy", "link": "https://wikipedia.org/wiki/Norman_conquest_of_southern_Italy"}]}, {"year": "1112", "text": "<PERSON>, Count of Barcelona, and <PERSON><PERSON>, Countess of Provence, marry, uniting the fortunes of those two states.", "html": "1112 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Barcelona\" title=\"<PERSON>, Count of Barcelona\"><PERSON>, Count of Barcelona</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Countess_of_Provence\" title=\"<PERSON><PERSON>, Countess of Provence\"><PERSON><PERSON>, Countess of Provence</a>, marry, uniting the fortunes of those two states.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Barcelona\" title=\"<PERSON>, Count of Barcelona\"><PERSON>, Count of Barcelona</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Countess_of_Provence\" title=\"<PERSON><PERSON>, Countess of Provence\"><PERSON><PERSON>, Countess of Provence</a>, marry, uniting the fortunes of those two states.", "links": [{"title": "<PERSON>, Count of Barcelona", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Barcelona"}, {"title": "<PERSON><PERSON>, Countess of Provence", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Countess_of_Provence"}]}, {"year": "1451", "text": "Sultan <PERSON><PERSON><PERSON> inherits the throne of the Ottoman Empire.", "html": "1451 - <a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">Sultan</a> <a href=\"https://wikipedia.org/wiki/Mehmed_II\" title=\"Mehmed II\"><PERSON><PERSON><PERSON> II</a> inherits the throne of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">Sultan</a> <a href=\"https://wikipedia.org/wiki/Mehmed_II\" title=\"Mehmed II\"><PERSON><PERSON><PERSON> II</a> inherits the throne of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sultan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1488", "text": "<PERSON><PERSON><PERSON><PERSON> of Portugal lands in Mossel Bay after rounding the Cape of Good Hope, becoming the first known European to travel so far south.", "html": "1488 - <a href=\"https://wikipedia.org/wiki/Bartolomeu_Dias\" title=\"Bartolomeu Dias\">Bartolomeu Dias</a> of Portugal lands in <a href=\"https://wikipedia.org/wiki/Mossel_Bay\" title=\"Mossel Bay\">Mossel Bay</a> after rounding the <a href=\"https://wikipedia.org/wiki/Cape_of_Good_Hope\" title=\"Cape of Good Hope\">Cape of Good Hope</a>, becoming the first known European to travel so far south.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartolomeu_Dias\" title=\"Bartolomeu Dias\">Bartolomeu Dias</a> of Portugal lands in <a href=\"https://wikipedia.org/wiki/Mossel_Bay\" title=\"Mossel Bay\">Mossel Bay</a> after rounding the <a href=\"https://wikipedia.org/wiki/Cape_of_Good_Hope\" title=\"Cape of Good Hope\">Cape of Good Hope</a>, becoming the first known European to travel so far south.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>u_Dias"}, {"title": "Mossel Bay", "link": "https://wikipedia.org/wiki/Mossel_Bay"}, {"title": "Cape of Good Hope", "link": "https://wikipedia.org/wiki/Cape_of_Good_Hope"}]}, {"year": "1509", "text": "The Portuguese navy defeats a joint fleet of the Ottoman Empire, the Republic of Venice, the Sultan of Gujarat, the Mamlûk Burji Sultanate of Egypt, the Zamorin of Calicut, and the Republic of Ragusa at the Battle of Diu in Diu, India.", "html": "1509 - The <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> navy defeats a joint fleet of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Sultan of Gujarat</a>, the <a href=\"https://wikipedia.org/wiki/Burji_dynasty\" class=\"mw-redirect\" title=\"Burji dynasty\">Mamlûk Burji Sultanate of Egypt</a>, the <a href=\"https://wikipedia.org/wiki/Saamoothiri\" class=\"mw-redirect\" title=\"Saamoothiri\">Zamorin</a> of <a href=\"https://wikipedia.org/wiki/Kozhikode\" title=\"Kozhikode\">Calicut</a>, and the <a href=\"https://wikipedia.org/wiki/Republic_of_Ragusa\" title=\"Republic of Ragusa\">Republic of Ragusa</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Diu_(1509)\" class=\"mw-redirect\" title=\"Battle of Diu (1509)\">Battle of Diu</a> in <a href=\"https://wikipedia.org/wiki/Diu,_India\" title=\"Diu, India\">Diu, India</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> navy defeats a joint fleet of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Sultan of Gujarat</a>, the <a href=\"https://wikipedia.org/wiki/Burji_dynasty\" class=\"mw-redirect\" title=\"Burji dynasty\">Mamlûk Burji Sultanate of Egypt</a>, the <a href=\"https://wikipedia.org/wiki/Saamoothiri\" class=\"mw-redirect\" title=\"Saamoothiri\">Zamorin</a> of <a href=\"https://wikipedia.org/wiki/Kozhikode\" title=\"Kozhikode\">Calicut</a>, and the <a href=\"https://wikipedia.org/wiki/Republic_of_Ragusa\" title=\"Republic of Ragusa\">Republic of Ragusa</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Diu_(1509)\" class=\"mw-redirect\" title=\"Battle of Diu (1509)\">Battle of Diu</a> in <a href=\"https://wikipedia.org/wiki/Diu,_India\" title=\"Diu, India\">Diu, India</a>.", "links": [{"title": "Portuguese Empire", "link": "https://wikipedia.org/wiki/Portuguese_Empire"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Burji dynasty", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_dynasty"}, {"title": "Saamoothiri", "link": "https://wikipedia.org/wiki/Saamoothiri"}, {"title": "Kozhikode", "link": "https://wikipedia.org/wiki/Kozhikode"}, {"title": "Republic of Ragusa", "link": "https://wikipedia.org/wiki/Republic_of_Ragusa"}, {"title": "Battle of Diu (1509)", "link": "https://wikipedia.org/wiki/Battle_of_Diu_(1509)"}, {"title": "Diu, India", "link": "https://wikipedia.org/wiki/Diu,_India"}]}, {"year": "1583", "text": "Battle of São Vicente takes place off Portuguese Brazil where three English warships led by navigator <PERSON> fight off three Spanish galleons sinking one in the process.", "html": "1583 - <a href=\"https://wikipedia.org/wiki/Battle_of_S%C3%A3o_Vicente\" title=\"Battle of São Vicente\">Battle of São Vicente</a> takes place off <a href=\"https://wikipedia.org/wiki/Portuguese_Brazil\" class=\"mw-redirect\" title=\"Portuguese Brazil\">Portuguese Brazil</a> where three English warships led by navigator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fight off three <a href=\"https://wikipedia.org/wiki/Spanish_galleon\" class=\"mw-redirect\" title=\"Spanish galleon\">Spanish galleons</a> sinking one in the process.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_S%C3%A3o_Vicente\" title=\"Battle of São Vicente\">Battle of São Vicente</a> takes place off <a href=\"https://wikipedia.org/wiki/Portuguese_Brazil\" class=\"mw-redirect\" title=\"Portuguese Brazil\">Portuguese Brazil</a> where three English warships led by navigator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> fight off three <a href=\"https://wikipedia.org/wiki/Spanish_galleon\" class=\"mw-redirect\" title=\"Spanish galleon\">Spanish galleons</a> sinking one in the process.", "links": [{"title": "Battle of São Vicente", "link": "https://wikipedia.org/wiki/Battle_of_S%C3%A3<PERSON>_<PERSON>"}, {"title": "Portuguese Brazil", "link": "https://wikipedia.org/wiki/Portuguese_Brazil"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Spanish galleon", "link": "https://wikipedia.org/wiki/Spanish_galleon"}]}, {"year": "1637", "text": "Tulip Mania collapses within the Dutch Republic.", "html": "1637 - <a href=\"https://wikipedia.org/wiki/Tulip_Mania\" class=\"mw-redirect\" title=\"Tulip Mania\">Tulip Mania</a> collapses within the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tulip_Mania\" class=\"mw-redirect\" title=\"Tulip Mania\">Tulip Mania</a> collapses within the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mania"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}]}, {"year": "1639", "text": " The House of Assembly of Barbados meets for the first time.", "html": "1639 - The <a href=\"https://wikipedia.org/wiki/House_of_Assembly_of_Barbados\" title=\"House of Assembly of Barbados\">House of Assembly of Barbados</a> meets for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/House_of_Assembly_of_Barbados\" title=\"House of Assembly of Barbados\">House of Assembly of Barbados</a> meets for the first time.", "links": [{"title": "House of Assembly of Barbados", "link": "https://wikipedia.org/wiki/House_of_Assembly_of_Barbados"}]}, {"year": "1690", "text": "The colony of Massachusetts issues the first paper money in the Americas.", "html": "1690 - The <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">colony of Massachusetts</a> issues the first <a href=\"https://wikipedia.org/wiki/Paper_money\" title=\"Paper money\">paper money</a> in the Americas.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">colony of Massachusetts</a> issues the first <a href=\"https://wikipedia.org/wiki/Paper_money\" title=\"Paper money\">paper money</a> in the Americas.", "links": [{"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}, {"title": "Paper money", "link": "https://wikipedia.org/wiki/Paper_money"}]}, {"year": "1706", "text": "During the Battle of Fraustadt Swedish forces defeat a superior Saxon-Polish-Russian force by deploying a double envelopment.", "html": "1706 - During the <a href=\"https://wikipedia.org/wiki/Battle_of_Fraustadt\" title=\"Battle of Fraustadt\">Battle of Fraustadt</a> Swedish forces defeat a superior <a href=\"https://wikipedia.org/wiki/Saxony\" title=\"Saxony\">Saxon</a>-Polish-Russian force by deploying a <a href=\"https://wikipedia.org/wiki/Double_envelopment\" class=\"mw-redirect\" title=\"Double envelopment\">double envelopment</a>.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Battle_of_Fraustadt\" title=\"Battle of Fraustadt\">Battle of Fraustadt</a> Swedish forces defeat a superior <a href=\"https://wikipedia.org/wiki/Saxony\" title=\"Saxony\">Saxon</a>-Polish-Russian force by deploying a <a href=\"https://wikipedia.org/wiki/Double_envelopment\" class=\"mw-redirect\" title=\"Double envelopment\">double envelopment</a>.", "links": [{"title": "Battle of Fraustadt", "link": "https://wikipedia.org/wiki/Battle_of_Fraustadt"}, {"title": "Saxony", "link": "https://wikipedia.org/wiki/Saxony"}, {"title": "Double envelopment", "link": "https://wikipedia.org/wiki/Double_envelopment"}]}, {"year": "1716", "text": "The 1716 Algiers earthquake sequence began with an Mw  7.0 mainshock that caused severe damage and killed 20,000 in Algeria.", "html": "1716 - The <a href=\"https://wikipedia.org/wiki/1716_Algiers_earthquake\" title=\"1716 Algiers earthquake\">1716 Algiers earthquake</a> sequence began with an M<sub>w</sub>  7.0 mainshock that caused severe damage and killed 20,000 in Algeria.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1716_Algiers_earthquake\" title=\"1716 Algiers earthquake\">1716 Algiers earthquake</a> sequence began with an M<sub>w</sub>  7.0 mainshock that caused severe damage and killed 20,000 in Algeria.", "links": [{"title": "1716 Algiers earthquake", "link": "https://wikipedia.org/wiki/1716_Algiers_earthquake"}]}, {"year": "1781", "text": "American Revolutionary War: British forces seize the Dutch-owned Caribbean island Sint Eustatius.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British forces <a href=\"https://wikipedia.org/wiki/Capture_of_Sint_Eustatius\" title=\"Capture of Sint Eustatius\">seize</a> the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch</a>-owned Caribbean island <a href=\"https://wikipedia.org/wiki/Sint_Eustatius\" title=\"Sint Eustatius\">Sint Eustatius</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British forces <a href=\"https://wikipedia.org/wiki/Capture_of_Sint_Eustatius\" title=\"Capture of Sint Eustatius\">seize</a> the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch</a>-owned Caribbean island <a href=\"https://wikipedia.org/wiki/Sint_Eustatius\" title=\"Sint Eustatius\">Sint Eustatius</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Capture of Sint Eustatius", "link": "https://wikipedia.org/wiki/Capture_of_Sint_Eustatius"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}, {"title": "Sint Eustatius", "link": "https://wikipedia.org/wiki/Sint_Eustatius"}]}, {"year": "1783", "text": "Spain-United States relations are first established.", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Spain%E2%80%93United_States_relations\" title=\"Spain-United States relations\">Spain-United States relations</a> are first established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spain%E2%80%93United_States_relations\" title=\"Spain-United States relations\">Spain-United States relations</a> are first established.", "links": [{"title": "Spain-United States relations", "link": "https://wikipedia.org/wiki/Spain%E2%80%93United_States_relations"}]}, {"year": "1787", "text": "Militia led by General <PERSON> crush the remnants of <PERSON><PERSON>' Rebellion in Petersham, Massachusetts.", "html": "1787 - Militia led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> crush the remnants of <a href=\"https://wikipedia.org/wiki/Shays%27_Rebellion\" class=\"mw-redirect\" title=\"Shay<PERSON>' Rebellion\">Shay<PERSON>' Rebellion</a> in <a href=\"https://wikipedia.org/wiki/Petersham,_Massachusetts\" title=\"Petersham, Massachusetts\">Petersham, Massachusetts</a>.", "no_year_html": "Militia led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> crush the remnants of <a href=\"https://wikipedia.org/wiki/Shays%27_Rebellion\" class=\"mw-redirect\" title=\"Shay<PERSON>' Rebellion\">Shay<PERSON>' Rebellion</a> in <a href=\"https://wikipedia.org/wiki/Petersham,_Massachusetts\" title=\"Petersham, Massachusetts\">Petersham, Massachusetts</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>' Rebellion", "link": "https://wikipedia.org/wiki/Shays%27_Rebellion"}, {"title": "Petersham, Massachusetts", "link": "https://wikipedia.org/wiki/Petersham,_Massachusetts"}]}, {"year": "1807", "text": "A British military force, under Brigadier-General Sir <PERSON> captures the Spanish Empire city of Montevideo, now the capital of Uruguay.", "html": "1807 - A British military force, under Brigadier-General Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> captures the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a> city of <a href=\"https://wikipedia.org/wiki/Montevideo\" title=\"Montevideo\">Montevideo</a>, now the capital of <a href=\"https://wikipedia.org/wiki/Uruguay\" title=\"Uruguay\">Uruguay</a>.", "no_year_html": "A British military force, under Brigadier-General Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> captures the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a> city of <a href=\"https://wikipedia.org/wiki/Montevideo\" title=\"Montevideo\">Montevideo</a>, now the capital of <a href=\"https://wikipedia.org/wiki/Uruguay\" title=\"Uruguay\">Uruguay</a>.", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Montevideo", "link": "https://wikipedia.org/wiki/Montevideo"}, {"title": "Uruguay", "link": "https://wikipedia.org/wiki/Uruguay"}]}, {"year": "1809", "text": "The Territory of Illinois is created by the 10th United States Congress.", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Illinois_Territory\" title=\"Illinois Territory\">The Territory of Illinois</a> is created by the <a href=\"https://wikipedia.org/wiki/10th_United_States_Congress\" title=\"10th United States Congress\">10th United States Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Illinois_Territory\" title=\"Illinois Territory\">The Territory of Illinois</a> is created by the <a href=\"https://wikipedia.org/wiki/10th_United_States_Congress\" title=\"10th United States Congress\">10th United States Congress</a>.", "links": [{"title": "Illinois Territory", "link": "https://wikipedia.org/wiki/Illinois_Territory"}, {"title": "10th United States Congress", "link": "https://wikipedia.org/wiki/10th_United_States_Congress"}]}, {"year": "1813", "text": "<PERSON> defeats a Spanish royalist army at the Battle of San Lorenzo, part of the Argentine War of Independence.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"José <PERSON> Martín\"><PERSON></a> defeats a Spanish royalist army at the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Lorenzo\" title=\"Battle of San Lorenzo\">Battle of San Lorenzo</a>, part of the <a href=\"https://wikipedia.org/wiki/Argentine_War_of_Independence\" title=\"Argentine War of Independence\">Argentine War of Independence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"José de San Martín\"><PERSON></a> defeats a Spanish royalist army at the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Lorenzo\" title=\"Battle of San Lorenzo\">Battle of San Lorenzo</a>, part of the <a href=\"https://wikipedia.org/wiki/Argentine_War_of_Independence\" title=\"Argentine War of Independence\">Argentine War of Independence</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn"}, {"title": "Battle of San Lorenzo", "link": "https://wikipedia.org/wiki/Battle_of_San_Lorenzo"}, {"title": "Argentine War of Independence", "link": "https://wikipedia.org/wiki/Argentine_War_of_Independence"}]}, {"year": "1830", "text": "The London Protocol of 1830 establishes the full independence and sovereignty of Greece from the Ottoman Empire as the final result of the Greek War of Independence.", "html": "1830 - The <a href=\"https://wikipedia.org/wiki/London_Protocol_(1830)\" title=\"London Protocol (1830)\">London Protocol of 1830</a> establishes the full independence and sovereignty of <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> as the final result of the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/London_Protocol_(1830)\" title=\"London Protocol (1830)\">London Protocol of 1830</a> establishes the full independence and sovereignty of <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> as the final result of the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>.", "links": [{"title": "London Protocol (1830)", "link": "https://wikipedia.org/wiki/London_Protocol_(1830)"}, {"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}]}, {"year": "1862", "text": "Moldavia and Wallachia formally unite to create the Romanian United Principalities.", "html": "1862 - Moldavia and Wallachia formally unite to create the <a href=\"https://wikipedia.org/wiki/United_Principalities_of_Moldavia_and_Wallachia\" title=\"United Principalities of Moldavia and Wallachia\">Romanian United Principalities</a>.", "no_year_html": "Moldavia and Wallachia formally unite to create the <a href=\"https://wikipedia.org/wiki/United_Principalities_of_Moldavia_and_Wallachia\" title=\"United Principalities of Moldavia and Wallachia\">Romanian United Principalities</a>.", "links": [{"title": "United Principalities of Moldavia and Wallachia", "link": "https://wikipedia.org/wiki/United_Principalities_of_Moldavia_and_Wallachia"}]}, {"year": "1870", "text": "The Fifteenth Amendment to the United States Constitution is ratified, guaranteeing voting rights to male citizens regardless of race.", "html": "1870 - The <a href=\"https://wikipedia.org/wiki/Fifteenth_Amendment_to_the_United_States_Constitution\" title=\"Fifteenth Amendment to the United States Constitution\">Fifteenth Amendment to the United States Constitution</a> is ratified, guaranteeing voting rights to male citizens regardless of race.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fifteenth_Amendment_to_the_United_States_Constitution\" title=\"Fifteenth Amendment to the United States Constitution\">Fifteenth Amendment to the United States Constitution</a> is ratified, guaranteeing voting rights to male citizens regardless of race.", "links": [{"title": "Fifteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fifteenth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1913", "text": "The Sixteenth Amendment to the United States Constitution is ratified, authorizing the Federal government to impose and collect an income tax.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Sixteenth_Amendment_to_the_United_States_Constitution\" title=\"Sixteenth Amendment to the United States Constitution\">Sixteenth Amendment to the United States Constitution</a> is ratified, authorizing the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">Federal government</a> to impose and collect an <a href=\"https://wikipedia.org/wiki/Income_tax\" title=\"Income tax\">income tax</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sixteenth_Amendment_to_the_United_States_Constitution\" title=\"Sixteenth Amendment to the United States Constitution\">Sixteenth Amendment to the United States Constitution</a> is ratified, authorizing the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">Federal government</a> to impose and collect an <a href=\"https://wikipedia.org/wiki/Income_tax\" title=\"Income tax\">income tax</a>.", "links": [{"title": "Sixteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Sixteenth_Amendment_to_the_United_States_Constitution"}, {"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}, {"title": "Income tax", "link": "https://wikipedia.org/wiki/Income_tax"}]}, {"year": "1916", "text": "The Centre Block of the Parliament buildings in Ottawa, Ontario, Canada burns down with the loss of seven lives.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/Centre_Block#Great_fire\" title=\"Centre Block\">Centre Block</a> of the <a href=\"https://wikipedia.org/wiki/Parliament_of_Canada\" title=\"Parliament of Canada\">Parliament</a> buildings in <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa, Ontario</a>, Canada burns down with the loss of seven lives.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Centre_Block#Great_fire\" title=\"Centre Block\">Centre Block</a> of the <a href=\"https://wikipedia.org/wiki/Parliament_of_Canada\" title=\"Parliament of Canada\">Parliament</a> buildings in <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa, Ontario</a>, Canada burns down with the loss of seven lives.", "links": [{"title": "Centre Block", "link": "https://wikipedia.org/wiki/Centre_Block#Great_fire"}, {"title": "Parliament of Canada", "link": "https://wikipedia.org/wiki/Parliament_of_Canada"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}]}, {"year": "1917", "text": "World War I: The American entry into World War I begins when diplomatic relations with Germany are severed due to its unrestricted submarine warfare.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/American_entry_into_World_War_I\" title=\"American entry into World War I\">American entry into World War I</a> begins when diplomatic relations with Germany are severed due to its unrestricted submarine warfare.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/American_entry_into_World_War_I\" title=\"American entry into World War I\">American entry into World War I</a> begins when diplomatic relations with Germany are severed due to its unrestricted submarine warfare.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "American entry into World War I", "link": "https://wikipedia.org/wiki/American_entry_into_World_War_I"}]}, {"year": "1918", "text": "The Twin Peaks Tunnel in San Francisco, California begins service as the longest streetcar tunnel in the world at 11,920 feet (3,630 meters) long.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Twin_Peaks_Tunnel\" title=\"Twin Peaks Tunnel\">Twin Peaks Tunnel</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>, <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> begins service as the longest <a href=\"https://wikipedia.org/wiki/Streetcar\" class=\"mw-redirect\" title=\"Streetcar\">streetcar</a> tunnel in the world at 11,920 feet (3,630 meters) long.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Twin_Peaks_Tunnel\" title=\"Twin Peaks Tunnel\">Twin Peaks Tunnel</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>, <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> begins service as the longest <a href=\"https://wikipedia.org/wiki/Streetcar\" class=\"mw-redirect\" title=\"Streetcar\">streetcar</a> tunnel in the world at 11,920 feet (3,630 meters) long.", "links": [{"title": "Twin Peaks Tunnel", "link": "https://wikipedia.org/wiki/Twin_Peaks_Tunnel"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Streetcar", "link": "https://wikipedia.org/wiki/Streetcar"}]}, {"year": "1927", "text": "A revolt against the military dictatorship of Portugal breaks out at Oporto.", "html": "1927 - A <a href=\"https://wikipedia.org/wiki/February_1927_Revolt\" title=\"February 1927 Revolt\">revolt</a> against the <a href=\"https://wikipedia.org/wiki/Ditadura_Nacional\" title=\"Ditadura Nacional\">military dictatorship</a> of <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> breaks out at <a href=\"https://wikipedia.org/wiki/Oporto\" class=\"mw-redirect\" title=\"Oporto\">Oporto</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/February_1927_Revolt\" title=\"February 1927 Revolt\">revolt</a> against the <a href=\"https://wikipedia.org/wiki/Ditadura_Nacional\" title=\"Ditadura Nacional\">military dictatorship</a> of <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> breaks out at <a href=\"https://wikipedia.org/wiki/Oporto\" class=\"mw-redirect\" title=\"Oporto\">Oporto</a>.", "links": [{"title": "February 1927 Revolt", "link": "https://wikipedia.org/wiki/February_1927_Revolt"}, {"title": "Ditadura Nacional", "link": "https://wikipedia.org/wiki/Ditadura_Nacional"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}, {"title": "Oporto", "link": "https://wikipedia.org/wiki/Oporto"}]}, {"year": "1930", "text": "The Communist Party of Vietnam is founded at a \"Unification Conference\" held in Kowloon, British Hong Kong.", "html": "1930 - The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Vietnam\" title=\"Communist Party of Vietnam\">Communist Party of Vietnam</a> is founded at a \"Unification Conference\" held in <a href=\"https://wikipedia.org/wiki/Kowloon_Peninsula\" title=\"Kowloon Peninsula\">Kowloon</a>, <a href=\"https://wikipedia.org/wiki/British_Hong_Kong\" title=\"British Hong Kong\">British Hong Kong</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Vietnam\" title=\"Communist Party of Vietnam\">Communist Party of Vietnam</a> is founded at a \"Unification Conference\" held in <a href=\"https://wikipedia.org/wiki/Kowloon_Peninsula\" title=\"Kowloon Peninsula\">Kowloon</a>, <a href=\"https://wikipedia.org/wiki/British_Hong_Kong\" title=\"British Hong Kong\">British Hong Kong</a>.", "links": [{"title": "Communist Party of Vietnam", "link": "https://wikipedia.org/wiki/Communist_Party_of_Vietnam"}, {"title": "Kowloon Peninsula", "link": "https://wikipedia.org/wiki/Kowloon_Peninsula"}, {"title": "British Hong Kong", "link": "https://wikipedia.org/wiki/British_Hong_Kong"}]}, {"year": "1931", "text": "The Hawke's Bay earthquake, New Zealand's worst natural disaster, kills 258.", "html": "1931 - The <a href=\"https://wikipedia.org/wiki/1931_Hawke%27s_Bay_earthquake\" title=\"1931 Hawke's Bay earthquake\">Hawke's Bay earthquake</a>, New Zealand's worst natural disaster, kills 258.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1931_Hawke%27s_Bay_earthquake\" title=\"1931 Hawke's Bay earthquake\">Hawke's Bay earthquake</a>, New Zealand's worst natural disaster, kills 258.", "links": [{"title": "1931 Hawke's Bay earthquake", "link": "https://wikipedia.org/wiki/1931_Hawke%27s_Bay_earthquake"}]}, {"year": "1933", "text": "<PERSON> announces that the expansion of Lebensraum into Eastern Europe, and its ruthless Germanisation, are the ultimate geopolitical objectives of Nazi foreign policy.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> announces that the expansion of <i><a href=\"https://wikipedia.org/wiki/Lebensraum\" title=\"Lebensraum\">Lebensraum</a></i> into Eastern Europe, and its ruthless <a href=\"https://wikipedia.org/wiki/Germanisation\" title=\"Germanisation\">Germanisation</a>, are the ultimate geopolitical objectives of <a href=\"https://wikipedia.org/wiki/Nazi\" class=\"mw-redirect\" title=\"Nazi\">Nazi</a> foreign policy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> announces that the expansion of <i><a href=\"https://wikipedia.org/wiki/Lebensraum\" title=\"Lebensraum\">Lebensraum</a></i> into Eastern Europe, and its ruthless <a href=\"https://wikipedia.org/wiki/Germanisation\" title=\"Germanisation\">Germanisation</a>, are the ultimate geopolitical objectives of <a href=\"https://wikipedia.org/wiki/Nazi\" class=\"mw-redirect\" title=\"Nazi\">Nazi</a> foreign policy.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lebensraum", "link": "https://wikipedia.org/wiki/Lebensraum"}, {"title": "Germanisation", "link": "https://wikipedia.org/wiki/Germanisation"}, {"title": "Nazi", "link": "https://wikipedia.org/wiki/Nazi"}]}, {"year": "1943", "text": "The SS Dorchester is sunk by a German U-boat. Only 230 of 902 men aboard survive.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/SS_Dorchester\" title=\"SS Dorchester\">SS <i>Dorchester</i></a> is sunk by a German U-boat. Only 230 of 902 men aboard survive.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/SS_Dorchester\" title=\"SS Dorchester\">SS <i>Dorchester</i></a> is sunk by a German U-boat. Only 230 of 902 men aboard survive.", "links": [{"title": "SS Dorchester", "link": "https://wikipedia.org/wiki/SS_Dorchester"}]}, {"year": "1944", "text": "World War II: During the Gilbert and Marshall Islands campaign, U.S. Army and Marine forces seize Kwajalein Atoll from the defending Japanese garrison.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: During the <a href=\"https://wikipedia.org/wiki/Gilbert_and_Marshall_Islands_campaign\" title=\"Gilbert and Marshall Islands campaign\">Gilbert and Marshall Islands campaign</a>, U.S. <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">Army</a> and <a href=\"https://wikipedia.org/wiki/United_States_Marines\" class=\"mw-redirect\" title=\"United States Marines\">Marine</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Kwajalein\" title=\"Battle of Kwajalein\">seize Kwajalein Atoll</a> from the defending <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> garrison.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: During the <a href=\"https://wikipedia.org/wiki/Gilbert_and_Marshall_Islands_campaign\" title=\"Gilbert and Marshall Islands campaign\">Gilbert and Marshall Islands campaign</a>, U.S. <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">Army</a> and <a href=\"https://wikipedia.org/wiki/United_States_Marines\" class=\"mw-redirect\" title=\"United States Marines\">Marine</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Kwajalein\" title=\"Battle of Kwajalein\">seize Kwajalein Atoll</a> from the defending <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> garrison.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Gilbert and Marshall Islands campaign", "link": "https://wikipedia.org/wiki/<PERSON>_and_Marshall_Islands_campaign"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "United States Marines", "link": "https://wikipedia.org/wiki/United_States_Marines"}, {"title": "Battle of Kwajalein", "link": "https://wikipedia.org/wiki/Battle_of_Kwajalein"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}]}, {"year": "1945", "text": "World War II: As part of Operation Thunderclap, 1,000 B-17s of the Eighth Air Force bomb Berlin, a raid which kills between 2,500 and 3,000 and dehouses another 120,000.", "html": "1945 - World War II: As part of <a href=\"https://wikipedia.org/wiki/Bombing_of_Berlin_in_World_War_II#March_1944_to_April_1945\" title=\"Bombing of Berlin in World War II\">Operation Thunderclap</a>, 1,000 <a href=\"https://wikipedia.org/wiki/B-17_Flying_Fortress\" class=\"mw-redirect\" title=\"B-17 Flying Fortress\">B-17s</a> of the <a href=\"https://wikipedia.org/wiki/Eighth_Air_Force\" title=\"Eighth Air Force\">Eighth Air Force</a> bomb Berlin, a raid which kills between 2,500 and 3,000 and <a href=\"https://wikipedia.org/wiki/Dehousing\" title=\"Dehousing\">dehouses</a> another 120,000.", "no_year_html": "World War II: As part of <a href=\"https://wikipedia.org/wiki/Bombing_of_Berlin_in_World_War_II#March_1944_to_April_1945\" title=\"Bombing of Berlin in World War II\">Operation Thunderclap</a>, 1,000 <a href=\"https://wikipedia.org/wiki/B-17_Flying_Fortress\" class=\"mw-redirect\" title=\"B-17 Flying Fortress\">B-17s</a> of the <a href=\"https://wikipedia.org/wiki/Eighth_Air_Force\" title=\"Eighth Air Force\">Eighth Air Force</a> bomb Berlin, a raid which kills between 2,500 and 3,000 and <a href=\"https://wikipedia.org/wiki/Dehousing\" title=\"Dehousing\">dehouses</a> another 120,000.", "links": [{"title": "Bombing of Berlin in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Berlin_in_World_War_II#March_1944_to_April_1945"}, {"title": "B-17 Flying Fortress", "link": "https://wikipedia.org/wiki/B-17_Flying_Fortress"}, {"title": "Eighth Air Force", "link": "https://wikipedia.org/wiki/Eighth_Air_Force"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dehousing"}]}, {"year": "1945", "text": "World War II: The United States and the Philippine Commonwealth begin a month-long battle to retake Manila from Japan.", "html": "1945 - World War II: The United States and the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">Philippine Commonwealth</a> begin <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_(1945)\" title=\"Battle of Manila (1945)\">a month-long battle</a> to retake <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a> from <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>.", "no_year_html": "World War II: The United States and the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">Philippine Commonwealth</a> begin <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_(1945)\" title=\"Battle of Manila (1945)\">a month-long battle</a> to retake <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a> from <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>.", "links": [{"title": "Commonwealth of the Philippines", "link": "https://wikipedia.org/wiki/Commonwealth_of_the_Philippines"}, {"title": "Battle of Manila (1945)", "link": "https://wikipedia.org/wiki/Battle_of_Manila_(1945)"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}]}, {"year": "1953", "text": "The Batepá massacre occurred in São Tomé when the colonial administration and Portuguese landowners unleashed a wave of violence against the native creoles known as forros.", "html": "1953 - The <a href=\"https://wikipedia.org/wiki/Batep%C3%A1_massacre\" title=\"Batepá massacre\">Batepá massacre</a> occurred in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Tom%C3%A9\" title=\"São Tomé\">São <PERSON></a> when the colonial administration and Portuguese landowners unleashed a wave of violence against the native <a href=\"https://wikipedia.org/wiki/Creole_peoples#Portuguese_Africa\" title=\"Creole peoples\">creoles</a> known as <i><a href=\"https://wikipedia.org/wiki/Forro_Creole\" title=\"Forro Creole\">forros</a></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Batep%C3%A1_massacre\" title=\"Batepá massacre\">Batepá massacre</a> occurred in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Tom%C3%A9\" title=\"São Tomé\">São Tomé</a> when the colonial administration and Portuguese landowners unleashed a wave of violence against the native <a href=\"https://wikipedia.org/wiki/Creole_peoples#Portuguese_Africa\" title=\"Creole peoples\">creoles</a> known as <i><a href=\"https://wikipedia.org/wiki/Forro_Creole\" title=\"Forro Creole\">forros</a></i>.", "links": [{"title": "Batepá massacre", "link": "https://wikipedia.org/wiki/Batep%C3%A1_massacre"}, {"title": "São Tomé", "link": "https://wikipedia.org/wiki/S%C3%A3o_Tom%C3%A9"}, {"title": "Creole peoples", "link": "https://wikipedia.org/wiki/Creole_peoples#Portuguese_Africa"}, {"title": "Forro Creole", "link": "https://wikipedia.org/wiki/Forro_Creole"}]}, {"year": "1958", "text": "Founding of the Benelux Economic Union, creating a testing ground for a later European Economic Community.", "html": "1958 - Founding of the <a href=\"https://wikipedia.org/wiki/Benelux\" title=\"Benelux\">Benelux</a> Economic Union, creating a testing ground for a later <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a>.", "no_year_html": "Founding of the <a href=\"https://wikipedia.org/wiki/Benelux\" title=\"Benelux\">Benelux</a> Economic Union, creating a testing ground for a later <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a>.", "links": [{"title": "Benelux", "link": "https://wikipedia.org/wiki/Benelux"}, {"title": "European Economic Community", "link": "https://wikipedia.org/wiki/European_Economic_Community"}]}, {"year": "1959", "text": "Rock and roll musicians <PERSON>, <PERSON>, and <PERSON><PERSON> <PERSON><PERSON> \"The Big Bopper\" <PERSON> are killed in a plane crash along with the pilot near Clear Lake, Iowa, an event later known as The Day the Music Died.", "html": "1959 - Rock and roll musicians <a href=\"https://wikipedia.org/wiki/<PERSON>_Holly\" title=\"<PERSON> Holly\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/The_Big_Bopper\" title=\"The Big Bopper\">J. P. \"The Big Bopper\" Richardson</a> are killed in a plane crash along with the pilot near <a href=\"https://wikipedia.org/wiki/Clear_Lake,_Iowa\" title=\"Clear Lake, Iowa\">Clear Lake, Iowa</a>, an event later known as <a href=\"https://wikipedia.org/wiki/The_Day_the_Music_Died\" title=\"The Day the Music Died\">The Day the Music Died</a>.", "no_year_html": "Rock and roll musicians <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/The_Big_Bopper\" title=\"The Big Bopper\">J. P. \"The Big Bopper\" Richardson</a> are killed in a plane crash along with the pilot near <a href=\"https://wikipedia.org/wiki/Clear_Lake,_Iowa\" title=\"Clear Lake, Iowa\">Clear Lake, Iowa</a>, an event later known as <a href=\"https://wikipedia.org/wiki/The_Day_the_Music_Died\" title=\"The Day the Music Died\">The Day the Music Died</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Big Bopper", "link": "https://wikipedia.org/wiki/The_<PERSON>_Bopper"}, {"title": "Clear Lake, Iowa", "link": "https://wikipedia.org/wiki/Clear_Lake,_Iowa"}, {"title": "The Day the Music Died", "link": "https://wikipedia.org/wiki/The_Day_the_Music_Died"}]}, {"year": "1959", "text": "Sixty-five people are killed when American Airlines Flight 320 crashes into the East River on approach to LaGuardia Airport in New York City.", "html": "1959 - Sixty-five people are killed when <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_320\" title=\"American Airlines Flight 320\">American Airlines Flight 320</a> crashes into the <a href=\"https://wikipedia.org/wiki/East_River\" title=\"East River\">East River</a> on approach to <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "Sixty-five people are killed when <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_320\" title=\"American Airlines Flight 320\">American Airlines Flight 320</a> crashes into the <a href=\"https://wikipedia.org/wiki/East_River\" title=\"East River\">East River</a> on approach to <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "American Airlines Flight 320", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_320"}, {"title": "East River", "link": "https://wikipedia.org/wiki/East_River"}, {"title": "LaGuardia Airport", "link": "https://wikipedia.org/wiki/LaGuardia_Airport"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1960", "text": "British Prime Minister <PERSON> speaks of \"a wind of change\", signalling that his Government was likely to support decolonisation.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">British Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> speaks of \"a <a href=\"https://wikipedia.org/wiki/Wind_of_Change_(speech)\" title=\"Wind of Change (speech)\">wind of change</a>\", signalling that his Government was likely to support decolonisation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">British Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> speaks of \"a <a href=\"https://wikipedia.org/wiki/Wind_of_Change_(speech)\" title=\"Wind of Change (speech)\">wind of change</a>\", signalling that his Government was likely to support decolonisation.", "links": [{"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wind of Change (speech)", "link": "https://wikipedia.org/wiki/Wind_of_Change_(speech)"}]}, {"year": "1961", "text": "The United States Air Force begins Operation Looking Glass, and over the next 30 years, a \"Doomsday Plane\" is always in the air, with the capability of taking direct control of the United States' bombers and missiles in the event of the destruction of the SAC's command post.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> begins <a href=\"https://wikipedia.org/wiki/Operation_Looking_Glass\" title=\"Operation Looking Glass\">Operation Looking Glass</a>, and over the next 30 years, a \"Doomsday Plane\" is always in the air, with the capability of taking direct control of the United States' bombers and missiles in the event of the destruction of the <a href=\"https://wikipedia.org/wiki/Strategic_Air_Command\" title=\"Strategic Air Command\">SAC</a>'s command post.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> begins <a href=\"https://wikipedia.org/wiki/Operation_Looking_Glass\" title=\"Operation Looking Glass\">Operation Looking Glass</a>, and over the next 30 years, a \"Doomsday Plane\" is always in the air, with the capability of taking direct control of the United States' bombers and missiles in the event of the destruction of the <a href=\"https://wikipedia.org/wiki/Strategic_Air_Command\" title=\"Strategic Air Command\">SAC</a>'s command post.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Operation Looking Glass", "link": "https://wikipedia.org/wiki/Operation_Looking_Glass"}, {"title": "Strategic Air Command", "link": "https://wikipedia.org/wiki/Strategic_Air_Command"}]}, {"year": "1966", "text": "The Soviet Union's Luna 9 becomes the first spacecraft to make a soft landing on the Moon, and the first spacecraft to take pictures from the surface of the Moon.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>'s <a href=\"https://wikipedia.org/wiki/Luna_9\" title=\"Luna 9\">Luna 9</a> becomes the first spacecraft to make a soft landing on the Moon, and the first spacecraft to take pictures from the surface of the Moon.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>'s <a href=\"https://wikipedia.org/wiki/Luna_9\" title=\"Luna 9\">Luna 9</a> becomes the first spacecraft to make a soft landing on the Moon, and the first spacecraft to take pictures from the surface of the Moon.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Luna 9", "link": "https://wikipedia.org/wiki/Luna_9"}]}, {"year": "1971", "text": "New York Police Officer <PERSON> is shot during a drug bust in Brooklyn and survives to later testify against police corruption.", "html": "1971 - New York Police Officer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is shot during a drug bust in <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a> and survives to later testify against police corruption.", "no_year_html": "New York Police Officer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is shot during a drug bust in <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a> and survives to later testify against police corruption.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Brooklyn", "link": "https://wikipedia.org/wiki/Brooklyn"}]}, {"year": "1972", "text": "The first day of the seven-day 1972 Iran blizzard, which would kill at least 4,000 people, making it the deadliest snowstorm in history.", "html": "1972 - The first day of the seven-day <a href=\"https://wikipedia.org/wiki/1972_Iran_blizzard\" title=\"1972 Iran blizzard\">1972 Iran blizzard</a>, which would kill at least 4,000 people, making it the <a href=\"https://wikipedia.org/wiki/List_of_natural_disasters_by_death_toll#Blizzards\" title=\"List of natural disasters by death toll\">deadliest snowstorm in history</a>.", "no_year_html": "The first day of the seven-day <a href=\"https://wikipedia.org/wiki/1972_Iran_blizzard\" title=\"1972 Iran blizzard\">1972 Iran blizzard</a>, which would kill at least 4,000 people, making it the <a href=\"https://wikipedia.org/wiki/List_of_natural_disasters_by_death_toll#Blizzards\" title=\"List of natural disasters by death toll\">deadliest snowstorm in history</a>.", "links": [{"title": "1972 Iran blizzard", "link": "https://wikipedia.org/wiki/1972_Iran_blizzard"}, {"title": "List of natural disasters by death toll", "link": "https://wikipedia.org/wiki/List_of_natural_disasters_by_death_toll#Blizzards"}]}, {"year": "1984", "text": "Doctor <PERSON> and a research team at Harbor-UCLA Medical Center in the United States announce history's first embryo transfer, from one woman to another resulting in a live birth.", "html": "1984 - Doctor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a research team at Harbor-UCLA Medical Center in the United States announce history's first <a href=\"https://wikipedia.org/wiki/Embryo_transfer\" title=\"Embryo transfer\">embryo transfer</a>, from one woman to another resulting in a live birth.", "no_year_html": "Doctor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a research team at Harbor-UCLA Medical Center in the United States announce history's first <a href=\"https://wikipedia.org/wiki/Embryo_transfer\" title=\"Embryo transfer\">embryo transfer</a>, from one woman to another resulting in a live birth.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Embryo transfer", "link": "https://wikipedia.org/wiki/Embryo_transfer"}]}, {"year": "1984", "text": "Space Shuttle program: STS-41-B is launched using Space Shuttle Challenger.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-41-B\" title=\"STS-41-B\">STS-41-B</a> is launched using <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Space Shuttle Challenger</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-41-B\" title=\"STS-41-B\">STS-41-B</a> is launched using <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Space Shuttle Challenger</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-41-B", "link": "https://wikipedia.org/wiki/STS-41-B"}, {"title": "Space Shuttle Challenger", "link": "https://wikipedia.org/wiki/Space_Shuttle_Challenger"}]}, {"year": "1989", "text": "After a stroke two weeks previously, South African President <PERSON><PERSON> <PERSON><PERSON> resigns as leader of the National Party, but stays on as president for six more months.", "html": "1989 - After a <a href=\"https://wikipedia.org/wiki/Stroke\" title=\"Stroke\">stroke</a> two weeks previously, <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">South African President</a> <a href=\"https://wikipedia.org/wiki/P._W._Botha\" title=\"P. W. Botha\"><PERSON><PERSON> <PERSON><PERSON></a> resigns as leader of the <a href=\"https://wikipedia.org/wiki/National_Party_(South_Africa)\" title=\"National Party (South Africa)\">National Party</a>, but stays on as president for six more months.", "no_year_html": "After a <a href=\"https://wikipedia.org/wiki/Stroke\" title=\"Stroke\">stroke</a> two weeks previously, <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">South African President</a> <a href=\"https://wikipedia.org/wiki/P._W._Botha\" title=\"P. W. Botha\"><PERSON><PERSON> <PERSON><PERSON></a> resigns as leader of the <a href=\"https://wikipedia.org/wiki/National_Party_(South_Africa)\" title=\"National Party (South Africa)\">National Party</a>, but stays on as president for six more months.", "links": [{"title": "Stroke", "link": "https://wikipedia.org/wiki/Stroke"}, {"title": "President of South Africa", "link": "https://wikipedia.org/wiki/President_of_South_Africa"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "National Party (South Africa)", "link": "https://wikipedia.org/wiki/National_Party_(South_Africa)"}]}, {"year": "1989", "text": "A military coup overthrows <PERSON>, dictator of Paraguay since 1954.", "html": "1989 - A <a href=\"https://wikipedia.org/wiki/1989_Paraguayan_coup_d%27%C3%A9tat\" title=\"1989 Paraguayan coup d'état\">military coup</a> overthrows <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, dictator of <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a> since <a href=\"https://wikipedia.org/wiki/1954\" title=\"1954\">1954</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1989_Paraguayan_coup_d%27%C3%A9tat\" title=\"1989 Paraguayan coup d'état\">military coup</a> overthrows <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, dictator of <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a> since <a href=\"https://wikipedia.org/wiki/1954\" title=\"1954\">1954</a>.", "links": [{"title": "1989 Paraguayan coup d'état", "link": "https://wikipedia.org/wiki/1989_Paraguayan_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Paraguay", "link": "https://wikipedia.org/wiki/Paraguay"}, {"title": "1954", "link": "https://wikipedia.org/wiki/1954"}]}, {"year": "1994", "text": "Space Shuttle program: STS-60 is launched, carrying <PERSON>, the first Russian cosmonaut to fly aboard the Shuttle.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-60\" title=\"STS-60\">STS-60</a> is launched, carrying <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first Russian cosmonaut to fly aboard the Shuttle.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-60\" title=\"STS-60\">STS-60</a> is launched, carrying <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first Russian cosmonaut to fly aboard the Shuttle.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-60", "link": "https://wikipedia.org/wiki/STS-60"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "Astronaut <PERSON> becomes the first woman to pilot the Space Shuttle as mission STS-63 gets underway from Kennedy Space Center in Florida.", "html": "1995 - Astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to pilot the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle</a> as mission <a href=\"https://wikipedia.org/wiki/STS-63\" title=\"STS-63\">STS-63</a> gets underway from <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>.", "no_year_html": "Astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to pilot the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle</a> as mission <a href=\"https://wikipedia.org/wiki/STS-63\" title=\"STS-63\">STS-63</a> gets underway from <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-63", "link": "https://wikipedia.org/wiki/STS-63"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}]}, {"year": "1998", "text": "Cavalese cable car disaster: A United States military pilot causes the death of 20 people when his low-flying plane cuts the cable of a cable-car near Trento, Italy.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/1998_Cavalese_cable_car_crash\" title=\"1998 Cavalese cable car crash\">Cavalese cable car disaster</a>: A United States military pilot causes the death of 20 people when his low-flying plane cuts the cable of a cable-car near <a href=\"https://wikipedia.org/wiki/Trento\" title=\"Trento\">Trento</a>, Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1998_Cavalese_cable_car_crash\" title=\"1998 Cavalese cable car crash\">Cavalese cable car disaster</a>: A United States military pilot causes the death of 20 people when his low-flying plane cuts the cable of a cable-car near <a href=\"https://wikipedia.org/wiki/Trento\" title=\"Trento\">Trento</a>, Italy.", "links": [{"title": "1998 Cavalese cable car crash", "link": "https://wikipedia.org/wiki/1998_Cavalese_cable_car_crash"}, {"title": "Trento", "link": "https://wikipedia.org/wiki/Trento"}]}, {"year": "2005", "text": "One hundred five people are killed when Kam Air Flight 904 crashes in the Pamir Mountains in Afghanistan.", "html": "2005 - One hundred five people are killed when <a href=\"https://wikipedia.org/wiki/Kam_Air_Flight_904\" title=\"Kam Air Flight 904\">Kam Air Flight 904</a> crashes in the <a href=\"https://wikipedia.org/wiki/Pamir_Mountains\" title=\"Pamir Mountains\">Pamir Mountains</a> in Afghanistan.", "no_year_html": "One hundred five people are killed when <a href=\"https://wikipedia.org/wiki/Kam_Air_Flight_904\" title=\"Kam Air Flight 904\">Kam Air Flight 904</a> crashes in the <a href=\"https://wikipedia.org/wiki/Pamir_Mountains\" title=\"Pamir Mountains\">Pamir Mountains</a> in Afghanistan.", "links": [{"title": "Kam Air Flight 904", "link": "https://wikipedia.org/wiki/Kam_Air_Flight_904"}, {"title": "Pamir Mountains", "link": "https://wikipedia.org/wiki/Pamir_Mountains"}]}, {"year": "2007", "text": "A Baghdad market bombing kills at least 135 people and injures a further 339.", "html": "2007 - A <a href=\"https://wikipedia.org/wiki/February_2007_Al-Saydiya_market_bombing\" title=\"February 2007 Al-Saydiya market bombing\">Baghdad market bombing</a> kills at least 135 people and injures a further 339.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/February_2007_Al-Saydiya_market_bombing\" title=\"February 2007 Al-Saydiya market bombing\">Baghdad market bombing</a> kills at least 135 people and injures a further 339.", "links": [{"title": "February 2007 Al-Saydiya market bombing", "link": "https://wikipedia.org/wiki/February_2007_Al-Saydiya_market_bombing"}]}, {"year": "2014", "text": "Two people are shot and killed and 29 students are taken hostage at a high school in Moscow, Russia.", "html": "2014 - Two people are <a href=\"https://wikipedia.org/wiki/2014_Moscow_school_shooting\" title=\"2014 Moscow school shooting\">shot and killed</a> and 29 students are taken hostage at a high school in <a href=\"https://wikipedia.org/wiki/Moscow,_Russia\" class=\"mw-redirect\" title=\"Moscow, Russia\">Moscow, Russia</a>.", "no_year_html": "Two people are <a href=\"https://wikipedia.org/wiki/2014_Moscow_school_shooting\" title=\"2014 Moscow school shooting\">shot and killed</a> and 29 students are taken hostage at a high school in <a href=\"https://wikipedia.org/wiki/Moscow,_Russia\" class=\"mw-redirect\" title=\"Moscow, Russia\">Moscow, Russia</a>.", "links": [{"title": "2014 Moscow school shooting", "link": "https://wikipedia.org/wiki/2014_Moscow_school_shooting"}, {"title": "Moscow, Russia", "link": "https://wikipedia.org/wiki/Moscow,_Russia"}]}, {"year": "2023", "text": "2023 Ohio train derailment: A freight train containing vinyl chloride and other hazardous materials derails and burns in East Palestine, Ohio, United States, releasing hydrogen chloride and phosgene into the air and contaminating the Ohio River.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/East_Palestine,_Ohio,_train_derailment\" title=\"East Palestine, Ohio, train derailment\">2023 Ohio train derailment</a>: A freight train containing <a href=\"https://wikipedia.org/wiki/Vinyl_chloride\" title=\"Vinyl chloride\">vinyl chloride</a> and other hazardous materials derails and burns in <a href=\"https://wikipedia.org/wiki/East_Palestine,_Ohio\" title=\"East Palestine, Ohio\">East Palestine, Ohio</a>, United States, releasing <a href=\"https://wikipedia.org/wiki/Hydrogen_chloride\" title=\"Hydrogen chloride\">hydrogen chloride</a> and <a href=\"https://wikipedia.org/wiki/Phosgene\" title=\"Phosgene\">phosgene</a> into the air and contaminating the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Palestine,_Ohio,_train_derailment\" title=\"East Palestine, Ohio, train derailment\">2023 Ohio train derailment</a>: A freight train containing <a href=\"https://wikipedia.org/wiki/Vinyl_chloride\" title=\"Vinyl chloride\">vinyl chloride</a> and other hazardous materials derails and burns in <a href=\"https://wikipedia.org/wiki/East_Palestine,_Ohio\" title=\"East Palestine, Ohio\">East Palestine, Ohio</a>, United States, releasing <a href=\"https://wikipedia.org/wiki/Hydrogen_chloride\" title=\"Hydrogen chloride\">hydrogen chloride</a> and <a href=\"https://wikipedia.org/wiki/Phosgene\" title=\"Phosgene\">phosgene</a> into the air and contaminating the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a>.", "links": [{"title": "East Palestine, Ohio, train derailment", "link": "https://wikipedia.org/wiki/East_Palestine,_Ohio,_train_derailment"}, {"title": "Vinyl chloride", "link": "https://wikipedia.org/wiki/Vinyl_chloride"}, {"title": "East Palestine, Ohio", "link": "https://wikipedia.org/wiki/East_Palestine,_Ohio"}, {"title": "Hydrogen chloride", "link": "https://wikipedia.org/wiki/Hydrogen_chloride"}, {"title": "Phosgene", "link": "https://wikipedia.org/wiki/Phosgene"}, {"title": "Ohio River", "link": "https://wikipedia.org/wiki/Ohio_River"}]}], "Births": [{"year": "1338", "text": "<PERSON> of Bourbon (d. 1378)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Bourbon\"><PERSON> of Bourbon</a> (d. 1378)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Bourbon\"><PERSON> of Bourbon</a> (d. 1378)", "links": [{"title": "<PERSON> of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bourbon"}]}, {"year": "1393", "text": "<PERSON>, 2nd Earl of Northumberland, English nobleman and military commander (d. 1455)", "html": "1393 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Northumberland\" title=\"<PERSON>, 2nd Earl of Northumberland\"><PERSON>, 2nd Earl of Northumberland</a>, English nobleman and military commander (d. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Northumberland\" title=\"<PERSON>, 2nd Earl of Northumberland\"><PERSON>, 2nd Earl of Northumberland</a>, English nobleman and military commander (d. 1455)", "links": [{"title": "<PERSON>, 2nd Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Northumberland"}]}, {"year": "1428", "text": "<PERSON>, Queen of Cyprus (d. 1458) [citation needed]", "html": "1428 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pa<PERSON>ologina\" title=\"<PERSON> Pa<PERSON>olo<PERSON>\"><PERSON></a>, Queen of Cyprus (d. 1458) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ologin<PERSON>\" title=\"<PERSON> Palaiologina\"><PERSON></a>, Queen of Cyprus (d. 1458) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gina"}]}, {"year": "1478", "text": "<PERSON>, 3rd Duke of Buckingham (d. 1521)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_of_Buckingham\" title=\"<PERSON>, 3rd Duke of Buckingham\"><PERSON>, 3rd Duke of Buckingham</a> (d. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_of_Buckingham\" title=\"<PERSON>, 3rd Duke of Buckingham\"><PERSON>, 3rd Duke of Buckingham</a> (d. 1521)", "links": [{"title": "<PERSON>, 3rd Duke of Buckingham", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Buckingham"}]}, {"year": "1504", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (d. 1577)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/Scipione_Rebiba\" title=\"Scipione Rebiba\"><PERSON><PERSON><PERSON> Re<PERSON></a>, Italian cardinal (d. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scipione_Rebiba\" title=\"Scipione Rebiba\"><PERSON><PERSON><PERSON> Re<PERSON></a>, Italian cardinal (d. 1577)", "links": [{"title": "Scipione Rebiba", "link": "https://wikipedia.org/wiki/Scipione_Rebiba"}]}, {"year": "1677", "text": "<PERSON>, Czech architect, designed the Karlova Koruna Chateau (d. 1723)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Koruna Chateau\"><PERSON><PERSON></a> (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>teau\" title=\"<PERSON><PERSON> Koruna Chateau\"><PERSON><PERSON> Koruna Cha<PERSON></a> (d. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON><PERSON>, Spanish admiral (d. 1741)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>zo\"><PERSON><PERSON></a>, Spanish admiral (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish admiral (d. 1741)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, Prussian general (d. 1773)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian general (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Se<PERSON>litz\"><PERSON></a>, Prussian general (d. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON>, Austrian composer and theorist (d. 1809)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and theorist (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and theorist (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON>, American soldier and politician, 1st United States Postmaster General (d. 1813)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Postmaster General", "link": "https://wikipedia.org/wiki/United_States_Postmaster_General"}]}, {"year": "1757", "text": "<PERSON>, Italian ophthalmologist and surgeon (d. 1833)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ophthalmologist and surgeon (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ophthalmologist and surgeon (d. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, German author (d. 1847)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, Scottish physician and author (d. 1836)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Scottish physician and author (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Scottish physician and author (d. 1836)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "1780", "text": "<PERSON><PERSON><PERSON> <PERSON>, Aromanian grammarian and professor (d. uncertain)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Aromanian grammarian and professor (d. uncertain)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Aromanian grammarian and professor (d. uncertain)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, English scientist (d. 1852)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, Venezuelan general and politician, 2nd President of Bolivia (d. 1830)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>cre"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1807", "text": "<PERSON>, American general and politician (d. 1891)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, German pianist, composer, and conductor (d. 1847)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, American journalist and politician (d. 1872)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, Indian credited with starting the Non-cooperation movement", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indian credited with starting the <a href=\"https://wikipedia.org/wiki/Non-cooperation_movement_(1909%E2%80%9322)\" class=\"mw-redirect\" title=\"Non-cooperation movement (1909-22)\">Non-cooperation movement</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indian credited with starting the <a href=\"https://wikipedia.org/wiki/Non-cooperation_movement_(1909%E2%80%9322)\" class=\"mw-redirect\" title=\"Non-cooperation movement (1909-22)\">Non-cooperation movement</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Non-cooperation movement (1909-22)", "link": "https://wikipedia.org/wiki/Non-cooperation_movement_(1909%E2%80%9322)"}]}, {"year": "1815", "text": "<PERSON>, 5th President of Liberia (d. 1872)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 5th President of Liberia (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 5th President of Liberia (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, French geologist and mineralogist (d. 1881)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"A<PERSON>lle <PERSON>\"><PERSON><PERSON><PERSON></a>, French geologist and mineralogist (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Achille <PERSON>\"><PERSON><PERSON><PERSON></a>, French geologist and mineralogist (d. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON><PERSON>, French pianist and composer (d. 1864)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Prudent\" title=\"<PERSON><PERSON>rudent\"><PERSON><PERSON></a>, French pianist and composer (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Prudent\" title=\"É<PERSON> Prudent\"><PERSON><PERSON></a>, French pianist and composer (d. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Prudent"}]}, {"year": "1821", "text": "<PERSON>, American physician and educator (d. 1910)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and educator (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and educator (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON><PERSON>, American explorer and educator (d. 1894)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American explorer and educator (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American explorer and educator (d. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, English journalist and businessman (d. 1877)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and businessman (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and businessman (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON><PERSON>, 3rd Marquess of Salisbury, English politician, Prime Minister of the United Kingdom (d. 1903)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_3rd_Marquess_of_Salisbury\" title=\"<PERSON>, 3rd Marquess of Salisbury\"><PERSON>, 3rd Marquess of Salisbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_3rd_Marquess_of_Salisbury\" title=\"<PERSON>, 3rd Marquess of Salisbury\"><PERSON>, 3rd Marquess of Salisbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1903)", "links": [{"title": "<PERSON><PERSON>, 3rd Marquess of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>,_3rd_Marquess_of_Salisbury"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1840", "text": "<PERSON>, Scottish-Australian politician, 19th Premier of Victoria (d. 1911)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1911)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1842", "text": "<PERSON>, American composer and poet (d. 1881)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and poet (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and poet (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American-Canadian businessman (d. 1915)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian businessman (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian businessman (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Italian sculptor, designed the Vulcan statue (d. 1935)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor, designed the <a href=\"https://wikipedia.org/wiki/Vulcan_statue\" title=\"Vulcan statue\">Vulcan statue</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor, designed the <a href=\"https://wikipedia.org/wiki/Vulcan_statue\" title=\"Vulcan statue\">Vulcan statue</a> (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Vulcan statue", "link": "https://wikipedia.org/wiki/Vulcan_statue"}]}, {"year": "1859", "text": "<PERSON>, German engineer, designed the Junkers J 1 (d. 1935)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, designed the <a href=\"https://wikipedia.org/wiki/Junkers_J_1\" title=\"Junkers J 1\">Junkers J 1</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, designed the <a href=\"https://wikipedia.org/wiki/Junkers_J_1\" title=\"Junkers J 1\">Junkers J 1</a> (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Junkers J 1", "link": "https://wikipedia.org/wiki/Junkers_J_1"}]}, {"year": "1862", "text": "<PERSON>, American lawyer and judge (d. 1946)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>Reynolds\" title=\"<PERSON>eynolds\"><PERSON></a>, American lawyer and judge (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>cReynolds\" title=\"<PERSON>cReynolds\"><PERSON></a>, American lawyer and judge (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American biologist, educator and zoologist (d. 1923)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(zoologist)\" title=\"<PERSON> (zoologist)\"><PERSON></a>, American biologist, educator and zoologist (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(zoologist)\" title=\"<PERSON> (zoologist)\"><PERSON></a>, American biologist, educator and zoologist (d. 1923)", "links": [{"title": "<PERSON> (zoologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(zoologist)"}]}, {"year": "1872", "text": "<PERSON>, American baseball player and manager (d. 1934)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American novelist, poet, playwright, (d. 1946)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, playwright, (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, playwright, (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, New Zealand soldier and politician, 21st Prime Minister of New Zealand (d. 1943)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand soldier and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand soldier and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gordon_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Ukrainian Soviet revolutionary and politician (d. 1958)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian Soviet revolutionary and politician (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian Soviet revolutionary and politician (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Austrian pharmacist and poet (d. 1914)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pharmacist and poet (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pharmacist and poet (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_Trakl"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Estonian poet, playwright, and critic (d. 1977)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet, playwright, and critic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet, playwright, and critic (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Danish director and screenwriter (d. 1968)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director and screenwriter (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Finnish lawyer, politician and the Governor of the Bank of Finland; 5th President of Finland (d. 1956)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Rist<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer, politician and the Governor of the <a href=\"https://wikipedia.org/wiki/Bank_of_Finland\" title=\"Bank of Finland\">Bank of Finland</a>; 5th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer, politician and the Governor of the <a href=\"https://wikipedia.org/wiki/Bank_of_Finland\" title=\"Bank of Finland\">Bank of Finland</a>; 5th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rist<PERSON>_Ryti"}, {"title": "Bank of Finland", "link": "https://wikipedia.org/wiki/Bank_of_Finland"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1892", "text": "<PERSON>, Spanish physician and politician, 67th Prime Minister of Spain (d. 1956)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish physician and politician, 67th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish physician and politician, 67th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_<PERSON>egr%C3%ADn"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1893", "text": "<PERSON>, Algerian-French mathematician and academic (d. 1978)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French mathematician and academic (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French mathematician and academic (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American painter and illustrator (d. 1978)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Finnish architect, designed the Finlandia Hall and Aalto Theatre (d. 1976)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish architect, designed the <a href=\"https://wikipedia.org/wiki/Finlandia_Hall\" title=\"Finlandia Hall\">Finlandia Hall</a> and <a href=\"https://wikipedia.org/wiki/Aalto_Theatre\" title=\"Aalto Theatre\">Aalto Theatre</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish architect, designed the <a href=\"https://wikipedia.org/wiki/Finlandia_Hall\" title=\"Finlandia Hall\">Finlandia Hall</a> and <a href=\"https://wikipedia.org/wiki/Aalto_Theatre\" title=\"Aalto Theatre\">Aalto Theatre</a> (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Finlandia Hall", "link": "https://wikipedia.org/wiki/Finlandia_Hall"}, {"title": "Aalto Theatre", "link": "https://wikipedia.org/wiki/Aalto_Theatre"}]}, {"year": "1899", "text": "<PERSON> Filho, Brazilian journalist, lawyer, and politician, 18th President of Brazil (d. 1970)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Caf%C3%A9_Filho\" title=\"Café Filho\">Café Filho</a>, Brazilian journalist, lawyer, and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caf%C3%A9_Fil<PERSON>\" title=\"Café Filho\">Café Filho</a>, Brazilian journalist, lawyer, and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1970)", "links": [{"title": "Café Filho", "link": "https://wikipedia.org/wiki/Caf%C3%A9_Fi<PERSON>ho"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1900", "text": "<PERSON>, English-American singer (d. 1984)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, 14th Duke of Hamilton, Scottish soldier, pilot, and politician (d. 1973)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_Duke_<PERSON>_Hamilton\" title=\"<PERSON>, 14th Duke of Hamilton\"><PERSON>, 14th Duke <PERSON> Hamilton</a>, Scottish soldier, pilot, and politician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Duke_<PERSON>_Hamilton\" title=\"<PERSON>, 14th Duke of Hamilton\"><PERSON>, 14th Duke <PERSON> Hamilton</a>, Scottish soldier, pilot, and politician (d. 1973)", "links": [{"title": "<PERSON>, 14th Duke of Hamilton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American gangster (d. 1934)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Pretty_Boy_Floyd\" title=\"Pretty Boy Floyd\">Pretty Boy Floyd</a>, American gangster (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pretty_Boy_Floyd\" title=\"Pretty Boy Floyd\">Pretty Boy Floyd</a>, American gangster (d. 1934)", "links": [{"title": "Pretty Boy Floyd", "link": "https://wikipedia.org/wiki/Pretty_Boy_Floyd"}]}, {"year": "1905", "text": "<PERSON>, Estonian linguist and academic (d. 1990)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian linguist and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian linguist and academic (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Swedish-American mathematician and academic (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American mathematician and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American mathematician and academic (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Indian-English author and activist (d. 1989)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English author and activist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English author and activist (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American author and philanthropist (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and philanthropist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and philanthropist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, French lawyer and director (d. 1989)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and director (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and director (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1909", "text": "<PERSON>, French mystic and philosopher (d. 1943)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mystic and philosopher (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mystic and philosopher (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, French organist and composer (d. 1940)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French organist and composer (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French organist and composer (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, French anthropologist and politician (d. 1990)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French anthropologist and politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French anthropologist and politician (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actress, singer, and dancer (d. 2018)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Estonian wrestler and hammer thrower (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler and hammer thrower (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler and hammer thrower (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Polish-Israeli rabbi and general (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Israeli rabbi and general (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Israeli rabbi and general (d. 1994)", "links": [{"title": "S<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shlomo_<PERSON>n"}]}, {"year": "1918", "text": "<PERSON>, American actor and producer (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American runner, baseball player, and manager (d. 1994)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner, baseball player, and manager (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner, baseball player, and manager (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American physician and author (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Canadian lawyer and politician, 25th Lieutenant Governor of Quebec (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1924", "text": "<PERSON><PERSON> <PERSON><PERSON>, English historian and author (d. 1993)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and author (d. 1993)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor and comedian (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, German lawyer and politician, 8th Mayor of Berlin (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Governing_Mayor_of_Berlin\" title=\"Governing Mayor of Berlin\">Mayor of Berlin</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Governing_Mayor_of_Berlin\" title=\"Governing Mayor of Berlin\">Mayor of Berlin</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governing Mayor of Berlin", "link": "https://wikipedia.org/wiki/Governing_Mayor_of_Berlin"}]}, {"year": "1927", "text": "<PERSON>, American actor, director, and screenwriter (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Filipino journalist and politician, 21st President of the Senate of the Philippines (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>le"}, {"title": "President of the Senate of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines"}]}, {"year": "1933", "text": "<PERSON>, American lawyer and politician (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Argentinian actor and screenwriter (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3"}]}, {"year": "1935", "text": "<PERSON> \"Guitar\" <PERSON>, American blues, soul, and funk singer-songwriter and guitarist (d. 1996)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Guitar%22_<PERSON>\" title='<PERSON> \"Guitar\" Watson'><PERSON> \"Guitar\" <PERSON></a>, American blues, soul, and funk singer-songwriter and guitarist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Guitar%22_<PERSON>\" title='<PERSON> \"Guitar\" Watson'><PERSON> \"Guitar\" <PERSON></a>, American blues, soul, and funk singer-songwriter and guitarist (d. 1996)", "links": [{"title": "<PERSON> \"Guitar\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22G<PERSON>ar%22_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Australian cricketer and coach", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1937", "text": "<PERSON>, Swiss author and photographer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actor (d. 1982)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American boxer and trainer (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer and trainer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer and trainer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American director, producer, and screenwriter (d. 2016)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American football player and sportscaster", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Jr., American wrestler and trainer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American wrestler and trainer", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Dan<PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Dan<PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American football player and sportscaster", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American novelist, essayist, and poet (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English musician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian actor and director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 2024)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1948", "text": "<PERSON>, East Timorese Roman Catholic bishop and Nobel Peace Prize laureate", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, East Timorese <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> bishop and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, East Timorese <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> bishop and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Swedish author and playwright (d. 2015)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and playwright (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and playwright (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American golfer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1950", "text": "<PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fairchild\" title=\"Morgan Fairchild\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morgan_Fairchild\" title=\"Morgan Fairchild\"><PERSON></a>, American actress", "links": [{"title": "Morgan Fairchild", "link": "https://wikipedia.org/wiki/<PERSON>_Fairchild"}]}, {"year": "1950", "text": "<PERSON>, Japanese-English actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian radio and television host (d. 2020)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio and television host (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio and television host (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Haitian footballer (d. 1993)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Ars%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian footballer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ars%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian footballer (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ars%C3%A8ne_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Lithuanian footballer and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eugeni<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American journalist and author (d. 2014)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball player and sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor and comedian", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nathan Lane\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Nathan Lane\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American musician and songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American mathematician, geneticist, and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, geneticist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, geneticist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Jr., American commander, pilot, and astronaut", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American commander, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American commander, pilot, and astronaut", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1958", "text": "<PERSON>, American economist and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, English musician and songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Colombian economist and politician, 67th Colombian Minister of Finance", "html": "1959 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_Iv%C3%A1n_Zuluaga\" title=\"Óscar <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Colombian economist and politician, 67th <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_and_Public_Credit_(Colombia)\" class=\"mw-redirect\" title=\"Ministry of Finance and Public Credit (Colombia)\">Colombian Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_Iv%C3%A1n_Zuluaga\" title=\"Óscar <PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian economist and politician, 67th <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_and_Public_Credit_(Colombia)\" class=\"mw-redirect\" title=\"Ministry of Finance and Public Credit (Colombia)\">Colombian Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_Iv%C3%A1n_Zuluaga"}, {"title": "Ministry of Finance and Public Credit (Colombia)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_and_Public_Credit_(Colombia)"}]}, {"year": "1960", "text": "<PERSON>, American wrestler", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, German footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6w\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6w\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joachim_L%C3%B6w"}]}, {"year": "1961", "text": "<PERSON>, American singer and actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress, singer, and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Vietnamese politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/V%C5%A9_%C4%90%E1%BB%A9c_%C4%90am\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C5%A9_%C4%90%E1%BB%A9c_%C4%90am\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C5%A9_%C4%90%E1%BB%A9c_%C4%90am"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian economist and academic", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Rajan\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Rajan\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Estonian historian, journalist, and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Indrek_Tarand\" title=\"Indrek Tarand\">In<PERSON><PERSON></a>, Estonian historian, journalist, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indrek_Tarand\" title=\"Indrek Tarand\">In<PERSON><PERSON></a>, Estonian historian, journalist, and politician", "links": [{"title": "Indrek <PERSON>", "link": "https://wikipedia.org/wiki/Indrek_Tarand"}]}, {"year": "1965", "text": "<PERSON>, Portuguese politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American actress and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, New Zealand cricketer and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and sportscaster", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1967", "text": "<PERSON>, English footballer and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Finnish footballer and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Paate<PERSON>\"><PERSON><PERSON></a>, Finnish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>en"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Serbian-American basketball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vlade_Divac"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Lebanese singer, songwriter, and composer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese singer, songwriter, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese singer, songwriter, and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Ku%C4%8Dera\" title=\"Fr<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Ku%C4%8Dera\" title=\"Fr<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_Ku%C4%8Dera"}]}, {"year": "1969", "text": "<PERSON>, American soldier, lawyer, and politician, 44th Attorney General of Delaware (d. 2015)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 44th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Delaware\" title=\"Attorney General of Delaware\">Attorney General of Delaware</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 44th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Delaware\" title=\"Attorney General of Delaware\">Attorney General of Delaware</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney General of Delaware", "link": "https://wikipedia.org/wiki/Attorney_General_of_Delaware"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, South African golfer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>tie<PERSON>_<PERSON>n"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Colombian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_C%C3%B3rdoba\" title=\"Óscar Córdoba\"><PERSON><PERSON><PERSON> Córdoba</a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_C%C3%B3rdoba\" title=\"Óscar Córdoba\"><PERSON><PERSON><PERSON> Córdoba</a>, Colombian footballer", "links": [{"title": "Óscar Córdoba", "link": "https://wikipedia.org/wiki/%C3%93scar_C%C3%B3rdoba"}]}, {"year": "1970", "text": "<PERSON>, English actor, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Davis"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English playwright (d. 1999)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, South Korean actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-cheon\" title=\"<PERSON>-cheon\"><PERSON>-<PERSON>eon</a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-cheon\" title=\"<PERSON>-cheon\"><PERSON>cheon</a>, South Korean actor", "links": [{"title": "<PERSON>eon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-cheon"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Danish pianist and composer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Mexican journalist and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sod"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Omani-Australian actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Omani-Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Omani-Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor, comedian, and musician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese fashion model and actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese fashion model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese fashion model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American actress and model", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Maitland_Ward\" title=\"Maitland Ward\">Maitland Ward</a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maitland_Ward\" title=\"Maitland Ward\"><PERSON><PERSON> Ward</a>, American actress and model", "links": [{"title": "Maitland Ward", "link": "https://wikipedia.org/wiki/Maitland_Ward"}]}, {"year": "1977", "text": "<PERSON>, Puerto Rican singer, songwriter, rapper, actor and record producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Daddy_Yankee\" title=\"Daddy Yankee\">Daddy <PERSON></a>, Puerto Rican singer, songwriter, rapper, actor and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Daddy_Yankee\" title=\"Daddy Yankee\">Daddy Yankee</a>, Puerto Rican singer, songwriter, rapper, actor and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Daddy_Yankee"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Mare<PERSON>_%C5%BDidlick%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mare<PERSON>_%C5%BDidlick%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marek_%C5%BDidlick%C3%BD"}]}, {"year": "1978", "text": "<PERSON>, Spanish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, British-Lebanese barrister and activist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-Lebanese barrister and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-Lebanese barrister and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>al_Clooney"}]}, {"year": "1982", "text": "<PERSON>, American wrestler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian speed skater", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%88ve_<PERSON>t\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%88ve_<PERSON>t\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marie-%C3%88ve_<PERSON><PERSON>t"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American fraudster, founder of Theranos", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fraudster, founder of <a href=\"https://wikipedia.org/wiki/Theranos\" title=\"Theranos\">Theranos</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fraudster, founder of <a href=\"https://wikipedia.org/wiki/Theranos\" title=\"Theranos\">Theranos</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Theranos", "link": "https://wikipedia.org/wiki/Theranos"}]}, {"year": "1984", "text": "<PERSON>, American actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian wrestler and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Belarusian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Canadian speed skater", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Albanian singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/El<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> G<PERSON>ta\"><PERSON><PERSON></a>, Albanian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El<PERSON>_<PERSON>ta\" title=\"<PERSON><PERSON> Gjata\"><PERSON><PERSON></a>, Albanian singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elvana_G<PERSON>ta"}]}, {"year": "1988", "text": "<PERSON>, South Korean singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyun\" title=\"<PERSON> Kyu-hyun\"><PERSON>yun</a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyun\" title=\"<PERSON> Kyu-hyun\"><PERSON>hyun</a>, South Korean singer", "links": [{"title": "<PERSON>yun", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyun"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Chinese singer and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Chinese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Chinese singer and actress", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Slobodan_Rajkovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slobodan_Rajkovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Slobodan_Rajkovi%C4%87"}]}, {"year": "1990", "text": "<PERSON>, American-Jamaican singer-songwriter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Jamaican singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Jamaican singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (running back)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Rougned_Odor\" title=\"Rougned Odor\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rougned_Odor\" title=\"Rougned Odor\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rougned_Odor"}]}, {"year": "1995", "text": "<PERSON>, Japanese actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Tsuchiya\" title=\"<PERSON> Tsuchiya\"><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T<PERSON>chiya\" title=\"Tao Tsuchiya\"><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Japanese actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON>\" title=\"Tre <PERSON>\"><PERSON><PERSON> <PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tre <PERSON>\"><PERSON><PERSON> <PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tre_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2001)\" title=\"<PERSON> (footballer, born 2001)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2001)\" title=\"<PERSON> (footballer, born 2001)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 2001)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2001)"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American basketball player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "6", "text": "<PERSON>, emperor of the Han Dynasty (b. 9 BC)", "html": "6 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Han\" title=\"Emperor <PERSON> of Han\"><PERSON></a>, emperor of the Han Dynasty (b. 9 BC)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>\" title=\"Emperor <PERSON> of Han\"><PERSON></a>, emperor of the Han Dynasty (b. 9 BC)", "links": [{"title": "Emperor <PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "456", "text": "<PERSON><PERSON><PERSON> II, ruler of Tikal", "html": "456 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27awiil_II\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>wi<PERSON> II\"><PERSON><PERSON><PERSON> II</a>, ruler of Tikal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27awiil_II\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>wiil II\"><PERSON><PERSON><PERSON> II</a>, ruler of Tikal", "links": [{"title": "<PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_K%27awiil_II"}]}, {"year": "639", "text": "<PERSON><PERSON><PERSON><PERSON>, ruler of Piedras Negras", "html": "639 - <a href=\"https://wikipedia.org/wiki/K%27inich_Yo%27nal_Ahk_I\" class=\"mw-redirect\" title=\"K'inich Yo'nal Ahk I\"><PERSON>'inich Yo'nal Ah<PERSON> I</a>, ruler of Piedras Negras", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%27inich_Yo%27nal_Ahk_I\" class=\"mw-redirect\" title=\"K'inich Yo'nal Ahk I\"><PERSON><PERSON>inich Yo'nal Ah<PERSON> I</a>, ruler of Piedras Negras", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>nal <PERSON> I", "link": "https://wikipedia.org/wiki/K%27inich_Yo%27nal_Ahk_I"}]}, {"year": "699", "text": "<PERSON><PERSON><PERSON><PERSON>, English nun and saint", "html": "699 - <a href=\"https://wikipedia.org/wiki/Werburgh\" title=\"Werburgh\">Werburgh</a>, English nun and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Werburgh\" title=\"Werburgh\">Werburgh</a>, English nun and saint", "links": [{"title": "Werburgh", "link": "https://wikipedia.org/wiki/Werburgh"}]}, {"year": "865", "text": "<PERSON><PERSON><PERSON>, Frankish archbishop (b. 801)", "html": "865 - <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish archbishop (b. 801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish archbishop (b. 801)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ansgar"}]}, {"year": "929", "text": "<PERSON>, margrave of Tuscany", "html": "929 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Tuscany\" title=\"<PERSON>, Margrave of Tuscany\"><PERSON></a>, margrave of Tuscany", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Tuscany\" title=\"<PERSON>, <PERSON><PERSON> of Tuscany\"><PERSON></a>, margrave of Tuscany", "links": [{"title": "<PERSON>, Margrave of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Tuscany"}]}, {"year": "938", "text": "<PERSON>, Chinese general (b. 862)", "html": "938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "994", "text": "<PERSON>, duke of Aquitaine (b. 937)", "html": "994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 937)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1014", "text": "<PERSON><PERSON><PERSON>, king of Denmark and England (b. 960)", "html": "1014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Forkbeard\" title=\"<PERSON><PERSON><PERSON>beard\"><PERSON><PERSON><PERSON></a>, king of Denmark and England (b. 960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>beard\" title=\"<PERSON><PERSON><PERSON>beard\"><PERSON><PERSON><PERSON></a>, king of Denmark and England (b. 960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>beard"}]}, {"year": "1116", "text": "<PERSON><PERSON>, king of Hungary", "html": "1116 - <a href=\"https://wikipedia.org/wiki/<PERSON>oman,_King_of_Hungary\" title=\"<PERSON><PERSON>, King of Hungary\"><PERSON><PERSON></a>, king of Hungary", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Hungary\" title=\"<PERSON><PERSON>, King of Hungary\"><PERSON><PERSON></a>, king of Hungary", "links": [{"title": "<PERSON><PERSON>, King of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Hungary"}]}, {"year": "1161", "text": "<PERSON><PERSON>, king of Norway (b. 1135)", "html": "1161 - <a href=\"https://wikipedia.org/wiki/Inge_I_of_Norway\" class=\"mw-redirect\" title=\"Inge I of Norway\">In<PERSON> <PERSON></a>, king of Norway (b. 1135)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inge_I_of_Norway\" class=\"mw-redirect\" title=\"Inge I of Norway\">Inge <PERSON></a>, king of Norway (b. 1135)", "links": [{"title": "Inge I of Norway", "link": "https://wikipedia.org/wiki/Inge_I_of_Norway"}]}, {"year": "1252", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian Grand Prince (b. 1196)", "html": "1252 - <a href=\"https://wikipedia.org/wiki/Svia<PERSON><PERSON>_III_of_Vladimir\" title=\"<PERSON><PERSON><PERSON><PERSON> III of Vladimir\"><PERSON><PERSON><PERSON><PERSON></a>, Russian Grand Prince (b. 1196)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Svia<PERSON><PERSON>_III_of_Vladimir\" title=\"<PERSON><PERSON><PERSON><PERSON> III of Vladimir\"><PERSON><PERSON><PERSON><PERSON></a>, Russian Grand Prince (b. 1196)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Vladimir", "link": "https://wikipedia.org/wiki/Sviatoslav_III_of_Vladimir"}]}, {"year": "1399", "text": "<PERSON> Gaunt, Belgian-English politician, Lord <PERSON> (b. 1340)", "html": "1399 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gaunt\"><PERSON> Gaunt</a>, Belgian-English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Steward\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward</a> (b. 1340)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gaunt\"><PERSON> Gaunt</a>, Belgian-English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Steward\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward</a> (b. 1340)", "links": [{"title": "<PERSON> of Gaunt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord High Steward", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Steward"}]}, {"year": "1428", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1386)", "html": "1428 - <a href=\"https://wikipedia.org/wiki/Ashika<PERSON>_Yoshi<PERSON>chi\" title=\"Ashikaga Yoshimochi\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1386)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashika<PERSON>_<PERSON>chi\" title=\"Ashika<PERSON> Yoshimochi\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1386)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ash<PERSON><PERSON>_Yo<PERSON><PERSON>chi"}]}, {"year": "1451", "text": "<PERSON><PERSON> <PERSON>, Ottoman sultan (b. 1404)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/Murad_II\" title=\"Murad II\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murad_II\" title=\"Murad II\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1404)", "links": [{"title": "Murad II", "link": "https://wikipedia.org/wiki/Murad_II"}]}, {"year": "1468", "text": "<PERSON>, German publisher, invented the printing press (b. 1398)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publisher, invented the <a href=\"https://wikipedia.org/wiki/Printing_press\" title=\"Printing press\">printing press</a> (b. 1398)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publisher, invented the <a href=\"https://wikipedia.org/wiki/Printing_press\" title=\"Printing press\">printing press</a> (b. 1398)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Printing press", "link": "https://wikipedia.org/wiki/Printing_press"}]}, {"year": "1475", "text": "<PERSON>, Count of Nassau-Siegen, German count (b. 1410)", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count (b. 1410)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count (b. 1410)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1537", "text": "<PERSON>, 10th Earl of Kildare (b. 1513)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Kildare\" title=\"<PERSON>, 10th Earl of Kildare\"><PERSON>, 10th Earl of Kildare</a> (b. 1513)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Kildare\" title=\"<PERSON>, 10th Earl of Kildare\"><PERSON>, 10th Earl of Kildare</a> (b. 1513)", "links": [{"title": "<PERSON>, 10th Earl of Kildare", "link": "https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Kildare"}]}, {"year": "1566", "text": "<PERSON>, Flemish theologian and author (b. 1513)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish theologian and author (b. 1513)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish theologian and author (b. 1513)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1618", "text": "<PERSON>, duke of Pomerania (b. 1573)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON></a>, duke of Pomerania (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON></a>, duke of Pomerania (b. 1573)", "links": [{"title": "<PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1619", "text": "<PERSON>, 11th Baron <PERSON>, English politician, Lord Warden of the Cinque Ports (b. 1564)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_11th_Baron_<PERSON>\" title=\"<PERSON>, 11th Baron <PERSON>\"><PERSON>, 11th Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_11th_Baron_<PERSON>\" title=\"<PERSON>, 11th Baron <PERSON>\"><PERSON>, 11th Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1564)", "links": [{"title": "<PERSON>, 11th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Baron_<PERSON>"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1737", "text": "<PERSON><PERSON><PERSON>, Italian mathematician and academic (b. 1648)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and academic (b. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and academic (b. 1648)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, Spanish statesman and economist (b. 1723)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Count_<PERSON>_Campomanes\" title=\"<PERSON>, Count of Campomanes\"><PERSON></a>, Spanish statesman and economist (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Count_<PERSON>_Campomanes\" title=\"<PERSON>, Count of Campomanes\"><PERSON></a>, Spanish statesman and economist (b. 1723)", "links": [{"title": "<PERSON>, Count of Campomanes", "link": "https://wikipedia.org/wiki/Pedro_Rodr%C3%<PERSON><PERSON><PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, Argentinian sergeant (b. 1789)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON> Cabral\"><PERSON></a>, Argentinian sergeant (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON> Cabral\"><PERSON></a>, Argentinian sergeant (b. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON><PERSON>, Vietnamese emperor (b. 1762)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Long\" title=\"<PERSON><PERSON> Long\"><PERSON><PERSON></a>, Vietnamese emperor (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Long\" title=\"<PERSON><PERSON> Long\"><PERSON><PERSON></a>, Vietnamese emperor (b. 1762)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_Long"}]}, {"year": "1832", "text": "<PERSON>, English surgeon and poet (b. 1754)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surgeon and poet (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surgeon and poet (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON>, French physicist, astronomer, and mathematician (b. 1774)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist, astronomer, and mathematician (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist, astronomer, and mathematician (b. 1774)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, Canadian poet, author, and historian (b. 1809)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian poet, author, and historian (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian poet, author, and historian (b. 1809)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, English gynecologist and surgeon (b. 1811)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gynecologist and surgeon (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gynecologist and surgeon (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Dutch supercentenarian (b. 1788)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch supercentenarian (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch supercentenarian (b. 1788)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, South African general and politician, State President of the Orange Free State (b. 1854)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Christi<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African general and politician, <a href=\"https://wikipedia.org/wiki/State_President_of_the_Orange_Free_State\" title=\"State President of the Orange Free State\">State President of the Orange Free State</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christi<PERSON>_<PERSON>_<PERSON>\" title=\"Christi<PERSON>\"><PERSON><PERSON></a>, South African general and politician, <a href=\"https://wikipedia.org/wiki/State_President_of_the_Orange_Free_State\" title=\"State President of the Orange Free State\">State President of the Orange Free State</a> (b. 1854)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "State President of the Orange Free State", "link": "https://wikipedia.org/wiki/State_President_of_the_Orange_Free_State"}]}, {"year": "1922", "text": "<PERSON>, Irish painter and illustrator (b. 1839)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Yeats\"><PERSON></a>, Irish painter and illustrator (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Yeats\"><PERSON></a>, Irish painter and illustrator (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American historian, academic, and politician, 28th President of the United States, Nobel Prize laureate (b. 1856)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, academic, and politician, 28th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, academic, and politician, 28th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Danish mathematician and engineer (b. 1878)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish mathematician and engineer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish mathematician and engineer (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, German engineer, designed the Junkers J 1 (b. 1859)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, designed the <a href=\"https://wikipedia.org/wiki/Junkers_J_1\" title=\"Junkers J 1\">Junkers J 1</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, designed the <a href=\"https://wikipedia.org/wiki/Junkers_J_1\" title=\"Junkers J 1\">Junkers J 1</a> (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Junkers J 1", "link": "https://wikipedia.org/wiki/Junkers_J_1"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, French singer and actress (b. 1865)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer and actress (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer and actress (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German lawyer and judge (b. 1893)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and judge (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and judge (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American admiral and pilot (b. 1887)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and pilot (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and pilot (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American journalist and politician, 32nd United States Secretary of the Interior (b. 1874)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 32nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 32nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Russian general (b. 1895)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, French mathematician and academic (b. 1871)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Borel\" title=\"É<PERSON>\"><PERSON><PERSON></a>, French mathematician and academic (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Borel\" title=\"É<PERSON>\"><PERSON><PERSON></a>, French mathematician and academic (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Borel"}]}, {"year": "1956", "text": "<PERSON>, English-Belgian race car driver and trumpet player (b. 1916)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Belgian race car driver and trumpet player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Belgian race car driver and trumpet player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "The Day the Music Died\n<PERSON>, American singer-songwriter and guitarist (b. 1930)\n<PERSON>, American singer-songwriter and guitarist (b. 1936)\n<PERSON>, American singer-songwriter and guitarist (b. 1941)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/The_Day_the_Music_Died\" title=\"The Day the Music Died\">The Day the Music Died</a>\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/The_Big_Bopper\" title=\"The Big Bopper\"><PERSON> Big Bopper</a>, American singer-songwriter and guitarist (b. 1930)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_Holly\" title=\"<PERSON> Holly\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Day_the_Music_Died\" title=\"The Day the Music Died\">The Day the Music Died</a>\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/The_Big_Bopper\" title=\"The Big Bopper\">The Big Bopper</a>, American singer-songwriter and guitarist (b. 1930)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_Holly\" title=\"<PERSON> Holly\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)</li>\n</ul>", "links": [{"title": "The Day the Music Died", "link": "https://wikipedia.org/wiki/The_Day_the_Music_Died"}, {"title": "The Big Bopper", "link": "https://wikipedia.org/wiki/The_<PERSON>_Bopper"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON> <PERSON>, American singer-songwriter and guitarist (b. 1930)", "text": null, "html": "The <PERSON> Bo<PERSON>, American singer-songwriter and guitarist (b. 1930) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/The_Big_Bopper\" title=\"The Big Bopper\">The <PERSON> Bopper</a>, American singer-songwriter and guitarist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/The_Big_Bopper\" title=\"The Big Bopper\">The <PERSON> Bopper</a>, American singer-songwriter and guitarist (b. 1930)", "links": [{"title": "The Big Bopper", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/The_<PERSON>_Bopper"}]}, {"year": "<PERSON>, American singer-songwriter and guitarist (b. 1936)", "text": null, "html": "<PERSON>, American singer-songwriter and guitarist (b. 1936) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Holly\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American singer-songwriter and guitarist (b. 1941)", "text": null, "html": "<PERSON>, American singer-songwriter and guitarist (b. 1941) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Italian singer and actor (b. 1921)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer and actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer and actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, 1st Viscount <PERSON>, Scottish-Australian captain and politician, 14th Governor-General of Australia (b. 1893)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Dun<PERSON><PERSON>\" title=\"<PERSON>, 1st Viscount Dunrossil\"><PERSON>, 1st Viscount Dun<PERSON></a>, Scottish-Australian captain and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>nross<PERSON>\" title=\"<PERSON>, 1st Viscount Dunrossil\"><PERSON>, 1st Viscount Dunrossil</a>, Scottish-Australian captain and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (b. 1893)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1961", "text": "<PERSON>, American actress (b. 1905)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American biochemist (b. 1879)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English songwriter and producer (b. 1929)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and producer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian journalist and politician, 7th Chief Minister of Madras State (b. 1909)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/C._N._Annadurai\" title=\"C. N. Annadurai\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Tamil_Nadu\" class=\"mw-redirect\" title=\"List of Chief Ministers of Tamil Nadu\">Chief Minister of Madras State</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._N._Annadurai\" title=\"C. N. Annadurai\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Tamil_Nadu\" class=\"mw-redirect\" title=\"List of Chief Ministers of Tamil Nadu\">Chief Minister of Madras State</a> (b. 1909)", "links": [{"title": "C. N. Annadurai", "link": "https://wikipedia.org/wiki/C._N._Annadurai"}, {"title": "List of Chief Ministers of Tamil Nadu", "link": "https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Tamil_Nadu"}]}, {"year": "1969", "text": "<PERSON>, Mozambican activist and academic (b. 1920)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mozambican activist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mozambican activist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American physicist and engineer (b. 1873)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Egyptian singer-songwriter and actress (b. 1904)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Umm_Kulthum\" title=\"Umm Kulthum\"><PERSON><PERSON></a>, Egyptian singer-songwriter and actress (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umm_Kulthum\" title=\"Umm Kulthum\"><PERSON><PERSON></a>, Egyptian singer-songwriter and actress (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umm_Kulthum"}]}, {"year": "1985", "text": "<PERSON>, American physicist and academic (b. 1912)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor, director, and screenwriter (b. 1929)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American pianist, composer, and conductor (b. 1916)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian pianist and conductor (b. 1927)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian pianist and conductor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian pianist and conductor (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oy<PERSON>_Bernier"}]}, {"year": "1996", "text": "<PERSON>, American actress and banker (b. 1922)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and banker (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and banker (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer-songwriter and pianist (b. 1950)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Georgian biologist and politician, 4th Prime Minister of Georgia (b. 1963)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian biologist and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Georgia\" title=\"Prime Minister of Georgia\">Prime Minister of Georgia</a> (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian biologist and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Georgia\" title=\"Prime Minister of Georgia\">Prime Minister of Georgia</a> (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zurab_Zhvania"}, {"title": "Prime Minister of Georgia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Georgia"}]}, {"year": "2005", "text": "<PERSON>, German-American biologist and ornithologist (b. 1904)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biologist and ornithologist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biologist and ornithologist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor and activist (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and activist (b. 1923)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Chinese monk and scholar, founded the Dharma Drum Mountain (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Sheng-yen\" title=\"Sheng-yen\"><PERSON>g-yen</a>, Chinese monk and scholar, founded the <a href=\"https://wikipedia.org/wiki/Dharma_Drum_Mountain\" title=\"Dharma Drum Mountain\">Dharma Drum Mountain</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheng-yen\" title=\"Sheng-yen\"><PERSON>g-yen</a>, Chinese monk and scholar, founded the <a href=\"https://wikipedia.org/wiki/Dharma_Drum_Mountain\" title=\"Dharma Drum Mountain\">Dharma Drum Mountain</a> (b. 1930)", "links": [{"title": "Sheng-yen", "link": "https://wikipedia.org/wiki/Sheng-yen"}, {"title": "Dharma Drum Mountain", "link": "https://wikipedia.org/wiki/Dharma_Drum_Mountain"}]}, {"year": "2010", "text": "<PERSON>, American basketball player and coach (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actress (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, French actress (b. 1952)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, French actress (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, French actress (b. 1952)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Singaporean academic and politician, 1st Deputy Prime Minister of Singapore (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean academic and politician, 1st <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore\" title=\"Deputy Prime Minister of Singapore\">Deputy Prime Minister of Singapore</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean academic and politician, 1st <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore\" title=\"Deputy Prime Minister of Singapore\">Deputy Prime Minister of Singapore</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Singapore", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore"}]}, {"year": "2012", "text": "<PERSON>, American actor and director (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American general (b. 1962)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Indian director, producer, and screenwriter (b. 1961)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian director, producer, and screenwriter (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian director, producer, and screenwriter (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American actor, director, and producer (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_King\" title=\"Zalman King\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_King\" title=\"Zal<PERSON> King\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Polish physician and academic (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish physician and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish physician and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zczeklik"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American politician (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Ukrainian-Russian composer and producer (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian composer and producer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian composer and producer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American soldier and pilot (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Pakistani politician, Chief Minister of Balochistan (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Balochistan\" title=\"Chief Minister of Balochistan\">Chief Minister of Balochistan</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Balochistan\" title=\"Chief Minister of Balochistan\">Chief Minister of Balochistan</a> (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of Balochistan", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Balochistan"}]}, {"year": "2015", "text": "<PERSON>, English historian, author, and academic (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actress and singer (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, American actress and singer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, American actress and singer (b. 1918)", "links": [{"title": "<PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)"}]}, {"year": "2015", "text": "<PERSON>, American golfer (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Pakistani lawyer and judge, 12th Chief Justice of Pakistan (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani lawyer and judge, 12th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Pakistan\" title=\"Chief Justice of Pakistan\">Chief Justice of Pakistan</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani lawyer and judge, 12th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Pakistan\" title=\"Chief Justice of Pakistan\">Chief Justice of Pakistan</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of Pakistan", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Pakistan"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1952)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, 23rd Governor of Madhya Pradesh (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_Madhya_Pradesh\" class=\"mw-redirect\" title=\"Governor of Madhya Pradesh\">Governor of Madhya Pradesh</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_Madhya_Pradesh\" class=\"mw-redirect\" title=\"Governor of Madhya Pradesh\">Governor of Madhya Pradesh</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Madhya Pradesh", "link": "https://wikipedia.org/wiki/Governor_of_Madhya_Pradesh"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian politician and economist (b. 1945)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian politician and economist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian politician and economist (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON><PERSON>, Albanian poet, writer and politician (b. 1931)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Drit%C3%ABro_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Albanian poet, writer and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Drit%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Albanian poet, writer and politician (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Drit%C3%ABro_A<PERSON>li"}]}, {"year": "2019", "text": "<PERSON>, American actress (b. 1926)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1966)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_St._John\" title=\"Kristoff St. John\"><PERSON><PERSON><PERSON>. John</a>, American actor (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_St._John\" title=\"Kristoff St. John\"><PERSON><PERSON><PERSON> John</a>, American actor (b. 1966)", "links": [{"title": "<PERSON><PERSON>ff St. John", "link": "https://wikipedia.org/wiki/Kristoff_St._John"}]}, {"year": "2020", "text": "<PERSON>, French-American philosopher, author, and critic (b. 1929)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American philosopher, author, and critic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American philosopher, author, and critic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, Sri Lankan industrialist (b. 1942)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan industrialist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan industrialist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON>, Sri Lankan corporate leader and executive (b. 1940)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan corporate leader and executive (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>lendra\"><PERSON><PERSON><PERSON></a>, Sri Lankan corporate leader and executive (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}