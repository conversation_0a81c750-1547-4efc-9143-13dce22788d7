{"date": "October 7", "url": "https://wikipedia.org/wiki/October_7", "data": {"Events": [{"year": "3761 BC", "text": "The epoch reference date (start) of the modern Hebrew calendar.", "html": "3761 BC - 3761 BC - The epoch reference date (start) of the modern <a href=\"https://wikipedia.org/wiki/Hebrew_calendar\" title=\"Hebrew calendar\">Hebrew calendar</a>.", "no_year_html": "3761 BC - The epoch reference date (start) of the modern <a href=\"https://wikipedia.org/wiki/Hebrew_calendar\" title=\"Hebrew calendar\">Hebrew calendar</a>.", "links": [{"title": "Hebrew calendar", "link": "https://wikipedia.org/wiki/Hebrew_calendar"}]}, {"year": "1403", "text": "Venetian-Genoese wars: The Genoese fleet under a French admiral is defeated by a Venetian fleet at the Battle of Modon.", "html": "1403 - Venetian-Genoese wars: The Genoese fleet under a French admiral is defeated by a Venetian fleet at the <a href=\"https://wikipedia.org/wiki/Battle_of_Modon_(1403)\" title=\"Battle of Modon (1403)\">Battle of Modon</a>.", "no_year_html": "Venetian-Genoese wars: The Genoese fleet under a French admiral is defeated by a Venetian fleet at the <a href=\"https://wikipedia.org/wiki/Battle_of_Modon_(1403)\" title=\"Battle of Modon (1403)\">Battle of Modon</a>.", "links": [{"title": "Battle of Modon (1403)", "link": "https://wikipedia.org/wiki/Battle_of_Modon_(1403)"}]}, {"year": "1477", "text": "Uppsala University is inaugurated after receiving its corporate rights from Pope Sixtus IV in February the same year.", "html": "1477 - <a href=\"https://wikipedia.org/wiki/Uppsala_University\" title=\"Uppsala University\">Uppsala University</a> is inaugurated after receiving its corporate rights from <a href=\"https://wikipedia.org/wiki/Pope_Sixtus_IV\" title=\"Pope Sixtus IV\">Pope Sixtus IV</a> in February the same year.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uppsala_University\" title=\"Uppsala University\">Uppsala University</a> is inaugurated after receiving its corporate rights from <a href=\"https://wikipedia.org/wiki/Pope_Sixtus_IV\" title=\"Pope Sixtus IV\">Pope Sixtus IV</a> in February the same year.", "links": [{"title": "Uppsala University", "link": "https://wikipedia.org/wiki/Uppsala_University"}, {"title": "<PERSON> <PERSON><PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_IV"}]}, {"year": "1513", "text": "War of the League of Cambrai: Spain defeats Venice.", "html": "1513 - War of the League of Cambrai: Spain <a href=\"https://wikipedia.org/wiki/Battle_of_La_Motta_(1513)\" title=\"Battle of La Motta (1513)\">defeats</a> Venice.", "no_year_html": "War of the League of Cambrai: Spain <a href=\"https://wikipedia.org/wiki/Battle_of_La_Motta_(1513)\" title=\"Battle of La Motta (1513)\">defeats</a> Venice.", "links": [{"title": "Battle of La Motta (1513)", "link": "https://wikipedia.org/wiki/Battle_of_La_Motta_(1513)"}]}, {"year": "1571", "text": "The Battle of Lepanto is fought, and the Ottoman Navy suffers its first defeat.", "html": "1571 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Lepanto\" title=\"Battle of Lepanto\">Battle of Lepanto</a> is fought, and the Ottoman Navy suffers its first defeat.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Lepanto\" title=\"Battle of Lepanto\">Battle of Lepanto</a> is fought, and the Ottoman Navy suffers its first defeat.", "links": [{"title": "Battle of Lepanto", "link": "https://wikipedia.org/wiki/Battle_of_Lepanto"}]}, {"year": "1691", "text": "The charter for the Province of Massachusetts Bay is issued.", "html": "1691 - The charter for the <a href=\"https://wikipedia.org/wiki/Province_of_Massachusetts_Bay\" title=\"Province of Massachusetts Bay\">Province of Massachusetts Bay</a> is issued.", "no_year_html": "The charter for the <a href=\"https://wikipedia.org/wiki/Province_of_Massachusetts_Bay\" title=\"Province of Massachusetts Bay\">Province of Massachusetts Bay</a> is issued.", "links": [{"title": "Province of Massachusetts Bay", "link": "https://wikipedia.org/wiki/Province_of_Massachusetts_Bay"}]}, {"year": "1763", "text": "King <PERSON> issues the Royal Proclamation of 1763, closing Indigenous lands in North America north and west of the Alleghenies to white settlements.", "html": "1763 - King <PERSON> issues the <a href=\"https://wikipedia.org/wiki/Royal_Proclamation_of_1763\" title=\"Royal Proclamation of 1763\">Royal Proclamation of 1763</a>, closing Indigenous lands in North America north and west of the Alleghenies to white settlements.", "no_year_html": "King <PERSON> issues the <a href=\"https://wikipedia.org/wiki/Royal_Proclamation_of_1763\" title=\"Royal Proclamation of 1763\">Royal Proclamation of 1763</a>, closing Indigenous lands in North America north and west of the Alleghenies to white settlements.", "links": [{"title": "Royal Proclamation of 1763", "link": "https://wikipedia.org/wiki/Royal_Proclamation_of_1763"}]}, {"year": "1777", "text": "American Revolutionary War: The Americans defeat British forces under general <PERSON> in the Second Battle of Saratoga, also known as the Battle of Bemis Heights, compelling <PERSON><PERSON><PERSON><PERSON>'s eventual surrender.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The Americans defeat British forces under general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the Second Battle of Saratoga, also known as the <a href=\"https://wikipedia.org/wiki/Battle_of_Bemis_Heights\" class=\"mw-redirect\" title=\"Battle of Bemis Heights\">Battle of Bemis Heights</a>, compelling <PERSON><PERSON><PERSON><PERSON>'s eventual <a href=\"https://wikipedia.org/wiki/Saratoga_campaign#Surrender\" title=\"Saratoga campaign\">surrender</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The Americans defeat British forces under general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the Second Battle of Saratoga, also known as the <a href=\"https://wikipedia.org/wiki/Battle_of_Bemis_Heights\" class=\"mw-redirect\" title=\"Battle of Bemis Heights\">Battle of Bemis Heights</a>, compelling <PERSON><PERSON><PERSON><PERSON>'s eventual <a href=\"https://wikipedia.org/wiki/Saratoga_campaign#Surrender\" title=\"Saratoga campaign\">surrender</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Battle of Bemis Heights", "link": "https://wikipedia.org/wiki/Battle_of_Bemis_Heights"}, {"title": "Saratoga campaign", "link": "https://wikipedia.org/wiki/Saratoga_campaign#Surrender"}]}, {"year": "1780", "text": "American Revolutionary War: American militia defeat royalist irregulars led by British major <PERSON> at the Battle of Kings Mountain in South Carolina, often regarded as the turning point in the war's Southern theater.", "html": "1780 - American Revolutionary War: American militia defeat <a href=\"https://wikipedia.org/wiki/Loyalist_(American_Revolution)\" title=\"Loyalist (American Revolution)\">royalist</a> irregulars led by British major <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kings_Mountain\" title=\"Battle of Kings Mountain\">Battle of Kings Mountain</a> in <a href=\"https://wikipedia.org/wiki/South_Carolina_in_the_American_Revolution\" title=\"South Carolina in the American Revolution\">South Carolina</a>, often regarded as the turning point in the war's <a href=\"https://wikipedia.org/wiki/Southern_theater_of_the_American_Revolutionary_War\" title=\"Southern theater of the American Revolutionary War\">Southern theater</a>.", "no_year_html": "American Revolutionary War: American militia defeat <a href=\"https://wikipedia.org/wiki/Loyalist_(American_Revolution)\" title=\"Loyalist (American Revolution)\">royalist</a> irregulars led by British major <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kings_Mountain\" title=\"Battle of Kings Mountain\">Battle of Kings Mountain</a> in <a href=\"https://wikipedia.org/wiki/South_Carolina_in_the_American_Revolution\" title=\"South Carolina in the American Revolution\">South Carolina</a>, often regarded as the turning point in the war's <a href=\"https://wikipedia.org/wiki/Southern_theater_of_the_American_Revolutionary_War\" title=\"Southern theater of the American Revolutionary War\">Southern theater</a>.", "links": [{"title": "Loyalist (American Revolution)", "link": "https://wikipedia.org/wiki/Loyalist_(American_Revolution)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Kings Mountain", "link": "https://wikipedia.org/wiki/Battle_of_Kings_Mountain"}, {"title": "South Carolina in the American Revolution", "link": "https://wikipedia.org/wiki/South_Carolina_in_the_American_Revolution"}, {"title": "Southern theater of the American Revolutionary War", "link": "https://wikipedia.org/wiki/Southern_theater_of_the_American_Revolutionary_War"}]}, {"year": "1800", "text": "French corsair <PERSON>, commander of the 18-gun ship La Confiance, captures the British 38-gun Kent.", "html": "1800 - French corsair <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, commander of the 18-gun ship <i>La Confiance</i>, captures the British 38-gun <i>Kent</i>.", "no_year_html": "French corsair <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, commander of the 18-gun ship <i>La Confiance</i>, captures the British 38-gun <i>Kent</i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1826", "text": "The Granite Railway begins operations as the first chartered railway in the U.S.", "html": "1826 - The <a href=\"https://wikipedia.org/wiki/Granite_Railway\" title=\"Granite Railway\">Granite Railway</a> begins operations as the first chartered railway in the U.S.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Granite_Railway\" title=\"Granite Railway\">Granite Railway</a> begins operations as the first chartered railway in the U.S.", "links": [{"title": "Granite Railway", "link": "https://wikipedia.org/wiki/Granite_Railway"}]}, {"year": "1828", "text": "Morea expedition: The city of Patras, Greece, is liberated by the French expeditionary force.", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Morea_expedition\" title=\"Morea expedition\">Morea expedition</a>: The city of Patras, Greece, is liberated by the French expeditionary force.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morea_expedition\" title=\"Morea expedition\">Morea expedition</a>: The city of Patras, Greece, is liberated by the French expeditionary force.", "links": [{"title": "Morea expedition", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_expedition"}]}, {"year": "1840", "text": "<PERSON> becomes King of the Netherlands.", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"<PERSON> II of the Netherlands\"><PERSON></a> becomes King of the Netherlands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"<PERSON> II of the Netherlands\"><PERSON></a> becomes King of the Netherlands.", "links": [{"title": "<PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands"}]}, {"year": "1864", "text": "American Civil War: A US Navy ship captures a Confederate raider in a Brazilian seaport.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A US Navy ship <a href=\"https://wikipedia.org/wiki/Bahia_incident\" title=\"Bahia incident\">captures</a> a Confederate raider in a Brazilian seaport.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A US Navy ship <a href=\"https://wikipedia.org/wiki/Bahia_incident\" title=\"Bahia incident\">captures</a> a Confederate raider in a Brazilian seaport.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Bahia incident", "link": "https://wikipedia.org/wiki/Bahia_incident"}]}, {"year": "1868", "text": "Cornell University holds opening day ceremonies; initial student enrollment is 412, the highest at any American university to that date.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> holds opening day ceremonies; initial student enrollment is 412, the highest at any American university to that date.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> holds opening day ceremonies; initial student enrollment is 412, the highest at any American university to that date.", "links": [{"title": "Cornell University", "link": "https://wikipedia.org/wiki/Cornell_University"}]}, {"year": "1870", "text": "Franco-Prussian War: <PERSON> escapes the siege of Paris in a hot-air balloon.", "html": "1870 - Franco-Prussian War: <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes the siege of Paris in a hot-air balloon.", "no_year_html": "Franco-Prussian War: <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes the siege of Paris in a hot-air balloon.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON><PERSON>"}]}, {"year": "1879", "text": "Germany and Austria-Hungary sign the \"Twofold Covenant\" and create the Dual Alliance.", "html": "1879 - Germany and Austria-Hungary sign the \"Twofold Covenant\" and create the <a href=\"https://wikipedia.org/wiki/Dual_Alliance_(1879)\" title=\"Dual Alliance (1879)\">Dual Alliance</a>.", "no_year_html": "Germany and Austria-Hungary sign the \"Twofold Covenant\" and create the <a href=\"https://wikipedia.org/wiki/Dual_Alliance_(1879)\" title=\"Dual Alliance (1879)\">Dual Alliance</a>.", "links": [{"title": "Dual Alliance (1879)", "link": "https://wikipedia.org/wiki/Dual_Alliance_(1879)"}]}, {"year": "1912", "text": "The Helsinki Stock Exchange sees its first transaction.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/Nasdaq_Helsinki\" title=\"Nasdaq Helsinki\">Helsinki Stock Exchange</a> sees its first transaction.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nasdaq_Helsinki\" title=\"Nasdaq Helsinki\">Helsinki Stock Exchange</a> sees its first transaction.", "links": [{"title": "Nasdaq Helsinki", "link": "https://wikipedia.org/wiki/Nasdaq_Helsinki"}]}, {"year": "1913", "text": "Ford Motor Company introduces the first moving vehicle assembly line.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> introduces the first moving <a href=\"https://wikipedia.org/wiki/Assembly_line\" title=\"Assembly line\">vehicle assembly line</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> introduces the first moving <a href=\"https://wikipedia.org/wiki/Assembly_line\" title=\"Assembly line\">vehicle assembly line</a>.", "links": [{"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}, {"title": "Assembly line", "link": "https://wikipedia.org/wiki/Assembly_line"}]}, {"year": "1916", "text": "Georgia Tech defeats Cumberland University 222-0 in the most lopsided college football game in American history.", "html": "1916 - Georgia Tech defeats Cumberland University 222-0 in the most <a href=\"https://wikipedia.org/wiki/1916_Cumberland_vs._Georgia_Tech_football_game\" title=\"1916 Cumberland vs. Georgia Tech football game\">lopsided college football game</a> in American history.", "no_year_html": "Georgia Tech defeats Cumberland University 222-0 in the most <a href=\"https://wikipedia.org/wiki/1916_Cumberland_vs._Georgia_Tech_football_game\" title=\"1916 Cumberland vs. Georgia Tech football game\">lopsided college football game</a> in American history.", "links": [{"title": "1916 Cumberland vs. Georgia Tech football game", "link": "https://wikipedia.org/wiki/1916_Cumberland_vs._Georgia_Tech_football_game"}]}, {"year": "1919", "text": "KLM, the flag carrier of the Netherlands, is founded. It is the oldest airline still operating under its original name.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/KLM\" title=\"KLM\">KLM</a>, the flag carrier of the Netherlands, is founded. It is the oldest airline still operating under its original name.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/KLM\" title=\"KLM\">KLM</a>, the flag carrier of the Netherlands, is founded. It is the oldest airline still operating under its original name.", "links": [{"title": "KLM", "link": "https://wikipedia.org/wiki/KLM"}]}, {"year": "1924", "text": "<PERSON> becomes prime minister of Greece for a short period of time.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes prime minister of Greece for a short period of time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes prime minister of Greece for a short period of time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON> <PERSON> becomes Ecumenical Patriarch of Constantinople.", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Ph<PERSON><PERSON>_II_of_Constantinople\" title=\"Ph<PERSON><PERSON> II of Constantinople\"><PERSON><PERSON><PERSON> II</a> becomes Ecumenical Patriarch of Constantinople.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> II of Constantinople\"><PERSON><PERSON><PERSON> II</a> becomes Ecumenical Patriarch of Constantinople.", "links": [{"title": "<PERSON><PERSON><PERSON> II of Constantinople", "link": "https://wikipedia.org/wiki/Photius_II_of_Constantinople"}]}, {"year": "1940", "text": "World War II: The McCollum memo proposes bringing the United States into the war in Europe by provoking the Japanese to attack the United States.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/McCollum_memo\" title=\"McCollum memo\"><PERSON><PERSON><PERSON>oll<PERSON> memo</a> proposes bringing the United States into the war in Europe by provoking the Japanese to attack the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/McCollum_memo\" title=\"McCollum memo\"><PERSON><PERSON><PERSON>oll<PERSON> memo</a> proposes bringing the United States into the war in Europe by provoking the Japanese to attack the United States.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "McCollum memo", "link": "https://wikipedia.org/wiki/McCollum_memo"}]}, {"year": "1944", "text": "World War II: The Sonderkommando Revolt in Auschwitz was an uprising of prisoners (especially the Sonderkommando) at the Auschwitz concentration camp, they burnt down Crematorium IV.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Sonderkommando_Revolt_in_Auschwitz\" title=\"Sonderkommando Revolt in Auschwitz\">Sonderkommando Revolt in Auschwitz</a> was an <a href=\"https://wikipedia.org/wiki/Uprising\" class=\"mw-redirect\" title=\"Uprising\">uprising</a> of prisoners (especially the <a href=\"https://wikipedia.org/wiki/Sonderkommando\" title=\"Sonderkommando\">Sonderkommando</a>) at the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>, they burnt down Crematorium IV.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Sonderkommando_Revolt_in_Auschwitz\" title=\"Sonderkommando Revolt in Auschwitz\">Sonderkommando Revolt in Auschwitz</a> was an <a href=\"https://wikipedia.org/wiki/Uprising\" class=\"mw-redirect\" title=\"Uprising\">uprising</a> of prisoners (especially the <a href=\"https://wikipedia.org/wiki/Sonderkommando\" title=\"Sonderkommando\">Sonderkommando</a>) at the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>, they burnt down Crematorium IV.", "links": [{"title": "Sonderkommando Revolt in Auschwitz", "link": "https://wikipedia.org/wiki/Sonderkommando_Revolt_in_Auschwitz"}, {"title": "Uprising", "link": "https://wikipedia.org/wiki/Uprising"}, {"title": "Sonderkommando", "link": "https://wikipedia.org/wiki/Sonderkommando"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1949", "text": "The communist German Democratic Republic (East Germany) is formed.", "html": "1949 - The communist <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">German Democratic Republic</a> (East Germany) is formed.", "no_year_html": "The communist <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">German Democratic Republic</a> (East Germany) is formed.", "links": [{"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}]}, {"year": "1950", "text": "<PERSON> establishes the Missionaries of Charity.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> establishes the Missionaries of Charity.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> establishes the Missionaries of Charity.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "The 1958 Pakistani coup d'état inaugurates a prolonged period of military rule.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/1958_Pakistani_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1958 Pakistani coup d'état\">1958 Pakistani coup d'état</a> inaugurates a prolonged period of military rule.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1958_Pakistani_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1958 Pakistani coup d'état\">1958 Pakistani coup d'état</a> inaugurates a prolonged period of military rule.", "links": [{"title": "1958 Pakistani coup d'état", "link": "https://wikipedia.org/wiki/1958_Pakistani_coup_d%27%C3%A9tat"}]}, {"year": "1958", "text": "The U.S. crewed space-flight project is renamed to Project Mercury.", "html": "1958 - The U.S. crewed space-flight project is renamed to <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>.", "no_year_html": "The U.S. crewed space-flight project is renamed to <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>.", "links": [{"title": "Project Mercury", "link": "https://wikipedia.org/wiki/Project_Mercury"}]}, {"year": "1959", "text": "The Soviet probe Luna 3 transmits the first-ever photographs of the far side of the Moon.", "html": "1959 - The Soviet probe <a href=\"https://wikipedia.org/wiki/Luna_3\" title=\"Luna 3\">Luna 3</a> transmits the first-ever photographs of the far side of the Moon.", "no_year_html": "The Soviet probe <a href=\"https://wikipedia.org/wiki/Luna_3\" title=\"Luna 3\">Luna 3</a> transmits the first-ever photographs of the far side of the Moon.", "links": [{"title": "Luna 3", "link": "https://wikipedia.org/wiki/Luna_3"}]}, {"year": "1961", "text": "A Douglas Dakota IV operated by Derby Aviation (later renamed to British Midland International) crashes in Canigou, France, killing 34 people.", "html": "1961 - A <a href=\"https://wikipedia.org/wiki/Douglas_C-47_Skytrain\" title=\"Douglas C-47 Skytrain\">Douglas Dakota IV</a> operated by Derby Aviation (later renamed to <a href=\"https://wikipedia.org/wiki/British_Midland_International\" title=\"British Midland International\">British Midland International</a>) <a href=\"https://wikipedia.org/wiki/1961_Derby_Aviation_crash\" title=\"1961 Derby Aviation crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Canigou\" class=\"mw-redirect\" title=\"Canigou\">Canigou</a>, France, killing 34 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Douglas_C-47_Skytrain\" title=\"Douglas C-47 Skytrain\">Douglas Dakota IV</a> operated by Derby Aviation (later renamed to <a href=\"https://wikipedia.org/wiki/British_Midland_International\" title=\"British Midland International\">British Midland International</a>) <a href=\"https://wikipedia.org/wiki/1961_Derby_Aviation_crash\" title=\"1961 Derby Aviation crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Canigou\" class=\"mw-redirect\" title=\"Canigou\">Canigou</a>, France, killing 34 people.", "links": [{"title": "Douglas C-47 Skytrain", "link": "https://wikipedia.org/wiki/Douglas_C-47_Skytrain"}, {"title": "British Midland International", "link": "https://wikipedia.org/wiki/British_Midland_International"}, {"title": "1961 Derby Aviation crash", "link": "https://wikipedia.org/wiki/1961_Derby_Aviation_crash"}, {"title": "Canigou", "link": "https://wikipedia.org/wiki/Canigou"}]}, {"year": "1963", "text": "President <PERSON> signs the ratification of the Partial Nuclear Test Ban Treaty.", "html": "1963 - President <PERSON> signs the ratification of the <a href=\"https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty\" title=\"Partial Nuclear Test Ban Treaty\">Partial Nuclear Test Ban Treaty</a>.", "no_year_html": "President <PERSON> signs the ratification of the <a href=\"https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty\" title=\"Partial Nuclear Test Ban Treaty\">Partial Nuclear Test Ban Treaty</a>.", "links": [{"title": "Partial Nuclear Test Ban Treaty", "link": "https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty"}]}, {"year": "1963", "text": "Buddhist crisis: <PERSON><PERSON> worsening relations, outspoken South Vietnamese First Lady Madame <PERSON> arrives in the US for a speaking tour, continuing a flurry of attacks on the <PERSON> administration.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a>: <PERSON><PERSON> worsening relations, outspoken South Vietnamese First Lady <a href=\"https://wikipedia.org/wiki/Madame_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Madame <PERSON>\">Madame <PERSON><PERSON></a> arrives in the US for a speaking tour, continuing a flurry of attacks on the <a href=\"https://wikipedia.org/wiki/<PERSON>_administration\" class=\"mw-redirect\" title=\"<PERSON> administration\"><PERSON> administration</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a>: <PERSON><PERSON> worsening relations, outspoken South Vietnamese First Lady <a href=\"https://wikipedia.org/wiki/Madame_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Madame <PERSON>\">Madame <PERSON><PERSON></a> arrives in the US for a speaking tour, continuing a flurry of attacks on the <a href=\"https://wikipedia.org/wiki/<PERSON>_administration\" class=\"mw-redirect\" title=\"<PERSON> administration\"><PERSON> administration</a>.", "links": [{"title": "Buddhist crisis", "link": "https://wikipedia.org/wiki/Buddhist_crisis"}, {"title": "Madame <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> administration", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "The Fourth Soviet Constitution is adopted.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/1977_Soviet_Constitution\" class=\"mw-redirect\" title=\"1977 Soviet Constitution\">Fourth Soviet Constitution</a> is adopted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1977_Soviet_Constitution\" class=\"mw-redirect\" title=\"1977 Soviet Constitution\">Fourth Soviet Constitution</a> is adopted.", "links": [{"title": "1977 Soviet Constitution", "link": "https://wikipedia.org/wiki/1977_Soviet_Constitution"}]}, {"year": "1978", "text": "Aeroflot Flight 1080 crashes after takeoff from Koltsovo International Airport, killing 38.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_1080\" title=\"Aeroflot Flight 1080\">Aeroflot Flight 1080</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Koltsovo_International_Airport\" title=\"Koltsovo International Airport\">Koltsovo International Airport</a>, killing 38.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_1080\" title=\"Aeroflot Flight 1080\">Aeroflot Flight 1080</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Koltsovo_International_Airport\" title=\"Koltsovo International Airport\">Koltsovo International Airport</a>, killing 38.", "links": [{"title": "Aeroflot Flight 1080", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_1080"}, {"title": "Koltsovo International Airport", "link": "https://wikipedia.org/wiki/Koltsovo_International_Airport"}]}, {"year": "1979", "text": "Swissair Flight 316 crashes at Ellinikon International Airport in Athens, Greece, killing 14.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Swissair_Flight_316\" title=\"Swissair Flight 316\">Swissair Flight 316</a> crashes at <a href=\"https://wikipedia.org/wiki/Ellinikon_International_Airport\" title=\"Ellinikon International Airport\">Ellinikon International Airport</a> in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, Greece, killing 14.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swissair_Flight_316\" title=\"Swissair Flight 316\">Swissair Flight 316</a> crashes at <a href=\"https://wikipedia.org/wiki/Ellinikon_International_Airport\" title=\"Ellinikon International Airport\">Ellinikon International Airport</a> in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, Greece, killing 14.", "links": [{"title": "Swissair Flight 316", "link": "https://wikipedia.org/wiki/Swissair_Flight_316"}, {"title": "Ellinikon International Airport", "link": "https://wikipedia.org/wiki/Ellinikon_International_Airport"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}]}, {"year": "1985", "text": "The Mameyes landslide kills almost 200 people in Puerto Rico.", "html": "1985 - The <a href=\"https://wikipedia.org/wiki/1985_Puerto_Rico_floods\" title=\"1985 Puerto Rico floods\"><PERSON><PERSON><PERSON> landslide</a> kills almost 200 people in Puerto Rico.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1985_Puerto_Rico_floods\" title=\"1985 Puerto Rico floods\"><PERSON><PERSON><PERSON> landslide</a> kills almost 200 people in Puerto Rico.", "links": [{"title": "1985 Puerto Rico floods", "link": "https://wikipedia.org/wiki/1985_Puerto_Rico_floods"}]}, {"year": "1985", "text": "Four men from the Palestine Liberation Front hijack the MS <PERSON><PERSON><PERSON> off the coast of Egypt.", "html": "1985 - Four men from the Palestine Liberation Front <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_hijacking\" title=\"<PERSON><PERSON><PERSON> hijacking\">hijack</a> the <a href=\"https://wikipedia.org/wiki/MS_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"MS <PERSON><PERSON><PERSON>\">MS <i><PERSON><PERSON><PERSON></i></a> off the coast of Egypt.", "no_year_html": "Four men from the Palestine Liberation Front <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_hijacking\" title=\"<PERSON><PERSON><PERSON> hijacking\">hijack</a> the <a href=\"https://wikipedia.org/wiki/MS_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"MS <PERSON><PERSON><PERSON>\">MS <i><PERSON><PERSON><PERSON></i></a> off the coast of Egypt.", "links": [{"title": "<PERSON><PERSON><PERSON> hijacking", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON><PERSON>_hijacking"}, {"title": "MS <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "Sikh nationalists declare the independence of Khalistan from India; it is not internationally recognized.", "html": "1987 - Sikh nationalists declare the independence of <a href=\"https://wikipedia.org/wiki/Khalistan_movement\" title=\"Khalistan movement\">Khalistan</a> from India; it is not internationally recognized.", "no_year_html": "Sikh nationalists declare the independence of <a href=\"https://wikipedia.org/wiki/Khalistan_movement\" title=\"Khalistan movement\">Khalistan</a> from India; it is not internationally recognized.", "links": [{"title": "Khalistan movement", "link": "https://wikipedia.org/wiki/Khalistan_movement"}]}, {"year": "1988", "text": "A hunter discovers three gray whales trapped under the ice near Alaska; the situation becomes a multinational effort to free the whales.", "html": "1988 - A hunter discovers three gray whales trapped under the ice near Alaska; the situation becomes <a href=\"https://wikipedia.org/wiki/Operation_Breakthrough\" title=\"Operation Breakthrough\">a multinational effort to free the whales</a>.", "no_year_html": "A hunter discovers three gray whales trapped under the ice near Alaska; the situation becomes <a href=\"https://wikipedia.org/wiki/Operation_Breakthrough\" title=\"Operation Breakthrough\">a multinational effort to free the whales</a>.", "links": [{"title": "Operation Breakthrough", "link": "https://wikipedia.org/wiki/Operation_Breakthrough"}]}, {"year": "1991", "text": "Croatian War of Independence: Bombing of the Banski Dvori in Zagreb, Croatia.", "html": "1991 - Croatian War of Independence: <a href=\"https://wikipedia.org/wiki/Bombing_of_the_Banski_Dvori\" title=\"Bombing of the Banski Dvori\">Bombing of the Banski Dvori</a> in Zagreb, Croatia.", "no_year_html": "Croatian War of Independence: <a href=\"https://wikipedia.org/wiki/Bombing_of_the_Banski_Dvori\" title=\"Bombing of the Banski Dvori\">Bombing of the Banski Dvori</a> in Zagreb, Croatia.", "links": [{"title": "Bombing of the Banski Dvori", "link": "https://wikipedia.org/wiki/Bombing_of_the_Banski_Dvori"}]}, {"year": "1993", "text": "The flood of '93 ends at St. Louis, Missouri, 103 days after it began, as the Mississippi River falls below flood stage.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Great_Flood_of_1993\" title=\"Great Flood of 1993\">flood of '93</a> ends at St. Louis, Missouri, 103 days after it began, as the Mississippi River falls below flood stage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Flood_of_1993\" title=\"Great Flood of 1993\">flood of '93</a> ends at St. Louis, Missouri, 103 days after it began, as the Mississippi River falls below flood stage.", "links": [{"title": "Great Flood of 1993", "link": "https://wikipedia.org/wiki/Great_Flood_of_1993"}]}, {"year": "1996", "text": "Fox News Channel begins broadcasting.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Fox_News\" title=\"Fox News\">Fox News Channel</a> begins broadcasting.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fox_News\" title=\"Fox News\">Fox News Channel</a> begins broadcasting.", "links": [{"title": "Fox News", "link": "https://wikipedia.org/wiki/Fox_News"}]}, {"year": "1998", "text": "<PERSON>, a gay student at the University of Wyoming, is found tied to a fence after being savagely beaten by two young adults in Laramie, Wyoming. He dies five days later.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a gay student at the University of Wyoming, is found tied to a fence after being savagely beaten by two young adults in Laramie, Wyoming. He dies five days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a gay student at the University of Wyoming, is found tied to a fence after being savagely beaten by two young adults in Laramie, Wyoming. He dies five days later.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2000", "text": "Israeli-Palestinian conflict: Hezbollah militants capture three Israeli Defense Force soldiers in a cross-border raid.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a> militants capture three <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israeli Defense Force</a> soldiers in a <a href=\"https://wikipedia.org/wiki/2000_Hezbollah_cross-border_raid\" title=\"2000 Hezbollah cross-border raid\">cross-border raid</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a> militants capture three <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israeli Defense Force</a> soldiers in a <a href=\"https://wikipedia.org/wiki/2000_Hezbollah_cross-border_raid\" title=\"2000 Hezbollah cross-border raid\">cross-border raid</a>.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "Hezbollah", "link": "https://wikipedia.org/wiki/Hezbollah"}, {"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}, {"title": "2000 Hezbollah cross-border raid", "link": "https://wikipedia.org/wiki/2000_Hezbollah_cross-border_raid"}]}, {"year": "2001", "text": "The U.S. invasion of Afghanistan begins with an air assault and covert operations on the ground, starting the longest war in American history.", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/United_States_invasion_of_Afghanistan\" title=\"United States invasion of Afghanistan\">U.S. invasion of Afghanistan</a> begins with an air assault and covert operations on the ground, starting the <a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">longest war</a> in American history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_invasion_of_Afghanistan\" title=\"United States invasion of Afghanistan\">U.S. invasion of Afghanistan</a> begins with an air assault and covert operations on the ground, starting the <a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">longest war</a> in American history.", "links": [{"title": "United States invasion of Afghanistan", "link": "https://wikipedia.org/wiki/United_States_invasion_of_Afghanistan"}, {"title": "War in Afghanistan (2001-2021)", "link": "https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)"}]}, {"year": "2002", "text": "The Space Shuttle Atlantis launches on STS-112 to continue assembly of the International Space Station.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-112\" title=\"STS-112\">STS-112</a> to continue <a href=\"https://wikipedia.org/wiki/Assembly_of_the_International_Space_Station\" title=\"Assembly of the International Space Station\">assembly of the International Space Station</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-112\" title=\"STS-112\">STS-112</a> to continue <a href=\"https://wikipedia.org/wiki/Assembly_of_the_International_Space_Station\" title=\"Assembly of the International Space Station\">assembly of the International Space Station</a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-112", "link": "https://wikipedia.org/wiki/STS-112"}, {"title": "Assembly of the International Space Station", "link": "https://wikipedia.org/wiki/Assembly_of_the_International_Space_Station"}]}, {"year": "2004", "text": "Three bombs explode at Taba and Nuweiba in the Sinai Peninsula, Egypt, killing 34.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/2004_Sinai_bombings\" title=\"2004 Sinai bombings\">Three bombs explode</a> at <a href=\"https://wikipedia.org/wiki/Taba,_Egypt\" title=\"Taba, Egypt\">Taba</a> and <a href=\"https://wikipedia.org/wiki/Nuweiba\" title=\"Nuweiba\">N<PERSON>wei<PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>, Egypt, killing 34.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2004_Sinai_bombings\" title=\"2004 Sinai bombings\">Three bombs explode</a> at <a href=\"https://wikipedia.org/wiki/Taba,_Egypt\" title=\"Taba, Egypt\">Taba</a> and <a href=\"https://wikipedia.org/wiki/Nuweiba\" title=\"Nuweiba\">Nuweiba</a> in the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>, Egypt, killing 34.", "links": [{"title": "2004 Sinai bombings", "link": "https://wikipedia.org/wiki/2004_Sinai_bombings"}, {"title": "Taba, Egypt", "link": "https://wikipedia.org/wiki/Taba,_Egypt"}, {"title": "Nuweiba", "link": "https://wikipedia.org/wiki/Nuweiba"}, {"title": "Sinai Peninsula", "link": "https://wikipedia.org/wiki/Sinai_Peninsula"}]}, {"year": "2008", "text": "Asteroid 2008 TC3 impacts the Earth over Sudan, the first time an asteroid impact is detected prior to its entry into Earth's atmosphere.", "html": "2008 - Asteroid <a href=\"https://wikipedia.org/wiki/2008_TC3\" title=\"2008 TC3\">2008 TC3</a> impacts the Earth over Sudan, the first time an asteroid impact is detected prior to its entry into Earth's atmosphere.", "no_year_html": "Asteroid <a href=\"https://wikipedia.org/wiki/2008_TC3\" title=\"2008 TC3\">2008 TC3</a> impacts the Earth over Sudan, the first time an asteroid impact is detected prior to its entry into Earth's atmosphere.", "links": [{"title": "2008 TC3", "link": "https://wikipedia.org/wiki/2008_TC3"}]}, {"year": "2008", "text": "Qantas Flight 72 experiences an in-flight upset near Learmonth, Victoria, Australia, injuring 112.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Qantas_Flight_72\" title=\"Qantas Flight 72\">Qantas Flight 72</a> experiences an in-flight upset near <a href=\"https://wikipedia.org/wiki/Learmonth,_Victoria\" title=\"Learmonth, Victoria\">Learmonth, Victoria</a>, Australia, injuring 112.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qantas_Flight_72\" title=\"Qantas Flight 72\">Qantas Flight 72</a> experiences an in-flight upset near <a href=\"https://wikipedia.org/wiki/Learmonth,_Victoria\" title=\"Learmonth, Victoria\">Learmonth, Victoria</a>, Australia, injuring 112.", "links": [{"title": "Qantas Flight 72", "link": "https://wikipedia.org/wiki/Qantas_Flight_72"}, {"title": "Learmonth, Victoria", "link": "https://wikipedia.org/wiki/Learmonth,_Victoria"}]}, {"year": "2016", "text": "In the wake of Hurricane <PERSON>, the death toll rises to over 800.", "html": "2016 - In the wake of <a href=\"https://wikipedia.org/wiki/Hurricane_Matthew\" title=\"Hurricane Matthew\">Hurricane <PERSON></a>, the death toll rises to over 800.", "no_year_html": "In the wake of <a href=\"https://wikipedia.org/wiki/Hurricane_Matthew\" title=\"Hurricane Matthew\">Hurricane <PERSON></a>, the death toll rises to over 800.", "links": [{"title": "Hurricane Matthew", "link": "https://wikipedia.org/wiki/Hurricane_Matthew"}]}, {"year": "2022", "text": "10 people die and 8 are injured in an explosion at petrol station in Creeslough.", "html": "2022 - 10 people die and 8 are injured in <a href=\"https://wikipedia.org/wiki/Creeslough_explosion\" title=\"Creeslough explosion\">an explosion at petrol station</a> in <a href=\"https://wikipedia.org/wiki/Creeslough\" title=\"Creeslough\">Creeslough</a>.", "no_year_html": "10 people die and 8 are injured in <a href=\"https://wikipedia.org/wiki/Creeslough_explosion\" title=\"Creeslough explosion\">an explosion at petrol station</a> in <a href=\"https://wikipedia.org/wiki/Creeslough\" title=\"Creeslough\">Creeslough</a>.", "links": [{"title": "Creeslough explosion", "link": "https://wikipedia.org/wiki/Creeslough_explosion"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Creeslough"}]}, {"year": "2022", "text": "Ales Bialiatski, along with two organisations, Memorial & Center for Civil Liberties, are awarded the Nobel Peace Prize.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Ales_Bialiatski\" title=\"Ales Bialiatski\">Ales Bialiatski</a>, along with two organisations, <a href=\"https://wikipedia.org/wiki/Memorial_(society)\" title=\"Memorial (society)\">Memorial</a> &amp; <a href=\"https://wikipedia.org/wiki/Center_for_Civil_Liberties_(human_rights_organization)\" title=\"Center for Civil Liberties (human rights organization)\">Center for Civil Liberties</a>, are <a href=\"https://wikipedia.org/wiki/2022_Nobel_Peace_Prize\" title=\"2022 Nobel Peace Prize\">awarded</a> the <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ales_Bialiatski\" title=\"Ales Bialiatski\">Ales Bialiatski</a>, along with two organisations, <a href=\"https://wikipedia.org/wiki/Memorial_(society)\" title=\"Memorial (society)\">Memorial</a> &amp; <a href=\"https://wikipedia.org/wiki/Center_for_Civil_Liberties_(human_rights_organization)\" title=\"Center for Civil Liberties (human rights organization)\">Center for Civil Liberties</a>, are <a href=\"https://wikipedia.org/wiki/2022_Nobel_Peace_Prize\" title=\"2022 Nobel Peace Prize\">awarded</a> the <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize</a>.", "links": [{"title": "Ales B<PERSON>iatski", "link": "https://wikipedia.org/wiki/Ales_Bialiatski"}, {"title": "Memorial (society)", "link": "https://wikipedia.org/wiki/Memorial_(society)"}, {"title": "Center for Civil Liberties (human rights organization)", "link": "https://wikipedia.org/wiki/Center_for_Civil_Liberties_(human_rights_organization)"}, {"title": "2022 Nobel Peace Prize", "link": "https://wikipedia.org/wiki/2022_Nobel_Peace_Prize"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "2023", "text": "Hamas and several other Palestinian militant groups launch an attack into Israel, which results in the deaths of around 1,200, mostly civilians, and taking more than 240 hostages, including civilians and soldiers. The attack initiated the Israel-Hamas war.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> and several other <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> militant groups launch <a href=\"https://wikipedia.org/wiki/2023_Hamas-led_attack_on_Israel\" class=\"mw-redirect\" title=\"2023 Hamas-led attack on Israel\">an attack</a> into <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>, which results in the deaths of around 1,200, mostly civilians, and taking more than <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war_hostage_crisis\" class=\"mw-redirect\" title=\"Israel-Hamas war hostage crisis\">240 hostages</a>, including civilians and soldiers. The attack initiated the <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war\" class=\"mw-redirect\" title=\"Israel-Hamas war\">Israel-Hamas war</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> and several other <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> militant groups launch <a href=\"https://wikipedia.org/wiki/2023_Hamas-led_attack_on_Israel\" class=\"mw-redirect\" title=\"2023 Hamas-led attack on Israel\">an attack</a> into <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>, which results in the deaths of around 1,200, mostly civilians, and taking more than <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war_hostage_crisis\" class=\"mw-redirect\" title=\"Israel-Hamas war hostage crisis\">240 hostages</a>, including civilians and soldiers. The attack initiated the <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war\" class=\"mw-redirect\" title=\"Israel-Hamas war\">Israel-Hamas war</a>.", "links": [{"title": "Hamas", "link": "https://wikipedia.org/wiki/Hamas"}, {"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}, {"title": "2023 Hamas-led attack on Israel", "link": "https://wikipedia.org/wiki/2023_Hamas-led_attack_on_Israel"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Israel-Hamas war hostage crisis", "link": "https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war_hostage_crisis"}, {"title": "Israel-Hamas war", "link": "https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war"}]}], "Births": [{"year": "14 BC", "text": "<PERSON><PERSON><PERSON>, Roman politician (d. 23 AD)", "html": "14 BC - 14 BC - <a href=\"https://wikipedia.org/wiki/Dr<PERSON><PERSON>_Julius_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Julius Caesar\"><PERSON><PERSON><PERSON></a>, Roman politician (d. 23 AD)", "no_year_html": "14 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Julius_Caesar\" title=\"<PERSON><PERSON><PERSON> Julius Caesar\"><PERSON><PERSON><PERSON></a>, Roman politician (d. 23 AD)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1301", "text": "Grand Prince <PERSON> of Tver (d. 1339)", "html": "1301 - Grand Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Tver\" class=\"mw-redirect\" title=\"<PERSON> of Tver\"><PERSON> of Tver</a> (d. 1339)", "no_year_html": "Grand Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Tver\" class=\"mw-redirect\" title=\"<PERSON> of Tver\"><PERSON> of Tver</a> (d. 1339)", "links": [{"title": "<PERSON> of Tver", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Tver"}]}, {"year": "1409", "text": "<PERSON> of Luxembourg (d. 1442)", "html": "1409 - <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Luxembourg\" title=\"<PERSON> of Luxembourg\"><PERSON> of Luxembourg</a> (d. 1442)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_of_Luxembourg\" title=\"<PERSON> of Luxembourg\"><PERSON> of Luxembourg</a> (d. 1442)", "links": [{"title": "<PERSON> of Luxembourg", "link": "https://wikipedia.org/wiki/Elizabeth_of_Luxembourg"}]}, {"year": "1471", "text": "<PERSON> of Denmark (d. 1533)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (d. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (d. 1533)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark"}]}, {"year": "1474", "text": "<PERSON>, Margrave of Baden-Baden (d. 1536)", "html": "1474 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Baden-Baden\" title=\"<PERSON>, Margrave of Baden-Baden\"><PERSON>, Margrave of Baden-Baden</a> (d. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden\" title=\"<PERSON>, Margrave of Baden-Baden\"><PERSON>, Margrave of Baden-Baden</a> (d. 1536)", "links": [{"title": "<PERSON>, Margrave of Baden-Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden"}]}, {"year": "1482", "text": "<PERSON>, Margrave of Baden-Durlach (d. 1553)", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Mar<PERSON>_of_Baden-Durlach\" title=\"<PERSON>, Margrave of Baden-Durlach\"><PERSON>, Margrave of Baden-Durlach</a> (d. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Baden-Durlach\" title=\"<PERSON>, Margrave of Baden-Durlach\"><PERSON>, Margrave of Baden-Durlach</a> (d. 1553)", "links": [{"title": "<PERSON>, Margrave of Baden-Durlach", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Baden-Durlach"}]}, {"year": "1573", "text": "<PERSON>, English archbishop and academic (d. 1645)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (d. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1576", "text": "<PERSON>, English poet and playwright (d. 1634)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English poet and playwright (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English poet and playwright (d. 1634)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)"}]}, {"year": "1586", "text": "<PERSON>, Dutch diplomat (d. 1643)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch diplomat (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch diplomat (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1589", "text": "Archduchess <PERSON> of Austria (d. 1631)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria\" title=\"Archduchess <PERSON> of Austria\">Archduchess <PERSON> of Austria</a> (d. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria\" title=\"Archduchess <PERSON> of Austria\">Archduchess <PERSON> of Austria</a> (d. 1631)", "links": [{"title": "Archduchess <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria"}]}, {"year": "1591", "text": "<PERSON>, French architect (d. 1669)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (d. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1597", "text": "Captain <PERSON>, English settler and soldier (d. 1672)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/Captain_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Captain <PERSON>\">Captain <PERSON></a>, English settler and soldier (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Captain_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Captain <PERSON>\">Captain <PERSON></a>, English settler and soldier (d. 1672)", "links": [{"title": "Captain <PERSON>", "link": "https://wikipedia.org/wiki/Captain_<PERSON>_<PERSON>"}]}, {"year": "1635", "text": "<PERSON>, French painter (d. 1709)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON><PERSON>, English general (d. 1759)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Elliott\"><PERSON><PERSON></a>, English general (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Elliott\" title=\"<PERSON><PERSON> Elliott\"><PERSON><PERSON></a>, English general (d. 1759)", "links": [{"title": "<PERSON><PERSON> Elliott", "link": "https://wikipedia.org/wiki/Granville_Elliott"}]}, {"year": "1728", "text": "<PERSON>, American soldier, lawyer, and politician, 4th Governor of Delaware (d. 1784)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Delaware\" class=\"mw-redirect\" title=\"List of Governors of Delaware\">Governor of Delaware</a> (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Delaware\" class=\"mw-redirect\" title=\"List of Governors of Delaware\">Governor of Delaware</a> (d. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Delaware", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Delaware"}]}, {"year": "1744", "text": "<PERSON>, Russian general and politician, War Governor of Saint Petersburg (d. 1819)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/Saint_Petersburg_Governorate\" title=\"Saint Petersburg Governorate\">War Governor of Saint Petersburg</a> (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/Saint_Petersburg_Governorate\" title=\"Saint Petersburg Governorate\">War Governor of Saint Petersburg</a> (d. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Saint Petersburg Governorate", "link": "https://wikipedia.org/wiki/Saint_Petersburg_Governorate"}]}, {"year": "1746", "text": "<PERSON>, American composer and educator (d. 1800)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON> of Sweden (d. 1818)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (d. 1818)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_XIII_of_Sweden"}]}, {"year": "1769", "text": "<PERSON>, American lawyer, jurist, and politician, 1st Mayor of Detroit (d. 1846)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Detroit\" class=\"mw-redirect\" title=\"Mayor of Detroit\">Mayor of Detroit</a> (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Detroit\" class=\"mw-redirect\" title=\"Mayor of Detroit\">Mayor of Detroit</a> (d. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Detroit", "link": "https://wikipedia.org/wiki/Mayor_of_Detroit"}]}, {"year": "1786", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician (d. 1871)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (d. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON>, French instrument maker and businessman (d. 1875)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French instrument maker and businessman (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French instrument maker and businessman (d. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1819", "text": "<PERSON>, American author and patriot (d. 1905)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and patriot (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and patriot (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, American general (d. 1879)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (d. 1879)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(general)"}]}, {"year": "1832", "text": "<PERSON>, American lawyer and composer (d. 1918)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>verse\" title=\"<PERSON>verse\"><PERSON></a>, American lawyer and composer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>verse\" title=\"<PERSON>verse\"><PERSON></a>, American lawyer and composer (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>verse"}]}, {"year": "1835", "text": "<PERSON>, German composer and educator (d. 1913)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, Canadian scholar and jurist, 4th Chief Justice of Canada (d. 1911)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian scholar and jurist, 4th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian scholar and jurist, 4th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_<PERSON>z%C3%A9<PERSON>_<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1841", "text": "<PERSON> of Montenegro (d. 1921)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Montenegro\" title=\"<PERSON> of Montenegro\"><PERSON> of Montenegro</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Montenegro\" title=\"<PERSON> of Montenegro\"><PERSON> of Montenegro</a> (d. 1921)", "links": [{"title": "<PERSON> of Montenegro", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Montenegro"}]}, {"year": "1849", "text": "<PERSON>, American poet and author (d. 1916)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON>, Greek general and politician (d. 1936)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leon<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-Austrian religious leader, 26th Superior General of the Society of Jesus (d. 1942)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Austrian religious leader, 26th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Austrian religious leader, 26th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wlodi<PERSON>_Led%C3%<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1870", "text": "Uncle <PERSON>, American old-time country banjo player, singer-songwriter, and comedian (d. 1952)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Uncle_<PERSON>_<PERSON>\" title=\"Uncle <PERSON>\">Uncle <PERSON></a>, American old-time country banjo player, singer-songwriter, and comedian (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uncle_<PERSON>_<PERSON>\" title=\"Uncle <PERSON>\">Uncle <PERSON></a>, American old-time country banjo player, singer-songwriter, and comedian (d. 1952)", "links": [{"title": "Uncle <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, South African cricketer (d. 1934)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Swedish-born American labor activist and poet (d. 1915)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Swedish-born American labor activist and poet (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Swedish-born American labor activist and poet (d. 1915)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)"}]}, {"year": "1881", "text": "<PERSON>, Ukrainian-Russian general (d. 1918)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian general (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian general (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American lieutenant and pilot (d. 1927)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Danish physicist and philosopher, Nobel Prize laureate (d. 1962)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish physicist and philosopher, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish physicist and philosopher, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, American admiral, Medal of Honor recipient (d. 1948)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American admiral, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American admiral, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1887", "text": "<PERSON>, English cricketer and coach (d. 1961)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1887)\" title=\"<PERSON> (cricketer, born 1887)\"><PERSON></a>, English cricketer and coach (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1887)\" title=\"<PERSON> (cricketer, born 1887)\"><PERSON></a>, English cricketer and coach (d. 1961)", "links": [{"title": "<PERSON> (cricketer, born 1887)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1887)"}]}, {"year": "1888", "text": "<PERSON>, American agronomist and politician, 33rd Vice President of the United States (d. 1965)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American agronomist and politician, 33rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American agronomist and politician, 33rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1888", "text": "<PERSON>, American educator and activist (d. 1985)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and activist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and activist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1968)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, American director and producer (d. 1982)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and producer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and producer (d. 1982)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Trinidadian-American author and publisher (d. 1979)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-American author and publisher (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-American author and publisher (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Belgian linguist and author (d. 1980)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian linguist and author (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian linguist and author (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Spanish Filipino football player and manager (d. 1964)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Paulino_Alc%C3%A1ntara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish Filipino football player and manager (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paulino_Alc%C3%A1ntara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish Filipino football player and manager (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Paulino_Alc%C3%A1ntara"}]}, {"year": "1897", "text": "<PERSON>, American religious leader (d. 1975)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Burmese lawyer and politician (d. 1947)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Thakin_<PERSON><PERSON>\" title=\"Thakin Mya\"><PERSON><PERSON><PERSON></a>, Burmese lawyer and politician (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thakin_<PERSON><PERSON>\" title=\"Thakin Mya\"><PERSON><PERSON><PERSON></a>, Burmese lawyer and politician (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thakin_<PERSON>a"}]}, {"year": "1900", "text": "<PERSON>, German commander and politician (d. 1945)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German commander and politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German commander and politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Canadian ice hockey player and executive (d. 1977)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and executive (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and executive (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Italian footballer and coach (d. 1968)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and coach (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and coach (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American baseball player (d. 1958)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actor (d. 1977)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Scottish-American librarian and author (d. 1985)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American librarian and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American librarian and author (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Finnish author (d. 1990)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Ukrainian-American pianist and educator (d. 1995)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American pianist and educator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American pianist and educator (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON> 2nd, American soldier and politician, 72nd Mayor of Albany (d. 1983)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_2nd\" title=\"<PERSON><PERSON><PERSON> 2nd\"><PERSON><PERSON><PERSON> 2nd</a>, American soldier and politician, 72nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Albany\" class=\"mw-redirect\" title=\"Mayor of Albany\">Mayor of Albany</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_2nd\" title=\"<PERSON><PERSON><PERSON> 2nd\"><PERSON><PERSON><PERSON> 2nd</a>, American soldier and politician, 72nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Albany\" class=\"mw-redirect\" title=\"Mayor of Albany\">Mayor of Albany</a> (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON> 2nd", "link": "https://wikipedia.org/wiki/<PERSON>st<PERSON>_Corning_2nd"}, {"title": "Mayor of Albany", "link": "https://wikipedia.org/wiki/Mayor_of_Albany"}]}, {"year": "1910", "text": "<PERSON>, American art collector and philanthropist (d. 1986)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and philanthropist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and philanthropist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American singer, trumpet player, and bandleader (d. 1973)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Monroe\"><PERSON></a>, American singer, trumpet player, and bandleader (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Monroe\"><PERSON></a>, American singer, trumpet player, and bandleader (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Peruvian architect and politician, 85th President of Peru (d. 2002)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA<PERSON>_Terry\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Peruvian architect and politician, 85th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Peruvian architect and politician, 85th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Bela%C3%BAnde_Terry"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1912", "text": "<PERSON>, English race car driver (d. 1984)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 1984)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1913", "text": "<PERSON>, Dutch journalist and author (d. 1987)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and author (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Estonian pianist, guitarist, and composer (d. 1949)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist, guitarist, and composer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist, guitarist, and composer (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Indian actress (d. 1974)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Begum Akhtar\"><PERSON><PERSON></a>, Indian actress (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Begum Akhtar\"><PERSON><PERSON></a>, Indian actress (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Be<PERSON>_<PERSON>khtar"}]}, {"year": "1914", "text": "<PERSON>, English actress (d. 1982)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (d. 1982)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>(actress)"}]}, {"year": "1914", "text": "<PERSON>, American actor and singer (d. 1992)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American golfer (d. 2003)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, American actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON>\"><PERSON></a>, American actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American historian, philosopher, and academic (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, philosopher, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, philosopher, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American computer scientist and academic (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ram\"><PERSON><PERSON></a>, American computer scientist and academic (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ram"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Australian academic and politician, 19th Governor-General of Australia (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian academic and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian academic and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1919", "text": "<PERSON>, French historian and author (d. 1996)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, German soldier and politician, German Federal Minister of Defence (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)\" title=\"Federal Ministry of Defence (Germany)\">German Federal Minister of Defence</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)\" title=\"Federal Ministry of Defence (Germany)\">German Federal Minister of Defence</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Defence (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)"}]}, {"year": "1920", "text": "<PERSON>, English footballer and manager (d. 1998)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Belgian footballer and coach (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and coach (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American baseball player, coach, and manager (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American journalist and critic (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, German SS officer (d. 1945)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech animator and director (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/B%C5%99<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech animator and director (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C5%99<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech animator and director (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C5%99eti<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Canadian painter and sculptor (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian painter and sculptor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian painter and sculptor (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American basketball player (d. 1995)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer and actor (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2009)", "links": [{"title": "Al <PERSON>", "link": "https://wikipedia.org/wiki/Al_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON> <PERSON><PERSON>, Scottish psychiatrist and author (d. 1989)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Laing\" title=\"<PERSON><PERSON> <PERSON><PERSON> Laing\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish psychiatrist and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>._Laing\" title=\"R. <PERSON>. Laing\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish psychiatrist and author (d. 1989)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ng"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Spanish-Mexican film actor and singer (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Demetrio_Gonz%C3%A1lez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-Mexican film actor and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_Gonz%C3%A1lez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-Mexican film actor and singer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetrio_Gonz%C3%A1lez"}]}, {"year": "1928", "text": "<PERSON>, Brazilian composer, singer, writer, host and critic (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Me<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian composer, singer, writer, host and critic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Messi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian composer, singer, writer, host and critic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Me<PERSON>as"}]}, {"year": "1928", "text": "<PERSON>, Algerian politician (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, English autism researcher (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lorna Wing\"><PERSON><PERSON></a>, English autism researcher (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lorna Wing\"><PERSON><PERSON></a>, English autism researcher (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lorna_Wing"}]}, {"year": "1929", "text": "<PERSON>, Canadian director and producer, co-founded the IMAX Corporation (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, Canadian director and producer, co-founded the <a href=\"https://wikipedia.org/wiki/IMAX_Corporation\" title=\"IMAX Corporation\">IMAX Corporation</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, Canadian director and producer, co-founded the <a href=\"https://wikipedia.org/wiki/IMAX_Corporation\" title=\"IMAX Corporation\">IMAX Corporation</a> (d. 2021)", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>(filmmaker)"}, {"title": "IMAX Corporation", "link": "https://wikipedia.org/wiki/IMAX_Corporation"}]}, {"year": "1929", "text": "<PERSON>, American Catholic priest and author (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Catholic priest and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Catholic priest and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English journalist and author (d. 1993)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American race car driver (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Crider\"><PERSON></a>, American race car driver (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Curtis Crider\"><PERSON></a>, American race car driver (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rider"}]}, {"year": "1931", "text": "<PERSON>, American basketball player and coach (d. 2004)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fitzsimmons\"><PERSON></a>, American basketball player and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fitzsimmons\"><PERSON></a>, American basketball player and coach (d. 2004)", "links": [{"title": "Cotton Fitzsimmons", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mmons"}]}, {"year": "1931", "text": "<PERSON>, American football player and coach (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2014)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Sri Lankan journalist, lawyer, and academic (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist, lawyer, and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist, lawyer, and academic (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON>, South African archbishop and activist, Nobel Prize laureate (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African archbishop and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African archbishop and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Dutch bishop (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch bishop (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch bishop (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American race car driver and pilot (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and pilot (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and pilot (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American poet, playwright, and academic (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, playwright, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, playwright, and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, German far-left terrorist, co-founder of the Red Army Faction, journalist (d. 1976)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Far-left\" class=\"mw-redirect\" title=\"Far-left\">far-left</a> <a href=\"https://wikipedia.org/wiki/Terrorist\" class=\"mw-redirect\" title=\"Terrorist\">terrorist</a>, co-founder of the <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a>, journalist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Far-left\" class=\"mw-redirect\" title=\"Far-left\">far-left</a> <a href=\"https://wikipedia.org/wiki/Terrorist\" class=\"mw-redirect\" title=\"Terrorist\">terrorist</a>, co-founder of the <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a>, journalist (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Far-left", "link": "https://wikipedia.org/wiki/Far-left"}, {"title": "Terrorist", "link": "https://wikipedia.org/wiki/Terrorist"}, {"title": "Red Army Faction", "link": "https://wikipedia.org/wiki/Red_Army_Faction"}]}, {"year": "1934", "text": "<PERSON>, American basketball player (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English general and historian", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Marines_officer)\" title=\"<PERSON> (Royal Marines officer)\"><PERSON></a>, English general and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Marines_officer)\" title=\"<PERSON> (Royal Marines officer)\"><PERSON></a>, English general and historian", "links": [{"title": "<PERSON> (Royal Marines officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Marines_officer)"}]}, {"year": "1935", "text": "<PERSON>, Australian novelist, playwright, and essayist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian novelist, playwright, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian novelist, playwright, and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English director, producer, and screenwriter (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English journalist and author (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 1994)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>et_<PERSON>\" title=\"Chet Powers\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>et_<PERSON>\" title=\"Chet Powers\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1994)", "links": [{"title": "Chet <PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Polish academic and politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Szyszkowska"}]}, {"year": "1938", "text": "<PERSON>, Jamaican actress and theatre director", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican actress and theatre director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican actress and theatre director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English tennis player and sportscaster", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player and sportscaster", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1939", "text": "<PERSON>, American computer scientist and author", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian television host, author, and critic (d. 2019)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host, author, and critic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host, author, and critic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (d. 2016)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1939", "text": "<PERSON>, Congolese cardinal (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese cardinal (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese cardinal (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American football player and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American talk show host, comedian and television personality", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host, comedian and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host, comedian and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Cuban baseball player and coach", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>l\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>l\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Cardenal"}]}, {"year": "1943", "text": "<PERSON>, American colonel, journalist, and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Oliver_North\" title=\"Oliver North\"><PERSON></a>, American colonel, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oliver_North\" title=\"Oliver North\"><PERSON></a>, American colonel, journalist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_North"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American singer-songwriter and musician (d. 1979)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and musician (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and musician (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll"}]}, {"year": "1944", "text": "<PERSON>, Chinese civil servant and politician, 2nd Chief Executive of Hong Kong", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese civil servant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Executive_of_Hong_Kong\" title=\"Chief Executive of Hong Kong\">Chief Executive of Hong Kong</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese civil servant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Executive_of_Hong_Kong\" title=\"Chief Executive of Hong Kong\">Chief Executive of Hong Kong</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Executive of Hong Kong", "link": "https://wikipedia.org/wiki/Chief_Executive_of_Hong_Kong"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and director", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Scottish physicist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, Scottish physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, Scottish physicist and academic", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(physicist)"}]}, {"year": "1946", "text": "<PERSON>, Australian rugby player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American lawyer, activist, and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer, activist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer, activist, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian footballer and referee", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American poet and essayist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English climatologist and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English climatologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, English climatologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American composer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American bass player and priest", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and priest", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and priest", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American football player and coach (d. 2025)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Tanzanian colonel, economist, and politician, 4th President of Tanzania", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tanzanian colonel, economist, and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tanzanian colonel, economist, and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ki<PERSON>te"}, {"title": "President of Tanzania", "link": "https://wikipedia.org/wiki/President_of_Tanzania"}]}, {"year": "1951", "text": "<PERSON><PERSON>, French comic book creator, comics artist and film director", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French comic book creator, comics artist and film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French comic book creator, comics artist and film director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enki_Bilal"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Russian colonel and politician, 4th President of Russia", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Russia", "link": "https://wikipedia.org/wiki/President_of_Russia"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player (d. 2002)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian cricketer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian actress and playwright (d. 2014)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and playwright (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Estonian journalist and actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American drummer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer", "links": [{"title": "Tico Torres", "link": "https://wikipedia.org/wiki/Tico_Torres"}]}, {"year": "1955", "text": "<PERSON>, American computer scientist and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and author", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}]}, {"year": "1955", "text": "<PERSON>, Australian photographer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, French-American cellist and educator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American cellist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American cellist and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English rugby player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian-English sound engineer and producer (d. 2013)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English sound engineer and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English sound engineer and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Filipino basketball player, actor, and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player, actor, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player, actor, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English figure skater", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English businessman and record producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Peruvian lawyer and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Mexican wrestler (d. 2017)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Braz<PERSON>_de_Oro_(wrestler)\" title=\"<PERSON><PERSON><PERSON> <PERSON> Oro (wrestler)\"><PERSON><PERSON><PERSON> Oro</a>, Mexican wrestler (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Braz<PERSON>_de_Oro_(wrestler)\" title=\"<PERSON><PERSON><PERSON> <PERSON> Oro (wrestler)\"><PERSON><PERSON><PERSON></a>, Mexican wrestler (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1960", "text": "<PERSON>, American historian and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, American historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, American historian and author", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>(historian)"}]}, {"year": "1961", "text": "<PERSON>, Australian singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American football player and coach (d. 2018)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian businessman and politician, 35th Mayor of Calgary", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/Mayor_of_Calgary\" class=\"mw-redirect\" title=\"Mayor of Calgary\">Mayor of Calgary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/Mayor_of_Calgary\" class=\"mw-redirect\" title=\"Mayor of Calgary\">Mayor of Calgary</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of Calgary", "link": "https://wikipedia.org/wiki/Mayor_of_Calgary"}]}, {"year": "1962", "text": "<PERSON><PERSON>, English comedian", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, German-English cricketer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1962)\" title=\"<PERSON> (cricketer, born 1962)\"><PERSON></a>, German-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1962)\" title=\"<PERSON> (cricketer, born 1962)\"><PERSON></a>, German-English cricketer", "links": [{"title": "<PERSON> (cricketer, born 1962)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1962)"}]}, {"year": "1964", "text": "<PERSON>, English singer-songwriter, musician, and record producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter, musician, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter, musician, and record producer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1964", "text": "<PERSON>, American LGBT rights activist, journalist and television producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT rights activist, journalist and television producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Savage\"><PERSON></a>, American LGBT rights activist, journalist and television producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1964)\" title=\"<PERSON> (footballer, born 1964)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1964)\" title=\"<PERSON> (footballer, born 1964)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1964)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1964)"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Japanese race car driver", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>,  American novelist, short story writer, poet, and filmmaker", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sherman Alexie\"><PERSON></a>, American novelist, short story writer, poet, and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sherman Alexie\"><PERSON></a>, American novelist, short story writer, poet, and filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sherman_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Italian-American composer and conductor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian cyclist and author (d. 2012)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Australian cyclist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Australian cyclist and author (d. 2012)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)"}]}, {"year": "1967", "text": "<PERSON>, American law professor, author and activist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American law professor, author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American law professor, author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English golfer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, English golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American model and actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American wrestler", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hosa<PERSON>\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mali<PERSON>_<PERSON>saka"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian singer and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer and actor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Filipino basketball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1973)\" title=\"<PERSON><PERSON> (footballer, born 1973)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1973)\" title=\"<PERSON><PERSON> (footballer, born 1973)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1973)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1973)"}]}, {"year": "1973", "text": "<PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Finnish footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Sami_<PERSON>pi%C3%A4\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sami_<PERSON>%C3%A4\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sami_Hyypi%C3%A4"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Georgian politician and diplomat, 7th Prime Minister of Georgia", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian politician and diplomat, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Georgia\" title=\"Prime Minister of Georgia\">Prime Minister of Georgia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian politician and diplomat, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Georgia\" title=\"Prime Minister of Georgia\">Prime Minister of Georgia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Georgia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Georgia"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Danish skateboarder", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Glifberg\"><PERSON><PERSON></a>, Danish skateboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Glifberg\"><PERSON><PERSON></a>, Danish skateboarder", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Russian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_Nigmatullin\" title=\"Ruslan Nigmatullin\"><PERSON><PERSON><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_Nigmatullin\" title=\"Ruslan Nigmatullin\"><PERSON><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>llin"}]}, {"year": "1974", "text": "<PERSON>, Swedish singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English-Australian comedian, actor, and singer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian comedian, actor, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian comedian, actor, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American wrestler", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Rhyno\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON>no\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler", "links": [{"title": "Rhyno", "link": "https://wikipedia.org/wiki/Rhyno"}]}, {"year": "1976", "text": "<PERSON>, Spanish motorcycle racer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Argentinian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Santiago_Solari\" title=\"Santiago Solari\">Santiago Solari</a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Solari\" title=\"Santiago Solari\">Santiago Solari</a>, Argentinian footballer and manager", "links": [{"title": "Santiago Solari", "link": "https://wikipedia.org/wiki/Santiago_Solari"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French comics writer and illustrator", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French comics writer and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French comics writer and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English trumpet player and educator", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpet player and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter and dancer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Romanian gymnast", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Simona_Am%C3%A2nar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simon<PERSON>_Am%C3%A2nar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Simona_Am%C3%A2nar"}]}, {"year": "1979", "text": "<PERSON>, Canadian actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Algerian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>ri\" title=\"<PERSON><PERSON> Ginepri\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>ri\" title=\"<PERSON><PERSON> Ginepri\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ginepri"}]}, {"year": "1982", "text": "<PERSON>, American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1982", "text": "<PERSON>, Chinese pianist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chinese pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chinese pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English journalist and author", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bland\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Trinidadian cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American rapper, DJ, and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Flying_Lotus\" title=\"Flying Lotus\">Flying Lotus</a>, American rapper, DJ, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flying_Lotus\" title=\"Flying Lotus\">Flying Lotus</a>, American rapper, DJ, and producer", "links": [{"title": "Flying Lotus", "link": "https://wikipedia.org/wiki/Flying_Lotus"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sal<PERSON> Butt\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sal<PERSON> Butt\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Japanese actor and singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Danish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American soldier and politician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Daniel\" title=\"Chase Daniel\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Daniel\" title=\"Chase Daniel\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American soccer player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Faroese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Faroese_footballer)\" title=\"<PERSON><PERSON> (Faroese footballer)\"><PERSON><PERSON></a>, Faroese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Faroese_footballer)\" title=\"<PERSON><PERSON> (Faroese footballer)\"><PERSON><PERSON></a>, Faroese footballer", "links": [{"title": "<PERSON><PERSON> (Faroese footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Faroese_footballer)"}]}, {"year": "1986", "text": "<PERSON>, American actress, model, and former pornographic actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, model, and former pornographic actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, model, and former pornographic actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Holland_Roden\" title=\"Holland Roden\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Holland_Roden\" title=\"Holland Roden\"><PERSON></a>, American actress", "links": [{"title": "Holland Roden", "link": "https://wikipedia.org/wiki/Holland_Roden"}]}, {"year": "1986", "text": "<PERSON>, New Zealand cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, New Zealand footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Aiden_English\" class=\"mw-redirect\" title=\"Aiden English\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aiden_English\" class=\"mw-redirect\" title=\"Aiden English\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Aiden_English"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_<PERSON>rrey"}]}, {"year": "1988", "text": "<PERSON>, Brazilian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Coates\" title=\"Sebastián Coates\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Coates\" title=\"<PERSON><PERSON><PERSON><PERSON> Coates\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "Sebastián Coates", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_Coates"}]}, {"year": "1991", "text": "<PERSON>, Swedish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Chinese singer-songwriter and actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Canadian basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian professional footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian professional footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Norwegian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English professional footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English professional footballer", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)"}]}, {"year": "1995", "text": "<PERSON>, Dutch professional football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch professional football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch professional football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Scottish singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, South Korean Go player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Go_player)\" title=\"<PERSON> (Go player)\"><PERSON></a>, South Korean Go player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Go_player)\" title=\"<PERSON> (Go player)\"><PERSON></a>, South Korean Go player", "links": [{"title": "<PERSON> (Go player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Go_player)"}]}, {"year": "1997", "text": "<PERSON>, American actress and singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actress, writer, and activist", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, writer, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, writer, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, English footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American YouTuber", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "Princess <PERSON> <PERSON><PERSON><PERSON>, Princess of Lesotho", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Princess_Senate_<PERSON><PERSON><PERSON>\" title=\"Princess <PERSON> Seeiso\">Princess <PERSON> <PERSON><PERSON><PERSON></a>, Princess of Lesotho", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Senate_<PERSON><PERSON><PERSON>\" title=\"Princess <PERSON> Seeiso\">Princess <PERSON> <PERSON><PERSON><PERSON></a>, Princess of Lesotho", "links": [{"title": "Princess <PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Princess_Senate_See<PERSON>o"}]}], "Deaths": [{"year": "336", "text": "<PERSON>, pope of the Catholic Church", "html": "336 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pope Mark\"><PERSON></a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "858", "text": "<PERSON><PERSON>, Japanese emperor (b. 826)", "html": "858 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese emperor (b. 826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese emperor (b. 826)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "929", "text": "<PERSON> the <PERSON>, French king (b. 879)", "html": "929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Simple\" title=\"<PERSON> the Simple\"><PERSON> the Simple</a>, French king (b. 879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple\" title=\"<PERSON> the Simple\"><PERSON></a>, French king (b. 879)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple"}]}, {"year": "950", "text": "<PERSON>, Chinese empress consort", "html": "950 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Later_Jin)\" title=\"Empress <PERSON> (Later Jin)\"><PERSON></a>, Chinese empress consort", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Later_Jin)\" title=\"Empress <PERSON> (Later Jin)\"><PERSON></a>, Chinese empress consort", "links": [{"title": "<PERSON> (Later Jin)", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_(Later_Jin)"}]}, {"year": "951", "text": "<PERSON>, emperor of the Liao Dynasty (b. 919)", "html": "951 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON>zong of Liao\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Liao_dynasty\" title=\"Liao dynasty\">Liao Dynasty</a> (b. 919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON>zong of Liao\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Liao_dynasty\" title=\"Liao dynasty\">Liao Dynasty</a> (b. 919)", "links": [{"title": "Emperor <PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao"}, {"title": "Liao dynasty", "link": "https://wikipedia.org/wiki/Liao_dynasty"}]}, {"year": "951", "text": "<PERSON>, Chinese Khitan empress dowager", "html": "951 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(<PERSON><PERSON>)\" title=\"Empress Dow<PERSON> (<PERSON><PERSON>)\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Khitan_people\" title=\"Khitan people\">Khitan</a> empress dowager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(<PERSON><PERSON>)\" title=\"Empress <PERSON><PERSON> (<PERSON><PERSON>)\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Khitan_people\" title=\"Khitan people\">Khitan</a> empress dowager", "links": [{"title": "Empress <PERSON><PERSON> (<PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(<PERSON><PERSON>)"}, {"title": "Khitan people", "link": "https://wikipedia.org/wiki/Khitan_people"}]}, {"year": "951", "text": "<PERSON><PERSON>, Chinese Khitan empress consort", "html": "951 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_(Liao_dynasty)\" title=\"Empress <PERSON><PERSON> (Liao dynasty)\"><PERSON><PERSON></a>, Chinese Khitan empress consort", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_(Liao_dynasty)\" title=\"Empress <PERSON><PERSON> (Liao dynasty)\"><PERSON><PERSON></a>, Chinese Khitan empress consort", "links": [{"title": "Empress <PERSON><PERSON> (Liao dynasty)", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_(Liao_dynasty)"}]}, {"year": "988", "text": "<PERSON><PERSON>, king of Wuyue (b. 929)", "html": "988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/<PERSON>yu<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yue"}]}, {"year": "1242", "text": "<PERSON><PERSON><PERSON>, Japanese emperor (b. 1197)", "html": "1242 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 1197)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 1197)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1259", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian ruler", "html": "1259 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_III_da_Romano\" title=\"<PERSON><PERSON><PERSON><PERSON> III da Romano\"><PERSON><PERSON><PERSON><PERSON> da Romano</a>, Italian ruler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_III_da_Romano\" title=\"<PERSON><PERSON><PERSON><PERSON> III da Romano\"><PERSON><PERSON><PERSON><PERSON> III da Romano</a>, Italian ruler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> III da Romano", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1363", "text": "<PERSON>, English noblewoman (b. 1304)", "html": "1363 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Ormonde\" title=\"<PERSON>, Countess of Ormonde\"><PERSON></a>, English noblewoman (b. 1304)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Ormonde\" title=\"<PERSON>, Countess of Ormonde\"><PERSON></a>, English noblewoman (b. 1304)", "links": [{"title": "<PERSON>, Countess of Ormonde", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_<PERSON><PERSON>"}]}, {"year": "1368", "text": "<PERSON> of Antwerp, 1st Duke of Clarence, Belgian-English politician (b. 1338)", "html": "1368 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Antwerp,_1st_Duke_of_Clarence\" class=\"mw-redirect\" title=\"<PERSON> of Antwerp, 1st Duke of Clarence\"><PERSON> of Antwerp, 1st Duke of Clarence</a>, Belgian-English politician (b. 1338)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Antwerp,_1st_Duke_of_Clarence\" class=\"mw-redirect\" title=\"<PERSON> of Antwerp, 1st Duke of Clarence\"><PERSON> of Antwerp, 1st Duke of Clarence</a>, Belgian-English politician (b. 1338)", "links": [{"title": "<PERSON> of Antwerp, 1st Duke of Clarence", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Antwerp,_1st_Duke_<PERSON>_Clarence"}]}, {"year": "1461", "text": "<PERSON>, follower of <PERSON> Arc (b. c. 1390)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, follower of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Joan of Arc\"><PERSON> of Arc</a> (b. c. 1390)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, follower of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joan of Arc\"><PERSON> of Arc</a> (b. c. 1390)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1468", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian nobleman (b. 1417)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian nobleman (b. 1417)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian nobleman (b. 1417)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1553", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish composer (b. 1500)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/Crist%C3%B3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>rist<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish composer (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crist%C3%B3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>rist<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish composer (b. 1500)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Crist%C3%B3<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1571", "text": "<PERSON>, Ottoman soldier and politician, Ottoman Governor of Egypt", "html": "1571 - <a href=\"https://wikipedia.org/wiki/Sufi_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sufi <PERSON>\">Sufi <PERSON></a>, Ottoman soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_governors_of_Egypt\" title=\"List of Ottoman governors of Egypt\">Ottoman Governor of Egypt</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sufi_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sufi Ali <PERSON>\">Sufi <PERSON></a>, Ottoman soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_governors_of_Egypt\" title=\"List of Ottoman governors of Egypt\">Ottoman Governor of Egypt</a>", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ottoman governors of Egypt", "link": "https://wikipedia.org/wiki/List_of_Ottoman_governors_of_Egypt"}]}, {"year": "1571", "text": "<PERSON> of Saxe-Lauenburg, Danish queen consort of <PERSON> of Denmark (b. 1511)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"Dorothea of Saxe-Lauenburg\"><PERSON> of Saxe-Lauenburg</a>, Danish queen consort of <a href=\"https://wikipedia.org/wiki/Christian_III_of_Denmark\" title=\"Christian III of Denmark\">Christian <PERSON> of Denmark</a> (b. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxe-Lauenburg\" title=\"Dorothea of Saxe-Lauenburg\">Dorothea of Saxe-Lauenburg</a>, Danish queen consort of <a href=\"https://wikipedia.org/wiki/Christian_III_of_Denmark\" title=\"Christian III of Denmark\">Christian <PERSON> of Denmark</a> (b. 1511)", "links": [{"title": "Dorothea of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxe-Lauenburg"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_III_of_Denmark"}]}, {"year": "1577", "text": "<PERSON>, English soldier, courtier, and poet (b. 1535)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, courtier, and poet (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, courtier, and poet (b. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1612", "text": "<PERSON>, Italian poet, playwright, and diplomat (b. 1538)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet, playwright, and diplomat (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet, playwright, and diplomat (b. 1538)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON><PERSON>, Polish-Lithuanian commander (b. 1547)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_%C5%BB%C3%B3%C5%82kie<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Lithuanian commander (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_%C5%BB%C3%B3%C5%82kie<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Lithuanian commander (b. 1547)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_%C5%BB%C3%B3%C5%82<PERSON>wski"}]}, {"year": "1637", "text": "<PERSON>, duke of Savoy (b. 1587)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Savoy\" title=\"Savoy\">Savoy</a> (b. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Savoy\" title=\"Savoy\">Savoy</a> (b. 1587)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy"}, {"title": "Savoy", "link": "https://wikipedia.org/wiki/Savoy"}]}, {"year": "1651", "text": "<PERSON>, French scholar (b. 1559)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar (b. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar (b. 1559)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1653", "text": "<PERSON><PERSON>, Italian cardinal (b. 1581)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cardinal (b. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cardinal (b. 1581)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fausto_Poli"}]}, {"year": "1708", "text": "<PERSON><PERSON>, Indian 10th Sikh guru (b. 1666)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Indian 10th <a href=\"https://wikipedia.org/wiki/Sikh_guru\" class=\"mw-redirect\" title=\"Sikh guru\">Sikh guru</a> (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Indian 10th <a href=\"https://wikipedia.org/wiki/Sikh_guru\" class=\"mw-redirect\" title=\"Sikh guru\">Sikh guru</a> (b. 1666)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Sikh guru", "link": "https://wikipedia.org/wiki/Sikh_guru"}]}, {"year": "1747", "text": "<PERSON><PERSON><PERSON>, Italian painter (b. 1681)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (b. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (b. 1681)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lama"}]}, {"year": "1772", "text": "<PERSON>, American preacher and abolitionist (b. 1720)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American preacher and abolitionist (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American preacher and abolitionist (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, German-American pastor and missionary (b. 1711)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pastor and missionary (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pastor and missionary (b. 1711)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, American lawyer and politician (b. 1725)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, 1st Marquess of Downshire, English politician, President of the Board of Trade (b. 1718)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Downshire\" title=\"<PERSON>, 1st Marquess of Downshire\"><PERSON>, 1st Marquess of Downshire</a>, English politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Board_of_Trade\" title=\"President of the Board of Trade\">President of the Board of Trade</a> (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hill,_1st_Marquess_of_Downshire\" title=\"<PERSON>, 1st Marquess of Downshire\"><PERSON>, 1st Marquess of Downshire</a>, English politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Board_of_Trade\" title=\"President of the Board of Trade\">President of the Board of Trade</a> (b. 1718)", "links": [{"title": "<PERSON>, 1st Marquess of Downshire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Downshire"}, {"title": "President of the Board of Trade", "link": "https://wikipedia.org/wiki/President_of_the_Board_of_Trade"}]}, {"year": "1796", "text": "<PERSON>, Scottish mathematician and philosopher (b. 1710)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician and philosopher (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician and philosopher (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, American short story writer, poet, and critic (b. 1809)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, poet, and critic (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, poet, and critic (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, French Roman Catholic missionary to Japan (b. 1829)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Roman Catholic missionary to Japan (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Roman Catholic missionary to Japan (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American physician, author, and poet (b. 1809)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American physician, author, and poet (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American physician, author, and poet (b. 1809)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1903", "text": "<PERSON>, German mathematician and academic (b. 1832)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English historian and explorer (b. 1831)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bird\"><PERSON></a>, English historian and explorer (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bird\"><PERSON></a>, English historian and explorer (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Canadian journalist and politician, 18th Mayor of Montreal (b. 1848)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician, 18th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician, 18th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (b. 1848)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>nd"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1911", "text": "<PERSON>, English neurologist and physician (b. 1835)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English neurologist and physician (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English neurologist and physician (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Australian lawyer and politician, 2nd Prime Minister of Australia (b. 1856)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1925", "text": "<PERSON>, American baseball player and manager (b. 1880)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, German psychologist and academic (b. 1856)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychologist and academic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychologist and academic (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Australian politician, 20th Premier of Victoria (b. 1861)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1939", "text": "<PERSON>, American neurosurgeon and academic (b. 1869)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American neurosurgeon and academic (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American neurosurgeon and academic (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English author and poet (b. 1880)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Radclyffe_Hall\" title=\"Radclyffe Hall\">Radclyffe Hall</a>, English author and poet (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radclyffe_Hall\" title=\"Radclyffe Hall\">Rad<PERSON><PERSON>ffe Hall</a>, English author and poet (b. 1880)", "links": [{"title": "Radclyffe Hall", "link": "https://wikipedia.org/wiki/Rad<PERSON><PERSON><PERSON>_Hall"}]}, {"year": "1944", "text": "<PERSON>, German colonel and pilot (b. 1918)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American engineer (b. 1876)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Willis Carrier\"><PERSON></a>, American engineer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Willis Carrier\"><PERSON></a>, American engineer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Dutch businessman, co-founded Philips (b. 1874)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman, co-founded <a href=\"https://wikipedia.org/wiki/Philips\" title=\"<PERSON>\"><PERSON></a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman, co-founded <a href=\"https://wikipedia.org/wiki/Philips\" title=\"<PERSON>\"><PERSON></a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Philips", "link": "https://wikipedia.org/wiki/Philips"}]}, {"year": "1956", "text": "<PERSON>, American businessman, founded Birds Eye (b. 1886)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Eye\" title=\"Birds Eye\">Birds Eye</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Eye\" title=\"Birds Eye\">Birds Eye</a> (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Birds Eye", "link": "https://wikipedia.org/wiki/Birds_Eye"}]}, {"year": "1959", "text": "<PERSON>, American tenor and actor (b. 1921)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Indonesian military officer (b. 1918)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Atmaja\" title=\"Oking Jaya Atmaja\"><PERSON><PERSON></a>, Indonesian military officer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_At<PERSON>ja\" title=\"Oking Jaya Atmaja\"><PERSON><PERSON></a>, Indonesian military officer (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ing_Jaya_At<PERSON>ja"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter (b. 1890)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_Asikis"}]}, {"year": "1967", "text": "<PERSON>, English journalist and politician, Nobel Prize laureate (b. 1872)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1969", "text": "<PERSON>, Belgian cyclist (b. 1888)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_<PERSON><PERSON>r"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian priest and academic (b. 1906)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian priest and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian priest and academic (b. 1906)", "links": [{"title": "<PERSON>phon<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American astronomer, professor, science popularizer, and skeptic (b. 1927)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, professor, science popularizer, and skeptic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, professor, science popularizer, and skeptic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Turkish pianist, composer, and conductor (b. 1904)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Cemal_Re%C5%9Fit_Rey\" title=\"Cemal Reşit Rey\"><PERSON><PERSON></a>, Turkish pianist, composer, and conductor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cemal_Re%C5%9Fit_Rey\" title=\"Cemal Reş<PERSON> Rey\"><PERSON><PERSON></a>, Turkish pianist, composer, and conductor (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cemal_Re%C5%9Fit_Rey"}]}, {"year": "1990", "text": "<PERSON>, Australian architect (b. 1893)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Italian beatified (b.1971)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian beatified (b.1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian beatified (b.1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American animator (b. 1890)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON>wick\"><PERSON><PERSON></a>, American animator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_<PERSON>wick"}]}, {"year": "1991", "text": "<PERSON>, American colonel and pilot (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, American colonel and pilot (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, American colonel and pilot (b. 1921)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(pilot)"}]}, {"year": "1991", "text": "<PERSON>, American baseball player and manager (b. 1905)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian footballer (b. 1965)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American philosopher and educator (b. 1930)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and educator (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and educator (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Indian businessman and politician (b.1927)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian businessman and politician (b.1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian businessman and politician (b.1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, South African-born Irish actor (b. 1910)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born Irish actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born Irish actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Danish-English physician and immunologist, Nobel Prize laureate (b. 1911)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kaj Jerne\"><PERSON><PERSON></a>, Danish-English physician and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-English physician and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1995", "text": "<PERSON>, Australian film critic and author (b. 1928)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian film critic and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian film critic and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>-<PERSON>, Austrian-Czech-American mathematician, attendant of the Vienna Circle (b. 1906)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Czech-American mathematician, attendant of the Vienna Circle (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Czech-American mathematician, attendant of the Vienna Circle (b. 1906)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Surinamese-Dutch author, playwright, and politician (b. 1903)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese-Dutch author, playwright, and politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese-Dutch author, playwright, and politician (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Dutch strongman and weightlifter (b. 1952)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch strongman and weightlifter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch strongman and weightlifter (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>d"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American cartoonist and author (b. 1909)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist and author (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English-American wrestler and trainer (b. 1955)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, English-American wrestler and trainer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, English-American wrestler and trainer (b. 1955)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "2001", "text": "<PERSON>, Canadian chemist and businessman (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian chemist and businessman (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian chemist and businessman (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Italian singer-songwriter and guitarist (b. 1942)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Canadian lawyer and politician (b. 1932)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American composer and educator (b. 1912)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and educator (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and educator (b. 1912)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Japanese composer, lyricist, and singer (b. 1959)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer, lyricist, and singer (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer, lyricist, and singer (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English race car driver (b. 1935)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American actor and comedian (b. 1949)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles Rocket\"><PERSON></a>, American actor and comedian (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Spanish cyclist (b. 1985)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Julen_Goikoetxea\" title=\"Julen Goikoetxea\"><PERSON><PERSON></a>, Spanish cyclist (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julen_Goikoetxea\" title=\"Julen Goikoetxea\"><PERSON><PERSON></a>, Spanish cyclist (b. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>en_Go<PERSON>et<PERSON>a"}]}, {"year": "2006", "text": "<PERSON>, American-Russian journalist and activist (b. 1958)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Russian journalist and activist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Russian journalist and activist (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Japanese motorcycle racer (b. 1975)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Abe\"><PERSON><PERSON><PERSON></a>, Japanese motorcycle racer (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese motorcycle racer (b. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American lawyer and politician (b. 1931)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American photographer (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Irving Penn\"><PERSON></a>, American photographer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Irving Penn\"><PERSON></a>, American photographer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American keyboard player, composer, and producer (b. 1956)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/T_<PERSON><PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, American keyboard player, composer, and producer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, American keyboard player, composer, and producer (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Croatian lawyer and politician, 7th Prime Minister of Yugoslavia (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Planinc"}, {"title": "Prime Minister of Yugoslavia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Albanian politician, 1st President of Albania (b. 1925)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Albania\" title=\"President of Albania\">President of Albania</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Albania\" title=\"President of Albania\">President of Albania</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}, {"title": "President of Albania", "link": "https://wikipedia.org/wiki/President_of_Albania"}]}, {"year": "2011", "text": "<PERSON>, Hungarian-American cinematographer (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American cinematographer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American cinematographer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Trinidadian-American politician, 41st Lieutenant Governor of California (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ally\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Trinidadian-American politician, 41st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_California\" title=\"Lieutenant Governor of California\">Lieutenant Governor of California</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian-American politician, 41st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_California\" title=\"Lieutenant Governor of California\">Lieutenant Governor of California</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of California", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_California"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Belgian-French author and poet (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-French author and poet (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-French author and poet (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American-Australian singer-songwriter and pianist (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian singer-songwriter and pianist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian singer-songwriter and pianist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English footballer (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer (b. 1953)", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)"}]}, {"year": "2013", "text": "<PERSON>, American actress (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, French actor, director, producer, and screenwriter (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Patrice_Ch%C3%A9reau\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, producer, and screenwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pat<PERSON>_Ch%C3%A9reau\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, producer, and screenwriter (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patrice_Ch%C3%A9reau"}]}, {"year": "2013", "text": "<PERSON>, American admiral (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Filipino police officer and politician, 36th Executive Secretary of the Philippines (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino police officer and politician, 36th <a href=\"https://wikipedia.org/wiki/Executive_Secretary_(Philippines)\" title=\"Executive Secretary (Philippines)\">Executive Secretary of the Philippines</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino police officer and politician, 36th <a href=\"https://wikipedia.org/wiki/Executive_Secretary_(Philippines)\" title=\"Executive Secretary (Philippines)\">Executive Secretary of the Philippines</a> (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Executive Secretary (Philippines)", "link": "https://wikipedia.org/wiki/Executive_Secretary_(Philippines)"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician, 45th Lieutenant Governor of Colorado (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Colorado\" class=\"mw-redirect\" title=\"Lieutenant Governor of Colorado\">Lieutenant Governor of Colorado</a> (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Colorado\" class=\"mw-redirect\" title=\"Lieutenant Governor of Colorado\">Lieutenant Governor of Colorado</a> (b. 1964)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Lieutenant Governor of Colorado", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Colorado"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Georgian footballer (b. 1988)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian footballer (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian footballer (b. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Polish-German author and playwright (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-German author and playwright (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-German author and playwright (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Canadian-American actress and singer (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress and singer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress and singer (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_Withers"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Iranian general (b. 1951)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian general (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian general (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Marshallese politician, 5th President of the Marshall Islands (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Jurelang_Zedkaia\" title=\"Jurelang Zedkaia\"><PERSON><PERSON><PERSON></a>, Marshallese politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Marshall_Islands\" class=\"mw-redirect\" title=\"List of Presidents of the Marshall Islands\">President of the Marshall Islands</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jurelang_Zedkaia\" title=\"Jurelang Zedkai<PERSON>\"><PERSON><PERSON><PERSON></a>, Marshallese politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Marshall_Islands\" class=\"mw-redirect\" title=\"List of Presidents of the Marshall Islands\">President of the Marshall Islands</a> (b. 1950)", "links": [{"title": "Jurelang Zedkaia", "link": "https://wikipedia.org/wiki/Jurelang_Zedkaia"}, {"title": "List of Presidents of the Marshall Islands", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Marshall_Islands"}]}, {"year": "2016", "text": "<PERSON>, Australian actor, comedian (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, comedian (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, comedian (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Mexican chemist (b. 1943)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican chemist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican chemist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Indian actor (b. 1942)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Arun_Bali\" title=\"Arun <PERSON>\"><PERSON><PERSON></a>, Indian actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arun_Bali\" title=\"Arun <PERSON>\"><PERSON><PERSON></a>, Indian actor (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arun_Bali"}]}, {"year": "2023", "text": "Israelis murdered or fell in the line of duty during the October 7 attack\n<PERSON><PERSON>, Israeli footballer (b. 1980)\n<PERSON><PERSON>, Israeli police officer (b. 1979)\nAsaf Hamami Israeli soldier who served as commander of the Southern Brigade (b. 1982)\n<PERSON><PERSON>, American-Israeli peace activist (b. 1991)\n<PERSON><PERSON>, Israeli politician (b. 1973)\n<PERSON><PERSON>, Israel Defense Forces officer (b. 1979)\n<PERSON><PERSON>, Israeli-German tattoo artist (b.2001)\n<PERSON><PERSON><PERSON>, retired Israeli police officer (b. 1961)\n<PERSON><PERSON>, an off-duty Israeli soldier who has been credited with saving the lives of at least 7 people (b. 2001)\n<PERSON>, Canadian-Israeli peace activist (b. 1949)\n<PERSON><PERSON><PERSON>, Israel Defense Forces officer (b. 1980)\n<PERSON><PERSON><PERSON>, Israeli filmmaker (b. 1986)", "html": "2023 - Israelis murdered or fell in the line of duty during the <a href=\"https://wikipedia.org/wiki/October_7_attack\" class=\"mw-redirect\" title=\"October 7 attack\">October 7 attack</a>\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer (b. 1980)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli police officer (b. 1979)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Israeli soldier who served as commander of the Southern Brigade (b. 1982)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Israeli peace activist (b. 1991)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli politician (b. 1973)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Roi_Levy\" title=\"Roi Levy\">Roi Levy</a>, Israel Defense Forces officer (b. 1979)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Shani_Louk\" class=\"mw-redirect\" title=\"Shani Louk\">Shani Louk</a>, Israeli-German tattoo artist (b.2001)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Izhar_Peled\" title=\"Izhar Peled\">Izhar Peled</a>, retired Israeli police officer (b. 1961)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Aner_Shapira\" class=\"mw-redirect\" title=\"Aner Shapira\">Aner Shapira</a>, an off-duty Israeli soldier who has been credited with saving the lives of at least 7 people (b. 2001)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Vivian_Silver\" title=\"Vivian Silver\">Vivian Silver</a>, Canadian-Israeli peace activist (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yonatan_Steinberg\" title=\"Yonatan Steinberg\">Yonatan Steinberg</a>, Israel Defense Forces officer (b. 1980)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yahav_Winner\" title=\"Yahav Winner\">Yahav Winner</a>, Israeli filmmaker (b. 1986)</li>\n</ul>", "no_year_html": "Israelis murdered or fell in the line of duty during the <a href=\"https://wikipedia.org/wiki/October_7_attack\" class=\"mw-redirect\" title=\"October 7 attack\">October 7 attack</a>\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer (b. 1980)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli police officer (b. 1979)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Israeli soldier who served as commander of the Southern Brigade (b. 1982)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Israeli peace activist (b. 1991)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli politician (b. 1973)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Roi_Levy\" title=\"Roi Levy\">Roi Levy</a>, Israel Defense Forces officer (b. 1979)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Shani_Louk\" class=\"mw-redirect\" title=\"Shani Louk\">Shani Louk</a>, Israeli-German tattoo artist (b.2001)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Izhar_Peled\" title=\"Izhar Peled\">Izhar Peled</a>, retired Israeli police officer (b. 1961)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Aner_Shapira\" class=\"mw-redirect\" title=\"Aner Shapira\">Aner Shapira</a>, an off-duty Israeli soldier who has been credited with saving the lives of at least 7 people (b. 2001)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Vivian_Silver\" title=\"Vivian Silver\">Vivian Silver</a>, Canadian-Israeli peace activist (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yonatan_Steinberg\" title=\"Yonatan Steinberg\">Yonatan Steinberg</a>, Israel Defense Forces officer (b. 1980)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yahav_Winner\" title=\"Yahav Winner\">Yahav Winner</a>, Israeli filmmaker (b. 1986)</li>\n</ul>", "links": [{"title": "October 7 attack", "link": "https://wikipedia.org/wiki/October_7_attack"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ami"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Yahav Winner", "link": "https://wikipedia.org/wiki/Yahav_Winner"}]}, {"year": "<PERSON><PERSON>, Israeli footballer (b. 1980)", "text": null, "html": "<PERSON><PERSON>, Israeli footballer (b. 1980) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer (b. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Israeli police officer (b. 1979)", "text": null, "html": "<PERSON><PERSON>, Israeli police officer (b. 1979) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli police officer (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli police officer (b. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON> Israeli soldier who served as commander of the Southern Brigade (b. 1982)", "text": null, "html": "<PERSON><PERSON> Israeli soldier who served as commander of the Southern Brigade (b. 1982) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Israeli soldier who served as commander of the Southern Brigade (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> Israeli soldier who served as commander of the Southern Brigade (b. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ami"}]}, {"year": "<PERSON><PERSON>, American-Israeli peace activist (b. 1991)", "text": null, "html": "<PERSON><PERSON>, American-Israeli peace activist (b. 1991) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Israeli peace activist (b. 1991)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Israeli peace activist (b. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Israeli politician (b. 1973)", "text": null, "html": "<PERSON><PERSON>, Israeli politician (b. 1973) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli politician (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli politician (b. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "<PERSON><PERSON>, Israel Defense Forces officer (b. 1979)", "text": null, "html": "<PERSON><PERSON>, Israel Defense Forces officer (b. 1979) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Israel Defense Forces officer (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Israel Defense Forces officer (b. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Israeli-German tattoo artist (b.2001)", "text": null, "html": "<PERSON><PERSON>, Israeli-German tattoo artist (b.2001) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-German tattoo artist (b.2001)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-German tattoo artist (b.2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, retired Israeli police officer (b. 1961)", "text": null, "html": "<PERSON><PERSON><PERSON>, retired Israeli police officer (b. 1961) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, retired Israeli police officer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, retired Israeli police officer (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, an off-duty Israeli soldier who has been credited with saving the lives of at least 7 people (b. 2001)", "text": null, "html": "<PERSON><PERSON>, an off-duty Israeli soldier who has been credited with saving the lives of at least 7 people (b. 2001) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, an off-duty Israeli soldier who has been credited with saving the lives of at least 7 people (b. 2001)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, an off-duty Israeli soldier who has been credited with saving the lives of at least 7 people (b. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "<PERSON>, Canadian-Israeli peace activist (b. 1949)", "text": null, "html": "<PERSON>, Canadian-Israeli peace activist (b. 1949) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Israeli peace activist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Israeli peace activist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, Israel Defense Forces officer (b. 1980)", "text": null, "html": "<PERSON><PERSON><PERSON>, Israel Defense Forces officer (b. 1980) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Yo<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israel Defense Forces officer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israel Defense Forces officer (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, Israeli filmmaker (b. 1986)", "text": null, "html": "<PERSON>hav Winner, Israeli filmmaker (b. 1986) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Yahav_Winner\" title=\"Yahav Winner\">Yahav Winner</a>, Israeli filmmaker (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Yahav_Winner\" title=\"Yahav Winner\">Yahav Winner</a>, Israeli filmmaker (b. 1986)", "links": [{"title": "Yahav Winner", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Yahav_Winner"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American singer (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>y_Houston\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cissy_Houston"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American businessman (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman (b. 1938)", "links": [{"title": "Arie L<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Burmese politician, physician and former political prisoner (b. 1951)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Zaw Myint Maung\"><PERSON><PERSON></a>, Burmese politician, physician and former political prisoner (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Zaw Myint Maung\"><PERSON><PERSON></a>, Burmese politician, physician and former political prisoner (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American novelist (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>re_<PERSON>l"}]}]}}