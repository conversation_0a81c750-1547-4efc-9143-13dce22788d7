{"date": "January 5", "url": "https://wikipedia.org/wiki/January_5", "data": {"Events": [{"year": "1477", "text": "Battle of Nancy: <PERSON> the Bold is defeated and killed in a conflict with <PERSON>, Duke of Lorraine; Burgundy subsequently becomes part of France.", "html": "1477 - <a href=\"https://wikipedia.org/wiki/Battle_of_Nancy\" title=\"Battle of Nancy\">Battle of Nancy</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a> is defeated and killed in a conflict with <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_II,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a>; <a href=\"https://wikipedia.org/wiki/Duchy_of_Burgundy\" title=\"Duchy of Burgundy\">Burgundy</a> subsequently becomes part of France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Nancy\" title=\"Battle of Nancy\">Battle of Nancy</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a> is defeated and killed in a conflict with <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_II,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a>; <a href=\"https://wikipedia.org/wiki/Duchy_of_Burgundy\" title=\"Duchy of Burgundy\">Burgundy</a> subsequently becomes part of France.", "links": [{"title": "Battle of Nancy", "link": "https://wikipedia.org/wiki/Battle_of_Nancy"}, {"title": "<PERSON> the Bold", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/Ren%C3%A9_II,_<PERSON>_of_Lorraine"}, {"title": "Duchy of Burgundy", "link": "https://wikipedia.org/wiki/Duchy_of_Burgundy"}]}, {"year": "1675", "text": "Battle of Colmar: The French army defeats forces from Austria and Brandenburg.", "html": "1675 - <a href=\"https://wikipedia.org/wiki/Battle_of_Turckheim\" title=\"Battle of Turckheim\">Battle of Colmar</a>: The French army defeats forces from Austria and <a href=\"https://wikipedia.org/wiki/Brandenburg\" title=\"Brandenburg\">Brandenburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Turckheim\" title=\"Battle of Turckheim\">Battle of Colmar</a>: The French army defeats forces from Austria and <a href=\"https://wikipedia.org/wiki/Brandenburg\" title=\"Brandenburg\">Brandenburg</a>.", "links": [{"title": "Battle of Turckheim", "link": "https://wikipedia.org/wiki/Battle_of_Turckheim"}, {"title": "Brandenburg", "link": "https://wikipedia.org/wiki/Brandenburg"}]}, {"year": "1757", "text": "<PERSON> of France survives an assassination attempt by <PERSON><PERSON><PERSON>, who becomes the last person to be executed in France by drawing and quartering (the traditional form of capital punishment used for regicides).", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis XV\"><PERSON></a> of France survives an <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassination</a> attempt by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, who becomes the last person to be <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">executed</a> in France by <a href=\"https://wikipedia.org/wiki/Hanged,_drawn_and_quartered\" title=\"Hanged, drawn and quartered\">drawing and quartering</a> (the traditional form of capital punishment used for <a href=\"https://wikipedia.org/wiki/Regicide\" title=\"Regicide\">regicides</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis XV\"><PERSON></a> of France survives an <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassination</a> attempt by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, who becomes the last person to be <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">executed</a> in France by <a href=\"https://wikipedia.org/wiki/Hanged,_drawn_and_quartered\" title=\"Hanged, drawn and quartered\">drawing and quartering</a> (the traditional form of capital punishment used for <a href=\"https://wikipedia.org/wiki/Regicide\" title=\"Regicide\">regicides</a>).", "links": [{"title": "Louis XV", "link": "https://wikipedia.org/wiki/Louis_XV"}, {"title": "Assassination", "link": "https://wikipedia.org/wiki/Assassination"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A7<PERSON>_<PERSON>s"}, {"title": "Capital punishment", "link": "https://wikipedia.org/wiki/Capital_punishment"}, {"title": "Hanged, drawn and quartered", "link": "https://wikipedia.org/wiki/Hanged,_drawn_and_quartered"}, {"title": "Regicide", "link": "https://wikipedia.org/wiki/Regicide"}]}, {"year": "1781", "text": "American Revolutionary War: Richmond, Virginia, is burned by British naval forces led by former American general <PERSON>.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>, is <a href=\"https://wikipedia.org/wiki/Raid_on_Richmond\" title=\"Raid on Richmond\">burned</a> by <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> <a href=\"https://wikipedia.org/wiki/Her_Majesty%27s_Naval_Service\" class=\"mw-redirect\" title=\"Her Majesty's Naval Service\">naval forces</a> led by former American general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>, is <a href=\"https://wikipedia.org/wiki/Raid_on_Richmond\" title=\"Raid on Richmond\">burned</a> by <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> <a href=\"https://wikipedia.org/wiki/Her_Majesty%27s_Naval_Service\" class=\"mw-redirect\" title=\"Her Majesty's Naval Service\">naval forces</a> led by former American general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}, {"title": "Raid on Richmond", "link": "https://wikipedia.org/wiki/Raid_on_Richmond"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Her Majesty's Naval Service", "link": "https://wikipedia.org/wiki/Her_Majesty%27s_Naval_Service"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "The government of Central America votes for total annexation to the First Mexican Empire.", "html": "1822 - The government of <a href=\"https://wikipedia.org/wiki/Central_America\" title=\"Central America\">Central America</a> votes for <a href=\"https://wikipedia.org/wiki/Central_America_under_Mexican_rule\" title=\"Central America under Mexican rule\">total annexation</a> to the <a href=\"https://wikipedia.org/wiki/First_Mexican_Empire\" title=\"First Mexican Empire\">First Mexican Empire</a>.", "no_year_html": "The government of <a href=\"https://wikipedia.org/wiki/Central_America\" title=\"Central America\">Central America</a> votes for <a href=\"https://wikipedia.org/wiki/Central_America_under_Mexican_rule\" title=\"Central America under Mexican rule\">total annexation</a> to the <a href=\"https://wikipedia.org/wiki/First_Mexican_Empire\" title=\"First Mexican Empire\">First Mexican Empire</a>.", "links": [{"title": "Central America", "link": "https://wikipedia.org/wiki/Central_America"}, {"title": "Central America under Mexican rule", "link": "https://wikipedia.org/wiki/Central_America_under_Mexican_rule"}, {"title": "First Mexican Empire", "link": "https://wikipedia.org/wiki/First_Mexican_Empire"}]}, {"year": "1875", "text": "The Palais Garnier, one of the most famous opera houses in the world, is inaugurated in Paris.", "html": "1875 - The <a href=\"https://wikipedia.org/wiki/Palais_Garnier\" title=\"Palais Garnier\">Palais Garnier</a>, one of the most famous <a href=\"https://wikipedia.org/wiki/Opera_house\" title=\"Opera house\">opera houses</a> in the world, is inaugurated in Paris.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Palais_Garnier\" title=\"Palais Garnier\">Palais Garnier</a>, one of the most famous <a href=\"https://wikipedia.org/wiki/Opera_house\" title=\"Opera house\">opera houses</a> in the world, is inaugurated in Paris.", "links": [{"title": "Palais Garnier", "link": "https://wikipedia.org/wiki/Palais_Garnier"}, {"title": "Opera house", "link": "https://wikipedia.org/wiki/Opera_house"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON> affair: French army officer <PERSON> is stripped of his rank and sentenced to life imprisonment on Devil's Island.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair\" title=\"<PERSON><PERSON><PERSON><PERSON> affair\"><PERSON><PERSON><PERSON><PERSON> affair</a>: French army officer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is stripped of his rank and sentenced to life imprisonment on <a href=\"https://wikipedia.org/wiki/Devil%27s_Island\" title=\"Devil's Island\">Devil's Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair\" title=\"<PERSON><PERSON><PERSON><PERSON> affair\"><PERSON><PERSON><PERSON><PERSON> affair</a>: French army officer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is stripped of his rank and sentenced to life imprisonment on <a href=\"https://wikipedia.org/wiki/Devil%27s_Island\" title=\"Devil's Island\">Devil's Island</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> affair", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Devil's Island", "link": "https://wikipedia.org/wiki/Devil%27s_Island"}]}, {"year": "1900", "text": "Irish nationalist leader <PERSON> calls for revolt against British rule.", "html": "1900 - Irish nationalist leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> calls for revolt against British rule.", "no_year_html": "Irish nationalist leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> calls for revolt against British rule.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "Kappa Alpha Psi, the world's third-oldest and largest black fraternity, is founded at Indiana University.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Kappa_Alpha_Psi\" title=\"Kappa Alpha Psi\">Kappa Alpha Psi</a>, the world's third-oldest and largest <a href=\"https://wikipedia.org/wiki/Black_fraternity\" class=\"mw-redirect\" title=\"Black fraternity\">black fraternity</a>, is founded at <a href=\"https://wikipedia.org/wiki/Indiana_University\" title=\"Indiana University\">Indiana University</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kappa_Alpha_Psi\" title=\"Kappa Alpha Psi\">Kappa Alpha Psi</a>, the world's third-oldest and largest <a href=\"https://wikipedia.org/wiki/Black_fraternity\" class=\"mw-redirect\" title=\"Black fraternity\">black fraternity</a>, is founded at <a href=\"https://wikipedia.org/wiki/Indiana_University\" title=\"Indiana University\">Indiana University</a>.", "links": [{"title": "Kappa Alpha Psi", "link": "https://wikipedia.org/wiki/Kappa_Alpha_Psi"}, {"title": "Black fraternity", "link": "https://wikipedia.org/wiki/Black_fraternity"}, {"title": "Indiana University", "link": "https://wikipedia.org/wiki/Indiana_University"}]}, {"year": "1912", "text": "The sixth All-Russian Conference of the Russian Social Democratic Labour Party (Prague Party Conference) opens. In the course of the conference, <PERSON> and his supporters break from the rest of the party to form the Bolshevik movement.", "html": "1912 - The sixth All-Russian Conference of the <a href=\"https://wikipedia.org/wiki/Russian_Social_Democratic_Labour_Party\" title=\"Russian Social Democratic Labour Party\">Russian Social Democratic Labour Party</a> (<a href=\"https://wikipedia.org/wiki/Prague_Party_Conference\" class=\"mw-redirect\" title=\"Prague Party Conference\">Prague Party Conference</a>) opens. In the course of the conference, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Vladimir <PERSON>\"><PERSON></a> and his supporters break from the rest of the party to form the <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> movement.", "no_year_html": "The sixth All-Russian Conference of the <a href=\"https://wikipedia.org/wiki/Russian_Social_Democratic_Labour_Party\" title=\"Russian Social Democratic Labour Party\">Russian Social Democratic Labour Party</a> (<a href=\"https://wikipedia.org/wiki/Prague_Party_Conference\" class=\"mw-redirect\" title=\"Prague Party Conference\">Prague Party Conference</a>) opens. In the course of the conference, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his supporters break from the rest of the party to form the <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> movement.", "links": [{"title": "Russian Social Democratic Labour Party", "link": "https://wikipedia.org/wiki/Russian_Social_Democratic_Labour_Party"}, {"title": "Prague Party Conference", "link": "https://wikipedia.org/wiki/Prague_Party_Conference"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lenin"}, {"title": "Bolshevik", "link": "https://wikipedia.org/wiki/Bolshevik"}]}, {"year": "1913", "text": "First Balkan War: The Battle of Lemnos begins; Greek admiral <PERSON><PERSON><PERSON> forces the Turkish fleet to retreat to its base within the Dardanelles, from which it did not venture for the rest of the war.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Lemnos_(1913)\" title=\"Battle of Lemnos (1913)\">Battle of Lemnos</a> begins; Greek admiral <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> forces the Turkish fleet to retreat to its base within the <a href=\"https://wikipedia.org/wiki/Dardanelles\" title=\"Dardanelles\">Dardanelles</a>, from which it did not venture for the rest of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Lemnos_(1913)\" title=\"Battle of Lemnos (1913)\">Battle of Lemnos</a> begins; Greek admiral <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> forces the Turkish fleet to retreat to its base within the <a href=\"https://wikipedia.org/wiki/Dardanelles\" title=\"Dardanelles\">Dardanelles</a>, from which it did not venture for the rest of the war.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Battle of Lemnos (1913)", "link": "https://wikipedia.org/wiki/Battle_of_Lemnos_(1913)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Dardanelles", "link": "https://wikipedia.org/wiki/Dardanelles"}]}, {"year": "1914", "text": "The Ford Motor Company announces an eight-hour workday and minimum daily wage of $5 in salary plus bonuses.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> announces an <a href=\"https://wikipedia.org/wiki/Eight-hour_day\" class=\"mw-redirect\" title=\"Eight-hour day\">eight-hour workday</a> and minimum daily wage of $5 in salary plus bonuses.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> announces an <a href=\"https://wikipedia.org/wiki/Eight-hour_day\" class=\"mw-redirect\" title=\"Eight-hour day\">eight-hour workday</a> and minimum daily wage of $5 in salary plus bonuses.", "links": [{"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}, {"title": "Eight-hour day", "link": "https://wikipedia.org/wiki/Eight-hour_day"}]}, {"year": "1919", "text": "The German Workers' Party, which would become the Nazi Party, is founded in Munich.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/German_Workers%27_Party\" title=\"German Workers' Party\">German Workers' Party</a>, which would become the <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a>, is founded in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/German_Workers%27_Party\" title=\"German Workers' Party\">German Workers' Party</a>, which would become the <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a>, is founded in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>.", "links": [{"title": "German Workers' Party", "link": "https://wikipedia.org/wiki/German_Workers%27_Party"}, {"title": "Nazi Party", "link": "https://wikipedia.org/wiki/Nazi_Party"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}]}, {"year": "1925", "text": "<PERSON><PERSON> of Wyoming becomes the first female governor in the United States.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Wyoming\" title=\"Wyoming\">Wyoming</a> becomes the first female governor in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Wyoming\" title=\"Wyoming\">Wyoming</a> becomes the first female governor in the United States.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Wyoming", "link": "https://wikipedia.org/wiki/Wyoming"}]}, {"year": "1933", "text": "Construction of the Golden Gate Bridge begins in San Francisco Bay.", "html": "1933 - Construction of the <a href=\"https://wikipedia.org/wiki/Golden_Gate_Bridge\" title=\"Golden Gate Bridge\">Golden Gate Bridge</a> begins in <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a>.", "no_year_html": "Construction of the <a href=\"https://wikipedia.org/wiki/Golden_Gate_Bridge\" title=\"Golden Gate Bridge\">Golden Gate Bridge</a> begins in <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a>.", "links": [{"title": "Golden Gate Bridge", "link": "https://wikipedia.org/wiki/Golden_Gate_Bridge"}, {"title": "San Francisco Bay", "link": "https://wikipedia.org/wiki/San_Francisco_Bay"}]}, {"year": "1941", "text": "<PERSON>, a 37-year-old pilot and the first woman to fly solo from London to Australia, disappears after bailing out of her plane over the River Thames, and is presumed dead.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 37-year-old pilot and the first woman to fly solo from London to Australia, disappears after bailing out of her plane over the River Thames, and is presumed dead.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 37-year-old pilot and the first woman to fly solo from London to Australia, disappears after bailing out of her plane over the River Thames, and is presumed dead.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "The Daily Mail becomes the first major London newspaper to be published on both sides of the Atlantic Ocean.", "html": "1944 - The <i><a href=\"https://wikipedia.org/wiki/Daily_Mail\" title=\"Daily Mail\">Daily Mail</a></i> becomes the first major <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> newspaper to be published on both sides of the Atlantic Ocean.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Daily_Mail\" title=\"Daily Mail\">Daily Mail</a></i> becomes the first major <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> newspaper to be published on both sides of the Atlantic Ocean.", "links": [{"title": "Daily Mail", "link": "https://wikipedia.org/wiki/Daily_Mail"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1945", "text": "The Soviet Union recognizes the new pro-Soviet Provisional Government of the Republic of Poland.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> recognizes the new pro-Soviet <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Poland\" title=\"Provisional Government of the Republic of Poland\">Provisional Government of the Republic of Poland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> recognizes the new pro-Soviet <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Poland\" title=\"Provisional Government of the Republic of Poland\">Provisional Government of the Republic of Poland</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Provisional Government of the Republic of Poland", "link": "https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Poland"}]}, {"year": "1949", "text": "In his State of the Union address, United States President <PERSON> unveils his Fair Deal program.", "html": "1949 - In his State of the Union address, United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> unveils his <a href=\"https://wikipedia.org/wiki/Fair_Deal\" title=\"Fair Deal\">Fair Deal</a> program.", "no_year_html": "In his State of the Union address, United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> unveils his <a href=\"https://wikipedia.org/wiki/Fair_Deal\" title=\"Fair Deal\">Fair Deal</a> program.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Fair Deal", "link": "https://wikipedia.org/wiki/Fair_Deal"}]}, {"year": "1953", "text": "The play Waiting for <PERSON><PERSON> by <PERSON> receives its première in Paris.", "html": "1953 - The play <i><a href=\"https://wikipedia.org/wiki/Waiting_for_<PERSON><PERSON>\" title=\"Waiting for <PERSON><PERSON>\">Waiting for <PERSON><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives its première in Paris.", "no_year_html": "The play <i><a href=\"https://wikipedia.org/wiki/Waiting_for_<PERSON><PERSON>\" title=\"Waiting for <PERSON><PERSON>\">Waiting for <PERSON><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives its première in Paris.", "links": [{"title": "Waiting for <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waiting_for_<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "In a speech given to the United States Congress, United States President <PERSON> announces the establishment of what will later be called the Eisenhower Doctrine.", "html": "1957 - In a speech given to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the establishment of what will later be called the <a href=\"https://wikipedia.org/wiki/Eisenhower_Doctrine\" title=\"Eisenhower Doctrine\">Eisenhower Doctrine</a>.", "no_year_html": "In a speech given to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the establishment of what will later be called the <a href=\"https://wikipedia.org/wiki/<PERSON>_Doctrine\" title=\"Eisenhower Doctrine\">Eisenhower Doctrine</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Eisenhower Doctrine", "link": "https://wikipedia.org/wiki/Eisenhower_Doctrine"}]}, {"year": "1967", "text": "Cultural Revolution: The Shanghai People's Commune is established following the seizure of power from local city officials by revolutionaries.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Shanghai_People%27s_Commune\" class=\"mw-redirect\" title=\"Shanghai People's Commune\">Shanghai People's Commune</a> is established following the <a href=\"https://wikipedia.org/wiki/Seizure_of_power_(Cultural_Revolution)\" title=\"Seizure of power (Cultural Revolution)\">seizure of power</a> from local city officials by revolutionaries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Shanghai_People%27s_Commune\" class=\"mw-redirect\" title=\"Shanghai People's Commune\">Shanghai People's Commune</a> is established following the <a href=\"https://wikipedia.org/wiki/Seizure_of_power_(Cultural_Revolution)\" title=\"Seizure of power (Cultural Revolution)\">seizure of power</a> from local city officials by revolutionaries.", "links": [{"title": "Cultural Revolution", "link": "https://wikipedia.org/wiki/Cultural_Revolution"}, {"title": "Shanghai People's Commune", "link": "https://wikipedia.org/wiki/Shanghai_People%27s_Commune"}, {"title": "Seizure of power (Cultural Revolution)", "link": "https://wikipedia.org/wiki/Seizure_of_power_(Cultural_Revolution)"}]}, {"year": "1968", "text": "<PERSON> comes to power in Czechoslovakia, effectively beginning the \"Prague Spring\".", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a> comes to power in <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>, effectively beginning the \"<a href=\"https://wikipedia.org/wiki/Prague_Spring\" title=\"Prague Spring\">Prague Spring</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a> comes to power in <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>, effectively beginning the \"<a href=\"https://wikipedia.org/wiki/Prague_Spring\" title=\"Prague Spring\">Prague Spring</a>\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_Dub%C4%8Dek"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Prague Spring", "link": "https://wikipedia.org/wiki/Prague_Spring"}]}, {"year": "1969", "text": "The Venera 5 space probe is launched at 06:28:08 UTC from Baikonur.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Venera_5\" title=\"Venera 5\">Venera 5</a> space probe is launched at 06:28:08 UTC from <a href=\"https://wikipedia.org/wiki/Baikonur_Cosmodrome\" title=\"Baikonur Cosmodrome\">Baikonur</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Venera_5\" title=\"Venera 5\">Venera 5</a> space probe is launched at 06:28:08 UTC from <a href=\"https://wikipedia.org/wiki/Baikonur_Cosmodrome\" title=\"Baikonur Cosmodrome\">Baikonur</a>.", "links": [{"title": "Venera 5", "link": "https://wikipedia.org/wiki/Venera_5"}, {"title": "Baikonur Cosmodrome", "link": "https://wikipedia.org/wiki/Baikonur_Cosmodrome"}]}, {"year": "1969", "text": "Ariana Afghan Airlines Flight 701 crashes in Fernhill, West Sussex, UK, while on approach to Gatwick Airport, killing 50 people.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ariana_Afghan_Airlines_Flight_701\" title=\"Ariana Afghan Airlines Flight 701\">Ariana Afghan Airlines Flight 701</a> crashes in <a href=\"https://wikipedia.org/wiki/Fernhill,_West_Sussex\" title=\"Fernhill, West Sussex\">Fernhill, West Sussex</a>, UK, while on approach to <a href=\"https://wikipedia.org/wiki/Gatwick_Airport\" title=\"Gatwick Airport\">Gatwick Airport</a>, killing 50 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ariana_Afghan_Airlines_Flight_701\" title=\"Ariana Afghan Airlines Flight 701\">Ariana Afghan Airlines Flight 701</a> crashes in <a href=\"https://wikipedia.org/wiki/Fernhill,_West_Sussex\" title=\"Fernhill, West Sussex\">Fernhill, West Sussex</a>, UK, while on approach to <a href=\"https://wikipedia.org/wiki/Gatwick_Airport\" title=\"Gatwick Airport\">Gatwick Airport</a>, killing 50 people.", "links": [{"title": "Ariana Afghan Airlines Flight 701", "link": "https://wikipedia.org/wiki/Ariana_Afghan_Airlines_Flight_701"}, {"title": "Fernhill, West Sussex", "link": "https://wikipedia.org/wiki/Fernhill,_West_Sussex"}, {"title": "Gatwick Airport", "link": "https://wikipedia.org/wiki/Gatwick_Airport"}]}, {"year": "1970", "text": "The 7.1 Mw  Tonghai earthquake shakes Tonghai County, Yunnan province, China, with a maximum Mercalli intensity of X (Extreme). Between 10,000 and 15,000 people are known to have been killed and about another 26,000 are injured.", "html": "1970 - The 7.1 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1970_Tonghai_earthquake\" title=\"1970 Tonghai earthquake\">Tonghai earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Tonghai_County\" title=\"Tonghai County\">Tonghai County</a>, <a href=\"https://wikipedia.org/wiki/Yunnan\" title=\"Yunnan\">Yunnan</a> province, China, with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>). Between 10,000 and 15,000 people are known to have been killed and about another 26,000 are injured.", "no_year_html": "The 7.1 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1970_Tonghai_earthquake\" title=\"1970 Tonghai earthquake\">Tonghai earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Tonghai_County\" title=\"Tonghai County\">Tonghai County</a>, <a href=\"https://wikipedia.org/wiki/Yunnan\" title=\"Yunnan\">Yunnan</a> province, China, with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>). Between 10,000 and 15,000 people are known to have been killed and about another 26,000 are injured.", "links": [{"title": "1970 Tonghai earthquake", "link": "https://wikipedia.org/wiki/1970_Tonghai_earthquake"}, {"title": "Tonghai County", "link": "https://wikipedia.org/wiki/Tonghai_County"}, {"title": "Yunnan", "link": "https://wikipedia.org/wiki/Yunnan"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1972", "text": "US President <PERSON> announces the Space Shuttle program.", "html": "1972 - US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}]}, {"year": "1975", "text": "The Tasman Bridge in Tasmania, Australia, is struck by the bulk ore carrier Lake Illawarra, killing twelve people.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Tasman_Bridge\" title=\"Tasman Bridge\">Tasman Bridge</a> in <a href=\"https://wikipedia.org/wiki/Tasmania\" title=\"Tasmania\">Tasmania</a>, Australia, <a href=\"https://wikipedia.org/wiki/Tasman_Bridge_disaster\" title=\"Tasman Bridge disaster\">is struck</a> by the bulk ore carrier <i>Lake Illawarra</i>, killing twelve people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tasman_Bridge\" title=\"Tasman Bridge\">Tasman Bridge</a> in <a href=\"https://wikipedia.org/wiki/Tasmania\" title=\"Tasmania\">Tasmania</a>, Australia, <a href=\"https://wikipedia.org/wiki/Tasman_Bridge_disaster\" title=\"Tasman Bridge disaster\">is struck</a> by the bulk ore carrier <i>Lake Illawarra</i>, killing twelve people.", "links": [{"title": "Tasman Bridge", "link": "https://wikipedia.org/wiki/Tasman_Bridge"}, {"title": "Tasmania", "link": "https://wikipedia.org/wiki/Tasmania"}, {"title": "Tasman Bridge disaster", "link": "https://wikipedia.org/wiki/Tasman_Bridge_disaster"}]}, {"year": "1976", "text": "The Khmer Rouge announce that the new Constitution of Democratic Kampuchea is ratified.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> announce that the new Constitution of <a href=\"https://wikipedia.org/wiki/Democratic_Kampuchea\" title=\"Democratic Kampuchea\">Democratic Kampuchea</a> is ratified.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> announce that the new Constitution of <a href=\"https://wikipedia.org/wiki/Democratic_Kampuchea\" title=\"Democratic Kampuchea\">Democratic Kampuchea</a> is ratified.", "links": [{"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}, {"title": "Democratic Kampuchea", "link": "https://wikipedia.org/wiki/Democratic_Kampuchea"}]}, {"year": "1976", "text": "The Troubles: Gunmen shoot dead ten Protestant civilians after stopping their minibus at Kingsmill in County Armagh, Northern Ireland, UK, allegedly as retaliation for a string of attacks on Catholic civilians in the area by Loyalists, particularly the killing of six Catholics the night before.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: Gunmen <a href=\"https://wikipedia.org/wiki/Kingsmill_massacre\" title=\"Kingsmill massacre\">shoot dead ten Protestant civilians</a> after stopping their minibus at Kingsmill in <a href=\"https://wikipedia.org/wiki/County_Armagh\" title=\"County Armagh\">County Armagh</a>, Northern Ireland, UK, allegedly as retaliation for a string of attacks on <a href=\"https://wikipedia.org/wiki/Irish_Catholic\" class=\"mw-redirect\" title=\"Irish Catholic\">Catholic</a> civilians in the area by <a href=\"https://wikipedia.org/wiki/Ulster_loyalism\" title=\"Ulster loyalism\">Loyalists</a>, particularly the <a href=\"https://wikipedia.org/wiki/Reavey_and_O%27Dowd_killings\" title=\"Reavey and O'Dowd killings\">killing of six Catholics the night before</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: Gunmen <a href=\"https://wikipedia.org/wiki/Kingsmill_massacre\" title=\"Kingsmill massacre\">shoot dead ten Protestant civilians</a> after stopping their minibus at Kingsmill in <a href=\"https://wikipedia.org/wiki/County_Armagh\" title=\"County Armagh\">County Armagh</a>, Northern Ireland, UK, allegedly as retaliation for a string of attacks on <a href=\"https://wikipedia.org/wiki/Irish_Catholic\" class=\"mw-redirect\" title=\"Irish Catholic\">Catholic</a> civilians in the area by <a href=\"https://wikipedia.org/wiki/Ulster_loyalism\" title=\"Ulster loyalism\">Loyalists</a>, particularly the <a href=\"https://wikipedia.org/wiki/Reavey_and_O%27Dowd_killings\" title=\"Reavey and O'Dowd killings\">killing of six Catholics the night before</a>.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Kingsmill massacre", "link": "https://wikipedia.org/wiki/Kingsmill_massacre"}, {"title": "County Armagh", "link": "https://wikipedia.org/wiki/County_Armagh"}, {"title": "Irish Catholic", "link": "https://wikipedia.org/wiki/Irish_Catholic"}, {"title": "Ulster loyalism", "link": "https://wikipedia.org/wiki/Ulster_loyalism"}, {"title": "<PERSON><PERSON><PERSON> and <PERSON> killings", "link": "https://wikipedia.org/wiki/Re<PERSON><PERSON>_and_O%27Dowd_killings"}]}, {"year": "1991", "text": "Georgian forces enter Tskhinvali, the capital of South Ossetia, Georgia, opening the 1991-92 South Ossetia War.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgian</a> forces enter <a href=\"https://wikipedia.org/wiki/Tskhinvali\" title=\"Tskhinvali\">Tskhinvali</a>, the capital of <a href=\"https://wikipedia.org/wiki/South_Ossetia\" title=\"South Ossetia\">South Ossetia, Georgia</a>, opening the <a href=\"https://wikipedia.org/wiki/1991%E2%80%9392_South_Ossetia_War\" class=\"mw-redirect\" title=\"1991-92 South Ossetia War\">1991-92 South Ossetia War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgian</a> forces enter <a href=\"https://wikipedia.org/wiki/Tskhinvali\" title=\"Tskhinvali\">Tskhinvali</a>, the capital of <a href=\"https://wikipedia.org/wiki/South_Ossetia\" title=\"South Ossetia\">South Ossetia, Georgia</a>, opening the <a href=\"https://wikipedia.org/wiki/1991%E2%80%9392_South_Ossetia_War\" class=\"mw-redirect\" title=\"1991-92 South Ossetia War\">1991-92 South Ossetia War</a>.", "links": [{"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "Tskhinvali", "link": "https://wikipedia.org/wiki/Tskhinvali"}, {"title": "South Ossetia", "link": "https://wikipedia.org/wiki/South_Ossetia"}, {"title": "1991-92 South Ossetia War", "link": "https://wikipedia.org/wiki/1991%E2%80%9392_South_Ossetia_War"}]}, {"year": "1991", "text": "Somali Civil War: The United States Embassy to Somalia in Mogadishu is evacuated by helicopter airlift days after the outbreak of violence in Mogadishu.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Somali_Civil_War\" title=\"Somali Civil War\">Somali Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Embassy_of_the_United_States,_Mogadishu\" title=\"Embassy of the United States, Mogadishu\">United States Embassy to Somalia</a> in <a href=\"https://wikipedia.org/wiki/Mogadishu\" title=\"Mogadishu\">Mogadishu</a> is <a href=\"https://wikipedia.org/wiki/Operation_Eastern_Exit\" title=\"Operation Eastern Exit\">evacuated by helicopter airlift</a> days after the outbreak of violence in Mogadishu.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Somali_Civil_War\" title=\"Somali Civil War\">Somali Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Embassy_of_the_United_States,_Mogadishu\" title=\"Embassy of the United States, Mogadishu\">United States Embassy to Somalia</a> in <a href=\"https://wikipedia.org/wiki/Mogadishu\" title=\"Mogadishu\">Mogadishu</a> is <a href=\"https://wikipedia.org/wiki/Operation_Eastern_Exit\" title=\"Operation Eastern Exit\">evacuated by helicopter airlift</a> days after the outbreak of violence in Mogadishu.", "links": [{"title": "Somali Civil War", "link": "https://wikipedia.org/wiki/Somali_Civil_War"}, {"title": "Embassy of the United States, Mogadishu", "link": "https://wikipedia.org/wiki/Embassy_of_the_United_States,_Mogadishu"}, {"title": "Mogadishu", "link": "https://wikipedia.org/wiki/Mogadishu"}, {"title": "Operation Eastern Exit", "link": "https://wikipedia.org/wiki/Operation_Eastern_Exit"}]}, {"year": "1993", "text": "The oil tanker MV Braer runs aground on the coast of the Shetland Islands, spilling 84,700 tons of crude oil.", "html": "1993 - The oil tanker <a href=\"https://wikipedia.org/wiki/MV_Braer\" title=\"MV Braer\">MV <i>Braer</i></a> runs aground on the coast of the <a href=\"https://wikipedia.org/wiki/Shetland\" title=\"Shetland\">Shetland</a> Islands, spilling 84,700 tons of crude oil.", "no_year_html": "The oil tanker <a href=\"https://wikipedia.org/wiki/MV_Braer\" title=\"MV Braer\">MV <i>Braer</i></a> runs aground on the coast of the <a href=\"https://wikipedia.org/wiki/Shetland\" title=\"Shetland\">Shetland</a> Islands, spilling 84,700 tons of crude oil.", "links": [{"title": "MV Braer", "link": "https://wikipedia.org/wiki/MV_Braer"}, {"title": "Shetland", "link": "https://wikipedia.org/wiki/Shetland"}]}, {"year": "2005", "text": "The dwarf planet <PERSON><PERSON> is discovered by Palomar Observatory-based astronomers, later motivating the International Astronomical Union (IAU) to define the term planet for the first time.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dwarf_planet)\" title=\"Eris (dwarf planet)\">dwarf planet <PERSON><PERSON></a> is discovered by <a href=\"https://wikipedia.org/wiki/Palomar_Observatory\" title=\"Palomar Observatory\">Palomar Observatory</a>-based astronomers, later motivating the <a href=\"https://wikipedia.org/wiki/International_Astronomical_Union\" title=\"International Astronomical Union\">International Astronomical Union</a> (IAU) to <a href=\"https://wikipedia.org/wiki/Definition_of_planet\" title=\"Definition of planet\">define the term <i>planet</i></a> for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dwarf_planet)\" title=\"Eris (dwarf planet)\">dwarf planet <PERSON><PERSON></a> is discovered by <a href=\"https://wikipedia.org/wiki/Palomar_Observatory\" title=\"Palomar Observatory\">Palomar Observatory</a>-based astronomers, later motivating the <a href=\"https://wikipedia.org/wiki/International_Astronomical_Union\" title=\"International Astronomical Union\">International Astronomical Union</a> (IAU) to <a href=\"https://wikipedia.org/wiki/Definition_of_planet\" title=\"Definition of planet\">define the term <i>planet</i></a> for the first time.", "links": [{"title": "<PERSON><PERSON> (dwarf planet)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(dwarf_planet)"}, {"title": "Palomar Observatory", "link": "https://wikipedia.org/wiki/Palomar_Observatory"}, {"title": "International Astronomical Union", "link": "https://wikipedia.org/wiki/International_Astronomical_Union"}, {"title": "Definition of planet", "link": "https://wikipedia.org/wiki/Definition_of_planet"}]}, {"year": "2014", "text": "A launch of the communication satellite GSAT-14 aboard the GSLV MK.II D5 marks the first successful flight of an Indian cryogenic engine.", "html": "2014 - A launch of the communication satellite <a href=\"https://wikipedia.org/wiki/GSAT-14\" title=\"GSAT-14\">GSAT-14</a> aboard the <a href=\"https://wikipedia.org/wiki/Geosynchronous_Satellite_Launch_Vehicle\" title=\"Geosynchronous Satellite Launch Vehicle\">GSLV MK.II D5</a> marks the first successful flight of an <a href=\"https://wikipedia.org/wiki/CE-7.5\" title=\"CE-7.5\">Indian cryogenic engine</a>.", "no_year_html": "A launch of the communication satellite <a href=\"https://wikipedia.org/wiki/GSAT-14\" title=\"GSAT-14\">GSAT-14</a> aboard the <a href=\"https://wikipedia.org/wiki/Geosynchronous_Satellite_Launch_Vehicle\" title=\"Geosynchronous Satellite Launch Vehicle\">GSLV MK.II D5</a> marks the first successful flight of an <a href=\"https://wikipedia.org/wiki/CE-7.5\" title=\"CE-7.5\">Indian cryogenic engine</a>.", "links": [{"title": "GSAT-14", "link": "https://wikipedia.org/wiki/GSAT-14"}, {"title": "Geosynchronous Satellite Launch Vehicle", "link": "https://wikipedia.org/wiki/Geosynchronous_Satellite_Launch_Vehicle"}, {"title": "CE-7.5", "link": "https://wikipedia.org/wiki/CE-7.5"}]}, {"year": "2022", "text": "Kazakh President <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dismisses Prime Minister <PERSON><PERSON><PERSON> and declares state of emergency over the 2022 Kazakh unrest.", "html": "2022 - Kazakh President <a href=\"https://wikipedia.org/wiki/Kassy<PERSON>-<PERSON><PERSON>\" title=\"Kassym-<PERSON><PERSON>\">Kassy<PERSON>-<PERSON><PERSON></a> dismisses Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a> and declares <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> over the <a href=\"https://wikipedia.org/wiki/2022_Kazakh_unrest\" title=\"2022 Kazakh unrest\">2022 Kazakh unrest</a>.", "no_year_html": "Kazakh President <a href=\"https://wikipedia.org/wiki/Kassy<PERSON>-<PERSON><PERSON>\" title=\"Kassym-<PERSON><PERSON>\">Kassym-<PERSON><PERSON></a> dismisses Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a> and declares <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> over the <a href=\"https://wikipedia.org/wiki/2022_Kazakh_unrest\" title=\"2022 Kazakh unrest\">2022 Kazakh unrest</a>.", "links": [{"title": "Kassym-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "State of emergency", "link": "https://wikipedia.org/wiki/State_of_emergency"}, {"title": "2022 Kazakh unrest", "link": "https://wikipedia.org/wiki/2022_Kazakh_unrest"}]}, {"year": "2024", "text": "Alaska Airlines Flight 1282 makes an emergency landing at Portland International Airport after a door plug blows off the Boeing 737 MAX 9 operating the flight. There are no fatalities, but the accident prompts the 737 MAX to be grounded and renews scrutiny on Boeing's manufacturing and design issues.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Alaska_Airlines_Flight_1282\" title=\"Alaska Airlines Flight 1282\">Alaska Airlines Flight 1282</a> makes an emergency landing at <a href=\"https://wikipedia.org/wiki/Portland_International_Airport\" title=\"Portland International Airport\">Portland International Airport</a> after a door plug blows off the Boeing 737 MAX 9 operating the flight. There are no fatalities, but the accident prompts the <a href=\"https://wikipedia.org/wiki/Boeing_737_MAX\" title=\"Boeing 737 MAX\">737 MAX to be grounded</a> and renews scrutiny on <a href=\"https://wikipedia.org/wiki/Boeing_manufacturing_and_design_issues\" title=\"Boeing manufacturing and design issues\">Boeing's manufacturing and design issues</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alaska_Airlines_Flight_1282\" title=\"Alaska Airlines Flight 1282\">Alaska Airlines Flight 1282</a> makes an emergency landing at <a href=\"https://wikipedia.org/wiki/Portland_International_Airport\" title=\"Portland International Airport\">Portland International Airport</a> after a door plug blows off the Boeing 737 MAX 9 operating the flight. There are no fatalities, but the accident prompts the <a href=\"https://wikipedia.org/wiki/Boeing_737_MAX\" title=\"Boeing 737 MAX\">737 MAX to be grounded</a> and renews scrutiny on <a href=\"https://wikipedia.org/wiki/Boeing_manufacturing_and_design_issues\" title=\"Boeing manufacturing and design issues\">Boeing's manufacturing and design issues</a>.", "links": [{"title": "Alaska Airlines Flight 1282", "link": "https://wikipedia.org/wiki/Alaska_Airlines_Flight_1282"}, {"title": "Portland International Airport", "link": "https://wikipedia.org/wiki/Portland_International_Airport"}, {"title": "Boeing 737 MAX", "link": "https://wikipedia.org/wiki/Boeing_737_MAX"}, {"title": "Boeing manufacturing and design issues", "link": "https://wikipedia.org/wiki/Boeing_manufacturing_and_design_issues"}]}], "Births": [{"year": "1209", "text": "<PERSON>, 1st Earl of Cornwall, English prince, nominal King of Germany (d. 1272)", "html": "1209 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Cornwall\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Cornwall\"><PERSON>, 1st Earl of Cornwall</a>, English prince, nominal King of Germany (d. 1272)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Cornwall\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Cornwall\"><PERSON>, 1st Earl of Cornwall</a>, English prince, nominal King of Germany (d. 1272)", "links": [{"title": "<PERSON>, 1st Earl of Cornwall", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Cornwall"}]}, {"year": "1530", "text": "<PERSON><PERSON>, monk of the Order of the Minims (d. 1571)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, monk of the Order of the Minims (d. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, monk of the Order of the Minims (d. 1571)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1548", "text": "<PERSON>, Spanish priest, philosopher, and theologian (d. 1617)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/Francisco_Su%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Spanish priest, philosopher, and theologian (d. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Su%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Spanish priest, philosopher, and theologian (d. 1617)", "links": [{"title": "Francisco <PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Su%C3%A1rez"}]}, {"year": "1587", "text": "<PERSON>, Chinese geographer and explorer (d. 1641)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese geographer and explorer (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Xi<PERSON>\"><PERSON></a>, Chinese geographer and explorer (d. 1641)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1592", "text": "<PERSON>, Mughal emperor (d. 1666)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mughal emperor (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mughal emperor (d. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian military commander (d. 1664)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Zr%C3%ADnyi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian military commander (d. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Zr%C3%ADnyi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian military commander (d. 1664)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_Zr%C3%ADnyi"}]}, {"year": "1640", "text": "<PERSON>, Italian composer (d. 1713)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON>, French-English general and explorer (d. 1800)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English general and explorer (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English general and explorer (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON><PERSON><PERSON>, French economist and academic (d. 1832)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and academic (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and academic (d. 1832)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, American commander (d. 1820)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander (d. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON>, American general and explorer (d. 1813)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/Zebulon_Pike\" title=\"Zebulon Pike\"><PERSON>eb<PERSON> Pike</a>, American general and explorer (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zebulon_Pike\" title=\"Zebulon Pike\"><PERSON>eb<PERSON> Pike</a>, American general and explorer (d. 1813)", "links": [{"title": "Zebulon Pike", "link": "https://wikipedia.org/wiki/Zebulon_Pike"}]}, {"year": "1781", "text": "<PERSON><PERSON>, three terms mayor of San Antonio, in Spanish Texas (d. 1836)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_de_Abrego\" title=\"<PERSON><PERSON> de Abrego\"><PERSON><PERSON> Abrego</a>, three terms mayor of San Antonio, in Spanish Texas (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_de_Abrego\" title=\"<PERSON><PERSON> de Abrego\"><PERSON><PERSON> Abrego</a>, three terms mayor of San Antonio, in Spanish Texas (d. 1836)", "links": [{"title": "Gaspar <PERSON> de Abrego", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_de_Abrego"}]}, {"year": "1793", "text": "<PERSON>, American lawyer and politician (d. 1855)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, Austrian priest and activist (d. 1881)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Anton_F%C3%BCster\" title=\"<PERSON>\"><PERSON></a>, Austrian priest and activist (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anton_F%C3%BCster\" title=\"<PERSON>\"><PERSON></a>, Austrian priest and activist (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anton_F%C3%BCster"}]}, {"year": "1834", "text": "<PERSON>, English surgeon and explorer (d. 1861)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surgeon and explorer (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surgeon and explorer (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, French mathematician and academic (d. 1922)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, German philosopher and author, Nobel Prize laureate (d. 1926)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1846", "text": "<PERSON><PERSON>, Syrian Roman Catholic nun; later canonized (d. 1878)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Maria<PERSON> Ba<PERSON>ardy\"><PERSON><PERSON></a>, Syrian Roman Catholic nun; later canonized (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ba<PERSON>ardy\"><PERSON><PERSON></a>, Syrian Roman Catholic nun; later canonized (d. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mariam_<PERSON>y"}]}, {"year": "1855", "text": "<PERSON>, American businessman, founded the Gillette Company (d. 1932)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/King_Camp_Gillette\" class=\"mw-redirect\" title=\"King Camp Gillette\">King Camp Gillette</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(brand)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (brand)\">Gillette Company</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_Camp_Gillette\" class=\"mw-redirect\" title=\"King Camp Gillette\">King Camp Gillette</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(brand)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (brand)\">Gillette Company</a> (d. 1932)", "links": [{"title": "King Camp Gillette", "link": "https://wikipedia.org/wiki/King_Camp_Gillette"}, {"title": "Gillette (brand)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(brand)"}]}, {"year": "1864", "text": "<PERSON>, American baseball player and manager (d. 1911)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, Greek lawyer and politician, 94th Prime Minister of Greece (d. 1922)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 94th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 94th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1871", "text": "<PERSON>, American composer and academic (d. 1940)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>verse"}]}, {"year": "1874", "text": "<PERSON>, American physiologist and academic, Nobel Prize laureate (d. 1965)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1876", "text": "<PERSON>, German lawyer and politician, Chancellor of West Germany (d. 1967)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of West Germany</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of West Germany</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Chancellors of Germany", "link": "https://wikipedia.org/wiki/List_of_Chancellors_of_Germany"}]}, {"year": "1879", "text": "<PERSON>, Austrian physician and academic (d. 1946)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and academic (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Russian pianist and composer (d. 1951)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Spanish sculptor and painter (d. 1934)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor and painter (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor and painter (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American journalist (d. 1958)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>pe"}]}, {"year": "1882", "text": "<PERSON>, 18th president of Liberia (d. 1955)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 18th president of Liberia (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 18th president of Liberia (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Italian-English poet and civil servant (d. 1940)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-English poet and civil servant (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-English poet and civil servant (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Israeli physicist and engineer (d. 1976)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli physicist and engineer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli physicist and engineer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American nurse (d. 1984)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian-American guru and philosopher (d. 1952)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Paramah<PERSON><PERSON>_Yogananda\" title=\"Paramah<PERSON><PERSON> Yogananda\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-American guru and philosopher (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Para<PERSON><PERSON><PERSON>_Yogananda\" title=\"Paramah<PERSON><PERSON> Yogananda\"><PERSON><PERSON><PERSON><PERSON>nan<PERSON></a>, Indian-American guru and philosopher (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mah<PERSON><PERSON>_Yogananda"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Japanese philosopher and author (d. 1945)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese philosopher and author (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese philosopher and author (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, French-American painter (d. 1955)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American painter (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American painter (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, French journalist (d. 1989)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French journalist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French journalist (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-M%C3%A9ry"}]}, {"year": "1902", "text": "<PERSON>, English journalist and author (d. 1989)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Australian pilot and navigator (d. 1957)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pilot and navigator (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pilot and navigator (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American astrologer and psychic (d. 1997)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astrologer and psychic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astrologer and psychic (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Austrian violinist (d. 1995)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian violinist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian violinist (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English archaeologist and academic (d. 1978)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Finnish athlete (d. 1969)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish athlete (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish athlete (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mar<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Italian-American actor (d. 1963)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Swiss-American sculptor, painter, and photographer (d. 1995)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-American sculptor, painter, and photographer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-American sculptor, painter, and photographer (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American mathematician and computer scientist (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and computer scientist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and computer scientist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, New Zealand runner and journalist (d. 1949)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and journalist (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and journalist (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, French actor and screenwriter (d. 2001)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and screenwriter (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian rugby league player (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor and director (d. 1959)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Canadian geographer and cartographer (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian geographer and cartographer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian geographer and cartographer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American businessman and diplomat (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and diplomat (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and diplomat (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, German director and producer (d. 1966)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German director and producer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German director and producer (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yman"}]}, {"year": "1919", "text": "<PERSON>, Sri Lankan theorist and politician (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan theorist and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan theorist and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Italian flute player (d. 1992)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Severino_Gazzelloni\" title=\"Severino Gazzelloni\"><PERSON><PERSON><PERSON></a>, Italian flute player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Severino_Gazzelloni\" title=\"Severino Gazzelloni\"><PERSON><PERSON><PERSON></a>, Italian flute player (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Severino_Gazzelloni"}]}, {"year": "1920", "text": "<PERSON>, Italian pianist and educator (d. 1995)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and educator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and educator (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Swiss author and playwright (d. 1990)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCrrenmatt\" title=\"<PERSON>\"><PERSON></a>, Swiss author and playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Friedrich_<PERSON>%C3%BCrrenmatt\" title=\"<PERSON>\"><PERSON></a>, Swiss author and playwright (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Friedrich_D%C3%BCrrenmatt"}]}, {"year": "1921", "text": "<PERSON>, Grand Duke of Luxembourg, Luxembourgish soldier and aristocrat (d. 2019)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Luxembourg\" title=\"<PERSON>, Grand Duke of Luxembourg\"><PERSON>, Grand Duke of Luxembourg</a>, Luxembourgish soldier and aristocrat (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Luxembourg\" title=\"<PERSON>, Grand Duke of Luxembourg\"><PERSON>, Grand Duke of Luxembourg</a>, Luxembourgish soldier and aristocrat (d. 2019)", "links": [{"title": "<PERSON>, Grand Duke of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Luxembourg"}]}, {"year": "1921", "text": "<PERSON>, American politician and diplomat, 67th Governor of Maine (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 67th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 67th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Maine", "link": "https://wikipedia.org/wiki/Governor_of_Maine"}]}, {"year": "1922", "text": "<PERSON>, Australian admiral (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian admiral (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian admiral (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American football executive (d. 2025)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Virginia_Hal<PERSON>_<PERSON>\" title=\"Virginia Halas McCaskey\">Virginia <PERSON></a>, American football executive (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Halas_<PERSON>\" title=\"Virginia Halas McCaskey\">Virginia <PERSON></a>, American football executive (d. 2025)", "links": [{"title": "Virginia Halas <PERSON>", "link": "https://wikipedia.org/wiki/Virginia_Halas_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American radio host and producer, founded Sun Records (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host and producer, founded <a href=\"https://wikipedia.org/wiki/Sun_Records\" title=\"Sun Records\">Sun Records</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host and producer, founded <a href=\"https://wikipedia.org/wiki/Sun_Records\" title=\"Sun Records\">Sun Records</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sun Records", "link": "https://wikipedia.org/wiki/Sun_Records"}]}, {"year": "1925", "text": "<PERSON>, American basketball player and coach (d. 2024)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lou_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish runner (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish runner (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish runner (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON> <PERSON><PERSON>, American poet (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/W._D._Snodgrass\" title=\"W. D. Snodgrass\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._D._Snodgrass\" title=\"W. D. Snodgrass\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet (d. 2009)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_Snodgrass"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American businessman and activist (d. 2000)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Williams\"><PERSON><PERSON></a>, American businessman and activist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Williams\"><PERSON><PERSON></a>, American businessman and activist (d. 2000)", "links": [{"title": "Hosea Williams", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, American guru and author, founded Iraivan Temple (d. 2001)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>va<PERSON>_Subramuniyaswami\" title=\"<PERSON>vaya Subramuniyaswami\"><PERSON><PERSON><PERSON>ramuni<PERSON></a>, American guru and author, founded <a href=\"https://wikipedia.org/wiki/Iraivan_Temple\" title=\"Iraivan Temple\">Iraivan Temple</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Subramuniyaswami\" title=\"<PERSON>va<PERSON> Subramuniyaswami\"><PERSON><PERSON><PERSON> Subramuni<PERSON></a>, American guru and author, founded <a href=\"https://wikipedia.org/wiki/Iraivan_Temple\" title=\"Iraivan Temple\">Iraivan Temple</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sivaya_Subramuniyaswami"}, {"title": "Iraivan Temple", "link": "https://wikipedia.org/wiki/Iraivan_Temple"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani cricketer (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (cricketer)\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (cricketer)\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani lawyer and politician, 4th President of Pakistan (d. 1979)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1928", "text": "<PERSON>, English actress (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American soldier, lawyer, and politician, 42nd Vice President of the United States (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 42nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 42nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Finnish footballer and manager (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Au<PERSON>_Rytk%C3%B6nen\" title=\"<PERSON><PERSON> Rytkönen\"><PERSON><PERSON></a>, Finnish footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Au<PERSON>_Rytk%C3%B6nen\" title=\"<PERSON><PERSON> Rytkö<PERSON>\"><PERSON><PERSON></a>, Finnish footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aulis_Rytk%C3%B6nen"}]}, {"year": "1930", "text": "<PERSON>, Australian rugby league player (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American dancer and choreographer, founded the Alvin Ailey American Dance Theater (d. 1989)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_American_Dance_Theater\" title=\"Alvin <PERSON> American Dance Theater\"><PERSON> American Dance Theater</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_American_Dance_Theater\" title=\"Alvin <PERSON> American Dance Theater\"><PERSON> American Dance Theater</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Alvin <PERSON>ley American Dance Theater", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_American_Dance_Theater"}]}, {"year": "1931", "text": "<PERSON>, Austrian pianist, poet, and author", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist, poet, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist, poet, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_<PERSON>del"}]}, {"year": "1931", "text": "<PERSON>, American athlete (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American athlete (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American athlete (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor and director", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Italian novelist, literary critic, and philosopher (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Umberto_Eco\" title=\"Umberto Eco\"><PERSON><PERSON></a>, Italian novelist, literary critic, and philosopher (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umberto_Eco\" title=\"<PERSON><PERSON> Eco\"><PERSON><PERSON></a>, Italian novelist, literary critic, and philosopher (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umberto_Eco"}]}, {"year": "1932", "text": "<PERSON>, American football player and coach (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ll\"><PERSON></a>, American football player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Indian politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1934", "text": "<PERSON>, South African-American songwriter and producer, co-founded A & R Recording (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American songwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/A_%26_R_Recording\" title=\"A &amp; R Recording\">A &amp; R Recording</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American songwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/A_%26_R_Recording\" title=\"A &amp; R Recording\">A &amp; R Recording</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "A & R Recording", "link": "https://wikipedia.org/wiki/A_%26_R_Recording"}]}, {"year": "1936", "text": "<PERSON>, American journalist and memoirist (d. 2016)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Florence_King\" title=\"Florence King\"><PERSON></a>, American journalist and memoirist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_King\" title=\"Florence King\"><PERSON></a>, American journalist and memoirist (d. 2016)", "links": [{"title": "Florence King", "link": "https://wikipedia.org/wiki/Florence_King"}]}, {"year": "1936", "text": "<PERSON>, New Zealand rugby player (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>en"}]}, {"year": "1938", "text": "<PERSON> of Spain", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain\" class=\"mw-redirect\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain\" class=\"mw-redirect\" title=\"<PERSON> of <PERSON>\"><PERSON> of Spain</a>", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, Kenyan author and playwright", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Ng%C5%A9g%C4%A9_wa_Thiong%27o\" title=\"<PERSON><PERSON><PERSON><PERSON> wa Thiong'o\"><PERSON><PERSON><PERSON><PERSON> wa Thi<PERSON>'o</a>, Kenyan author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ng%C5%A9g%C4%A9_wa_Thiong%27o\" title=\"<PERSON><PERSON><PERSON><PERSON> wa Thiong'o\"><PERSON><PERSON><PERSON><PERSON> wa Thiong'o</a>, Kenyan author and playwright", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> wa <PERSON>o", "link": "https://wikipedia.org/wiki/Ng%C5%A9g%C4%A9_wa_<PERSON>hiong%27o"}]}, {"year": "1939", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Sri Lankan politician (d. 1997)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan politician (d. 1997)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Australian singer-songwriter and bassist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Guy\" title=\"Athol Guy\"><PERSON><PERSON></a>, Australian singer-songwriter and bassist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Guy\" title=\"Athol Guy\"><PERSON><PERSON></a>, Australian singer-songwriter and bassist", "links": [{"title": "<PERSON><PERSON> Guy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Guy"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Surinamese-Dutch film director (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_la_Parra\" title=\"<PERSON><PERSON> de la Parra\"><PERSON><PERSON> <PERSON> Parra</a>, Surinamese-Dutch film director (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_la_Parra\" title=\"<PERSON><PERSON> de la Parra\"><PERSON><PERSON> <PERSON> Parra</a>, Surinamese-Dutch film director (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_la_<PERSON>rra"}]}, {"year": "1941", "text": "<PERSON>, New Zealand cricketer (d. 2008)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American tennis player (d. 1986)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Japanese animator, director, and screenwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese animator, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese animator, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Indian cricketer and coach (d. 2011)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer and coach (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Italian pianist and conductor (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian pianist and conductor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian pianist and conductor (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American journalist and talk show host", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Kuwaiti royal and politician, 7th Prime Minister of Kuwait (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Jaber_Al-Mubarak_Al-Hamad_Al-Sabah\" title=\"Jaber Al-Mubarak Al-Hamad Al-Sabah\"><PERSON>aber Al-Mubarak Al-Hamad Al-Sabah</a>, Kuwaiti royal and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kuwait\" title=\"Prime Minister of Kuwait\">Prime Minister of Kuwait</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaber_Al-Mubarak_Al-Hamad_Al-Sabah\" title=\"Jaber Al-Mubarak Al-Hamad Al-Sabah\"><PERSON><PERSON>r Al-Mubarak Al-Hamad Al-Sabah</a>, Kuwaiti royal and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kuwait\" title=\"Prime Minister of Kuwait\">Prime Minister of Kuwait</a> (d. 2024)", "links": [{"title": "Jaber Al-Mubarak Al-Hamad Al-Sabah", "link": "https://wikipedia.org/wiki/Jaber_Al-Mubarak_Al-Hamad_Al-Sabah"}, {"title": "Prime Minister of Kuwait", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Kuwait"}]}, {"year": "1943", "text": "<PERSON>, Australian lawyer and judge", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Georgian footballer and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ila<PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ilava\"><PERSON><PERSON><PERSON></a>, Georgian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American nurse and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American politician, 45th Governor of Pennsylvania", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 45th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania\" class=\"mw-redirect\" title=\"List of Governors of Pennsylvania\">Governor of Pennsylvania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 45th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania\" class=\"mw-redirect\" title=\"List of Governors of Pennsylvania\">Governor of Pennsylvania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Pennsylvania", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania"}]}, {"year": "1946", "text": "<PERSON>, American actress, director, and businesswoman", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and politician, 70th Governor of Ohio", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 70th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 70th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Ohio", "link": "https://wikipedia.org/wiki/Governor_of_Ohio"}]}, {"year": "1947", "text": "<PERSON>, American football player (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Mercury_Morris\" title=\"<PERSON> Morris\"><PERSON></a>, American football player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mercury_Morris\" title=\"Mercury Morris\"><PERSON></a>, American football player (d. 2024)", "links": [{"title": "Mercury Morris", "link": "https://wikipedia.org/wiki/Mercury_Morris"}]}, {"year": "1948", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Romanian historian, philosopher, and author (d. 1991)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian historian, philosopher, and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian historian, philosopher, and author (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Baron <PERSON>, English lawyer and politician, Attorney General for England and Wales", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1950", "text": "<PERSON>, Canadian lawyer and politician, 8th Deputy Prime Minister of Canada", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada"}]}, {"year": "1950", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/U<PERSON>_<PERSON>%C3%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uli_Hoene%C3%9F"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English-Australian journalist and politician, 44th Premier of South Australia", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian journalist and politician, 44th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian journalist and politician, 44th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1953", "text": "<PERSON>, American civil servant and academic, 18th Director of Central Intelligence", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil servant and academic, 18th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil servant and academic, 18th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>et"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "1954", "text": "<PERSON>, American basketball player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Alex_English\" title=\"Alex English\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alex_English\" title=\"Alex English\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_English"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian author and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Krasznahorkai\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Krasznahorkai\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Krasznahorkai"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian lawyer and politician, Chief Minister of West Bengal", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, German academic and politician, 12th President of Germany", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German academic and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German academic and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "President of Germany", "link": "https://wikipedia.org/wiki/President_of_Germany"}]}, {"year": "1957", "text": "<PERSON>, Australian rugby league player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian rugby league player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Hrdina\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Hrdina\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Hrdina"}]}, {"year": "1958", "text": "<PERSON>, American baseball player and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian curler", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Swedish footballer and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6mberg\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6mberg\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glenn_Str%C3%B6mberg"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_DeMent\" title=\"<PERSON> De<PERSON>ent\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_DeMent\" title=\"<PERSON> DeMent\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Iris_DeMent"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress and model", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>is"}]}, {"year": "1962", "text": "<PERSON>, American baseball player and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, British footballer and actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British footballer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British footballer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby league player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Swedish high jumper", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Patrik_Sj%C3%B6berg\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pat<PERSON>_Sj%C3%B6berg\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patrik_Sj%C3%B6berg"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress, dancer, and choreographer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Canadian ice hockey player and engineer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A9_Juneau\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A9_Juneau\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and engineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A9_Juneau"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter, actor, and director", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Marilyn Manson\"><PERSON></a>, American singer-songwriter, actor, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Manson\" title=\"Marilyn Manson\"><PERSON></a>, American singer-songwriter, actor, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Scottish actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American golfer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian rugby league player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Norwegian multi-instrumentalist and composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian multi-instrumentalist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian multi-instrumentalist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Greek singer-songwriter, producer, and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter, producer, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Indian actor and filmmaker", "html": "1973 - <a href=\"https://wikipedia.org/wiki/U<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor and filmmaker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_<PERSON>pra"}]}, {"year": "1974", "text": "<PERSON>, American actress, comedian, and writer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Welsh sprinter and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh sprinter and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh sprinter and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dunn\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dunn\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American ice hockey player and scout", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Spanish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Diego_Trist%C3%A1n\" title=\"Diego Tristán\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Trist%C3%A1n\" title=\"Diego Tristán\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Trist%C3%A1n"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby league player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/January_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/January_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/January_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian pole vaulter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giuseppe_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian musician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Deadmau5\" title=\"Deadmau5\">Dead<PERSON>u<PERSON></a>, Canadian musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deadmau5\" title=\"Deadmau5\"><PERSON><PERSON>u<PERSON></a>, Canadian musician", "links": [{"title": "Deadmau5", "link": "https://wikipedia.org/wiki/Deadmau5"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Brooklyn_Sudano\" title=\"Brooklyn Sudano\">Brooklyn Sudano</a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brooklyn_Sudano\" title=\"Brooklyn Sudano\">Brooklyn Sudano</a>, American actress", "links": [{"title": "Brooklyn Sudano", "link": "https://wikipedia.org/wiki/Brooklyn_Sudano"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>oki\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Croatian skier", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "1984", "text": "<PERSON>, Bahamian sprinter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Bronx_Goodwin\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bronx_Goodwin\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "Bronx Goodwin", "link": "https://wikipedia.org/wiki/Bronx_Goodwin"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Filinga_Filiga\" title=\"Filinga Filiga\">Filinga Filiga</a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Filinga_Filiga\" title=\"Filinga Filiga\">Filinga Filiga</a>, New Zealand rugby league player", "links": [{"title": "Filinga Filiga", "link": "https://wikipedia.org/wiki/Filinga_Filiga"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1985", "text": "<PERSON>, Uruguayan footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Vera\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Vera\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Indian actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1987", "text": "<PERSON>, American race car driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American television personality", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Czech ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_Sal%C3%A1k"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Malaysian track cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>wang\"><PERSON><PERSON><PERSON><PERSON></a>, Malaysian track cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Malaysian track cyclist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Man<PERSON><PERSON> Gill\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON><PERSON>_<PERSON>\" title=\"Man<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "Mandip Gill", "link": "https://wikipedia.org/wiki/Man<PERSON><PERSON>_Gill"}]}, {"year": "1988", "text": "<PERSON>", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_Kalini%C4%87_(footballer)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Serbian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Radulji<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Venezuelan-American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Kriszti%C3%A1n_N%C3%A9meth\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kriszti%C3%A1n_N%C3%A9meth\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kriszti%C3%A1n_N%C3%A9meth"}]}, {"year": "1990", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C. J. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C. J. Cron\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Dutch footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Cuban-American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, Cuban-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_I<PERSON><PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, Cuban-American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Iglesias_(baseball)"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1991", "text": "<PERSON>, Romanian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_football)"}]}, {"year": "1992", "text": "<PERSON>, American actor, singer, and dancer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, English actress, singer-songwriter, and model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Su<PERSON>_Waterhouse\" title=\"Suki Waterhouse\"><PERSON><PERSON> Waterhouse</a>, English actress, singer-songwriter, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Su<PERSON>_Waterhouse\" title=\"Suki Waterhouse\"><PERSON><PERSON> Waterhouse</a>, English actress, singer-songwriter, and model", "links": [{"title": "Suki Waterhouse", "link": "https://wikipedia.org/wiki/Suki_Waterhouse"}]}, {"year": "1993", "text": "<PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian race car driver", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Latvian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Zem<PERSON>_Girgensons\" title=\"Zemgus Girgensons\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zemgus_Girgensons\" title=\"Zemgus Girgensons\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>em<PERSON>_<PERSON>ons"}]}, {"year": "1994", "text": "<PERSON>, American ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Brazilian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Toafofoa_<PERSON>\" title=\"Toafofoa Sipley\">Toaf<PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toafofoa_<PERSON>\" title=\"Toafofoa Sipley\">Toaf<PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "Toafof<PERSON>", "link": "https://wikipedia.org/wiki/Toafofoa_<PERSON>y"}]}, {"year": "1996", "text": "<PERSON>, New Zealand rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American basketball player and coach", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Vallejo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Vallejo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Vallejo"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Carles_Ale%C3%B1%C3%A1\" title=\"Carles Aleñá\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carles_Ale%C3%B1%C3%A1\" title=\"Carles Aleñá\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Carles_Ale%C3%B1%C3%A1"}]}, {"year": "1998", "text": "<PERSON>, Australian rugby league player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian ice hockey player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2009", "text": "<PERSON>, American actor", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "842", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph (b. 796)", "html": "842 - <a href=\"https://wikipedia.org/wiki/Al-Mu%27tasim\" title=\"<PERSON>-<PERSON>'tasim\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Mu%27tasim\" title=\"Al-Mu'tasim\"><PERSON><PERSON><PERSON><PERSON><PERSON>si<PERSON></a>, <PERSON><PERSON> caliph (b. 796)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Mu%27tasim"}]}, {"year": "941", "text": "<PERSON>, Chinese chancellor (b. 884)", "html": "941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (b. 884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (b. 884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1066", "text": "<PERSON> the Confessor, English king (b. 1004)", "html": "1066 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a>, English king (b. 1004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a>, English king (b. 1004)", "links": [{"title": "<PERSON> the Confessor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Confessor"}]}, {"year": "1173", "text": "<PERSON><PERSON><PERSON> the Curly, High Duke of Poland (b. 1120)", "html": "1173 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C5%82aw_IV_the_Curly\" title=\"<PERSON><PERSON><PERSON> IV the Curly\"><PERSON><PERSON><PERSON> IV the Curly</a>, <a href=\"https://wikipedia.org/wiki/High_Duke_of_Poland\" class=\"mw-redirect\" title=\"High Duke of Poland\">High Duke of Poland</a> (b. 1120)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C5%82aw_IV_the_Curly\" title=\"<PERSON><PERSON><PERSON> IV the Curly\"><PERSON><PERSON><PERSON> IV the Curly</a>, <a href=\"https://wikipedia.org/wiki/High_Duke_of_Poland\" class=\"mw-redirect\" title=\"High Duke of Poland\">High Duke of Poland</a> (b. 1120)", "links": [{"title": "<PERSON><PERSON><PERSON> the Curly", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_IV_the_Curly"}, {"title": "High Duke of Poland", "link": "https://wikipedia.org/wiki/High_Duke_of_Poland"}]}, {"year": "1382", "text": "<PERSON><PERSON>, Countess of Ulster (b. 1355)", "html": "1382 - <a href=\"https://wikipedia.org/wiki/Philippa_Plantagenet\" class=\"mw-redirect\" title=\"Philippa Plantagenet\"><PERSON><PERSON></a>, Countess of Ulster (b. 1355)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philipp<PERSON>_Plantagenet\" class=\"mw-redirect\" title=\"Philippa Plantagenet\"><PERSON><PERSON></a>, Countess of Ulster (b. 1355)", "links": [{"title": "Philippa Plantagenet", "link": "https://wikipedia.org/wiki/Philippa_Plantagenet"}]}, {"year": "1400", "text": "<PERSON>, 3rd Earl of Salisbury, English politician (b. 1350)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_<PERSON>_Salisbury\" class=\"mw-redirect\" title=\"<PERSON>, 3rd Earl <PERSON> Salisbury\"><PERSON>, 3rd Earl of Salisbury</a>, English politician (b. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_<PERSON>_Salisbury\" class=\"mw-redirect\" title=\"<PERSON>, 3rd Earl of Salisbury\"><PERSON>, 3rd Earl of Salisbury</a>, English politician (b. 1350)", "links": [{"title": "<PERSON>, 3rd Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Salisbury"}]}, {"year": "1430", "text": "<PERSON><PERSON> of England, Queen of Denmark, Norway and Sweden (b. 1394)", "html": "1430 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_England\" title=\"<PERSON><PERSON> of England\"><PERSON><PERSON> of England</a>, Queen of Denmark, Norway and Sweden (b. 1394)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_England\" title=\"<PERSON><PERSON> of England\"><PERSON><PERSON> of England</a>, Queen of Denmark, Norway and Sweden (b. 1394)", "links": [{"title": "<PERSON><PERSON> of England", "link": "https://wikipedia.org/wiki/Philippa_of_England"}]}, {"year": "1477", "text": "<PERSON>, Duke of Burgundy (b. 1433)", "html": "1477 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1433)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1524", "text": "<PERSON><PERSON>, Croatian poet (b. 1450)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian poet (b. 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian poet (b. 1450)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marko_Maruli%C4%87"}]}, {"year": "1527", "text": "<PERSON>, Swiss martyr (b. 1498)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss martyr (b. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss martyr (b. 1498)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1578", "text": "<PERSON><PERSON><PERSON>, Dalmatian painter (b. 1498)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dalmatian painter (b. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dalmatian painter (b. 1498)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON> of Hanau-Lichtenberg, German noblewoman (b. 1542)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanau-Lichtenberg\" title=\"<PERSON> of Hanau-Lichtenberg\"><PERSON> of Hanau-Lichtenberg</a>, German noblewoman (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanau-Lichtenberg\" title=\"<PERSON> of Hanau-Lichtenberg\"><PERSON> of Hanau-Lichtenberg</a>, German noblewoman (b. 1542)", "links": [{"title": "<PERSON> of Hanau-Lichtenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Hanau-<PERSON>"}]}, {"year": "1589", "text": "<PERSON>, queen of <PERSON> of France (b. 1519)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1519)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_Medici"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1713", "text": "<PERSON>, French explorer and author (b. 1643)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer and author (b. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer and author (b. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, Italian composer and educator (b. 1667)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "Empress <PERSON> of Russia (b. 1709)", "html": "1762 - Empress <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1709)", "no_year_html": "Empress <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1709)", "links": [{"title": "Elizabeth of Russia", "link": "https://wikipedia.org/wiki/Elizabeth_of_Russia"}]}, {"year": "1771", "text": "<PERSON>, 4th Duke of Bedford, English politician, Secretary of State for the Southern Department (b. 1710)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_<PERSON>_Bedford\" title=\"<PERSON>, 4th Duke of Bedford\"><PERSON>, 4th Duke of Bedford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_<PERSON>_Bedford\" title=\"<PERSON>, 4th Duke of Bedford\"><PERSON>, 4th Duke of Bedford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1710)", "links": [{"title": "<PERSON>, 4th Duke of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Duke_<PERSON>_Bedford"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1796", "text": "<PERSON>, American jurist and politician, 18th Governor of Connecticut (b. 1731)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(statesman)\" class=\"mw-redirect\" title=\"<PERSON> (statesman)\"><PERSON></a>, American jurist and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (b. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(statesman)\" class=\"mw-redirect\" title=\"<PERSON> (statesman)\"><PERSON></a>, American jurist and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (b. 1731)", "links": [{"title": "<PERSON> (statesman)", "link": "https://wikipedia.org/wiki/<PERSON>_(statesman)"}, {"title": "Governor of Connecticut", "link": "https://wikipedia.org/wiki/Governor_of_Connecticut"}]}, {"year": "1823", "text": "<PERSON>, Scottish-Australian colonel and politician, Lieutenant Governor of New South Wales (b. 1764)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Marines_officer)\" class=\"mw-redirect\" title=\"<PERSON> (British Marines officer)\"><PERSON></a>, Scottish-Australian colonel and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_New_South_Wales\" class=\"mw-redirect\" title=\"Lieutenant Governor of New South Wales\">Lieutenant Governor of New South Wales</a> (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Marines_officer)\" class=\"mw-redirect\" title=\"<PERSON> (British Marines officer)\"><PERSON></a>, Scottish-Australian colonel and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_New_South_Wales\" class=\"mw-redirect\" title=\"Lieutenant Governor of New South Wales\">Lieutenant Governor of New South Wales</a> (b. 1764)", "links": [{"title": "<PERSON> (British Marines officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Marines_officer)"}, {"title": "Lieutenant Governor of New South Wales", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_New_South_Wales"}]}, {"year": "1845", "text": "<PERSON>, English painter and illustrator (b. 1753)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter and illustrator (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter and illustrator (b. 1753)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1846", "text": "<PERSON>, American painter and illustrator (b. 1812)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Austrian field marshal (b. 1766)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Czech-American bishop and saint (b. 1811)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American bishop and saint (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American bishop and saint (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Australian poet and public servant (b. 1806)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and public servant (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and public servant (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Norwegian author and scholar (b. 1812)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bj%C3%B8<PERSON>en\" title=\"<PERSON>\"><PERSON></a>, Norwegian author and scholar (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bj%C3%B8<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author and scholar (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Asbj%C3%B8<PERSON><PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Austrian pianist and composer (b. 1803)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON><PERSON> Schmidt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish collector and art connoisseur who donated a large collection to the National Museum in Kraków (b. 1818)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>i%C4%85%C5%BCy%C5%84ski\" title=\"Konstanty Schmidt-Ciążyński\">Konst<PERSON><PERSON> Schmidt-Ciążyński</a>, Polish collector and art connoisseur who donated a large collection to the <a href=\"https://wikipedia.org/wiki/National_Museum_in_Krak%C3%B3w\" title=\"National Museum in Kraków\">National Museum in Kraków</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-Ci%C4%85%C5%BCy%C5%84ski\" title=\"Konstanty Schmidt-Ciążyński\">Konst<PERSON><PERSON> Schmidt-Ciążyński</a>, Polish collector and art connoisseur who donated a large collection to the <a href=\"https://wikipedia.org/wiki/National_Museum_in_Krak%C3%B3w\" title=\"National Museum in Kraków\">National Museum in Kraków</a> (b. 1818)", "links": [{"title": "Konstanty Schmidt-Ciążyński", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Schmidt-Ci%C4%85%C5%BCy%C5%84ski"}, {"title": "National Museum in Kraków", "link": "https://wikipedia.org/wiki/National_Museum_in_Krak%C3%B3w"}]}, {"year": "1899", "text": "<PERSON>, American professor, astronomer and mathematician (b. 1818)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professor, astronomer and mathematician (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professor, astronomer and mathematician (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, German paleontologist and geologist (b. 1839)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German paleontologist and geologist (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German paleontologist and geologist (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French-Swiss economist and academic (b. 1834)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss economist and academic (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss economist and academic (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Walras"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, English painter (b. 1865)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Isobe<PERSON>_Lilian_G<PERSON>ag\" title=\"Isobel Lilian Gloag\"><PERSON><PERSON><PERSON></a>, English painter (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>obe<PERSON>_Lilian_G<PERSON>ag\" title=\"Isobel Lilian Gloag\"><PERSON><PERSON><PERSON></a>, English painter (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ag"}]}, {"year": "1922", "text": "<PERSON>, Anglo-Irish sailor and explorer (b. 1874)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish sailor and explorer (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish sailor and explorer (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American lawyer and politician, 30th President of the United States (b. 1872)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1942", "text": "<PERSON>, Italian photographer, model, actress, and activist (b. 1896)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian photographer, model, actress, and activist (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian photographer, model, actress, and activist (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American botanist, educator, and inventor (b. 1864)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist, educator, and inventor (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist, educator, and inventor (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, South Korean-American journalist and activist (b. 1864)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-American journalist and activist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-American journalist and activist (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pil"}]}, {"year": "1951", "text": "<PERSON>, Russian journalist and author (b. 1899)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and author (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, 2nd Marquess of Linlithgow, Scottish colonel and politician, 46th Governor-General of India (b. 1887)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Marquess_of_Linlithgow\" title=\"<PERSON>, 2nd Marquess of Linlithgow\"><PERSON>, 2nd Marquess of Linlithgow</a>, Scottish colonel and politician, 46th <a href=\"https://wikipedia.org/wiki/List_of_governors-general_of_India\" title=\"List of governors-general of India\">Governor-General of India</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Linlithgow\" title=\"<PERSON>, 2nd Marquess of Linlithgow\"><PERSON>, 2nd Marquess of Linlithgow</a>, Scottish colonel and politician, 46th <a href=\"https://wikipedia.org/wiki/List_of_governors-general_of_India\" title=\"List of governors-general of India\">Governor-General of India</a> (b. 1887)", "links": [{"title": "<PERSON>, 2nd Marquess of Linlithgow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Marquess_of_Linlithgow"}, {"title": "List of governors-general of India", "link": "https://wikipedia.org/wiki/List_of_governors-general_of_India"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Bulgarian-Italian physician and activist (b. 1869)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian-Italian physician and activist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian-Italian physician and activist (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player and manager (b. 1891)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Rabbit_Maranville\" title=\"Rabbit Maranville\"><PERSON></a>, American baseball player and manager (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rabbit_Maranville\" title=\"Rabbit Maranville\"><PERSON></a>, American baseball player and manager (b. 1891)", "links": [{"title": "Rabbit <PERSON>", "link": "https://wikipedia.org/wiki/Rabbit_Maranville"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress and singer (b. 1875)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Misting<PERSON><PERSON>\" title=\"<PERSON>sting<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress and singer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sting<PERSON><PERSON>\" title=\"<PERSON>sting<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress and singer (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player, coach, and manager (b. 1896)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>sby\"><PERSON></a>, American baseball player, coach, and manager (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hornsby\"><PERSON></a>, American baseball player, coach, and manager (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hornsby"}]}, {"year": "1970", "text": "<PERSON>, German physicist and mathematician, Nobel Prize laureate (b. 1882)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Born\"><PERSON></a>, German physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Born\" title=\"Max Born\"><PERSON></a>, German physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1970", "text": "<PERSON>, Catalan composer and scholar (b. 1896)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan composer and scholar (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan composer and scholar (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian-American sound designer and engineer (b. 1899)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American sound designer and engineer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American sound designer and engineer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Russian pianist and educator (b. 1907)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lev_O<PERSON>in"}]}, {"year": "1976", "text": "<PERSON>, Irish lawyer and politician, 3rd Taoiseach of Ireland (b. 1891)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\"><PERSON><PERSON><PERSON> of Ireland</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1978", "text": "<PERSON>, American author and screenwriter (b. 1927)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor, singer, and screenwriter (b. 1894)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and screenwriter (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and screenwriter (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American bassist, composer, bandleader (b. 1922)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist, composer, bandleader (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist, composer, bandleader (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American chemist and astronomer, Nobel Prize laureate (b. 1893)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Italian poet and philosopher (b. 1901)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Lanza_del_Vasto\" title=\"Lanza del Vasto\">Lanza del Vasto</a>, Italian poet and philosopher (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lanza_del_Vasto\" title=\"Lanza del Vasto\">Lanza del Vasto</a>, Italian poet and philosopher (b. 1901)", "links": [{"title": "Lanza del Vasto", "link": "https://wikipedia.org/wiki/Lanza_del_Vasto"}]}, {"year": "1982", "text": "<PERSON>, American actor (b. 1917)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian general and politician, 7th Chief Justice of Victoria (b. 1892)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general and politician, 7th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Victoria\" title=\"Chief Justice of Victoria\">Chief Justice of Victoria</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general and politician, 7th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Victoria\" title=\"Chief Justice of Victoria\">Chief Justice of Victoria</a> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of Victoria", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Victoria"}]}, {"year": "1985", "text": "<PERSON>, American cinematographer (b. 1906)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cinematographer)\" title=\"<PERSON> (cinematographer)\"><PERSON></a>, American cinematographer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cinematographer)\" title=\"<PERSON> (cinematographer)\"><PERSON></a>, American cinematographer (b. 1906)", "links": [{"title": "<PERSON> (cinematographer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cinematographer)"}]}, {"year": "1987", "text": "<PERSON>, Canadian author and academic (b. 1926)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Norwegian-Canadian skier (b. 1875)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Canadian skier (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Canadian skier (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1914)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Serbian poet and academic (b. 1922)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Vasko_Popa\" title=\"Vasko Popa\"><PERSON><PERSON><PERSON></a>, Serbian poet and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasko_Popa\" title=\"Vasko Popa\"><PERSON><PERSON><PERSON></a>, Serbian poet and academic (b. 1922)", "links": [{"title": "Vask<PERSON> Pop<PERSON>", "link": "https://wikipedia.org/wiki/Vasko_Popa"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American lawyer and politician, 55th Speaker of the United States House of Representatives (b. 1912)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Tip_O%27Neill\" title=\"Tip <PERSON>\"><PERSON><PERSON> <PERSON></a>, American lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tip_O%27Neill\" title=\"T<PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tip_O%27Neill"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1997", "text": "<PERSON>, Belgian author and illustrator (b. 1924)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American composer and songwriter (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Burton_Lane\" title=\"Burton Lane\"><PERSON></a>, American composer and songwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burton_Lane\" title=\"Burton Lane\"><PERSON></a>, American composer and songwriter (b. 1912)", "links": [{"title": "Burton Lane", "link": "https://wikipedia.org/wiki/Burton_Lane"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter, producer, actor, and politician (b. 1935)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, actor, and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, actor, and politician (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Sri Lankan Tamil lawyer and politician (b. 1938)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan Tamil lawyer and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan Tamil lawyer and politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kumar_<PERSON>lam"}]}, {"year": "2003", "text": "<PERSON>, Welsh politician, Chancellor of the Exchequer (b. 1920)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "2004", "text": "<PERSON>, English biologist and chemist, co-developed penicillin (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and chemist, co-developed <a href=\"https://wikipedia.org/wiki/Penicillin\" title=\"Penicillin\">penicillin</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and chemist, co-developed <a href=\"https://wikipedia.org/wiki/Penicillin\" title=\"Penicillin\">penicillin</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Penicillin", "link": "https://wikipedia.org/wiki/Penicillin"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Welsh educator and politician, Home Secretary (b. 1920)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh educator and politician, Home Secretary (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh educator and politician, Home Secretary (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Taiwanese-Japanese businessman, founded Nissin Foods (b. 1910)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>of<PERSON>_Ando\" title=\"Momof<PERSON> Ando\"><PERSON><PERSON><PERSON></a>, Taiwanese-Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Nissin_Foods\" title=\"Nissin Foods\">Nissin Foods</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ando\" title=\"Momof<PERSON> Ando\"><PERSON><PERSON><PERSON></a>, Taiwanese-Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Nissin_Foods\" title=\"Nissin Foods\">Nissin Foods</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>of<PERSON>_<PERSON>o"}, {"title": "Nissin Foods", "link": "https://wikipedia.org/wiki/<PERSON>ssin_Foods"}]}, {"year": "2009", "text": "<PERSON>, American lawyer and politician, 72nd United States Attorney General (b. 1918)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American lawyer and politician, 72nd <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American lawyer and politician, 72nd <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "2010", "text": "<PERSON>, American singer-songwriter, trumpet player, and producer (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, trumpet player, and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, trumpet player, and producer (b. 1928)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2010", "text": "<PERSON>, American painter (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Spanish painter and sculptor (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADaz_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and sculptor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADaz_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and sculptor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Isaac_D%C3%ADaz_Pardo"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American author, playwright, and screenwriter (b. 1900)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, playwright, and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, playwright, and screenwriter (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Pakistani scholar and politician (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani scholar and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani scholar and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Mozambican-Portuguese footballer and manager (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Eus%C3%A9bio\" title=\"Eusébio\"><PERSON><PERSON><PERSON><PERSON></a>, Mozambican-Portuguese footballer and manager (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eus%C3%A9bio\" title=\"Eusébio\"><PERSON><PERSON><PERSON><PERSON></a>, Mozambican-Portuguese footballer and manager (b. 1942)", "links": [{"title": "Eusébio", "link": "https://wikipedia.org/wiki/Eus%C3%A9bio"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_Z<PERSON>ta"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, French racing driver and motorcycle racer (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing driver and motorcycle racer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing driver and motorcycle racer (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American bishop (b. 1912)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, French pianist, composer, and conductor (b. 1925)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and conductor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and conductor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, English rape victim and activist (b. 1965)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rape victim and activist (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rape victim and activist (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Pakistani three star general and politician (b. 1921)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani three star general and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani three star general and politician (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American astronomer best known as the co-discoverer of comet <PERSON><PERSON><PERSON><PERSON> (b. 1949)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer best known as the co-discoverer of <a href=\"https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp\" title=\"Comet Hale-Bopp\">comet <PERSON>-<PERSON><PERSON></a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer best known as the co-discoverer of <a href=\"https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp\" title=\"Comet Hale-Bopp\">comet <PERSON>-<PERSON><PERSON></a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Comet Hale-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp"}]}, {"year": "2018", "text": "<PERSON>, German ballerina (b. 1941)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ballerina (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ballerina (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American women's rights activist (b. 1928)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American women's rights activist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American women's rights activist (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, Serbian footballer and manager (b. 1937)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0ekularac\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0ekularac\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0ekularac"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi Islamic scholar and politician (b. 1938)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Tafazzul_Haque_Habiganji\" class=\"mw-redirect\" title=\"Tafazzul Haque Habiganji\">Tafazz<PERSON> Haque Habiganji</a>, Bangladeshi Islamic scholar and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tafazzul_Haque_Habiganji\" class=\"mw-redirect\" title=\"Tafazzul Haque Habiganji\"><PERSON>fazz<PERSON> Haque Habiganji</a>, Bangladeshi Islamic scholar and politician (b. 1938)", "links": [{"title": "Tafazzul Haque Habiganji", "link": "https://wikipedia.org/wiki/Tafazzul_Haque_Habiganji"}]}, {"year": "2021", "text": "<PERSON>, English footballer (b. 1946)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1946)\" title=\"<PERSON> (footballer, born 1946)\"><PERSON></a>, English footballer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1946)\" title=\"<PERSON> (footballer, born 1946)\"><PERSON></a>, English footballer (b. 1946)", "links": [{"title": "<PERSON> (footballer, born 1946)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1946)"}]}, {"year": "2021", "text": "<PERSON>, English violinist and composer (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and composer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and composer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, South Korean actress and model (b. 1992)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress and model (b. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress and model (b. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>o"}]}, {"year": "2024", "text": "<PERSON>, American journalist and executive editor of The New York Times (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and executive editor of <a href=\"https://wikipedia.org/wiki/The_New_York_Times\" title=\"The New York Times\"><i>The</i> <i>New York Times</i></a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and executive editor of <a href=\"https://wikipedia.org/wiki/The_New_York_Times\" title=\"The New York Times\"><i>The</i> <i>New York Times</i></a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "The New York Times", "link": "https://wikipedia.org/wiki/The_New_York_Times"}]}, {"year": "2025", "text": "<PERSON>, Australian-American former Scientologist, critic (b. 1955)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American former Scientologist, critic (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American former Scientologist, critic (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON>, Greek economist, lawyer, and politician, Prime Minister of Greece (b. 1936)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}]}}