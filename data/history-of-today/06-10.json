{"date": "June 10", "url": "https://wikipedia.org/wiki/June_10", "data": {"Events": [{"year": "671", "text": "Emperor <PERSON><PERSON> of Japan introduces a water clock (clepsydra) called <PERSON>oko<PERSON>. The instrument, which measures time and indicates hours, is placed in the capital of Ōtsu.", "html": "671 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan introduces a water clock (<a href=\"https://wikipedia.org/wiki/Water_clock\" title=\"Water clock\">clepsy<PERSON></a>) called <i><PERSON><PERSON><PERSON></i>. The instrument, which measures time and indicates hours, is placed in the capital of <a href=\"https://wikipedia.org/wiki/%C5%8Ctsu,_Shiga\" class=\"mw-redirect\" title=\"Ōtsu, Shiga\">Ōtsu</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan introduces a water clock (<a href=\"https://wikipedia.org/wiki/Water_clock\" title=\"Water clock\">clepsydra</a>) called <i><PERSON><PERSON><PERSON></i>. The instrument, which measures time and indicates hours, is placed in the capital of <a href=\"https://wikipedia.org/wiki/%C5%8Ctsu,_Shiga\" class=\"mw-redirect\" title=\"Ōtsu, Shiga\">Ōtsu</a>.", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}, {"title": "Water clock", "link": "https://wikipedia.org/wiki/Water_clock"}, {"title": "Ōtsu, Shiga", "link": "https://wikipedia.org/wiki/%C5%8C<PERSON>,_<PERSON>ga"}]}, {"year": "1190", "text": "Third Crusade: <PERSON> drowns in the river Saleph while leading an army to Jerusalem.", "html": "1190 - <a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"Frederick I, Holy Roman Emperor\"><PERSON></a> drowns in the river <a href=\"https://wikipedia.org/wiki/G%C3%B6ksu\" title=\"Göksu\"><PERSON><PERSON></a> while leading an army to <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"Frederick I, Holy Roman Emperor\"><PERSON></a> drowns in the river <a href=\"https://wikipedia.org/wiki/G%C3%B6ksu\" title=\"Göksu\"><PERSON>ph</a> while leading an army to <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "Third Crusade", "link": "https://wikipedia.org/wiki/Third_Crusade"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6ksu"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1225", "text": " <PERSON> <PERSON><PERSON> issues the bull Vineae Domini custodes in which he approves the mission of Dominican friars to Morocco.", "html": "1225 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_III\" title=\"Pope Honorius III\">Pope <PERSON><PERSON></a> issues the bull <a href=\"https://wikipedia.org/wiki/Vineae_Domini_custodes\" title=\"Vineae Domini custodes\">Vineae Domini custodes</a> in which he approves <a href=\"https://wikipedia.org/wiki/Catholic_Church_in_Morocco#Middle_Ages\" title=\"Catholic Church in Morocco\">the mission of Dominican friars to Morocco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_III\" title=\"Pope Honorius III\">Pope <PERSON><PERSON> III</a> issues the bull <a href=\"https://wikipedia.org/wiki/Vineae_Domini_custodes\" title=\"Vineae Domini custodes\">Vineae Domini custodes</a> in which he approves <a href=\"https://wikipedia.org/wiki/Catholic_Church_in_Morocco#Middle_Ages\" title=\"Catholic Church in Morocco\">the mission of Dominican friars to Morocco</a>.", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vineae Domini custodes", "link": "https://wikipedia.org/wiki/Vineae_Domini_custodes"}, {"title": "Catholic Church in Morocco", "link": "https://wikipedia.org/wiki/Catholic_Church_in_Morocco#Middle_Ages"}]}, {"year": "1329", "text": "The Battle of Pelekanon is the last attempt of the Byzantine Empire to retain its cities in Asia Minor.", "html": "1329 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Pelekanon\" title=\"Battle of Pelekanon\">Battle of Pelekanon</a> is the last attempt of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> to retain its cities in <a href=\"https://wikipedia.org/wiki/Asia_Minor\" class=\"mw-redirect\" title=\"Asia Minor\">Asia Minor</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Pelekanon\" title=\"Battle of Pelekanon\">Battle of Pelekanon</a> is the last attempt of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> to retain its cities in <a href=\"https://wikipedia.org/wiki/Asia_Minor\" class=\"mw-redirect\" title=\"Asia Minor\">Asia Minor</a>.", "links": [{"title": "Battle of Pelekanon", "link": "https://wikipedia.org/wiki/Battle_of_Pelekanon"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Asia Minor", "link": "https://wikipedia.org/wiki/Asia_Minor"}]}, {"year": "1358", "text": "Battle of Mello: The peasant forces of the Jacquerie are crushed by the army of the French nobility.", "html": "1358 - <a href=\"https://wikipedia.org/wiki/Battle_of_Mello\" title=\"Battle of Mello\">Battle of Mello</a>: The peasant forces of the <a href=\"https://wikipedia.org/wiki/Jacquerie\" title=\"Jacquerie\">Jacquerie</a> are crushed by the army of the French nobility.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Mello\" title=\"Battle of Mello\">Battle of Mello</a>: The peasant forces of the <a href=\"https://wikipedia.org/wiki/Jacquerie\" title=\"Jacquerie\">Jacquerie</a> are crushed by the army of the French nobility.", "links": [{"title": "Battle of Mello", "link": "https://wikipedia.org/wiki/Battle_of_Mello"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jac<PERSON>ie"}]}, {"year": "1523", "text": "Copenhagen is surrounded by the army of <PERSON> of Denmark, as the city will not recognise him as the successor of <PERSON> of Denmark.", "html": "1523 - <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a> is surrounded by the army of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>, as the city will not recognise him as the successor of <a href=\"https://wikipedia.org/wiki/Christian_II_of_Denmark\" title=\"Christian II of Denmark\"><PERSON> of Denmark</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a> is surrounded by the army of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>, as the city will not recognise him as the successor of <a href=\"https://wikipedia.org/wiki/Christian_II_of_Denmark\" title=\"Christian II of Denmark\"><PERSON> of Denmark</a>.", "links": [{"title": "Copenhagen", "link": "https://wikipedia.org/wiki/Copenhagen"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_II_of_Denmark"}]}, {"year": "1539", "text": "Council of Trent: Pope <PERSON> sends out letters to his bishops, delaying the Council due to war and the difficulty bishops had traveling to Venice.", "html": "1539 - <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON> III</a> sends out letters to his <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishops</a>, delaying the Council due to war and the difficulty bishops had traveling to <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> sends out letters to his <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishops</a>, delaying the Council due to war and the difficulty bishops had traveling to <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a>.", "links": [{"title": "Council of Trent", "link": "https://wikipedia.org/wiki/Council_of_Trent"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bishop"}, {"title": "Venice", "link": "https://wikipedia.org/wiki/Venice"}]}, {"year": "1596", "text": "<PERSON> and <PERSON> discover Bear Island.", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Bar<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> discover <a href=\"https://wikipedia.org/wiki/Bear_Island_(Norway)\" class=\"mw-redirect\" title=\"Bear Island (Norway)\">Bear Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> discover <a href=\"https://wikipedia.org/wiki/Bear_Island_(Norway)\" class=\"mw-redirect\" title=\"Bear Island (Norway)\">Bear Island</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bear Island (Norway)", "link": "https://wikipedia.org/wiki/Bear_Island_(Norway)"}]}, {"year": "1619", "text": "Thirty Years' War: Battle of Záblatí, a turning point in the Bohemian Revolt.", "html": "1619 - <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Z%C3%A1blat%C3%AD\" class=\"mw-redirect\" title=\"Battle of Záblatí\">Battle of Záblatí</a>, a turning point in the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War#The_Bohemian_Revolt\" title=\"Thirty Years' War\">Bohemian Revolt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Z%C3%A1blat%C3%AD\" class=\"mw-redirect\" title=\"Battle of Záblatí\">Battle of Záblatí</a>, a turning point in the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War#The_Bohemian_Revolt\" title=\"Thirty Years' War\">Bohemian Revolt</a>.", "links": [{"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}, {"title": "Battle of Záblatí", "link": "https://wikipedia.org/wiki/Battle_of_Z%C3%A1blat%C3%AD"}, {"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War#The_Bohemian_Revolt"}]}, {"year": "1624", "text": "Signing of the Treaty of Compiègne between France and the Netherlands.", "html": "1624 - Signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Compi%C3%A8gne_(1624)\" title=\"Treaty of Compiègne (1624)\">Treaty of Compiègne</a> between France and the Netherlands.", "no_year_html": "Signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Compi%C3%A8gne_(1624)\" title=\"Treaty of Compiègne (1624)\">Treaty of Compiègne</a> between France and the Netherlands.", "links": [{"title": "Treaty of Compiègne (1624)", "link": "https://wikipedia.org/wiki/Treaty_of_Compi%C3%A8gne_(1624)"}]}, {"year": "1692", "text": "Salem witch trials: <PERSON> is hanged at Gallows Hill near Salem, Massachusetts, for \"certaine Detestable Arts called Witchcraft and Sorceries\".", "html": "1692 - <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hanged at Gallows Hill near <a href=\"https://wikipedia.org/wiki/Salem,_Massachusetts\" title=\"Salem, Massachusetts\">Salem, Massachusetts</a>, for \"certaine Detestable Arts called Witchcraft and Sorceries\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hanged at Gallows Hill near <a href=\"https://wikipedia.org/wiki/Salem,_Massachusetts\" title=\"Salem, Massachusetts\">Salem, Massachusetts</a>, for \"certaine Detestable Arts called Witchcraft and Sorceries\".", "links": [{"title": "Salem witch trials", "link": "https://wikipedia.org/wiki/Salem_witch_trials"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Salem, Massachusetts", "link": "https://wikipedia.org/wiki/Salem,_Massachusetts"}]}, {"year": "1719", "text": "Jacobite risings: Battle of Glen Shiel.", "html": "1719 - <a href=\"https://wikipedia.org/wiki/Jacobite_risings\" class=\"mw-redirect\" title=\"Jacobite risings\">Jacobite risings</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Glen_Shiel\" title=\"Battle of Glen Shiel\">Battle of Glen Shiel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jacobite_risings\" class=\"mw-redirect\" title=\"Jacobite risings\">Jacobite risings</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Glen_Shiel\" title=\"Battle of Glen Shiel\">Battle of Glen Shiel</a>.", "links": [{"title": "Jacobite risings", "link": "https://wikipedia.org/wiki/Jacobite_risings"}, {"title": "Battle of Glen Shiel", "link": "https://wikipedia.org/wiki/Battle_of_Glen_Shiel"}]}, {"year": "1782", "text": "King <PERSON> (Rama I) of Siam (modern day Thailand) is crowned.", "html": "1782 - King <a href=\"https://wikipedia.org/wiki/Buddha_Yo<PERSON><PERSON>_Chulaloke\" class=\"mw-redirect\" title=\"Buddha Yod<PERSON> Chulaloke\">Buddha <PERSON><PERSON><PERSON></a> (<PERSON>) of Siam (modern day <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>) is <a href=\"https://wikipedia.org/wiki/Coronation_of_the_Thai_monarch\" title=\"Coronation of the Thai monarch\">crowned</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Buddha_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Buddha Yo<PERSON><PERSON> Chulaloke\">Buddha <PERSON><PERSON><PERSON></a> (<PERSON>) of Siam (modern day <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>) is <a href=\"https://wikipedia.org/wiki/Coronation_of_the_Thai_monarch\" title=\"Coronation of the Thai monarch\">crowned</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Chulaloke"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "Coronation of the Thai monarch", "link": "https://wikipedia.org/wiki/Coronation_of_the_Thai_monarch"}]}, {"year": "1786", "text": "A landslide dam on the Dadu River created by an earthquake ten days earlier collapses, killing 100,000 in the Sichuan province of China.", "html": "1786 - A <a href=\"https://wikipedia.org/wiki/Landslide_dam\" title=\"Landslide dam\">landslide dam</a> on the <a href=\"https://wikipedia.org/wiki/Dadu_River_(Sichuan)\" class=\"mw-redirect\" title=\"Dadu River (Sichuan)\">Dadu River</a> created by <a href=\"https://wikipedia.org/wiki/1786_Kangding-Luding_earthquake\" title=\"1786 Kangding-Luding earthquake\">an earthquake</a> ten days earlier collapses, killing 100,000 in the <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a> province of China.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Landslide_dam\" title=\"Landslide dam\">landslide dam</a> on the <a href=\"https://wikipedia.org/wiki/Dadu_River_(Sichuan)\" class=\"mw-redirect\" title=\"Dadu River (Sichuan)\">Dadu River</a> created by <a href=\"https://wikipedia.org/wiki/1786_Kangding-Luding_earthquake\" title=\"1786 Kangding-Luding earthquake\">an earthquake</a> ten days earlier collapses, killing 100,000 in the <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a> province of China.", "links": [{"title": "Landslide dam", "link": "https://wikipedia.org/wiki/Landslide_dam"}, {"title": "Dadu River (Sichuan)", "link": "https://wikipedia.org/wiki/Dadu_River_(Sichuan)"}, {"title": "1786 Kangding-Luding earthquake", "link": "https://wikipedia.org/wiki/1786_Kangding-Luding_earthquake"}, {"title": "Sichuan", "link": "https://wikipedia.org/wiki/Sichuan"}]}, {"year": "1793", "text": "The Jardin des Plantes museum opens in Paris. A year later, it becomes the first public zoo.", "html": "1793 - The <a href=\"https://wikipedia.org/wiki/Jardin_des_Plantes\" class=\"mw-redirect\" title=\"Jardin des Plantes\">Jardin des Plantes</a> museum opens in Paris. A year later, it becomes the first public <a href=\"https://wikipedia.org/wiki/Zoo\" title=\"Zoo\">zoo</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/J<PERSON>in_des_Plantes\" class=\"mw-redirect\" title=\"Jardin des Plantes\">Jardin des Plantes</a> museum opens in Paris. A year later, it becomes the first public <a href=\"https://wikipedia.org/wiki/Zoo\" title=\"Zoo\">zoo</a>.", "links": [{"title": "Jardin des Plantes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_des_Plantes"}, {"title": "Zoo", "link": "https://wikipedia.org/wiki/Zoo"}]}, {"year": "1793", "text": "French Revolution: Following the arrests of Girondin leaders, the Jacobins gain control of the Committee of Public Safety installing the revolutionary dictatorship.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: Following the arrests of <a href=\"https://wikipedia.org/wiki/Girondin\" class=\"mw-redirect\" title=\"Girondin\">Girondin</a> leaders, the <a href=\"https://wikipedia.org/wiki/Jacobins\" title=\"Jacob<PERSON>\"><PERSON><PERSON></a> gain control of the <a href=\"https://wikipedia.org/wiki/Committee_of_Public_Safety\" title=\"Committee of Public Safety\">Committee of Public Safety</a> installing the <i><a href=\"https://wikipedia.org/wiki/Reign_of_Terror\" title=\"Reign of Terror\">revolutionary dictatorship</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: Following the arrests of <a href=\"https://wikipedia.org/wiki/Girondin\" class=\"mw-redirect\" title=\"Girondin\">Girondin</a> leaders, the <a href=\"https://wikipedia.org/wiki/Jacobins\" title=\"Jacob<PERSON>\"><PERSON><PERSON></a> gain control of the <a href=\"https://wikipedia.org/wiki/Committee_of_Public_Safety\" title=\"Committee of Public Safety\">Committee of Public Safety</a> installing the <i><a href=\"https://wikipedia.org/wiki/Reign_of_Terror\" title=\"Reign of Terror\">revolutionary dictatorship</a></i>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "Girondi<PERSON>", "link": "https://wikipedia.org/wiki/Girondin"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Committee of Public Safety", "link": "https://wikipedia.org/wiki/Committee_of_Public_Safety"}, {"title": "Reign of Terror", "link": "https://wikipedia.org/wiki/Reign_of_Terror"}]}, {"year": "1805", "text": "First Barbary War: <PERSON> signs a treaty ending the hostilities between Tripolitania and the United States.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a treaty ending the hostilities between <a href=\"https://wikipedia.org/wiki/Tripolitania\" title=\"Tripolitania\">Tripolitania</a> and the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a treaty ending the hostilities between <a href=\"https://wikipedia.org/wiki/Tripolitania\" title=\"Tripolitania\">Tripolitania</a> and the United States.", "links": [{"title": "First Barbary War", "link": "https://wikipedia.org/wiki/First_Barbary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tripolitania", "link": "https://wikipedia.org/wiki/Tripolitania"}]}, {"year": "1829", "text": "The first Boat Race between the University of Oxford and the University of Cambridge takes place on the Thames in London.", "html": "1829 - The first <a href=\"https://wikipedia.org/wiki/The_Boat_Race\" title=\"The Boat Race\">Boat Race</a> between the <a href=\"https://wikipedia.org/wiki/University_of_Oxford\" title=\"University of Oxford\">University of Oxford</a> and the <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">University of Cambridge</a> takes place on the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">Thames</a> in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/The_Boat_Race\" title=\"The Boat Race\">Boat Race</a> between the <a href=\"https://wikipedia.org/wiki/University_of_Oxford\" title=\"University of Oxford\">University of Oxford</a> and the <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">University of Cambridge</a> takes place on the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">Thames</a> in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "The Boat Race", "link": "https://wikipedia.org/wiki/The_Boat_Race"}, {"title": "University of Oxford", "link": "https://wikipedia.org/wiki/University_of_Oxford"}, {"title": "University of Cambridge", "link": "https://wikipedia.org/wiki/University_of_Cambridge"}, {"title": "River Thames", "link": "https://wikipedia.org/wiki/River_Thames"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1838", "text": "Myall Creek massacre: Twenty-eight Aboriginal Australians are murdered.", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Myall_Creek_massacre\" title=\"Myall Creek massacre\">Myall Creek massacre</a>: Twenty-eight <a href=\"https://wikipedia.org/wiki/Aboriginal_Australians\" title=\"Aboriginal Australians\">Aboriginal Australians</a> are murdered.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Myall_Creek_massacre\" title=\"Myall Creek massacre\">Myall Creek massacre</a>: Twenty-eight <a href=\"https://wikipedia.org/wiki/Aboriginal_Australians\" title=\"Aboriginal Australians\">Aboriginal Australians</a> are murdered.", "links": [{"title": "Myall Creek massacre", "link": "https://wikipedia.org/wiki/Myall_Creek_massacre"}, {"title": "Aboriginal Australians", "link": "https://wikipedia.org/wiki/Aboriginal_Australians"}]}, {"year": "1854", "text": "The United States Naval Academy graduates its first class of students.", "html": "1854 - The <a href=\"https://wikipedia.org/wiki/United_States_Naval_Academy\" title=\"United States Naval Academy\">United States Naval Academy</a> graduates its first class of students.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Naval_Academy\" title=\"United States Naval Academy\">United States Naval Academy</a> graduates its first class of students.", "links": [{"title": "United States Naval Academy", "link": "https://wikipedia.org/wiki/United_States_Naval_Academy"}]}, {"year": "1861", "text": "American Civil War: Battle of Big Bethel: Confederate troops under <PERSON> defeat a much larger Union force led by General <PERSON><PERSON><PERSON> in Virginia.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Big_Bethel\" title=\"Battle of Big Bethel\">Battle of Big Bethel</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gruder\" title=\"John <PERSON> Ma<PERSON>ruder\"><PERSON></a> defeat a much larger <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> force led by General <a href=\"https://wikipedia.org/wiki/Ebenezer_W._Pierce\" class=\"mw-redirect\" title=\"Ebenezer W. Pierce\"><PERSON><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Big_Bethel\" title=\"Battle of Big Bethel\">Battle of Big Bethel</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gruder\" title=\"John <PERSON> Ma<PERSON>ruder\"><PERSON></a> defeat a much larger <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> force led by General <a href=\"https://wikipedia.org/wiki/Eben<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ebenezer W. Pierce\">Eben<PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Big Bethel", "link": "https://wikipedia.org/wiki/Battle_of_Big_Bethel"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eben<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}]}, {"year": "1863", "text": "During the French intervention in Mexico, Mexico City is captured by French troops.", "html": "1863 - During the <a href=\"https://wikipedia.org/wiki/Second_French_intervention_in_Mexico\" title=\"Second French intervention in Mexico\">French intervention in Mexico</a>, Mexico City is captured by <a href=\"https://wikipedia.org/wiki/Second_French_Empire\" title=\"Second French Empire\">French</a> troops.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Second_French_intervention_in_Mexico\" title=\"Second French intervention in Mexico\">French intervention in Mexico</a>, Mexico City is captured by <a href=\"https://wikipedia.org/wiki/Second_French_Empire\" title=\"Second French Empire\">French</a> troops.", "links": [{"title": "Second French intervention in Mexico", "link": "https://wikipedia.org/wiki/Second_French_intervention_in_Mexico"}, {"title": "Second French Empire", "link": "https://wikipedia.org/wiki/Second_French_Empire"}]}, {"year": "1864", "text": "American Civil War: Battle of Brice's Crossroads: Confederate troops under <PERSON> defeat a much larger Union force led by General <PERSON> in Mississippi.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Brice%27s_Crossroads\" class=\"mw-redirect\" title=\"Battle of Brice's Crossroads\">Battle of Brice's Crossroads</a>: Confederate troops under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Nathan <PERSON>\"><PERSON></a> defeat a much larger Union force led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Brice%27s_Crossroads\" class=\"mw-redirect\" title=\"Battle of Brice's Crossroads\">Battle of Brice's Crossroads</a>: Confederate troops under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Nathan <PERSON>\"><PERSON></a> defeat a much larger Union force led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>.", "links": [{"title": "Battle of Brice's Crossroads", "link": "https://wikipedia.org/wiki/Battle_of_Brice%27s_Crossroads"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Prince of Serbia is assassinated.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> III</a>, <a href=\"https://wikipedia.org/wiki/Principality_of_Serbia\" title=\"Principality of Serbia\">Prince of Serbia</a> is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> III</a>, <a href=\"https://wikipedia.org/wiki/Principality_of_Serbia\" title=\"Principality of Serbia\">Prince of Serbia</a> is assassinated.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_O<PERSON>novi%C4%87"}, {"title": "Principality of Serbia", "link": "https://wikipedia.org/wiki/Principality_of_Serbia"}]}, {"year": "1871", "text": "Sinmiyangyo: Captain <PERSON><PERSON><PERSON><PERSON> leads 109 US Marines in a naval attack on Han River forts on Kanghwa Island, Korea.", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Sinmiyangyo\" class=\"mw-redirect\" title=\"Sinmiyangyo\">Sinmiyangyo</a>: Captain <PERSON><PERSON><PERSON><PERSON> leads 109 <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">US Marines</a> in a naval attack on <a href=\"https://wikipedia.org/wiki/Han_River_(Korea)\" title=\"Han River (Korea)\">Han River</a> forts on <a href=\"https://wikipedia.org/wiki/Kanghwa_Island\" class=\"mw-redirect\" title=\"Kanghwa Island\">Kanghwa Island</a>, Korea.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sinmiyangyo\" class=\"mw-redirect\" title=\"Sinmiyangyo\">Sinmiyangyo</a>: Captain <PERSON><PERSON><PERSON><PERSON> leads 109 <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">US Marines</a> in a naval attack on <a href=\"https://wikipedia.org/wiki/Han_River_(Korea)\" title=\"Han River (Korea)\">Han River</a> forts on <a href=\"https://wikipedia.org/wiki/Kanghwa_Island\" class=\"mw-redirect\" title=\"Kanghwa Island\">Kanghwa Island</a>, Korea.", "links": [{"title": "Sinmiyangyo", "link": "https://wikipedia.org/wiki/Sinmiyangyo"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Han River (Korea)", "link": "https://wikipedia.org/wiki/Han_River_(Korea)"}, {"title": "Kanghwa Island", "link": "https://wikipedia.org/wiki/Kanghwa_Island"}]}, {"year": "1878", "text": "League of Prizren is established, to oppose the decisions of the Congress of Berlin and the Treaty of San Stefano, as a consequence of which the Albanian lands in the Balkans were being partitioned and given to the neighbor states of Serbia, Montenegro, Bulgaria, and Greece.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/League_of_Prizren\" title=\"League of Prizren\">League of Prizren</a> is established, to oppose the decisions of the <a href=\"https://wikipedia.org/wiki/Congress_of_Berlin\" title=\"Congress of Berlin\">Congress of Berlin</a> and the <a href=\"https://wikipedia.org/wiki/Treaty_of_San_Stefano\" title=\"Treaty of San Stefano\">Treaty of San Stefano</a>, as a consequence of which the Albanian lands in the <a href=\"https://wikipedia.org/wiki/Balkans\" title=\"Balkans\">Balkans</a> were being partitioned and given to the neighbor states of <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>, <a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegro</a>, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, and Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/League_of_Prizren\" title=\"League of Prizren\">League of Prizren</a> is established, to oppose the decisions of the <a href=\"https://wikipedia.org/wiki/Congress_of_Berlin\" title=\"Congress of Berlin\">Congress of Berlin</a> and the <a href=\"https://wikipedia.org/wiki/Treaty_of_San_Stefano\" title=\"Treaty of San Stefano\">Treaty of San Stefano</a>, as a consequence of which the Albanian lands in the <a href=\"https://wikipedia.org/wiki/Balkans\" title=\"Balkans\">Balkans</a> were being partitioned and given to the neighbor states of <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>, <a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegro</a>, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, and Greece.", "links": [{"title": "League of Prizren", "link": "https://wikipedia.org/wiki/League_of_Prizren"}, {"title": "Congress of Berlin", "link": "https://wikipedia.org/wiki/Congress_of_Berlin"}, {"title": "Treaty of San Stefano", "link": "https://wikipedia.org/wiki/Treaty_of_San_Stefano"}, {"title": "Balkans", "link": "https://wikipedia.org/wiki/Balkans"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Montenegro", "link": "https://wikipedia.org/wiki/Montenegro"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}]}, {"year": "1886", "text": "Mount Tarawera in New Zealand erupts, killing 153 people and burying the famous Pink and White Terraces. Eruptions continue for three months creating a large, 17 km (11 mi) long fissure across the mountain peak.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Mount_Tarawera\" title=\"Mount Tarawera\">Mount Tarawera</a> in New Zealand <a href=\"https://wikipedia.org/wiki/1886_eruption_of_Mount_Tarawera\" title=\"1886 eruption of Mount Tarawera\">erupts</a>, killing 153 people and burying the famous <a href=\"https://wikipedia.org/wiki/Pink_and_White_Terraces\" title=\"Pink and White Terraces\">Pink and White Terraces</a>. Eruptions continue for three months creating a large, 17 km (11 mi) long fissure across the mountain peak.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mount_Tarawera\" title=\"Mount Tarawera\">Mount Tarawera</a> in New Zealand <a href=\"https://wikipedia.org/wiki/1886_eruption_of_Mount_Tarawera\" title=\"1886 eruption of Mount Tarawera\">erupts</a>, killing 153 people and burying the famous <a href=\"https://wikipedia.org/wiki/Pink_and_White_Terraces\" title=\"Pink and White Terraces\">Pink and White Terraces</a>. Eruptions continue for three months creating a large, 17 km (11 mi) long fissure across the mountain peak.", "links": [{"title": "Mount Tarawera", "link": "https://wikipedia.org/wiki/Mount_Tarawera"}, {"title": "1886 eruption of Mount Tarawera", "link": "https://wikipedia.org/wiki/1886_eruption_of_Mount_Tarawera"}, {"title": "Pink and White Terraces", "link": "https://wikipedia.org/wiki/Pink_and_White_Terraces"}]}, {"year": "1898", "text": "Spanish-American War: In the Battle of Guantánamo Bay, U.S. Marines begin the American invasion of Spanish-held Cuba.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Guant%C3%A1namo_Bay\" title=\"Battle of Guantánamo Bay\">Battle of Guantánamo Bay</a>, <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> begin the American invasion of Spanish-held <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Guant%C3%A1namo_Bay\" title=\"Battle of Guantánamo Bay\">Battle of Guantánamo Bay</a>, <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> begin the American invasion of Spanish-held <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "Battle of Guantánamo Bay", "link": "https://wikipedia.org/wiki/Battle_of_Guant%C3%A1namo_Bay"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1916", "text": "The Arab Revolt against the Ottoman Empire was declared by <PERSON>, Sharif of Mecca.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/Arab_Revolt\" title=\"Arab Revolt\">Arab Revolt</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> was declared by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sharif_of_Mecca\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON> of Mecca\"><PERSON>, Sharif of Mecca</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arab_Revolt\" title=\"Arab Revolt\">Arab Revolt</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> was declared by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sharif_of_Mecca\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON> of Mecca\"><PERSON>, Sharif of Mecca</a>.", "links": [{"title": "Arab Revolt", "link": "https://wikipedia.org/wiki/Arab_Revolt"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON>, Sharif of Mecca", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sharif_of_Mecca"}]}, {"year": "1918", "text": "The Austro-Hungarian battleship SMS Szent István sinks off the Croatian coast after being torpedoed by an Italian MAS motorboat; the event is recorded by camera from a nearby vessel.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Navy\" title=\"Austro-Hungarian Navy\">Austro-Hungarian</a> battleship <a href=\"https://wikipedia.org/wiki/SMS_Szent_Istv%C3%A1n\" title=\"SMS Szent István\">SMS <i>Szent István</i></a> sinks off the <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> coast after being torpedoed by an <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> <a href=\"https://wikipedia.org/wiki/MAS_(ships)\" class=\"mw-redirect\" title=\"MAS (ships)\">MAS</a> motorboat; the event is recorded by camera from a nearby vessel.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Navy\" title=\"Austro-Hungarian Navy\">Austro-Hungarian</a> battleship <a href=\"https://wikipedia.org/wiki/SMS_Szent_Istv%C3%A1n\" title=\"SMS Szent István\">SMS <i>Szent István</i></a> sinks off the <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> coast after being torpedoed by an <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> <a href=\"https://wikipedia.org/wiki/MAS_(ships)\" class=\"mw-redirect\" title=\"MAS (ships)\">MAS</a> motorboat; the event is recorded by camera from a nearby vessel.", "links": [{"title": "Austro-Hungarian Navy", "link": "https://wikipedia.org/wiki/Austro-Hungarian_Navy"}, {"title": "SMS Szent István", "link": "https://wikipedia.org/wiki/SMS_Szent_Istv%C3%A1n"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "MAS (ships)", "link": "https://wikipedia.org/wiki/MAS_(ships)"}]}, {"year": "1924", "text": "Fascists kidnap and kill Italian Socialist leader <PERSON> in Rome.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Fascist\" class=\"mw-redirect\" title=\"Fascist\">Fascists</a> kidnap and kill Italian <a href=\"https://wikipedia.org/wiki/Socialism\" title=\"Socialism\">Socialist</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in Rome.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fascist\" class=\"mw-redirect\" title=\"Fascist\">Fascists</a> kidnap and kill Italian <a href=\"https://wikipedia.org/wiki/Socialism\" title=\"Socialism\">Socialist</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in Rome.", "links": [{"title": "Fascist", "link": "https://wikipedia.org/wiki/Fascist"}, {"title": "Socialism", "link": "https://wikipedia.org/wiki/Socialism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "Dr. <PERSON> takes his last drink, and Alcoholics Anonymous is founded in Akron, Ohio, United States, by him and <PERSON>.", "html": "1935 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(doctor)\" title=\"<PERSON> (doctor)\"><PERSON></a> takes his last drink, and <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a> is founded in <a href=\"https://wikipedia.org/wiki/Akron,_Ohio\" title=\"Akron, Ohio\">Akron</a>, <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, United States, by him and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(doctor)\" title=\"<PERSON> (doctor)\"><PERSON></a> takes his last drink, and <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a> is founded in <a href=\"https://wikipedia.org/wiki/Akron,_Ohio\" title=\"Akron, Ohio\">Akron</a>, <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, United States, by him and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON> (doctor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doctor)"}, {"title": "Alcoholics Anonymous", "link": "https://wikipedia.org/wiki/Alcoholics_Anonymous"}, {"title": "Akron, Ohio", "link": "https://wikipedia.org/wiki/Akron,_Ohio"}, {"title": "Ohio", "link": "https://wikipedia.org/wiki/Ohio"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "1935", "text": "Chaco War ends: A truce is called between Bolivia and Paraguay who had been fighting since 1932.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Chaco_War\" title=\"Chaco War\">Chaco War</a> ends: A truce is called between <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a> and <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a> who had been fighting since <a href=\"https://wikipedia.org/wiki/1932\" title=\"1932\">1932</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chaco_War\" title=\"Chaco War\">Chaco War</a> ends: A truce is called between <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a> and <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a> who had been fighting since <a href=\"https://wikipedia.org/wiki/1932\" title=\"1932\">1932</a>.", "links": [{"title": "Chaco War", "link": "https://wikipedia.org/wiki/Chaco_War"}, {"title": "Bolivia", "link": "https://wikipedia.org/wiki/Bolivia"}, {"title": "Paraguay", "link": "https://wikipedia.org/wiki/Paraguay"}, {"title": "1932", "link": "https://wikipedia.org/wiki/1932"}]}, {"year": "1940", "text": "World War II: Fascist Italy declares war on France and the United Kingdom, beginning an invasion of southern France.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Fascist_Italy_(1922%E2%80%931943)\" class=\"mw-redirect\" title=\"Fascist Italy (1922-1943)\">Fascist Italy</a> declares war on France and the United Kingdom, beginning an <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_France\" title=\"Italian invasion of France\">invasion of southern France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Fascist_Italy_(1922%E2%80%931943)\" class=\"mw-redirect\" title=\"Fascist Italy (1922-1943)\">Fascist Italy</a> declares war on France and the United Kingdom, beginning an <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_France\" title=\"Italian invasion of France\">invasion of southern France</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Fascist <PERSON> (1922-1943)", "link": "https://wikipedia.org/wiki/Fascist_Italy_(1922%E2%80%931943)"}, {"title": "Italian invasion of France", "link": "https://wikipedia.org/wiki/Italian_invasion_of_France"}]}, {"year": "1940", "text": "World War II: U.S. President <PERSON> denounces Italy's actions in his \"Stab in the Back\" speech at the graduation ceremonies of the University of Virginia.", "html": "1940 - World War II: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> denounces Italy's actions in his \"Stab in the Back\" speech at the graduation ceremonies of the <a href=\"https://wikipedia.org/wiki/University_of_Virginia\" title=\"University of Virginia\">University of Virginia</a>.", "no_year_html": "World War II: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> denounces Italy's actions in his \"Stab in the Back\" speech at the graduation ceremonies of the <a href=\"https://wikipedia.org/wiki/University_of_Virginia\" title=\"University of Virginia\">University of Virginia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "University of Virginia", "link": "https://wikipedia.org/wiki/University_of_Virginia"}]}, {"year": "1940", "text": "World War II: Military resistance to the German occupation of Norway ends.", "html": "1940 - World War II: Military resistance to the <a href=\"https://wikipedia.org/wiki/German_occupation_of_Norway\" title=\"German occupation of Norway\">German occupation of Norway</a> ends.", "no_year_html": "World War II: Military resistance to the <a href=\"https://wikipedia.org/wiki/German_occupation_of_Norway\" title=\"German occupation of Norway\">German occupation of Norway</a> ends.", "links": [{"title": "German occupation of Norway", "link": "https://wikipedia.org/wiki/German_occupation_of_Norway"}]}, {"year": "1942", "text": "World War II: The Lidice massacre is perpetrated as a reprisal for the assassination of Obergruppenführer <PERSON><PERSON><PERSON>.", "html": "1942 - World War II: The <a href=\"https://wikipedia.org/wiki/Lidice_massacre\" title=\"Lidice massacre\">Lidice massacre</a> is perpetrated as a reprisal for the assassination of <i><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></i> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Lidice_massacre\" title=\"Lidice massacre\">Lidice massacre</a> is perpetrated as a reprisal for the assassination of <i><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></i> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Lidice massacre", "link": "https://wikipedia.org/wiki/Lidice_massacre"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "World War II: Six hundred forty-three men, women and children massacred at Oradour-sur-Glane, France.", "html": "1944 - World War II: Six hundred forty-three men, women and children <a href=\"https://wikipedia.org/wiki/Oradour-sur-Glane_massacre\" title=\"Oradour-sur-Glane massacre\">massacred</a> at <a href=\"https://wikipedia.org/wiki/Oradour-sur-Glane\" title=\"Oradour-sur-Glane\">Oradour-sur-Glane</a>, France.", "no_year_html": "World War II: Six hundred forty-three men, women and children <a href=\"https://wikipedia.org/wiki/Oradour-sur-Glane_massacre\" title=\"Oradour-sur-Glane massacre\">massacred</a> at <a href=\"https://wikipedia.org/wiki/Oradour-sur-Glane\" title=\"Oradour-sur-Glane\">Oradour-sur-Glane</a>, France.", "links": [{"title": "Oradour-sur-Glane massacre", "link": "https://wikipedia.org/wiki/Oradour-sur-Glane_massacre"}, {"title": "Oradour-sur-Glane", "link": "https://wikipedia.org/wiki/Oradour-sur-Glane"}]}, {"year": "1944", "text": "World War II: In Distomo, Boeotia, Greece, 228 men, women and children are massacred by German troops.", "html": "1944 - World War II: In <a href=\"https://wikipedia.org/wiki/Distomo\" title=\"Disto<PERSON>\">Distomo</a>, <a href=\"https://wikipedia.org/wiki/Boeotia\" title=\"Boeotia\">Boeotia</a>, Greece, 228 men, women and children are <a href=\"https://wikipedia.org/wiki/Distomo_massacre\" title=\"Distomo massacre\">massacred by German troops</a>.", "no_year_html": "World War II: In <a href=\"https://wikipedia.org/wiki/Distomo\" title=\"Distomo\">Distomo</a>, <a href=\"https://wikipedia.org/wiki/Boeotia\" title=\"Boeotia\">Boeotia</a>, Greece, 228 men, women and children are <a href=\"https://wikipedia.org/wiki/Distomo_massacre\" title=\"Distomo massacre\">massacred by German troops</a>.", "links": [{"title": "Distomo", "link": "https://wikipedia.org/wiki/Distomo"}, {"title": "Boeotia", "link": "https://wikipedia.org/wiki/Boeotia"}, {"title": "Distomo massacre", "link": "https://wikipedia.org/wiki/Distomo_massacre"}]}, {"year": "1944", "text": "In baseball, 15-year-old <PERSON> of the Cincinnati Reds becomes the youngest player ever in a major-league game.", "html": "1944 - In <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a>, 15-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Cincinnati_Reds\" title=\"Cincinnati Reds\">Cincinnati Reds</a> becomes the youngest player ever in a major-league game.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a>, 15-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Cincinnati_Reds\" title=\"Cincinnati Reds\">Cincinnati Reds</a> becomes the youngest player ever in a major-league game.", "links": [{"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cincinnati Reds", "link": "https://wikipedia.org/wiki/Cincinnati_Reds"}]}, {"year": "1945", "text": "Australian Imperial Forces land in Brunei Bay to liberate Brunei.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Second_Australian_Imperial_Force\" title=\"Second Australian Imperial Force\">Australian Imperial Forces</a> land in <a href=\"https://wikipedia.org/wiki/Brunei_Bay\" title=\"Brunei Bay\">Brunei Bay</a> to liberate <a href=\"https://wikipedia.org/wiki/Brunei\" title=\"Brunei\">Brunei</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Australian_Imperial_Force\" title=\"Second Australian Imperial Force\">Australian Imperial Forces</a> land in <a href=\"https://wikipedia.org/wiki/Brunei_Bay\" title=\"Brunei Bay\">Brunei Bay</a> to liberate <a href=\"https://wikipedia.org/wiki/Brunei\" title=\"Brunei\">Brunei</a>.", "links": [{"title": "Second Australian Imperial Force", "link": "https://wikipedia.org/wiki/Second_Australian_Imperial_Force"}, {"title": "Brunei Bay", "link": "https://wikipedia.org/wiki/Brunei_Bay"}, {"title": "Brunei", "link": "https://wikipedia.org/wiki/Brunei"}]}, {"year": "1947", "text": "Saab produces its first automobile.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Saab_Automobile\" title=\"Saab Automobile\">Saab</a> produces its first automobile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saab_Automobile\" title=\"Saab Automobile\">Saab</a> produces its first automobile.", "links": [{"title": "Saab Automobile", "link": "https://wikipedia.org/wiki/Saab_Automobile"}]}, {"year": "1957", "text": "<PERSON> leads the Progressive Conservative Party of Canada to a stunning upset in the 1957 Canadian federal election, ending 22 years of Liberal Party government.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada\" title=\"Progressive Conservative Party of Canada\">Progressive Conservative Party of Canada</a> to a stunning upset in the <a href=\"https://wikipedia.org/wiki/1957_Canadian_federal_election\" title=\"1957 Canadian federal election\">1957 Canadian federal election</a>, ending 22 years of <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada\" title=\"Liberal Party of Canada\">Liberal Party</a> government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada\" title=\"Progressive Conservative Party of Canada\">Progressive Conservative Party of Canada</a> to a stunning upset in the <a href=\"https://wikipedia.org/wiki/1957_Canadian_federal_election\" title=\"1957 Canadian federal election\">1957 Canadian federal election</a>, ending 22 years of <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada\" title=\"Liberal Party of Canada\">Liberal Party</a> government.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Progressive Conservative Party of Canada", "link": "https://wikipedia.org/wiki/Progressive_Conservative_Party_of_Canada"}, {"title": "1957 Canadian federal election", "link": "https://wikipedia.org/wiki/1957_Canadian_federal_election"}, {"title": "Liberal Party of Canada", "link": "https://wikipedia.org/wiki/Liberal_Party_of_Canada"}]}, {"year": "1960", "text": "Trans Australia Airlines Flight 538 crashes near Mackay Airport in Mackay, Queensland, Australia, killing 29.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Trans_Australia_Airlines_Flight_538\" title=\"Trans Australia Airlines Flight 538\">Trans Australia Airlines Flight 538</a> crashes near <a href=\"https://wikipedia.org/wiki/Mackay_Airport\" title=\"Mackay Airport\">Mackay Airport</a> in <a href=\"https://wikipedia.org/wiki/Mackay,_Queensland\" title=\"Mackay, Queensland\">Mackay, Queensland</a>, Australia, killing 29.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trans_Australia_Airlines_Flight_538\" title=\"Trans Australia Airlines Flight 538\">Trans Australia Airlines Flight 538</a> crashes near <a href=\"https://wikipedia.org/wiki/Mackay_Airport\" title=\"Mackay Airport\">Mackay Airport</a> in <a href=\"https://wikipedia.org/wiki/Mackay,_Queensland\" title=\"Mackay, Queensland\">Mackay, Queensland</a>, Australia, killing 29.", "links": [{"title": "Trans Australia Airlines Flight 538", "link": "https://wikipedia.org/wiki/Trans_Australia_Airlines_Flight_538"}, {"title": "Mackay Airport", "link": "https://wikipedia.org/wiki/Mackay_Airport"}, {"title": "Mackay, Queensland", "link": "https://wikipedia.org/wiki/Mackay,_Queensland"}]}, {"year": "1963", "text": "The Equal Pay Act of 1963, aimed at abolishing wage disparity based on sex, was signed into law by <PERSON> as part of his New Frontier Program.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Equal_Pay_Act_of_1963\" title=\"Equal Pay Act of 1963\">Equal Pay Act of 1963</a>, aimed at abolishing <a href=\"https://wikipedia.org/wiki/Gender_pay_gap\" title=\"Gender pay gap\">wage disparity based on sex</a>, was signed into law by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as part of his <a href=\"https://wikipedia.org/wiki/New_Frontier\" title=\"New Frontier\">New Frontier</a> Program.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Equal_Pay_Act_of_1963\" title=\"Equal Pay Act of 1963\">Equal Pay Act of 1963</a>, aimed at abolishing <a href=\"https://wikipedia.org/wiki/Gender_pay_gap\" title=\"Gender pay gap\">wage disparity based on sex</a>, was signed into law by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as part of his <a href=\"https://wikipedia.org/wiki/New_Frontier\" title=\"New Frontier\">New Frontier</a> Program.", "links": [{"title": "Equal Pay Act of 1963", "link": "https://wikipedia.org/wiki/Equal_Pay_Act_of_1963"}, {"title": "Gender pay gap", "link": "https://wikipedia.org/wiki/Gender_pay_gap"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "New Frontier", "link": "https://wikipedia.org/wiki/New_Frontier"}]}, {"year": "1964", "text": "United States Senate breaks a 75-day filibuster against the Civil Rights Act of 1964, leading to the bill's passage.", "html": "1964 - United States Senate breaks a 75-day <a href=\"https://wikipedia.org/wiki/Filibuster\" title=\"Filibuster\">filibuster</a> against the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1964\" title=\"Civil Rights Act of 1964\">Civil Rights Act of 1964</a>, leading to the bill's passage.", "no_year_html": "United States Senate breaks a 75-day <a href=\"https://wikipedia.org/wiki/Filibuster\" title=\"Filibuster\">filibuster</a> against the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1964\" title=\"Civil Rights Act of 1964\">Civil Rights Act of 1964</a>, leading to the bill's passage.", "links": [{"title": "Filibuster", "link": "https://wikipedia.org/wiki/Filibuster"}, {"title": "Civil Rights Act of 1964", "link": "https://wikipedia.org/wiki/Civil_Rights_Act_of_1964"}]}, {"year": "1967", "text": "The Six-Day War ends: Israel and Syria agree to a cease-fire.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a> ends: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> and <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> agree to a cease-fire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a> ends: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> and <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> agree to a cease-fire.", "links": [{"title": "Six-Day War", "link": "https://wikipedia.org/wiki/Six-Day_War"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "1977", "text": "<PERSON> escapes from Brushy Mountain State Penitentiary in Petros, Tennessee. He is recaptured three days later.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes from <a href=\"https://wikipedia.org/wiki/Brushy_Mountain_State_Penitentiary\" title=\"Brushy Mountain State Penitentiary\">Brushy Mountain State Penitentiary</a> in <a href=\"https://wikipedia.org/wiki/Petros,_Tennessee\" title=\"Petros, Tennessee\">Petros, Tennessee</a>. He is recaptured three days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes from <a href=\"https://wikipedia.org/wiki/Brushy_Mountain_State_Penitentiary\" title=\"Brushy Mountain State Penitentiary\">Brushy Mountain State Penitentiary</a> in <a href=\"https://wikipedia.org/wiki/Petros,_Tennessee\" title=\"Petros, Tennessee\">Petros, Tennessee</a>. He is recaptured three days later.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Brushy Mountain State Penitentiary", "link": "https://wikipedia.org/wiki/Brushy_Mountain_State_Penitentiary"}, {"title": "Petros, Tennessee", "link": "https://wikipedia.org/wiki/Petros,_Tennessee"}]}, {"year": "1980", "text": "The African National Congress in South Africa publishes a call to fight from their imprisoned leader <PERSON>.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">African National Congress</a> in South Africa publishes a call to fight from their imprisoned leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">African National Congress</a> in South Africa publishes a call to fight from their imprisoned leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "African National Congress", "link": "https://wikipedia.org/wiki/African_National_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}]}, {"year": "1982", "text": "Lebanon War: The Syrian Arab Army defeats the Israeli Defense Forces in the Battle of Sultan <PERSON>.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/1982_Lebanon_War\" title=\"1982 Lebanon War\">Lebanon War</a>: The Syrian Arab Army defeats the Israeli Defense Forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sultan_Yacoub\" title=\"Battle of Sultan Yacoub\">Battle of Sultan Ya<PERSON>ub</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1982_Lebanon_War\" title=\"1982 Lebanon War\">Lebanon War</a>: The Syrian Arab Army defeats the Israeli Defense Forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sultan_Yacoub\" title=\"Battle of Sultan Yacoub\">Battle of Sultan <PERSON>ub</a>.", "links": [{"title": "1982 Lebanon War", "link": "https://wikipedia.org/wiki/1982_Lebanon_War"}, {"title": "Battle of Sultan <PERSON>", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "June Democratic Struggle: The June Democratic Struggle starts in South Korea, and people protest against the government.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/June_Democratic_Struggle\" title=\"June Democratic Struggle\">June Democratic Struggle</a>: The June Democratic Struggle starts in South Korea, and people protest against the government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Democratic_Struggle\" title=\"June Democratic Struggle\">June Democratic Struggle</a>: The June Democratic Struggle starts in South Korea, and people protest against the government.", "links": [{"title": "June Democratic Struggle", "link": "https://wikipedia.org/wiki/June_Democratic_Struggle"}]}, {"year": "1990", "text": "British Airways Flight 5390 lands safely at Southampton Airport after a blowout in the cockpit causes the captain to be partially sucked from the cockpit. There are no fatalities.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/British_Airways_Flight_5390\" title=\"British Airways Flight 5390\">British Airways Flight 5390</a> lands safely at <a href=\"https://wikipedia.org/wiki/Southampton_Airport\" title=\"Southampton Airport\">Southampton Airport</a> after a blowout in the cockpit causes the captain to be partially sucked from the cockpit. There are no fatalities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Airways_Flight_5390\" title=\"British Airways Flight 5390\">British Airways Flight 5390</a> lands safely at <a href=\"https://wikipedia.org/wiki/Southampton_Airport\" title=\"Southampton Airport\">Southampton Airport</a> after a blowout in the cockpit causes the captain to be partially sucked from the cockpit. There are no fatalities.", "links": [{"title": "British Airways Flight 5390", "link": "https://wikipedia.org/wiki/British_Airways_Flight_5390"}, {"title": "Southampton Airport", "link": "https://wikipedia.org/wiki/Southampton_Airport"}]}, {"year": "1991", "text": "Eleven-year-old <PERSON><PERSON><PERSON> is kidnapped in South Lake Tahoe, California; she would remain a captive until 2009.", "html": "1991 - Eleven-year-old <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is kidnapped in <a href=\"https://wikipedia.org/wiki/South_Lake_Tahoe,_California\" title=\"South Lake Tahoe, California\">South Lake Tahoe, California</a>; she would remain a captive until 2009.", "no_year_html": "Eleven-year-old <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is kidnapped in <a href=\"https://wikipedia.org/wiki/South_Lake_Tahoe,_California\" title=\"South Lake Tahoe, California\">South Lake Tahoe, California</a>; she would remain a captive until 2009.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "South Lake Tahoe, California", "link": "https://wikipedia.org/wiki/South_Lake_Tahoe,_California"}]}, {"year": "1994", "text": "China conducts a nuclear test for DF-31 warhead at Area C (Beishan), Lop Nur, its prominence being due to the Cox Report.", "html": "1994 - China conducts a nuclear test for <a href=\"https://wikipedia.org/wiki/DF-31\" title=\"DF-31\">DF-31</a> warhead at Area C (Beishan), <a href=\"https://wikipedia.org/wiki/Lop_Nur\" title=\"Lop Nur\">Lop Nur</a>, its prominence being due to the <a href=\"https://wikipedia.org/wiki/Cox_Report\" title=\"Cox Report\">Cox Report</a>.", "no_year_html": "China conducts a nuclear test for <a href=\"https://wikipedia.org/wiki/DF-31\" title=\"DF-31\">DF-31</a> warhead at Area C (Beishan), <a href=\"https://wikipedia.org/wiki/Lop_Nur\" title=\"Lop Nur\">Lop Nur</a>, its prominence being due to the <a href=\"https://wikipedia.org/wiki/Cox_Report\" title=\"Cox Report\">Cox Report</a>.", "links": [{"title": "DF-31", "link": "https://wikipedia.org/wiki/DF-31"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lop_Nur"}, {"title": "Cox Report", "link": "https://wikipedia.org/wiki/Cox_Report"}]}, {"year": "1996", "text": "Peace talks begin in Northern Ireland without the participation of <PERSON><PERSON>.", "html": "1996 - Peace talks begin in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> without the participation of <a href=\"https://wikipedia.org/wiki/Sinn_F%C3%A9in\" title=\"Sinn Féin\">Sinn Féin</a>.", "no_year_html": "Peace talks begin in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> without the participation of <a href=\"https://wikipedia.org/wiki/Sinn_F%C3%A9in\" title=\"Sinn Féin\">Sinn Féin</a>.", "links": [{"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sinn_F%C3%A9in"}]}, {"year": "1997", "text": "Before fleeing his northern stronghold, Khmer Rouge leader <PERSON> orders the killing of his defense chief <PERSON> and 11 of <PERSON>'s family members.", "html": "1997 - Before fleeing his northern stronghold, <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\"><PERSON> Rouge</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the killing of his defense chief <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sen\"><PERSON></a> and 11 of <PERSON>'s family members.", "no_year_html": "Before fleeing his northern stronghold, <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\"><PERSON> Rouge</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the killing of his defense chief <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 11 of <PERSON>'s family members.", "links": [{"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}, {"title": "Pol Po<PERSON>", "link": "https://wikipedia.org/wiki/Pol_Pot"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "Kosovo War: NATO suspends its airstrikes after <PERSON><PERSON><PERSON><PERSON> agrees to withdraw Serbian forces from Kosovo.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Kosovo_War\" title=\"Kosovo War\">Kosovo War</a>: <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> suspends its <a href=\"https://wikipedia.org/wiki/Airstrike\" title=\"Airstrike\">airstrikes</a> after <a href=\"https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> agrees to withdraw <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbian</a> forces from <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kosovo_War\" title=\"Kosovo War\">Kosovo War</a>: <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> suspends its <a href=\"https://wikipedia.org/wiki/Airstrike\" title=\"Airstrike\">airstrikes</a> after <a href=\"https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> agrees to withdraw <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbian</a> forces from <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a>.", "links": [{"title": "Kosovo War", "link": "https://wikipedia.org/wiki/Kosovo_War"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "Airstrike", "link": "https://wikipedia.org/wiki/Airstrike"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Kosovo", "link": "https://wikipedia.org/wiki/Kosovo"}]}, {"year": "2001", "text": "Pope <PERSON> canonizes Lebanon's first female saint, Saint <PERSON><PERSON><PERSON>.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> canonizes <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>'s first female saint, <a href=\"https://wikipedia.org/wiki/Saint_Rafqa\" class=\"mw-redirect\" title=\"Saint Rafqa\">Saint <PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> canonizes <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>'s first female saint, <a href=\"https://wikipedia.org/wiki/Saint_Rafqa\" class=\"mw-redirect\" title=\"Saint Rafqa\">Saint <PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Rafqa"}]}, {"year": "2002", "text": "The first direct electronic communication experiment between the nervous systems of two humans is carried out by <PERSON> in the United Kingdom.", "html": "2002 - The first direct electronic communication experiment between the nervous systems of two humans is carried out by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the United Kingdom.", "no_year_html": "The first direct electronic communication experiment between the nervous systems of two humans is carried out by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the United Kingdom.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "The Spirit rover is launched, beginning NASA's Mars Exploration Rover mission.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Spirit_(rover)\" title=\"Spirit (rover)\"><i>Spirit</i> rover</a> is launched, beginning <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_Exploration_Rover\" title=\"Mars Exploration Rover\">Mars Exploration Rover</a> mission.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Spirit_(rover)\" title=\"Spirit (rover)\"><i>Spirit</i> rover</a> is launched, beginning <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_Exploration_Rover\" title=\"Mars Exploration Rover\">Mars Exploration Rover</a> mission.", "links": [{"title": "Spirit (rover)", "link": "https://wikipedia.org/wiki/<PERSON>_(rover)"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars Exploration Rover", "link": "https://wikipedia.org/wiki/Mars_Exploration_Rover"}]}, {"year": "2008", "text": "Sudan Airways Flight 109 crashes at Khartoum International Airport, killing 30 people.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Sudan_Airways_Flight_109\" title=\"Sudan Airways Flight 109\">Sudan Airways Flight 109</a> crashes at <a href=\"https://wikipedia.org/wiki/Khartoum_International_Airport\" title=\"Khartoum International Airport\">Khartoum International Airport</a>, killing 30 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sudan_Airways_Flight_109\" title=\"Sudan Airways Flight 109\">Sudan Airways Flight 109</a> crashes at <a href=\"https://wikipedia.org/wiki/Khartoum_International_Airport\" title=\"Khartoum International Airport\">Khartoum International Airport</a>, killing 30 people.", "links": [{"title": "Sudan Airways Flight 109", "link": "https://wikipedia.org/wiki/Sudan_Airways_Flight_109"}, {"title": "Khartoum International Airport", "link": "https://wikipedia.org/wiki/Khartoum_International_Airport"}]}, {"year": "2009", "text": "Eighty-eight year-old <PERSON> opens fire inside the United States Holocaust Memorial Museum and fatally shoots Museum Special Police Officer <PERSON>. Other security guards returned fire, wounding <PERSON>, who was apprehended.", "html": "2009 - Eighty-eight year-old <PERSON> opens fire inside the <a href=\"https://wikipedia.org/wiki/United_States_Holocaust_Memorial_Museum\" title=\"United States Holocaust Memorial Museum\">United States Holocaust Memorial Museum</a> and <a href=\"https://wikipedia.org/wiki/United_States_Holocaust_Memorial_Museum_shooting\" title=\"United States Holocaust Memorial Museum shooting\">fatally shoots</a> Museum Special Police Officer <PERSON>. Other security guards returned fire, wounding <PERSON>, who was apprehended.", "no_year_html": "Eighty-eight year-old <PERSON> opens fire inside the <a href=\"https://wikipedia.org/wiki/United_States_Holocaust_Memorial_Museum\" title=\"United States Holocaust Memorial Museum\">United States Holocaust Memorial Museum</a> and <a href=\"https://wikipedia.org/wiki/United_States_Holocaust_Memorial_Museum_shooting\" title=\"United States Holocaust Memorial Museum shooting\">fatally shoots</a> Museum Special Police Officer <PERSON>. Other security guards returned fire, wounding <PERSON>, who was apprehended.", "links": [{"title": "United States Holocaust Memorial Museum", "link": "https://wikipedia.org/wiki/United_States_Holocaust_Memorial_Museum"}, {"title": "United States Holocaust Memorial Museum shooting", "link": "https://wikipedia.org/wiki/United_States_Holocaust_Memorial_Museum_shooting"}]}, {"year": "2018", "text": "Opportunity rover, sends it last message back to earth. The mission was finally declared over on February 13, 2019.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Opportunity_(rover)\" title=\"Opportunity (rover)\"><i>Opportunity</i> rover</a>, sends it last message back to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">earth</a>. The mission was finally declared over on February 13, 2019.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Opportunity_(rover)\" title=\"Opportunity (rover)\"><i>Opportunity</i> rover</a>, sends it last message back to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">earth</a>. The mission was finally declared over on February 13, 2019.", "links": [{"title": "Opportunity (rover)", "link": "https://wikipedia.org/wiki/Opportunity_(rover)"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}]}, {"year": "2024", "text": "A plane crash in Malawi leaves 10 people dead, including the country's Vice President <PERSON><PERSON>.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_Chikangawa_Dornier_228_crash\" title=\"2024 Chikangawa Dornier 228 crash\">A plane crash</a> in <a href=\"https://wikipedia.org/wiki/Malawi\" title=\"Malawi\">Malawi</a> leaves 10 people dead, including the country's Vice President <a href=\"https://wikipedia.org/wiki/Saul<PERSON>_Chilima\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_Chikangawa_Dornier_228_crash\" title=\"2024 Chikangawa Dornier 228 crash\">A plane crash</a> in <a href=\"https://wikipedia.org/wiki/Malawi\" title=\"Malawi\">Malawi</a> leaves 10 people dead, including the country's Vice President <a href=\"https://wikipedia.org/wiki/Saul<PERSON>_Chilima\" title=\"<PERSON><PERSON> Chilim<PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "2024 Chikangawa Dornier 228 crash", "link": "https://wikipedia.org/wiki/2024_Chikangawa_Dornier_228_crash"}, {"title": "Malawi", "link": "https://wikipedia.org/wiki/Malawi"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saul<PERSON>_<PERSON>a"}]}], "Births": [{"year": "867", "text": "Emperor <PERSON><PERSON> of Japan (d. 931)", "html": "867 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (d. 931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (d. 931)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "940", "text": "<PERSON><PERSON>, Persian mathematician and astronomer (d. 998)", "html": "940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON>%27_Buzjani\" class=\"mw-redirect\" title=\"<PERSON> al-Wafa' Buzjani\"><PERSON>Waf<PERSON> Buzjan<PERSON></a>, Persian <a href=\"https://wikipedia.org/wiki/Mathematics_in_medieval_Islam\" class=\"mw-redirect\" title=\"Mathematics in medieval Islam\">mathematician</a> and <a href=\"https://wikipedia.org/wiki/Astronomy_in_the_medieval_Islamic_world\" title=\"Astronomy in the medieval Islamic world\">astronomer</a> (d. 998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>%27_Buzjani\" class=\"mw-redirect\" title=\"<PERSON> al-Wafa' Buzjani\"><PERSON>Wafa' Buzjan<PERSON></a>, Persian <a href=\"https://wikipedia.org/wiki/Mathematics_in_medieval_Islam\" class=\"mw-redirect\" title=\"Mathematics in medieval Islam\">mathematician</a> and <a href=\"https://wikipedia.org/wiki/Astronomy_in_the_medieval_Islamic_world\" title=\"Astronomy in the medieval Islamic world\">astronomer</a> (d. 998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_al-Wafa%27_<PERSON>uzjani"}, {"title": "Mathematics in medieval Islam", "link": "https://wikipedia.org/wiki/Mathematics_in_medieval_Islam"}, {"title": "Astronomy in the medieval Islamic world", "link": "https://wikipedia.org/wiki/Astronomy_in_the_medieval_Islamic_world"}]}, {"year": "1213", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Persian poet and philosopher (d. 1289)", "html": "1213 - <a href=\"https://wikipedia.org/wiki/Fakhr-al-Din_Iraqi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>-<PERSON>-Din Iraqi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian poet and philosopher (d. 1289)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fakhr-al-Din_Iraqi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>-<PERSON>-Din <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian poet and philosopher (d. 1289)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>-Din_<PERSON>"}]}, {"year": "1465", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian statesman and jurist (d. 1530)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/Mercuri<PERSON>_Gattinara\" class=\"mw-redirect\" title=\"Me<PERSON><PERSON><PERSON> Gatt<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian statesman and jurist (d. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON>uri<PERSON>_<PERSON>ra\" class=\"mw-redirect\" title=\"Me<PERSON>uri<PERSON> Gattinara\"><PERSON><PERSON><PERSON><PERSON></a>, Italian statesman and jurist (d. 1530)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Me<PERSON><PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1513", "text": "<PERSON>, Duke of Montpensier (1561-1582) (d. 1582)", "html": "1513 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Montpensier\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Montpensier\"><PERSON>, Duke of Montpensier</a> (1561-1582) (d. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Montpensier\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Montpensier\"><PERSON>, Duke of Montpensier</a> (1561-1582) (d. 1582)", "links": [{"title": "<PERSON>, Duke of Montpensier", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_Mont<PERSON>sie<PERSON>"}]}, {"year": "1557", "text": "<PERSON><PERSON><PERSON>, Italian painter (d. 1622)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1622)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1632", "text": "<PERSON><PERSON><PERSON>, French bishop and author (d. 1710)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/Esprit_Fl%C3%A9chier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French bishop and author (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esprit_Fl%C3%A9chier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French bishop and author (d. 1710)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esprit_Fl%C3%A9chier"}]}, {"year": "1688", "text": "<PERSON>, claimant to the English and Scottish throne (d. 1766)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, claimant to the English and Scottish throne (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, claimant to the English and Scottish throne (d. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "Princess <PERSON> of Great Britain (d. 1757)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/Princess_Caroline_of_Great_Britain\" title=\"Princess <PERSON> of Great Britain\">Princess <PERSON> of Great Britain</a> (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Caroline_of_Great_Britain\" title=\"Princess <PERSON> of Great Britain\">Princess <PERSON> of Great Britain</a> (d. 1757)", "links": [{"title": "Princess <PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/Princess_Caroline_of_Great_Britain"}]}, {"year": "1716", "text": "<PERSON>, Swedish physician and explorer (d. 1784)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physician and explorer (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physician and explorer (d. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1753", "text": "<PERSON>, American physician and politician, 12th Governor of Massachusetts (d. 1825)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1804", "text": "<PERSON>, German ornithologist and herpetologist (d. 1884)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ornithologist and herpetologist (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ornithologist and herpetologist (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1819", "text": "<PERSON><PERSON>, French-Swiss painter and sculptor (d. 1877)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Swiss painter and sculptor (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Swiss painter and sculptor (d. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON><PERSON>, Norwegian-American skier (d. 1897)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/Sondre_<PERSON>heim\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American skier (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sondre_<PERSON>heim\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American skier (d. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sondre_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, English poet and journalist (d. 1904)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and journalist (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and journalist (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON>, German engineer (d. 1891)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German engineer (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German engineer (d. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, American lieutenant and politician (d. 1920)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, American educator and politician (d. 1930)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Danish lawyer and politician, 19th Prime Minister of Denmark (d. 1912)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Ludvig_<PERSON>-Ledreborg\" title=\"Ludvig Holstein-Ledreborg\"><PERSON>d<PERSON><PERSON></a>, Danish lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludvig_<PERSON>-Ledreborg\" title=\"Ludvig <PERSON>-Ledreborg\">Ludvi<PERSON></a>, Danish lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (d. 1912)", "links": [{"title": "Ludvig Holstein-Ledreborg", "link": "https://wikipedia.org/wiki/Ludvig_Holstein-Ledreborg"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "1840", "text": "<PERSON>, Danish painter (d. 1920)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, Austrian composer and conductor (d. 1900)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, American attorney (d. 1919)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Irish feminist writer (d. 1943)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sarah Grand\"><PERSON></a>, Irish feminist writer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sarah Grand\"><PERSON></a>, Irish feminist writer (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, Swedish-Russian businessman (d. 1932)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Emanuel_<PERSON>\" title=\"Emanuel Nobel\"><PERSON></a>, Swedish-Russian businessman (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emanuel_Nobel\" title=\"Emanuel Nobel\"><PERSON></a>, Swedish-Russian businessman (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "Mrs. <PERSON>, American actress (d. 1937)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Mrs._<PERSON>_<PERSON>\" title=\"Mrs. <PERSON>\">Mrs. <PERSON></a>, American actress (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mrs._<PERSON>_<PERSON>\" title=\"Mrs. <PERSON>\">Mrs. <PERSON></a>, American actress (d. 1937)", "links": [{"title": "Mrs. <PERSON>", "link": "https://wikipedia.org/wiki/Mrs._<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Dutch author and poet (d. 1923)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON>, Scottish architect (d. 1960)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>mper\"><PERSON><PERSON></a>, Scottish architect (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>mper\"><PERSON><PERSON></a>, Scottish architect (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American physician and explorer (d. 1940)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and explorer (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and explorer (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Nahua-Mexican evangelizer, theologian, and religious founder (d. 1961)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Margarito_Bautista\" title=\"Mar<PERSON>ito Bautista\"><PERSON><PERSON><PERSON></a>, Nahua-Mexican evangelizer, theologian, and religious founder (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Margarito_Ba<PERSON>sta\" title=\"Margarito Bautista\"><PERSON><PERSON><PERSON></a>, Nahua-Mexican evangelizer, theologian, and religious founder (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Margarito_Bautista"}]}, {"year": "1880", "text": "<PERSON>, French painter and sculptor (d. 1954)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Derain"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Norwegian Esperantist and teacher (d. 1969)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Nils_%C3%98kland\" title=\"<PERSON><PERSON>nd\"><PERSON><PERSON></a>, Norwegian Esperantist and teacher (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nils_%C3%98kland\" title=\"<PERSON><PERSON> Økland\"><PERSON><PERSON></a>, Norwegian Esperantist and teacher (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nils_%C3%98kland"}]}, {"year": "1884", "text": "<PERSON>, English captain (d. 1917)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Leone <PERSON>tus <PERSON>\"><PERSON></a>, English captain (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Leone Sextus <PERSON>\"><PERSON></a>, English captain (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leone_Sextus_Tollemache"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Japanese actor and producer (d. 1973)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Sessue_<PERSON>\" title=\"<PERSON>ssue <PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and producer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sessue_<PERSON>\" title=\"Sessue <PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and producer (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sessue_<PERSON>wa"}]}, {"year": "1891", "text": "<PERSON>, Swiss-American songwriter (d. 1945)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American songwriter (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American songwriter (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, American actress (d. 1952)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1897", "text": "Grand Duchess <PERSON> of Russia (d. 1918)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1918)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1898", "text": "Princess <PERSON><PERSON><PERSON> of Anhalt (d. 1983)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>-<PERSON>_of_Anhalt\" title=\"Princess <PERSON><PERSON> of Anhalt\">Princess <PERSON><PERSON> of Anhalt</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>-<PERSON>_of_Anhalt\" title=\"Princess <PERSON><PERSON> of Anhalt\">Princess <PERSON><PERSON> of Anhalt</a> (d. 1983)", "links": [{"title": "Princess <PERSON><PERSON><PERSON> of Anhalt", "link": "https://wikipedia.org/wiki/Princess_<PERSON>-<PERSON>_<PERSON>_An<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Polish racing driver (d. 1933)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish racing driver (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish racing driver (d. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Austrian-American composer (d. 1988)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American composer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American composer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Chinese architect and poet (d. 1955)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese architect and poet (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese architect and poet (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American painter and critic (d. 1975)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Fairfield_Porter\" title=\"<PERSON> Porter\"><PERSON></a>, American painter and critic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fairfield_Porter\" title=\"Fairfield Porter\"><PERSON></a>, American painter and critic (d. 1975)", "links": [{"title": "Fairfield Porter", "link": "https://wikipedia.org/wiki/Fairfield_Porter"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American jazz trombonist (d. 1985)[n 1]", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz trombonist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz trombonist (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Australian soldier and businessman (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and businessman (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and businessman (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American baseball player and manager (d. 1958)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON> <PERSON>, American singer-songwriter and guitarist (d. 1976)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Howlin%27_<PERSON>\" title=\"Howlin' <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Howlin%27_<PERSON>\" title=\"Howlin' <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "links": [{"title": "Howlin' Wolf", "link": "https://wikipedia.org/wiki/How<PERSON>%27_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American harpsichord player and musicologist (d. 1984)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and musicologist (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and musicologist (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1911", "text": "<PERSON>, English playwright and screenwriter (d. 1977)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Canadian lawyer and politician, 11th Premier of Quebec (d. 1980)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Russian pianist and composer (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, German-Israeli biochemist and academic (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli biochemist and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli biochemist and academic (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Turkish poet and playwright (d. 1988)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Oktay_R%C4%B1<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Turkish poet and playwright (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oktay_R%C4%B1<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Turkish poet and playwright (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oktay_R%C4%B1fa<PERSON>_<PERSON><PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Canadian-American novelist, essayist and short story writer, Nobel Prize laureate (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American novelist, essayist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American novelist, essayist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Turkish author (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish author (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Per<PERSON>_<PERSON>lal"}]}, {"year": "1916", "text": "<PERSON>, American entrepreneur, founded Dunkin' Donuts (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, founded <a href=\"https://wikipedia.org/wiki/Dunkin%27_Donuts\" title=\"Dunkin' Donuts\">Dunkin' Donuts</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, founded <a href=\"https://wikipedia.org/wiki/Dunkin%27_Donuts\" title=\"Dunkin' Donuts\">Dunkin' Donuts</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Dunkin' Donuts", "link": "https://wikipedia.org/wiki/Dunkin%27_Donuts"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, French singer and actress (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer and actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer and actress (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English-Canadian actor and director (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor and director (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor and director (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Palestinian physician and politician (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian physician and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hai<PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian physician and politician (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Irish footballer, rugby player, and physician (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Flanagan\" title=\"<PERSON>\"><PERSON></a>, Irish footballer, rugby player, and physician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Flanagan\" title=\"<PERSON>\"><PERSON></a>, Irish footballer, rugby player, and physician (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Flanagan"}]}, {"year": "1921", "text": "<PERSON>, Duke of Edinburgh (d. 2021)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh\" title=\"Prince <PERSON>, Duke of Edinburgh\">Prince <PERSON>, Duke of Edinburgh</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh\" title=\"Prince <PERSON>, Duke of Edinburgh\">Prince <PERSON>, Duke of Edinburgh</a> (d. 2021)", "links": [{"title": "<PERSON>, Duke of Edinburgh", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh"}]}, {"year": "1921", "text": "<PERSON>, French cyclist (d. 1980)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress and singer (d. 1969)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, South African-Australian actor (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Australian rugby league player (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Canadian singer-songwriter and guitarist (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Czech-English captain, publisher, and politician (d. 1991)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English captain, publisher, and politician (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English captain, publisher, and politician (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, German mathematician, computer scientist, and academic (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, computer scientist, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, computer scientist, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian ice hockey player (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American historian, author, and journalist (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and journalist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and journalist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American novelist and short-story writer (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short-story writer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short-story writer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Italian conductor (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English actor, screenwriter and film director (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter and film director (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter and film director (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Brazilian zoologist (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian zoologist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian zoologist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian footballer, coach, and manager (d. 2002)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer, coach, and manager (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer, coach, and manager (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_<PERSON><PERSON>a"}]}, {"year": "1927", "text": "<PERSON>, Chinese politician, 29th Vice Premier of the Republic of China (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ng\" title=\"<PERSON>-kang\"><PERSON></a>, Chinese politician, 29th <a href=\"https://wikipedia.org/wiki/List_of_vice_premiers_of_the_Republic_of_China\" class=\"mw-redirect\" title=\"List of vice premiers of the Republic of China\">Vice Premier of the Republic of China</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ng\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 29th <a href=\"https://wikipedia.org/wiki/List_of_vice_premiers_of_the_Republic_of_China\" class=\"mw-redirect\" title=\"List of vice premiers of the Republic of China\">Vice Premier of the Republic of China</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-kang"}, {"title": "List of vice premiers of the Republic of China", "link": "https://wikipedia.org/wiki/List_of_vice_premiers_of_the_Republic_of_China"}]}, {"year": "1927", "text": "<PERSON>, American basketball player and coach (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1927)\" title=\"<PERSON> (basketball, born 1927)\"><PERSON></a>, American basketball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1927)\" title=\"<PERSON> (basketball, born 1927)\"><PERSON></a>, American basketball player and coach (d. 2013)", "links": [{"title": "<PERSON> (basketball, born 1927)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1927)"}]}, {"year": "1927", "text": "<PERSON>, American astrophysicist and academic (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American author and illustrator (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American general, pilot, and astronaut (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, pilot, and astronaut (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, pilot, and astronaut (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian farmer and politician, 42nd Australian Minister for Defence", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and politician, 42nd <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and politician, 42nd <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1929", "text": "<PERSON>, <PERSON> of Blackburn, British Labour Party politician (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Blackburn\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON> of Blackburn\"><PERSON>, Baron <PERSON> of Blackburn</a>, British Labour Party politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Blackburn\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON> of Blackburn\"><PERSON>, Baron <PERSON> of Blackburn</a>, British Labour Party politician (d. 2016)", "links": [{"title": "<PERSON>, <PERSON> of Blackburn", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>_Blackburn"}]}, {"year": "1929", "text": "<PERSON><PERSON> <PERSON><PERSON>, American biologist, author, and academic (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American biologist, author, and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American biologist, author, and academic (d. 2021)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Czech-American author and Holocaust survivor", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Aranka_Siegal\" title=\"Aranka Siegal\"><PERSON><PERSON></a>, Czech-American author and <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a> survivor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aranka_Siegal\" title=\"Aranka Siegal\"><PERSON><PERSON></a>, Czech-American author and <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a> survivor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aranka_Siegal"}, {"title": "Holocaust", "link": "https://wikipedia.org/wiki/Holocaust"}]}, {"year": "1930", "text": "<PERSON>, American baseball and football player (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball and football player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball and football player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Chinese politician, 8th Mayor of Beijing (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 8th <a href=\"https://wikipedia.org/wiki/Mayor_of_Beijing\" title=\"Mayor of Beijing\">Mayor of Beijing</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 8th <a href=\"https://wikipedia.org/wiki/Mayor_of_Beijing\" title=\"Mayor of Beijing\">Mayor of Beijing</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}, {"title": "Mayor of Beijing", "link": "https://wikipedia.org/wiki/Mayor_of_Beijing"}]}, {"year": "1931", "text": "<PERSON>, English academic and diplomat, British Ambassador to Russia", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Russia\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Russia\">British Ambassador to Russia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Russia\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Russia\">British Ambassador to Russia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}, {"title": "List of Ambassadors of the United Kingdom to Russia", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Russia"}]}, {"year": "1931", "text": "<PERSON>, Brazilian singer-songwriter and guitarist (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and guitarist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and guitarist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>o"}]}, {"year": "1932", "text": "<PERSON>, French mathematician and academic", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, French mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, French mathematician and academic", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_(mathematician)"}]}, {"year": "1933", "text": "<PERSON>, American football player and coach (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English lawyer and judge", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_(judge)"}]}, {"year": "1934", "text": "<PERSON>, Baron <PERSON>, English politician (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician (d. 2023)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English racing driver (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Vic_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vic_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_Elford"}]}, {"year": "1935", "text": "<PERSON>, Chinese self-taught mathematician (d. 1983)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Chinese self-taught mathematician (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Chinese self-taught mathematician (d. 1983)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(mathematician)"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Japanese author and illustrator (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Indian businessman and politician (d. 2022)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and politician (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Belgian-Polish singer-songwriter and actress (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Violetta_Villas\" title=\"Violetta Villas\"><PERSON><PERSON></a>, Belgian-Polish singer-songwriter and actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Violetta_Villas\" title=\"Violetta Villas\"><PERSON><PERSON></a>, Belgian-Polish singer-songwriter and actress (d. 2011)", "links": [{"title": "Violetta Villas", "link": "https://wikipedia.org/wiki/Violetta_Villas"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian mathematician and academic (d. 2009)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Vasanti_N._Bhat-Nayak\" title=\"Vasanti N. Bhat-Nayak\">V<PERSON><PERSON> <PERSON></a>, Indian mathematician and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasanti_<PERSON>._Bhat-Nayak\" title=\"Vasanti N. Bhat-Nayak\"><PERSON><PERSON><PERSON> <PERSON></a>, Indian mathematician and academic (d. 2009)", "links": [{"title": "Vasanti N. Bhat-Nayak", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON><PERSON>_B<PERSON>-Nayak"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American-New Zealand meteorologist (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-New Zealand meteorologist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-New Zealand meteorologist (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English drummer (d. 1994)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English drummer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English drummer (d. 1994)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>(drummer)"}]}, {"year": "1941", "text": "<PERSON>, American drummer (d. 2018)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, German actor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Prochnow\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Prochnow\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_Prochnow"}]}, {"year": "1941", "text": "<PERSON>, Australian racing driver", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1942", "text": "<PERSON>, Northern Irish journalist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, French singer and actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Goya\"><PERSON><PERSON></a>, French singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Goya\"><PERSON><PERSON></a>, French singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Goya"}]}, {"year": "1942", "text": "<PERSON>, <PERSON>, Scottish lawyer and judge", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "links": [{"title": "<PERSON>, Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English journalist and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Icelandic politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Sigr%C3%AD%C3%B0ur_J%C3%B3hannesd%C3%B3ttir\" title=\"Sig<PERSON><PERSON><PERSON><PERSON>dótti<PERSON>\"><PERSON>g<PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sigr%C3%AD%C3%B0ur_J%C3%B3hannesd%C3%B3ttir\" title=\"Sig<PERSON><PERSON><PERSON><PERSON> J<PERSON>hannesdóttir\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigr%C3%AD%C3%B0ur_J%C3%B3hannesd%C3%B3ttir"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish-Israeli weightlifter (d. 1972)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'ev <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Polish-Israeli weightlifter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'ev <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Polish-Israeli weightlifter (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ze%27ev_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English rock bass player (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English rock bass player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English rock bass player (d. 2022)", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)"}]}, {"year": "1947", "text": "<PERSON>, Canadian businessman, lawyer, and jurist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, lawyer, and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, lawyer, and jurist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American baseball player and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English air marshal", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>(RAF_officer)"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Dominican-American baseball player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/El%C3%ADas_Sosa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El%C3%ADas_Sosa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/El%C3%ADas_Sosa"}]}, {"year": "1951", "text": "<PERSON>, American football player and sportscaster", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Dan_Fouts\" title=\"Dan Fouts\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dan_Fouts\" title=\"Dan Fouts\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "Dan Fouts", "link": "https://wikipedia.org/wiki/Dan_Fouts"}]}, {"year": "1951", "text": "<PERSON>, Australian boxer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, Australian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, Australian boxer", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, German pentathlete", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>ak\"><PERSON><PERSON><PERSON><PERSON></a>, German pentathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>urg<PERSON><PERSON> Pollak\"><PERSON><PERSON><PERSON><PERSON></a>, German pentathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ak"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American author (d. 2010)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English painter and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American lawyer and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Irish director and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American artist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian journalist and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Canadian businesswoman", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rich Hall\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rich_Hall\" title=\"Rich Hall\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rich_Hall"}]}, {"year": "1955", "text": "<PERSON>, German theologian and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, English architectural historian (d. 2023)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English architectural historian (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English architectural historian (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Japanese game designer and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Suzuki\"><PERSON></a>, Japanese game designer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yu Suzuki\"><PERSON></a>, Japanese game designer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Italian footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American heavy metal guitarist, songwriter, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> C\"><PERSON></a>, American heavy metal guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> C\"><PERSON></a>, American heavy metal guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American lawyer and politician, 54th Governor of New York", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Indian film actor and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nan<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actor and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and musician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Deal\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Deal\" title=\"Kim Deal\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, English singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Priest\" title=\"<PERSON><PERSON> Priest\"><PERSON><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Priest\" title=\"<PERSON><PERSON> Priest\"><PERSON><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress, singer and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Brazilian poet and author (d. 1982)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet and author (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ode Herzer\"><PERSON></a>, Brazilian poet and author (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bigode_Herzer"}]}, {"year": "1962", "text": "<PERSON>, Hong Kong singer-songwriter and guitarist (d. 1993)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter and guitarist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter and guitarist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Hong Kong American character actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Hong Kong American character actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Hong Kong American character actor", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American lawyer and politician, 26th Governor of Oklahoma", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Oklahoma\" title=\"Governor of Oklahoma\">Governor of Oklahoma</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Oklahoma\" title=\"Governor of Oklahoma\">Governor of Oklahoma</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oklahoma", "link": "https://wikipedia.org/wiki/Governor_of_Oklahoma"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, German computer scientist and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German computer scientist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English model, actress, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model, actress, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model, actress, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American alternative rock musician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joey <PERSON>\"><PERSON></a>, American alternative rock musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joey Santiago\"><PERSON></a>, American alternative rock musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1967", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American rapper (d. 1995)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper (d. 1995)", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>(rapper)"}]}, {"year": "1967", "text": "<PERSON>, Canadian nurse and serial killer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian nurse and serial killer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian nurse and serial killer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American comedian and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American football player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1969", "text": "<PERSON>, Australian rugby league player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Norwegian footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American journalist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Snow\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Japanese game designer, director, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese game designer, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese game designer, director, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Filipino journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newscaster)\" title=\"<PERSON> (newscaster)\"><PERSON></a>, Filipino journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(newscaster)\" title=\"<PERSON> (newscaster)\"><PERSON></a>, Filipino journalist", "links": [{"title": "<PERSON> (newscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newscaster)"}]}, {"year": "1970", "text": "<PERSON>, Australian rugby league player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Where<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Whereat\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Whereat"}]}, {"year": "1970", "text": "<PERSON>, Welsh sport shooter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sport shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sport shooter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American journalist and politician, 55th Governor of Louisiana", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}]}, {"year": "1971", "text": "<PERSON>, French footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian radio and television host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sandilands"}]}, {"year": "1972", "text": "<PERSON>, American director and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Macedonian politician, Prime Minister of the Republic of Macedonia", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Rad<PERSON><PERSON>_%C5%A0ekerinska\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Macedonian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Macedonia\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Macedonia\">Prime Minister of the Republic of Macedonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON><PERSON><PERSON>_%C5%A0ekerinska\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Macedonian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Macedonia\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Macedonia\">Prime Minister of the Republic of Macedonia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radmila_%C5%A0ekerinska"}, {"title": "Prime Minister of the Republic of Macedonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Macedonia"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Indian-American businessman", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sundar_Pichai\" title=\"Sundar Pichai\"><PERSON><PERSON></a>, Indian-American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sundar_Pichai\" title=\"Sundar Pichai\"><PERSON><PERSON></a>, Indian-American businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sundar_Pichai"}]}, {"year": "1972", "text": "<PERSON>, Sri Lankan cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Flesh-n-Bone\" title=\"Flesh-n-Bone\"><PERSON>lesh-n-<PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flesh-n-Bone\" title=\"Flesh-n-Bone\"><PERSON>lesh-n-<PERSON></a>, American rapper and actor", "links": [{"title": "Flesh-n-Bone", "link": "https://wikipedia.org/wiki/Flesh-n-Bone"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Reese\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American screenwriter, director, film and television producer, and LGBT rights activist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, director, film and television producer, and LGBT rights activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, director, film and television producer, and LGBT rights activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Danish footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll"}]}, {"year": "1976", "text": "<PERSON>, Dutch politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Iranian martial artist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON> (Nergal), Polish singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (<PERSON><PERSON><PERSON>), Polish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (<PERSON><PERSON><PERSON>), Polish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Indian-American mathematician and computer scientist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American mathematician and computer scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American mathematician and computer scientist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Subhas<PERSON>_<PERSON>hot"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian ice dancer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ice dancer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ice dancer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lou<PERSON>s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1980", "text": "<PERSON>, American actress and voice actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Matuzal%C3%A9m\" title=\"<PERSON>uzalé<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matuzal%C3%A9m\" title=\"<PERSON>uzalé<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matuzal%C3%A9m"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player (d. 2013)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Italian bodybuilder (d. 2013)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian bodybuilder (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian bodybuilder (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English racing driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, South African cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Russian sprinter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1982", "text": "<PERSON>, American figure skater", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ski"}]}, {"year": "1982", "text": "Princess <PERSON>, Duchess of Hälsingland and Gästrikland", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_H%C3%A4lsingland_and_G%C3%A4strikland\" title=\"Princess <PERSON>, Duchess of Hälsingland and Gästrikland\">Princess <PERSON>, Duchess of Hälsingland and Gästrikland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_<PERSON>%C3%A4lsingland_and_G%C3%A4strikland\" title=\"Princess <PERSON>, Duchess of Hälsingland and Gästrikland\">Princess <PERSON>, Duchess of Hälsingland and Gästrikland</a>", "links": [{"title": "Princess <PERSON>, Duchess of Hälsingland and Gästrikland", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_<PERSON>%C3%A4lsingland_and_G%C3%A4strikland"}]}, {"year": "1982", "text": "<PERSON>, Brazilian ballerina and journalist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Ana_L%C3%BAcia_Souza\" title=\"<PERSON> L<PERSON> Souza\"><PERSON></a>, Brazilian ballerina and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana_L%C3%BAcia_Souza\" title=\"<PERSON> Lúcia Souza\"><PERSON></a>, Brazilian ballerina and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_L%C3%BAcia_Souza"}]}, {"year": "1983", "text": "<PERSON>, Barbadian athlete", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Barbadian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Barbadian athlete", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1983", "text": "<PERSON>, American football player (d. 2022)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_III\" title=\"<PERSON> III\"><PERSON></a>, American football player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Marion Barber III\"><PERSON></a>, American football player (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American actress and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> So<PERSON>ki\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sobieski\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Swiss footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German sprinter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Belgian martial artist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Irish rower", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, Irish rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, Irish rower", "links": [{"title": "<PERSON> (rower)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Hong Kong-American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Estonian tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Luxembourger cyclist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Dominican baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Al_Alburquerque\" title=\"Al Alburquerque\">Al Alburquerque</a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Alburquerque\" title=\"Al Alburquerque\">Al Alburquerque</a>, Dominican baseball player", "links": [{"title": "Al Alburquerque", "link": "https://wikipedia.org/wiki/Al_Alburquerque"}]}, {"year": "1986", "text": "<PERSON>, Italian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German-Austrian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Nigerian-American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian-American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, South African cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_cricketer)\" title=\"<PERSON> (South African cricketer)\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_cricketer)\" title=\"<PERSON> (South African cricketer)\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON> (South African cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_African_cricketer)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Gambian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>apha_Carayol\" title=\"Mustapha Carayol\"><PERSON><PERSON><PERSON></a>, Gambian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>aph<PERSON>_<PERSON>\" title=\"<PERSON>apha Cara<PERSON>l\"><PERSON><PERSON><PERSON></a>, Gambian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>apha_<PERSON>yol"}]}, {"year": "1989", "text": "<PERSON>, Romanian singer-songwriter, dancer, and model", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian singer-songwriter, dancer, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stan\"><PERSON></a>, Romanian singer-songwriter, dancer, and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American figure skater", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Alex<PERSON>_Scimeca_Knierim\" class=\"mw-redirect\" title=\"Alexa Scimeca Knierim\"><PERSON><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alexa_Scimeca_Knierim\" class=\"mw-redirect\" title=\"Alexa Scimeca Knierim\"><PERSON><PERSON></a>, American figure skater", "links": [{"title": "Alexa Scimeca K<PERSON>im", "link": "https://wikipedia.org/wiki/Alexa_Scimeca_K<PERSON>im"}]}, {"year": "1992", "text": "<PERSON>, American model and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Chinese singer and actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Chinese_entertainer)\" title=\"<PERSON> (Chinese entertainer)\"><PERSON></a>, Chinese singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Chinese_entertainer)\" title=\"<PERSON> (Chinese entertainer)\"><PERSON></a>, Chinese singer and actor", "links": [{"title": "<PERSON> (Chinese entertainer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Chinese_entertainer)"}]}, {"year": "1997", "text": "<PERSON>, Hong Kong foil fencer, 2020 Olympic champion", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-long\" class=\"mw-redirect\" title=\"<PERSON> Ka-long\"><PERSON></a>, Hong Kong foil fencer, <a href=\"https://wikipedia.org/wiki/Fencing_at_the_2020_Summer_Olympics_%E2%80%93_Men%27s_foil\" title=\"Fencing at the 2020 Summer Olympics - Men's foil\">2020 Olympic</a> champion", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Ka-long\"><PERSON></a>, Hong Kong foil fencer, <a href=\"https://wikipedia.org/wiki/Fencing_at_the_2020_Summer_Olympics_%E2%80%93_Men%27s_foil\" title=\"Fencing at the 2020 Summer Olympics - Men's foil\">2020 Olympic</a> champion", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Fencing at the 2020 Summer Olympics - Men's foil", "link": "https://wikipedia.org/wiki/Fencing_at_the_2020_Summer_Olympics_%E2%80%93_Men%27s_foil"}]}, {"year": "1998", "text": "<PERSON>, Australian rugby league player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Portuguese footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Le%C3%A3o"}]}, {"year": "1999", "text": "<PERSON>, Belgian singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Belgian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Belgian singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "2001", "text": "<PERSON>,  Saint Lucian medal-winning 100m sprinter at the 2024 Summer Olympics.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saint Lucian medal-winning 100m sprinter at the 2024 Summer Olympics.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saint Lucian medal-winning 100m sprinter at the 2024 Summer Olympics.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "38", "text": "<PERSON>, Roman sister of <PERSON><PERSON><PERSON> (b. 16 AD)", "html": "38 - AD 38 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman sister of <a href=\"https://wikipedia.org/wiki/Caligula\" title=\"<PERSON>ig<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 16 AD)", "no_year_html": "AD 38 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman sister of <a href=\"https://wikipedia.org/wiki/Caligula\" title=\"<PERSON>ig<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 16 AD)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Caligula"}]}, {"year": "223", "text": "<PERSON>, Chinese emperor (b. 161)", "html": "223 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Be<PERSON>\"><PERSON></a>, Chinese emperor (b. 161)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Be<PERSON>\"><PERSON></a>, Chinese emperor (b. 161)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "779", "text": "Emperor <PERSON><PERSON> of Tang (b. 727)", "html": "779 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON> of Tang</a> (b. 727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON> of Tang</a> (b. 727)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}]}, {"year": "754", "text": "<PERSON><PERSON>, Muslim caliph (b. 721)", "html": "754 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"As-<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Muslim caliph (b. 721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"As-<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Muslim caliph (b. 721)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>"}]}, {"year": "871", "text": "<PERSON><PERSON>, Frankish nobleman", "html": "871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Troyes\" title=\"<PERSON><PERSON> <PERSON>, Count of Troyes\">Odo <PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Troyes\" title=\"<PERSON><PERSON> <PERSON>, Count of Troyes\">Odo <PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "links": [{"title": "<PERSON><PERSON>, Count of Troyes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_<PERSON>"}, {"title": "Nobility", "link": "https://wikipedia.org/wiki/Nobility"}]}, {"year": "903", "text": "<PERSON>, Chinese warlord", "html": "903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cheng Rui\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Warlord\" title=\"Warlord\">warlord</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Warlord\" title=\"Warlord\">warlord</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Warlord", "link": "https://wikipedia.org/wiki/Warlord"}]}, {"year": "932", "text": "<PERSON>, Chinese general", "html": "932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "942", "text": "<PERSON>, emperor of Southern Han (b. 889)", "html": "942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a>, emperor of Southern Han (b. 889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a>, emperor of Southern Han (b. 889)", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)"}]}, {"year": "1075", "text": "<PERSON>, Margrave of Austria (b. 1027)", "html": "1075 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON>, Margrave of Austria\"><PERSON>, Margrave of Austria</a> (b. 1027)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON>, Margrave of Austria\"><PERSON>, Margrave of Austria</a> (b. 1027)", "links": [{"title": "<PERSON>, Margrave of Austria", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Austria"}]}, {"year": "1141", "text": "<PERSON><PERSON><PERSON> of Northeim (b. 1087)", "html": "1141 - <a href=\"https://wikipedia.org/wiki/Richenza_of_Northeim\" title=\"Richenza of Northeim\">Richenza of Northeim</a> (b. 1087)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Richenza_of_Northeim\" title=\"Richenza of Northeim\">Richenza of Northeim</a> (b. 1087)", "links": [{"title": "Richenza of Northeim", "link": "https://wikipedia.org/wiki/Richenza_of_Northeim"}]}, {"year": "1190", "text": "<PERSON>, Holy Roman Emperor (b. 1122)", "html": "1190 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1122)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1122)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1261", "text": "<PERSON> of Brandenburg, Duchess of Brunswick-Lüneburg (b. 1210)", "html": "1261 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brandenburg,_Duchess_of_Brunswick-L%C3%BCneburg\" title=\"Matilda of Brandenburg, Duchess of Brunswick-Lüneburg\"><PERSON> of Brandenburg, Duchess of Brunswick-Lüneburg</a> (b. 1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matilda_of_Brandenburg,_Duchess_of_Brunswick-L%C3%BCneburg\" title=\"Matilda of Brandenburg, Duchess of Brunswick-Lüneburg\"><PERSON> of Brandenburg, Duchess of Brunswick-Lüneburg</a> (b. 1210)", "links": [{"title": "<PERSON> of Brandenburg, Duchess of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_of_Brandenburg,_Duchess_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1338", "text": "<PERSON><PERSON><PERSON>, Japanese governor (b. 1318)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/Kitabatake_Akiie\" title=\"Kitabatake Akiie\"><PERSON><PERSON><PERSON></a>, Japanese governor (b. 1318)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kitabatake_Akiie\" title=\"Kitabatake Akiie\"><PERSON><PERSON><PERSON></a>, Japanese governor (b. 1318)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kit<PERSON><PERSON>_A<PERSON>ie"}]}, {"year": "1364", "text": "<PERSON> of Austria (b. 1281)", "html": "1364 - <a href=\"https://wikipedia.org/wiki/Agnes_of_Austria_(1281%E2%80%931364)\" title=\"<PERSON> of Austria (1281-1364)\"><PERSON> of Austria</a> (b. 1281)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agnes_of_Austria_(1281%E2%80%931364)\" title=\"<PERSON> of Austria (1281-1364)\"><PERSON> of Austria</a> (b. 1281)", "links": [{"title": "<PERSON> of Austria (1281-1364)", "link": "https://wikipedia.org/wiki/Agnes_of_Austria_(1281%E2%80%931364)"}]}, {"year": "1424", "text": "<PERSON>, Duke of Austria (b. 1377)", "html": "1424 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1377)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1377)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria"}]}, {"year": "1437", "text": "<PERSON> Navarre, Queen of England (b. 1370)", "html": "1437 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_England\" title=\"<PERSON> of Navarre, Queen of England\"><PERSON> of Navarre, Queen of England</a> (b. 1370)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_England\" title=\"<PERSON> of Navarre, Queen of England\"><PERSON> Navarre, Queen of England</a> (b. 1370)", "links": [{"title": "<PERSON> of Navarre, Queen of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_England"}]}, {"year": "1468", "text": "<PERSON><PERSON><PERSON>, supreme leader of Tayyibi Isma'ilism, scholar and historian (b. 1392)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, supreme leader of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27ilism\" title=\"<PERSON>yyibi <PERSON>'ilism\"><PERSON><PERSON><PERSON><PERSON></a>, scholar and historian (b. 1392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, supreme leader of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27ilism\" title=\"<PERSON><PERSON><PERSON><PERSON>'il<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, scholar and historian (b. 1392)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idr<PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tayyibi_Isma%27ilism"}]}, {"year": "1552", "text": "<PERSON>, English poet and author (b. 1476)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1476)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1476)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1556", "text": "<PERSON>, German composer and theorist (b. 1486)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (b. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (b. 1486)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON><PERSON>, Portuguese poet (b. 1524-25)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_de_Cam%C3%B5es\" title=\"<PERSON><PERSON> Camões\"><PERSON><PERSON></a>, Portuguese poet (b. 1524-25)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_de_Cam%C3%B5es\" title=\"<PERSON><PERSON> Camões\"><PERSON><PERSON></a>, Portuguese poet (b. 1524-25)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%ADs_de_Cam%C3%B5es"}]}, {"year": "1604", "text": "<PERSON>, Italian actress (b. 1562)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (b. 1562)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, English politician, Attorney General for England and Wales (b. 1531)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (b. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (b. 1531)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1654", "text": "<PERSON>, Italian sculptor (b. 1598)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (b. 1598)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, Swedish lawyer and politician (b. 1635)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>_<PERSON>lenstier<PERSON>\" title=\"<PERSON>lenstier<PERSON>\"><PERSON></a>, Swedish lawyer and politician (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>_<PERSON>lenstier<PERSON>\" title=\"<PERSON>lenstier<PERSON>\"><PERSON></a>, Swedish lawyer and politician (b. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>yllenstierna"}]}, {"year": "1692", "text": "<PERSON>, Colonial Massachusetts woman hanged as a witch during the Salem witch trials (b. 1632)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colonial Massachusetts woman hanged as a witch during the <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a> (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colonial Massachusetts woman hanged as a witch during the <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a> (b. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Salem witch trials", "link": "https://wikipedia.org/wiki/Salem_witch_trials"}]}, {"year": "1735", "text": "<PERSON>, English historian and author (b. 1678)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquarian)\" title=\"<PERSON> (antiquarian)\"><PERSON></a>, English historian and author (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquarian)\" title=\"<PERSON> (antiquarian)\"><PERSON></a>, English historian and author (b. 1678)", "links": [{"title": "<PERSON> (antiquarian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquarian)"}]}, {"year": "1753", "text": "<PERSON>, German architect (b. 1678)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Unfriedt\"><PERSON></a>, German architect (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Unfriedt\"><PERSON></a>, German architect (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Burmese king (b. 1736)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/Hsinbyushin\" title=\"Hsinbyush<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hsinbyushin\" title=\"<PERSON>sinbyush<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (b. 1736)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hsinbyushin"}]}, {"year": "1776", "text": "<PERSON>, Austrian instrument maker (b. 1722)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leopold Widhalm\"><PERSON></a>, Austrian instrument maker (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leopold Widhalm\"><PERSON></a>, Austrian instrument maker (b. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_Widhalm"}]}, {"year": "1791", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French admiral (b. 1720)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_Motte\" title=\"To<PERSON><PERSON><PERSON><PERSON><PERSON> Motte\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French admiral (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tte\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French admiral (b. 1720)", "links": [{"title": "Toussaint<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Grand Duke of Baden (b. 1728)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Baden\" title=\"<PERSON>, Grand Duke of Baden\"><PERSON>, Grand Duke of Baden</a> (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Baden\" title=\"<PERSON>, Grand Duke of Baden\"><PERSON>, Grand Duke of Baden</a> (b. 1728)", "links": [{"title": "<PERSON>, Grand Duke of Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Baden"}]}, {"year": "1831", "text": "<PERSON>, German-Russian field marshal (b. 1785)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Russian field marshal (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Russian field marshal (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON>, French physicist and mathematician (b. 1775)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and mathematician (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_Amp%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and mathematician (b. 1775)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_Amp%C3%A8re"}]}, {"year": "1849", "text": "<PERSON>, French general and politician (b. 1784)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Scottish botanist (b. 1773)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_botanist_from_Montrose)\" class=\"mw-redirect\" title=\"<PERSON> (Scottish botanist from Montrose)\"><PERSON></a>, Scottish botanist (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_botanist_from_Montrose)\" class=\"mw-redirect\" title=\"<PERSON> (Scottish botanist from Montrose)\"><PERSON></a>, Scottish botanist (b. 1773)", "links": [{"title": "<PERSON> (Scottish botanist from Montrose)", "link": "https://wikipedia.org/wiki/<PERSON>_(Scottish_botanist_from_Montrose)"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Prince of Serbia (b. 1823)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87_III,_Prince_of_Serbia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Prince of Serbia\"><PERSON><PERSON><PERSON>, Prince of Serbia</a> (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87_III,_Prince_of_Serbia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Prince of Serbia\"><PERSON><PERSON><PERSON>, Prince of Serbia</a> (b. 1823)", "links": [{"title": "<PERSON><PERSON><PERSON>, Prince of Serbia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_O<PERSON>novi%C4%87_III,_Prince_of_Serbia"}]}, {"year": "1899", "text": "<PERSON>, French composer (b. 1855)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Scottish poet, author, and playwright (b. 1841)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet, author, and playwright (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet, author, and playwright (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Catalan priest and poet (b. 1845)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Catalan priest and poet (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Catalan priest and poet (b. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ague<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English-New Zealand politician, 15th Prime Minister of New Zealand (b. 1845)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1909", "text": "<PERSON>, American minister, historian, and author (b. 1822)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, historian, and author (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, historian, and author (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian architect (b. 1845)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian architect (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian architect (b. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96d%C3%B6n_<PERSON><PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Italian author, poet, and composer (b. 1842)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Arrigo_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian author, poet, and composer (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian author, poet, and composer (b. 1842)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arrigo_<PERSON>ito"}]}, {"year": "1923", "text": "<PERSON>, French soldier and author (b. 1850)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Italian lawyer and politician (b. 1885)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Spanish architect, designed the <PERSON> (b. 1852)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aud%C3%AD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish architect, designed the <a href=\"https://wikipedia.org/wiki/Park_G%C3%BCell\" title=\"<PERSON>\"><PERSON></a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aud%C3%AD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish architect, designed the <a href=\"https://wikipedia.org/wiki/Park_G%C3%BCell\" title=\"<PERSON>\"><PERSON></a> (b. 1852)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_Gaud%C3%AD"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Park_G%C3%BCell"}]}, {"year": "1930", "text": "<PERSON>, German historian and theologian (b. 1851)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and theologian (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and theologian (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English composer and educator (b. 1862)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English-Australian politician, 26th Premier of Victoria (b. 1856)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1937", "text": "<PERSON>, Canadian lawyer and politician, 8th Prime Minister of Canada (b. 1854)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1939", "text": "<PERSON>, Australian politician, 28th Premier of Tasmania (b. 1890)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1940", "text": "<PERSON>, Jamaican journalist and activist, founded the Black Star Line (b. 1887)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican journalist and activist, founded the <a href=\"https://wikipedia.org/wiki/Black_Star_Line\" title=\"Black Star Line\">Black Star Line</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican journalist and activist, founded the <a href=\"https://wikipedia.org/wiki/Black_Star_Line\" title=\"Black Star Line\">Black Star Line</a> (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Black Star Line", "link": "https://wikipedia.org/wiki/Black_Star_Line"}]}, {"year": "1944", "text": "<PERSON>, Dutch mathematician and academic (b. 1910)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American boxer (b. 1878)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1878)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1947", "text": "<PERSON>, Canadian businessman and politician, 12th Mayor of Vancouver (b. 1852)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian businessman and politician, 12th <a href=\"https://wikipedia.org/wiki/Mayor_of_Vancouver\" class=\"mw-redirect\" title=\"Mayor of Vancouver\">Mayor of Vancouver</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian businessman and politician, 12th <a href=\"https://wikipedia.org/wiki/Mayor_of_Vancouver\" class=\"mw-redirect\" title=\"Mayor of Vancouver\">Mayor of Vancouver</a> (b. 1852)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Mayor of Vancouver", "link": "https://wikipedia.org/wiki/Mayor_of_Vancouver"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Danish-Norwegian novelist, essayist, and translator, Nobel Prize laureate (b. 1882)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Sigrid_Undset\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-Norwegian novelist, essayist, and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-Norwegian novelist, essayist, and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigrid_Undset"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1955", "text": "<PERSON>, Indian-American golfer (b. 1876)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American golfer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American golfer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American journalist, poet, and playwright (b. 1880)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rimk%C3%A9\" title=\"<PERSON> Weld Grimké\"><PERSON></a>, American journalist, poet, and playwright (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rimk%C3%A9\" title=\"<PERSON> Weld Grimké\"><PERSON></a>, American journalist, poet, and playwright (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Weld_Grimk%C3%A9"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Hungarian politician (b. 1883)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Mesk%C3%B3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian politician (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Mesk%C3%B3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian politician (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Mesk%C3%B3"}]}, {"year": "1963", "text": "<PERSON>, English cartoonist (b. 1936)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Turkish footballer and manager (b. 1908)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vahap_%C3%96zaltay\" title=\"<PERSON>ahap Özaltay\"><PERSON><PERSON><PERSON></a>, Turkish footballer and manager (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vahap_%C3%96zaltay\" title=\"Vahap Özaltay\"><PERSON><PERSON><PERSON></a>, Turkish footballer and manager (b. 1908)", "links": [{"title": "Vahap <PERSON>", "link": "https://wikipedia.org/wiki/Vahap_%C3%96zaltay"}]}, {"year": "1967", "text": "<PERSON>, American actor (b. 1900)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tracy\"><PERSON></a>, American actor (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English actor (b. 1909)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American playwright and novelist (b. 1913)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and novelist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and novelist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}]}, {"year": "1974", "text": "<PERSON>, Duke of Gloucester (b. 1900)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester\" title=\"Prince <PERSON>, Duke of Gloucester\">Prince <PERSON>, Duke of Gloucester</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester\" title=\"Prince <PERSON>, Duke of Gloucester\">Prince <PERSON>, Duke of Gloucester</a> (b. 1900)", "links": [{"title": "<PERSON> <PERSON>, Duke of Gloucester", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American film producer, co-founded Paramount Pictures (b. 1873)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Paramount_Pictures\" title=\"Paramount Pictures\">Paramount Pictures</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Paramount_Pictures\" title=\"Paramount Pictures\">Paramount Pictures</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Paramount Pictures", "link": "https://wikipedia.org/wiki/Paramount_Pictures"}]}, {"year": "1982", "text": "<PERSON><PERSON>, German actor, director, and screenwriter (b. 1945)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor, director, and screenwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor, director, and screenwriter (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Turkish author and poet (b. 1901)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nusret_Zorlutuna\" title=\"Halide Nusret Zorlutuna\"><PERSON><PERSON></a>, Turkish author and poet (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nusret_Zorlutuna\" title=\"Halide Nusret Zorlutuna\"><PERSON><PERSON>orlut<PERSON></a>, Turkish author and poet (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Halide_Nusret_Zorlutuna"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American author and playwright (b. 1919)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and playwright (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and playwright (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actress (b. 1943)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American novelist and short story writer (b. 1908)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Louis_L%27Amour\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_L%27Amour\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_L%27Amour"}]}, {"year": "1991", "text": "<PERSON>, French author and illustrator, co-founded Les Éditions de Minuit (b. 1902)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator, co-founded <a href=\"https://wikipedia.org/wiki/Les_%C3%89ditions_de_Minuit\" title=\"Les Éditions de Minuit\">Les Éditions de Minuit</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator, co-founded <a href=\"https://wikipedia.org/wiki/Les_%C3%89ditions_de_Minuit\" title=\"Les Éditions de Minuit\">Les Éditions de Minuit</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Les Éditions de Minuit", "link": "https://wikipedia.org/wiki/Les_%C3%89ditions_de_<PERSON>uit"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Chinese-Japanese pianist and composer (b. 1931)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-Japanese pianist and composer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-Japanese pianist and composer (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English comedian, actor, writer and presenter (b. 1931)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, writer and presenter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, writer and presenter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian soldier, football player, and politician (b. 1910)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, football player, and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, football player, and politician (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actress (b. 1915)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American baseball player (b. 1921)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1998", "text": "<PERSON>, English author (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hammond Innes\"><PERSON></a>, English author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hammond_Innes\" title=\"Hammond Innes\"><PERSON></a>, English author (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hammond_Innes"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Syrian general and politician, 18th President of Syria (b. 1930)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Syrian general and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_Syria\" title=\"President of Syria\">President of Syria</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Syrian general and politician, 18th <a href=\"https://wikipedia.org/wiki/President_of_Syria\" title=\"President of Syria\">President of Syria</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "President of Syria", "link": "https://wikipedia.org/wiki/President_of_Syria"}]}, {"year": "2000", "text": "<PERSON>, English cricketer (b. 1930)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Princess of Iran (b. 1970)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Princess of Iran (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Princess of Iran (b. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American mobster (b. 1940)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mobster (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mobster (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American colonel and politician, 11th White House Chief of Staff (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 11th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 11th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "White House Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Chief_of_Staff"}]}, {"year": "2003", "text": "<PERSON>, English philosopher and academic (b. 1929)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Welsh academic and politician (b. 1939)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_politician)\" title=\"<PERSON> (Welsh politician)\"><PERSON></a>, Welsh academic and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_politician)\" title=\"<PERSON> (Welsh politician)\"><PERSON></a>, Welsh academic and politician (b. 1939)", "links": [{"title": "<PERSON> (Welsh politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_politician)"}]}, {"year": "2004", "text": "<PERSON>, American singer-songwriter, pianist, and actor (b. 1930)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, French actress and singer (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and singer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and singer (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>re"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Greek economist and politician, 177th Prime Minister of Greece (b. 1904)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Xenophon <PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and politician, 177th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eno<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and politician, 177th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>enophon_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "2005", "text": "<PERSON>, American aircraft designer, designed the Pitts Special (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aircraft designer, designed the <a href=\"https://wikipedia.org/wiki/Pitts_Special\" title=\"Pitts Special\">Pitts Special</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aircraft designer, designed the <a href=\"https://wikipedia.org/wiki/Pitts_Special\" title=\"Pitts Special\">Pitts Special</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>s Special", "link": "https://wikipedia.org/wiki/Pitts_Special"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American-New Zealand meteorologist (b. 1940)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-New Zealand meteorologist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-New Zealand meteorologist (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Kyrgyzstani author and diplomat (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kyrgyzstani author and diplomat (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kyrgyzstani author and diplomat (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Greek footballer (b. 1940)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eli<PERSON>_Ske<PERSON>filakas"}]}, {"year": "2010", "text": "<PERSON>, American archbishop (b. 1939)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archbishop (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archbishop (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, German painter and photographer (b. 1941)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter and photographer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter and photographer (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Irish lawyer and politician, 25th Irish Minister for Finance (b. 1959)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nr\" title=\"<PERSON> Jnr\"><PERSON></a>, Irish lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Ireland)\" title=\"Minister for Finance (Ireland)\">Irish Minister for Finance</a> (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nr\" title=\"<PERSON> Jnr\"><PERSON> Jn<PERSON></a>, Irish lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Ireland)\" title=\"Minister for Finance (Ireland)\">Irish Minister for Finance</a> (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jnr"}, {"title": "Minister for Finance (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Finance_(Ireland)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Italian conductor (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian conductor (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "2012", "text": "<PERSON>, American sportscaster (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fusselle\" title=\"<PERSON> Fu<PERSON>le\"><PERSON></a>, American sportscaster (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Fusselle\" title=\"<PERSON>le\"><PERSON></a>, American sportscaster (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fusselle"}]}, {"year": "2012", "text": "<PERSON>, Dutch songwriter and producer (b. 1947)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch songwriter and producer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch songwriter and producer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French painter and academic (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Kenyan politician (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan politician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan politician (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Kenyan economist and politician, 6th Vice-President of Kenya (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan economist and politician, 6th <a href=\"https://wikipedia.org/wiki/Vice-President_of_Kenya\" class=\"mw-redirect\" title=\"Vice-President of Kenya\">Vice-President of Kenya</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan economist and politician, 6th <a href=\"https://wikipedia.org/wiki/Vice-President_of_Kenya\" class=\"mw-redirect\" title=\"Vice-President of Kenya\">Vice-President of Kenya</a> (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice-President of Kenya", "link": "https://wikipedia.org/wiki/Vice-President_of_Kenya"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Chinese-Indonesian businessman, founded Bank Central Asia (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-Indonesian businessman, founded <a href=\"https://wikipedia.org/wiki/Bank_Central_Asia\" title=\"Bank Central Asia\">Bank Central Asia</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-Indonesian businessman, founded <a href=\"https://wikipedia.org/wiki/Bank_Central_Asia\" title=\"Bank Central Asia\">Bank Central Asia</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bank Central Asia", "link": "https://wikipedia.org/wiki/Bank_Central_Asia"}]}, {"year": "2012", "text": "<PERSON>, English footballer (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Gordon_<PERSON>\" title=\"Gordon West\"><PERSON></a>, English footballer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gordon_West\" title=\"Gordon West\"><PERSON></a>, English footballer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gordon_West"}]}, {"year": "2013", "text": "<PERSON>, American political consultant (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political consultant (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political consultant (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Spanish footballer and coach (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and coach (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and coach (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Brazilian lawyer and politician, 57th Governor of Rio de Janeiro (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/Governor_of_Rio_de_Janeiro\" class=\"mw-redirect\" title=\"Governor of Rio de Janeiro\">Governor of Rio de Janeiro</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/Governor_of_Rio_de_Janeiro\" class=\"mw-redirect\" title=\"Governor of Rio de Janeiro\">Governor of Rio de Janeiro</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>car"}, {"title": "Governor of Rio de Janeiro", "link": "https://wikipedia.org/wiki/Governor_of_Rio_de_Janeiro"}]}, {"year": "2014", "text": "<PERSON>, Australian cricketer and manager (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and manager (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and manager (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American theologian and academic (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, American theologian and academic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, American theologian and academic (b. 1917)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(theologian)"}]}, {"year": "2014", "text": "<PERSON>, American radio host and politician (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American radio host and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American radio host and politician (b. 1920)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "2015", "text": "<PERSON>, American film producer and philanthropist (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer and philanthropist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer and philanthropist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German author and publisher (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and publisher (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and publisher (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American singer-songwriter (b. 1994)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Indonesian singer and actress (b. 1980)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian singer and actress (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian singer and actress (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American singer, winner of the 2008 season of America's Got Talent (b. 1975)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, winner of the 2008 season of <i><a href=\"https://wikipedia.org/wiki/America%27s_Got_Talent\" title=\"America's Got Talent\">America's Got Talent</a></i> (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, winner of the 2008 season of <i><a href=\"https://wikipedia.org/wiki/America%27s_Got_Talent\" title=\"America's Got Talent\">America's Got Talent</a></i> (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "America's Got Talent", "link": "https://wikipedia.org/wiki/America%27s_Got_Talent"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American baseball player (b. 1954)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American mathematician and domestic terrorist (b. 1942)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and domestic terrorist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and domestic terrorist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "Victims in the 2024 Chikangawa Dornier 228 crash:\n<PERSON><PERSON>, Malawian economist and politician (b. 1973)\n<PERSON>, Malawian teacher, politician, and former First Lady of Malawi (b. 1964)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_Chikangawa_Dornier_228_crash\" title=\"2024 Chikangawa Dornier 228 crash\">Victims in the 2024 Chikangawa Dornier 228 crash</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malawian economist and politician (b. 1973)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malawian teacher, politician, and former First <PERSON> Malawi (b. 1964)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_Chikangawa_Dornier_228_crash\" title=\"2024 Chikangawa Dornier 228 crash\">Victims in the 2024 Chikangawa Dornier 228 crash</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malawian economist and politician (b. 1973)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malawian teacher, politician, and former <PERSON> Malawi (b. 1964)</li>\n</ul>", "links": [{"title": "2024 Chikangawa Dornier 228 crash", "link": "https://wikipedia.org/wiki/2024_Chikangawa_Dornier_228_crash"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saul<PERSON>_<PERSON>a"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Malawian economist and politician (b. 1973)", "text": null, "html": "<PERSON><PERSON>, Malawian economist and politician (b. 1973) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malawian economist and politician (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malawian economist and politician (b. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Saulos_<PERSON>lima"}]}, {"year": "<PERSON>, Malawian teacher, politician, and former First Lady of Malawi (b. 1964)", "text": null, "html": "<PERSON>, Malawian teacher, politician, and former First Lady of Malawi (b. 1964) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malawian teacher, politician, and former First Lady of Malawi (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malawian teacher, politician, and former First Lady of Malawi (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian politician, 36th Premier of South Australia (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Steele_<PERSON>\" title=\"Steele Hall\"><PERSON></a>, Australian politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Steele_Hall\" title=\"Steele Hall\"><PERSON></a>, Australian politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1928)", "links": [{"title": "Steele Hall", "link": "https://wikipedia.org/wiki/Steele_Hall"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}]}}